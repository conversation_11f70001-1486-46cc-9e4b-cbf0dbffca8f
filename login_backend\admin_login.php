<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 获取请求体
$data = json_decode(file_get_contents('php://input'), true);

// 验证输入
if (!isset($data['username']) || empty($data['username']) || !isset($data['password']) || empty($data['password'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少必填字段: 用户名或密码']);
    exit();
}

$username = $data['username'];
$password = $data['password'];

// 实例化数据库连接
$db = new Database();
$conn = $db->getConnection();

// 查询用户
$stmt = $conn->prepare("SELECT id, username, password, real_name FROM admin_users WHERE username = :username");
$stmt->bindParam(':username', $username);
$stmt->execute();
$admin = $stmt->fetch(PDO::FETCH_ASSOC);

// 验证用户是否存在
if (!$admin) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '用户名或密码错误']);
    exit();
}

// 验证密码
if (!password_verify($password, $admin['password'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '用户名或密码错误']);
    exit();
}

// 更新最后登录时间
$updateStmt = $conn->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = :id");
$updateStmt->bindParam(':id', $admin['id']);
$updateStmt->execute();

// 生成管理员令牌
$auth = new Auth();
$token = $auth->generateAdminToken($admin['id'], $admin['username']);

// 返回结果
echo json_encode([
    'error' => false,
    'data' => [
        'token' => $token,
        'user_id' => $admin['id'],
        'username' => $admin['username'],
        'real_name' => $admin['real_name']
    ]
]); 