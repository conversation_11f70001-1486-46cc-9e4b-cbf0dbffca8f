const app = getApp();

Page({
  data: {
    loading: false,
    progress: 0,
    showResult: false,
    result: {
      total_processed: 0,
      updated_count: 0,
      skipped_count: 0,
      enhanced_count: 0,
      updated_items: [],
      tag_cleanup: {
        valid_tags_count: 0,
        format_cleaned_count: 0,
        invalid_cleaned_count: 0
      }
    },
    showDetailsList: false,
    progressTimer: null,
    isPolling: false
  },

  onLoad: function(options) {
    // 初始化页面
  },

  onUnload: function() {
    // 在页面卸载时清除定时器
    this.clearProgressTimer();
  },

  clearProgressTimer: function() {
    if (this.data.progressTimer) {
      clearInterval(this.data.progressTimer);
      this.setData({
        progressTimer: null,
        isPolling: false
      });
    }
  },

  // 更新衣物标签
  updateClothingTags: function() {
    // 避免重复点击
    if (this.data.loading) return;
    
    // 显示确认对话框
    wx.showModal({
      title: '更新衣物标签',
      content: '系统将分析缺少标签或标签不完善的衣物，并自动生成或补充标签信息，该过程可能需要一些时间，是否继续？',
      confirmText: '继续',
      success: (res) => {
        if (res.confirm) {
          this.startUpdateProcess();
        }
      }
    });
  },
  
  // 开始更新流程
  startUpdateProcess: function() {
    this.setData({
      loading: true,
      progress: 0,
      showResult: false
    });
    
    // 发起衣物标签更新请求
    this.requestUpdateTags();
    
    // 开始轮询进度
    this.startProgressPolling();
  },
  
  // 请求更新标签
  requestUpdateTags: function() {
    wx.request({
      url: `${app.globalData.apiBaseUrl}/update_clothing_tags.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          // 更新成功
          console.log('更新标签成功:', res.data);
          
          // 停止轮询
          this.clearProgressTimer();
          
          // 将进度设为100%
          this.setData({
            progress: 100
          });
          
          // 处理成功响应
          this.handleSuccessResponse(res.data.data);
          
        } else {
          // 更新失败
          this.clearProgressTimer();
          this.setData({ loading: false });
          
          wx.showToast({
            title: res.data && res.data.msg ? res.data.msg : '更新失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        // 尝试使用POST方法重试请求
        this.retryWithPost();
      }
    });
  },
  
  // 开始轮询进度
  startProgressPolling: function() {
    // 确保不会重复启动轮询
    if (this.data.isPolling) return;
    
    this.setData({
      isPolling: true
    });
    
    // 每2秒查询一次进度
    const timer = setInterval(() => {
      this.checkProgress();
    }, 2000);
    
    this.setData({
      progressTimer: timer
    });
  },
  
  // 检查进度
  checkProgress: function() {
    wx.request({
      url: `${app.globalData.apiBaseUrl}/update_clothing_tags.php?check_progress=1`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          const progressData = res.data.data;
          
          console.log('进度数据:', progressData);
          
          // 更新进度
          if (progressData.total_items > 0) {
            const currentProgress = progressData.progress || 0;
            
            this.setData({
              progress: currentProgress
            });
            
            // 如果处理已完成，停止轮询
            if (progressData.status === 'completed' || currentProgress >= 100) {
              this.clearProgressTimer();
            }
          }
        } else {
          console.error('获取进度失败:', res.data);
        }
      },
      fail: (err) => {
        console.error('获取进度请求失败:', err);
      }
    });
  },
  
  // 使用POST方法重试请求
  retryWithPost: function() {
    wx.request({
      url: `${app.globalData.apiBaseUrl}/update_clothing_tags.php`,
      method: 'POST',
      header: {
        'Authorization': app.globalData.token,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          // 更新成功
          console.log('POST重试成功:', res.data);
          
          // 停止轮询
          this.clearProgressTimer();
          
          // 将进度设为100%
          this.setData({
            progress: 100
          });
          
          // 处理成功响应
          this.handleSuccessResponse(res.data.data);
          
        } else {
          this.clearProgressTimer();
          this.setData({ loading: false });
          wx.showToast({
            title: res.data && res.data.msg ? res.data.msg : '更新失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('POST重试失败:', err);
        this.clearProgressTimer();
        this.setData({ loading: false });
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 处理成功响应
  handleSuccessResponse: function(data) {
    // 添加调试日志
    console.log('API返回的结果数据:', data);
    
    // 显示成功消息，包含标签更新信息
    const updatedCount = data.updated_count || 0;
    const enhancedCount = data.enhanced_count || 0;
    const formatCleanedCount = data.tag_cleanup && data.tag_cleanup.format_cleaned_count || 0;
    
    if (updatedCount > 0 || enhancedCount > 0 || formatCleanedCount > 0) {
      let message = '';
      
      if (updatedCount > 0 && enhancedCount > 0) {
        message = `成功更新${updatedCount - enhancedCount}件衣物的标签，补充${enhancedCount}件衣物的标签`;
      } else if (enhancedCount > 0) {
        message = `成功补充${enhancedCount}件衣物的标签`;
      } else if (updatedCount > 0) {
        message = `成功更新${updatedCount}件衣物的标签`;
      }
      
      // 如果有格式清理，添加到消息中
      if (formatCleanedCount > 0) {
        message += `，修正${formatCleanedCount}件衣物的标签格式`;
      }
      
      wx.showToast({
        title: message,
        icon: 'success',
        duration: 2000
      });
      
      // 延迟显示结果页面，避免与Toast消息重叠
      setTimeout(() => {
        this.setData({
          loading: false,
          showResult: true,
          result: data
        });
        
        // 设置需要刷新标签列表
        app.globalData.needRefreshTags = true;
      }, 2200);  // 比Toast持续时间稍长一些
    } else {
      // 如果没有更新，直接显示结果
      setTimeout(() => {
        this.setData({
          loading: false,
          showResult: true,
          result: data
        });
        
        // 设置需要刷新标签列表
        app.globalData.needRefreshTags = true;
      }, 500);
    }
  },
  
  // 切换显示详细列表
  toggleDetailsList: function() {
    this.setData({
      showDetailsList: !this.data.showDetailsList
    });
  },
  
  // 查看更新后的衣物详情
  viewClothingDetail: function(e) {
    const { id } = e.currentTarget.dataset;
    // 跳转到衣物详情页
    wx.navigateTo({
      url: `/pages/clothing/detail/detail?id=${id}`
    });
  },
  
  // 返回标签列表页
  goToTagList: function() {
    // 如果是从标签详情页进入的，则返回上一页
    // 否则跳转到标签列表页
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];
    
    if (prevPage && prevPage.route.includes('clothing-tags/detail')) {
      wx.navigateBack();
    } else {
      wx.redirectTo({
        url: '/pages/clothing-tags/index/index'
      });
    }
  },
  
  // 返回
  goBack: function() {
    wx.navigateBack();
  }
}) 