<?php
// 创建穿搭圈子API
// 模块1：圈子基础管理模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

// 验证必需参数
if (!isset($input['name']) || empty(trim($input['name']))) {
    echo json_encode([
        'status' => 'error',
        'message' => '圈子名称不能为空'
    ]);
    exit;
}

$name = trim($input['name']);
$description = isset($input['description']) ? trim($input['description']) : '';

// 验证圈子名称长度
if (mb_strlen($name) > 50) {
    echo json_encode([
        'status' => 'error',
        'message' => '圈子名称不能超过50个字符'
    ]);
    exit;
}

// 验证描述长度
if (mb_strlen($description) > 500) {
    echo json_encode([
        'status' => 'error',
        'message' => '圈子描述不能超过500个字符'
    ]);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查用户是否已经创建了圈子或加入了其他圈子
    $checkSql = "SELECT c.id, c.name, cm.role 
                 FROM circle_members cm 
                 JOIN outfit_circles c ON cm.circle_id = c.id 
                 WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkStmt->execute();
    
    $existingCircle = $checkStmt->fetch(PDO::FETCH_ASSOC);
    if ($existingCircle) {
        echo json_encode([
            'status' => 'error',
            'message' => '您已经在圈子"' . $existingCircle['name'] . '"中，无法创建新圈子'
        ]);
        exit;
    }
    
    // 生成唯一邀请码
    $invitationCode = generateUniqueInvitationCode($conn);
    
    // 开始事务
    $conn->beginTransaction();
    
    // 创建圈子
    $createCircleSql = "INSERT INTO outfit_circles (name, description, invitation_code, creator_id) 
                        VALUES (:name, :description, :invitation_code, :creator_id)";
    $createStmt = $conn->prepare($createCircleSql);
    $createStmt->bindParam(':name', $name);
    $createStmt->bindParam(':description', $description);
    $createStmt->bindParam(':invitation_code', $invitationCode);
    $createStmt->bindParam(':creator_id', $userId, PDO::PARAM_INT);
    $createStmt->execute();
    
    $circleId = $conn->lastInsertId();
    
    // 将创建者添加到成员表
    $addMemberSql = "INSERT INTO circle_members (circle_id, user_id, role) 
                     VALUES (:circle_id, :user_id, 'creator')";
    $addMemberStmt = $conn->prepare($addMemberSql);
    $addMemberStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $addMemberStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $addMemberStmt->execute();
    
    // 提交事务
    $conn->commit();
    
    echo json_encode([
        'status' => 'success',
        'message' => '圈子创建成功',
        'data' => [
            'circle_id' => $circleId,
            'name' => $name,
            'description' => $description,
            'invitation_code' => $invitationCode
        ]
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if (isset($conn)) {
        $conn->rollBack();
    }
    
    echo json_encode([
        'status' => 'error',
        'message' => '创建圈子失败：' . $e->getMessage()
    ]);
}

// 生成唯一邀请码的函数
function generateUniqueInvitationCode($conn, $length = 8) {
    $maxAttempts = 10;
    $attempts = 0;
    
    do {
        $code = generateRandomCode($length);
        
        // 检查邀请码是否已存在
        $checkSql = "SELECT id FROM outfit_circles WHERE invitation_code = :code";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bindParam(':code', $code);
        $checkStmt->execute();
        
        if ($checkStmt->rowCount() === 0) {
            return $code;
        }
        
        $attempts++;
    } while ($attempts < $maxAttempts);
    
    throw new Exception('无法生成唯一邀请码');
}

// 生成随机邀请码的函数
function generateRandomCode($length = 8) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }
    
    return $code;
}
?>
