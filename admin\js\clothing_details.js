/**
 * 衣物详情脚本
 */
const ClothingDetails = {
    // 衣物ID
    clothingId: null,
    
    // 衣物数据
    clothingData: null,
    
    // DOM元素
    elements: {},
    
    // 内部图片查看器
    imageViewer: {
        // 查看器元素
        viewer: null,
        image: null,
        closeBtn: null,
        isInitialized: false,
        
        // 初始化查看器
        init: function() {
            if (this.isInitialized) return;
            
            // 创建查看器DOM
            this.viewer = document.createElement('div');
            this.viewer.className = 'image-viewer';
            this.viewer.style.cssText = 'position:fixed; top:0; left:0; width:100%; height:100%; ' +
                'background-color:rgba(0,0,0,0.9); z-index:9999; display:none; ' +
                'align-items:center; justify-content:center;';
            
            // 创建图片元素
            this.image = document.createElement('img');
            this.image.className = 'viewer-image';
            this.image.style.cssText = 'max-width:90%; max-height:90%; object-fit:contain;';
            this.viewer.appendChild(this.image);
            
            // 创建关闭按钮
            this.closeBtn = document.createElement('button');
            this.closeBtn.innerHTML = '&times;';
            this.closeBtn.style.cssText = 'position:absolute; top:15px; right:20px; ' +
                'background:none; border:none; color:white; font-size:30px; ' +
                'cursor:pointer; z-index:10000;';
            this.viewer.appendChild(this.closeBtn);
            
            // 添加到页面
            document.body.appendChild(this.viewer);
            
            // 绑定事件
            this.closeBtn.addEventListener('click', () => this.close());
            this.viewer.addEventListener('click', (e) => {
                if (e.target === this.viewer) this.close();
            });
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') this.close();
            });
            
            this.isInitialized = true;
            console.log('内部图片查看器初始化成功');
        },
        
        // 显示图片
        show: function(imageUrl) {
            if (!this.isInitialized) this.init();
            
            this.image.src = imageUrl;
            this.image.onload = () => {
                this.viewer.style.display = 'flex';
                document.body.style.overflow = 'hidden';
            };
            this.image.onerror = () => {
                console.error('图片加载失败:', imageUrl);
                alert('图片加载失败');
            };
        },
        
        // 关闭查看器
        close: function() {
            if (!this.isInitialized) return;
            this.viewer.style.display = 'none';
            document.body.style.overflow = '';
        },
        
        // 绑定图片点击事件
        bindImages: function(selector) {
            if (!this.isInitialized) this.init();
            
            const images = document.querySelectorAll(selector);
            console.log(`为${images.length}个元素绑定内部图片查看器`);
            
            images.forEach(img => {
                if (!img.dataset.hasInternalViewer) {
                    img.style.cursor = 'pointer';
                    
                    img.addEventListener('click', (event) => {
                        event.preventDefault();
                        event.stopPropagation();
                        
                        const imageUrl = img.getAttribute('data-origin') || img.src;
                        this.show(imageUrl);
                    });
                    
                    img.dataset.hasInternalViewer = 'true';
                }
            });
        }
    },
    
    /**
     * 初始化
     */
    init: function() {
        // 获取URL参数中的衣物ID
        const urlParams = new URLSearchParams(window.location.search);
        this.clothingId = urlParams.get('id');
        
        if (!this.clothingId) {
            alert('缺少衣物ID参数');
            window.location.href = 'clothing_list.html';
            return;
        }
        
        // 初始化DOM元素引用
        this.elements = {
            clothingDetailContainer: document.getElementById('clothingDetailContainer'),
            descriptionContainer: document.getElementById('descriptionContainer').querySelector('.description-content'),
            userInfoContainer: document.getElementById('userInfoContainer')
        };
        
        // 初始化内部图片查看器
        this.imageViewer.init();
        
        // 加载衣物详情
        this.loadClothingDetails();
    },
    
    /**
     * 加载衣物详情
     */
    loadClothingDetails: function() {
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        fetch(`${Auth.apiBaseUrl}/get_admin_clothing_details.php?id=${this.clothingId}`, {
            method: 'GET',
            headers: {
                'Authorization': token
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '加载失败');
            }
            
            this.clothingData = data.data;
            this.renderClothingDetails(data.data.clothing);
            this.renderClothingDescription(data.data.clothing);
            this.renderUserInfo(data.data.user);
        })
        .catch(error => {
            console.error('获取衣物详情失败:', error);
            this.elements.clothingDetailContainer.innerHTML = `<div class="empty-text">加载失败: ${error.message}</div>`;
            this.elements.descriptionContainer.innerHTML = '<div class="empty-text">加载失败</div>';
            this.elements.userInfoContainer.innerHTML = '<div class="empty-text">加载失败</div>';
        });
    },
    
    /**
     * 渲染衣物基本信息
     * @param {Object} clothing 衣物数据
     */
    renderClothingDetails: function(clothing) {
        if (!clothing) {
            this.elements.clothingDetailContainer.innerHTML = '<div class="empty-text">衣物数据不存在</div>';
            return;
        }
        
        const categoryText = this.getCategoryText(clothing.category);
        
        // 默认图片
        const defaultImg = 'images/default-cloth.png';
        
        // 检查图片URL并处理微信临时文件路径
        let imageUrl;
        if (!clothing.image_url || clothing.image_url.trim() === '') {
            imageUrl = defaultImg;
        } else if (clothing.image_url.startsWith('wxfile://')) {
            // 不支持的微信临时文件URL，使用默认图片
            imageUrl = defaultImg;
        } else {
            imageUrl = clothing.image_url;
        }
        
        // 处理标签
        let tagsHtml = '';
        if (clothing.tags_array && clothing.tags_array.length > 0) {
            tagsHtml = '<div class="tag-list">';
            clothing.tags_array.forEach(tag => {
                tagsHtml += `<span class="tag">${tag}</span>`;
            });
            tagsHtml += '</div>';
        }
        
        const html = `
            <div class="clothing-details">
                <div class="clothing-image-container">
                    <img src="${imageUrl}" class="clothing-image" alt="衣物图片" onerror="this.src='${defaultImg}'">
                </div>
                <div class="clothing-info">
                    <div class="clothing-name">${clothing.name}</div>
                    <div class="clothing-id">ID: ${clothing.id}</div>
                    <div class="clothing-meta">
                        <div class="meta-item">
                            <span class="meta-label">分类:</span>
                            <span class="category-badge">${categoryText}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">创建时间:</span>
                            <span>${this.formatDate(clothing.created_at)}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">最后更新:</span>
                            <span>${clothing.updated_at ? this.formatDate(clothing.updated_at) : '无'}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">标签:</span>
                            <span>${clothing.tags || '无'}</span>
                        </div>
                    </div>
                    ${tagsHtml}
                    <div class="clothing-actions">
                        <button class="clothing-action-btn edit-btn" onclick="ClothingDetails.editClothing()">编辑衣物</button>
                        <button class="clothing-action-btn delete-btn" onclick="ClothingDetails.deleteClothing()">删除衣物</button>
                    </div>
                </div>
            </div>
        `;
        
        this.elements.clothingDetailContainer.innerHTML = html;
        
        // 给衣物图片添加点击事件，支持查看大图
        try {
            console.log('使用内部图片查看器绑定图片预览');
            this.imageViewer.bindImages('.clothing-image');
            console.log('成功绑定图片预览');
        } catch (error) {
            console.error('绑定图片预览失败:', error);
        }
    },
    
    /**
     * 渲染衣物描述信息
     * @param {Object} clothing 衣物数据
     */
    renderClothingDescription: function(clothing) {
        if (!clothing || !clothing.description) {
            this.elements.descriptionContainer.innerHTML = '<div class="empty-text">暂无详细属性</div>';
            return;
        }
        
        // 处理description字段，可能是对象或JSON字符串
        let description = clothing.description;
        if (typeof description === 'string') {
            try {
                description = JSON.parse(description);
            } catch (e) {
                // 如果解析失败，使用原始字符串
            }
        }
        
        // 如果description不是对象，则直接显示
        if (typeof description !== 'object' || description === null) {
            this.elements.descriptionContainer.innerHTML = `<div>${description}</div>`;
            return;
        }
        
        // 生成描述信息HTML
        let html = '';
        for (const key in description) {
            const value = description[key];
            html += `
                <div class="description-item">
                    <span class="description-label">${key}:</span>
                    <span>${value}</span>
                </div>
            `;
        }
        
        if (html === '') {
            html = '<div class="empty-text">暂无详细属性</div>';
        }
        
        this.elements.descriptionContainer.innerHTML = html;
    },
    
    /**
     * 渲染用户信息
     * @param {Object} user 用户数据
     */
    renderUserInfo: function(user) {
        if (!user) {
            this.elements.userInfoContainer.innerHTML = '<div class="empty-text">用户数据不存在</div>';
            return;
        }
        
        const gender = this.getGenderText(user.gender);
        
        // 本地默认头像
        const defaultAvatar = 'images/default-avatar.png';
        
        // 检查头像URL并处理微信临时文件路径
        let avatarUrl;
        if (!user.avatar_url || user.avatar_url.trim() === '') {
            avatarUrl = defaultAvatar;
        } else if (user.avatar_url.startsWith('wxfile://')) {
            // 不支持的微信临时文件URL，使用默认头像
            avatarUrl = defaultAvatar;
        } else {
            avatarUrl = user.avatar_url;
        }
        
        const html = `
            <h3>所属用户信息</h3>
            <div class="user-info-section">
                <div class="user-info-header">
                    <img src="${avatarUrl}" class="user-avatar" alt="用户头像" onerror="this.src='${defaultAvatar}'">
                    <div class="user-name">${user.nickname || '未设置昵称'}</div>
                </div>
                <div class="user-meta">
                    <div class="user-meta-item">
                        <span class="meta-label">用户ID:</span>
                        <span>${user.id}</span>
                    </div>
                    <div class="user-meta-item">
                        <span class="meta-label">性别:</span>
                        <span>${gender}</span>
                    </div>
                    <div class="user-meta-item">
                        <span class="meta-label">创建时间:</span>
                        <span>${this.formatDate(user.created_at)}</span>
                    </div>
                </div>
                <div style="margin-top: 10px;">
                    <button class="clothing-action-btn view-btn" onclick="window.location.href='user_details.html?id=${user.id}'">查看用户详情</button>
                </div>
            </div>
        `;
        
        this.elements.userInfoContainer.innerHTML = html;
    },
    
    /**
     * 编辑衣物
     */
    editClothing: function() {
        window.location.href = `clothing_edit.html?id=${this.clothingId}`;
    },
    
    /**
     * 删除衣物
     */
    deleteClothing: function() {
        if (!confirm('确定要删除该衣物吗？此操作不可恢复。')) {
            return;
        }
        
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        fetch(`${Auth.apiBaseUrl}/delete_admin_clothing.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': token
            },
            body: JSON.stringify({
                id: this.clothingId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '操作失败');
            }
            
            alert(data.msg || '删除成功');
            window.location.href = 'clothing_list.html';
        })
        .catch(error => {
            console.error('删除衣物失败:', error);
            alert(`操作失败: ${error.message}`);
        });
    },
    
    /**
     * 获取分类文本
     * @param {String} category 分类代码
     * @returns {String} 分类文本
     */
    getCategoryText: function(category) {
        const categoryMap = {
            'tops': '上衣',
            'pants': '裤子',
            'skirts': '裙子',
            'coats': '外套',
            'shoes': '鞋子',
            'bags': '包包',
            'accessories': '配饰'
        };
        
        return categoryMap[category] || category;
    },
    
    /**
     * 获取性别文本
     * @param {Number} gender 性别码
     * @returns {String} 性别文本
     */
    getGenderText: function(gender) {
        switch(parseInt(gender)) {
            case 1: return '男';
            case 2: return '女';
            default: return '未知';
        }
    },
    
    /**
     * 格式化日期显示
     * @param {String} dateStr 日期字符串
     * @returns {String} 格式化的日期
     */
    formatDate: function(dateStr) {
        if (!dateStr) return '未知';
        
        const date = new Date(dateStr);
        return isNaN(date.getTime()) 
            ? dateStr 
            : date.getFullYear() + '-' + 
              String(date.getMonth() + 1).padStart(2, '0') + '-' + 
              String(date.getDate()).padStart(2, '0') + ' ' +
              String(date.getHours()).padStart(2, '0') + ':' +
              String(date.getMinutes()).padStart(2, '0');
    }
}; 