const app = getApp();

Page({
  data: {
    tags: [],
    tagGroups: [],
    loading: true,
    loadFailed: false
  },

  onLoad: function(options) {
    // 加载标签数据
    this.loadTagData();
  },

  onShow: function() {
    // 如果设置了需要刷新标签
    if (app.globalData.needRefreshTags) {
      this.loadTagData();
      app.globalData.needRefreshTags = false;
    }
  },

  // 加载标签数据
  loadTagData: function() {
    this.setData({ loading: true, loadFailed: false });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothes.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      data: {
        all_clothes: 1 // 获取所有衣物
      },
      success: (res) => {
        if (res.statusCode === 200 && !res.data.error) {
          const clothes = res.data.data || [];
          this.processTagsFromClothes(clothes);
        } else {
          this.setData({ 
            loading: false, 
            loadFailed: true 
          });
          wx.showToast({
            title: '加载失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        this.setData({ 
          loading: false, 
          loadFailed: true 
        });
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 处理从衣物中提取的标签
  processTagsFromClothes: function(clothes) {
    // 收集所有标签及其使用次数
    const tagCount = {};
    const tagItemMap = {};
    
    // 过滤掉无效的衣物数据
    const validClothes = clothes.filter(item => item && item.id);
    
    validClothes.forEach(item => {
      if (item.tags) {
        const tags = item.tags.split(',');
        tags.forEach(tag => {
          tag = tag.trim();
          // 过滤标签，只保留中文字符
          tag = this.filterTagToChineseOnly(tag);
          if (tag) {
            // 增加标签计数
            tagCount[tag] = (tagCount[tag] || 0) + 1;
            
            // 关联标签到衣物
            if (!tagItemMap[tag]) {
              tagItemMap[tag] = [];
            }
            tagItemMap[tag].push(item);
          }
        });
      }
    });
    
    // 转换为数组并按使用次数排序
    const tagArray = Object.keys(tagCount).map(tag => ({
      name: tag,
      count: tagCount[tag],
      items: tagItemMap[tag]
    })).sort((a, b) => b.count - a.count);
    
    // 对标签进行分组
    const groups = this.groupTags(tagArray);
    
    this.setData({
      tags: tagArray,
      tagGroups: groups,
      loading: false
    });
  },
  
  // 过滤标签，只保留中文字符
  filterTagToChineseOnly: function(tag) {
    if (!tag) return '';
    // 使用正则表达式匹配中文字符
    const chineseChars = tag.match(/[\u4e00-\u9fa5]+/g);
    if (!chineseChars) return '';
    return chineseChars.join('');
  },
  
  // 对标签进行分组
  groupTags: function(tags) {
    // 预定义的标签分组
    const groupDefinitions = [
      { name: '季节', keywords: ['春季', '夏季', '秋季', '冬季'] },
      { name: '场合', keywords: ['休闲', '通勤', '派对', '运动', '正式'] },
      { name: '风格', keywords: ['简约', '复古', '优雅', '街头', '学院'] },
      { 
        name: '适合天气', 
        keywords: ['晴天', '雨天', '阴天', '多云', '闷热', '寒冷', '微凉'],
        // 添加正则模式匹配
        patterns: [/适合.*天气/, /天气.*适合/, /晴天/, /雨天/, /阴天/, /多云/, /闷热/, /寒冷/, /微凉/]
      },
      { name: '搭配', keywords: ['百搭', '搭配', '内搭', '叠穿'] }
    ];
    
    // 创建分组结果
    const groups = groupDefinitions.map(group => ({
      name: group.name,
      tags: []
    }));
    
    // 添加其他组
    groups.push({ name: '其他', tags: [] });
    
    // 遍历标签并分配到相应组
    tags.forEach(tag => {
      let assigned = false;
      
      // 检查标签是否属于预定义分组
      for (let i = 0; i < groupDefinitions.length; i++) {
        const group = groupDefinitions[i];
        
        // 检查关键词匹配
        for (let j = 0; j < group.keywords.length; j++) {
          if (tag.name.includes(group.keywords[j])) {
            groups[i].tags.push(tag);
            assigned = true;
            break;
          }
        }
        
        // 如果有正则模式，则尝试匹配
        if (!assigned && group.patterns) {
          for (let j = 0; j < group.patterns.length; j++) {
            if (group.patterns[j].test(tag.name)) {
              groups[i].tags.push(tag);
              assigned = true;
              break;
            }
          }
        }
        
        if (assigned) break;
      }
      
      // 如果没有分配到任何组，则添加到"其他"组
      if (!assigned) {
        groups[groups.length - 1].tags.push(tag);
      }
    });
    
    // 过滤掉没有标签的组，并重新排序确保"适合天气"排在最前面
    const filteredGroups = groups.filter(group => group.tags.length > 0);

    // 自定义排序，将"适合天气"排在第一位
    filteredGroups.sort((a, b) => {
      if (a.name === '适合天气') return -1;
      if (b.name === '适合天气') return 1;
      return 0;
    });
    
    return filteredGroups;
  },
  
  // 点击标签
  onTagTap: function(e) {
    const { tag } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/clothing-tags/detail/detail?tag=${encodeURIComponent(tag)}`
    });
  },
  
  // 跳转到标签更新页面
  goToUpdateTags: function() {
    wx.navigateTo({
      url: '/pages/clothing-tags/update/update'
    });
  },
  
  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadTagData();
    wx.stopPullDownRefresh();
  }
})
