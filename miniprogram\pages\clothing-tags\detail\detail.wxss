.container {
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  min-height: 100vh;
  box-sizing: border-box;
  background-color: #f8f8f8;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.back-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
}

.title-container {
  flex: 1;
}

.tag-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #888;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.error-text {
  font-size: 30rpx;
  color: #666;
  margin: 30rpx 0;
}

.retry-button {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #fff;
  color: #000000;
  font-size: 28rpx;
  border-radius: 40rpx;
  border: 1px solid #ddd;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
}

/* 衣物列表 */
.clothes-container {
  flex: 1;
  padding-bottom: 120rpx;
}

.clothes-list {
  display: flex;
  flex-direction: column;
}

.clothing-item {
  display: flex;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.clothing-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.clothing-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.clothing-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.clothing-category {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.clothing-tags {
  display: flex;
  flex-wrap: wrap;
}

.clothing-tag {
  font-size: 24rpx;
  color: #000000;
  background-color: #EFF3F9;
  border-radius: 20rpx;
  padding: 6rpx 16rpx;
  margin-right: 12rpx;
  margin-bottom: 12rpx;
}

/* 底部按钮 */
.bottom-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20rpx 30rpx 50rpx;
  background: linear-gradient(to top, rgba(248, 248, 248, 1), rgba(248, 248, 248, 0.9));
  box-sizing: border-box;
  z-index: 10;
}

.update-button {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000000;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.2);
} 