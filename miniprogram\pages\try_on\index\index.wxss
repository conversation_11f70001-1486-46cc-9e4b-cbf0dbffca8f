/* 页面容器 */
page {
  background-color: #f9f9f9;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 试衣次数显示 */
.try-on-count-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background-color: white;
  border-radius: 12px;
  margin: 0 12px 8px 12px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  position: relative;
}

.count-item {
  display: flex;
  align-items: center;
}

.count-label {
  font-size: 13px;
  color: #666;
  margin-right: 5px;
}

.count-value {
  font-size: 15px;
  font-weight: bold;
  color: #333;
}

.buy-count-btn {
  background-color: #000000;
  color: white;
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 15px;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

/* 试衣须知按钮 */
.guide-btn {
  background-color: transparent;
  border: 1px solid #000000;
  color: #000000;
  font-size: 12px;
  padding: 3px 10px;
  border-radius: 15px;
  margin-left: 8px;
}

/* 试衣须知弹窗 */
.guide-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.guide-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 600rpx;
  max-height: 80%;
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  z-index: 1001;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.guide-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.guide-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.guide-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.guide-close {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  color: #999;
}

/* 试衣须知弹窗的滚动区域 */
.guide-scroll {
  flex: 1;
  padding: 15px;
  max-height: 60vh;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

.guide-section {
  margin-bottom: 20px;
  width: 100%;
  box-sizing: border-box;
}

.guide-section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  border-left: 3px solid #000;
  padding-left: 10px;
  box-sizing: border-box;
}

.guide-item {
  margin-bottom: 12px;
  width: 100%;
  box-sizing: border-box;
}

.guide-item-title {
  font-size: 14px;
  font-weight: 500;
  color: #444;
  margin-bottom: 4px;
  box-sizing: border-box;
}

.guide-item-content {
  font-size: 13px;
  color: #666;
  line-height: 1.6;
  width: 100%;
  word-wrap: break-word;
  word-break: break-all;
  box-sizing: border-box;
  padding-right: 15px;
  overflow-wrap: break-word;
}

.guide-item-content view {
  margin-bottom: 4px;
  width: 100%;
  box-sizing: border-box;
}

/* 试穿结果 */
.try-on-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 15px;
}

.result-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 15px;
  color: #333;
}

.result-photo-container {
  width: 100%;
  max-width: 320px;
  aspect-ratio: 4/5;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  background-color: #fff;
  margin-bottom: 20px;
}

.result-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.result-actions {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 320px;
  gap: 10px;
  margin-top: 5px;
}

.action-button {
  border-radius: 20px;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
  border: none;
  margin: 0;
  padding: 0 15px;
}

.action-button.primary {
  background-color: #1aad19;
  color: white;
}

.action-button.secondary {
  background-color: #07c160;
  color: white;
}

.action-button.default {
  background-color: #f7f7f7;
  color: #333;
  border: 1px solid #ddd;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  overflow-y: auto;
  padding-top: 10px; /* 减小上边距，使照片列表上移 */
  padding-bottom: 150px; /* 为底部导航栏和按钮留出更多空间 */
}

/* 照片网格 */
.photos-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px; /* 减少间距，更紧凑 */
  padding: 12px;
}

.photo-item {
  aspect-ratio: 4/5; /* 调整为更高一点的比例 */
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: relative;
  background-color: #f0f0f0;
}

.photo-item.selected {
  border: 3px solid #000;
  box-shadow: 0 4px 8px rgba(0,0,0,0.15); /* 增加选中时的阴影 */
}

.photo-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: flex-start; /* 顶部对齐 */
  justify-content: center;
  background-color: #f5f5f5;
}

/* 添加专用的竖向照片容器样式 */
.photo-container:not(.landscape) {
  padding-top: 0;
  align-items: flex-start;
  /* 让内容略微上移，但不要过度 */
  padding-bottom: 15%; /* 底部添加内边距，使内容向上移 */
}

.photo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: 50% 30%; /* 居中但偏上方 */
  background-color: #f5f5f5;
  transition: transform 0.2s ease;
}

/* 横向图片容器 */
.photo-container.landscape {
  padding: 5px 0;
  align-items: center;
}

/* 横向图片样式 */
.photo-image.landscape-image {
  object-fit: contain;
  max-height: 100%;
  max-width: 100%;
  object-position: center;
}

/* 竖向图片样式 */
.photo-image.portrait-image {
  transform: none; /* 不放大 */
  object-position: 50% 20%; /* 水平居中，垂直位置偏上 */
  height: 120%; /* 增加高度以显示更多内容 */
  width: 100%;
  margin-top: 0%; /* 上移，显示更多上半身 */
}

.photo-type {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(0,0,0,0.7);
  color: white;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 10px;
  z-index: 10;
}

/* 空状态提示 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.empty-icon {
  width: 150rpx;
  height: 150rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  max-width: 280px;
  height: 44px;
  background-color: #000;
  color: #fff;
  border-radius: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  margin-top: 10px;
}

.add-photo-btn {
  background-color: #000;
  color: #fff;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

/* 添加照片按钮样式 */
.add-photo-item {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
  border: 2px dashed #ccc;
  box-shadow: none;
}

.add-photo-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.add-icon {
  font-size: 36px;
  color: #aaa;
  line-height: 1;
  margin-bottom: 8px;
}

.add-text {
  font-size: 14px;
  color: #888;
}

/* 底部按钮 */
.bottom-bar {
  position: fixed;
  bottom: -5px; /* 移到页面最底部 */
  left: 0;
  right: 0;
  height: 70px;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  padding-bottom: calc(env(safe-area-inset-bottom) + 10px); /* 适应底部安全区域并增加内边距 */
}

.action-btn {
  width: 90%;
  height: 50px;
  border-radius: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.action-btn.active {
  background-color: #000;
  color: #fff;
}

.action-btn.disabled {
  background-color: #ccc;
  color: #666;
}

.icon-tshirt {
  margin-right: 8px;
}

.icon-camera {
  font-size: 36px;
}

/* 修改试衣须知底部和"我知道了"按钮样式 */
.guide-footer {
  padding: 15px 15px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.guide-btn-confirm {
  background-color: #000000;
  color: white;
  font-size: 16px;
  width: 80%;
  padding: 10px 0;
  border-radius: 20px;
  text-align: center;
  font-weight: 500;
  border: none;
}

.container {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 180rpx; /* 增加底部内边距，确保不被底部按钮遮挡 */
  background-color: #f8f8f8;
  min-height: 100vh;
}

.header {
  margin-bottom: 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 15rpx 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
}

.search-btn {
  padding: 10rpx 30rpx;
  background-color: #000;
  color: #fff;
  font-size: 28rpx;
  border-radius: 6rpx;
}

.merchant-list {
  padding: 10rpx;
  margin-bottom: 10rpx; /* 减少底部边距 */
  padding-bottom: 60rpx; /* 减少底部内边距 */
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.merchant-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.merchant-item:last-child {
  border-bottom: none;
}

.merchant-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f0f0f0;
}

.merchant-info {
  flex: 1;
}

.merchant-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.merchant-status {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.status-tag {
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 5rpx 15rpx;
  border-radius: 30rpx;
}

.status-share {
  color: #000;
  background-color: rgba(0, 0, 0, 0.1);
}

.merchant-arrow {
  width: 40rpx;
  height: 40rpx;
  color: #ccc;
}

.photo-section {
  background-color: #fff;
  padding: 30rpx 20rpx;
  border-radius: 10rpx;
  margin-top: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.view-more {
  font-size: 26rpx;
  color: #000000;
}

.photo-list {
  display: flex;
  flex-wrap: wrap;
}

.photo-item {
  width: calc(33.33% - 10rpx);
  margin-right: 15rpx;
  margin-bottom: 15rpx;
  height: 220rpx;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}

.photo-item:nth-child(3n) {
  margin-right: 0;
}

.photo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.add-photo {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 1rpx dashed #ddd;
}

.add-icon {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 10rpx;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 0;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
  margin-bottom: 60rpx; /* 减少底部边距 */
}

.pagination-btn {
  padding: 10rpx 30rpx;
  background-color: #000000;
  color: #fff;
  font-size: 28rpx;
  border-radius: 6rpx;
  margin: 0 20rpx;
}

.pagination-btn.disabled {
  background-color: #ddd;
  color: #999;
}

.pagination-info {
  font-size: 28rpx;
  color: #666;
}

/* 衣物数量标签 */
.clothes-count-tag {
  font-size: 24rpx;
  color: #666;
  background-color: #f8f8f8;
  padding: 5rpx 15rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clothes-count-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 4rpx;
}

.clothes-count-text {
  font-weight: 500;
}

/* 底部固定购买次数栏 */
.try-on-credits-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px; /* 减小高度 */
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
  z-index: 100;
  padding-bottom: 0; /* 移除底部内边距 */
  border-top: 1px solid #f0f0f0;
}

.credits-container {
  display: flex;
  align-items: center;
}

.credits-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}

.credits-info {
  display: flex;
  flex-direction: column;
}

.credits-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.credits-counts {
  display: flex;
  align-items: center;
}

.free-count {
  font-size: 13px;
  color: #333;
  font-weight: 500;
}

.count-separator {
  margin: 0 6px;
  color: #ddd;
}

.paid-count {
  font-size: 13px;
  color: #333;
  font-weight: 500;
}

.buy-credits-btn {
  background-color: #000000;
  color: #ffffff;
  font-size: 14px;
  padding: 6px 16px;
  border-radius: 20px;
  font-weight: 500;
}

/* 移除底部安全区域 */
.bottom-safe-area {
  display: none; /* 隐藏这个元素 */
}

/* 调整主内容区域的底部内边距，确保不被固定底部栏遮挡 */
.container {
  padding-bottom: 50px; /* 仅保留与购买次数栏相同的高度 */
} 