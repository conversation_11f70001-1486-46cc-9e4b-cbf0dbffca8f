<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 验证管理员权限
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证管理员token
$adminData = $auth->verifyAdminToken($token);
if (!$adminData) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取数据库连接
$db = new Database();
$conn = $db->getConnection();

// 获取查询参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 10;
$search = isset($_GET['search']) ? $_GET['search'] : '';
$status = isset($_GET['status']) ? intval($_GET['status']) : null;

// 验证分页参数
if ($page < 1) $page = 1;
if ($per_page < 1) $per_page = 10;
if ($per_page > 100) $per_page = 100;

// 计算偏移量
$offset = ($page - 1) * $per_page;

// 构建SQL查询 - 先获取记录总数
$countQuery = "SELECT COUNT(*) as total FROM recommended_outfits WHERE 1=1";
$paramsCount = [];

// 构建SQL查询 - 查询具体数据
$query = "SELECT ro.*, roc.name as category_name 
          FROM recommended_outfits ro 
          LEFT JOIN recommended_outfit_categories roc ON ro.category_id = roc.id 
          WHERE 1=1";
$params = [];

// 添加搜索条件
if (!empty($search)) {
    $searchParam = "%$search%";
    $countQuery .= " AND (name LIKE ? OR description LIKE ?)";
    $query .= " AND (ro.name LIKE ? OR ro.description LIKE ?)";
    $paramsCount[] = $searchParam;
    $paramsCount[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
}

// 添加状态过滤条件
if ($status !== null) {
    $countQuery .= " AND status = ?";
    $query .= " AND ro.status = ?";
    $paramsCount[] = $status;
    $params[] = $status;
}

// 添加排序和分页
$query .= " ORDER BY ro.sort_order ASC, ro.created_at DESC LIMIT ? OFFSET ?";
$params[] = $per_page;
$params[] = $offset;

try {
    // 执行总数查询
    $countStmt = $conn->prepare($countQuery);
    if (!empty($paramsCount)) {
        $i = 1;
        foreach ($paramsCount as $param) {
            $countStmt->bindValue($i++, $param);
        }
    }
    $countStmt->execute();
    $totalRecords = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 执行数据查询
    $stmt = $conn->prepare($query);
    if (!empty($params)) {
        $i = 1;
        foreach ($params as $index => $param) {
            // 为LIMIT和OFFSET参数指定整数类型
            if ($index >= count($params) - 2) {
                $stmt->bindValue($i++, $param, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($i++, $param);
            }
        }
    }
    $stmt->execute();
    $outfits = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 对每个穿搭对象进行处理，可能添加额外信息
    foreach ($outfits as &$outfit) {
        // 获取统计数据
        $statsQuery = "SELECT view_count, copy_link_count FROM recommended_outfit_stats WHERE outfit_id = ? LIMIT 1";
        $statsStmt = $conn->prepare($statsQuery);
        $statsStmt->bindValue(1, $outfit['id']);
        $statsStmt->execute();
        $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($stats) {
            $outfit['view_count'] = $stats['view_count'];
            $outfit['copy_link_count'] = $stats['copy_link_count'];
        } else {
            $outfit['view_count'] = 0;
            $outfit['copy_link_count'] = 0;
        }
        
        // 计算商品数量
        $itemsQuery = "SELECT COUNT(*) as item_count FROM recommended_outfit_items WHERE outfit_id = ?";
        $itemsStmt = $conn->prepare($itemsQuery);
        $itemsStmt->bindValue(1, $outfit['id']);
        $itemsStmt->execute();
        $itemsCount = $itemsStmt->fetch(PDO::FETCH_ASSOC);
        $outfit['item_count'] = $itemsCount['item_count'];
    }
    
    // 计算总页数
    $totalPages = ceil($totalRecords / $per_page);
    
    // 构建分页信息
    $pagination = [
        'total' => $totalRecords,
        'per_page' => $per_page,
        'current_page' => $page,
        'total_pages' => $totalPages,
        'has_more' => $page < $totalPages
    ];
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'data' => $outfits,
        'pagination' => $pagination
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '数据库错误: ' . $e->getMessage()]);
} 