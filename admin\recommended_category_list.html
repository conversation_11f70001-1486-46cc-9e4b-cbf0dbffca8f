<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推荐穿搭分类管理</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        .status-badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: normal;
            display: inline-block;
        }
        .status-active {
            background-color: #52c41a;
            color: white;
        }
        .status-inactive {
            background-color: #8c8c8c;
            color: white;
        }
        .outfit-count {
            background-color: #f5f5f5;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 12px;
            color: #595959;
        }
        .filter-container {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .filter-input {
            width: 200px;
            padding: 6px 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        .filter-select {
            width: 120px;
            padding: 6px 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        .filter-btn {
            padding: 6px 12px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .filter-btn:hover {
            background-color: #40a9ff;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        .table th, .table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }
        .table th {
            background-color: #fafafa;
            font-weight: 500;
        }
        .table tr:hover {
            background-color: #fafafa;
        }
        .pagination {
            display: flex;
            list-style: none;
            padding: 0;
            margin-top: 20px;
            justify-content: flex-end;
        }
        .pagination li {
            margin: 0 5px;
        }
        .pagination a {
            display: inline-block;
            padding: 5px 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            text-decoration: none;
            color: #1890ff;
        }
        .pagination a:hover {
            border-color: #1890ff;
        }
        .pagination .active a {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        .btn {
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            text-decoration: none;
            display: inline-block;
        }
        .btn-add {
            background-color: #52c41a;
            color: white;
            border: none;
        }
        .btn-add:hover {
            background-color: #73d13d;
        }
        .btn-edit {
            background-color: #1890ff;
            color: white;
            border: none;
        }
        .btn-edit:hover {
            background-color: #40a9ff;
        }
        .btn-delete {
            background-color: #ff4d4f;
            color: white;
            border: none;
        }
        .btn-delete:hover {
            background-color: #ff7875;
        }
        .btn-reset {
            background-color: #f5f5f5;
            color: #595959;
            border: 1px solid #d9d9d9;
        }
        .btn-reset:hover {
            background-color: #e8e8e8;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            border-radius: 4px;
            width: 400px;
            max-width: 90%;
        }
        .modal-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 15px;
        }
        .modal-body {
            margin-bottom: 20px;
        }
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        .close {
            float: right;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>推荐穿搭分类管理</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <!-- 操作栏 -->
            <div class="action-bar">
                <button id="addCategoryBtn" class="btn btn-add">添加分类</button>
            </div>
            
            <!-- 过滤器 -->
            <div class="filter-container">
                <input type="text" id="searchInput" class="filter-input" placeholder="搜索分类名称或描述">
                <select id="statusFilter" class="filter-select">
                    <option value="">所有状态</option>
                    <option value="1">启用</option>
                    <option value="0">禁用</option>
                </select>
                <button id="searchBtn" class="filter-btn">搜索</button>
                <button id="resetFilterBtn" class="btn btn-reset">重置</button>
            </div>
            
            <!-- 数据表格 -->
            <div class="card">
                <div id="tableLoading" class="loading-indicator">加载中...</div>
                <div id="tableError" class="error-message"></div>
                
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>名称</th>
                            <th>描述</th>
                            <th>穿搭数量</th>
                            <th>排序</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="categoryTableBody">
                        <!-- 分类数据将通过JS动态加载 -->
                    </tbody>
                </table>
                
                <!-- 分页 -->
                <div class="pagination-container">
                    <div id="paginationInfo" class="pagination-info">加载中...</div>
                    <ul id="pagination" class="pagination"></ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 删除确认模态框 -->
    <div id="deleteCategoryModal" class="modal">
        <div class="modal-content">
            <span class="close" id="closeModalBtn">&times;</span>
            <h3 class="modal-title">确认删除</h3>
            <div class="modal-body">
                <p>您确定要删除分类"<span id="deleteCategoryName"></span>"吗？</p>
                <p class="text-danger">注意：只有没有关联穿搭的分类才能被删除。</p>
            </div>
            <div class="modal-footer">
                <button id="cancelDeleteBtn" class="btn btn-reset">取消</button>
                <button id="confirmDeleteBtn" class="btn btn-delete">确认删除</button>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // 添加缺失的config对象和函数
        const config = {
            apiBaseUrl: '../login_backend'
        };
        
        function getAdminToken() {
            return Auth.getToken();
        }
        
        function checkAdminLogin() {
            return Auth.isLoggedIn();
        }
        
        function showToast(message, type) {
            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `message message-${type}`;
            toast.textContent = message;
            
            // 添加到body
            document.body.appendChild(toast);
            
            // 3秒后自动移除
            setTimeout(() => {
                toast.classList.add('message-hide');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }
        
        // 创建CategoryManager对象
        const CategoryManager = {
            currentPage: 1,
            pageSize: 10,
            totalPages: 1,
            totalItems: 0,
            categories: [],
            selectedCategoryId: null,
            
            // 初始化函数
            init: function() {
                this.bindEvents();
                this.loadCategories();
            },
            
            // 绑定事件处理函数
            bindEvents: function() {
                // 添加分类按钮
                $('#addCategoryBtn').click(() => {
                    window.location.href = 'recommended_category_edit.html';
                });
                
                // 搜索按钮
                $('#searchBtn').click(() => {
                    this.currentPage = 1;
                    this.loadCategories();
                });
                
                // 重置按钮
                $('#resetFilterBtn').click(() => {
                    $('#searchInput').val('');
                    $('#statusFilter').val('');
                    this.currentPage = 1;
                    this.loadCategories();
                });
                
                // 删除模态框事件
                $('#closeModalBtn, #cancelDeleteBtn').click(() => {
                    $('#deleteCategoryModal').hide();
                });
                
                $('#confirmDeleteBtn').click(() => {
                    this.deleteCategory();
                });
            },
            
            // 加载分类列表
            loadCategories: function() {
                const searchText = $('#searchInput').val().trim();
                const status = $('#statusFilter').val();
                
                $('#tableLoading').show();
                $('#tableError').hide();
                $('#categoryTableBody').empty();
                
                const params = new URLSearchParams();
                params.append('page', this.currentPage);
                params.append('per_page', this.pageSize);
                
                if (searchText) {
                    params.append('search', searchText);
                }
                
                if (status !== '') {
                    params.append('status', status);
                }
                
                // 发送API请求
                fetch(`${config.apiBaseUrl}/admin_get_recommended_categories.php?${params.toString()}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + Auth.getToken()
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    $('#tableLoading').hide();
                    
                    if (data.error) {
                        $('#tableError').text(data.msg || '加载分类列表失败').show();
                        return;
                    }
                    
                    this.categories = data.data || [];
                    this.totalItems = data.pagination?.total || 0;
                    this.totalPages = data.pagination?.total_pages || 1;
                    this.currentPage = data.pagination?.current_page || 1;
                    
                    this.renderCategories();
                    this.renderPagination();
                })
                .catch(error => {
                    $('#tableLoading').hide();
                    $('#tableError').text(`加载失败: ${error.message}`).show();
                    console.error('加载分类列表失败:', error);
                });
            },
            
            // 渲染分类列表
            renderCategories: function() {
                const tableBody = $('#categoryTableBody');
                tableBody.empty();
                
                if (this.categories.length === 0) {
                    tableBody.html('<tr><td colspan="8" style="text-align:center;padding:20px;">暂无数据</td></tr>');
                    return;
                }
                
                this.categories.forEach(category => {
                    const row = $('<tr></tr>');
                    
                    // 格式化日期
                    const createdDate = new Date(category.created_at);
                    const formattedDate = `${createdDate.getFullYear()}-${String(createdDate.getMonth() + 1).padStart(2, '0')}-${String(createdDate.getDate()).padStart(2, '0')} ${String(createdDate.getHours()).padStart(2, '0')}:${String(createdDate.getMinutes()).padStart(2, '0')}`;
                    
                    row.html(`
                        <td>${category.id}</td>
                        <td>${category.name}</td>
                        <td>${category.description || '-'}</td>
                        <td>${category.outfit_count || 0}</td>
                        <td>${category.sort_order || 0}</td>
                        <td>
                            <span class="status-badge ${category.status == 1 ? 'status-active' : 'status-inactive'}">
                                ${category.status == 1 ? '启用' : '禁用'}
                            </span>
                        </td>
                        <td>${formattedDate}</td>
                        <td>
                            <a href="recommended_category_edit.html?id=${category.id}" class="btn btn-edit">编辑</a>
                            ${category.outfit_count > 0 ? 
                                `<button class="btn btn-delete" disabled title="该分类下有穿搭，无法删除">删除</button>` : 
                                `<button class="btn btn-delete" data-id="${category.id}" data-name="${category.name}">删除</button>`
                            }
                        </td>
                    `);
                    
                    tableBody.append(row);
                });
                
                // 绑定删除按钮事件
                $('.btn-delete').not('[disabled]').click(event => {
                    const button = $(event.currentTarget);
                    const id = button.data('id');
                    const name = button.data('name');
                    this.showDeleteConfirm(id, name);
                });
            },
            
            // 渲染分页
            renderPagination: function() {
                const pagination = $('#pagination');
                pagination.empty();
                
                $('#paginationInfo').text(`共 ${this.totalItems} 条，第 ${this.currentPage}/${this.totalPages} 页`);
                
                if (this.totalPages <= 1) {
                    return;
                }
                
                // 上一页
                const prevBtn = $(`<li><a href="#" ${this.currentPage <= 1 ? 'class="disabled"' : ''}>上一页</a></li>`);
                if (this.currentPage > 1) {
                    prevBtn.click(() => {
                        this.currentPage--;
                        this.loadCategories();
                        return false;
                    });
                }
                pagination.append(prevBtn);
                
                // 页码
                const startPage = Math.max(1, this.currentPage - 2);
                const endPage = Math.min(this.totalPages, startPage + 4);
                
                for (let i = startPage; i <= endPage; i++) {
                    const pageBtn = $(`<li ${i === this.currentPage ? 'class="active"' : ''}><a href="#">${i}</a></li>`);
                    if (i !== this.currentPage) {
                        pageBtn.click(() => {
                            this.currentPage = i;
                            this.loadCategories();
                            return false;
                        });
                    }
                    pagination.append(pageBtn);
                }
                
                // 下一页
                const nextBtn = $(`<li><a href="#" ${this.currentPage >= this.totalPages ? 'class="disabled"' : ''}>下一页</a></li>`);
                if (this.currentPage < this.totalPages) {
                    nextBtn.click(() => {
                        this.currentPage++;
                        this.loadCategories();
                        return false;
                    });
                }
                pagination.append(nextBtn);
            },
            
            // 显示删除确认框
            showDeleteConfirm: function(id, name) {
                this.selectedCategoryId = id;
                $('#deleteCategoryName').text(name);
                $('#deleteCategoryModal').show();
            },
            
            // 删除分类
            deleteCategory: function() {
                if (!this.selectedCategoryId) {
                    return;
                }
                
                $('#confirmDeleteBtn').prop('disabled', true).text('删除中...');
                
                fetch(`${config.apiBaseUrl}/admin_delete_recommended_category.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + Auth.getToken()
                    },
                    body: JSON.stringify({ id: this.selectedCategoryId })
                })
                .then(response => response.json())
                .then(data => {
                    $('#deleteCategoryModal').hide();
                    $('#confirmDeleteBtn').prop('disabled', false).text('确认删除');
                    
                    if (data.error) {
                        showToast(data.msg || '删除失败', 'error');
                    } else {
                        showToast('删除成功', 'success');
                        this.loadCategories();
                    }
                })
                .catch(error => {
                    $('#deleteCategoryModal').hide();
                    $('#confirmDeleteBtn').prop('disabled', false).text('确认删除');
                    showToast(`删除失败: ${error.message}`, 'error');
                    console.error('删除分类失败:', error);
                });
            }
        };
    </script>
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/recommended_categories.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为recommended_category
            Sidebar.init('recommended_category');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化推荐穿搭分类列表
            if (typeof CategoryManager !== 'undefined') {
                // 创建一个RecommendedCategoryList对象，封装CategoryManager的功能
                window.RecommendedCategoryList = CategoryManager;
                
                // 调用初始化方法
                RecommendedCategoryList.init();
            } else {
                console.error('推荐穿搭分类列表功能未找到');
                document.getElementById('tableLoading').style.display = 'block';
                document.getElementById('tableLoading').textContent = '加载失败：推荐穿搭分类列表功能模块缺失';
            }
        });
    </script>
    
    <style>
        /* 消息提示样式 */
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 4px;
            z-index: 9999;
            animation: messageIn 0.3s ease;
        }
        
        .message-success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        
        .message-error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        
        .message-hide {
            animation: messageOut 0.3s ease forwards;
        }
        
        @keyframes messageIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes messageOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-20px); }
        }
        
        /* 加载指示器和错误消息样式 */
        .loading-indicator {
            text-align: center;
            padding: 20px;
            color: #8c8c8c;
            display: none;
        }
        
        .error-message {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }
        
        /* 页面特定样式 */
        .action-bar {
            margin-bottom: 20px;
            display: flex;
            justify-content: flex-end;
        }
        
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
        }
        
        .pagination-info {
            color: #8c8c8c;
            font-size: 13px;
        }
    </style>
</body>
</html> 