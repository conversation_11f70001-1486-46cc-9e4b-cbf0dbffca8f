<view class="container">
  <!-- 上传状态区域 -->
  <view class="status-area">
    <block wx:if="{{uploadStatus === 'waiting'}}">
      <view class="status-text">准备上传 {{selectedImages.length}} 张图片</view>
    </block>
    <block wx:elif="{{uploadStatus === 'uploading'}}">
      <view class="status-text">正在处理图片...</view>
      <view class="progress-bar">
        <view class="progress-inner" style="width: 50%;"></view>
      </view>
    </block>
    <block wx:elif="{{uploadStatus === 'complete'}}">
      <view class="status-text">已处理 {{uploadResults.total}} 张图片，成功 {{uploadResults.success}} 张</view>
      <view class="upload-actions">
        <view class="action-btn" bindtap="reChooseImages">重选图片</view>
      </view>
    </block>
  </view>

  <!-- 衣物列表区域 -->
  <scroll-view scroll-y="true" class="clothes-list">
    <block wx:for="{{clothingItems}}" wx:key="index">
      <view class="clothing-item {{currentEditIndex === index ? 'editing' : ''}}">
        <!-- 衣物预览 -->
        <view class="preview-section">
          <image class="clothing-image" src="{{item.imageUrl}}" mode="aspectFit"></image>
          <view class="clothing-info">
            <view class="clothing-name">{{item.name || '未命名衣物'}}</view>
            <view class="clothing-category">{{item.category ? (item.category === 'tops' ? '上衣' : item.category === 'pants' ? '裤子' : item.category === 'skirts' ? '裙子' : item.category === 'coats' ? '外套' : item.category === 'shoes' ? '鞋子' : item.category === 'bags' ? '包包' : item.category === 'accessories' ? '配饰' : '未分类') : '未分类'}}</view>
          </view>
          <view class="detail-btn" wx:if="{{currentEditIndex !== index}}" bindtap="editItem" data-index="{{index}}">详情</view>
        </view>

        <!-- 编辑区域 - 仅在编辑状态显示 -->
        <view class="edit-section" wx:if="{{currentEditIndex === index}}">
          <view class="edit-header">
            <view class="edit-title">编辑衣物信息</view>
            <view class="close-btn" bindtap="finishEdit">收起</view>
          </view>

          <!-- 名称输入 -->
          <view class="input-group">
            <view class="input-label">名称</view>
            <input class="input-control" value="{{item.name}}" bindinput="updateItemName" placeholder="衣物名称"/>
          </view>

          <!-- 类别选择 -->
          <view class="input-group">
            <view class="input-label">类别</view>
            <view class="category-options">
              <view wx:for="{{categories}}" wx:key="value" wx:for-item="category"
                    class="category-option {{item.category === category.value ? 'selected' : ''}}"
                    bindtap="updateItemCategory" data-value="{{category.value}}">
                {{category.name}}
              </view>
            </view>
          </view>

          <!-- 标签选择 -->
          <view class="input-group">
            <view class="input-label">标签</view>
            <view class="tags-options">
              <view wx:for="{{tags}}" wx:key="value" wx:for-item="tag"
                    class="tag-option {{item.selectedTags[tag.value] ? 'selected' : ''}}"
                    bindtap="toggleTag" data-value="{{tag.value}}">
                {{tag.name}}
              </view>
              <block wx:for="{{item.customTags}}" wx:key="value" wx:for-item="customTag">
                <view class="tag-option custom {{item.selectedTags[customTag.value] ? 'selected' : ''}}"
                      bindtap="toggleTag" data-value="{{customTag.value}}">
                  {{customTag.name}}
                </view>
              </block>
            </view>
          </view>

          <!-- 颜色输入 -->
          <view class="input-group">
            <view class="input-label">颜色</view>
            <input class="input-control" value="{{item.color}}" bindinput="updateItemColor" placeholder="衣物颜色"/>
          </view>

          <!-- 品牌输入 -->
          <view class="input-group">
            <view class="input-label">品牌</view>
            <input class="input-control" value="{{item.brand}}" bindinput="updateItemBrand" placeholder="衣物品牌"/>
          </view>

          <!-- 衣橱选择 -->
          <view class="input-group">
            <view class="input-label">衣橱</view>
            <picker bindchange="bindWardrobeChange" range="{{wardrobeList}}" range-key="name">
              <view class="wardrobe-picker">
                <text>{{item.wardrobeName || selectedWardrobeName}}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
          </view>

          <!-- 价格输入 -->
          <view class="input-group">
            <view class="input-label">价格</view>
            <input class="input-control" value="{{item.price}}" bindinput="updateItemPrice" placeholder="衣物价格" type="digit"/>
          </view>
        </view>
      </view>
    </block>
  </scroll-view>

  <!-- 底部按钮区域 -->
  <view class="bottom-actions">
    <button class="action-button primary" bindtap="saveAllItems">保存全部衣物</button>
  </view>
</view> 