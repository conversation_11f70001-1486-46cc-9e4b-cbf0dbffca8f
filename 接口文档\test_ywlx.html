<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>衣物分类API测试页面</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"] {
            display: block;
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        button {
            flex: 1;
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f0f8ff;
            border-left: 4px solid #4CAF50;
            border-radius: 4px;
            display: none;
        }
        .loading {
            text-align: center;
            margin: 20px 0;
            display: none;
        }
        .spinner {
            width: 40px;
            height: 40px;
            margin: 0 auto;
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #4CAF50;
            animation: spin 1s ease-in-out infinite;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .preview-container {
            margin-top: 20px;
            text-align: center;
        }
        #imagePreview {
            max-width: 100%;
            max-height: 300px;
            display: none;
            margin: 0 auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            margin-right: 5px;
        }
        .tab.active {
            background: #f0f8ff;
            border-color: #ddd;
            border-radius: 4px 4px 0 0;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .error {
            color: red;
            margin-top: 10px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>衣物分类API测试</h1>
        
        <div class="tabs">
            <div class="tab active" data-tab="upload">文件上传</div>
            <div class="tab" data-tab="url">图片URL</div>
            <div class="tab" data-tab="base64">Base64</div>
            <div class="tab" data-tab="wx">小程序格式</div>
        </div>
        
        <div class="tab-content active" id="upload">
            <div class="form-group">
                <label for="imageFile">选择衣物图片</label>
                <input type="file" id="imageFile" accept="image/*" onchange="previewImage(this)">
            </div>
        </div>
        
        <div class="tab-content" id="url">
            <div class="input-group">
                <label for="imageUrl">输入图片URL</label>
                <input type="text" id="imageUrl" placeholder="https://example.com/image.jpg" onchange="loadImageFromUrl()">
            </div>
        </div>
        
        <div class="tab-content" id="base64">
            <div class="input-group">
                <label for="imageBase64">输入Base64图片数据</label>
                <input type="text" id="imageBase64" placeholder="data:image/jpeg;base64,/9j..." onchange="loadImageFromBase64()">
            </div>
        </div>
        
        <div class="tab-content" id="wx">
            <div class="input-group">
                <label for="imageUrlWx">微信小程序请求格式（输入图片URL）</label>
                <input type="text" id="imageUrlWx" placeholder="https://example.com/image.jpg" onchange="loadImageFromUrlWx()">
                <small>这个选项会模拟微信小程序的请求格式，直接发送JSON数据而不使用Content-Type: multipart/form-data</small>
            </div>
        </div>
        
        <div class="preview-container">
            <img id="imagePreview" alt="图片预览">
        </div>
        
        <div class="button-group">
            <button id="submitBtn" onclick="submitImage()">识别衣物类型</button>
            <button onclick="clearForm()">清除</button>
        </div>
        
        <div class="loading" id="loadingIndicator">
            <div class="spinner"></div>
            <p>正在识别中，请稍候...</p>
        </div>
        
        <div class="result" id="resultBox">
            <h3>识别结果</h3>
            <p>衣物类型: <strong id="clothingType"></strong></p>
            <p>类型解释: <span id="clothingTypeExplanation"></span></p>
            <p>API响应详情:</p>
            <pre id="apiResponse"></pre>
        </div>
    </div>

    <script>
        // API地址
        const API_URL = 'https://www.furrywoo.com/geminicdai/ywlx.php';
        
        // 衣物类型映射
        const clothingTypeMap = {
            'Upper': '上衣（Upper - 上半身服装）',
            'Lower': '下装（Lower - 下半身服装）',
            'Dress': '连衣裙（Dress - 全身服装）'
        };
        
        // 切换标签页
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
                
                // 清除之前的结果
                document.getElementById('resultBox').style.display = 'none';
            });
        });
        
        // 预览上传的图片
        function previewImage(input) {
            const preview = document.getElementById('imagePreview');
            const file = input.files[0];
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                }
                reader.readAsDataURL(file);
            }
        }
        
        // 从URL加载图片
        function loadImageFromUrl() {
            const url = document.getElementById('imageUrl').value;
            const preview = document.getElementById('imagePreview');
            
            if (url) {
                preview.src = url;
                preview.style.display = 'block';
                preview.onerror = function() {
                    alert('无法加载图片，请检查URL是否正确');
                    preview.style.display = 'none';
                };
            }
        }
        
        // 从Base64加载图片
        function loadImageFromBase64() {
            const base64 = document.getElementById('imageBase64').value;
            const preview = document.getElementById('imagePreview');
            
            if (base64) {
                preview.src = base64;
                preview.style.display = 'block';
                preview.onerror = function() {
                    alert('无法加载图片，请检查Base64格式是否正确');
                    preview.style.display = 'none';
                };
            }
        }
        
        // 从URL加载图片 (微信小程序格式)
        function loadImageFromUrlWx() {
            const url = document.getElementById('imageUrlWx').value;
            const preview = document.getElementById('imagePreview');
            
            if (url) {
                preview.src = url;
                preview.style.display = 'block';
                preview.onerror = function() {
                    alert('无法加载图片，请检查URL是否正确');
                    preview.style.display = 'none';
                };
            }
        }
        
        // 提交图片到API
        function submitImage() {
            // 获取当前活动标签
            const activeTab = document.querySelector('.tab.active').dataset.tab;
            let formData = new FormData();
            let hasImage = false;
            let useJson = false;
            let jsonData = {};
            
            // 根据不同标签页处理数据
            if (activeTab === 'upload') {
                const fileInput = document.getElementById('imageFile');
                if (fileInput.files.length > 0) {
                    formData.append('image', fileInput.files[0]);
                    hasImage = true;
                }
            } else if (activeTab === 'url') {
                const url = document.getElementById('imageUrl').value;
                if (url) {
                    formData.append('image_url', url);
                    hasImage = true;
                }
            } else if (activeTab === 'base64') {
                const base64 = document.getElementById('imageBase64').value;
                if (base64) {
                    formData.append('image_base64', base64);
                    hasImage = true;
                }
            } else if (activeTab === 'wx') {
                const url = document.getElementById('imageUrlWx').value;
                if (url) {
                    jsonData = { image_url: url };
                    hasImage = true;
                    useJson = true;
                }
            }
            
            if (!hasImage) {
                alert('请选择或提供图片');
                return;
            }
            
            // 显示加载指示器
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('submitBtn').disabled = true;
            document.getElementById('resultBox').style.display = 'none';
            
            // 发送请求
            if (useJson) {
                // 使用JSON格式请求（微信小程序方式）
                fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(jsonData)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误! 状态: ${response.status}`);
                    }
                    return response.json();
                })
                .then(handleResponse)
                .catch(handleError);
            } else {
                // 使用表单数据请求（标准方式）
                fetch(API_URL, {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误! 状态: ${response.status}`);
                    }
                    return response.json();
                })
                .then(handleResponse)
                .catch(handleError);
            }
        }
        
        // 处理API响应
        function handleResponse(data) {
            console.log('API响应:', data);
            
            // 显示结果
            const clothingType = data.clothing_type || '未识别';
            document.getElementById('clothingType').textContent = clothingType;
            
            // 显示类型解释
            const explanation = clothingTypeMap[clothingType] || clothingType;
            document.getElementById('clothingTypeExplanation').textContent = explanation;
            
            document.getElementById('apiResponse').textContent = JSON.stringify(data, null, 2);
            document.getElementById('resultBox').style.display = 'block';
            
            // 隐藏加载指示器
            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('submitBtn').disabled = false;
        }
        
        // 处理错误
        function handleError(error) {
            console.error('错误:', error);
            
            // 显示错误信息
            document.getElementById('clothingType').textContent = '识别失败';
            document.getElementById('apiResponse').textContent = `发生错误: ${error.message}`;
            document.getElementById('resultBox').style.display = 'block';
            
            // 隐藏加载指示器
            document.getElementById('loadingIndicator').style.display = 'none';
            document.getElementById('submitBtn').disabled = false;
        }
        
        // 清除表单
        function clearForm() {
            document.getElementById('imageFile').value = '';
            document.getElementById('imageUrl').value = '';
            document.getElementById('imageBase64').value = '';
            document.getElementById('imageUrlWx').value = '';
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('resultBox').style.display = 'none';
        }
    </script>
</body>
</html> 