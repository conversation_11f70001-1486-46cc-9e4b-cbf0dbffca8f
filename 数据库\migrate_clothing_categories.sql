-- 衣物分类数据迁移脚本
-- 功能：为clothes表添加category_id字段，建立与clothing_categories表的关联
-- 注意：保留原有category字段作为备份，确保数据安全

-- 1. 为clothes表添加category_id字段
ALTER TABLE `clothes` 
ADD COLUMN `category_id` int(11) DEFAULT NULL COMMENT '关联的分类ID' AFTER `category`,
ADD INDEX `idx_category_id` (`category_id`);

-- 2. 添加外键约束（可选，建议先测试后再添加）
-- ALTER TABLE `clothes` 
-- ADD CONSTRAINT `clothes_category_fk` FOREIGN KEY (`category_id`) REFERENCES `clothing_categories` (`id`) ON DELETE SET NULL;

-- 3. 创建分类代码到ID的映射视图（用于迁移参考）
CREATE OR REPLACE VIEW `v_category_mapping` AS
SELECT 
    id as category_id,
    code as category_code,
    name as category_name,
    is_system
FROM `clothing_categories` 
WHERE is_system = 1
ORDER BY sort_order; 