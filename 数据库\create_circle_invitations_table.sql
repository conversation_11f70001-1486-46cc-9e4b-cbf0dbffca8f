-- 创建圈子邀请记录表
-- 模块3：邀请分享模块

CREATE TABLE IF NOT EXISTS `circle_invitations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `circle_id` int(11) NOT NULL COMMENT '圈子ID',
  `inviter_id` int(11) NOT NULL COMMENT '邀请者用户ID',
  `invitation_code` varchar(20) NOT NULL COMMENT '邀请码',
  `share_type` enum('wechat','timeline','copy') NOT NULL DEFAULT 'wechat' COMMENT '分享类型：wechat=微信好友，timeline=朋友圈，copy=复制链接',
  `share_count` int(11) NOT NULL DEFAULT '1' COMMENT '分享次数',
  `join_count` int(11) NOT NULL DEFAULT '0' COMMENT '通过此邀请加入的人数',
  `last_shared_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后分享时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_circle_id` (`circle_id`),
  KEY `idx_inviter_id` (`inviter_id`),
  KEY `idx_invitation_code` (`invitation_code`),
  KEY `idx_share_type` (`share_type`),
  KEY `idx_last_shared_at` (`last_shared_at`),
  UNIQUE KEY `uk_circle_inviter_code` (`circle_id`, `inviter_id`, `invitation_code`),
  CONSTRAINT `fk_circle_invitations_circle` FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_circle_invitations_inviter` FOREIGN KEY (`inviter_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='圈子邀请记录表';

-- 创建索引优化查询性能
CREATE INDEX `idx_circle_invitations_stats` ON `circle_invitations` (`circle_id`, `share_count`, `join_count`);
CREATE INDEX `idx_inviter_invitations` ON `circle_invitations` (`inviter_id`, `last_shared_at`);
