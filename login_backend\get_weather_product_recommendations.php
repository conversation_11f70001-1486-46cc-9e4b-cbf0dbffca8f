<?php
// 启用输出缓冲，确保只返回JSON数据
ob_start();

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 全局错误处理，将PHP错误转为JSON响应
function handleError($errno, $errstr, $errfile, $errline) {
    $error_message = "PHP Error [$errno]: $errstr in $errfile on line $errline";
    logDebug("捕获到PHP错误", ['error' => $error_message]);
    
    // 清除之前的输出
    ob_clean();
    
    // 返回JSON格式的错误
    header('Content-Type: application/json');
    echo json_encode(['status' => 'error', 'message' => '服务器错误，请稍后重试']);
    exit;
}
set_error_handler('handleError');

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 日志函数
function logDebug($message, $data = null) {
    $log_file = __DIR__ . '/weather_product_recommendation_debug.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] {$message}";
    
    if ($data !== null) {
        $log_message .= ': ' . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    
    file_put_contents($log_file, $log_message . PHP_EOL, FILE_APPEND);
}

// 注册shutdown函数，确保在脚本结束时清除输出缓冲并返回JSON
function shutdownHandler() {
    $error = error_get_last();
    if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        // 清除之前的输出
        ob_clean();
        
        // 记录致命错误
        logDebug("捕获到致命错误", ['error' => $error]);
        
        // 返回JSON格式的错误
        header('Content-Type: application/json');
        echo json_encode(['status' => 'error', 'message' => '服务器发生错误，请稍后重试']);
    }
}
register_shutdown_function('shutdownHandler');

// 初始化数据库连接
$db = new Database();
$pdo = $db->getConnection();

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 验证用户令牌
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '缺少授权头']);
    exit();
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证用户token
$userData = $auth->verifyToken($token);
if (!$userData) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '无效或已过期的令牌']);
    exit();
}

// 获取用户ID
$user_id = $userData['sub'];
logDebug("处理用户请求", ['user_id' => $user_id]);

// 获取请求参数
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $refresh = isset($_GET['refresh']) ? intval($_GET['refresh']) : 0;
    $latitude = isset($_GET['latitude']) ? $_GET['latitude'] : '';
    $longitude = isset($_GET['longitude']) ? $_GET['longitude'] : '';
    $city_id = isset($_GET['city_id']) ? $_GET['city_id'] : '';
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 5; // 默认返回5个推荐商品
} else {
    $refresh = isset($_POST['refresh']) ? intval($_POST['refresh']) : 0;
    $latitude = isset($_POST['latitude']) ? $_POST['latitude'] : '';
    $longitude = isset($_POST['longitude']) ? $_POST['longitude'] : '';
    $city_id = isset($_POST['city_id']) ? $_POST['city_id'] : '';
    $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 5; // 默认返回5个推荐商品
}

logDebug("请求参数", [
    'refresh' => $refresh,
    'latitude' => $latitude,
    'longitude' => $longitude,
    'city_id' => $city_id,
    'limit' => $limit
]);

// 检查必要参数
if (empty($latitude) && empty($longitude) && empty($city_id)) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少位置信息，请提供经纬度或城市ID'
    ]);
    logDebug("错误: 缺少位置信息");
    exit;
}

try {
    // 获取天气数据
    $weather_data = fetchWeatherData($latitude, $longitude, $city_id);
    if (isset($weather_data['error'])) {
        echo json_encode([
            'status' => 'error',
            'message' => '获取天气数据失败: ' . $weather_data['message']
        ]);
        logDebug("获取天气数据失败", $weather_data);
        exit;
    }
    
    // 强化验证：检查天气数据的完整性
    if (!isset($weather_data['location']) || !isset($weather_data['now']) ||
        !isset($weather_data['location']['name']) || !isset($weather_data['now']['temp']) || !isset($weather_data['now']['text']) ||
        $weather_data['location']['name'] === null || $weather_data['now']['temp'] === null || $weather_data['now']['text'] === null) {
        
        logDebug("天气数据不完整或关键字段为空", [
            'location' => isset($weather_data['location']) ? (isset($weather_data['location']['name']) ? $weather_data['location']['name'] : 'missing name') : 'missing location',
            'temp' => isset($weather_data['now']) ? (isset($weather_data['now']['temp']) ? $weather_data['now']['temp'] : 'missing temp') : 'missing now',
            'text' => isset($weather_data['now']) ? (isset($weather_data['now']['text']) ? $weather_data['now']['text'] : 'missing text') : 'missing now'
        ]);
        
        echo json_encode([
            'status' => 'error',
            'message' => '获取到的天气数据不完整，请稍后重试'
        ]);
        exit;
    }
    
    logDebug("获取到的天气数据", [
        'location' => $weather_data['location']['name'],
        'temp' => $weather_data['now']['temp'],
        'text' => $weather_data['now']['text'],
        'windDir' => $weather_data['now']['windDir'] ?? '未知',
        'windScale' => $weather_data['now']['windScale'] ?? '--',
        'humidity' => $weather_data['now']['humidity'] ?? '--'
    ]);
    
    // 构建天气键，用于数据库缓存
    $weather_key = '';
    if (!empty($weather_data['location']['id'])) {
        $weather_key = $weather_data['location']['id'];
    } else if (!empty($weather_data['location']['name'])) {
        $weather_key = $weather_data['location']['name'];
    } else {
        // 如果没有有效的location标识，使用请求参数作为备用
        $weather_key = !empty($city_id) ? 'city_' . $city_id : 'loc_' . $latitude . '_' . $longitude;
    }
    
    if (!empty($weather_data['now']['text']) && !empty($weather_data['now']['temp'])) {
        $weather_key .= '_' . $weather_data['now']['text'] . '_' . round($weather_data['now']['temp']);
    } else {
        $weather_key .= '_unknown_' . time(); // 添加时间戳确保唯一性
    }
    $weather_key = strtolower($weather_key); // 转小写以确保一致性
    
    logDebug("生成的天气键", ['weather_key' => $weather_key]);
    
    // 如果是刷新请求或者没有缓存的推荐结果，则生成新的推荐
    $should_generate_new = $refresh == 1;
    $cached_recommendation = null;
    
    // 检查缓存表是否存在
    ensureWeatherProductRecommendationTableExists($pdo);
    
    // 获取最近的推荐结果，无论是否刷新都需要获取
    $stmt = $pdo->prepare("SELECT * FROM weather_product_recommendations 
                           WHERE user_id = :user_id AND weather_key = :weather_key
                           ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([
        ':user_id' => $user_id,
        ':weather_key' => $weather_key
    ]);
    $cached_recommendation = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$should_generate_new && $cached_recommendation) {
        // 检查缓存是否在近期（比如3小时内）
        $cached_time = strtotime($cached_recommendation['created_at']);
        $current_time = time();
        $time_diff = $current_time - $cached_time;
        
        // 如果缓存时间超过3小时，则生成新的推荐
        if ($time_diff > 10800) { // 10800秒=3小时
            $should_generate_new = true;
            logDebug("缓存已过期，需要生成新推荐", ['缓存时间' => $cached_recommendation['created_at'], '过期时间差(秒)' => $time_diff]);
        } else {
            logDebug("使用缓存的推荐结果", ['缓存时间' => $cached_recommendation['created_at']]);
        }
    } else if (!$should_generate_new && $cached_recommendation === false) {
        $should_generate_new = true;
        logDebug("没有找到缓存的推荐结果，需要生成新推荐");
    } else {
        logDebug("请求强制刷新推荐结果");
    }
    
    if ($should_generate_new) {
        // 根据天气情况生成商品推荐
        $recommended_products = generateProductRecommendations($pdo, $weather_data, $limit);
        
        if (empty($recommended_products)) {
            echo json_encode([
                'status' => 'error',
                'message' => '未能找到匹配当前天气的商品推荐'
            ]);
            logDebug("错误: 未能生成商品推荐");
            exit;
        }
        
        // 存储推荐结果到数据库
        $stmt = $pdo->prepare("INSERT INTO weather_product_recommendations 
                              (user_id, weather_key, weather_data, recommendation_data, created_at) 
                              VALUES (:user_id, :weather_key, :weather_data, :recommendation_data, NOW())");
        $stmt->execute([
            ':user_id' => $user_id,
            ':weather_key' => $weather_key,
            ':weather_data' => json_encode($weather_data),
            ':recommendation_data' => json_encode($recommended_products)
        ]);
        logDebug("已保存推荐结果到数据库");
        
        // 构建响应
        $response = [
            'status' => 'success',
            'weather' => [
                'city' => $weather_data['location']['name'],
                'temp' => $weather_data['now']['temp'],
                'text' => $weather_data['now']['text'],
                'windDir' => $weather_data['now']['windDir'] ?? '未知',
                'windScale' => $weather_data['now']['windScale'] ?? '--',
                'humidity' => $weather_data['now']['humidity'] ?? '--',
                'feelsLike' => $weather_data['now']['feelsLike'] ?? '--'
            ],
            'recommended_products' => $recommended_products
        ];
        
        echo json_encode($response);
        logDebug("已向客户端返回新的推荐结果");
    } else {
        // 获取缓存的推荐结果
        $cached_data = json_decode($cached_recommendation['recommendation_data'], true);
        logDebug("从缓存获取的推荐结果", ['product_count' => count($cached_data)]);
        
        // 构建响应
        $response = [
            'status' => 'success',
            'weather' => [
                'city' => $weather_data['location']['name'],
                'temp' => $weather_data['now']['temp'],
                'text' => $weather_data['now']['text'],
                'windDir' => $weather_data['now']['windDir'] ?? '未知',
                'windScale' => $weather_data['now']['windScale'] ?? '--',
                'humidity' => $weather_data['now']['humidity'] ?? '--',
                'feelsLike' => $weather_data['now']['feelsLike'] ?? '--'
            ],
            'recommended_products' => $cached_data,
            'cached' => true
        ];
        
        echo json_encode($response);
        logDebug("已向客户端返回缓存的推荐结果");
    }
    
} catch (Exception $e) {
    logDebug("处理过程中发生异常", ['exception' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
    
    // 清除之前的输出
    ob_clean();
    
    // 设置状态码并返回JSON错误信息
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => '服务器处理异常: ' . $e->getMessage()]);
} catch (Throwable $t) {
    logDebug("处理过程中发生严重错误", ['error' => $t->getMessage(), 'trace' => $t->getTraceAsString()]);
    
    // 清除之前的输出
    ob_clean();
    
    // 设置状态码并返回JSON错误信息
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => '服务器处理错误，请稍后重试']);
}

// 确保所有输出都经过输出缓冲
ob_end_flush();

/**
 * 获取天气数据 (从get_weather_recommendation.php复制)
 * @param string $latitude 纬度
 * @param string $longitude 经度
 * @param string $city_id 城市ID
 * @return array 天气数据或错误信息
 */
function fetchWeatherData($latitude, $longitude, $city_id) {
    logDebug("开始获取天气数据", [
        'latitude' => $latitude,
        'longitude' => $longitude,
        'city_id' => $city_id,
        'from_request' => [
            'lat' => $_GET['lat'] ?? null,
            'lon' => $_GET['lon'] ?? null,
            'latitude' => $_GET['latitude'] ?? null,
            'longitude' => $_GET['longitude'] ?? null,
            'location' => $_GET['location'] ?? null,
            'cityid' => $_GET['cityid'] ?? null,
            'city' => $_GET['city'] ?? null
        ]
    ]);
    
    // 检查是否通过location参数传递了经纬度
    $location = $_GET['location'] ?? null;
    if (empty($latitude) && empty($longitude) && !empty($location)) {
        // 尝试解析location参数（格式：经度,纬度）
        $locationParts = explode(',', $location);
        if (count($locationParts) === 2) {
            $longitude = trim($locationParts[0]);
            $latitude = trim($locationParts[1]);
            logDebug("从location参数解析经纬度", [
                'location' => $location,
                'parsed_longitude' => $longitude,
                'parsed_latitude' => $latitude
            ]);
        }
    }
    
    $params = [];
    
    // 根据提供的参数构建请求
    if (!empty($city_id)) {
        $params['location'] = $city_id;
    } elseif (!empty($latitude) && !empty($longitude)) {
        $params['location'] = $longitude . ',' . $latitude;
        // 同时添加标准参数，确保兼容性
        $params['latitude'] = $latitude;
        $params['longitude'] = $longitude;
    } else {
        return ['error' => true, 'message' => '缺少位置信息参数'];
    }
    
    // 构建API URL
    $query = http_build_query($params);
    $api_url = API_DOMAIN . '/login_backend/get_weather.php?' . $query;
    
    logDebug("调用天气API", ['url' => $api_url]);
    
    // 发起请求
    $ch = curl_init($api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 设置超时时间
    
    $response = curl_exec($ch);
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        logDebug("天气API调用失败", ['curl_error' => $error]);
        return ['error' => true, 'message' => '获取天气数据失败: ' . $error];
    }
    
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code != 200) {
        logDebug("天气API返回非200状态码", ['http_code' => $http_code]);
        return ['error' => true, 'message' => '天气服务返回错误状态码: ' . $http_code];
    }
    
    // 记录原始响应
    logDebug("天气API原始响应", ['raw_response' => substr($response, 0, 500)]);
    
    // 尝试解析响应
    $weather_data = @json_decode($response, true);
    if (!$weather_data) {
        // 检查响应是否是HTML而非JSON
        if (preg_match('/<html|<!DOCTYPE html|<body/i', $response)) {
            logDebug("天气API返回HTML而非JSON", ['response_excerpt' => substr($response, 0, 200)]);
            return ['error' => true, 'message' => '天气服务返回了非JSON格式的响应'];
        }
        
        logDebug("解析天气API响应失败", ['raw_response' => substr($response, 0, 500), 'json_error' => json_last_error_msg()]);
        return ['error' => true, 'message' => '解析天气数据失败: ' . json_last_error_msg()];
    }
    
    // 检查并处理success/data格式的响应
    if (isset($weather_data['success']) && $weather_data['success'] === true && isset($weather_data['data'])) {
        logDebug("检测到success/data格式的天气数据，进行格式转换");
        
        $data = $weather_data['data'];
        $standardized_data = [];
        
        // 常用城市英文名到中文名的映射
        $cityNameMapping = [
            'hangzhou' => '杭州',
            'shanghai' => '上海',
            'beijing' => '北京',
            'guangzhou' => '广州',
            'shenzhen' => '深圳',
            'suzhou' => '苏州',
            'nanjing' => '南京',
            'wuhan' => '武汉',
            'chengdu' => '成都',
            'chongqing' => '重庆',
            'tianjin' => '天津',
            'xiamen' => '厦门',
            'xian' => '西安',
            'qingdao' => '青岛',
            'dalian' => '大连',
            'ningbo' => '宁波',
            'shenyang' => '沈阳',
            'changsha' => '长沙',
            'kunming' => '昆明',
            'zhengzhou' => '郑州',
            'fuzhou' => '福州',
            'jinan' => '济南',
            'hefei' => '合肥',
            'changchun' => '长春'
        ];
        
        // 获取城市名称，尝试转换英文为中文
        $cityName = $data['city'] ?? (!empty($city_id) ? $city_id : "未知城市");
        if (isset($cityNameMapping[strtolower($cityName)])) {
            $cityName = $cityNameMapping[strtolower($cityName)];
        }
        
        // 构建location部分
        $standardized_data['location'] = [
            'name' => $cityName,
            'id' => $data['cityid'] ?? '',
        ];
        
        // 构建now部分，确保包含前端所需的所有字段
        $standardized_data['now'] = [
            'temp' => $data['temp'] ?? '--',              // 温度
            'text' => $data['text'] ?? '未知',            // 天气状况
            'feelsLike' => $data['feelsLike'] ?? '--',    // 体感温度
            'windDir' => $data['windDir'] ?? '未知',      // 风向
            'windScale' => $data['windScale'] ?? '--',    // 风力等级
            'humidity' => $data['humidity'] ?? '--',      // 湿度
            'pressure' => $data['pressure'] ?? '--',      // 气压
            'vis' => $data['vis'] ?? '--',                // 能见度
            'precip' => $data['precip'] ?? '0',           // 降水量
        ];
        
        // 保存原始数据用于调试
        $standardized_data['_original_data'] = $data;
        
        // 替换为标准格式
        $weather_data = $standardized_data;
        
        logDebug("转换后的天气数据格式", [
            'location' => $weather_data['location'],
            'now' => array_intersect_key($weather_data['now'], ['temp' => 1, 'text' => 1, 'windDir' => 1, 'windScale' => 1, 'humidity' => 1])
        ]);
    }
    
    if (isset($weather_data['code']) && $weather_data['code'] != '200') {
        logDebug("天气API返回错误码", ['code' => $weather_data['code'], 'msg' => $weather_data['msg'] ?? '未知错误']);
        return ['error' => true, 'message' => '天气服务返回错误: ' . ($weather_data['msg'] ?? '未知错误')];
    }
    
    // 验证关键字段是否存在
    if (!isset($weather_data['location']) || !isset($weather_data['now'])) {
        logDebug("天气数据缺少关键部分", ['has_location' => isset($weather_data['location']), 'has_now' => isset($weather_data['now'])]);
        return ['error' => true, 'message' => '天气数据格式不完整'];
    }
    
    // 确保location和now对象中有必要的字段，如果没有则设置默认值
    if (!isset($weather_data['location']['name']) || $weather_data['location']['name'] === null) {
        logDebug("天气数据缺少城市名称");
        $weather_data['location']['name'] = !empty($city_id) ? $city_id : "未知城市";
    }
    
    if (!isset($weather_data['now']['temp']) || $weather_data['now']['temp'] === null) {
        logDebug("天气数据缺少温度");
        $weather_data['now']['temp'] = "--";
    }
    
    if (!isset($weather_data['now']['text']) || $weather_data['now']['text'] === null) {
        logDebug("天气数据缺少天气状况");
        $weather_data['now']['text'] = "未知";
    }
    
    logDebug("成功获取天气数据");
    return $weather_data;
}

/**
 * 确保weather_product_recommendations表存在
 */
function ensureWeatherProductRecommendationTableExists($conn) {
    try {
        // 检查表是否存在
        $stmt = $conn->query("SHOW TABLES LIKE 'weather_product_recommendations'");
        if ($stmt->rowCount() == 0) {
            // 表不存在，创建表
            $conn->exec("CREATE TABLE weather_product_recommendations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                weather_key VARCHAR(100) NOT NULL COMMENT '天气键(城市_天气状况_温度)',
                weather_data TEXT NOT NULL COMMENT '天气数据JSON',
                recommendation_data TEXT NOT NULL COMMENT '推荐商品数据JSON',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_weather_key (user_id, weather_key),
                INDEX idx_created_at (created_at),
                CONSTRAINT fk_weather_prod_rec_user FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='基于天气的商品推荐记录'");
            logDebug("创建weather_product_recommendations表成功");
        }
    } catch (PDOException $e) {
        logDebug("检查或创建weather_product_recommendations表失败", ['error' => $e->getMessage()]);
        throw $e;
    }
}

/**
 * 根据天气情况生成商品推荐
 * @param PDO $pdo 数据库连接
 * @param array $weather_data 天气数据
 * @param int $limit 返回的商品数量限制
 * @return array 推荐商品列表
 */
function generateProductRecommendations($pdo, $weather_data, $limit = 5) {
    logDebug("开始根据天气生成商品推荐");
    
    // 提取天气信息
    $weather_text = $weather_data['now']['text'];
    $temperature = floatval($weather_data['now']['temp']);
    $wind_scale = isset($weather_data['now']['windScale']) ? intval($weather_data['now']['windScale']) : 0;
    $humidity = isset($weather_data['now']['humidity']) ? intval($weather_data['now']['humidity']) : 0;
    
    logDebug("天气参数提取", [
        'weather_text' => $weather_text,
        'temperature' => $temperature,
        'wind_scale' => $wind_scale,
        'humidity' => $humidity
    ]);
    
    // 基于天气文本的关键词映射
    $weather_keywords = [];
    
    // 天气状况关键词
    if (strpos($weather_text, '雨') !== false) {
        $weather_keywords[] = '雨伞';
        $weather_keywords[] = '雨衣';
        $weather_keywords[] = '防水';
        $weather_keywords[] = '防滑';
    }
    
    if (strpos($weather_text, '雪') !== false) {
        $weather_keywords[] = '保暖';
        $weather_keywords[] = '防滑';
        $weather_keywords[] = '手套';
    }
    
    if (strpos($weather_text, '晴') !== false) {
        $weather_keywords[] = '防晒';
        $weather_keywords[] = '太阳镜';
        $weather_keywords[] = '遮阳';
    }
    
    if (strpos($weather_text, '雾') !== false || strpos($weather_text, '霾') !== false) {
        $weather_keywords[] = '口罩';
        $weather_keywords[] = '防护';
    }
    
    if (strpos($weather_text, '沙') !== false) {
        $weather_keywords[] = '口罩';
        $weather_keywords[] = '防护眼镜';
    }
    
    // 温度相关关键词
    if ($temperature >= 30) {
        $weather_keywords[] = '清凉';
        $weather_keywords[] = '遮阳';
        $weather_keywords[] = '防晒';
        $weather_keywords[] = '凉鞋';
        $weather_keywords[] = '短袖';
    } elseif ($temperature >= 20 && $temperature < 30) {
        $weather_keywords[] = '舒适';
        $weather_keywords[] = '透气';
        $weather_keywords[] = '轻便';
    } elseif ($temperature >= 10 && $temperature < 20) {
        $weather_keywords[] = '外套';
        $weather_keywords[] = '长袖';
        $weather_keywords[] = '薄款';
    } elseif ($temperature >= 0 && $temperature < 10) {
        $weather_keywords[] = '保暖';
        $weather_keywords[] = '厚外套';
        $weather_keywords[] = '围巾';
        $weather_keywords[] = '帽子';
    } elseif ($temperature < 0) {
        $weather_keywords[] = '羽绒服';
        $weather_keywords[] = '保暖';
        $weather_keywords[] = '手套';
        $weather_keywords[] = '棉袄';
        $weather_keywords[] = '暖宝宝';
    }
    
    // 风力相关关键词
    if ($wind_scale >= 5) {
        $weather_keywords[] = '防风';
        $weather_keywords[] = '风衣';
    }
    
    // 湿度相关关键词
    if ($humidity >= 80) {
        $weather_keywords[] = '除湿';
        $weather_keywords[] = '防潮';
    } elseif ($humidity <= 30) {
        $weather_keywords[] = '保湿';
        $weather_keywords[] = '补水';
    }
    
    // 去重
    $weather_keywords = array_unique($weather_keywords);
    
    logDebug("生成天气关键词", ['keywords' => $weather_keywords]);
    
    // 构建SQL查询，根据标签和关键词筛选商品
    $query = "SELECT * FROM taobao_products WHERE status = 1";
    $conditions = [];
    $params = [];
    
    // 添加关键词条件
    if (!empty($weather_keywords)) {
        $keyword_conditions = [];
        $keyword_index = 0;
        
        foreach ($weather_keywords as $keyword) {
            $param_key = ":keyword" . $keyword_index;
            $keyword_conditions[] = "
                (title LIKE $param_key OR 
                 tags LIKE $param_key)
            ";
            $params[$param_key] = "%" . $keyword . "%";
            $keyword_index++;
        }
        
        if (!empty($keyword_conditions)) {
            $conditions[] = "(" . implode(" OR ", $keyword_conditions) . ")";
        }
    }
    
    // 添加其他条件如优惠券、评分等
    $conditions[] = "is_fake_tpwd = 0"; // 只使用真实淘口令的商品
    $conditions[] = "coupon_amount > 0"; // 有优惠券的商品
    
    // 组合所有条件
    if (!empty($conditions)) {
        $query .= " AND " . implode(" AND ", $conditions);
    }
    
    // 添加排序
    $query .= " ORDER BY RAND() LIMIT :limit"; // 随机排序，限制返回数量
    
    // 执行查询
    $stmt = $pdo->prepare($query);
    
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    logDebug("查询到的商品数量", ['count' => count($products)]);
    
    // 如果查询结果为空，尝试使用更宽松的条件再次查询
    if (empty($products)) {
        logDebug("没有找到匹配关键词的商品，尝试更宽松的条件");
        
        // 简化查询，只基于热门商品和优惠券
        $retry_query = "SELECT * FROM taobao_products 
                       WHERE status = 1 
                       AND is_fake_tpwd = 0 
                       AND coupon_amount > 0 
                       ORDER BY volume DESC, commission_rate DESC 
                       LIMIT :limit";
        
        $retry_stmt = $pdo->prepare($retry_query);
        $retry_stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $retry_stmt->execute();
        
        $products = $retry_stmt->fetchAll(PDO::FETCH_ASSOC);
        logDebug("使用宽松条件查询到的商品数量", ['count' => count($products)]);
    }
    
    // 格式化返回结果
    $formatted_products = [];
    foreach ($products as $product) {
        // 处理小图列表，将JSON转换回数组
        if (!empty($product['small_images'])) {
            $product['small_images'] = json_decode($product['small_images'], true);
        } else {
            $product['small_images'] = [];
        }
        
        // 确保所有价格格式正确（两位小数）
        $product['original_price'] = number_format($product['original_price'], 2, '.', '');
        $product['zk_final_price'] = number_format($product['zk_final_price'], 2, '.', '');
        $product['final_price'] = number_format($product['final_price'], 2, '.', '');
        $product['coupon_amount'] = number_format($product['coupon_amount'], 2, '.', '');
        $product['commission_rate'] = number_format($product['commission_rate'], 2, '.', '');
        $product['commission_amount'] = number_format($product['commission_amount'], 2, '.', '');
        
        // 添加匹配的天气关键词
        $matched_keywords = [];
        foreach ($weather_keywords as $keyword) {
            if (strpos($product['title'], $keyword) !== false || 
                (isset($product['tags']) && strpos($product['tags'], $keyword) !== false)) {
                $matched_keywords[] = $keyword;
            }
        }
        $product['matched_weather_keywords'] = $matched_keywords;
        
        $formatted_products[] = $product;
    }
    
    logDebug("返回格式化后的商品", ['count' => count($formatted_products)]);
    return $formatted_products;
}
?> 