<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] != 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证管理员身份
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取查询参数
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 10;
$search = isset($_GET['search']) ? $_GET['search'] : '';
$sortBy = isset($_GET['sort_by']) ? $_GET['sort_by'] : 'id';
$sortOrder = isset($_GET['sort_order']) && strtolower($_GET['sort_order']) === 'asc' ? 'ASC' : 'DESC';
$userId = isset($_GET['user_id']) ? (int)$_GET['user_id'] : null;
$category = isset($_GET['category']) ? $_GET['category'] : null;

// 验证并限制参数
$page = max(1, $page);
$perPage = max(1, min(100, $perPage)); // 限制每页最大数量为100
$offset = ($page - 1) * $perPage;

// 允许排序的字段
$allowedSortFields = ['id', 'name', 'category', 'created_at', 'updated_at'];
if (!in_array($sortBy, $allowedSortFields)) {
    $sortBy = 'id';
}

// 允许的分类
$allowedCategories = ['tops', 'pants', 'skirts', 'coats', 'shoes', 'bags', 'accessories'];
if ($category && !in_array($category, $allowedCategories)) {
    $category = null;
}

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

// 构建查询条件
$whereClause = '';
$params = [];

if (!empty($search) || $userId !== null || $category !== null) {
    $whereClause = " WHERE ";
    $conditions = [];
    
    if (!empty($search)) {
        $conditions[] = "(name LIKE :search OR tags LIKE :search)";
        $params[':search'] = "%$search%";
    }
    
    if ($userId !== null) {
        $conditions[] = "user_id = :user_id";
        $params[':user_id'] = $userId;
    }
    
    if ($category !== null) {
        $conditions[] = "category = :category";
        $params[':category'] = $category;
    }
    
    $whereClause .= implode(" AND ", $conditions);
}

// 查询总记录数
$countQuery = "SELECT COUNT(*) as total FROM clothes" . $whereClause;
$countStmt = $conn->prepare($countQuery);
foreach ($params as $key => $value) {
    $countStmt->bindValue($key, $value);
}
$countStmt->execute();
$totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

// 计算总页数
$totalPages = ceil($totalCount / $perPage);

// 构建主查询 - 获取衣物记录
$query = "SELECT c.id, c.user_id, c.name, c.category, c.image_url, c.tags, c.description, c.created_at, c.updated_at, 
          u.nickname as user_nickname, u.avatar_url as user_avatar
          FROM clothes c
          LEFT JOIN users u ON c.user_id = u.id" . $whereClause . 
          " ORDER BY c.$sortBy $sortOrder 
          LIMIT :offset, :limit";

$stmt = $conn->prepare($query);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':limit', $perPage, PDO::PARAM_INT);

foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}

$stmt->execute();
$clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 格式化输出
$result = [
    'error' => false,
    'data' => $clothes,
    'pagination' => [
        'total' => (int)$totalCount,
        'per_page' => $perPage,
        'current_page' => $page,
        'total_pages' => $totalPages
    ]
];

// 设置上一页和下一页链接
if ($page > 1) {
    $result['pagination']['prev_page'] = $page - 1;
}
if ($page < $totalPages) {
    $result['pagination']['next_page'] = $page + 1;
}

echo json_encode($result); 