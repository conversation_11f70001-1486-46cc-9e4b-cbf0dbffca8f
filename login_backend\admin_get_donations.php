<?php
/**
 * 管理员获取打赏记录API
 */
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查Authorization头是否存在
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取请求参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$perPage = isset($_GET['per_page']) ? intval($_GET['per_page']) : 10;
$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : null;
$nickname = isset($_GET['nickname']) ? $_GET['nickname'] : null;
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : null;
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : null;

// 验证页码和每页数量
if ($page < 1) {
    $page = 1;
}

if ($perPage < 1 || $perPage > 100) {
    $perPage = 10;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 构建SQL查询
    $sql = "SELECT d.*, u.nickname as user_nickname, u.avatar_url as user_avatar 
            FROM donations d 
            LEFT JOIN users u ON d.user_id = u.id 
            WHERE 1=1";
    $countSql = "SELECT COUNT(*) FROM donations d WHERE 1=1";
    $summarySql = "SELECT 
                    COUNT(*) as total_count,
                    SUM(CASE WHEN status = 'success' THEN amount ELSE 0 END) as total_amount,
                    SUM(CASE WHEN status = 'success' AND DATE(paid_at) = CURDATE() THEN 1 ELSE 0 END) as today_count,
                    SUM(CASE WHEN status = 'success' AND DATE(paid_at) = CURDATE() THEN amount ELSE 0 END) as today_amount
                  FROM donations";
    
    $params = [];
    $whereConditions = [];

    // 添加筛选条件
    if ($userId) {
        $whereConditions[] = "d.user_id = :user_id";
        $params[':user_id'] = $userId;
    }
    
    if ($nickname) {
        $whereConditions[] = "(d.nickname LIKE :nickname OR u.nickname LIKE :nickname)";
        $params[':nickname'] = "%$nickname%";
    }
    
    if ($startDate) {
        $whereConditions[] = "d.created_at >= :start_date";
        $params[':start_date'] = $startDate . " 00:00:00";
    }
    
    if ($endDate) {
        $whereConditions[] = "d.created_at <= :end_date";
        $params[':end_date'] = $endDate . " 23:59:59";
    }
    
    // 合并条件到SQL语句
    if (!empty($whereConditions)) {
        $conditionStr = implode(" AND ", $whereConditions);
        $sql .= " AND " . $conditionStr;
        $countSql .= " AND " . $conditionStr;
        $summarySql .= " WHERE " . $conditionStr;
    }
    
    // 添加排序和分页
    $sql .= " ORDER BY d.created_at DESC LIMIT :offset, :limit";
    
    // 准备并执行查询
    $stmt = $conn->prepare($sql);
    
    // 绑定查询参数
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    
    $offset = ($page - 1) * $perPage;
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':limit', $perPage, PDO::PARAM_INT);
    
    $stmt->execute();
    $donations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取总记录数
    $countStmt = $conn->prepare($countSql);
    foreach ($params as $key => $value) {
        $countStmt->bindValue($key, $value);
    }
    $countStmt->execute();
    $totalItems = $countStmt->fetchColumn();
    
    // 计算总页数
    $totalPages = ceil($totalItems / $perPage);
    
    // 获取统计数据
    $summaryStmt = $conn->prepare($summarySql);
    foreach ($params as $key => $value) {
        $summaryStmt->bindValue($key, $value);
    }
    $summaryStmt->execute();
    $summary = $summaryStmt->fetch(PDO::FETCH_ASSOC);
    
    // 返回数据
    echo json_encode([
        'status' => 'success',
        'message' => '获取打赏记录成功',
        'data' => [
            'donations' => $donations,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_items' => $totalItems,
                'total_pages' => $totalPages
            ],
            'summary' => [
                'total_count' => intval($summary['total_count']),
                'total_amount' => floatval($summary['total_amount']),
                'today_count' => intval($summary['today_count']),
                'today_amount' => floatval($summary['today_amount'])
            ]
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => '获取打赏记录失败: ' . $e->getMessage()
    ]);
} 