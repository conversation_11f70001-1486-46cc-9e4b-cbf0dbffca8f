const app = getApp();

Page({
  data: {
    categories: [],
    isLoading: false,
    hasMoreData: false,
    page: 1,
    perPage: 20
  },

  onLoad: function (options) {
    this.loadCategories();
  },

  onShow: function () {
    // 从其他页面返回时刷新数据
    this.loadCategories();
  },

  onPullDownRefresh: function () {
    this.setData({
      page: 1,
      categories: []
    });
    this.loadCategories();
  },

  onReachBottom: function () {
    if (this.data.hasMoreData && !this.data.isLoading) {
      this.loadMore();
    }
  },

  // 加载分类列表
  loadCategories: function() {
    if (this.data.isLoading) {
      return;
    }

    this.setData({
      isLoading: true
    });

    const token = app.globalData.token;
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      this.setData({
        isLoading: false
      });
      return;
    }

    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothing_categories.php`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: (res) => {
        console.log('获取分类列表响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          const categories = res.data.data || [];
          
          // 为每个分类添加editable属性
          const processedCategories = categories.map(cat => ({
            ...cat,
            editable: true // 修改为所有分类都可编辑
          }));
          
          this.setData({
            categories: processedCategories,
            hasMoreData: false // 分类数据通常不需要分页
          });
        } else {
          wx.showToast({
            title: res.data.msg || '获取分类失败',
            icon: 'none'
          });
        }
        
        this.setData({
          isLoading: false
        });
        
        wx.stopPullDownRefresh();
      },
      fail: (err) => {
        console.error('获取分类列表失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        
        this.setData({
          isLoading: false
        });
        
        wx.stopPullDownRefresh();
      }
    });
  },

  // 加载更多
  loadMore: function() {
    // 分类数据通常不需要分页，这里保留接口以备将来使用
    console.log('加载更多分类...');
  },

  // 跳转到添加分类页面
  goToAddCategory: function() {
    wx.navigateTo({
      url: '/pages/clothing-categories/add/add'
    });
  },
  
  // 跳转到分类垃圾箱页面
  goToTrash: function() {
    wx.navigateTo({
      url: '/pages/clothing-categories/trash/trash'
    });
  },

  // 编辑分类
  editCategory: function(e) {
    const category = e.currentTarget.dataset.category;
    
    wx.navigateTo({
      url: `/pages/clothing-categories/edit/edit?id=${category.id}&name=${encodeURIComponent(category.name)}&sort_order=${category.sort_order}&is_system=${category.is_system ? 1 : 0}`
    });
  },

  // 删除分类
  deleteCategory: function(e) {
    const categoryId = e.currentTarget.dataset.id;
    const categoryName = e.currentTarget.dataset.name;
    const isSystem = e.currentTarget.dataset.isSystem;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除分类"${categoryName}"吗？删除后不可恢复。${isSystem ? '这是系统分类，删除后可能影响相关功能。' : ''}`,
      success: (res) => {
        if (res.confirm) {
          this.performDelete(categoryId);
        }
      }
    });
  },

  // 执行删除
  performDelete: function(categoryId) {
    const token = app.globalData.token;
    
    wx.showLoading({
      title: '删除中...'
    });

    wx.request({
      url: `${app.globalData.apiBaseUrl}/delete_clothing_category.php`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      data: {
        id: categoryId
      },
      success: (res) => {
        wx.hideLoading();
        
        console.log('删除分类响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
          
          // 设置全局刷新标志
          app.globalData.needRefreshCategories = true;
          
          // 刷新列表
          this.loadCategories();
        } else {
          wx.showToast({
            title: res.data.msg || '删除失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('删除分类失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  }
}); 