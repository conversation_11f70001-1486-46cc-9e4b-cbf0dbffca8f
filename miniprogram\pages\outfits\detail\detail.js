const app = getApp();

Page({
  data: {
    outfit: null,
    loading: true,
    canvasWidth: 0,
    canvasHeight: 0,
    isCreator: true,  // 默认设置为创建者
    shareUserId: null, // 存储分享者的用户ID
    fromSquare: false, // 是否来自穿搭广场
    isLiked: false,    // 当前用户是否已点赞
    // 新增：重试相关字段
    retryCount: 0,     // 重试次数
    originalDataSource: 'personal', // 原始数据源
    hasRetried: false  // 是否已重试过
  },
  
  onLoad: function(options) {
    // 获取穿搭ID和用户ID（如果是通过分享链接或穿搭广场）
    const outfitId = options.id;
    const shareUserId = options.userId; // 从分享链接或穿搭广场中获取用户ID
    const shareToken = options.token; // 从分享链接中获取临时Token
    const fromSquare = options.fromSquare === 'true'; // 是否来自穿搭广场

    // 新增：获取共享数据参数
    const includeCircleData = options.include_circle_data === 'true';
    const dataSource = options.data_source || 'personal';

    console.log('穿搭详情页参数:', {
      outfitId,
      shareUserId,
      fromSquare,
      includeCircleData,
      dataSource
    });

    if (!outfitId) {
      wx.showToast({
        title: '穿搭ID无效',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    // 保存分享用户ID和来源信息
    this.setData({
      outfitId: outfitId, // 保存穿搭ID用于重试
      shareUserId: shareUserId,
      fromSquare: fromSquare,
      includeCircleData: includeCircleData,
      dataSource: dataSource,
      originalDataSource: dataSource, // 保存原始数据源用于重试
      retryCount: 0,
      hasRetried: false
    });
    
    // 加载穿搭信息
    this.loadOutfit(outfitId, shareUserId, shareToken);
    
    // 获取屏幕信息，调整画布尺寸，保持与编辑页面一致的比例
    wx.getSystemInfo({
      success: (res) => {
        const canvasWidth = res.windowWidth;
        const canvasHeight = canvasWidth * 1.33; // 高宽比例约4:3，与编辑页面保持一致
        
        // 我们在CSS中已经使用了max-height: calc(100vw * 1.33)，不需要在这里设置具体高度
        // 但保留尺寸信息以便其他计算
        this.setData({
          canvasWidth: canvasWidth,
          canvasHeight: canvasHeight
        });
      }
    });
  },
  
  onShow: function() {
    // 每次显示页面时都刷新数据，确保获取最新状态
      const outfitId = this.data.outfit ? this.data.outfit.id : null;
      if (outfitId) {
      console.log('页面重新显示，强制刷新穿搭数据');
        this.loadOutfit(outfitId, this.data.shareUserId);
      }
    
    // 重置全局刷新标志
    if (app.globalData.needRefreshOutfits) {
      app.globalData.needRefreshOutfits = false;
    }
  },
  
  // 加载穿搭信息
  loadOutfit: function(outfitId, shareUserId, shareToken) {
    this.setData({ loading: true });

    // 参数验证：如果outfitId为空，从data中获取
    if (!outfitId) {
      outfitId = this.data.outfitId;
    }

    // 如果仍然没有outfitId，报错返回
    if (!outfitId) {
      console.error('loadOutfit: outfitId参数为空');
      wx.showToast({
        title: '穿搭ID无效',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    // 检查是否来自分享场景或穿搭广场
    const scene = wx.getLaunchOptionsSync().scene;
    const isFromShare = [1007, 1008, 1036, 1044, 1073, 1074].includes(scene) || !!shareUserId;
    const isFromSquare = this.data.fromSquare;
    
    console.log('启动场景:', scene, '是否来自分享:', isFromShare, '是否来自广场:', isFromSquare, '分享用户ID:', shareUserId);
    
    // 只有来自分享的场景或穿搭广场或有分享用户ID时才把isCreator设为false
    const isCreator = !(isFromShare || isFromSquare);
    
    // 强制从服务器获取最新数据，不使用本地缓存
    console.log('强制从服务器获取最新穿搭数据:', outfitId);
    
    // 如果本地没有数据或者是通过用户ID分享的，尝试从服务器获取
    console.log('尝试从服务器获取穿搭:', outfitId, shareUserId ? `用户ID: ${shareUserId}` : '');
    
    // 显示加载中状态
    wx.showLoading({
      title: '加载中...',
      mask: true
    });
    
    // 准备API请求参数
    const requestData = {
      page: 1,
      per_page: 100, // 获取足够多的数据增加找到的概率
    };

    // 根据页面参数决定是否包含圈子数据
    if (this.data.includeCircleData) {
      requestData.include_circle_data = 'true';
      requestData.data_source = this.data.dataSource || 'personal'; // 优先使用个人数据
      console.log('使用共享数据模式:', requestData.data_source);
    } else {
      // 优先使用个人数据，避免权限问题
      requestData.include_circle_data = 'true';
      requestData.data_source = 'personal';
      console.log('使用默认个人数据模式');
    }

    // 添加重试标记，便于后端调试
    if (this.data.retryCount > 0) {
      requestData.retry_count = this.data.retryCount;
    }

    // 如果有分享用户ID，添加到请求参数
    if (shareUserId) {
      requestData.shared_user_id = shareUserId;
      requestData.shared_outfit_id = outfitId;
    }
    
    // 准备请求头
    let headers = {};
    
    // 如果有分享Token，优先使用分享Token
    if (shareToken) {
      headers['Authorization'] = shareToken;
      console.log('使用分享Token请求数据');
    } 
    // 否则使用当前用户Token
    else if (app.globalData.token) {
      headers['Authorization'] = app.globalData.token;
      console.log('使用当前用户Token请求数据');
    } 
    // 如果都没有，则使用模拟访客模式的方式
    else {
      console.log('无可用Token，使用模拟访客模式');
      // 可以在这里添加一个公共的访客Token或标识
      requestData.guest_mode = true;
    }
    
    // 如果来自穿搭广场，直接使用穿搭广场API获取穿搭详情
    if (this.data.fromSquare) {
      this.loadOutfitFromSquare(outfitId, shareUserId);
      return;
    }
    
    // 访问服务器获取穿搭列表
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_outfits.php`,
      method: 'GET',
      data: requestData,
      header: headers,
      success: (res) => {
        console.log('服务器返回穿搭列表:', res.data);
        
        if (res.statusCode === 200 && res.data.success && res.data.data) {
          const outfits = res.data.data;
          
          // 尝试在穿搭列表中查找目标ID
          const outfit = outfits.find(item => item.id.toString() === outfitId.toString());
          
          if (outfit) {
            console.log('在服务器数据中找到穿搭:', outfit);
            
            // 处理服务器返回的数据
            const processedOutfit = {
              id: outfit.id.toString(),
              name: outfit.name || '',
              description: outfit.description || '',
              thumbnail: outfit.thumbnail || '',
              category_id: outfit.category_id ? String(outfit.category_id) : null,
              category_name: outfit.category_name || null,
              items: outfit.items || [],
              created_at: outfit.created_at || new Date().toISOString(),
              updated_at: outfit.updated_at || new Date().toISOString(),
              user_id: outfit.user_id || shareUserId || null,
              is_public: Boolean(outfit.is_public && outfit.is_public !== '0'),

              // 新增：保留共享数据相关字段
              data_source: outfit.data_source || 'personal',
              creator_nickname: outfit.creator_nickname || null,
              circle_id: outfit.circle_id || null,
              is_shared: outfit.is_shared || false,
              is_own: outfit.is_own || false,
              likes_count: outfit.likes_count || 0
            };
            
            // 设置数据
            this.setData({
              outfit: processedOutfit,
              isCreator: isCreator,
              loading: false
            });
            
            // 如果不是分享用户的穿搭，尝试保存到本地，方便下次直接使用
            if (!shareUserId && processedOutfit.items && processedOutfit.items.length > 0) {
              // 获取当前本地存储的穿搭列表
              const localOutfits = app.getLocalOutfits ? app.getLocalOutfits() : [];
              
              // 检查是否已存在此穿搭
              const existingIndex = localOutfits.findIndex(item => item.id === processedOutfit.id);
              
              if (existingIndex === -1) {
                // 不存在则添加
                localOutfits.push(processedOutfit);
                // 保存回本地存储
                if (app.saveLocalOutfits) {
                  app.saveLocalOutfits(localOutfits);
                  console.log('已将服务器穿搭数据保存到本地');
                }
              }
            }
          } else {
            // 未找到对应穿搭，尝试重试或创建空穿搭对象
            console.log('服务器数据中未找到该穿搭:', outfitId);

            // 如果重试次数未达到上限，尝试重试
            if (this.data.retryCount < 2) {
              console.log('尝试使用不同数据源重试');
              this.retryWithDifferentDataSource();
            } else {
              this.createEmptyOutfit(outfitId, isCreator);
            }
          }
        } else {
          // API返回错误，创建空穿搭对象
          console.log('获取穿搭列表失败:', res.data.error || '未知错误');
          
          // 如果是未授权错误并且有本地数据，可以直接使用本地数据
          if (res.data.msg && res.data.msg.includes('未提供授权') && outfit) {
            console.log('使用本地穿搭数据作为备选');
            this.setData({
              outfit: outfit,
              isCreator: false, // 使用分享链接进入时，始终设为非创建者
              loading: false
            });
          } else {
            this.createEmptyOutfit(outfitId, isCreator);
          }
        }
      },
      fail: (err) => {
        console.error('获取穿搭列表请求失败:', err);
        
        // 请求失败但有本地数据，可以使用本地数据
        if (outfit) {
          console.log('使用本地穿搭数据作为备选');
          this.setData({
            outfit: outfit,
            isCreator: false, // 使用分享链接进入时，始终设为非创建者
            loading: false
          });
        } else {
          // 没有本地数据，创建空穿搭对象
          this.createEmptyOutfit(outfitId, isCreator);
        }
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },
  
  // 从穿搭广场加载穿搭信息
  loadOutfitFromSquare: function(outfitId, userId) {
    console.log('从穿搭广场加载穿搭:', outfitId, '用户ID:', userId);
    
    // 显示加载中状态
    wx.showLoading({
      title: '加载中...',
      mask: true
    });
    
    // 请求公开穿搭详情
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_public_outfit_detail.php`,
      method: 'GET',
      data: {
        outfit_id: outfitId,
        user_id: userId
      },
      success: (res) => {
        console.log('服务器返回穿搭详情:', res.data);
        
        if (res.statusCode === 200 && res.data.success && res.data.data) {
          const outfit = res.data.data;
          
          // 处理服务器返回的数据
          const processedOutfit = {
            id: outfit.id.toString(),
            name: outfit.name || '',
            description: outfit.description || '',
            thumbnail: outfit.thumbnail || '',
            category_id: outfit.category_id ? String(outfit.category_id) : null,
            category_name: outfit.category_name || null,
            items: outfit.items || [],
            created_at: outfit.created_at || new Date().toISOString(),
            updated_at: outfit.updated_at || new Date().toISOString(),
            user_id: outfit.user_id || userId || null,
            creator_id: outfit.user_id || userId || null, // 添加creator_id字段
            is_public: Boolean(outfit.is_public && outfit.is_public !== '0'),
            creator_nickname: outfit.creator_nickname || '匿名用户',
            creator_avatar: outfit.creator_avatar || '',
            likes_count: outfit.likes_count || 0
          };
          
          // 设置数据
          this.setData({
            outfit: processedOutfit,
            isCreator: false, // 穿搭广场查看的穿搭一定不是自己的
            loading: false
          });
          
          // 检查用户是否已经点赞过
          this.checkLikeStatus(processedOutfit.id);
        } else {
          // API返回错误，创建空穿搭对象
          console.log('获取穿搭详情失败:', res.data.error || '未知错误');
          this.createEmptyOutfit(outfitId, false);
        }
      },
      fail: (err) => {
        console.error('获取穿搭详情请求失败:', err);
        this.createEmptyOutfit(outfitId, false);
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },
  
  // 检查用户是否已点赞
  checkLikeStatus: function(outfitId) {
    // 如果完全没有token，不检查点赞状态
    if (!app.globalData.token) {
      return;
    }
    
    // 体验账号也能看到点赞状态
    wx.request({
      url: `${app.globalData.apiBaseUrl}/check_outfit_like.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      data: {
        outfit_id: outfitId
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          this.setData({
            isLiked: res.data.is_liked
          });
        }
      }
    });
  },
  
  // 点赞/取消点赞
  toggleLike: function() {
    const outfitId = this.data.outfit.id;
    
    // 未登录用户提示登录 (完全没有token的情况)
    if (!app.globalData.token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 请求参数
    const data = {
      outfit_id: outfitId,
      action: this.data.isLiked ? 'unlike' : 'like'
    };
    
    // 先本地更新UI，提高响应速度
    const newLikeCount = this.data.isLiked ? 
      Math.max(0, (this.data.outfit.likes_count || 0) - 1) : 
      (this.data.outfit.likes_count || 0) + 1;
    
    this.setData({
      isLiked: !this.data.isLiked,
      'outfit.likes_count': newLikeCount
    });
    
    // 发送请求
    wx.request({
      url: `${app.globalData.apiBaseUrl}/toggle_outfit_like.php`,
      method: 'POST',
      header: {
        'Authorization': app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: data,
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          console.log('点赞状态更新成功:', res.data);
        } else {
          console.error('点赞操作失败:', res.data);
          // 恢复之前的状态
          const originalLikeCount = this.data.isLiked ? 
            (this.data.outfit.likes_count || 0) + 1 : 
            Math.max(0, (this.data.outfit.likes_count || 0) - 1);
          
          this.setData({
            isLiked: !this.data.isLiked,
            'outfit.likes_count': originalLikeCount
          });
          
          wx.showToast({
            title: '操作失败，请重试',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('点赞请求失败:', err);
        // 恢复之前的状态
        const originalLikeCount = this.data.isLiked ? 
          (this.data.outfit.likes_count || 0) + 1 : 
          Math.max(0, (this.data.outfit.likes_count || 0) - 1);
        
        this.setData({
          isLiked: !this.data.isLiked,
          'outfit.likes_count': originalLikeCount
        });
        
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 使用不同数据源重试
  retryWithDifferentDataSource: function() {
    console.log('开始使用不同数据源重试');

    const currentRetryCount = this.data.retryCount;
    const currentDataSource = this.data.dataSource;

    // 增加重试次数
    const newRetryCount = currentRetryCount + 1;

    // 定义重试序列：personal -> all -> personal_only
    let newDataSource;
    let includeCircleData = true;

    if (currentRetryCount === 0) {
      // 第一次重试：如果当前是personal，尝试all；如果是all，尝试personal
      if (currentDataSource === 'personal') {
        newDataSource = 'all';
      } else {
        newDataSource = 'personal';
      }
    } else if (currentRetryCount === 1) {
      // 第二次重试：尝试仅查询个人数据（不包含圈子数据）
      newDataSource = 'personal';
      includeCircleData = false;
    } else {
      // 超过重试次数，显示错误对话框
      console.log('已达到最大重试次数，显示错误对话框');
      this.showOutfitNotFoundDialog();
      return;
    }

    console.log(`第${newRetryCount}次重试：从数据源 ${currentDataSource} 切换到 ${newDataSource}，包含圈子数据: ${includeCircleData}`);

    // 更新数据源并重新加载
    this.setData({
      dataSource: newDataSource,
      includeCircleData: includeCircleData,
      retryCount: newRetryCount,
      hasRetried: true
    });

    // 重新加载穿搭
    this.loadOutfit(this.data.outfitId, this.data.shareUserId);
  },

  // 显示穿搭未找到对话框
  showOutfitNotFoundDialog: function() {
    // 根据重试次数提供不同的提示信息
    let content = '无法加载穿搭详情。';

    if (this.data.retryCount > 0) {
      content += '这可能是因为您已退出相关圈子，或穿搭已被删除。';
    } else {
      content += '可能是网络问题或权限变更导致的。';
    }

    wx.showModal({
      title: '穿搭信息',
      content: content,
      confirmText: '强制重试',
      cancelText: '返回',
      success: (res) => {
        if (res.confirm) {
          // 强制重试：重置所有状态，仅查询个人数据
          this.forceRetryWithPersonalData();
        } else {
          wx.navigateBack();
        }
      }
    });
  },

  // 强制重试：仅查询个人数据
  forceRetryWithPersonalData: function() {
    console.log('强制重试：仅查询个人数据');

    wx.showLoading({
      title: '重新加载中...',
      mask: true
    });

    // 重置所有状态，强制使用个人数据模式
    this.setData({
      dataSource: 'personal',
      includeCircleData: false,
      retryCount: 0,
      hasRetried: false
    });

    // 重新加载穿搭
    this.loadOutfit(this.data.outfitId, this.data.shareUserId);
  },

  // 创建空穿搭对象，确保界面能正常显示
  createEmptyOutfit: function(outfitId, isCreator) {
    console.log('创建空穿搭对象');
    
    const emptyOutfit = {
      id: outfitId,
      name: '分享的穿搭',
      description: '',
      thumbnail: '',
      items: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_public: false
    };
    
    this.setData({
      outfit: emptyOutfit,
      isCreator: false, // 强制设置为非创建者，这样会显示"我也要穿搭"按钮
      loading: false
    });
    
    // 显示友好提示
    wx.showToast({
      title: '无法加载完整穿搭信息',
      icon: 'none',
      duration: 2000
    });
  },
  
  // 切换穿搭公开状态
  togglePublicStatus: function(e) {
    // 获取开关新状态
    const isPublic = e.detail.value;
    
    // 如果用户将状态设为公开，弹出确认窗口
    if (isPublic) {
      wx.showModal({
        title: '确认公开',
        content: '公开后将在穿搭广场对其他用户可见，是否确认公开？',
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 用户点击确定，更新状态
            this.updateOutfitPublicStatus(isPublic);
          } else {
            // 用户点击取消，恢复开关状态
            this.setData({
              'outfit.is_public': false
            });
          }
        }
      });
    } else {
      // 直接设置为不公开，无需确认
      this.updateOutfitPublicStatus(isPublic);
    }
  },
  
  // 发送API请求更新穿搭公开状态
  updateOutfitPublicStatus: function(isPublic) {
    // 显示加载中提示
    wx.showLoading({
      title: isPublic ? '正在公开...' : '正在设为私有...',
      mask: true
    });
    
    // 发送请求到服务器
    wx.request({
      url: `${app.globalData.apiBaseUrl}/update_outfit_status.php`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': app.globalData.token
      },
      data: {
        outfit_id: this.data.outfit.id,
        is_public: isPublic ? 1 : 0
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          console.log('穿搭公开状态更新成功:', res.data);
          
          // 更新成功
          this.setData({
            'outfit.is_public': isPublic
          });
          
          wx.showToast({
            title: isPublic ? '已公开' : '已设为私有',
            icon: 'success',
            duration: 2000
          });
          
          console.log('穿搭公开状态已更新:', isPublic);
          
          // 标记需要刷新穿搭列表
          app.globalData.needRefreshOutfits = true;
          
          // 更新本地缓存中的穿搭数据
          this.updateLocalOutfitCache(this.data.outfit.id, {is_public: isPublic});
        } else {
          // 更新失败
          this.setData({
            'outfit.is_public': !isPublic // 恢复之前的状态
          });
          
          wx.showModal({
            title: '操作失败',
            content: res.data.error || '无法更新穿搭状态，请稍后重试',
            showCancel: false
          });
        }
      },
      fail: (err) => {
        console.error('更新穿搭状态失败:', err);
        
        // 恢复之前的状态
        this.setData({
          'outfit.is_public': !isPublic
        });
        
        wx.showModal({
          title: '网络错误',
          content: '无法连接服务器，请检查网络后重试',
          showCancel: false
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },
  
  // 更新本地缓存中的穿搭数据
  updateLocalOutfitCache: function(outfitId, updatedData) {
    if (!outfitId || !updatedData) return;
    
    console.log('尝试更新本地穿搭缓存:', outfitId, updatedData);
    
    // 检查是否有全局方法获取和保存本地穿搭
    if (typeof app.getLocalOutfits !== 'function' || typeof app.saveLocalOutfits !== 'function') {
      console.log('全局方法不存在，无法更新本地缓存');
      return;
    }
    
    // 获取本地缓存的穿搭列表
    const localOutfits = app.getLocalOutfits();
    if (!Array.isArray(localOutfits)) {
      console.log('本地缓存不存在或格式不正确');
      return;
    }
    
    // 查找要更新的穿搭
    const outfitIndex = localOutfits.findIndex(item => item.id === outfitId);
    if (outfitIndex === -1) {
      console.log('在本地缓存中未找到穿搭:', outfitId);
      return;
    }
    
    // 更新穿搭数据
    localOutfits[outfitIndex] = {
      ...localOutfits[outfitIndex],
      ...updatedData
    };
    
    // 保存回本地缓存
    app.saveLocalOutfits(localOutfits);
    console.log('本地穿搭缓存已更新');
    
    // 同时更新其他可能的缓存
    if (typeof app.refreshOutfits === 'function') {
      app.refreshOutfits();
      console.log('已触发全局穿搭列表刷新');
    }
  },
  
  // 前往编辑页面
  goToEdit: function() {
    // 仅创建者可编辑
    if (!this.data.isCreator) {
      return;
    }
    
    wx.navigateTo({
      url: `/pages/outfits/edit/edit?id=${this.data.outfit.id}`
    });
  },
  
  // 返回列表页
  goBack: function() {
    wx.navigateBack();
  },
  
  // 删除穿搭
  deleteOutfit: function() {
    // 仅创建者可删除
    if (!this.data.isCreator) {
      return;
    }
    
    const outfitName = this.data.outfit.name || '未命名穿搭';
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除 "${outfitName}" 穿搭吗？`,
      confirmColor: '#FF0000',
      success: (res) => {
        if (res.confirm) {
          // 显示删除中状态
          wx.showLoading({
            title: '删除中...',
            mask: true
          });
          
          app.deleteOutfit(this.data.outfit.id, (result) => {
            wx.hideLoading();
            
            if (result.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success',
                duration: 2000
              });
              
              // 返回列表页
              wx.navigateBack();
            } else {
              wx.showModal({
                title: '删除失败',
                content: result.error || '无法删除穿搭，请稍后重试',
                showCancel: false
              });
            }
          });
        }
      }
    });
  },
  
  // 引导用户登录
  goToLogin: function() {
    // 总是引导到登录页面，不管用户是否已登录
    // 通过redirect参数告诉登录页面，登录成功后应该跳转到哪里
    wx.navigateTo({
      url: '/pages/login/login?redirect=outfits&action=create'
    });
  },
  
  // 生成一个简单的临时令牌
  generateShareToken: function() {
    // 如果用户已登录，则使用其token
    if (app.globalData.token) {
      return app.globalData.token;
    }
    
    // 否则生成一个带有时间戳的标记，防止恶意使用
    const timestamp = new Date().getTime();
    const randomStr = Math.random().toString(36).substring(2, 15);
    const shareToken = `SHARE_${timestamp}_${randomStr}`;
    
    return shareToken;
  },
  
  // 分享给好友
  onShareAppMessage: function() {
    if (!this.data.outfit) {
      return {
        title: '我的穿搭',
        path: '/pages/outfits/index/index'
      };
    }
    
    // 获取当前用户ID
    const currentUserId = app.globalData.userInfo ? app.globalData.userInfo.id : null;
    // 使用穿搭中的用户ID或当前用户ID
    const userId = this.data.outfit.user_id || currentUserId;
    // 生成临时分享Token
    const shareToken = this.generateShareToken();
    
    return {
      title: `我的穿搭: ${this.data.outfit.name || '未命名穿搭'}`,
      path: `/pages/outfits/detail/detail?id=${this.data.outfit.id}&userId=${userId}&token=${shareToken}`,
      imageUrl: this.data.outfit.thumbnail || (this.data.outfit.items.length > 0 ? this.data.outfit.items[0].clothing_data.image_url : '')
    };
  },
  
  // 分享到朋友圈
  onShareTimeline: function() {
    if (!this.data.outfit) {
      return {
        title: '我的穿搭',
        query: ''
      };
    }
    
    // 获取当前用户ID
    const currentUserId = app.globalData.userInfo ? app.globalData.userInfo.id : null;
    // 使用穿搭中的用户ID或当前用户ID
    const userId = this.data.outfit.user_id || currentUserId;
    // 生成临时分享Token
    const shareToken = this.generateShareToken();
    
    return {
      title: `我的穿搭: ${this.data.outfit.name || '未命名穿搭'}`,
      query: `id=${this.data.outfit.id}&userId=${userId}&token=${shareToken}`,
      imageUrl: this.data.outfit.thumbnail || (this.data.outfit.items.length > 0 ? this.data.outfit.items[0].clothing_data.image_url : '')
    };
  },

  // 查看创建者的穿搭列表
  viewUserOutfits: function() {
    console.log('查看创建者穿搭，当前穿搭数据:', this.data.outfit);

    if (!this.data.outfit) {
      wx.showToast({
        title: '无法获取穿搭信息',
        icon: 'none'
      });
      return;
    }

    // 尝试从多个可能的字段中获取创建者ID
    const userId = this.data.outfit.creator_id || this.data.outfit.user_id || '';

    if (!userId) {
      console.error('无法获取创建者ID，穿搭数据:', this.data.outfit);
      wx.showToast({
        title: '无法获取创建者信息',
        icon: 'none'
      });
      return;
    }

    const userName = this.data.outfit.creator_nickname || '匿名用户';
    console.log('跳转到用户穿搭列表，用户ID:', userId, '用户名:', userName);

    // 跳转到用户穿搭页面
    wx.navigateTo({
      url: `/pages/user_outfits/index/index?user_id=${userId}&user_name=${encodeURIComponent(userName)}`
    });
  },

  // 查看衣物详情
  viewClothingDetail: function(e) {
    const clothingId = e.currentTarget.dataset.clothingId;
    const clothingData = e.currentTarget.dataset.clothingData;

    console.log('点击查看衣物详情，衣物ID:', clothingId, '衣物数据:', clothingData);
    console.log('当前页面数据源状态:', {
      includeCircleData: this.data.includeCircleData,
      dataSource: this.data.dataSource,
      shareUserId: this.data.shareUserId,
      fromSquare: this.data.fromSquare
    });

    if (!clothingId) {
      wx.showToast({
        title: '衣物信息不完整',
        icon: 'none'
      });
      return;
    }

    // 构建跳转参数
    let url = `/pages/clothing/detail/detail?id=${clothingId}`;

    // 总是传递数据源信息，确保一致性
    const currentDataSource = this.data.dataSource || 'personal';
    const currentIncludeCircleData = this.data.includeCircleData !== false;

    url += `&include_circle_data=${currentIncludeCircleData}&data_source=${currentDataSource}`;

    // 根据当前页面状态添加额外参数，保持上下文
    if (this.data.shareUserId) {
      url += `&shareUserId=${this.data.shareUserId}`;
    }

    // 如果来自穿搭广场，传递相关信息
    if (this.data.fromSquare) {
      url += `&fromSquare=true`;
    }

    console.log('跳转到衣物详情页，URL:', url);

    wx.navigateTo({
      url: url,
      fail: (err) => {
        console.error('跳转到衣物详情页失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 下载穿搭图片
  downloadOutfitImage: function() {
    const that = this;

    // 检查是否有穿搭数据
    if (!this.data.outfit || !this.data.outfit.items || this.data.outfit.items.length === 0) {
      wx.showToast({
        title: '暂无穿搭内容',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '生成图片中...'
    });

    // 获取outfit-view的尺寸
    const query = wx.createSelectorQuery();
    query.select('#outfit-view').boundingClientRect();
    query.exec((res) => {
      if (res[0]) {
        const rect = res[0];
        that.generateOutfitImage(rect.width, rect.height);
      } else {
        wx.hideLoading();
        wx.showToast({
          title: '获取尺寸失败',
          icon: 'none'
        });
      }
    });
  },

  // 生成穿搭图片
  generateOutfitImage: function(viewWidth, viewHeight) {
    const that = this;

    // 计算Canvas尺寸，保持与view相同的宽高比
    const maxWidth = 600;
    const maxHeight = 800;
    const viewRatio = viewWidth / viewHeight;

    let canvasWidth, canvasHeight;
    if (viewRatio > maxWidth / maxHeight) {
      // 宽度优先
      canvasWidth = maxWidth;
      canvasHeight = maxWidth / viewRatio;
    } else {
      // 高度优先
      canvasHeight = maxHeight;
      canvasWidth = maxHeight * viewRatio;
    }

    // 计算缩放比例
    const scale = canvasWidth / viewWidth;

    console.log('Canvas尺寸计算:', {
      viewWidth, viewHeight, viewRatio,
      canvasWidth, canvasHeight, scale
    });

    // 设置Canvas的实际尺寸
    const query = wx.createSelectorQuery();
    query.select('#outfitCanvas').node().exec((res) => {
      if (res[0] && res[0].node) {
        const canvas = res[0].node;
        canvas.width = canvasWidth;
        canvas.height = canvasHeight;

        const ctx = canvas.getContext('2d');

        // 清空canvas
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);

        // 设置白色背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        that.drawItemsOnCanvas(ctx, canvasWidth, canvasHeight, scale);
      } else {
        // 降级到旧版API
        const ctx = wx.createCanvasContext('outfitCanvas');

        // 清空canvas
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);

        // 设置白色背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        that.drawItemsOnCanvas(ctx, canvasWidth, canvasHeight, scale);
      }
    });
  },

  // 在Canvas上绘制衣物
  drawItemsOnCanvas: function(ctx, canvasWidth, canvasHeight, scale) {
    const that = this;
    const items = this.data.outfit.items;
    let loadedCount = 0;

    if (items.length === 0) {
      // 没有衣物，直接添加水印并保存
      this.addWatermarkAndSave(ctx, canvasWidth, canvasHeight);
      return;
    }

    const drawItem = (item, index) => {
      wx.getImageInfo({
        src: item.clothing_data.image_url,
        success: (imgRes) => {
          // 计算在canvas中的位置和尺寸，保持比例
          const x = item.position.x * scale;
          const y = item.position.y * scale;
          const width = item.size.width * scale;
          const height = item.size.height * scale;

          console.log(`绘制衣物 ${index}:`, {
            original: { x: item.position.x, y: item.position.y, w: item.size.width, h: item.size.height },
            scaled: { x, y, width, height },
            rotation: item.rotation
          });

          // 保存当前状态
          ctx.save();

          // 移动到旋转中心
          ctx.translate(x + width/2, y + height/2);

          // 旋转
          ctx.rotate(item.rotation * Math.PI / 180);

          // 绘制图片
          ctx.drawImage(imgRes.path, -width/2, -height/2, width, height);

          // 恢复状态
          ctx.restore();

          loadedCount++;

          // 所有图片加载完成后添加水印并保存
          if (loadedCount === items.length) {
            that.addWatermarkAndSave(ctx, canvasWidth, canvasHeight);
          }
        },
        fail: () => {
          console.error(`加载衣物图片失败: ${item.clothing_data.image_url}`);
          loadedCount++;
          if (loadedCount === items.length) {
            that.addWatermarkAndSave(ctx, canvasWidth, canvasHeight);
          }
        }
      });
    };

    // 按z-index排序后绘制
    const sortedItems = [...items].sort((a, b) => a.z_index - b.z_index);
    sortedItems.forEach((item, index) => {
      drawItem(item, index);
    });
  },

  // 添加水印并保存
  addWatermarkAndSave: function(ctx, canvasWidth, canvasHeight) {
    // 添加水印
    ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
    ctx.font = '24px Arial';
    ctx.textAlign = 'right';

    const watermarkText = '次元衣帽间';
    const padding = 20;

    ctx.fillText(watermarkText, canvasWidth - padding, canvasHeight - padding);

    // 检查是否是新版Canvas API
    if (ctx.draw) {
      // 旧版API
      ctx.draw(false, () => {
        this.saveCanvasImage('outfitCanvas');
      });
    } else {
      // 新版API，直接保存
      this.saveCanvasImage('#outfitCanvas');
    }
  },

  // 保存Canvas图片
  saveCanvasImage: function(canvasSelector) {
    const that = this;
    // 延迟一下确保绘制完成
    setTimeout(() => {
      if (canvasSelector.startsWith('#')) {
        // 新版Canvas API
        const query = wx.createSelectorQuery();
        query.select(canvasSelector).node().exec((res) => {
          if (res[0] && res[0].node) {
            const canvas = res[0].node;
            wx.canvasToTempFilePath({
              canvas: canvas,
              success: (res) => {
                that.saveToAlbum(res.tempFilePath);
              },
              fail: () => {
                wx.hideLoading();
                wx.showToast({
                  title: '生成图片失败',
                  icon: 'none'
                });
              }
            });
          }
        });
      } else {
        // 旧版Canvas API
        wx.canvasToTempFilePath({
          canvasId: canvasSelector,
          success: (res) => {
            that.saveToAlbum(res.tempFilePath);
          },
          fail: () => {
            wx.hideLoading();
            wx.showToast({
              title: '生成图片失败',
              icon: 'none'
            });
          }
        });
      }
    }, 500);
  },

  // 保存到相册
  saveToAlbum: function(filePath) {
    wx.hideLoading();

    // 保存到相册
    wx.saveImageToPhotosAlbum({
      filePath: filePath,
      success: () => {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        if (err.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '提示',
            content: '需要授权访问相册才能保存图片',
            confirmText: '去设置',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting();
              }
            }
          });
        } else {
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      }
    });
  },

  // 分享功能
  onShareAppMessage: function() {
    const outfit = this.data.outfit;
    if (!outfit) {
      return {
        title: '穿搭分享',
        path: '/pages/outfits/detail/detail'
      };
    }

    return {
      title: `分享穿搭：${outfit.name}`,
      path: `/pages/outfits/detail/detail?id=${outfit.id}&userId=${outfit.user_id}`,
      imageUrl: outfit.thumbnail || ''
    };
  }
});