<view class="container">
  <!-- 管理提示 -->
  <view class="manage-hint">
    点击选择衣物，可进行编辑或删除操作
  </view>
  
  <!-- 分类标签栏 -->
  <scroll-view scroll-x="true" class="category-tabs">
    <view class="tabs-container">
      <view class="tab-item {{currentCategory === 'all' ? 'active' : ''}}" data-category="all" bindtap="switchCategory">全部</view>
      <view class="tab-item {{currentCategory === 'tops' ? 'active' : ''}}" data-category="tops" bindtap="switchCategory">上衣</view>
      <view class="tab-item {{currentCategory === 'pants' ? 'active' : ''}}" data-category="pants" bindtap="switchCategory">裤子</view>
      <view class="tab-item {{currentCategory === 'skirts' ? 'active' : ''}}" data-category="skirts" bindtap="switchCategory">裙子</view>
      <view class="tab-item {{currentCategory === 'outerwear' ? 'active' : ''}}" data-category="outerwear" bindtap="switchCategory">外套</view>
      <view class="tab-item {{currentCategory === 'shoes' ? 'active' : ''}}" data-category="shoes" bindtap="switchCategory">鞋子</view>
      <view class="tab-item {{currentCategory === 'bags' ? 'active' : ''}}" data-category="bags" bindtap="switchCategory">包包</view>
      <view class="tab-item {{currentCategory === 'accessories' ? 'active' : ''}}" data-category="accessories" bindtap="switchCategory">配饰</view>
    </view>
  </scroll-view>
  
  <!-- 衣物列表 -->
  <scroll-view scroll-y="true" class="clothes-container">
    <view class="clothes-grid">
      <view wx:for="{{clothingList}}" wx:key="id" class="clothes-item {{item.selected ? 'selected' : ''}}" data-id="{{item.id}}" bindtap="toggleSelect">
        <image src="{{item.image_url}}" mode="aspectFit" class="clothes-img"></image>
        <!-- 显示衣物名称 -->
        <view class="clothes-name" wx:if="{{showClothingName && item.name}}">{{item.name}}</view>
        <view class="select-icon">
          <text class="check-icon">✓</text>
        </view>
      </view>
    </view>
  </scroll-view>
  
  <!-- 编辑工具栏 -->
  <view class="edit-toolbar" wx:if="{{hasSelected}}">
    <view class="edit-btn" bindtap="editSelected">
      <text class="icon-edit">✎</text> 编辑
    </view>
    <view class="delete-btn" bindtap="deleteSelected">
      <text class="icon-delete">🗑</text> 删除
    </view>
  </view>
</view> 