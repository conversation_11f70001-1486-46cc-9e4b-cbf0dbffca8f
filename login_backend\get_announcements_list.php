<?php
// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入数据库连接
require_once('db.php');
require_once('auth.php');

// 检查授权
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => '未授权访问',
        'code' => 401
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// 验证管理员token
try {
    $auth = new Auth();
    $payload = $auth->verifyAdminToken($token);
    
    if (!$payload) {
        echo json_encode([
            'error' => true,
            'msg' => '无效的授权令牌',
            'code' => 401
        ]);
        exit;
    }
    
    $db = new Database();
    $conn = $db->getConnection();
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => $e->getMessage(),
        'code' => 500
    ]);
    exit;
}

// 获取查询参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status = isset($_GET['status']) ? trim($_GET['status']) : '';

// 验证参数
if ($page < 1) $page = 1;
if ($limit < 1 || $limit > 100) $limit = 10;

// 计算偏移量
$offset = ($page - 1) * $limit;

try {
    // 构建WHERE子句
    $whereClause = "";
    $params = [];
    
    if (!empty($search)) {
        $whereClause .= "title LIKE :search";
        $params[':search'] = "%{$search}%";
    }
    
    if ($status !== '') {
        if (!empty($whereClause)) {
            $whereClause .= " AND ";
        }
        $whereClause .= "status = :status";
        $params[':status'] = $status;
    }
    
    // 构建SQL语句
    $countSql = "SELECT COUNT(*) FROM announcements";
    if (!empty($whereClause)) {
        $countSql .= " WHERE {$whereClause}";
    }
    
    $listSql = "SELECT id, title, content, start_time, end_time, status, created_at FROM announcements";
    if (!empty($whereClause)) {
        $listSql .= " WHERE {$whereClause}";
    }
    $listSql .= " ORDER BY created_at DESC LIMIT :offset, :limit";
    
    // 获取总记录数
    $countStmt = $conn->prepare($countSql);
    
    // 绑定参数
    foreach ($params as $key => $value) {
        $countStmt->bindValue($key, $value);
    }
    
    $countStmt->execute();
    $total = $countStmt->fetchColumn();
    
    // 获取列表数据
    $listStmt = $conn->prepare($listSql);
    
    // 绑定参数
    foreach ($params as $key => $value) {
        $listStmt->bindValue($key, $value);
    }
    
    // 添加分页参数
    $listStmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $listStmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    
    $listStmt->execute();
    
    // 获取结果
    $announcements = [];
    while ($row = $listStmt->fetch(PDO::FETCH_ASSOC)) {
        // 截断内容预览
        $content_preview = mb_substr(strip_tags($row['content']), 0, 100);
        if (mb_strlen($row['content']) > 100) {
            $content_preview .= '...';
        }
        
        $row['content_preview'] = $content_preview;
        $announcements[] = $row;
    }
    
    // 返回结果
    echo json_encode([
        'error' => false,
        'msg' => 'success',
        'data' => [
            'list' => $announcements,
            'page' => $page,
            'limit' => $limit,
            'total' => $total,
            'total_pages' => ceil($total / $limit)
        ]
    ]);
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => $e->getMessage(),
        'code' => 500
    ]);
}
?> 