<?php
/**
 * 完整测试共享数据源的分类和衣物显示
 * 模拟前端API调用流程
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "=== 完整共享数据源测试 ===\n";
    echo "当前用户ID: $userId\n\n";
    
    // 1. 模拟前端调用分类API (shared数据源)
    echo "1. 模拟分类API调用 (shared数据源):\n";
    
    // 模拟get_clothing_categories.php的查询逻辑
    $includeCircleData = true;
    $dataSource = 'shared';
    
    $sql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                   u.nickname as creator_nickname,
                   CASE 
                       WHEN c.is_system = 1 THEN 'system'
                       WHEN c.circle_id IS NULL THEN 'personal' 
                       ELSE 'shared' 
                   END as data_source
            FROM clothing_categories c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE (
                -- 当前用户的系统分类（用于显示系统分类下的共享衣物）
                (c.is_system = 1 AND c.user_id = :user_id) OR
                -- 其他用户的自定义分类（已同步到圈子的）
                (c.is_system = 0 AND c.user_id != :user_id AND c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'))
            )
            ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC";
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "分类API返回数量: " . count($categories) . "\n";
    foreach ($categories as $cat) {
        $typeLabel = $cat['is_system'] ? '[系统]' : '[自定义]';
        $creatorInfo = $cat['creator_nickname'] ? " (创建者: {$cat['creator_nickname']})" : '';
        echo "- $typeLabel {$cat['name']} (code: {$cat['code']}, data_source: {$cat['data_source']})$creatorInfo\n";
    }
    echo "\n";
    
    // 2. 对每个分类测试衣物数量
    echo "2. 测试每个分类下的衣物数量 (shared数据源):\n";
    
    foreach ($categories as $cat) {
        // 模拟get_clothes.php的查询逻辑 (shared数据源 + 分类过滤)
        $clothesSql = "SELECT c.id, c.name, c.category, c.user_id, c.circle_id, u.nickname as creator_nickname,
                              CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                       FROM clothes c
                       LEFT JOIN users u ON c.user_id = u.id
                       WHERE c.circle_id IS NOT NULL AND c.user_id != :user_id AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
                         AND c.category = :category_code
                       ORDER BY c.created_at DESC
                       LIMIT 5";
        
        $clothesStmt = $conn->prepare($clothesSql);
        $clothesStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $clothesStmt->bindParam(':category_code', $cat['code'], PDO::PARAM_STR);
        $clothesStmt->execute();
        $clothes = $clothesStmt->fetchAll(PDO::FETCH_ASSOC);
        
        $typeLabel = $cat['is_system'] ? '[系统]' : '[自定义]';
        echo "- $typeLabel {$cat['name']} (code: {$cat['code']}): " . count($clothes) . " 件衣物\n";
        
        foreach ($clothes as $item) {
            echo "  * {$item['name']} - 创建者: {$item['creator_nickname']} (用户{$item['user_id']})\n";
        }
    }
    echo "\n";
    
    // 3. 测试"全部"分类下的衣物 (shared数据源)
    echo "3. 测试'全部'分类下的衣物 (shared数据源):\n";
    
    $allClothesSql = "SELECT c.id, c.name, c.category, c.user_id, c.circle_id, u.nickname as creator_nickname,
                             CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                      FROM clothes c
                      LEFT JOIN users u ON c.user_id = u.id
                      WHERE c.circle_id IS NOT NULL AND c.user_id != :user_id AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
                      ORDER BY c.created_at DESC
                      LIMIT 10";
    
    $allClothesStmt = $conn->prepare($allClothesSql);
    $allClothesStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $allClothesStmt->execute();
    $allClothes = $allClothesStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "全部衣物数量: " . count($allClothes) . "\n";
    
    // 按分类统计
    $categoryStats = [];
    foreach ($allClothes as $item) {
        if (!isset($categoryStats[$item['category']])) {
            $categoryStats[$item['category']] = 0;
        }
        $categoryStats[$item['category']]++;
    }
    
    echo "按分类统计:\n";
    foreach ($categoryStats as $catCode => $count) {
        // 查找分类名称
        $catName = $catCode;
        foreach ($categories as $cat) {
            if ($cat['code'] === $catCode) {
                $catName = $cat['name'];
                break;
            }
        }
        echo "- $catName ($catCode): $count 件\n";
    }
    echo "\n";
    
    // 4. 对比测试 - all数据源
    echo "4. 对比测试 - all数据源分类:\n";
    
    $allDataSourceSql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                                u.nickname as creator_nickname,
                                CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                         FROM clothing_categories c
                         LEFT JOIN users u ON c.user_id = u.id
                         WHERE (
                             -- 当前用户的系统分类（每个用户都有自己的系统分类副本）
                             (c.is_system = 1 AND c.user_id = :user_id) OR
                             -- 所有自定义分类：个人的 + 圈子共享的
                             (c.is_system = 0 AND ((c.user_id = :user_id AND c.circle_id IS NULL) OR
                              (c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'))))
                         )
                         ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC";
    
    $allDataStmt = $conn->prepare($allDataSourceSql);
    $allDataStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $allDataStmt->execute();
    $allDataCategories = $allDataStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "all数据源分类数量: " . count($allDataCategories) . "\n";
    $systemCount = 0;
    $personalCount = 0;
    $sharedCount = 0;
    
    foreach ($allDataCategories as $cat) {
        if ($cat['is_system']) {
            $systemCount++;
        } elseif ($cat['data_source'] === 'personal') {
            $personalCount++;
        } else {
            $sharedCount++;
        }
    }
    
    echo "统计: 系统分类 $systemCount 个，个人自定义分类 $personalCount 个，共享自定义分类 $sharedCount 个\n\n";
    
    // 5. 总结和建议
    echo "5. 测试总结:\n";
    
    $sharedSystemCount = 0;
    $sharedCustomCount = 0;
    foreach ($categories as $cat) {
        if ($cat['is_system']) {
            $sharedSystemCount++;
        } else {
            $sharedCustomCount++;
        }
    }
    
    echo "✅ shared数据源分类显示:\n";
    echo "- 系统分类: $sharedSystemCount 个 " . ($sharedSystemCount > 0 ? "✅ 正确显示" : "❌ 缺失") . "\n";
    echo "- 其他用户自定义分类: $sharedCustomCount 个 " . ($sharedCustomCount >= 0 ? "✅ 正确显示" : "❌ 异常") . "\n";
    echo "- 共享衣物总数: " . count($allClothes) . " 件\n";
    echo "- 有衣物的分类数: " . count($categoryStats) . " 个\n";
    echo "\n";
    
    if ($sharedSystemCount > 0 && count($allClothes) > 0) {
        echo "🎉 修复成功！\n";
        echo "- shared数据源正确显示了系统分类，用户可以看到系统分类下的共享衣物\n";
        echo "- 同时显示了其他用户的自定义分类\n";
        echo "- 衣物数据正确排除了用户自己的数据\n";
    } elseif ($sharedSystemCount == 0) {
        echo "⚠️ 系统分类缺失，可能需要检查用户的系统分类初始化\n";
    } elseif (count($allClothes) == 0) {
        echo "⚠️ 没有共享衣物数据，可能需要检查数据同步状态\n";
    }
    
    echo "\n💡 前端使用建议:\n";
    echo "- 在分类选择器中，可以根据data_source字段区分显示系统分类和自定义分类\n";
    echo "- 系统分类显示为普通分类，自定义分类可以显示创建者信息\n";
    echo "- 确保'全部'选项能正确显示所有共享衣物\n";
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
