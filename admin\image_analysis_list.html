<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>形象分析管理 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .data-table th {
            background-color: #f7f7f7;
            padding: 12px 15px;
            text-align: left;
            font-weight: 500;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .data-table tr:last-child td {
            border-bottom: none;
        }
        
        .data-table tr:hover {
            background-color: #f9f9f9;
        }
        
        /* 标签状态样式 */
        .status-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        
        .status-processing {
            background-color: #fcf4e6;
            color: #fa8c16;
        }
        
        .status-completed {
            background-color: #f6ffed;
            color: #52c41a;
        }
        
        .status-failed {
            background-color: #fff2f0;
            color: #f5222d;
        }
        
        .payment-unpaid {
            background-color: #fff7e6;
            color: #fa8c16;
        }
        
        .payment-paid {
            background-color: #f6ffed;
            color: #52c41a;
        }
        
        .payment-refunded {
            background-color: #f9f0ff;
            color: #722ed1;
        }
        
        /* 操作按钮 */
        .action-btn {
            padding: 5px 10px;
            margin-right: 5px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background-color: white;
            color: #1890ff;
            cursor: pointer;
            font-size: 14px;
        }
        
        .action-btn:hover {
            border-color: #1890ff;
            background-color: #e6f7ff;
        }
        
        .delete-btn {
            color: #ff4d4f;
        }
        
        .delete-btn:hover {
            border-color: #ff4d4f;
            background-color: #fff1f0;
        }
        
        /* 搜索和筛选区域 */
        .filter-section {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .search-box {
            flex: 1;
            min-width: 250px;
        }
        
        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-label {
            font-size: 14px;
            color: #666;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            min-width: 120px;
        }
        
        .search-btn {
            padding: 8px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .reset-btn {
            padding: 8px 16px;
            background-color: white;
            color: #666;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        /* 分页控件 */
        .pagination {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-top: 20px;
        }
        
        .pagination-info {
            margin-right: 15px;
            color: #666;
            font-size: 14px;
        }
        
        .pagination-btn {
            padding: 6px 10px;
            margin: 0 2px;
            border: 1px solid #d9d9d9;
            background-color: white;
            color: #666;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .pagination-btn.active {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        
        .pagination-btn:hover:not(.active) {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .pagination-btn:disabled {
            color: #d9d9d9;
            cursor: not-allowed;
        }
        
        /* 加载状态 */
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 50px 0;
        }
        
        .loading-spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left: 4px solid #1890ff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 缩略图样式 */
        .thumbnail {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-right: 8px;
            vertical-align: middle;
        }
        
        /* 用户信息样式 */
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-name {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>形象分析管理</h2>
                <div class="user-info">
                    <span id="userName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="filter-section">
                <div class="search-box">
                    <input type="text" id="searchInput" class="search-input" placeholder="搜索用户ID、分析ID或订单ID">
                </div>
                <div class="filter-group">
                    <span class="filter-label">状态:</span>
                    <select id="statusFilter" class="filter-select">
                        <option value="">全部</option>
                        <option value="pending">待分析</option>
                        <option value="processing">分析中</option>
                        <option value="completed">已完成</option>
                        <option value="failed">失败</option>
                    </select>
                </div>
                <div class="filter-group">
                    <span class="filter-label">支付状态:</span>
                    <select id="paymentFilter" class="filter-select">
                        <option value="">全部</option>
                        <option value="unpaid">未支付</option>
                        <option value="paid">已支付</option>
                        <option value="refunded">已退款</option>
                    </select>
                </div>
                <button id="searchBtn" class="search-btn">搜索</button>
                <button id="resetBtn" class="reset-btn">重置</button>
            </div>
            
            <div id="loadingContainer" class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载数据...</p>
            </div>
            
            <div id="dataContainer" style="display: none;">
                <table class="data-table" id="analysisTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户</th>
                            <th>订单ID</th>
                            <th>状态</th>
                            <th>支付状态</th>
                            <th>金额</th>
                            <th>照片</th>
                            <th>创建时间</th>
                            <th>分析完成时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- 数据将通过JavaScript动态填充 -->
                    </tbody>
                </table>
                
                <div class="pagination" id="pagination">
                    <div class="pagination-info">
                        共 <span id="totalRecords">0</span> 条记录，第 <span id="currentPage">1</span>/<span id="totalPages">1</span> 页
                    </div>
                    <button id="firstPageBtn" class="pagination-btn" disabled>首页</button>
                    <button id="prevPageBtn" class="pagination-btn" disabled>上一页</button>
                    <div id="pageButtons">
                        <!-- 页码按钮将通过JavaScript动态填充 -->
                    </div>
                    <button id="nextPageBtn" class="pagination-btn">下一页</button>
                    <button id="lastPageBtn" class="pagination-btn">末页</button>
                </div>
            </div>
            
            <div id="emptyData" style="display: none; text-align: center; padding: 50px 0; background-color: white; border-radius: 8px; margin-bottom: 20px;">
                <p>暂无形象分析记录</p>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/image_viewer.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为image_analysis
            Sidebar.init('image_analysis');
            
            // 获取DOM元素
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');
            const paymentFilter = document.getElementById('paymentFilter');
            const searchBtn = document.getElementById('searchBtn');
            const resetBtn = document.getElementById('resetBtn');
            const tableBody = document.getElementById('tableBody');
            const loadingContainer = document.getElementById('loadingContainer');
            const dataContainer = document.getElementById('dataContainer');
            const emptyData = document.getElementById('emptyData');
            const totalRecords = document.getElementById('totalRecords');
            const currentPage = document.getElementById('currentPage');
            const totalPages = document.getElementById('totalPages');
            const pageButtons = document.getElementById('pageButtons');
            const firstPageBtn = document.getElementById('firstPageBtn');
            const prevPageBtn = document.getElementById('prevPageBtn');
            const nextPageBtn = document.getElementById('nextPageBtn');
            const lastPageBtn = document.getElementById('lastPageBtn');
            
            // 获取管理员信息
            const user = Auth.getCurrentUser();
            if (user) {
                document.getElementById('userName').textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 分页变量
            let currentPageNum = 1;
            let totalPageCount = 1;
            let pageSize = 10;
            
            // 加载形象分析数据
            function loadAnalysisData(page = 1) {
                // 显示加载中
                loadingContainer.style.display = 'flex';
                dataContainer.style.display = 'none';
                emptyData.style.display = 'none';
                
                // 获取筛选条件
                const search = searchInput.value.trim();
                const status = statusFilter.value;
                const paymentStatus = paymentFilter.value;
                
                // 构建请求URL
                const url = new URL('../login_backend/admin_get_image_analysis_list.php', window.location.origin);
                url.searchParams.append('page', page);
                url.searchParams.append('limit', pageSize);
                
                if (search) {
                    url.searchParams.append('search', search);
                }
                
                if (status) {
                    url.searchParams.append('status', status);
                }
                
                if (paymentStatus) {
                    url.searchParams.append('payment_status', paymentStatus);
                }
                
                // 发起请求
                fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${Auth.getToken()}`
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常');
                    }
                    return response.json();
                })
                .then(data => {
                    // 隐藏加载中
                    loadingContainer.style.display = 'none';
                    
                    if (data.error) {
                        alert(data.msg || '加载形象分析数据失败');
                        return;
                    }
                    
                    const { records, total, page, total_pages } = data.data;
                    
                    // 更新分页信息
                    currentPageNum = page;
                    totalPageCount = total_pages;
                    totalRecords.textContent = total;
                    currentPage.textContent = page;
                    totalPages.textContent = total_pages;
                    
                    // 根据记录数量决定显示内容
                    if (records.length === 0) {
                        emptyData.style.display = 'block';
                        dataContainer.style.display = 'none';
                    } else {
                        emptyData.style.display = 'none';
                        dataContainer.style.display = 'block';
                        
                        // 渲染数据表格
                        renderTable(records);
                        
                        // 更新分页控件
                        updatePagination();
                    }
                })
                .catch(error => {
                    console.error('获取形象分析数据失败:', error);
                    loadingContainer.style.display = 'none';
                    alert('获取数据失败，请检查网络连接或刷新页面重试');
                });
            }
            
            // 渲染表格数据
            function renderTable(records) {
                tableBody.innerHTML = '';
                
                records.forEach(record => {
                    const row = document.createElement('tr');
                    
                    // 格式化状态标签
                    const statusClass = `status-${record.status}`;
                    const statusText = getStatusText(record.status);
                    
                    const paymentClass = `payment-${record.payment_status}`;
                    const paymentText = getPaymentStatusText(record.payment_status);
                    
                    // 格式化创建时间
                    const createTime = formatDateTime(record.created_at);
                    const analyzeTime = record.analysis_time ? formatDateTime(record.analysis_time) : '-';
                    
                    // 构建缩略图
                    let thumbnailHtml = '';
                    if (record.photo_urls && record.photo_urls.length > 0) {
                        thumbnailHtml = `<img src="${record.photo_urls[0]}" class="thumbnail" data-urls='${JSON.stringify(record.photo_urls)}' alt="照片">`;
                    } else {
                        thumbnailHtml = '<span>无照片</span>';
                    }
                    
                    // 构建用户头像和昵称
                    let userHtml = '';
                    if (record.avatar_url) {
                        userHtml = `
                            <div class="user-info">
                                <img src="${record.avatar_url}" class="avatar" alt="头像">
                                <span class="user-name">${record.nickname || '未知用户'}</span>
                                <span>(ID: ${record.user_id})</span>
                            </div>
                        `;
                    } else {
                        userHtml = `
                            <div class="user-info">
                                <span class="user-name">${record.nickname || '未知用户'}</span>
                                <span>(ID: ${record.user_id})</span>
                            </div>
                        `;
                    }
                    
                    // 设置行内容
                    row.innerHTML = `
                        <td>${record.id}</td>
                        <td>${userHtml}</td>
                        <td>${record.order_id || '-'}</td>
                        <td><span class="status-tag ${statusClass}">${statusText}</span></td>
                        <td><span class="status-tag ${paymentClass}">${paymentText}</span></td>
                        <td>¥${parseFloat(record.amount).toFixed(2)}</td>
                        <td>${thumbnailHtml}</td>
                        <td>${createTime}</td>
                        <td>${analyzeTime}</td>
                        <td>
                            <button class="action-btn view-btn" data-id="${record.id}">详情</button>
                            <button class="action-btn delete-btn" data-id="${record.id}">删除</button>
                        </td>
                    `;
                    
                    tableBody.appendChild(row);
                });
                
                // 绑定查看详情按钮事件
                document.querySelectorAll('.view-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const analysisId = this.getAttribute('data-id');
                        window.location.href = `image_analysis_detail.html?id=${analysisId}`;
                    });
                });
                
                // 绑定删除按钮事件
                document.querySelectorAll('.delete-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const analysisId = this.getAttribute('data-id');
                        deleteAnalysis(analysisId);
                    });
                });
                
                // 绑定缩略图点击事件
                document.querySelectorAll('.thumbnail').forEach(img => {
                    img.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        try {
                            const urls = JSON.parse(this.getAttribute('data-urls'));
                            if (urls && urls.length > 0) {
                                ImageViewer.showImage(this.src);
                            }
                        } catch (error) {
                            console.error('解析图片URL失败:', error);
                        }
                    });
                });
            }
            
            // 更新分页控件
            function updatePagination() {
                // 更新首页、末页、上一页、下一页按钮状态
                firstPageBtn.disabled = currentPageNum === 1;
                prevPageBtn.disabled = currentPageNum === 1;
                nextPageBtn.disabled = currentPageNum === totalPageCount;
                lastPageBtn.disabled = currentPageNum === totalPageCount;
                
                // 生成页码按钮
                pageButtons.innerHTML = '';
                
                // 显示逻辑：当前页前后各显示2页，总共最多显示5个页码
                let startPage = Math.max(1, currentPageNum - 2);
                let endPage = Math.min(totalPageCount, currentPageNum + 2);
                
                // 如果总页数小于5，则全部显示
                if (totalPageCount <= 5) {
                    startPage = 1;
                    endPage = totalPageCount;
                } else {
                    // 如果当前页接近开头，则显示前5页
                    if (currentPageNum < 3) {
                        startPage = 1;
                        endPage = 5;
                    }
                    // 如果当前页接近结尾，则显示后5页
                    else if (currentPageNum > totalPageCount - 2) {
                        startPage = totalPageCount - 4;
                        endPage = totalPageCount;
                    }
                }
                
                // 生成页码按钮
                for (let i = startPage; i <= endPage; i++) {
                    const pageBtn = document.createElement('button');
                    pageBtn.className = `pagination-btn ${i === currentPageNum ? 'active' : ''}`;
                    pageBtn.textContent = i;
                    pageBtn.addEventListener('click', function() {
                        if (i !== currentPageNum) {
                            loadAnalysisData(i);
                        }
                    });
                    pageButtons.appendChild(pageBtn);
                }
            }
            
            // 获取状态文本
            function getStatusText(status) {
                const statusMap = {
                    'pending': '待分析',
                    'processing': '分析中',
                    'completed': '已完成',
                    'failed': '失败'
                };
                return statusMap[status] || status;
            }
            
            // 获取支付状态文本
            function getPaymentStatusText(status) {
                const statusMap = {
                    'unpaid': '未支付',
                    'paid': '已支付',
                    'refunded': '已退款'
                };
                return statusMap[status] || status;
            }
            
            // 格式化日期时间
            function formatDateTime(dateTimeStr) {
                if (!dateTimeStr) return '-';
                
                try {
                    const date = new Date(dateTimeStr);
                    return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`;
                } catch (e) {
                    return dateTimeStr;
                }
            }
            
            // 数字补零
            function padZero(num) {
                return num < 10 ? '0' + num : num;
            }
            
            // 绑定搜索按钮事件
            searchBtn.addEventListener('click', function() {
                loadAnalysisData(1); // 搜索时重置为第1页
            });
            
            // 绑定重置按钮事件
            resetBtn.addEventListener('click', function() {
                searchInput.value = '';
                statusFilter.value = '';
                paymentFilter.value = '';
                loadAnalysisData(1);
            });
            
            // 绑定分页按钮事件
            firstPageBtn.addEventListener('click', function() {
                if (!this.disabled) {
                    loadAnalysisData(1);
                }
            });
            
            prevPageBtn.addEventListener('click', function() {
                if (!this.disabled) {
                    loadAnalysisData(currentPageNum - 1);
                }
            });
            
            nextPageBtn.addEventListener('click', function() {
                if (!this.disabled) {
                    loadAnalysisData(currentPageNum + 1);
                }
            });
            
            lastPageBtn.addEventListener('click', function() {
                if (!this.disabled) {
                    loadAnalysisData(totalPageCount);
                }
            });
            
            // 加载初始数据
            loadAnalysisData();
            
            // 删除形象分析记录
            function deleteAnalysis(id) {
                if (!id) return;
                
                // 显示确认对话框
                if (confirm(`确定要删除ID为 ${id} 的形象分析记录吗？此操作不可恢复！`)) {
                    // 显示加载中
                    loadingContainer.style.display = 'flex';
                    dataContainer.style.display = 'none';
                    
                    // 发送删除请求
                    fetch('../login_backend/admin_delete_image_analysis.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${Auth.getToken()}`
                        },
                        body: JSON.stringify({ id: id })
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('网络响应不正常');
                        }
                        return response.json();
                    })
                    .then(data => {
                        loadingContainer.style.display = 'none';
                        
                        if (data.error) {
                            alert(data.msg || '删除形象分析记录失败');
                            return;
                        }
                        
                        // 显示成功消息
                        alert('形象分析记录已成功删除');
                        
                        // 重新加载数据
                        loadAnalysisData(currentPageNum);
                    })
                    .catch(error => {
                        console.error('删除形象分析记录失败:', error);
                        loadingContainer.style.display = 'none';
                        alert('删除失败，请检查网络连接或刷新页面重试');
                    });
                }
            }
        });
    </script>
</body>
</html> 