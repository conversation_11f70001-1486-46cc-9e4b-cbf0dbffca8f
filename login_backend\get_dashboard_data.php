<?php
// 设置错误日志记录
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);
ini_set('error_log', __DIR__ . 'logs/dashboard_error.log');

// 记录请求开始信息
$requestTime = date('Y-m-d H:i:s');
$requestParams = json_encode($_GET);
error_log("[$requestTime] 开始处理仪表盘数据请求: $requestParams");

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => '不支持的请求方法']);
    error_log("[$requestTime] 错误: 不支持的请求方法 " . $_SERVER['REQUEST_METHOD']);
    exit;
}

// 获取日期范围参数
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-6 days'));
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// 获取优先级参数
$priority = isset($_GET['priority']) ? $_GET['priority'] : 'all';

// 记录请求参数
error_log("[$requestTime] 请求参数: startDate=$startDate, endDate=$endDate, priority=$priority");

// 验证日期格式
if (!validateDate($startDate) || !validateDate($endDate)) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => '无效的日期格式，请使用YYYY-MM-DD格式']);
    error_log("[$requestTime] 错误: 无效的日期格式 startDate=$startDate, endDate=$endDate");
    exit;
}

// 确保开始日期不晚于结束日期
if (strtotime($startDate) > strtotime($endDate)) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => '开始日期不能晚于结束日期']);
    error_log("[$requestTime] 错误: 开始日期晚于结束日期 startDate=$startDate, endDate=$endDate");
    exit;
}

// 验证管理员身份
$authHeader = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
if (empty($authHeader) || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '未提供授权Token']);
    error_log("[$requestTime] 错误: 未提供授权Token");
    exit;
}

$token = $matches[1];
if (empty($token)) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '无效的Token']);
    error_log("[$requestTime] 错误: 无效的Token");
    exit;
}

// 引入配置文件
$configFile = __DIR__ . '/config.php';

// 检查配置文件是否存在
if (!file_exists($configFile)) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error', 
        'message' => '系统配置文件不存在',
        'debug' => '配置文件路径: ' . $configFile
    ]);
    error_log("[$requestTime] 错误: 系统配置文件不存在 $configFile");
    exit;
}

try {
    error_log("[$requestTime] 开始加载配置文件和依赖项");
    require_once $configFile;
    require_once 'auth.php';
    require_once 'db.php';
    error_log("[$requestTime] 配置文件和依赖项加载完成");
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error', 
        'message' => '加载系统配置失败',
        'debug' => $e->getMessage()
    ]);
    error_log("[$requestTime] 错误: 加载系统配置失败: " . $e->getMessage());
    exit;
}

// 验证Token
try {
    error_log("[$requestTime] 开始验证管理员Token");
    $auth = new Auth();
    $payload = $auth->verifyAdminToken($token);
    if (!$payload) {
        throw new Exception('管理员身份验证失败');
    }
    error_log("[$requestTime] 管理员Token验证成功: " . json_encode($payload));
} catch (Exception $e) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
    error_log("[$requestTime] 错误: Token验证失败: " . $e->getMessage());
    exit;
}

// 创建数据库连接
try {
    error_log("[$requestTime] 开始创建数据库连接");
    $db = new Database();
    $conn = $db->getConnection();
    error_log("[$requestTime] 数据库连接创建成功");
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error', 
        'message' => '数据库连接失败',
        'debug' => $e->getMessage()
    ]);
    error_log("[$requestTime] 错误: 数据库连接失败: " . $e->getMessage());
    exit;
}

// 生成日期数组
$dates = [];
$currentDate = new DateTime($startDate);
$lastDate = new DateTime($endDate);
while ($currentDate <= $lastDate) {
    $dates[] = $currentDate->format('Y-m-d');
    $currentDate->modify('+1 day');
}

// 获取统计数据
try {
    error_log("[$requestTime] 开始获取统计数据, 优先级: $priority");
    
    // 初始化返回数据
    $responseData = [];
    
    // 根据优先级加载不同的数据
    if ($priority === 'high' || $priority === 'all') {
        error_log("[$requestTime] 获取高优先级数据: 用户统计");
        // 高优先级数据：用户统计和衣物统计
        $responseData['users'] = getUserStats($conn, $startDate, $endDate);
        
        error_log("[$requestTime] 获取高优先级数据: 衣物统计");
        $responseData['clothing'] = getClothingStats($conn, $startDate, $endDate);
    
        error_log("[$requestTime] 获取高优先级数据: 照片基本统计");
        // 添加照片和试衣的基本统计数据（不包含分布数据）
        $responseData['photos'] = getBasicPhotoStats($conn, $startDate, $endDate);
        
        error_log("[$requestTime] 获取高优先级数据: 试衣基本统计");
        $responseData['tryOn'] = getBasicTryOnStats($conn, $startDate, $endDate);
    
        error_log("[$requestTime] 获取高优先级数据: 每日统计(简化版)");
        // 日期范围内的每日统计（简化版，仅包含用户和衣物数据）
        $responseData['dailyStats'] = getSimplifiedDailyStats($conn, $startDate, $endDate);
    
        // 获取打赏统计数据（简化版）
        error_log("[$requestTime] 获取高优先级数据: 打赏统计");
        $donationSummary = [
            'total' => 0,
            'totalAmount' => 0,
            'newDonations' => 0,
            'newAmount' => 0
        ];
        
        // 检查donations表是否存在
        try {
            $checkDonationsTable = "SHOW TABLES LIKE 'donations'";
            $donationsTableExists = $conn->query($checkDonationsTable)->rowCount() > 0;
            error_log("[$requestTime] 检查donations表是否存在: " . ($donationsTableExists ? '是' : '否'));
            
            if ($donationsTableExists) {
                // 总打赏数和总金额（简化查询）
                $donationTotalSql = "SELECT COUNT(*) as count, SUM(CASE WHEN status = 'success' THEN amount ELSE 0 END) as total_amount FROM donations";
                $donationTotalStmt = $conn->prepare($donationTotalSql);
                $donationTotalStmt->execute();
                $donationTotal = $donationTotalStmt->fetch(PDO::FETCH_ASSOC);
                $donationSummary['total'] = intval($donationTotal['count']);
                $donationSummary['totalAmount'] = floatval($donationTotal['total_amount']);
                
                // 新增打赏数和新增金额（最近7天）
                $newDonationsSql = "SELECT COUNT(*) as count FROM donations WHERE created_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)";
                $newDonationsStmt = $conn->prepare($newDonationsSql);
                $newDonationsStmt->execute();
                $newDonations = $newDonationsStmt->fetch(PDO::FETCH_ASSOC);
                $donationSummary['newDonations'] = intval($newDonations['count']);
            }
        } catch (Exception $e) {
            error_log("[$requestTime] 警告: 获取打赏数据时出错: " . $e->getMessage());
            // 继续执行，不中断流程
        }
        
        $responseData['donations'] = $donationSummary;
    }
    
    if ($priority === 'low' || $priority === 'all') {
        error_log("[$requestTime] 获取低优先级数据");
        // 低优先级数据：照片统计、试衣统计和API使用情况的详细数据
        try {
            error_log("[$requestTime] 获取低优先级数据: 照片详细统计");
            $photoStats = getPhotoStats($conn, $startDate, $endDate);
            
            error_log("[$requestTime] 获取低优先级数据: 试衣详细统计");
            $tryOnStats = getTryOnStats($conn, $startDate, $endDate);
            
            // 如果高优先级数据中已经有了基本统计数据，只添加分布数据
            if (isset($responseData['photos'])) {
                $responseData['photos']['typeDistribution'] = $photoStats['typeDistribution'];
            } else {
                $responseData['photos'] = $photoStats;
            }
            
            if (isset($responseData['tryOn'])) {
                $responseData['tryOn']['statusDistribution'] = $tryOnStats['statusDistribution'];
            } else {
                $responseData['tryOn'] = $tryOnStats;
            }
            
            error_log("[$requestTime] 获取低优先级数据: API使用情况");
            $responseData['apiUsage'] = getApiUsageStats($conn);
        } catch (Exception $e) {
            error_log("[$requestTime] 警告: 获取低优先级数据时出错: " . $e->getMessage());
            // 继续执行，不中断流程
        }
        
        // 如果是全部加载，并且高优先级数据中已经有了dailyStats，则更新它
        if ($priority === 'low' && !isset($responseData['dailyStats'])) {
            // 获取完整的每日统计数据
            try {
                error_log("[$requestTime] 获取完整的每日统计数据");
                $responseData['dailyStats'] = getDailyStats($conn, $startDate, $endDate);
            } catch (Exception $e) {
                error_log("[$requestTime] 警告: 获取完整每日统计数据时出错: " . $e->getMessage());
                // 继续执行，不中断流程
            }
        } else if ($priority === 'all') {
            // 获取完整的每日统计数据
            try {
                error_log("[$requestTime] 获取完整的每日统计数据");
                $responseData['dailyStats'] = getDailyStats($conn, $startDate, $endDate);
            } catch (Exception $e) {
                error_log("[$requestTime] 警告: 获取完整每日统计数据时出错: " . $e->getMessage());
                // 继续执行，不中断流程
            }
        }
        
        // 如果是全部加载，并且高优先级数据中已经有了donations，则更新它
        if ($priority === 'low' && !isset($responseData['donations'])) {
            // 获取完整的打赏统计数据
            try {
                error_log("[$requestTime] 获取完整的打赏统计数据");
                $responseData['donations'] = getDonationStats($conn, $startDate, $endDate);
            } catch (Exception $e) {
                error_log("[$requestTime] 警告: 获取完整打赏统计数据时出错: " . $e->getMessage());
                // 继续执行，不中断流程
            }
        } else if ($priority === 'all') {
            // 获取完整的打赏统计数据
            try {
                error_log("[$requestTime] 获取完整的打赏统计数据");
                $responseData['donations'] = getDonationStats($conn, $startDate, $endDate);
            } catch (Exception $e) {
                error_log("[$requestTime] 警告: 获取完整打赏统计数据时出错: " . $e->getMessage());
                // 继续执行，不中断流程
            }
        }
    }
    
    error_log("[$requestTime] 统计数据获取完成，准备返回响应");
    
    // 返回成功响应
    echo json_encode([
        'status' => 'success',
        'message' => '获取仪表盘数据成功',
        'data' => $responseData
    ]);
    
    error_log("[$requestTime] 仪表盘数据请求处理完成");
} catch (Exception $e) {
    http_response_code(500);
    $errorMessage = '获取仪表盘数据失败: ' . $e->getMessage();
    echo json_encode([
        'status' => 'error', 
        'message' => $errorMessage
    ]);
    
    // 记录详细错误信息
    error_log("[$requestTime] 严重错误: $errorMessage");
    error_log("[$requestTime] 错误堆栈: " . $e->getTraceAsString());
}

/**
 * 验证日期格式
 * @param string $date 日期字符串 (YYYY-MM-DD)
 * @return bool 是否为有效日期
 */
function validateDate($date) {
    $d = DateTime::createFromFormat('Y-m-d', $date);
    return $d && $d->format('Y-m-d') === $date;
}

/**
 * 获取用户统计数据
 */
function getUserStats($conn, $startDate, $endDate) {
    // 总用户数
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM users");
    $stmt->execute();
    $totalUsers = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 性别分布
    $stmt = $conn->prepare("
        SELECT 
            CASE gender 
                WHEN 0 THEN '未知' 
                WHEN 1 THEN '男' 
                WHEN 2 THEN '女' 
                ELSE '其他' 
            END as gender_text,
            COUNT(*) as count 
        FROM users 
        GROUP BY gender
    ");
    $stmt->execute();
    $genderDistribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 指定日期范围内新增用户
    $stmt = $conn->prepare("
        SELECT COUNT(*) as new_users 
        FROM users 
        WHERE created_at >= :start_date AND created_at <= :end_date
    ");
    $stmt->bindValue(':start_date', $startDate);
    $stmt->bindValue(':end_date', $endDate . ' 23:59:59'); // 包含结束日期的全天
    $stmt->execute();
    $newUsers = $stmt->fetch(PDO::FETCH_ASSOC)['new_users'];
    
    return [
        'total' => $totalUsers,
        'newUsers' => $newUsers,
        'genderDistribution' => $genderDistribution
    ];
}

/**
 * 获取衣物统计数据
 */
function getClothingStats($conn, $startDate, $endDate) {
    // 总衣物数
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM clothes");
    $stmt->execute();
    $totalClothes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 分类分布 - 修复分类数量统计问题并显示全部分类
    $stmt = $conn->prepare("
        SELECT 
            c.category,
            cc.name as category_name,
            COUNT(c.id) as count 
        FROM clothes c
        LEFT JOIN clothing_categories cc ON c.category = cc.code
        WHERE c.category IS NOT NULL
        GROUP BY c.category
        ORDER BY count DESC
    ");
    $stmt->execute();
    $categoryDistribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 单独统计category为NULL或空的记录
    $stmt = $conn->prepare("
        SELECT 
            'undefined' as category,
            '未分类' as category_name,
            COUNT(id) as count 
        FROM clothes 
        WHERE category IS NULL OR category = ''
    ");
    $stmt->execute();
    $undefinedCategory = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 如果有未分类的衣物，添加到结果中
    if ($undefinedCategory && $undefinedCategory['count'] > 0) {
        $categoryDistribution[] = $undefinedCategory;
    }
    
    // 指定日期范围内新增衣物
    $stmt = $conn->prepare("
        SELECT COUNT(*) as new_clothes 
        FROM clothes 
        WHERE created_at >= :start_date AND created_at <= :end_date
    ");
    $stmt->bindValue(':start_date', $startDate);
    $stmt->bindValue(':end_date', $endDate . ' 23:59:59'); // 包含结束日期的全天
    $stmt->execute();
    $newClothes = $stmt->fetch(PDO::FETCH_ASSOC)['new_clothes'];
    
    return [
        'total' => $totalClothes,
        'newClothes' => $newClothes,
        'categoryDistribution' => $categoryDistribution
    ];
}

/**
 * 获取照片统计数据
 */
function getPhotoStats($conn, $startDate, $endDate) {
    // 总照片数
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM photos");
    $stmt->execute();
    $totalPhotos = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 类型分布
    $stmt = $conn->prepare("
        SELECT 
            type,
            COUNT(*) as count 
        FROM photos 
        GROUP BY type
    ");
    $stmt->execute();
    $typeDistribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 指定日期范围内新增照片
    $stmt = $conn->prepare("
        SELECT COUNT(*) as new_photos 
        FROM photos 
        WHERE created_at >= :start_date AND created_at <= :end_date
    ");
    $stmt->bindValue(':start_date', $startDate);
    $stmt->bindValue(':end_date', $endDate . ' 23:59:59'); // 包含结束日期的全天
    $stmt->execute();
    $newPhotos = $stmt->fetch(PDO::FETCH_ASSOC)['new_photos'];
    
    return [
        'total' => $totalPhotos,
        'newPhotos' => $newPhotos,
        'typeDistribution' => $typeDistribution
    ];
}

/**
 * 获取试衣统计数据
 */
function getTryOnStats($conn, $startDate, $endDate) {
    // 总试衣次数
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM try_on_history");
    $stmt->execute();
    $totalTryOns = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 成功和失败的次数
    $stmt = $conn->prepare("
        SELECT 
            status,
            COUNT(*) as count 
        FROM try_on_history 
        GROUP BY status
    ");
    $stmt->execute();
    $statusDistribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 指定日期范围内试衣次数
    $stmt = $conn->prepare("
        SELECT COUNT(*) as new_try_ons 
        FROM try_on_history 
        WHERE created_at >= :start_date AND created_at <= :end_date
    ");
    $stmt->bindValue(':start_date', $startDate);
    $stmt->bindValue(':end_date', $endDate . ' 23:59:59'); // 包含结束日期的全天
    $stmt->execute();
    $newTryOns = $stmt->fetch(PDO::FETCH_ASSOC)['new_try_ons'];
    
    return [
        'total' => $totalTryOns,
        'newTryOns' => $newTryOns,
        'statusDistribution' => $statusDistribution
    ];
}

/**
 * 获取API使用情况
 */
function getApiUsageStats($conn) {
    // 检查API使用表是否存在
    $stmt = $conn->prepare("
        SELECT COUNT(*) as table_exists 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'api_usage'
    ");
    $stmt->execute();
    $tableExists = $stmt->fetch(PDO::FETCH_ASSOC)['table_exists'];
    
    // 如果表不存在，返回默认数据
    if (!$tableExists) {
        return [
            'tryOn' => [
                'total' => 10000,
                'used' => 0,
                'remaining' => 10000
            ],
            'photoEdit' => [
                'total' => 5000,
                'used' => 0,
                'remaining' => 5000
            ]
        ];
    }
    
    // 获取试衣API使用情况
    $stmt = $conn->prepare("
        SELECT total_quota, used_quota, reset_date
        FROM api_usage
        WHERE api_name = 'try_on'
    ");
    $stmt->execute();
    $tryOnUsage = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 获取照片编辑API使用情况
    $stmt = $conn->prepare("
        SELECT total_quota, used_quota, reset_date
        FROM api_usage
        WHERE api_name = 'photo_edit'
    ");
    $stmt->execute();
    $photoEditUsage = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return [
        'tryOn' => [
            'total' => $tryOnUsage ? intval($tryOnUsage['total_quota']) : 10000,
            'used' => $tryOnUsage ? intval($tryOnUsage['used_quota']) : 0,
            'remaining' => $tryOnUsage ? (intval($tryOnUsage['total_quota']) - intval($tryOnUsage['used_quota'])) : 10000,
            'resetDate' => $tryOnUsage ? $tryOnUsage['reset_date'] : null
        ],
        'photoEdit' => [
            'total' => $photoEditUsage ? intval($photoEditUsage['total_quota']) : 5000,
            'used' => $photoEditUsage ? intval($photoEditUsage['used_quota']) : 0,
            'remaining' => $photoEditUsage ? (intval($photoEditUsage['total_quota']) - intval($photoEditUsage['used_quota'])) : 5000,
            'resetDate' => $photoEditUsage ? $photoEditUsage['reset_date'] : null
        ]
    ];
}

/**
 * 获取指定日期范围内每日统计数据
 */
function getDailyStats($conn, $startDate, $endDate) {
    global $requestTime;
    error_log("[$requestTime] 执行getDailyStats函数: $startDate 至 $endDate");
    
    try {
        // 创建日期数组作为基础数据
        $dates = [];
        $result = [];
        $currentDate = new DateTime($startDate);
        $lastDate = new DateTime($endDate);
        
        while ($currentDate <= $lastDate) {
            $dateStr = $currentDate->format('Y-m-d');
            $dates[] = $dateStr;
            // 预先初始化每天的数据结构
            $result[$dateStr] = [
                'date' => $dateStr,
                'newUsers' => 0,
                'newClothes' => 0,
                'newPhotos' => 0,
                'newTryOns' => 0
            ];
            $currentDate->modify('+1 day');
        }
        
        error_log("[$requestTime] getDailyStats: 初始化了 " . count($dates) . " 天的数据结构");
        
        // 构建SQL查询
        $sql = "";
        
        try {
            error_log("[$requestTime] getDailyStats: 开始构建用户统计SQL");
            // 用户统计
            $sql = "SELECT DATE(created_at) as date, COUNT(*) as count FROM users 
                   WHERE created_at >= :start_date AND created_at <= :end_date
                   GROUP BY DATE(created_at)";
            
            $stmt = $conn->prepare($sql);
            $endDateWithTime = $endDate . ' 23:59:59';
            $stmt->bindValue(':start_date', $startDate);
            $stmt->bindValue(':end_date', $endDateWithTime);
            $stmt->execute();
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if (isset($result[$row['date']])) {
                    $result[$row['date']]['newUsers'] = intval($row['count']);
                }
            }
            error_log("[$requestTime] getDailyStats: 用户统计SQL执行成功");
        } catch (Exception $e) {
            error_log("[$requestTime] getDailyStats: 用户统计SQL执行失败: " . $e->getMessage());
            error_log("[$requestTime] getDailyStats: SQL: $sql");
        }
        
        try {
            error_log("[$requestTime] getDailyStats: 开始构建衣物统计SQL");
            // 衣物统计
            $sql = "SELECT DATE(created_at) as date, COUNT(*) as count FROM clothes 
                   WHERE created_at >= :start_date AND created_at <= :end_date
                   GROUP BY DATE(created_at)";
            
            $stmt = $conn->prepare($sql);
            $endDateWithTime = $endDate . ' 23:59:59';
            $stmt->bindValue(':start_date', $startDate);
            $stmt->bindValue(':end_date', $endDateWithTime);
            $stmt->execute();
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if (isset($result[$row['date']])) {
                    $result[$row['date']]['newClothes'] = intval($row['count']);
                }
            }
            error_log("[$requestTime] getDailyStats: 衣物统计SQL执行成功");
        } catch (Exception $e) {
            error_log("[$requestTime] getDailyStats: 衣物统计SQL执行失败: " . $e->getMessage());
            error_log("[$requestTime] getDailyStats: SQL: $sql");
        }
        
        try {
            error_log("[$requestTime] getDailyStats: 开始构建照片统计SQL");
            // 照片统计
            $sql = "SELECT DATE(created_at) as date, COUNT(*) as count FROM photos 
                   WHERE created_at >= :start_date AND created_at <= :end_date
                   GROUP BY DATE(created_at)";
            
            $stmt = $conn->prepare($sql);
            $endDateWithTime = $endDate . ' 23:59:59';
            $stmt->bindValue(':start_date', $startDate);
            $stmt->bindValue(':end_date', $endDateWithTime);
            $stmt->execute();
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if (isset($result[$row['date']])) {
                    $result[$row['date']]['newPhotos'] = intval($row['count']);
                }
            }
            error_log("[$requestTime] getDailyStats: 照片统计SQL执行成功");
        } catch (Exception $e) {
            error_log("[$requestTime] getDailyStats: 照片统计SQL执行失败: " . $e->getMessage());
            error_log("[$requestTime] getDailyStats: SQL: $sql");
        }
        
        try {
            error_log("[$requestTime] getDailyStats: 开始构建试衣统计SQL");
            // 试衣统计
            $sql = "SELECT DATE(created_at) as date, COUNT(*) as count FROM try_on_history 
                   WHERE created_at >= :start_date AND created_at <= :end_date
                   GROUP BY DATE(created_at)";
            
            $stmt = $conn->prepare($sql);
            $endDateWithTime = $endDate . ' 23:59:59';
            $stmt->bindValue(':start_date', $startDate);
            $stmt->bindValue(':end_date', $endDateWithTime);
            $stmt->execute();
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if (isset($result[$row['date']])) {
                    $result[$row['date']]['newTryOns'] = intval($row['count']);
                }
            }
            error_log("[$requestTime] getDailyStats: 试衣统计SQL执行成功");
        } catch (Exception $e) {
            error_log("[$requestTime] getDailyStats: 试衣统计SQL执行失败: " . $e->getMessage());
            error_log("[$requestTime] getDailyStats: SQL: $sql");
        }
        
        error_log("[$requestTime] getDailyStats: 所有统计SQL执行完成，返回结果");
        
        // 转换关联数组为索引数组
        return array_values($result);
    } catch (Exception $e) {
        error_log("[$requestTime] getDailyStats函数执行失败: " . $e->getMessage());
        error_log("[$requestTime] getDailyStats错误堆栈: " . $e->getTraceAsString());
        // 返回空数组，避免整个请求失败
        return [];
    }
}

/**
 * 获取简化版的每日统计数据
 */
function getSimplifiedDailyStats($conn, $startDate, $endDate) {
    global $requestTime;
    error_log("[$requestTime] 执行getSimplifiedDailyStats函数: $startDate 至 $endDate");
    
    try {
        // 创建日期数组作为基础数据
        $dates = [];
        $result = [];
        $currentDate = new DateTime($startDate);
        $lastDate = new DateTime($endDate);
        
        while ($currentDate <= $lastDate) {
            $dateStr = $currentDate->format('Y-m-d');
            $dates[] = $dateStr;
            // 预先初始化每天的数据结构
            $result[$dateStr] = [
                'date' => $dateStr,
                'newUsers' => 0,
                'newClothes' => 0,
                'newPhotos' => 0,
                'newTryOns' => 0
            ];
            $currentDate->modify('+1 day');
        }
        
        error_log("[$requestTime] getSimplifiedDailyStats: 初始化了 " . count($dates) . " 天的数据结构");
        
        // 使用单个查询获取所有日期的新增用户数
        try {
            error_log("[$requestTime] getSimplifiedDailyStats: 开始构建用户统计SQL");
            $sql = "SELECT DATE(created_at) as date, COUNT(*) as count FROM users 
                   WHERE created_at >= :start_date AND created_at <= :end_date
                   GROUP BY DATE(created_at)";
            
            $stmt = $conn->prepare($sql);
            $endDateWithTime = $endDate . ' 23:59:59';
            $stmt->bindValue(':start_date', $startDate);
            $stmt->bindValue(':end_date', $endDateWithTime);
            $stmt->execute();
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if (isset($result[$row['date']])) {
                    $result[$row['date']]['newUsers'] = intval($row['count']);
                }
            }
            error_log("[$requestTime] getSimplifiedDailyStats: 用户统计SQL执行成功");
        } catch (Exception $e) {
            error_log("[$requestTime] getSimplifiedDailyStats: 用户统计SQL执行失败: " . $e->getMessage());
            error_log("[$requestTime] getSimplifiedDailyStats: SQL: $sql");
        }
        
        try {
            error_log("[$requestTime] getSimplifiedDailyStats: 开始构建衣物统计SQL");
            $sql = "SELECT DATE(created_at) as date, COUNT(*) as count FROM clothes 
                   WHERE created_at >= :start_date AND created_at <= :end_date
                   GROUP BY DATE(created_at)";
            
            $stmt = $conn->prepare($sql);
            $endDateWithTime = $endDate . ' 23:59:59';
            $stmt->bindValue(':start_date', $startDate);
            $stmt->bindValue(':end_date', $endDateWithTime);
            $stmt->execute();
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if (isset($result[$row['date']])) {
                    $result[$row['date']]['newClothes'] = intval($row['count']);
                }
            }
            error_log("[$requestTime] getSimplifiedDailyStats: 衣物统计SQL执行成功");
        } catch (Exception $e) {
            error_log("[$requestTime] getSimplifiedDailyStats: 衣物统计SQL执行失败: " . $e->getMessage());
            error_log("[$requestTime] getSimplifiedDailyStats: SQL: $sql");
        }
        
        try {
            error_log("[$requestTime] getSimplifiedDailyStats: 开始构建照片统计SQL");
            $sql = "SELECT DATE(created_at) as date, COUNT(*) as count FROM photos 
                   WHERE created_at >= :start_date AND created_at <= :end_date
                   GROUP BY DATE(created_at)";
            
            $stmt = $conn->prepare($sql);
            $endDateWithTime = $endDate . ' 23:59:59';
            $stmt->bindValue(':start_date', $startDate);
            $stmt->bindValue(':end_date', $endDateWithTime);
            $stmt->execute();
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if (isset($result[$row['date']])) {
                    $result[$row['date']]['newPhotos'] = intval($row['count']);
                }
            }
            error_log("[$requestTime] getSimplifiedDailyStats: 照片统计SQL执行成功");
        } catch (Exception $e) {
            error_log("[$requestTime] getSimplifiedDailyStats: 照片统计SQL执行失败: " . $e->getMessage());
            error_log("[$requestTime] getSimplifiedDailyStats: SQL: $sql");
        }
        
        try {
            error_log("[$requestTime] getSimplifiedDailyStats: 开始构建试衣统计SQL");
            $sql = "SELECT DATE(created_at) as date, COUNT(*) as count FROM try_on_history 
                   WHERE created_at >= :start_date AND created_at <= :end_date
                   GROUP BY DATE(created_at)";
            
            $stmt = $conn->prepare($sql);
            $endDateWithTime = $endDate . ' 23:59:59';
            $stmt->bindValue(':start_date', $startDate);
            $stmt->bindValue(':end_date', $endDateWithTime);
            $stmt->execute();
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if (isset($result[$row['date']])) {
                    $result[$row['date']]['newTryOns'] = intval($row['count']);
                }
            }
            error_log("[$requestTime] getSimplifiedDailyStats: 试衣统计SQL执行成功");
        } catch (Exception $e) {
            error_log("[$requestTime] getSimplifiedDailyStats: 试衣统计SQL执行失败: " . $e->getMessage());
            error_log("[$requestTime] getSimplifiedDailyStats: SQL: $sql");
        }
        
        error_log("[$requestTime] getSimplifiedDailyStats: 所有统计SQL执行完成，返回结果");
        
        // 转换关联数组为索引数组
        return array_values($result);
    } catch (Exception $e) {
        error_log("[$requestTime] getSimplifiedDailyStats函数执行失败: " . $e->getMessage());
        error_log("[$requestTime] getSimplifiedDailyStats错误堆栈: " . $e->getTraceAsString());
        // 返回空数组，避免整个请求失败
        return [];
    }
}

/**
 * 获取打赏统计数据
 */
function getDonationStats($conn, $startDate, $endDate) {
    // 获取打赏统计数据
    $donationSummary = [
        'total' => 0,
        'totalAmount' => 0,
        'newDonations' => 0,
        'newAmount' => 0
    ];
    
    // 检查donations表是否存在
    $checkDonationsTable = "SHOW TABLES LIKE 'donations'";
    $donationsTableExists = $conn->query($checkDonationsTable)->rowCount() > 0;
    
    if ($donationsTableExists) {
        // 总打赏数和总金额
        $donationTotalSql = "SELECT COUNT(*) as count, SUM(CASE WHEN status = 'success' THEN amount ELSE 0 END) as total_amount FROM donations";
        $donationTotalStmt = $conn->prepare($donationTotalSql);
        $donationTotalStmt->execute();
        $donationTotal = $donationTotalStmt->fetch(PDO::FETCH_ASSOC);
        $donationSummary['total'] = intval($donationTotal['count']);
        $donationSummary['totalAmount'] = floatval($donationTotal['total_amount']);
        
        // 新增打赏数和新增金额（最近7天）
        $newDonationsSql = "SELECT COUNT(*) as count, SUM(CASE WHEN status = 'success' THEN amount ELSE 0 END) as total_amount 
                           FROM donations 
                           WHERE created_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)";
        $newDonationsStmt = $conn->prepare($newDonationsSql);
        $newDonationsStmt->execute();
        $newDonations = $newDonationsStmt->fetch(PDO::FETCH_ASSOC);
        $donationSummary['newDonations'] = intval($newDonations['count']);
        $donationSummary['newAmount'] = floatval($newDonations['total_amount']);
        
        // 创建日期数组
        $dates = [];
        $currentDate = new DateTime($startDate);
        $lastDate = new DateTime($endDate);
        while ($currentDate <= $lastDate) {
            $dates[] = $currentDate->format('Y-m-d');
            $currentDate->modify('+1 day');
        }
        
        // 获取每日统计数据
        $dailyStats = getSimplifiedDailyStats($conn, $startDate, $endDate);
        
        // 添加打赏日趋势数据
        $donationTrendSql = "SELECT DATE(created_at) as date, COUNT(*) as count, SUM(CASE WHEN status = 'success' THEN amount ELSE 0 END) as amount 
                            FROM donations 
                            WHERE created_at >= :start_date AND created_at <= :end_date 
                            GROUP BY DATE(created_at)";
        $donationTrendStmt = $conn->prepare($donationTrendSql);
        $donationTrendStmt->bindParam(':start_date', $startDate);
        $donationTrendStmt->bindParam(':end_date', $endDate);
        $donationTrendStmt->execute();
        $donationTrends = $donationTrendStmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 处理每天的打赏数据
        $dailyDonations = [];
        foreach ($dates as $date) {
            $dailyDonations[$date] = [
                'date' => $date,
                'count' => 0,
                'amount' => 0
            ];
        }
        
        foreach ($donationTrends as $trend) {
            if (isset($dailyDonations[$trend['date']])) {
                $dailyDonations[$trend['date']]['count'] = intval($trend['count']);
                $dailyDonations[$trend['date']]['amount'] = floatval($trend['amount']);
            }
        }
        
        // 将打赏趋势数据添加到日统计中
        foreach ($dailyStats as &$stat) {
            $date = $stat['date'];
            if (isset($dailyDonations[$date])) {
                $stat['newDonations'] = $dailyDonations[$date]['count'];
                $stat['donationAmount'] = $dailyDonations[$date]['amount'];
            } else {
                $stat['newDonations'] = 0;
                $stat['donationAmount'] = 0;
            }
        }
        
        // 添加日统计数据到返回结果
        $donationSummary['dailyStats'] = $dailyStats;
    }
    
    return $donationSummary;
}

/**
 * 获取照片基本统计数据（不包含分布数据）
 */
function getBasicPhotoStats($conn, $startDate, $endDate) {
    // 总照片数
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM photos");
    $stmt->execute();
    $totalPhotos = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 指定日期范围内新增照片
    $stmt = $conn->prepare("
        SELECT COUNT(*) as new_photos 
        FROM photos 
        WHERE created_at >= :start_date AND created_at <= :end_date
    ");
    $stmt->bindValue(':start_date', $startDate);
    $stmt->bindValue(':end_date', $endDate . ' 23:59:59'); // 包含结束日期的全天
    $stmt->execute();
    $newPhotos = $stmt->fetch(PDO::FETCH_ASSOC)['new_photos'];
    
    return [
        'total' => $totalPhotos,
        'newPhotos' => $newPhotos
    ];
}

/**
 * 获取试衣基本统计数据（不包含分布数据）
 */
function getBasicTryOnStats($conn, $startDate, $endDate) {
    // 总试衣次数
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM try_on_history");
    $stmt->execute();
    $totalTryOns = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 指定日期范围内试衣次数
    $stmt = $conn->prepare("
        SELECT COUNT(*) as new_try_ons 
        FROM try_on_history 
        WHERE created_at >= :start_date AND created_at <= :end_date
    ");
    $stmt->bindValue(':start_date', $startDate);
    $stmt->bindValue(':end_date', $endDate . ' 23:59:59'); // 包含结束日期的全天
    $stmt->execute();
    $newTryOns = $stmt->fetch(PDO::FETCH_ASSOC)['new_try_ons'];
    
    return [
        'total' => $totalTryOns,
        'newTryOns' => $newTryOns
    ];
} 