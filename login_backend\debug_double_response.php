<?php
// 双响应问题调试脚本
echo "编辑衣物双响应问题分析\n";
echo "==========================\n\n";

// 测试Bearer前缀处理
function testBearerHandling($token) {
    echo "原始token: $token\n";
    
    // 模拟不同API的处理方式
    $methods = [
        'get_clothes.php' => function($t) {
            if (strpos($t, 'Bearer ') === 0) {
                return substr($t, 7);
            }
            return $t;
        },
        'add_clothing.php' => function($t) {
            // add_clothing.php没有处理Bearer前缀（可能的问题）
            return $t;
        },
        'get_clothing_categories.php' => function($t) {
            if (strpos($t, 'Bearer ') === 0) {
                return substr($t, 7);
            }
            return $t;
        }
    ];
    
    foreach ($methods as $api => $handler) {
        $processed = $handler($token);
        echo "  $api: $processed\n";
    }
    echo "\n";
}

echo "1. Bearer前缀处理测试:\n";
testBearerHandling('Bearer mock_token_123456');

echo "2. 检查add_clothing.php的Bearer处理:\n";
$file = 'add_clothing.php';
if (file_exists($file)) {
    $content = file_get_contents($file);
    if (strpos($content, 'Bearer') !== false) {
        echo "✅ $file 包含Bearer处理逻辑\n";
    } else {
        echo "❌ $file 可能缺少Bearer处理逻辑\n";
    }
} else {
    echo "❌ 找不到 $file\n";
}

echo "\n3. 模拟API调用测试:\n";

// 简单的token验证测试
require_once 'auth.php';
$auth = new Auth();

$testTokens = [
    'mock_token_123456',
    'Bearer mock_token_123456'
];

foreach ($testTokens as $token) {
    echo "测试token: $token\n";
    
    // 处理Bearer前缀
    $processedToken = $token;
    if (strpos($token, 'Bearer ') === 0) {
        $processedToken = substr($token, 7);
    }
    
    $result = $auth->verifyToken($processedToken);
    if ($result) {
        echo "  ✅ 验证成功, 用户ID: {$result['sub']}\n";
    } else {
        echo "  ❌ 验证失败\n";
    }
    echo "\n";
}

echo "调试完成!\n";
?> 