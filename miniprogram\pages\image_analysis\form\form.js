const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    gender: 1, // 默认男性
    height: '',
    weight: '',
    bust: '',
    waist: '',
    hips: '',
    shoulderWidth: '',
    skinTone: '',
    faceShape: '',
    bodyShape: '',
    remarks: '',
    photos: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检查是否登录
    if (!wx.getStorageSync('token')) {
      wx.showModal({
        title: '提示',
        content: '请先登录以使用个人形象分析服务',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.redirectTo({
              url: '/pages/login/login'
            });
          } else {
            wx.navigateBack();
          }
        }
      });
    }
  },

  /**
   * 选择性别
   */
  selectGender: function (e) {
    this.setData({
      gender: parseInt(e.currentTarget.dataset.gender)
    });
  },

  /**
   * 输入身高
   */
  inputHeight: function (e) {
    this.setData({
      height: e.detail.value
    });
  },

  /**
   * 输入体重
   */
  inputWeight: function (e) {
    this.setData({
      weight: e.detail.value
    });
  },

  /**
   * 输入胸围
   */
  inputBust: function (e) {
    this.setData({
      bust: e.detail.value
    });
  },

  /**
   * 输入腰围
   */
  inputWaist: function (e) {
    this.setData({
      waist: e.detail.value
    });
  },

  /**
   * 输入臀围
   */
  inputHips: function (e) {
    this.setData({
      hips: e.detail.value
    });
  },

  /**
   * 输入肩宽
   */
  inputShoulderWidth: function (e) {
    this.setData({
      shoulderWidth: e.detail.value
    });
  },

  /**
   * 输入肤色
   */
  inputSkinTone: function (e) {
    this.setData({
      skinTone: e.detail.value
    });
  },

  /**
   * 输入脸型
   */
  inputFaceShape: function (e) {
    this.setData({
      faceShape: e.detail.value
    });
  },

  /**
   * 输入体型
   */
  inputBodyShape: function (e) {
    this.setData({
      bodyShape: e.detail.value
    });
  },

  /**
   * 输入备注
   */
  inputRemarks: function (e) {
    this.setData({
      remarks: e.detail.value
    });
  },

  /**
   * 添加照片
   */
  addPhoto: function () {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 上传照片
        this.uploadPhoto(res.tempFilePaths[0]);
      }
    });
  },

  /**
   * 上传照片到服务器
   */
  uploadPhoto: function (tempFilePath) {
    wx.showLoading({
      title: '上传中...',
    });

    const token = wx.getStorageSync('token');

    wx.uploadFile({
      url: app.globalData.baseUrl + '/upload_photo.php',
      filePath: tempFilePath,
      name: 'photo',
      header: {
        'Authorization': token
      },
      formData: {
        'type': 'image_analysis'
      },
      success: (res) => {
        wx.hideLoading();
        const data = JSON.parse(res.data);
        if (data.error) {
          wx.showToast({
            title: '上传失败: ' + data.msg,
            icon: 'none'
          });
          return;
        }

        // 添加到照片列表 - 修复获取URL的路径
        const photos = this.data.photos;
        photos.push(data.data.image_url); // 修正：使用data.data.image_url替代data.url
        this.setData({
          photos: photos
        });
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '上传失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 删除照片
   */
  deletePhoto: function (e) {
    const index = e.currentTarget.dataset.index;
    const photos = this.data.photos;
    photos.splice(index, 1);
    this.setData({
      photos: photos
    });
  },

  /**
   * 下一步
   */
  nextStep: function () {
    // 验证必填字段
    if (!this.data.height) {
      wx.showToast({
        title: '请输入身高',
        icon: 'none'
      });
      return;
    }

    if (!this.data.weight) {
      wx.showToast({
        title: '请输入体重',
        icon: 'none'
      });
      return;
    }

    // 创建分析记录
    wx.showLoading({
      title: '正在保存...',
    });

    const token = wx.getStorageSync('token');

    wx.request({
      url: app.globalData.baseUrl + '/create_image_analysis.php',
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      data: {
        gender: this.data.gender,
        height: this.data.height,
        weight: this.data.weight,
        bust: this.data.bust,
        waist: this.data.waist,
        hips: this.data.hips,
        shoulder_width: this.data.shoulderWidth,
        skin_tone: this.data.skinTone,
        face_shape: this.data.faceShape,
        body_shape: this.data.bodyShape,
        remarks: this.data.remarks,
        photo_urls: this.data.photos
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.data.error) {
          wx.showToast({
            title: '创建失败: ' + res.data.msg,
            icon: 'none'
          });
          return;
        }

        // 跳转到支付页面
        wx.navigateTo({
          url: '/pages/image_analysis/payment/payment?analysisId=' + res.data.data.analysis_id +
               '&orderId=' + res.data.data.order_id +
               '&amount=' + res.data.data.amount +
               '&payParams=' + encodeURIComponent(JSON.stringify(res.data.data.pay_params))
        });
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  }
}) 