<?php
/**
 * 查询支付订单状态API
 * 
 * 用于查询微信支付订单的状态
 * 
 * 请求方法：GET
 * 请求参数：
 * - order_id: 订单号
 * 
 * 返回：
 * {
 *   "error": false,
 *   "data": {
 *     "order_id": "订单号",
 *     "status": "状态",
 *     "count": 次数
 *   }
 * }
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';
require_once 'wx_pay_helper.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'error' => true,
        'msg' => '只支持GET请求'
    ]);
    exit;
}

// 验证用户身份
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供授权Token'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token包含Bearer前缀，去掉它
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    echo json_encode([
        'error' => true,
        'msg' => '无效的授权Token或Token已过期'
    ]);
    exit;
}

// 获取请求参数
if (!isset($_GET['order_id']) || empty($_GET['order_id'])) {
    echo json_encode([
        'error' => true,
        'msg' => '缺少订单号参数'
    ]);
    exit;
}

$orderId = $_GET['order_id'];
$userId = $payload['sub'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查询本地订单状态
    $stmt = $conn->prepare("
        SELECT * FROM recharge_records 
        WHERE order_id = :order_id 
        AND user_id = :user_id
    ");
    $stmt->bindParam(':order_id', $orderId);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        echo json_encode([
            'error' => true,
            'msg' => '未找到订单'
        ]);
        exit;
    }
    
    // 如果订单已支付成功，直接返回
    if ($order['status'] === 'success') {
        echo json_encode([
            'error' => false,
            'data' => [
                'order_id' => $order['order_id'],
                'status' => $order['status'],
                'count' => $order['count'],
                'amount' => $order['amount']
            ]
        ]);
        exit;
    }
    
    // 如果订单是pending状态，查询微信支付状态
    if ($order['status'] === 'pending') {
        // 记录日志
        error_log("查询微信支付订单状态，订单号: " . $orderId);
        
        try {
            $wxPayHelper = new WxPayHelper();
            $result = $wxPayHelper->queryOrder($orderId);
            
            // 记录查询结果
            error_log("微信支付API返回结果: " . json_encode($result));
            
            // 如果查询失败，尝试再次查询一次
            if (isset($result['error']) && $result['error']) {
                error_log("第一次查询失败，错误信息: " . $result['msg']);
                // 等待短暂时间再次尝试
                usleep(500000); // 0.5秒
                error_log("重试查询订单状态");
                $result = $wxPayHelper->queryOrder($orderId);
                error_log("重试查询结果: " . json_encode($result));
                
                // 如果两次查询都失败，返回本地状态
                if (isset($result['error']) && $result['error']) {
                    error_log("两次查询微信支付API都失败，返回本地状态。错误信息: " . $result['msg']);
                    echo json_encode([
                        'error' => false,
                        'data' => [
                            'order_id' => $order['order_id'],
                            'status' => $order['status'],
                            'count' => $order['count'],
                            'amount' => $order['amount']
                        ]
                    ]);
                    exit;
                }
            }
            
            // 如果订单已支付但本地未更新，同步状态
            if (isset($result['trade_state'])) {
                error_log("订单状态: " . $result['trade_state']);
                
                if ($result['trade_state'] === 'SUCCESS') {
                    error_log("订单已支付成功，更新本地状态");
                    
                    // 开始事务
                    $conn->beginTransaction();
                    
                    try {
                        // 更新订单状态
                        $updateStmt = $conn->prepare("
                            UPDATE recharge_records 
                            SET 
                                status = 'success', 
                                transaction_id = :transaction_id, 
                                paid_at = NOW() 
                            WHERE id = :id
                        ");
                        $transactionId = $result['transaction_id'] ?? '';
                        $updateStmt->bindParam(':transaction_id', $transactionId);
                        $updateStmt->bindParam(':id', $order['id'], PDO::PARAM_INT);
                        $updateResult = $updateStmt->execute();
                        
                        error_log("更新订单状态结果: " . ($updateResult ? 'success' : 'failed'));
                        
                        // 更新用户试衣次数
                        $tryOnCountMode = defined('TRY_ON_COUNT_MODE') ? TRY_ON_COUNT_MODE : 'daily';
                        $count = $order['count'];
                        
                        error_log("试衣次数模式: $tryOnCountMode, 增加次数: $count");
                        
                        if ($tryOnCountMode === 'dual') {
                            // 双层次数模式：增加付费次数
                            $updateUserStmt = $conn->prepare("
                                UPDATE users 
                                SET 
                                    paid_try_on_count = paid_try_on_count + :count 
                                WHERE id = :user_id
                            ");
                            $updateUserStmt->bindParam(':count', $count, PDO::PARAM_INT);
                            $updateUserStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
                            $updateUserResult = $updateUserStmt->execute();
                            
                            error_log("更新用户试衣次数结果: " . ($updateUserResult ? 'success' : 'failed'));
                        } else if ($tryOnCountMode === 'database') {
                            // 数据库模式：增加paid_try_on_count（原try_on_count）
                            $updateUserStmt = $conn->prepare("
                                UPDATE users 
                                SET 
                                    paid_try_on_count = paid_try_on_count + :count 
                                WHERE id = :user_id
                            ");
                            $updateUserStmt->bindParam(':count', $count, PDO::PARAM_INT);
                            $updateUserStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
                            $updateUserResult = $updateUserStmt->execute();
                            
                            error_log("更新用户试衣次数结果: " . ($updateUserResult ? 'success' : 'failed'));
                        } else {
                            // 每日一次模式：创建用户的paid_try_on_count字段并增加次数
                            // 检查users表是否有paid_try_on_count字段
                            try {
                                $checkFieldStmt = $conn->query("SHOW COLUMNS FROM users LIKE 'paid_try_on_count'");
                                
                                if ($checkFieldStmt->rowCount() === 0) {
                                    // 字段不存在，添加字段
                                    error_log("paid_try_on_count字段不存在，添加字段");
                                    $conn->exec("ALTER TABLE users ADD COLUMN paid_try_on_count INT NOT NULL DEFAULT 0 AFTER gender");
                                }
                                
                                // 增加付费次数
                                $updateUserStmt = $conn->prepare("
                                    UPDATE users 
                                    SET 
                                        paid_try_on_count = paid_try_on_count + :count 
                                    WHERE id = :user_id
                                ");
                                $updateUserStmt->bindParam(':count', $count, PDO::PARAM_INT);
                                $updateUserStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
                                $updateUserResult = $updateUserStmt->execute();
                                
                                error_log("更新用户试衣次数结果: " . ($updateUserResult ? 'success' : 'failed'));
                            } catch (Exception $e) {
                                error_log("检查或添加paid_try_on_count字段失败: " . $e->getMessage());
                                throw $e; // 重新抛出异常以触发回滚
                            }
                        }
                        
                        // 提交事务
                        $conn->commit();
                        error_log("事务提交成功，订单状态已更新");
                        
                        // 更新订单状态为成功
                        $order['status'] = 'success';
                    } catch (Exception $e) {
                        $conn->rollBack();
                        error_log("更新订单状态事务失败: " . $e->getMessage());
                    }
                } else if ($result['trade_state'] === 'NOTPAY' || $result['trade_state'] === 'USERPAYING') {
                    error_log("订单支付中或未支付: " . $result['trade_state']);
                } else {
                    error_log("订单其他状态: " . $result['trade_state'] . ", " . ($result['trade_state_desc'] ?? ''));
                    
                    // 如果订单状态为关闭或撤销，更新本地状态
                    if ($result['trade_state'] === 'CLOSED' || $result['trade_state'] === 'REVOKED') {
                        try {
                            $updateStmt = $conn->prepare("
                                UPDATE recharge_records 
                                SET 
                                    status = 'failed', 
                                    updated_at = NOW() 
                                WHERE id = :id
                            ");
                            $updateStmt->bindParam(':id', $order['id'], PDO::PARAM_INT);
                            $updateResult = $updateStmt->execute();
                            
                            error_log("更新订单为失败状态: " . ($updateResult ? 'success' : 'failed'));
                            $order['status'] = 'failed';
                        } catch (Exception $e) {
                            error_log("更新订单失败状态出错: " . $e->getMessage());
                        }
                    }
                }
            } else {
                error_log("API返回数据中没有trade_state字段，无法判断订单状态");
            }
        } catch (Exception $e) {
            error_log("查询订单状态过程中出错: " . $e->getMessage());
        }
    }
    
    // 返回最终状态
    echo json_encode([
        'error' => false,
        'data' => [
            'order_id' => $order['order_id'],
            'status' => $order['status'],
            'count' => $order['count'],
            'amount' => $order['amount']
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '查询订单失败: ' . $e->getMessage()
    ]);
} 