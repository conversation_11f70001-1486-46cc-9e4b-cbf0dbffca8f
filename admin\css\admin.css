/* 图片上传相关样式 */
.image-preview {
    width: 200px;
    height: 200px;
    border: 1px dashed #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    position: relative;
    overflow: hidden;
    background-color: #f9f9f9;
}

.image-preview.empty::before {
    content: '暂无图片';
    color: #999;
    font-size: 14px;
}

.image-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
}

.item-image-preview {
    width: 160px;
    height: 160px;
}

.image-input-group {
    display: flex;
    gap: 10px;
}

.image-input-group .form-input {
    flex: 1;
}

.upload-btn {
    padding: 6px 12px;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    white-space: nowrap;
}

.upload-btn:hover {
    background-color: #e0e0e0;
}

.required::after {
    content: " *";
    color: red;
}

/* 商品项样式 */
.item-row {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    position: relative;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.item-header h4 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.remove-item-btn {
    background-color: #ff4d4f;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    cursor: pointer;
    padding: 0;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-item-btn:hover {
    background-color: #ff7875;
} 