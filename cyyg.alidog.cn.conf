server
{
    listen 80;
    server_name cyyg.alidog.cn;
    index index.php index.html index.htm default.php default.htm default.html;
    root /www/wwwroot/cyyg.alidog.cn;
    #CERT-APPLY-CHECK--START
    # 用于SSL证书申请时的文件验证相关配置 -- 请勿删除
    include /www/server/panel/vhost/nginx/well-known/cyyg.alidog.cn.conf;
    #CERT-APPLY-CHECK--END

    #SSL-START SSL相关配置，请勿删除或修改下一行带注释的404规则
    #error_page 404/404.html;

    #SSL-END

    #ERROR-PAGE-START  错误页配置，可以注释、删除或修改
    error_page 404 /404.html;
    #error_page 502 /502.html;
    #ERROR-PAGE-END

    #PHP-INFO-START  PHP引用配置，可以注释或修改
    include enable-php-74.conf;
    #PHP-INFO-END

    #REWRITE-START URL重写规则引用,修改后将导致面板设置的伪静态规则失效
    include /www/server/panel/vhost/rewrite/cyyg.alidog.cn.conf;
    #REWRITE-END
    
    # 工作台API配置 - 合并版本
    location /gongzuotai/ {
    # 处理OPTIONS预检请求
    if ($request_method = 'OPTIONS') {        return 204;
    }
    
    # 重写URL，将/gongzuotai/xxx映射到/login_backend/gongzuotai/xxx.php
    rewrite ^/gongzuotai/(.*)$ /login_backend/gongzuotai/$1.php last;
    
    # 允许跨域请求
    }

    #禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md)
    {
        return 404;
    }

    #一键申请SSL证书验证目录相关设置
    location ~ \.well-known{
        allow all;
    }

    #禁止在证书验证目录放入敏感文件
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      30d;
        error_log /dev/null;
        access_log /dev/null;
    }

    location ~ .*\.(js|css)?$
    {
        expires      12h;
        error_log /dev/null;
        access_log /dev/null;
    }
    access_log  /www/wwwlogs/cyyg.alidog.cn.log;
    error_log  /www/wwwlogs/cyyg.alidog.cn.error.log;
}