/**
 * 用户详情脚本
 */
const UserDetails = {
    // 用户ID
    userId: null,
    
    // 用户数据
    userData: null,
    
    // 模态框相关数据
    modal: {
        type: null, // 'clothes' 或 'photos'
        page: 1,
        perPage: 20,
        totalPages: 1
    },
    
    // DOM元素
    elements: {},
    
    // 试衣次数相关数据
    tryOnData: {
        freeCount: 0,
        paidCount: 0,
        countMode: 'daily'
    },
    
    /**
     * 初始化
     */
    init: function() {
        // 获取URL参数中的用户ID
        const urlParams = new URLSearchParams(window.location.search);
        this.userId = urlParams.get('id');
        
        if (!this.userId) {
            alert('缺少用户ID参数');
            window.location.href = 'user_list.html';
            return;
        }
        
        // 初始化DOM元素引用
        this.elements = {
            userDetailContainer: document.getElementById('userDetailContainer'),
            statsContainer: document.getElementById('statsContainer'),
            clothesContainer: document.getElementById('clothesContainer'),
            photosContainer: document.getElementById('photosContainer'),
            
            // 试衣次数管理元素
            currentFreeCount: document.getElementById('currentFreeCount'),
            currentPaidCount: document.getElementById('currentPaidCount'),
            countModeLabel: document.getElementById('countModeLabel'),
            newFreeCountInput: document.getElementById('newFreeCountInput'),
            newPaidCountInput: document.getElementById('newPaidCountInput'),
            updateCountBtn: document.getElementById('updateCountBtn'),
            
            // 模态框元素
            modal: document.getElementById('itemsModal'),
            modalTitle: document.getElementById('modalTitle'),
            modalItemsContainer: document.getElementById('modalItemsContainer'),
            modalPrevBtn: document.getElementById('modalPrevBtn'),
            modalNextBtn: document.getElementById('modalNextBtn'),
            modalPageInfo: document.getElementById('modalPageInfo')
        };
        
        // 绑定模态框分页事件
        this.elements.modalPrevBtn.addEventListener('click', () => {
            if (this.modal.page > 1) {
                this.modal.page--;
                this.loadAllItems();
            }
        });
        
        this.elements.modalNextBtn.addEventListener('click', () => {
            if (this.modal.page < this.modal.totalPages) {
                this.modal.page++;
                this.loadAllItems();
            }
        });
        
        // 绑定试衣次数更新按钮事件
        if (this.elements.updateCountBtn) {
            this.elements.updateCountBtn.addEventListener('click', () => {
                this.updateTryOnCount();
            });
        }
        
        // 绑定回车键更新试衣次数
        if (this.elements.newFreeCountInput || this.elements.newPaidCountInput) {
            this.elements.newFreeCountInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.updateTryOnCount();
                }
            });
        }
        
        // 加载用户详情
        this.loadUserDetails();
    },
    
    /**
     * 加载用户详情
     */
    loadUserDetails: function() {
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        fetch(`${Auth.apiBaseUrl}/get_admin_user_details.php?user_id=${this.userId}`, {
            method: 'GET',
            headers: {
                'Authorization': token
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '加载失败');
            }
            
            this.userData = data.data;
            this.renderUserDetails(data.data.user);
            this.renderUserStats(data.data.statistics);
            this.renderRecentClothes(data.data.recent_clothes);
            this.renderRecentPhotos(data.data.recent_photos);
            
            // 加载试衣次数数据
            this.loadTryOnCount();
        })
        .catch(error => {
            console.error('获取用户详情失败:', error);
            this.elements.userDetailContainer.innerHTML = `<div class="empty-text">加载失败: ${error.message}</div>`;
            this.elements.statsContainer.innerHTML = '<div class="empty-text">加载失败</div>';
            this.elements.clothesContainer.innerHTML = '<div class="empty-text">加载失败</div>';
            this.elements.photosContainer.innerHTML = '<div class="empty-text">加载失败</div>';
        });
    },
    
    /**
     * 渲染用户基本信息
     * @param {Object} user 用户数据
     */
    renderUserDetails: function(user) {
        if (!user) {
            this.elements.userDetailContainer.innerHTML = '<div class="empty-text">用户数据不存在</div>';
            return;
        }
        
        const statusClass = parseInt(user.status) === 1 ? 'status-active' : 'status-disabled';
        const statusText = parseInt(user.status) === 1 ? '正常' : '禁用';
        const gender = this.getGenderText(user.gender);
        
        // 本地默认头像
        const defaultAvatar = 'images/default-avatar.png';
        
        // 检查头像URL并处理微信临时文件路径
        let avatarUrl;
        if (!user.avatar_url || user.avatar_url.trim() === '') {
            avatarUrl = defaultAvatar;
        } else if (user.avatar_url.startsWith('wxfile://')) {
            // 不支持的微信临时文件URL，使用默认头像
            avatarUrl = defaultAvatar;
        } else {
            avatarUrl = user.avatar_url;
        }
        
        const html = `
            <div class="user-profile">
                <img src="${avatarUrl}" 
                     class="user-avatar" 
                     alt="用户头像" 
                     onerror="this.src='${defaultAvatar}'">
                <div class="user-info-main">
                    <div class="user-name">${user.nickname || '未设置昵称'}</div>
                    <div class="user-id">ID: ${user.id}</div>
                    <div class="user-meta">
                        <div class="meta-item">
                            <span class="meta-label">状态:</span>
                            <span class="status-badge ${statusClass}">${statusText}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">性别:</span>
                            <span>${gender}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">OpenID:</span>
                            <span>${user.openid}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">UnionID:</span>
                            <span>${user.unionid || '无'}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">创建时间:</span>
                            <span>${this.formatDate(user.created_at)}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">最后更新:</span>
                            <span>${user.updated_at ? this.formatDate(user.updated_at) : '无'}</span>
                        </div>
                    </div>
                    <div class="user-actions">
                        ${parseInt(user.status) === 1 
                            ? `<button class="user-action-btn user-disable-btn" onclick="UserDetails.updateUserStatus(0)">禁用用户</button>` 
                            : `<button class="user-action-btn user-enable-btn" onclick="UserDetails.updateUserStatus(1)">启用用户</button>`
                        }
                        <button class="user-action-btn user-danger-btn" onclick="UserDetails.clearAllClothes()">清空衣物</button>
                    </div>
                </div>
            </div>
        `;
        
        this.elements.userDetailContainer.innerHTML = html;
    },
    
    /**
     * 渲染用户统计信息
     * @param {Object} stats 统计数据
     */
    renderUserStats: function(stats) {
        if (!stats) {
            this.elements.statsContainer.innerHTML = '<div class="empty-text">统计数据不存在</div>';
            return;
        }
        
        // 试衣次数模式显示
        let countModeText = '';
        let freeCountDisplay = '';
        let paidCountDisplay = '';
        
        if (this.tryOnData.countMode === 'dual') {
            countModeText = '双层计数模式';
            freeCountDisplay = `<div class="stat-card">
                <div class="stat-title">免费试衣次数 (每日刷新)</div>
                <div class="stat-value">${this.tryOnData.freeCount}</div>
            </div>`;
            paidCountDisplay = `<div class="stat-card">
                <div class="stat-title">付费试衣次数</div>
                <div class="stat-value">${this.tryOnData.paidCount}</div>
            </div>`;
        } else if (this.tryOnData.countMode === 'database') {
            countModeText = '仅付费计数模式';
            // 修复：显示免费次数，即使在database模式下
            freeCountDisplay = `<div class="stat-card">
                <div class="stat-title">免费试衣次数</div>
                <div class="stat-value">${this.tryOnData.freeCount}</div>
            </div>`;
            paidCountDisplay = `<div class="stat-card">
                <div class="stat-title">付费试衣次数</div>
                <div class="stat-value">${this.tryOnData.paidCount}</div>
            </div>`;
        } else {
            countModeText = '每日重置模式';
            freeCountDisplay = `<div class="stat-card">
                <div class="stat-title">每日试衣机会</div>
                <div class="stat-value">${this.tryOnData.freeCount}</div>
            </div>`;
            // 修复：显示付费次数，即使在daily模式下
            if (this.tryOnData.paidCount > 0) {
                paidCountDisplay = `<div class="stat-card">
                    <div class="stat-title">付费试衣次数</div>
                    <div class="stat-value">${this.tryOnData.paidCount}</div>
                </div>`;
            }
        }
        
        const html = `
            <div class="stat-card">
                <div class="stat-title">衣物数量</div>
                <div class="stat-value">${stats.total_clothes}</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">照片数量</div>
                <div class="stat-value">${stats.total_photos}</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">已试衣总次数</div>
                <div class="stat-value">${stats.total_try_on}</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">试衣计数模式</div>
                <div class="stat-value">${countModeText}</div>
            </div>
            ${freeCountDisplay}
            ${paidCountDisplay}
        `;
        
        this.elements.statsContainer.innerHTML = html;
    },
    
    /**
     * 渲染最近衣物
     * @param {Array} clothes 衣物数据
     */
    renderRecentClothes: function(clothes) {
        if (!clothes || clothes.length === 0) {
            this.elements.clothesContainer.innerHTML = '<div class="empty-text">暂无衣物数据</div>';
            return;
        }
        
        let html = '';
        clothes.forEach(item => {
            const defaultImg = 'images/default-cloth.png';
            
            // 检查图片URL并处理微信临时文件路径
            let imageUrl;
            if (!item.image_url || item.image_url.trim() === '') {
                imageUrl = defaultImg;
            } else if (item.image_url.startsWith('wxfile://')) {
                // 不支持的微信临时文件URL，使用默认图片
                imageUrl = defaultImg;
            } else {
                imageUrl = item.image_url;
            }
            
            html += `
                <div class="item-card">
                    <img src="${imageUrl}" class="item-img" alt="衣物图片" onerror="this.src='${defaultImg}'">
                    <div class="item-info">
                        <div class="item-name">${item.name}</div>
                        <div class="item-meta">${this.getCategoryText(item.category)} · ${this.formatDate(item.created_at)}</div>
                    </div>
                </div>
            `;
        });
        
        this.elements.clothesContainer.innerHTML = html;
    },
    
    /**
     * 渲染最近照片
     * @param {Array} photos 照片数据
     */
    renderRecentPhotos: function(photos) {
        if (!photos || photos.length === 0) {
            this.elements.photosContainer.innerHTML = '<div class="empty-text">暂无照片数据</div>';
            return;
        }
        
        let html = '';
        photos.forEach(item => {
            const defaultImg = 'images/default-photo.png';
            
            // 检查图片URL并处理微信临时文件路径
            let imageUrl;
            if (!item.image_url || item.image_url.trim() === '') {
                imageUrl = defaultImg;
            } else if (item.image_url.startsWith('wxfile://')) {
                // 不支持的微信临时文件URL，使用默认图片
                imageUrl = defaultImg;
            } else {
                imageUrl = item.image_url;
            }
            
            html += `
                <div class="item-card">
                    <img src="${imageUrl}" class="item-img" alt="照片" onerror="this.src='${defaultImg}'">
                    <div class="item-info">
                        <div class="item-name">照片 #${item.id}</div>
                        <div class="item-meta">${this.getPhotoTypeText(item.type)} · ${this.formatDate(item.created_at)}</div>
                    </div>
                </div>
            `;
        });
        
        this.elements.photosContainer.innerHTML = html;
    },
    
    /**
     * 查看所有项目
     * @param {String} type 类型：'clothes'或'photos'
     */
    viewAllItems: function(type) {
        if (type === 'clothes') {
            window.location.href = `clothing_list.html?user_id=${this.userId}`;
        } else {
            window.location.href = `photo_list.html?user_id=${this.userId}`;
        }
    },
    
    /**
     * 加载所有项目数据
     */
    loadAllItems: function() {
        // 此方法不再需要，因为使用了页面跳转
        // 保留此空方法以避免潜在的引用错误
    },
    
    /**
     * 渲染模态框中的衣物
     * @param {Array} clothes 衣物数据
     */
    renderModalClothes: function(clothes) {
        // 此方法不再需要，因为使用了页面跳转
        // 保留此空方法以避免潜在的引用错误
    },
    
    /**
     * 渲染模态框中的照片
     * @param {Array} photos 照片数据
     */
    renderModalPhotos: function(photos) {
        // 此方法不再需要，因为使用了页面跳转
        // 保留此空方法以避免潜在的引用错误
    },
    
    /**
     * 更新模态框分页控件
     * @param {Object} pagination 分页信息
     */
    updateModalPagination: function(pagination) {
        // 此方法不再需要，因为使用了页面跳转
        // 保留此空方法以避免潜在的引用错误
    },
    
    /**
     * 关闭模态框
     */
    closeModal: function() {
        // 此方法不再需要，因为使用了页面跳转
        // 保留此空方法以避免潜在的引用错误
    },
    
    /**
     * 更新用户状态
     * @param {Number} status 状态值 (0: 禁用, 1: 启用)
     */
    updateUserStatus: function(status) {
        if (!confirm(`确定要${status === 1 ? '启用' : '禁用'}该用户吗？`)) {
            return;
        }
        
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        fetch(`${Auth.apiBaseUrl}/update_admin_user_status.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': token
            },
            body: JSON.stringify({
                user_id: this.userId,
                status: status
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '操作失败');
            }
            
            alert(data.msg || '操作成功');
            
            // 更新本地用户状态并重新渲染
            if (this.userData && this.userData.user) {
                this.userData.user.status = status;
                this.renderUserDetails(this.userData.user);
            }
        })
        .catch(error => {
            console.error('更新用户状态失败:', error);
            alert(`操作失败: ${error.message}`);
        });
    },
    
    /**
     * 获取性别文本
     * @param {Number} gender 性别码
     * @returns {String} 性别文本
     */
    getGenderText: function(gender) {
        switch(parseInt(gender)) {
            case 1: return '男';
            case 2: return '女';
            default: return '未知';
        }
    },
    
    /**
     * 获取衣物分类文本
     * @param {String} category 分类代码
     * @returns {String} 分类文本
     */
    getCategoryText: function(category) {
        const categoryMap = {
            'tops': '上衣',
            'pants': '裤子',
            'skirts': '裙子',
            'coats': '外套',
            'shoes': '鞋子',
            'bags': '包包',
            'accessories': '配饰'
        };
        
        return categoryMap[category] || category;
    },
    
    /**
     * 获取照片类型文本
     * @param {String} type 照片类型
     * @returns {String} 类型文本
     */
    getPhotoTypeText: function(type) {
        const typeMap = {
            'full': '全身照',
            'half': '半身照'
        };
        
        return typeMap[type] || type || '未知类型';
    },
    
    /**
     * 格式化日期
     * @param {String} dateStr 日期字符串
     * @returns {String} 格式化的日期
     */
    formatDate: function(dateStr) {
        if (!dateStr) return '未知';
        
        const date = new Date(dateStr);
        return isNaN(date.getTime()) 
            ? dateStr 
            : date.getFullYear() + '-' + 
              String(date.getMonth() + 1).padStart(2, '0') + '-' + 
              String(date.getDate()).padStart(2, '0') + ' ' +
              String(date.getHours()).padStart(2, '0') + ':' +
              String(date.getMinutes()).padStart(2, '0');
    },
    
    /**
     * 加载用户试衣次数
     */
    loadTryOnCount: function() {
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        // 发起专门的API请求获取试衣次数信息
        fetch(`${Auth.apiBaseUrl}/get_admin_user_try_on_count.php?user_id=${this.userId}`, {
            method: 'GET',
            headers: {
                'Authorization': token
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '加载失败');
            }
            
            this.tryOnData.countMode = data.data.count_mode;
            
            // 根据计数模式处理不同的数据结构
            if (data.data.count_mode === 'dual') {
                this.tryOnData.freeCount = parseInt(data.data.free_try_on_count || 0);
                this.tryOnData.paidCount = parseInt(data.data.paid_try_on_count || 0);
            } else if (data.data.count_mode === 'database') {
                // 修复: 使用API返回的实际免费次数，而不是硬编码为0
                this.tryOnData.freeCount = parseInt(data.data.free_try_on_count || 0);
                this.tryOnData.paidCount = parseInt(data.data.try_on_count || 0);
            } else {
                // 每日模式
                this.tryOnData.freeCount = parseInt(data.data.free_try_on_count || data.data.try_on_count || 0);
                this.tryOnData.paidCount = parseInt(data.data.paid_try_on_count || 0);
            }
            
            this.renderTryOnCount();
            
            // 更新用户统计信息
            this.renderUserStats(this.userData.statistics);
        })
        .catch(error => {
            console.error('获取用户试衣次数失败:', error);
            if (this.elements.currentFreeCount) this.elements.currentFreeCount.textContent = '加载失败';
            if (this.elements.currentPaidCount) this.elements.currentPaidCount.textContent = '加载失败';
        });
    },
    
    /**
     * 渲染试衣次数信息
     */
    renderTryOnCount: function() {
        if (!this.elements.currentFreeCount || !this.elements.currentPaidCount || !this.elements.countModeLabel) return;
        
        this.elements.currentFreeCount.textContent = this.tryOnData.freeCount;
        this.elements.currentPaidCount.textContent = this.tryOnData.paidCount;
        
        // 显示当前计数模式
        let modeText = '每日重置模式';
        if (this.tryOnData.countMode === 'database') {
            modeText = '仅付费计数模式';
        } else if (this.tryOnData.countMode === 'dual') {
            modeText = '双层计数模式';
        }
        this.elements.countModeLabel.textContent = modeText;
        
        // 将当前值设置为输入框的默认值
        if (this.elements.newFreeCountInput) {
            this.elements.newFreeCountInput.value = this.tryOnData.freeCount;
        }
        if (this.elements.newPaidCountInput) {
            this.elements.newPaidCountInput.value = this.tryOnData.paidCount;
        }
    },
    
    /**
     * 更新用户试衣次数
     */
    updateTryOnCount: function() {
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        // 获取输入的新试衣次数
        const newFreeCount = parseInt(this.elements.newFreeCountInput.value) || 0;
        const newPaidCount = parseInt(this.elements.newPaidCountInput.value) || 0;
        
        // 验证输入
        if (newFreeCount < 0 || newPaidCount < 0) {
            alert('试衣次数不能为负数');
            return;
        }
        
        // 禁用按钮，避免重复提交
        this.elements.updateCountBtn.disabled = true;
        
        // 构造请求体
        const formData = new FormData();
        formData.append('user_id', this.userId);
        formData.append('free_try_on_count', newFreeCount);
        formData.append('paid_try_on_count', newPaidCount);
        
        // 发送请求
        fetch(`${Auth.apiBaseUrl}/update_user_try_on_count.php`, {
            method: 'POST',
            headers: {
                'Authorization': token
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // 恢复按钮状态
            this.elements.updateCountBtn.disabled = false;
            
            if (data.error) {
                throw new Error(data.msg || '更新失败');
            }
            
            // 更新成功，更新本地数据
            this.tryOnData.freeCount = newFreeCount;
            this.tryOnData.paidCount = newPaidCount;
            
            // 更新显示
            this.renderTryOnCount();
            
            // 更新用户数据中的试衣次数
            if (this.userData && this.userData.user) {
                this.userData.user.free_try_on_count = newFreeCount;
                this.userData.user.paid_try_on_count = newPaidCount;
            }
            
            // 更新统计信息
            this.renderUserStats(this.userData.statistics);
            
            alert('试衣次数更新成功');
        })
        .catch(error => {
            console.error('更新用户试衣次数失败:', error);
            alert('更新失败: ' + error.message);
            this.elements.updateCountBtn.disabled = false;
        });
    },
    
    /**
     * 清空所有衣物
     */
    clearAllClothes: function() {
        if (!confirm('确定要清空该用户的所有衣物吗？此操作不可恢复！')) {
            return;
        }
        
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        // 显示确认二次验证
        if (!confirm(`再次确认：将删除用户ID ${this.userId} 的所有衣物记录和图片，确定继续吗？`)) {
            return;
        }
        
        // 显示加载状态
        this.elements.clothesContainer.innerHTML = '<div class="loading">正在删除，请稍候...</div>';
        
        fetch(`${Auth.apiBaseUrl}/delete_all_user_clothes.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': token
            },
            body: JSON.stringify({
                user_id: this.userId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '操作失败');
            }
            
            alert(data.msg || '操作成功');
            
            // 更新用户统计信息和衣物展示
            if (this.userData && this.userData.statistics) {
                this.userData.statistics.total_clothes = 0;
                this.renderUserStats(this.userData.statistics);
            }
            
            // 清空衣物列表
            this.elements.clothesContainer.innerHTML = '<div class="empty-text">暂无衣物数据</div>';
            
            // 刷新页面以获取最新数据
            setTimeout(() => {
                location.reload();
            }, 1500);
        })
        .catch(error => {
            console.error('清空衣物失败:', error);
            alert(`操作失败: ${error.message}`);
            
            // 恢复原始显示
            this.renderRecentClothes(this.userData.recent_clothes || []);
        });
    }
}; 