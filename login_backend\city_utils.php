<?php
/**
 * 城市工具函数，用于处理和风天气城市代码和名称的转换
 */

/**
 * 直接从CSV文件创建城市ID映射表
 * 
 * @return array 城市ID => 城市信息的关联数组
 */
function buildCityIdMapFromCsv() {
    $csvFile = __DIR__ . '/json/China-City-List-latest.csv';
    
    // 检查文件是否存在
    if (!file_exists($csvFile)) {
        error_log("城市CSV文件不存在: $csvFile");
        return [];
    }
    
    error_log("开始从CSV文件构建城市ID映射表: $csvFile");
    
    // 读取CSV文件
    $content = file_get_contents($csvFile);
    if ($content === false) {
        error_log("无法读取城市CSV文件");
        return [];
    }
    
    // 将内容拆分为行
    $lines = explode("\n", $content);
    
    // 移除第一行（标题行）
    array_shift($lines);
    
    $cityMap = [];
    
    // 处理每一行
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        $fields = str_getcsv($line);
        if (count($fields) < 3) {
            continue; // 跳过格式不正确的行
        }
        
        $cityId = trim($fields[0]);
        $cityName = trim($fields[1]);
        
        // 确保城市ID有效
        if (empty($cityId) || !preg_match('/^\d+$/', $cityId)) {
            continue;
        }
        
        // 为杭州创建特殊映射（以防CSV中格式有问题）
        if ($cityId === '101210101' && empty($cityName)) {
            $cityName = '杭州';
        }
        
        // 提取其他有用字段
        $adm1 = isset($fields[6]) ? trim($fields[6]) : '';
        $adm2 = isset($fields[7]) ? trim($fields[7]) : '';
        $lat = isset($fields[9]) ? trim($fields[9]) : '';
        $lon = isset($fields[10]) ? trim($fields[10]) : '';
        
        // 存储城市信息
        $cityMap[$cityId] = [
            'id' => $cityId,
            'name' => $cityName,
            'adm1' => $adm1,
            'adm2' => $adm2,
            'lat' => $lat,
            'lon' => $lon
        ];
    }
    
    error_log("成功构建城市ID映射表，共 " . count($cityMap) . " 个城市");
    
    // 为测试添加常用城市日志
    if (isset($cityMap['101210101'])) {
        error_log("杭州市信息: " . json_encode($cityMap['101210101']));
    }
    if (isset($cityMap['101010100'])) {
        error_log("北京市信息: " . json_encode($cityMap['101010100']));
    }
    
    return $cityMap;
}

/**
 * 将CSV城市数据保存为JSON文件
 * 
 * @return bool 成功返回true，失败返回false
 */
function saveCityMapAsJson() {
    $cityMap = buildCityIdMapFromCsv();
    if (empty($cityMap)) {
        error_log("无城市数据可保存");
        return false;
    }
    
    $jsonFile = __DIR__ . '/json/city_map.json';
    $jsonDir = dirname($jsonFile);
    
    // 确保目录存在
    if (!is_dir($jsonDir)) {
        if (!mkdir($jsonDir, 0755, true)) {
            error_log("无法创建目录: $jsonDir");
            return false;
        }
    }
    
    // 保存为JSON文件
    $result = file_put_contents($jsonFile, json_encode($cityMap, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    if ($result === false) {
        error_log("保存城市数据到JSON文件失败");
        return false;
    }
    
    error_log("成功保存城市数据到JSON文件: $jsonFile");
    return true;
}

/**
 * 获取城市数据缓存
 * 优先使用JSON文件，如果JSON文件不存在或已过期，则尝试使用CSV文件
 * 
 * @return array 城市ID与城市信息的映射数组
 */
function getCityDataCache() {
    $cacheFile = __DIR__ . '/json/city_map.json';
    $cacheDir = dirname($cacheFile);
    $cacheExpire = 30 * 24 * 60 * 60; // 30天有效期
    
    // 确保缓存目录存在
    if (!is_dir($cacheDir)) {
        mkdir($cacheDir, 0755, true);
    }
    
    // 检查JSON缓存是否存在且未过期
    if (file_exists($cacheFile) && (time() - filemtime($cacheFile) < $cacheExpire)) {
        $jsonData = file_get_contents($cacheFile);
        $cityList = json_decode($jsonData, true);
        
        if (!empty($cityList)) {
            error_log("使用JSON缓存城市数据，共 " . count($cityList) . " 个城市");
            return $cityList;
        }
    }
    
    // 尝试从CSV创建城市映射
    $cityList = buildCityIdMapFromCsv();
    if (!empty($cityList)) {
        // 保存为JSON以备后用
        saveCityMapAsJson();
        return $cityList;
    }
    
    // 下载最新城市列表
    $cityList = downloadCityList();
    if (empty($cityList)) {
        // 如果下载失败但JSON缓存存在，仍然使用缓存
        if (file_exists($cacheFile)) {
            error_log("下载城市列表失败，尝试使用现有JSON缓存");
            $jsonData = file_get_contents($cacheFile);
            $cachedList = json_decode($jsonData, true);
            if (!empty($cachedList)) {
                return $cachedList;
            }
        }
        // 缓存不存在，返回空数组
        return [];
    }
    
    // 写入JSON缓存
    file_put_contents($cacheFile, json_encode($cityList, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    return $cityList;
}

/**
 * 下载和风天气城市列表
 * 
 * @return array 城市ID与城市信息的映射数组
 */
function downloadCityList() {
    $url = 'https://raw.githubusercontent.com/qwd/LocationList/master/China-City-List-latest.csv';
    
    error_log("开始下载城市列表: $url");
    
    try {
        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]);
        
        $csvData = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error || $httpCode !== 200) {
            error_log("下载城市列表失败: " . ($error ?: "HTTP状态码: $httpCode"));
            return [];
        }
        
        // 解析CSV数据
        $cityList = [];
        $lines = explode("\n", $csvData);
        $header = str_getcsv(array_shift($lines)); // 获取并移除头部
        
        foreach ($lines as $line) {
            if (empty(trim($line))) continue;
            
            $data = str_getcsv($line);
            if (count($data) < 4) continue;
            
            // 预期格式: ID,名称,名称(英文),所在地区等
            $cityId = trim($data[0]);
            if (empty($cityId)) continue;
            
            $cityList[$cityId] = [
                'id' => $cityId,
                'name' => trim($data[1]),
                'name_en' => isset($data[2]) ? trim($data[2]) : '',
                'adm1' => isset($data[3]) ? trim($data[3]) : '',
                'adm2' => isset($data[1]) ? trim($data[1]) : ''
            ];
        }
        
        error_log("成功下载和解析城市列表，共 " . count($cityList) . " 个城市");
        return $cityList;
        
    } catch (Exception $e) {
        error_log("下载城市列表异常: " . $e->getMessage());
        return [];
    }
}

/**
 * 通过城市ID查找城市信息
 * 
 * @param string $cityId 城市ID
 * @return array|null 城市信息数组，未找到则返回null
 */
function getCityById($cityId) {
    $cityList = getCityDataCache();
    return isset($cityList[$cityId]) ? $cityList[$cityId] : null;
}

/**
 * 通过经纬度获取城市信息
 * 
 * @param float $lat 纬度
 * @param float $lon 经度
 * @return array|null 城市信息，如果未找到则返回null
 */
function getCityByCoordinates($lat, $lon) {
    // 验证经纬度
    if (!is_numeric($lat) || !is_numeric($lon) || 
        $lat < -90 || $lat > 90 || $lon < -180 || $lon > 180) {
        error_log("城市工具 - 无效的经纬度格式: {$lat}, {$lon}");
        return null;
    }
    
    // 首先使用本地方法查找
    $cityId = getCityIdByLocation($lat, $lon);
    if ($cityId) {
        $cityInfo = getCityInfoById($cityId);
        if ($cityInfo) {
            error_log("城市工具 - 通过本地方法找到城市: {$cityInfo['name']} (ID: $cityId)");
        return $cityInfo;
    }
    }
    
    // 如果本地查找失败，尝试调用地理编码API
    error_log("城市工具 - 本地查找失败，尝试调用地理编码API: {$lat}, {$lon}");
    
    // 确保proxy_geo_api.php文件存在
    if (!file_exists(__DIR__ . '/proxy_geo_api.php')) {
        error_log("城市工具 - proxy_geo_api.php文件不存在");
        return null;
    }
    
    // 引入proxy_geo_api.php文件
    require_once __DIR__ . '/proxy_geo_api.php';
    
    // 调用地理编码API
    $geoResponse = callGeocodingAPI($lat, $lon);
    
    // 解析响应
    if ($geoResponse && isset($geoResponse['success']) && $geoResponse['success'] === true) {
        if (isset($geoResponse['data']) && is_array($geoResponse['data']) && !empty($geoResponse['data'])) {
            $cityData = $geoResponse['data'][0];
            
            // 构建城市信息
            $cityInfo = [
                'id' => $cityData['id'],
                'name' => $cityData['name'],
                'adm1' => $cityData['adm1'] ?? '',
                'adm2' => $cityData['adm2'] ?? '',
                'lat' => $cityData['lat'] ?? $lat,
                'lon' => $cityData['lon'] ?? $lon
            ];
            
            error_log("城市工具 - 通过地理编码API找到城市: {$cityInfo['name']} (ID: {$cityInfo['id']})");
            return $cityInfo;
        } else {
            error_log("城市工具 - 地理编码API返回成功但没有数据");
        }
    } else {
        error_log("城市工具 - 地理编码API调用失败: " . ($geoResponse['message'] ?? '未知错误'));
    }
    
    // 如果所有方法都失败，尝试使用已知大城市的经纬度范围进行粗略匹配
    $majorCities = [
        ['name' => '北京', 'id' => '101010100', 'adm1' => '北京市', 'lat' => 39.90, 'lon' => 116.40, 'radius' => 0.5],
        ['name' => '上海', 'id' => '101020100', 'adm1' => '上海市', 'lat' => 31.23, 'lon' => 121.47, 'radius' => 0.5],
        ['name' => '广州', 'id' => '101280101', 'adm1' => '广东省', 'lat' => 23.13, 'lon' => 113.26, 'radius' => 0.5],
        ['name' => '深圳', 'id' => '101280601', 'adm1' => '广东省', 'lat' => 22.54, 'lon' => 114.06, 'radius' => 0.5],
        ['name' => '天津', 'id' => '101030100', 'adm1' => '天津市', 'lat' => 39.09, 'lon' => 117.20, 'radius' => 0.5],
        ['name' => '重庆', 'id' => '101040100', 'adm1' => '重庆市', 'lat' => 29.56, 'lon' => 106.55, 'radius' => 0.5],
        ['name' => '成都', 'id' => '101270101', 'adm1' => '四川省', 'lat' => 30.66, 'lon' => 104.07, 'radius' => 0.5],
        ['name' => '杭州', 'id' => '101210101', 'adm1' => '浙江省', 'lat' => 30.22, 'lon' => 120.15, 'radius' => 0.5],
        ['name' => '武汉', 'id' => '101200101', 'adm1' => '湖北省', 'lat' => 30.59, 'lon' => 114.31, 'radius' => 0.5],
        ['name' => '西安', 'id' => '101110101', 'adm1' => '陕西省', 'lat' => 34.34, 'lon' => 108.94, 'radius' => 0.5],
    ];
    
    foreach ($majorCities as $city) {
        // 计算距离（简化版，仅用于粗略判断）
        $latDiff = abs($lat - $city['lat']);
        $lonDiff = abs($lon - $city['lon']);
            
        // 如果在半径范围内，认为匹配到这个城市
        if ($latDiff < $city['radius'] && $lonDiff < $city['radius']) {
            error_log("城市工具 - 通过范围匹配找到城市: {$city['name']} (ID: {$city['id']})");
            return [
                'id' => $city['id'],
                'name' => $city['name'],
                'adm1' => $city['adm1'],
                'adm2' => $city['name'] . '市',
                'lat' => $city['lat'],
                'lon' => $city['lon']
            ];
                }
            }
            
    // 如果所有方法都失败，返回null
    error_log("城市工具 - 无法找到匹配的城市，经纬度: {$lat}, {$lon}");
    return null;
}

/**
 * 格式化城市显示名称
 * 
 * @param array $cityInfo 城市信息
 * @return string 格式化后的城市名称
 */
function formatCityName($cityInfo) {
    if (empty($cityInfo)) {
        return '未知城市';
    }
    
    // 如果没有adm2或adm2与name相同，只显示name
    if (empty($cityInfo['adm2']) || $cityInfo['adm2'] == $cityInfo['name']) {
        return $cityInfo['name'];
    }
    
    // 如果adm2是城市名（以"市"结尾），name是区县名，显示"城市 区县"
    if (mb_substr($cityInfo['adm2'], -1) === '市' && mb_substr($cityInfo['name'], -1) !== '市') {
        return $cityInfo['adm2'] . ' ' . $cityInfo['name'];
    }
    
    // 其他情况显示完整名称
    return $cityInfo['name'];
}

// 确保只包含一次
if (!defined('CITY_UTILS_INCLUDED')) {
    define('CITY_UTILS_INCLUDED', true);

    /**
     * 验证并修复城市映射数据中的经纬度格式问题
     * 
     * @param array $cityMap 原始城市映射数据
     * @return array 修复后的城市映射数据
     */
    function validateAndFixCityMap($cityMap) {
        if (empty($cityMap) || !is_array($cityMap)) {
            error_log("城市工具 - 验证城市映射：数据为空或格式错误");
            return [];
        }
        
        $fixedMap = [];
        $fixCount = 0;
        $skipCount = 0;
        
        foreach ($cityMap as $cityId => $cityInfo) {
            // 跳过没有基本信息的城市
            if (empty($cityInfo['name'])) {
                $skipCount++;
                continue;
            }
            
            // 复制城市信息以便修改
            $fixedCityInfo = $cityInfo;
            
            // 检查并修复经纬度
            $needsFix = false;
            
            // 检查lat字段
            if (isset($cityInfo['lat'])) {
                // 尝试将lat转换为数值
                $lat = $cityInfo['lat'];
                
                // 检查是否为数字或可转换为数字的字符串
                if (is_numeric($lat)) {
                    $lat = (float)$lat;
                    
                    // 检查是否在有效范围内
                    if ($lat < -90 || $lat > 90) {
                        // 可能是经度值放在了纬度位置
                        if ($lat >= -180 && $lat <= 180) {
                            // 这个值可能是经度
                            $needsFix = true;
                        } else {
                            // 值完全超出范围，设为null以便后续处理
                            $lat = null;
                            $needsFix = true;
                        }
                    }
                } else {
                    // 非数字字符串，设为null以便后续处理
                    $lat = null;
                    $needsFix = true;
                }
                
                $fixedCityInfo['lat'] = $lat;
            } else {
                $fixedCityInfo['lat'] = null;
                $needsFix = true;
            }
            
            // 检查lon字段
            if (isset($cityInfo['lon'])) {
                // 尝试将lon转换为数值
                $lon = $cityInfo['lon'];
                
                // 检查是否为数字或可转换为数字的字符串
                if (is_numeric($lon)) {
                    $lon = (float)$lon;
                    
                    // 检查是否在有效范围内
                    if ($lon < -180 || $lon > 180) {
                        // 可能是纬度值放在了经度位置
                        if ($lon >= -90 && $lon <= 90) {
                            // 这个值可能是纬度
                            $needsFix = true;
                        } else {
                            // 值完全超出范围，设为null以便后续处理
                            $lon = null;
                            $needsFix = true;
                        }
                    }
                } else {
                    // 非数字字符串，设为null以便后续处理
                    $lon = null;
                    $needsFix = true;
                }
                
                $fixedCityInfo['lon'] = $lon;
            } else {
                $fixedCityInfo['lon'] = null;
                $needsFix = true;
            }
            
            // 检查经纬度是否需要交换
            if ($fixedCityInfo['lat'] !== null && $fixedCityInfo['lon'] !== null) {
                $lat = $fixedCityInfo['lat'];
                $lon = $fixedCityInfo['lon'];
                
                // 如果经度值在纬度范围，而纬度值在经度范围，可能需要交换
                if (($lat < -90 || $lat > 90 || $lon < -180 || $lon > 180) &&
                    ($lon >= -90 && $lon <= 90 && $lat >= -180 && $lat <= 180)) {
                    // 交换经纬度
                    $fixedCityInfo['lat'] = $lon;
                    $fixedCityInfo['lon'] = $lat;
                    $needsFix = true;
                }
            }
            
            // 记录修复情况
            if ($needsFix) {
                $fixCount++;
                error_log("城市工具 - 修复城市 {$cityInfo['name']} (ID: {$cityId}) 的经纬度: " . 
                          "原始值 lat=" . (isset($cityInfo['lat']) ? $cityInfo['lat'] : "未设置") . 
                          ", lon=" . (isset($cityInfo['lon']) ? $cityInfo['lon'] : "未设置") . 
                          " => 修复后 lat=" . (isset($fixedCityInfo['lat']) ? $fixedCityInfo['lat'] : "未设置") . 
                          ", lon=" . (isset($fixedCityInfo['lon']) ? $fixedCityInfo['lon'] : "未设置"));
            }
            
            // 保存修复后的城市信息
            $fixedMap[$cityId] = $fixedCityInfo;
        }
        
        error_log("城市工具 - 城市映射验证完成：总共 " . count($cityMap) . " 个城市，修复 {$fixCount} 个，跳过 {$skipCount} 个");
        return $fixedMap;
    }

    /**
     * 从本地JSON文件加载城市映射数据
     * 
     * @return array 城市映射数据
     */
    function loadCityMapFromJson() {
        static $cityMap = null;
        
        // 如果已加载，直接返回
        if ($cityMap !== null) {
            return $cityMap;
        }
        
        $jsonFile = __DIR__ . '/json/city_map.json';
        
        // 检查JSON文件是否存在
        if (!file_exists($jsonFile)) {
            error_log("城市工具 - 城市映射JSON文件不存在: $jsonFile");
            return [];
        }
        
        // 读取并解析JSON文件
        $jsonContent = file_get_contents($jsonFile);
        if ($jsonContent === false) {
            error_log("城市工具 - 无法读取城市映射JSON文件: $jsonFile");
            return [];
        }
        
        $rawCityMap = json_decode($jsonContent, true);
        if ($rawCityMap === null) {
            error_log("城市工具 - 城市映射JSON文件格式无效: $jsonFile");
            return [];
        }
        
        // 验证并修复城市映射数据
        $cityMap = validateAndFixCityMap($rawCityMap);
        
        error_log("城市工具 - 成功加载城市映射数据，包含 " . count($cityMap) . " 个城市");
        return $cityMap;
    }

    /**
     * 根据城市名称查找城市ID
     * 
     * @param string $cityName 城市名称
     * @return string|null 城市ID，如果未找到则返回null
     */
    function getCityIdByName($cityName) {
        // 移除省份信息
        $cityName = preg_replace('/^.*?[省自治区]|\s+/u', '', $cityName);
        $cityName = preg_replace('/市$/u', '', $cityName);
        
        // 常用城市直接映射（避免遍历整个数组）
        $commonCities = [
            '北京' => '101010100',
            '上海' => '101020100',
            '广州' => '101280101',
            '深圳' => '101280601',
            '杭州' => '101210101',
            '南京' => '101190101',
            '天津' => '101030100',
            '武汉' => '101200101',
            '西安' => '101110101',
            '成都' => '101270101',
            '重庆' => '101040100'
        ];
        
        if (isset($commonCities[$cityName])) {
            error_log("城市工具 - 常用城市映射: $cityName => {$commonCities[$cityName]}");
            return $commonCities[$cityName];
        }
        
        // 加载完整城市映射
        $cityMap = loadCityMapFromJson();
        
        // 先尝试精确匹配
        foreach ($cityMap as $cityId => $cityInfo) {
            $currentName = preg_replace('/市$/u', '', $cityInfo['name']);
            if ($currentName === $cityName) {
                error_log("城市工具 - 从JSON找到精确匹配城市: $cityName => $cityId");
                return $cityId;
            }
        }
        
        // 如果精确匹配失败，尝试模糊匹配 - 检查城市名是否包含在目标名中
        foreach ($cityMap as $cityId => $cityInfo) {
            $currentName = preg_replace('/市$/u', '', $cityInfo['name']);
            // 检查当前城市名是否包含搜索的城市名，或搜索的城市名是否包含当前城市名
            if (mb_strpos($currentName, $cityName) !== false || mb_strpos($cityName, $currentName) !== false) {
                error_log("城市工具 - 从JSON找到模糊匹配城市: $cityName => {$cityInfo['name']} ($cityId)");
                return $cityId;
            }
        }
        
        // 如果仍未找到，根据城市名首字母的拼音或英文名尝试匹配
        $firstChar = mb_substr($cityName, 0, 1, 'UTF-8');
        error_log("城市工具 - 尝试首字母匹配: $firstChar");
        
        foreach ($cityMap as $cityId => $cityInfo) {
            // 检查城市名首字母
            $cityFirstChar = mb_substr($cityInfo['name'], 0, 1, 'UTF-8');
            if ($cityFirstChar === $firstChar) {
                error_log("城市工具 - 从JSON找到首字母匹配城市: $cityName => {$cityInfo['name']} ($cityId)");
                return $cityId;
            }
        }
        
        // 如果仍然未找到，返回北京作为兜底
        error_log("城市工具 - 未找到城市 '$cityName'，使用北京作为兜底值");
        return '101010100'; // 北京
    }

    /**
     * 根据经纬度查找最近的城市ID
     * 
     * @param float $lat 纬度
     * @param float $lon 经度
     * @return string 最近的城市ID
     */
    function getCityIdByLocation($lat, $lon) {
        // 验证经纬度格式
        if (!is_numeric($lat) || !is_numeric($lon)) {
            error_log("城市工具 - 无效的经纬度格式: {$lat}, {$lon}，尝试使用合理的默认值");
            // 不直接返回杭州，而是提供一个通用的中国中心位置作为默认值
            $lat = 35.86166;  // 中国大致中心纬度
            $lon = 104.195397; // 中国大致中心经度
        }
        
        // 移除对杭州位置的特殊判断
        
        // 加载城市映射
        $cityMap = loadCityMapFromJson();
        
        $minDistance = PHP_FLOAT_MAX;
        $closestCityId = null; // 不预设默认城市ID
        
        // 记录经纬度处理次数，用于调试
        $validCityCount = 0;
        $invalidFormatCount = 0;
        
        // 遍历所有城市，找到最近的
        foreach ($cityMap as $cityId => $cityInfo) {
            // 确保经纬度字段存在且非空
            if (empty($cityInfo['lat']) || empty($cityInfo['lon'])) {
                $invalidFormatCount++;
                continue;
            }
            
            // 尝试获取经纬度，处理可能的格式问题
            $cityLat = null;
            $cityLon = null;
            
            // 检查经纬度格式并尝试标准化
            if (is_numeric($cityInfo['lat']) && is_numeric($cityInfo['lon'])) {
                // 标准格式
            $cityLat = (float)$cityInfo['lat'];
            $cityLon = (float)$cityInfo['lon'];
                
                // 简单验证经纬度范围
                if ($cityLat < -90 || $cityLat > 90 || $cityLon < -180 || $cityLon > 180) {
                    // 可能经纬度被交换了
                    if ($cityLon >= -90 && $cityLon <= 90 && $cityLat >= -180 && $cityLat <= 180) {
                        // 交换经纬度
                        $temp = $cityLat;
                        $cityLat = $cityLon;
                        $cityLon = $temp;
                    } else {
                        $invalidFormatCount++;
                        continue; // 跳过无效的经纬度
                    }
                }
            } else {
                $invalidFormatCount++;
                continue; // 跳过非数字的经纬度
            }
            
            $validCityCount++;
            
            // 使用球面距离计算（Haversine公式），考虑地球曲率
            $earthRadius = 6371; // 地球半径，单位公里
            
            // 将经纬度转换为弧度
            $latFrom = deg2rad($lat);
            $lonFrom = deg2rad($lon);
            $latTo = deg2rad($cityLat);
            $lonTo = deg2rad($cityLon);
            
            // Haversine公式
            $latDelta = $latTo - $latFrom;
            $lonDelta = $lonTo - $lonFrom;
            
            $a = sin($latDelta / 2) * sin($latDelta / 2) +
                 cos($latFrom) * cos($latTo) * 
                 sin($lonDelta / 2) * sin($lonDelta / 2);
            $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
            $distance = $earthRadius * $c;
            
            if ($distance < $minDistance) {
                $minDistance = $distance;
                $closestCityId = $cityId;
            }
        }
        
        // 如果没有找到任何有效城市，使用常用城市列表中地理中心位置最近的城市
        if ($closestCityId === null) {
            error_log("城市工具 - 没有找到有效城市，尝试使用常用城市列表");
            
            $commonCities = [
                '101010100' => ['name' => '北京', 'lat' => 39.9050, 'lon' => 116.4053],
                '101020100' => ['name' => '上海', 'lat' => 31.2317, 'lon' => 121.4726],
                '101280101' => ['name' => '广州', 'lat' => 23.1291, 'lon' => 113.2644],
                '101280601' => ['name' => '深圳', 'lat' => 22.5431, 'lon' => 114.0579],
                '101210101' => ['name' => '杭州', 'lat' => 30.2236, 'lon' => 120.1469],
                '101190101' => ['name' => '南京', 'lat' => 32.0584, 'lon' => 118.7965],
                '101030100' => ['name' => '天津', 'lat' => 39.0851, 'lon' => 117.1990],
                '101200101' => ['name' => '武汉', 'lat' => 30.5928, 'lon' => 114.3055],
                '101110101' => ['name' => '西安', 'lat' => 34.3416, 'lon' => 108.9398],
                '101270101' => ['name' => '成都', 'lat' => 30.6570, 'lon' => 104.0665],
                '101040100' => ['name' => '重庆', 'lat' => 29.5628, 'lon' => 106.5528]
            ];
            
            foreach ($commonCities as $cityId => $cityInfo) {
                $cityLat = $cityInfo['lat'];
                $cityLon = $cityInfo['lon'];
                
                // 使用Haversine公式计算距离
                $earthRadius = 6371;
                $latFrom = deg2rad($lat);
                $lonFrom = deg2rad($lon);
                $latTo = deg2rad($cityLat);
                $lonTo = deg2rad($cityLon);
                
                $latDelta = $latTo - $latFrom;
                $lonDelta = $lonTo - $lonFrom;
                
                $a = sin($latDelta / 2) * sin($latDelta / 2) +
                     cos($latFrom) * cos($latTo) * 
                     sin($lonDelta / 2) * sin($lonDelta / 2);
                $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
                $distance = $earthRadius * $c;
                
                if ($distance < $minDistance) {
                    $minDistance = $distance;
                    $closestCityId = $cityId;
                }
            }
        }
        
        // 安全检查：如果仍然没有找到城市，返回北京作为最后的备选
        if ($closestCityId === null) {
            error_log("城市工具 - 严重错误：无法确定最近城市，使用北京作为备选");
            $closestCityId = '101010100'; // 北京
        }
        
        error_log("城市工具 - 找到最近的城市ID: ({$lat}, {$lon}) => {$closestCityId}, 距离: {$minDistance}公里, 有效城市数: {$validCityCount}, 跳过无效格式: {$invalidFormatCount}");
        
        // 获取城市信息并记录
        $closestCity = isset($cityMap[$closestCityId]) ? $cityMap[$closestCityId] : null;
        if ($closestCity) {
            error_log("城市工具 - 最近的城市: " . $closestCity['name'] . 
                     (isset($closestCity['adm1']) ? ", " . $closestCity['adm1'] : "") . 
                     (isset($closestCity['adm2']) ? ", " . $closestCity['adm2'] : ""));
        }
        
        return $closestCityId;
    }

    /**
     * 获取城市信息
     * 
     * @param string $cityId 城市ID
     * @return array|null 城市信息，如果未找到则返回null
     */
    function getCityInfoById($cityId) {
        $cityMap = loadCityMapFromJson();
        
        if (isset($cityMap[$cityId])) {
            return $cityMap[$cityId];
        }
        
        error_log("城市工具 - 未找到城市ID: $cityId");
        return null;
    }
}
?> 