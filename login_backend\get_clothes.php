<?php
/**
 * Get Clothes API
 * 
 * Retrieves a user's clothes based on their authorization token
 * Supports optional category filtering
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * GET Parameters:
 * - category (optional): Filter by category (tops, pants, skirts, etc.)
 * - id (optional): Filter by specific clothing item ID
 * - wardrobe_id (optional): Filter by wardrobe ID
 * - merchant_id (optional): Filter by merchant ID
 * - all_clothes (optional): Set to 1 to return all clothes without filtering by category
 * 
 * Response:
 * {
 *   "error": false,
 *   "data": [
 *     {
 *       "id": 1,
 *       "name": "Black T-shirt",
 *       "category": "tops",
 *       "image_url": "https://example.com/image.jpg",
 *       "description": "Cotton black t-shirt",
 *       "created_at": "2023-03-31 12:00:00"
 *     },
 *     ...
 *   ]
 * }
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// Set response content type
header('Content-Type: application/json');

// Handle CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if Authorization header exists
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// 处理Bearer前缀，与其他API保持一致
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7); // 去除 "Bearer " 前缀
}

// Verify token
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid or expired token'
    ]);
    exit;
}

// Get user ID from token data
$userId = $tokenData['sub'];

// Get optional category filter and ID filter
$category = isset($_GET['category']) ? $_GET['category'] : null;
$clothingId = isset($_GET['id']) ? $_GET['id'] : null;
$wardrobeId = isset($_GET['wardrobe_id']) ? intval($_GET['wardrobe_id']) : null;
// 如果wardrobeId为0，视为不筛选
if ($wardrobeId === 0) {
    $wardrobeId = null;
}
// 添加商家ID参数支持
$merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : null;
// 添加获取所有衣物参数支持
$allClothes = isset($_GET['all_clothes']) && $_GET['all_clothes'] == '1';

// 新增：圈子数据相关参数（向后兼容，默认值保持原有行为）
$includeCircleData = isset($_GET['include_circle_data']) ? $_GET['include_circle_data'] === 'true' : false;
$dataSource = isset($_GET['data_source']) ? $_GET['data_source'] : 'personal'; // personal, shared, all

// 记录请求参数，便于调试
$requestParams = [
    'category' => $category,
    'id' => $clothingId,
    'wardrobe_id' => $wardrobeId,
    'merchant_id' => $merchantId,
    'all_clothes' => $allClothes ? '1' : '0',
    'include_circle_data' => $includeCircleData ? 'true' : 'false',
    'data_source' => $dataSource,
];

// 添加详细的调试日志
error_log("衣物API请求 - 用户ID: $userId, 参数: " . json_encode($requestParams));
error_log("wardrobeId处理: 原始值=" . ($_GET['wardrobe_id'] ?? 'null') . ", 处理后值=" . ($wardrobeId ?? 'null'));
error_log("GET_CLOTHES API请求参数: " . json_encode($requestParams));

// 记录商家模式
$isMerchantMode = $merchantId && $merchantId > 0;
error_log("API模式: " . ($isMerchantMode ? "商家模式，商家ID=$merchantId" : "普通模式"));

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 商家模式下验证商家状态
    if ($isMerchantMode) {
        try {
            // 检查商家是否存在且已入驻
            $merchantCheckSql = "SELECT id FROM users WHERE id = :merchant_id AND merchant_status = 'yes'";
            $merchantCheckStmt = $conn->prepare($merchantCheckSql);
            $merchantCheckStmt->execute(['merchant_id' => $merchantId]);
            if ($merchantCheckStmt->rowCount() === 0) {
                error_log("商家不存在或未入驻: merchant_id=$merchantId");
                echo json_encode([
                    'error' => true,
                    'msg' => '商家不存在或未入驻'
                ]);
                exit;
            }
        } catch (PDOException $e) {
            error_log("验证商家状态时出错: Code=" . $e->getCode() . ", Message=" . $e->getMessage());
        }
    }

    // 记录API调用信息
    if ($clothingId) {
        error_log("获取单个衣物详情: " . 
                  ($isMerchantMode ? "商家ID=$merchantId" : "用户ID=$userId") . 
                  ", 衣物ID=" . ($clothingId ?? 'null'));
    } else {
        $logMsg = "获取衣物列表: " . 
                  ($isMerchantMode ? "商家ID=$merchantId" : "用户ID=$userId");
        if ($category && $category !== 'all') {
            $logMsg .= ", 分类=$category";
        }
        if ($wardrobeId) {
            $logMsg .= ", 衣橱ID=$wardrobeId";
        }
        error_log($logMsg);
    }

    // 确定查询的用户ID
    $queryUserId = $isMerchantMode ? $merchantId : $userId;

    // 构建基础查询（向后兼容）
    if ($includeCircleData && !$isMerchantMode) {
        // 新功能：包含圈子数据的查询
        $sql = "SELECT c.id, c.name, c.category, c.image_url, c.tags, c.description, c.created_at, c.wardrobe_id, c.sort_order,
                       c.circle_id, c.user_id, u.nickname as creator_nickname,
                       CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                FROM clothes c
                LEFT JOIN users u ON c.user_id = u.id
                WHERE ";

        // 修复：如果查询特定ID，优先检查衣物所有权
        if ($clothingId) {
            // 首先检查是否为用户自己的衣物（不管circle_id状态）
            $sql .= "c.id = :clothing_id AND c.user_id = :user_id";
            $params = ['clothing_id' => $clothingId, 'user_id' => $queryUserId];
            error_log("查询特定衣物ID=" . ($clothingId ?? 'null') . "，优先检查所有权，用户ID: $queryUserId");
        } else {
            // 列表查询时根据数据源参数构建WHERE条件
            if ($dataSource === 'personal') {
                $sql .= "c.user_id = :user_id AND c.circle_id IS NULL";
                $params = ['user_id' => $queryUserId];
            } elseif ($dataSource === 'shared') {
                // 查询用户所在圈子的共享数据（排除用户自己的数据，且数据创建者仍在圈子中）
                $sql .= "c.circle_id IS NOT NULL AND c.user_id != :user_id
                         AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
                         AND c.user_id IN (SELECT user_id FROM circle_members WHERE circle_id = c.circle_id AND status = 'active')";
                $params = ['user_id' => $queryUserId];

                // 添加调试日志
                error_log("查询共享数据（排除自己，且创建者仍在圈子中）- 用户ID: $queryUserId");
            } else { // $dataSource === 'all'
                // 查询个人数据 + 圈子共享数据（且数据创建者仍在圈子中）
                $sql .= "((c.user_id = :user_id AND c.circle_id IS NULL) OR
                         (c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
                          AND c.user_id IN (SELECT user_id FROM circle_members WHERE circle_id = c.circle_id AND status = 'active')))";
                $params = ['user_id' => $queryUserId];

                // 添加调试日志
                error_log("查询全部数据（个人+共享，且创建者仍在圈子中）- 用户ID: $queryUserId");
            }
        }
    } else {
        // 原有逻辑：只查询个人数据（保持向后兼容）
        $sql = "SELECT id, name, category, image_url, tags, description, created_at, wardrobe_id, sort_order FROM clothes WHERE user_id = :user_id";
        $params = ['user_id' => $queryUserId];
    }

    // Add ID filter if provided
    if ($clothingId) {
        if ($includeCircleData && !$isMerchantMode) {
            // 对于圈子数据查询，需要明确指定表别名
            $sql .= " AND c.id = :id";
        } else {
            // 对于个人数据查询，保持原有逻辑
            $sql .= " AND id = :id";
        }
        $params['id'] = $clothingId;
    }
    // Add category filter if provided and not in all_clothes mode
    if ($category && $category !== 'all' && !$allClothes) {
        if ($includeCircleData && !$isMerchantMode) {
            // 对于圈子数据查询，需要明确指定表别名
            $sql .= " AND (c.category = :category OR c.category_id IN (SELECT id FROM clothing_categories WHERE code = :category_code))";
        } else {
            // 支持新的category_id字段和旧的category字段
            $sql .= " AND (category = :category OR category_id IN (SELECT id FROM clothing_categories WHERE code = :category_code))";
        }
        $params['category'] = $category;
        $params['category_code'] = $category;
    }
    // Add wardrobe filter if provided
    if ($wardrobeId) {
        if ($includeCircleData && !$isMerchantMode) {
            // 对于圈子数据查询，需要明确指定表别名，并确保衣橱权限正确
            if ($dataSource === 'shared') {
                // 只查询圈子共享衣橱中的衣物
                $sql .= " AND c.wardrobe_id = :wardrobe_id AND c.circle_id IS NOT NULL";
            } elseif ($dataSource === 'all') {
                // 查询指定衣橱中的衣物，包括个人和共享的
                // 基础WHERE条件已经包含了用户权限检查，这里只需要添加衣橱筛选
                $sql .= " AND c.wardrobe_id = :wardrobe_id";
            } else {
                // personal模式，只查询个人衣橱
                $sql .= " AND c.wardrobe_id = :wardrobe_id AND c.circle_id IS NULL";
            }
        } else {
            $sql .= " AND wardrobe_id = :wardrobe_id";
        }
        $params['wardrobe_id'] = $wardrobeId;

        // 添加调试日志
        error_log("衣橱筛选 - 衣橱ID: $wardrobeId, 数据源: $dataSource, 包含圈子数据: " . ($includeCircleData ? 'true' : 'false'));
    }

    $sql .= " ORDER BY created_at DESC";
    
    // 记录SQL查询信息
    error_log("SQL查询: $sql");
    error_log("参数: " . json_encode($params));

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);

    $clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 记录结果
    $count = count($clothes);
    if ($count > 0) {
        error_log("第一次查询成功: 从用户自己(ID={$userId})的衣物中找到 {$count} 件衣物");
    } else {
        error_log("第一次查询失败: 用户(ID={$userId})不拥有衣物ID=" . ($clothingId ?? 'null'));
    }
    
    // 如果未找到衣物且是查询特定ID，尝试圈子权限查询（仅当包含圈子数据时）
    if ($count === 0 && $clothingId && $includeCircleData) {
        error_log("用户自己的衣物查询失败，尝试圈子权限查询");

        // 构建圈子权限查询（确保数据创建者仍在圈子中）
        $circleSql = "SELECT c.id, c.name, c.category, c.image_url, c.tags, c.description, c.created_at, c.wardrobe_id, c.sort_order,
                             c.circle_id, c.user_id, u.nickname as creator_nickname,
                             CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                      FROM clothes c
                      LEFT JOIN users u ON c.user_id = u.id
                      WHERE c.id = :clothing_id AND c.circle_id IS NOT NULL
                            AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
                            AND c.user_id IN (SELECT user_id FROM circle_members WHERE circle_id = c.circle_id AND status = 'active')";

        $circleParams = ['clothing_id' => $clothingId, 'user_id' => $queryUserId];

        error_log("圈子权限查询SQL: $circleSql");
        error_log("圈子权限查询参数: " . json_encode($circleParams));

        $circleStmt = $conn->prepare($circleSql);
        $circleStmt->execute($circleParams);

        $circleClothes = $circleStmt->fetchAll(PDO::FETCH_ASSOC);
        $circleCount = count($circleClothes);

        if ($circleCount > 0) {
            error_log("圈子权限查询成功: 找到 {$circleCount} 件衣物");
            $clothes = $circleClothes;
            $count = $circleCount;
        } else {
            error_log("圈子权限查询也失败: 用户无权访问衣物ID=" . $clothingId);
        }
    }

    // 如果仍未找到衣物并且不是商家模式，则检查是否有merchant_id参数，尝试以商家模式重新查询
    if ($count === 0 && !$isMerchantMode && $clothingId) {
        $urlMerchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : null;
        
        if ($urlMerchantId && $urlMerchantId > 0) {
            error_log("在用户自己的衣物中未找到ID=" . ($clothingId ?? 'null') . "的衣物，尝试从商家(ID={$urlMerchantId})查询");
            
            // 检查商家是否存在且已入驻
            try {
                $merchantCheckSql = "SELECT id FROM users WHERE id = :merchant_id AND merchant_status = 'yes'";
                $merchantCheckStmt = $conn->prepare($merchantCheckSql);
                $merchantCheckStmt->execute(['merchant_id' => $urlMerchantId]);
                
                if ($merchantCheckStmt->rowCount() > 0) {
                    // 商家存在，尝试查询商家的衣物
                    $merchantSql = "SELECT id, name, category, image_url, tags, description, created_at, wardrobe_id, sort_order FROM clothes WHERE id = :id AND user_id = :merchant_id";
                    $merchantStmt = $conn->prepare($merchantSql);
                    $merchantStmt->execute([
                        'id' => $clothingId,
                        'merchant_id' => $urlMerchantId
                    ]);
                    
                    $merchantClothes = $merchantStmt->fetchAll(PDO::FETCH_ASSOC);
                    $merchantClothesCount = count($merchantClothes);
                    
                    error_log("第二次查询成功: 从URL参数指定的商家(ID={$urlMerchantId})中找到衣物");
                    
                    if ($merchantClothesCount > 0) {
                        // 查询商家信息
                        $merchantInfoSql = "SELECT nickname, avatar_url, share_try_on_credits FROM users WHERE id = :merchant_id";
                        $merchantInfoStmt = $conn->prepare($merchantInfoSql);
                        $merchantInfoStmt->execute(['merchant_id' => $urlMerchantId]);
                        $merchantInfo = $merchantInfoStmt->fetch(PDO::FETCH_ASSOC);
                        
                        // 将商家信息添加到衣物数据
                        foreach ($merchantClothes as &$item) {
                            $item['merchant'] = [
                                'id' => $urlMerchantId,
                                'nickname' => $merchantInfo ? $merchantInfo['nickname'] : '商家',
                                'avatar_url' => $merchantInfo ? $merchantInfo['avatar_url'] : '',
                                'share_try_on_credits' => $merchantInfo ? (int)$merchantInfo['share_try_on_credits'] : 0
                            ];
                            
                            // 添加标识字段，表明这是商家衣物
                            $item['is_merchant_clothes'] = true;
                        }
                        
                        // 使用商家衣物结果替换原结果
                        $clothes = $merchantClothes;
                        $count = $merchantClothesCount;
                        error_log("第二次查询成功: 从URL参数指定的商家(ID={$urlMerchantId})中找到衣物");
                    }
                } else {
                    error_log("第二次查询失败: 商家不存在或未入驻: merchant_id={$urlMerchantId}");
                }
            } catch (PDOException $e) {
                error_log("二次查询商家衣物出错: " . $e->getMessage());
            }
        }
    }
    
    // 如果经过上面两次查询还是没有找到衣物，尝试第三次查询: 从所有已入驻商家中查找该衣物
    if ($count === 0 && $clothingId) {
        error_log("第三次查询: 尝试从所有商家中查找衣物ID=" . ($clothingId ?? 'null'));
        
        try {
            // 首先查询拥有该衣物ID的商家
            $anyMerchantSql = "
                SELECT c.*, u.id as merchant_id, u.nickname, u.avatar_url, u.share_try_on_credits
                FROM clothes c 
                JOIN users u ON c.user_id = u.id 
                WHERE c.id = :clothing_id 
                AND u.merchant_status = 'yes'
                LIMIT 1
            ";
            $anyMerchantStmt = $conn->prepare($anyMerchantSql);
            $anyMerchantStmt->execute(['clothing_id' => $clothingId]);
            
            $merchantClothesWithInfo = $anyMerchantStmt->fetchAll(PDO::FETCH_ASSOC);
            $anyMerchantCount = count($merchantClothesWithInfo);
            
            error_log("第三次查询结果: 从所有商家中找到 {$anyMerchantCount} 件衣物");
            
            if ($anyMerchantCount > 0) {
                // 处理结果，为每件衣物添加商家信息
                foreach ($merchantClothesWithInfo as &$item) {
                    $merchantId = $item['merchant_id'];
                    
                    // 添加商家信息到衣物数据
                    $item['merchant'] = [
                        'id' => $merchantId,
                        'nickname' => $item['nickname'],
                        'avatar_url' => $item['avatar_url'],
                        'share_try_on_credits' => (int)$item['share_try_on_credits']
                    ];
                    
                    // 添加标识字段，表明这是商家衣物
                    $item['is_merchant_clothes'] = true;
                    
                    // 移除商家相关字段，避免重复
                    unset($item['merchant_id']);
                    unset($item['nickname']);
                    unset($item['avatar_url']);
                    unset($item['share_try_on_credits']);
                }
                
                // 使用找到的商家衣物替换原结果
                $clothes = $merchantClothesWithInfo;
                $count = $anyMerchantCount;
                error_log("成功从任意商家获取衣物数据，商家ID: " . $clothes[0]['merchant']['id']);
            } else {
                error_log("在所有商家中均未找到该衣物ID=" . ($clothingId ?? 'null'));
            }
        } catch (PDOException $e) {
            error_log("第三次查询出错: " . $e->getMessage());
        }
    }
    
    // 增强衣物数据 - 添加额外商家信息（如果是商家模式）
    if ($isMerchantMode && $clothingId && $count > 0) {
        try {
            // 获取商家信息
            $merchantSql = "SELECT nickname, avatar_url, share_try_on_credits FROM users WHERE id = :merchant_id";
            $merchantStmt = $conn->prepare($merchantSql);
            $merchantStmt->execute(['merchant_id' => $merchantId]);
            $merchantInfo = $merchantStmt->fetch(PDO::FETCH_ASSOC);
            
            if ($merchantInfo) {
                // 将商家信息添加到衣物数据中
                foreach ($clothes as &$item) {
                    $item['merchant'] = [
                        'id' => $merchantId,
                        'nickname' => $merchantInfo['nickname'],
                        'avatar_url' => $merchantInfo['avatar_url'],
                        'share_try_on_credits' => (int)$merchantInfo['share_try_on_credits']
                    ];
                }
                error_log("已将商家信息添加到衣物数据中");
            }
        } catch (PDOException $e) {
            error_log("获取商家信息失败: " . $e->getMessage());
        }
    }

    // 为向后兼容，如果没有包含圈子数据，需要为每个衣物添加数据源标识
    if (!$includeCircleData) {
        foreach ($clothes as &$item) {
            $item['data_source'] = 'personal';
            $item['creator_nickname'] = null; // 个人数据不需要创建者昵称
        }
    }

    // Return clothes data
    echo json_encode([
        'error' => false,
        'data' => $clothes,
        'meta' => [
            'include_circle_data' => $includeCircleData,
            'data_source' => $dataSource,
            'total_count' => count($clothes)
        ]
    ]);

} catch (PDOException $e) {
    // 详细记录错误信息
    $errorMessage = $e->getMessage();
    $errorCode = $e->getCode();
    error_log("获取衣物列表错误: Code=$errorCode, Message=$errorMessage");

    // Return error response
    echo json_encode([
        'error' => true,
        'msg' => "获取衣物失败: $errorCode - " . substr($errorMessage, 0, 100)
    ]);
}
?> 