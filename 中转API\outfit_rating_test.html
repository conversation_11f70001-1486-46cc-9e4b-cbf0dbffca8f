<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>穿搭打分API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 0;
            cursor: pointer;
            border-radius: 4px;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .preview {
            margin: 20px 0;
            text-align: center;
        }
        #imagePreview {
            max-width: 100%;
            max-height: 400px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
            display: none;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .rating-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: none;
        }
        .score-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        .score-item {
            text-align: center;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .score-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
        }
        .score-label {
            font-size: 0.9rem;
            color: #7f8c8d;
        }
        .section-title {
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
            margin-top: 15px;
            color: #34495e;
        }
    </style>
</head>
<body>
    <h1>穿搭打分API测试页面</h1>
    
    <div class="form-group">
        <label for="imageFile">选择穿搭照片:</label>
        <input type="file" id="imageFile" accept="image/*" onchange="previewImage(this)">
    </div>
    
    <div class="preview">
        <img id="imagePreview" style="display:none;" alt="Image Preview">
    </div>
    
    <div class="form-group">
        <label for="genderSelect">选择性别:</label>
        <select id="genderSelect">
            <option value="1">男性</option>
            <option value="2">女性</option>
        </select>
    </div>
    
    <button id="submitBtn" onclick="submitImage()" disabled>获取穿搭评分</button>
    
    <div id="loader" class="loader"></div>
    
    <div id="statusMessage"></div>
    
    <div id="ratingCard" class="rating-card">
        <h2>穿搭评分结果</h2>
        
        <div class="score-grid">
            <div class="score-item">
                <div class="score-value" id="overallScore">-</div>
                <div class="score-label">总体评分</div>
            </div>
            <div class="score-item">
                <div class="score-value" id="coordinationScore">-</div>
                <div class="score-label">协调性</div>
            </div>
            <div class="score-item">
                <div class="score-value" id="occasionScore">-</div>
                <div class="score-label">场合适宜性</div>
            </div>
            <div class="score-item">
                <div class="score-value" id="styleMatchScore">-</div>
                <div class="score-label">气质匹配</div>
            </div>
            <div class="score-item">
                <div class="score-value" id="fashionScore">-</div>
                <div class="score-label">时尚度</div>
            </div>
            <div class="score-item">
                <div class="score-value" id="detailScore">-</div>
                <div class="score-label">细节处理</div>
            </div>
        </div>
        
        <h3 class="section-title">穿搭分析</h3>
        <div id="outfitAnalysis"></div>
        
        <h3 class="section-title">优点</h3>
        <div id="strength"></div>
        
        <h3 class="section-title">改进建议</h3>
        <div id="improvement"></div>
        
        <h3 class="section-title">场合推荐</h3>
        <div id="occasionRecommendation"></div>
    </div>
    
    <h3>API响应原始数据：</h3>
    <pre id="responseJson">尚未发送请求</pre>
    
    <script>
        function previewImage(input) {
            const preview = document.getElementById('imagePreview');
            const submitBtn = document.getElementById('submitBtn');
            
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                    submitBtn.disabled = false; // 启用提交按钮
                };
                
                reader.readAsDataURL(input.files[0]);
            }
        }
        
        function submitImage() {
            // 获取图片
            const imageFile = document.getElementById('imageFile').files[0];
            if (!imageFile) {
                setStatus('请选择一张照片', 'error');
                return;
            }
            
            // 获取性别
            const gender = document.getElementById('genderSelect').value;
            
            // 转换为base64
            const reader = new FileReader();
            reader.onload = function(e) {
                const base64Image = e.target.result.split(',')[1]; // 去掉前缀
                
                // 显示加载中
                document.getElementById('loader').style.display = 'block';
                setStatus('正在获取穿搭评分...', '');
                document.getElementById('submitBtn').disabled = true;
                
                // 准备API请求数据
                const requestData = {
                    image_base64: base64Image,
                    user_info: {
                        gender: parseInt(gender)
                    }
                };
                
                // 发送请求到中转API
                fetch('outfit_rating_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => response.json())
                .then(data => {
                    // 隐藏加载中
                    document.getElementById('loader').style.display = 'none';
                    document.getElementById('submitBtn').disabled = false;
                    
                    // 显示原始响应
                    document.getElementById('responseJson').textContent = JSON.stringify(data, null, 2);
                    
                    if (data.error) {
                        setStatus('错误: ' + data.msg, 'error');
                        return;
                    }
                    
                    // 显示评分结果
                    setStatus('获取穿搭评分成功!', 'success');
                    displayRatingResults(data.data);
                })
                .catch(error => {
                    document.getElementById('loader').style.display = 'none';
                    document.getElementById('submitBtn').disabled = false;
                    setStatus('请求失败: ' + error.message, 'error');
                    console.error('请求失败:', error);
                });
            };
            
            reader.readAsDataURL(imageFile);
        }
        
        function setStatus(message, type) {
            const statusElement = document.getElementById('statusMessage');
            statusElement.textContent = message;
            statusElement.className = type;
        }
        
        function displayRatingResults(rating) {
            // 显示评分卡
            document.getElementById('ratingCard').style.display = 'block';
            
            // 设置各项评分
            document.getElementById('overallScore').textContent = rating.overall_score;
            document.getElementById('coordinationScore').textContent = rating.coordination_score;
            document.getElementById('occasionScore').textContent = rating.occasion_score;
            document.getElementById('styleMatchScore').textContent = rating.style_match_score;
            document.getElementById('fashionScore').textContent = rating.fashion_score;
            document.getElementById('detailScore').textContent = rating.detail_score;
            
            // 设置文字内容
            document.getElementById('outfitAnalysis').textContent = rating.outfit_analysis;
            document.getElementById('strength').textContent = rating.strength;
            document.getElementById('improvement').textContent = rating.improvement;
            document.getElementById('occasionRecommendation').textContent = rating.occasion_recommendation;
        }
    </script>
</body>
</html>