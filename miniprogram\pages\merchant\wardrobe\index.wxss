.container {
  padding: 0;
  background-color: #fff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 商家信息 */
.merchant-info {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.merchant-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f0f0f0;
}

.merchant-details {
  flex: 1;
}

.merchant-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.status-tag {
  display: inline-block;
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 5rpx 15rpx;
  border-radius: 30rpx;
}

.status-share {
  color: #25c5a0;
  background-color: rgba(37, 197, 160, 0.1);
}

/* 衣橱选择器 */
.wardrobe-switcher {
  background-color: #f9f9f9;
  padding: 12px 15px;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  display: flex;
  align-items: center;
}

.wardrobe-scroll {
  white-space: nowrap;
  flex: 1;
  overflow-x: auto;
}

.wardrobe-option {
  display: inline-block;
  padding: 8px 16px;
  margin-right: 8px;
  border-radius: 16px;
  font-size: 14px;
  color: #333;
  background-color: #fff;
  border: 1px solid rgba(0,0,0,0.1);
  transition: all 0.3s;
}

.wardrobe-option.active {
  background-color: #000;
  color: #fff;
  border-color: #000;
}

/* 分类选项卡容器 */
.category-tabs-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  background-color: #fff;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.category-tabs {
  display: flex;
  background-color: #fff;
  padding: 12px 15px;
  overflow-x: auto;
  white-space: nowrap;
  align-items: center;
}

.tab-item {
  padding: 8px 16px;
  margin-right: 8px;
  border-radius: 16px;
  font-size: 13px;
  white-space: nowrap;
  color: #666;
  background-color: #f5f5f5;
  display: inline-block;
}

.tab-item.active {
  background-color: #000;
  color: #fff;
}

/* 衣物容器 */
.clothes-container {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  padding-bottom: 5px; /* 为底部导航留出空间 */
}

.clothes-grid {
  display: grid;
  gap: 15px;
}

.clothes-grid.layout-mode-2 {
  grid-template-columns: repeat(2, 1fr);
}

.clothes-grid.layout-mode-3 {
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.clothes-grid.layout-mode-4 {
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.clothes-item {
  aspect-ratio: 3/4;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  box-sizing: border-box;
}

.layout-mode-3 .clothes-item {
  aspect-ratio: 2/3;
}

.layout-mode-4 .clothes-item {
  aspect-ratio: 1/1.5;
}

.clothing-image {
  width: 90%;
  height: 90%;
  object-fit: contain;
  border-radius: 8px;
}

/* 四列布局的特殊样式 */
.layout-mode-4 .clothing-image {
  width: 85%;
  height: 85%;
}

/* 试衣按钮 */
.try-on-btn {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  font-size: 9px;
  padding: 3px 6px;
  border-radius: 10px;
  z-index: 10;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  margin-top: 30px;
}

.empty-icon {
  width: 150rpx;
  height: 150rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
}

/* 加载中 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 0;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.loading-more {
  padding: 20rpx 0;
  text-align: center;
  width: 100%;
}

.loading-more-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.loading-more-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.loading-more-text {
  font-size: 24rpx;
  color: #999;
}

/* 没有更多数据 */
.no-more-data {
  text-align: center;
  padding: 20rpx 0 40rpx;
  width: 100%;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
  display: inline-block;
  position: relative;
}

.no-more-text::before,
.no-more-text::after {
  content: "";
  position: absolute;
  top: 50%;
  width: 60rpx;
  height: 1px;
  background: #eee;
}

.no-more-text::before {
  left: -80rpx;
}

.no-more-text::after {
  right: -80rpx;
}

.system-tag {
  font-size: 10px;
  color: #999;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 4px;
  border-radius: 4px;
  margin-left: 4px;
} 