<?php
/**
 * 创建个人形象分析记录API
 * 接收用户提交的身材数据和照片信息，创建新的分析记录
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';
require_once 'wx_pay_helper.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => 'Method Not Allowed']);
    exit;
}

// 检查是否有Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => 'Authorization header is required']);
    exit;
}

// 验证token
$token = $_SERVER['HTTP_AUTHORIZATION'];
$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => 'Invalid or expired token']);
    exit;
}

// 获取用户ID
$userId = $payload['sub'];

// 获取请求数据
$rawData = file_get_contents('php://input');
$requestData = json_decode($rawData, true);

// 验证必要字段
$requiredFields = ['height', 'weight'];
foreach ($requiredFields as $field) {
    if (!isset($requestData[$field]) || empty($requestData[$field])) {
        http_response_code(400);
        echo json_encode(['error' => true, 'msg' => "缺少必要字段: $field"]);
        exit;
    }
}

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

try {
    // 开始事务
    $conn->beginTransaction();
    
    // 准备插入数据
    $stmt = $conn->prepare("
        INSERT INTO user_image_analysis (
            user_id, height, weight, bust, waist, hips, shoulder_width,
            skin_tone, face_shape, body_shape, gender, photo_urls, remarks,
            status, payment_status, amount, created_at
        ) VALUES (
            :user_id, :height, :weight, :bust, :waist, :hips, :shoulder_width,
            :skin_tone, :face_shape, :body_shape, :gender, :photo_urls, :remarks,
            'pending', 'unpaid', 9.90, NOW()
        )
    ");
    
    // 处理照片URL
    $photoUrls = isset($requestData['photo_urls']) ? $requestData['photo_urls'] : [];
    $photoUrlsJson = json_encode($photoUrls);
    
    // 准备参数值（避免通过引用传递表达式结果）
    $height = $requestData['height'];
    $weight = $requestData['weight'];
    $bust = isset($requestData['bust']) ? $requestData['bust'] : null;
    $waist = isset($requestData['waist']) ? $requestData['waist'] : null;
    $hips = isset($requestData['hips']) ? $requestData['hips'] : null;
    $shoulderWidth = isset($requestData['shoulder_width']) ? $requestData['shoulder_width'] : null;
    $skinTone = isset($requestData['skin_tone']) ? $requestData['skin_tone'] : null;
    $faceShape = isset($requestData['face_shape']) ? $requestData['face_shape'] : null;
    $bodyShape = isset($requestData['body_shape']) ? $requestData['body_shape'] : null;
    $gender = isset($requestData['gender']) ? $requestData['gender'] : null;
    $remarks = isset($requestData['remarks']) ? $requestData['remarks'] : null;
    
    // 绑定参数
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':height', $height, PDO::PARAM_INT);
    $stmt->bindParam(':weight', $weight, PDO::PARAM_INT);
    $stmt->bindParam(':bust', $bust, PDO::PARAM_INT);
    $stmt->bindParam(':waist', $waist, PDO::PARAM_INT);
    $stmt->bindParam(':hips', $hips, PDO::PARAM_INT);
    $stmt->bindParam(':shoulder_width', $shoulderWidth, PDO::PARAM_INT);
    $stmt->bindParam(':skin_tone', $skinTone, PDO::PARAM_STR);
    $stmt->bindParam(':face_shape', $faceShape, PDO::PARAM_STR);
    $stmt->bindParam(':body_shape', $bodyShape, PDO::PARAM_STR);
    $stmt->bindParam(':gender', $gender, PDO::PARAM_INT);
    $stmt->bindParam(':photo_urls', $photoUrlsJson, PDO::PARAM_STR);
    $stmt->bindParam(':remarks', $remarks, PDO::PARAM_STR);
    
    // 执行插入
    $stmt->execute();
    
    // 获取新插入的分析ID
    $analysisId = $conn->lastInsertId();
    
    // 创建支付订单
    $wxPayHelper = new WxPayHelper();
    
    // 获取用户昵称
    $userStmt = $conn->prepare("SELECT nickname FROM users WHERE id = :user_id");
    $userStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $userStmt->execute();
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);
    $userNickname = $user['nickname'] ?? '用户';
    
    // 获取用户IP
    $userIp = $_SERVER['REMOTE_ADDR'];
    
    // 创建固定价格的支付订单（9.9元）
    // 定义一个特殊的套餐ID，用于个人形象分析服务
    $imageAnalysisPackageId = 99; // 假设ID 99是为形象分析预留的套餐ID
    
    // 使用WxPayHelper创建订单
    $orderResult = $wxPayHelper->createOrder($userId, $userNickname, $imageAnalysisPackageId, $userIp);
    
    if (isset($orderResult['error']) && $orderResult['error']) {
        throw new Exception('创建支付订单失败: ' . ($orderResult['msg'] ?? '未知错误'));
    }
    
    // 获取订单信息
    $outTradeNo = $orderResult['order_id'];
    $payParams = $orderResult['pay_params'];
    
    // 更新订单号
    $updateOrderStmt = $conn->prepare("
        UPDATE user_image_analysis 
        SET order_id = :order_id 
        WHERE id = :analysis_id
    ");
    $updateOrderStmt->bindParam(':order_id', $outTradeNo, PDO::PARAM_STR);
    $updateOrderStmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
    $updateOrderStmt->execute();
    
    // 提交事务
    $conn->commit();
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'msg' => '创建形象分析记录成功',
        'data' => [
            'analysis_id' => $analysisId,
            'order_id' => $outTradeNo,
            'amount' => 9.90,
            'pay_params' => $payParams
        ]
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    $conn->rollBack();
    
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '创建形象分析记录失败: ' . $e->getMessage()]);
} 