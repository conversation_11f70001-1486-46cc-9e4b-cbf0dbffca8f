<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 日志函数
function logDebug($message, $data = null) {
    $log_file = __DIR__ . '/outfit_rating_api_debug.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] {$message}";
    
    if ($data !== null) {
        if (is_array($data) && isset($data['image_base64'])) {
            // 避免记录整个图片base64到日志
            $data['image_base64'] = substr($data['image_base64'], 0, 100) . '... [truncated]';
        }
        $log_message .= ': ' . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    
    file_put_contents($log_file, $log_message . PHP_EOL, FILE_APPEND);
}

logDebug("接收到新的穿搭打分API请求", ['method' => $_SERVER['REQUEST_METHOD']]);

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    logDebug("OPTIONS预检请求，直接返回");
    exit;
}

// 检查请求数据
$json = file_get_contents('php://input');
if (empty($json)) {
    logDebug("错误: 缺少请求数据");
    echo json_encode(['error' => true, 'msg' => '缺少请求数据']);
    exit;
}

$data = json_decode($json, true);
if (!$data) {
    logDebug("错误: 无效的JSON数据", ['raw_input' => substr($json, 0, 1000)]);
    echo json_encode(['error' => true, 'msg' => '无效的JSON数据']);
    exit;
}

// 验证必要参数
if (!isset($data['image_base64'])) {
    logDebug("错误: 缺少必要参数 image_base64");
    echo json_encode(['error' => true, 'msg' => '缺少图片数据']);
    exit;
}

// 获取用户信息（如果有）
$user_info = isset($data['user_info']) ? $data['user_info'] : null;
$gender = isset($user_info['gender']) ? ($user_info['gender'] == 1 ? '男性' : '女性') : '未知';

logDebug("请求参数", [
    'has_image' => !empty($data['image_base64']),
    'image_size' => strlen($data['image_base64']),
    'gender' => $gender
]);

// 配置Gemini API密钥
$apiKey = 'AIzaSyD1-g64EwoKNcvs0LeAn9hbyHJRuKj0Slg'; // 实际API密钥
$model = 'gemini-1.5-flash'; // 使用的模型名称

// 构建提示词
$prompt = "作为一位专业的穿搭顾问，请对用户上传的穿搭照片进行详细评分和分析。\n\n";

// 添加用户性别信息
$prompt .= "用户性别: " . $gender . "\n\n";

$prompt .= "请对照片中的穿搭进行以下方面的评分和分析：\n";
$prompt .= "1. 整体协调性：衣服之间的颜色、款式是否协调\n";
$prompt .= "2. 场合适宜性：穿搭是否适合特定场合（休闲、正式、商务等）\n";
$prompt .= "3. 个人气质匹配：穿搭是否符合用户气质特点\n";
$prompt .= "4. 时尚度：穿搭的流行程度和时尚感\n";
$prompt .= "5. 细节处理：配饰、鞋子等细节是否得当\n\n";

$prompt .= "请使用以下JSON格式返回分析结果：\n";
$prompt .= "{\n";
$prompt .= "  \"overall_score\": 8.5,  // 总体评分(1-10分，保留一位小数)\n";
$prompt .= "  \"coordination_score\": 8, // 整体协调性评分(1-10分)\n";
$prompt .= "  \"occasion_score\": 9, // 场合适宜性评分(1-10分)\n";
$prompt .= "  \"style_match_score\": 8, // 个人气质匹配评分(1-10分)\n";
$prompt .= "  \"fashion_score\": 7, // 时尚度评分(1-10分)\n";
$prompt .= "  \"detail_score\": 9, // 细节处理评分(1-10分)\n";
$prompt .= "  \"outfit_analysis\": \"这是一段对穿搭的总体分析描述，100-150个汉字左右。内容要专业，包括对搭配、颜色、风格等方面的点评。\",\n";
$prompt .= "  \"strength\": \"穿搭的优点，30-50个汉字\",\n";
$prompt .= "  \"improvement\": \"可以改进的地方，具体实用的建议，40-60个汉字\",\n";
$prompt .= "  \"occasion_recommendation\": \"适合的场合推荐，20-30个汉字\"\n";
$prompt .= "}\n\n";

$prompt .= "注意：\n";
$prompt .= "- 请提供专业、有建设性的评价\n";
$prompt .= "- 评分要客观公正，避免过于严苛或过于宽松\n";
$prompt .= "- 请确保JSON格式正确，所有字段都必须存在\n";
$prompt .= "- 分析应该包含具体的观察和建议，而非笼统的评价\n";
$prompt .= "- 在分析中应该指出特定的衣物、颜色、搭配方式等\n";
$prompt .= "- 在给出改进建议时，提供具体可行的修改或替代选项\n";

logDebug("构建的提示词", ['prompt_length' => strlen($prompt), 'prompt_excerpt' => substr($prompt, 0, 500)]);

// 调用Gemini API
$geminiUrl = "https://generativelanguage.googleapis.com/v1beta/models/" . $model . ":generateContent?key=" . $apiKey;

// 使用随机种子确保每次请求都不同
$seed = mt_rand(1, 2000000);

// 构建请求内容
$content = [
    [
        "parts" => [
            [
                "text" => $prompt
            ],
            [
                "inline_data" => [
                    "mime_type" => "image/jpeg",
                    "data" => $data['image_base64']
                ]
            ]
        ]
    ]
];

$request = [
    "contents" => $content,
    "generationConfig" => [
        "temperature" => 0.7,
        "topP" => 0.8,
        "topK" => 40,
        "maxOutputTokens" => 1024,
        "stopSequences" => [],
        "seed" => $seed
    ]
];

logDebug("准备调用Gemini API", [
    'url' => $geminiUrl, 
    'temperature' => 0.7, 
    'topP' => 0.8, 
    'topK' => 40
]);

// 发送请求到Gemini API
$ch = curl_init($geminiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 30秒超时

$response = curl_exec($ch);

if (curl_errno($ch)) {
    $error = curl_error($ch);
    logDebug("调用Gemini API失败", ['curl_error' => $error]);
    echo json_encode(['error' => true, 'msg' => '调用AI评分服务失败: ' . $error]);
    exit;
}

curl_close($ch);
logDebug("Gemini API调用完成", ['response_length' => strlen($response)]);

// 解析Gemini API响应
$result = json_decode($response, true);

// 验证响应是否有效
if (!$result || !isset($result['candidates'][0]['content']['parts'][0]['text'])) {
    logDebug("Gemini API响应无效", ['result' => $result]);
    echo json_encode(['error' => true, 'msg' => 'AI评分服务响应无效']);
    exit;
}

// 提取Gemini生成的JSON内容
$generatedText = $result['candidates'][0]['content']['parts'][0]['text'];
logDebug("Gemini生成的文本", ['text_length' => strlen($generatedText), 'text_excerpt' => substr($generatedText, 0, 500)]);

// 从文本中提取JSON部分
preg_match('/\{.*\}/s', $generatedText, $matches);
if (empty($matches)) {
    logDebug("无法从Gemini响应中解析JSON", ['generated_text' => $generatedText]);
    echo json_encode(['error' => true, 'msg' => '无法解析AI评分结果']);
    exit;
}

$ratingJson = $matches[0];
$rating = json_decode($ratingJson, true);

if (!$rating) {
    logDebug("解析评分结果JSON失败", ['rating_json' => $ratingJson]);
    echo json_encode(['error' => true, 'msg' => '解析评分结果失败']);
    exit;
}

logDebug("解析后的评分结果", $rating);

// 确保返回所有必要的字段
$requiredFields = [
    'overall_score', 'coordination_score', 'occasion_score', 
    'style_match_score', 'fashion_score', 'detail_score',
    'outfit_analysis', 'strength', 'improvement', 'occasion_recommendation'
];

foreach ($requiredFields as $field) {
    if (!isset($rating[$field])) {
        // 如果缺少字段，添加默认值
        switch ($field) {
            case 'overall_score':
            case 'coordination_score':
            case 'occasion_score':
            case 'style_match_score':
            case 'fashion_score':
            case 'detail_score':
                $rating[$field] = 7.0; // 默认评分
                break;
            case 'outfit_analysis':
                $rating[$field] = "AI无法完全分析您的穿搭，但从可见部分来看，您的搭配整体协调。建议您提供更清晰的全身照片，以获得更准确的评价。";
                break;
            case 'strength':
                $rating[$field] = "搭配整体协调，色彩搭配得当";
                break;
            case 'improvement':
                $rating[$field] = "可尝试添加亮点配饰，提升整体造型层次感";
                break;
            case 'occasion_recommendation':
                $rating[$field] = "适合日常休闲场合";
                break;
        }
        logDebug("添加缺失字段的默认值", ['field' => $field, 'value' => $rating[$field]]);
    }
}

// 返回评分结果
echo json_encode([
    'error' => false,
    'data' => $rating
]);

logDebug("API请求处理完成，返回评分结果"); 