<view class="container">
  <!-- 背景 -->
  <view class="background"></view>
  
  <!-- 试穿进度弹框 -->
  <view class="progress-modal" wx:if="{{showProgress}}">
    <view class="progress-content">
      <view class="progress-title">试衣中</view>
      
      <view class="progress-status-container">
        <view class="progress-status">{{progressMessage}}</view>
        <view class="progress-timer" wx:if="{{countdownTime > 0}}">预计剩余 {{countdownTime}} 秒</view>
      </view>
      
      <view class="progress-bar-container">
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercentStr}};"></view>
        </view>
        <view class="progress-percent">{{progressPercentStr}}</view>
      </view>
      
      <view class="progress-stages">
        <view class="stage {{progressStatus === 'queued' || progressStatus === 'uploading' || progressStatus === 'processing' || progressStatus === 'finishing' || progressStatus === 'completed' ? 'active' : ''}}">
          <text class="stage-icon">📋</text>
          <text class="stage-text">排队中</text>
        </view>
        <view class="stage {{progressStatus === 'uploading' || progressStatus === 'processing' || progressStatus === 'finishing' || progressStatus === 'completed' ? 'active' : ''}}">
          <text class="stage-icon">📤</text>
          <text class="stage-text">提交照片中</text>
        </view>
        <view class="stage {{progressStatus === 'processing' || progressStatus === 'finishing' || progressStatus === 'completed' ? 'active' : ''}}">
          <text class="stage-icon">🔄</text>
          <text class="stage-text">试衣中</text>
        </view>
        <view class="stage {{progressStatus === 'finishing' || progressStatus === 'completed' ? 'active' : ''}}">
          <text class="stage-icon">✅</text>
          <text class="stage-text">即将完成</text>
        </view>
      </view>
      
      <view class="progress-tip">试衣过程需要一定时间，请耐心等待...</view>
    </view>
  </view>
</view> 