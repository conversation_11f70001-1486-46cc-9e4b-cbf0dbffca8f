-- 创建穿搭日历关联表
CREATE TABLE `outfit_calendar` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `outfit_id` int(11) NOT NULL COMMENT '穿搭ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `calendar_date` date NOT NULL COMMENT '日期',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_outfit_date` (`user_id`, `calendar_date`),
  KEY `idx_outfit_id` (`outfit_id`),
  KEY `idx_calendar_date` (`calendar_date`),
  CONSTRAINT `fk_outfit_calendar_outfit` FOREIGN KEY (`outfit_id`) REFERENCES `outfits` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_outfit_calendar_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='穿搭日历关联表';

-- 注意：暂时保留原有的outfits表中的calendar_date字段，可以在新功能完全测试通过后再考虑删除
-- ALTER TABLE `outfits` DROP COLUMN `calendar_date`, DROP INDEX `idx_calendar_date`; 