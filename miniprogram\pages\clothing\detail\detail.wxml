<view class="container">
  <!-- 内容区域（可滚动） -->
  <scroll-view scroll-y="true" class="scroll-container">
    <!-- 衣物图片 -->
    <view class="clothing-image">
      <view class="image-container" bindtap="showLargeImage">
        <image wx:if="{{clothingDetail.image_url}}" src="{{clothingDetail.image_url}}" mode="aspectFit" class="clothing-img"></image>
        <view wx:else class="placeholder-img">
          <text class="loading-text">图片加载中...</text>
        </view>
      </view>
    </view>
    
    <!-- 衣物信息 -->
    <view class="clothing-info">
      <view class="info-title">
        <text>基本信息</text>
      </view>
      
      <!-- 标签容器 -->
      <view class="tags-container">
        <view class="tag" wx:for="{{tags}}" wx:key="index">{{item}}</view>
      </view>
      
      <!-- 衣物基本信息 -->
      <view class="info-section">
        <view class="info-row">
          <view class="info-label">名称</view>
          <view class="info-value">{{clothingDetail.name || '未命名衣物'}}</view>
        </view>
        
        <view class="info-row" wx:if="{{descriptionObj.color}}">
          <view class="info-label">颜色</view>
          <view class="info-value">{{descriptionObj.color}}</view>
        </view>
        
        <view class="info-row" wx:if="{{descriptionObj.brand}}">
          <view class="info-label">品牌</view>
          <view class="info-value">{{descriptionObj.brand}}</view>
        </view>
        
        <view class="info-row" wx:if="{{descriptionObj.price}}">
          <view class="info-label">价格</view>
          <view class="info-value">{{descriptionObj.price.indexOf('¥') !== -1 ? descriptionObj.price : '¥' + descriptionObj.price}}</view>
        </view>
        
        <view class="info-row" wx:if="{{descriptionObj.notes}}">
          <view class="info-label">备注</view>
          <view class="info-value">{{descriptionObj.notes}}</view>
        </view>
        
        <view class="info-row">
          <view class="info-label">添加时间</view>
          <view class="info-value">{{clothingDetail.created_at || '未知'}}</view>
        </view>
      </view>
    </view>
    
    <!-- 关联穿搭模块 -->
    <view class="related-outfits">
      <view class="info-title">
        <text>关联穿搭</text>
        <text class="outfit-count" wx:if="{{relatedOutfits.length > 0}}">({{relatedOutfits.length}})</text>
      </view>
      
      <!-- 加载中 -->
      <view class="related-loading" wx:if="{{loadingRelatedOutfits}}">
        <view class="loading-spinner-small"></view>
        <view class="loading-text">加载中...</view>
      </view>
      
      <!-- 关联穿搭列表 -->
      <scroll-view scroll-x="true" class="outfits-scroll" wx:if="{{!loadingRelatedOutfits && relatedOutfits.length > 0}}">
        <view class="outfits-scroll-inner">
          <view 
            wx:for="{{relatedOutfits}}" 
            wx:key="id" 
            class="related-outfit-item" 
            bindtap="viewOutfit" 
            data-id="{{item.id}}">
            <view class="outfit-preview">
              <!-- 复用穿搭页面的preview样式，优先显示缩略图 -->
              <block wx:if="{{item.thumbnail}}">
                <image src="{{item.thumbnail}}" mode="aspectFill" class="outfit-thumbnail"></image>
              </block>
              <block wx:else>
                <!-- 复用穿搭视图，显示所有衣物 -->
                <view class="outfit-placeholder" wx:if="{{item.items && item.items.length > 0}}">
                  <!-- 创建一个与详情页类似的outfit-view -->
                  <view class="outfit-view-mini">
                    <view 
                      wx:for="{{item.items}}" 
                      wx:for-item="clothingItem" 
                      wx:key="clothing_id"
                      class="outfit-item-mini"
                      style="left: {{clothingItem.previewPosition ? clothingItem.previewPosition.x : 50}}px; top: {{clothingItem.previewPosition ? clothingItem.previewPosition.y : 50}}px; width: {{(clothingItem.size ? clothingItem.size.width : 100) * (clothingItem.previewPosition ? clothingItem.previewPosition.scale : 0.5)}}px; height: {{(clothingItem.size ? clothingItem.size.height : 120) * (clothingItem.previewPosition ? clothingItem.previewPosition.scale : 0.5)}}px; transform: rotate({{clothingItem.rotation || 0}}deg); z-index: {{clothingItem.z_index || 1}};">
                      <image src="{{clothingItem.clothing_data.image_url}}" mode="aspectFit" class="item-image-mini"></image>
                    </view>
                  </view>
                  <view class="placeholder-count" wx:if="{{item.items.length > 1}}">{{item.items.length}}件</view>
                </view>
                <view class="outfit-no-items" wx:else>
                  <text class="outfit-icon">👔</text>
                </view>
              </block>
            </view>
            <view class="outfit-name">{{item.name || '未命名穿搭'}}</view>
          </view>
        </view>
      </scroll-view>
      
      <!-- 无关联穿搭 -->
      <view class="no-related-outfits" wx:if="{{!loadingRelatedOutfits && relatedOutfits.length === 0}}">
        <text>该衣物尚未添加到任何穿搭中</text>
      </view>
    </view>
    
    <!-- 底部占位，确保内容不被底部按钮遮挡 -->
    <view class="bottom-space"></view>
  </scroll-view>
  
  <!-- 操作按钮 -->
  <view class="action-buttons" wx:if="{{!isMerchantMode && !permissionLoading}}">
    <view class="delete-btn" hover-class="delete-btn-hover" bindtap="deleteClothing" wx:if="{{canDelete}}">
      <text class="delete-icon">✕</text>删除
    </view>
    <view class="edit-btn" hover-class="edit-btn-hover" bindtap="editClothing" wx:if="{{canEdit}}">
      编辑
    </view>
    <view class="try-on-btn" hover-class="try-on-btn-hover" bindtap="tryOnClothing">
      试穿
    </view>
  </view>

  <!-- 权限加载中 -->
  <view class="permission-loading" wx:if="{{permissionLoading && !isMerchantMode}}">
    <text class="loading-text">检查权限中...</text>
  </view>

  <!-- 权限提示 -->
  <view class="permission-info" wx:if="{{!permissionLoading && !isMerchantMode && (!canEdit || !canDelete)}}">
    <text class="permission-text">
      {{isOwner ? '您是此衣物的创建者' : userRole === 'creator' ? '您是圈子创建者' : userRole === 'member' ? '您是圈子成员' : '访客模式'}}
    </text>
  </view>

  <!-- 商家衣物试穿按钮 -->
  <view class="action-buttons merchant-action-buttons" wx:if="{{isMerchantMode}}">
    <view class="try-on-btn" hover-class="try-on-btn-hover" bindtap="tryOnClothing">
      试穿此衣物
    </view>
  </view>

  <!-- 大图弹框 -->
  <view class="image-modal {{showImageModal ? 'show' : ''}}" bindtap="hideLargeImage">
    <view class="modal-content" catchtap="preventDefault">
      <image wx:if="{{clothingDetail.image_url}}" 
             src="{{clothingDetail.image_url}}" 
             mode="aspectFit" 
             class="large-image"></image>
      <view class="close-btn" bindtap="hideLargeImage">×</view>
    </view>
    <!-- 保存按钮 -->
    <view class="save-btn" catchtap="saveImageToPhone">保存</view>
  </view>
</view> 