<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>穿搭管理 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        .search-box {
            display: flex;
            margin-bottom: 20px;
        }
        .search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px 0 0 4px;
            outline: none;
        }
        .search-btn {
            padding: 8px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
        }
        .search-btn:hover {
            background-color: #40a9ff;
        }
        .outfit-table {
            width: 100%;
            border-collapse: collapse;
        }
        .outfit-table th,
        .outfit-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }
        .outfit-table th {
            background-color: #fafafa;
            font-weight: 500;
        }
        .outfit-table tr:hover {
            background-color: #f5f5f5;
        }
        .thumbnail {
            width: 60px;
            height: 60px;
            border-radius: 4px;
            object-fit: cover;
            background-color: #f5f5f5;
            border: 1px solid #e8e8e8;
        }
        
        .clothing-thumbnail {
            width: 50px;
            height: 50px;
            border-radius: 4px;
            object-fit: cover;
            background-color: #f5f5f5;
            border: 1px solid #e8e8e8;
        }
        
        /* 图片容器样式 */
        .clothing-thumbnails-container {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .action-btn {
            padding: 4px 10px;
            border-radius: 4px;
            border: 1px solid;
            background-color: transparent;
            cursor: pointer;
            margin-right: 5px;
            font-size: 13px;
        }
        .view-btn {
            color: #1890ff;
            border-color: #1890ff;
        }
        .view-btn:hover {
            background-color: #e6f7ff;
        }
        .pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        .pagination button {
            padding: 5px 10px;
            margin: 0 5px;
            border: 1px solid #d9d9d9;
            background-color: white;
            cursor: pointer;
            border-radius: 4px;
        }
        .pagination button:hover:not(:disabled) {
            color: #1890ff;
            border-color: #1890ff;
        }
        .pagination button:disabled {
            color: #d9d9d9;
            cursor: not-allowed;
        }
        .pagination .current-page {
            color: #1890ff;
            border-color: #1890ff;
        }
        .no-data {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        
        /* 详情模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            overflow-y: auto;
        }
        
        .modal-content {
            background-color: #fff;
            margin: 50px auto;
            width: 80%;
            max-width: 800px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            position: relative;
        }
        
        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 500;
            margin: 0;
        }
        
        .modal-close {
            font-size: 22px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
            background: none;
            border: none;
            padding: 0;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #f0f0f0;
            text-align: right;
        }
        
        /* 穿搭详情样式 */
        .outfit-item {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 15px;
        }
        
        .outfit-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .outfit-header .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
            object-fit: cover;
        }
        
        .outfit-header .user-info {
            flex: 1;
        }
        
        .outfit-header .user-info .user-name {
            font-weight: 500;
            color: #333;
        }
        
        .outfit-header .user-info .timestamp {
            font-size: 0.8rem;
            color: #999;
        }
        
        .outfit-images {
            margin-bottom: 20px;
        }
        
        .outfit-main-image {
            width: 100%;
            max-height: 400px;
            object-fit: contain;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .outfit-thumbnails {
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding-bottom: 10px;
        }
        
        .outfit-thumbnail {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .outfit-thumbnail.active {
            border-color: #1890ff;
        }
        
        .outfit-info-list {
            margin-bottom: 20px;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 10px;
        }
        
        .info-label {
            font-weight: 500;
            width: 100px;
            flex-shrink: 0;
        }
        
        .info-value {
            flex: 1;
        }
        
        .clothes-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .clothes-item {
            display: flex;
            border: 1px solid #eee;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        
        .clothes-image-container {
            flex: 0 0 150px;
            margin-right: 15px;
        }
        
        .clothes-image {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .clothes-details {
            flex: 1;
            overflow: hidden;
        }
        
        .clothes-category {
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 16px;
        }
        
        .clothes-description {
            font-size: 0.95em;
            color: #333;
            line-height: 1.5;
            background: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
        }
        
        .loading-indicator {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error-message {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #f5222d;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>穿搭管理</h2>
                <div class="user-info">
                    <span id="userName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div id="outfitError" class="error-message" style="display: none;"></div>
            <div id="outfitLoading" class="loading-indicator" style="display: none;">正在加载穿搭数据...</div>
            
            <div class="card">
                <div class="search-box">
                    <input type="text" id="searchInput" class="search-input" placeholder="搜索穿搭名称或用户ID..." />
                    <button id="searchBtn" class="search-btn">搜索</button>
                </div>
                
                <table class="outfit-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>预览图</th>
                            <th>名称</th>
                            <th>用户ID</th>
                            <th>分类</th>
                            <th>衣物数</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="outfitTableBody">
                        <tr>
                            <td colspan="8" class="no-data">加载中...</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="pagination" id="pagination">
                    <button id="prevBtn" disabled>&lt; 上一页</button>
                    <span id="pageInfo">第 0/0 页</span>
                    <button id="nextBtn" disabled>下一页 &gt;</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 穿搭详情模态框 -->
    <div id="outfitModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">穿搭详情</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="outfitModalBody">
                <!-- 穿搭详情内容将动态插入这里 -->
                <div class="loading-indicator">加载中...</div>
            </div>
            <div class="modal-footer">
                <button class="action-btn view-btn modal-close">关闭</button>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/outfit_list.js"></script>
    <script src="js/image_viewer.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为outfit
            Sidebar.init('outfit');
            
            // 显示用户信息
            const userName = document.getElementById('userName');
            const user = Auth.getCurrentUser();
            if (user) {
                userName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
        });
    </script>
</body>
</html> 