/* pages/outfit_rating/index/index.wxss */
.container {
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #f8f8f8;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 头部模块 */
.header-module {
  text-align: center;
  padding: 20rpx 0;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.subtitle {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

/* 上传模块 */
.upload-module {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.upload-area {
  width: 100%;
  height: 450rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  margin-bottom: 30rpx;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}

.upload-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.upload-text {
  font-size: 28rpx;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.upload-area:hover .preview-overlay {
  opacity: 1;
}

.overlay-text {
  color: #fff;
  font-size: 28rpx;
  padding: 10rpx 20rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 8rpx;
}

.button-area {
  display: flex;
  justify-content: center;
}

.primary-button {
  width: 80%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  color: #fff;
  background-color: #333;
  display: flex;
  justify-content: center;
  align-items: center;
}

.primary-button[disabled] {
  background-color: #ccc;
  color: #fff;
}

/* 小技巧模块 */
.tips-module {
  margin-top: 10rpx;
}

.tips-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tips-header {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.tip-item {
  display: flex;
  align-items: center;
}

.tip-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #333;
  color: #fff;
  border-radius: 50%;
  font-size: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.tip-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

 