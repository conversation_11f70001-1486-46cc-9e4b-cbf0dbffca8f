/**
 * 通用侧边栏导航组件
 */
const Sidebar = {
    /**
     * 初始化侧边栏
     * @param {string} activeItem - 当前活动的导航项ID
     */
    init: function(activeItem) {
        const sidebarEl = document.querySelector('.sidebar');
        
        if (!sidebarEl) {
            console.error('Sidebar container not found');
            return;
        }
        
        // 渲染侧边栏内容
        this.render(sidebarEl, activeItem);
        
        // 添加响应式菜单切换功能
        this.setupMobileToggle();
    },
    
    /**
     * 获取导航项配置
     * @returns {Array} 导航项配置数组
     */
    getMenuItems: function() {
        return [
            { id: 'dashboard', label: '仪表盘', url: 'dashboard.html' },
            { id: 'user', label: '用户管理', url: 'user_list.html' },
            { id: 'merchant', label: '商户管理', url: 'merchant_list.html' },
            { id: 'clothing', label: '衣物管理', url: 'clothing_list.html' },
            { id: 'photo', label: '照片管理', url: 'photo_list.html' },
            { id: 'try_on', label: '试衣历史', url: 'try_on_list.html' },
            { id: 'outfit', label: '穿搭管理', url: 'outfit_list.html' },
            { id: 'outfit_calendar', label: '穿搭日历', url: 'outfit_calendar_list.html' },
            { id: 'recommended_outfit', label: '推荐穿搭', url: 'recommended_outfit_list.html' },
            { id: 'ai_recommendation', label: 'AI推荐', url: 'ai_recommendation_list.html' },
            { id: 'recommended_category', label: '推荐穿搭分类', url: 'recommended_category_list.html' },
            { id: 'image_analysis', label: '形象分析', url: 'image_analysis_list.html' },
            { id: 'outfit_rating_history', label: '穿搭评分', url: 'outfit_rating_history.html' },
            { id: 'face_analysis', label: '面部分析', url: 'face_analysis_list.html' },
            { id: 'donation', label: '打赏记录', url: 'donation_list.html' },
            { id: 'invitation_code', label: '邀请码管理', url: 'invitation_code_list.html' },
            { id: 'announcement', label: '公告管理', url: 'announcement_list.html' },
            { id: 'taobao_product', label: '淘宝商品管理', url: 'taobao_product_list.html' },
            { id: 'system_settings', label: '系统设置', url: 'system_settings.html' }
        ];
    },
    
    /**
     * 渲染侧边栏内容
     * @param {HTMLElement} container - 侧边栏容器元素
     * @param {string} activeItem - 当前活动的导航项ID
     */
    render: function(container, activeItem) {
        // 创建Logo部分
        const logoHTML = `
            <div class="sidebar-logo">
                <img src="https://cyymj.oss-cn-shanghai.aliyuncs.com/logo.png" alt="次元衣柜">
                <h2>次元衣柜后台</h2>
            </div>
        `;
        
        // 创建菜单列表
        let menuItemsHTML = '';
        const menuItems = this.getMenuItems();
        
        menuItems.forEach(item => {
            const isActive = activeItem === item.id ? 'active' : '';
            menuItemsHTML += `
                <li class="menu-item ${isActive}">
                    <a href="${item.url}">${item.label}</a>
                </li>
            `;
        });
        
        // 组合完整的侧边栏HTML
        const sidebarHTML = `
            ${logoHTML}
            <ul class="menu">
                ${menuItemsHTML}
            </ul>
        `;
        
        // 更新侧边栏内容
        container.innerHTML = sidebarHTML;
    },
    
    /**
     * 设置移动端菜单切换功能
     */
    setupMobileToggle: function() {
        // 可以在这里添加移动端菜单切换的代码
        // 例如，在小屏幕上添加一个切换按钮来显示/隐藏侧边栏
    }
}; 