// pages/wardrobes/add/add.js
// 获取应用实例
const app = getApp();

Page({

    /**
     * 页面的初始数据
     */
    data: {
        name: '',
        description: '',
        sortOrder: '0',
        submitting: false
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        // 可以接收默认值，如从其他页面传递过来的初始值
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    },

    /**
     * 名称输入事件
     */
    onNameInput: function(e) {
        this.setData({
            name: e.detail.value
        });
    },

    /**
     * 描述输入事件
     */
    onDescriptionInput: function(e) {
        this.setData({
            description: e.detail.value
        });
    },

    /**
     * 排序输入事件
     */
    onSortOrderInput: function(e) {
        this.setData({
            sortOrder: e.detail.value
        });
    },

    /**
     * 取消操作
     */
    onCancel: function() {
        wx.navigateBack();
    },

    /**
     * 提交表单
     */
    onSubmit: function() {
        // 检查必填字段
        if (!this.data.name.trim()) {
            wx.showToast({
                title: '请输入衣橱名称',
                icon: 'none'
            });
            return;
        }

        // 防止重复提交
        if (this.data.submitting) {
            return;
        }

        this.setData({ submitting: true });

        // 整理数据
        const data = {
            name: this.data.name.trim(),
            description: this.data.description.trim(),
            sort_order: parseInt(this.data.sortOrder || 0)
        };

        // 发送请求
        wx.showLoading({ title: '正在创建...' });

        wx.request({
            url: `${app.globalData.apiBaseUrl}/add_wardrobe.php`,
            method: 'POST',
            header: {
                'content-type': 'application/json',
                'Authorization': app.globalData.token
            },
            data: data,
            success: (res) => {
                wx.hideLoading();
                console.log('创建衣橱响应:', res.data);

                if (res.statusCode === 200 && res.data.success) {
                    wx.showToast({
                        title: '创建成功',
                        icon: 'success',
                        duration: 1500
                    });

                    // 标记需要刷新列表
                    app.globalData.needRefreshWardrobes = true;

                    // 短暂延迟后返回上一页
                    setTimeout(() => {
                        wx.navigateBack();
                    }, 1500);
                } else {
                    wx.showToast({
                        title: res.data.message || '创建失败',
                        icon: 'none',
                        duration: 2000
                    });
                    this.setData({ submitting: false });
                }
            },
            fail: (err) => {
                wx.hideLoading();
                console.error('请求失败:', err);
                wx.showToast({
                    title: '网络请求失败',
                    icon: 'none',
                    duration: 2000
                });
                this.setData({ submitting: false });
            }
        });
    }
})