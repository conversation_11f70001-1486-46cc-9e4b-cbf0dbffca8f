<view class="container">
  <!-- 标题栏 -->
  <view class="header">
    <view class="title">衣物分类</view>
    <view class="action-buttons">
      <view class="trash-btn" bindtap="goToTrash">
        <image src="/images/trash.png" mode="aspectFit"></image>
      </view>
      <view class="add-btn" bindtap="goToAddCategory">
        <image src="/images/add.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 分类列表 -->
  <view class="category-list" wx:if="{{categories.length > 0}}">
    <view class="category-item" wx:for="{{categories}}" wx:key="id">
      <view class="category-info">
        <view class="category-name">
          {{item.name}}
          <text class="system-tag" wx:if="{{item.is_system}}">系统</text>
        </view>
      </view>
      <view class="category-actions">
        <view class="edit-btn" catchtap="editCategory" data-category="{{item}}">
          <image src="/images/edit.png" mode="aspectFit"></image>
        </view>
        <view class="delete-btn" catchtap="deleteCategory" 
              data-id="{{item.id}}" 
              data-name="{{item.name}}" 
              data-is-system="{{item.is_system}}">
          <image src="/images/delete.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <image class="empty-image" src="/images/empty-wardrobe.png" mode="aspectFit"></image>
    <view class="empty-text">暂无自定义分类</view>
    <view class="empty-desc">系统提供了基础分类，您也可以创建自定义分类</view>
    <view class="create-btn" bindtap="goToAddCategory">创建分类</view>
  </view>

  <!-- 加载状态 -->
  <view class="load-more" wx:if="{{isLoading}}">
    <view>加载中...</view>
  </view>
</view> 