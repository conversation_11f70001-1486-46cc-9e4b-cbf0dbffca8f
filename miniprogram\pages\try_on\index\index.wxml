<!-- 主内容区域 -->
<view class="container">
  <view class="header">
    <view class="search-box">
      <image class="search-icon" src="/images/search.png"></image>
      <input class="search-input" placeholder="搜索商家名称" value="{{keyword}}" bindinput="onKeywordInput" confirm-type="search" bindconfirm="searchMerchants"/>
      <view class="search-btn" bindtap="searchMerchants">搜索</view>
    </view>
      </view>
      
  <view class="merchant-list" wx:if="{{merchants.length > 0}}">
    <view class="merchant-item" wx:for="{{merchants}}" wx:key="id" bindtap="navigateToMerchantWardrobe" data-merchant-id="{{item.id}}">
      <image class="merchant-avatar" src="{{item.avatar_url || '/images/default-avatar.png'}}"></image>
      <view class="merchant-info">
        <view class="merchant-name">{{item.nickname || '商家用户'}}</view>
        <view class="merchant-status">
          <view class="status-tag {{item.share_try_on_credits ? 'status-share' : ''}}">
            {{item.share_try_on_credits ? '已开启共享试穿点数' : '未开启共享试穿点数'}}
          </view>
          <view class="clothes-count-tag">
            <text class="clothes-count-text">{{item.clothes_count || 0}}</text>
          </view>
        </view>
      </view>
      <image class="merchant-arrow" src="/images/arrow-right.png"></image>
    </view>
  </view>
  
  <view class="empty-state" wx:elif="{{!loading}}">
    <image class="empty-icon" src="/images/icons/empty.png"></image>
    <text class="empty-text">暂无商家</text>
      </view>
  
  <view class="photo-section" wx:if="{{showPhotoSection}}">
    <view class="section-title">
      <text>我的试穿照片</text>
      <navigator class="view-more" url="/pages/photos/index/index">查看更多</navigator>
    </view>

    <view class="photo-list">
      <view class="photo-item" wx:for="{{photos}}" wx:key="id" bindtap="selectPhoto" data-photo-id="{{item.id}}" data-photo-url="{{item.image_url}}">
        <image class="photo-image" src="{{item.image_url}}" mode="aspectFill"></image>
      </view>
      <view class="photo-item add-photo" bindtap="uploadPhoto">
        <image class="add-icon" src="/images/icons/add.png"></image>
        <text>上传照片</text>
      </view>
    </view>
  </view>
  
  <view class="loading" wx:if="{{loading}}">
    <image class="loading-icon" src="/images/icons/loading.gif"></image>
    <text class="loading-text">加载中...</text>
      </view>
  
  <view class="pagination" wx:if="{{totalPages > 1}}">
    <view class="pagination-btn {{currentPage <= 1 ? 'disabled' : ''}}" bindtap="prevPage">上一页</view>
    <view class="pagination-info">{{currentPage}}/{{totalPages}}</view>
    <view class="pagination-btn {{currentPage >= totalPages ? 'disabled' : ''}}" bindtap="nextPage">下一页</view>
    </view>
</view>

<!-- 底部按钮 - 仅在登录状态且有选中照片时显示 -->
<view class="bottom-bar" wx:if="{{isLoggedIn && !showTryOnResult && selectedPhotoId}}">
  <view class="action-btn active" bindtap="goToSelectClothing">
    选择试穿衣物
  </view>
</view>

<!-- 底部按钮 - 从衣橱选择 -
<view class="bottom-bar" wx:if="{{isLoggedIn && !showTryOnResult && !selectedPhotoId}}">
  <view class="action-btn active" bindtap="goToSelectFromWardrobe">
    从衣橱选择
  </view>
</view>->

<!-- 试衣须知弹窗 -->
<view class="guide-popup-mask" wx:if="{{showTryOnGuidePopup}}"></view>
<view class="guide-popup" wx:if="{{showTryOnGuidePopup}}">
  <view class="guide-content">
    <view class="guide-header">
      <text class="guide-title">试衣须知</text>
      <view class="guide-close" bindtap="closeTryOnGuide">×</view>
    </view>
    <scroll-view class="guide-scroll" scroll-y>
      <!-- 试衣方式说明 -->
      <view class="guide-section">
        <view class="guide-section-title">试衣方式</view>
        <view class="guide-item">
          <view class="guide-item-content">
            <view>• <text class="highlight">商家试衣</text>：选择商家提供的服饰进行试穿</view>
            <view>• <text class="highlight">衣橱试衣</text>：选择自己衣橱中的服饰进行试穿</view>
          </view>
        </view>
      </view>
      
      <!-- 精简的模特要求 -->
      <view class="guide-section">
        <view class="guide-section-title">模特照片要求</view>
        <view class="guide-item">
          <view class="guide-item-content">
            <view>• 全身正面照，光照良好，手部展示完整</view>
            <view>• 避免：侧身、坐姿、多人照片、光线暗/模糊</view>
          </view>
        </view>
      </view>
      
      <!-- 精简的服饰要求 -->
      <view class="guide-section">
        <view class="guide-section-title">服饰图片要求</view>
        <view class="guide-item">
          <view class="guide-item-content">
            <view>• 支持：上衣、裤子、裙子、外套、套装等</view>
            <view>• 服饰平铺拍摄，背景简洁，服饰展开无褶皱</view>
          </view>
        </view>
      </view>
    </scroll-view>
    <view class="guide-footer">
      <button class="guide-btn-confirm" bindtap="closeTryOnGuide">我知道了</button>
    </view>
  </view>
</view> 

<!-- 底部固定购买次数栏 -->
<view class="try-on-credits-bar" wx:if="{{isLoggedIn}}">
  <view class="credits-container">
    <image class="credits-icon" src="/images/count.png" mode="aspectFit"></image>
    <view class="credits-info">
      <text class="credits-title">试穿次数</text>
      <view class="credits-counts">
        <text class="free-count">免费{{freeCount}}次</text>
        <text class="count-separator">|</text>
        <text class="paid-count">付费{{paidCount}}次</text>
      </view>
    </view>
  </view>
  <view class="buy-credits-btn" bindtap="navigateToTryOnCount">购买次数</view>
</view>

<!-- 底部安全区域，确保内容不被底部栏遮挡 -->
<view class="bottom-safe-area" wx:if="{{isLoggedIn}}"></view> 