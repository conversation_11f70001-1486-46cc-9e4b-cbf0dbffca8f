<?php
header("Content-Type: application/json");
require_once './db.php';
require_once './auth.php';
require_once './config.php';

// 初始化响应数组
$response = [
    'code' => 0,
    'message' => 'success',
    'data' => []
];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['code'] = 405;
    $response['message'] = 'Method Not Allowed';
    echo json_encode($response);
    exit;
}

// 获取POST参数
$postData = json_decode(file_get_contents("php://input"), true);
$token = isset($postData['token']) ? $postData['token'] : '';
$merchantId = isset($postData['merchant_id']) ? intval($postData['merchant_id']) : 0;

// 处理 demo_mode 参数
$demoMode = false;
if (isset($postData['demo_mode'])) {
    // 支持各种可能的true值: true, "true", "1", 1
    if ($postData['demo_mode'] === true || 
        $postData['demo_mode'] === "true" || 
        $postData['demo_mode'] === "1" || 
        $postData['demo_mode'] === 1 ||
        $postData['demo_mode'] == true) {
        $demoMode = true;
    }
}

if (empty($token) && !$demoMode) {
    $response['code'] = 400;
    $response['message'] = 'Missing required parameters';
    echo json_encode($response);
    exit;
}

if ($merchantId <= 0) {
    $response['code'] = 400;
    $response['message'] = 'Missing required parameters';
    echo json_encode($response);
    exit;
}

// 验证用户token，除非是体验模式
$userId = null;
if (!$demoMode) {
    $auth = new Auth();
    $verifyResult = $auth->verifyToken($token);

    if (!$verifyResult) {
        $response['code'] = 401;
        $response['message'] = '无效或已过期的令牌';
        echo json_encode($response);
        exit;
    }
    
    $userId = $verifyResult['sub'];
} else {
    // 在体验模式下，使用默认用户ID
    $userId = 1;
}

$db = new Database();
$conn = $db->getConnection();

try {
    // 首先验证商家是否存在且已入驻
    $checkStmt = $conn->prepare("
        SELECT merchant_status 
        FROM users 
        WHERE id = :merchant_id AND merchant_status = 'yes'
    ");
    $checkStmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_INT);
    $checkStmt->execute();
    $checkResult = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$checkResult) {
        $response['code'] = 404;
        $response['message'] = '商家不存在或未入驻';
        echo json_encode($response);
        exit;
    }
    
    // 获取商家的衣橱列表
    $stmt = $conn->prepare("
        SELECT id, name, description, sort_order, is_default, created_at, updated_at
        FROM wardrobes 
        WHERE user_id = :merchant_id
        ORDER BY is_default DESC, sort_order ASC, id ASC
    ");
    $stmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_INT);
    $stmt->execute();
    
    $wardrobes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $response['data'] = $wardrobes;
} catch (Exception $e) {
    $response['code'] = 500;
    $response['message'] = '处理请求时发生错误: ' . $e->getMessage();
} finally {
    // PDO不需要手动关闭连接
    $conn = null;
}

echo json_encode($response); 