/* 全局容器 */
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #ffffff;
  box-sizing: border-box;
  position: relative;
}

/* 管理提示 */
.manage-hint {
  text-align: center;
  color: #666666;
  padding: 10px;
  font-size: 13px;
  background-color: #f9f9f9;
  margin-top: 0; /* 移除与自定义标题栏的间距 */
}

/* 分类标签栏 */
.category-tabs {
  white-space: nowrap;
  background-color: #ffffff;
  padding: 12px 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.tabs-container {
  display: inline-flex;
}

.tab-item {
  display: inline-block;
  padding: 8px 16px;
  margin-right: 8px;
  border-radius: 16px;
  font-size: 13px;
  color: #666666;
  background-color: #f5f5f5;
  transition: all 0.3s;
}

.tab-item.active {
  background-color: #000000;
  color: #ffffff;
}

/* 衣物容器 */
.clothes-container {
  flex: 1;
  padding: 15px;
  margin-bottom: 65px; /* 为底部工具栏留出空间 */
  box-sizing: border-box;
  height: calc(100vh - 35px - 45px - 65px); /* 调整高度计算：视口高度减去提示栏、标签栏和底部工具栏的高度 */
}

.clothes-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.clothes-item {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  aspect-ratio: 3/4;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  box-sizing: border-box;
}

.clothes-img {
  width: 90%;
  height: 90%;
  object-fit: contain;
  border-radius: 10px;
}

/* 衣物名称样式 */
.clothes-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
}

.clothes-item.selected::after {
  content: '';
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.3);
  border: 2px solid #000000;
  border-radius: 12px;
  box-sizing: border-box;
}

.clothes-item.selected .select-icon {
  display: flex;
}

.select-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #000000;
  color: white;
  display: none;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.check-icon {
  color: #ffffff;
  font-weight: bold;
}

/* 编辑工具栏 */
.edit-toolbar {
  height: 65px;
  background-color: #ffffff;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 0 20px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
}

.edit-btn, .delete-btn {
  padding: 10px 25px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.edit-btn {
  background-color: #f5f5f5;
  color: #333333;
}

.delete-btn {
  background-color: #000000;
  color: #ffffff;
}

.icon-edit, .icon-delete {
  margin-right: 5px;
} 