-- 淘宝客商品数据表
CREATE TABLE `taobao_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` varchar(50) NOT NULL COMMENT '淘宝商品ID',
  `title` varchar(255) NOT NULL COMMENT '商品标题',
  `image_url` text NOT NULL COMMENT '商品主图URL',
  `small_images` text COMMENT '商品小图URL列表，JSON格式',
  `original_price` decimal(10,2) NOT NULL COMMENT '商品原价',
  `zk_final_price` decimal(10,2) NOT NULL COMMENT '商品折扣价',
  `final_price` decimal(10,2) NOT NULL COMMENT '商品最终价格',
  `coupon_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠券金额',
  `coupon_info` varchar(255) DEFAULT NULL COMMENT '优惠券信息',
  `coupon_start_time` datetime DEFAULT NULL COMMENT '优惠券开始时间',
  `coupon_end_time` datetime DEFAULT NULL COMMENT '优惠券结束时间',
  `shop_title` varchar(100) DEFAULT NULL COMMENT '店铺名称',
  `seller_id` varchar(50) DEFAULT NULL COMMENT '卖家ID',
  `volume` int(11) DEFAULT '0' COMMENT '30天销量',
  `item_url` text COMMENT '商品推广链接',
  `coupon_click_url` text COMMENT '优惠券推广链接',
  `tpwd` varchar(255) DEFAULT NULL COMMENT '淘口令短链接',
  `is_fake_tpwd` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为模拟淘口令：1=是，0=否',
  `commission_rate` decimal(10,2) DEFAULT '0.00' COMMENT '佣金比例',
  `commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '佣金金额',
  `material_id` varchar(50) DEFAULT NULL COMMENT '所属物料ID',
  `category` varchar(50) DEFAULT NULL COMMENT '商品分类',
  `tags` text COMMENT '商品标签(JSON)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1=上架，0=下架',
  `is_recommend` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否推荐：1=是，0=否',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序',
  `last_sync_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后同步时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_item_id` (`item_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_recommend` (`is_recommend`),
  KEY `idx_material_id` (`material_id`),
  KEY `idx_category` (`category`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_last_sync_time` (`last_sync_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='淘宝客商品数据表'; 