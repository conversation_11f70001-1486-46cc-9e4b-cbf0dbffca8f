<?php
/**
 * 获取本地存储的淘宝联盟商品数据接口
 * 
 * 该接口用于查询系统中已同步的淘宝联盟商品数据，支持多种筛选条件和分页功能。
 * 支持按关键词、物料ID、分类、价格区间、优惠券状态和推荐状态等条件过滤。
 * 
 * 请求方式：GET
 * 接口路径：/login_backend/get_stored_taobao_products.php
 * 
 * 可用参数：
 * - page: 当前页码，默认为1
 * - page_size: 每页记录数，默认为20，最大100
 * - keyword: 搜索关键词，模糊匹配商品标题和店铺名
 * - material_id: 物料ID，精确匹配
 * - category: 商品分类，精确匹配
 * - min_price: 最低价格
 * - max_price: 最高价格
 * - coupon_only: 是否只显示有优惠券的商品，0或1
 * - is_recommend: 是否只显示推荐商品，0或1
 * - sort_field: 排序字段，可选值：id, title, original_price, final_price, coupon_amount, commission_rate, volume, sort_order, last_sync_time, created_at
 * - sort_order: 排序方式，可选值：asc, desc
 * - tpwd_type: 淘口令类型，可选值：real, simulate, none
 * 
 * 返回数据：
 * - error: 是否出错，布尔值
 * - msg: 错误时的提示消息
 * - data: 商品数据数组
 * - pagination: 分页信息，包括总记录数、当前页、每页数量、总页数
 * - filters: 可用于筛选的分类和物料信息
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

require_once 'config.php';
require_once 'auth.php';

// 验证用户Token (可选，如果想要对接口做权限控制)
$isAuthorized = false;
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION']) && !empty($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
    
    $auth = new Auth();
    $payload = $auth->verifyToken($token);
    
    if ($payload) {
        $isAuthorized = true;
    }
    // 注意：即使令牌无效，我们也继续执行，不再退出脚本
}

// 获取请求参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$page = max(1, $page); // 确保页码不小于1

$pageSize = isset($_GET['page_size']) ? intval($_GET['page_size']) : 20;
$pageSize = min(100, max(1, $pageSize)); // 限制页面大小在1-100之间

$keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
$materialId = isset($_GET['material_id']) ? trim($_GET['material_id']) : '';
$category = isset($_GET['category']) ? trim($_GET['category']) : '';
$minPrice = isset($_GET['min_price']) ? floatval($_GET['min_price']) : 0;
$maxPrice = isset($_GET['max_price']) ? floatval($_GET['max_price']) : 0;
$couponOnly = isset($_GET['coupon_only']) ? (bool)$_GET['coupon_only'] : false;
$sortField = isset($_GET['sort_field']) ? trim($_GET['sort_field']) : 'sort_order';
$sortOrder = isset($_GET['sort_order']) && strtolower($_GET['sort_order']) === 'desc' ? 'DESC' : 'ASC';
$isRecommend = isset($_GET['is_recommend']) ? (bool)$_GET['is_recommend'] : false;
$tpwdType = isset($_GET['tpwd_type']) ? trim($_GET['tpwd_type']) : '';

// 验证排序字段
$allowedSortFields = [
    'id', 'title', 'original_price', 'final_price', 'coupon_amount', 
    'commission_rate', 'volume', 'sort_order', 'last_sync_time', 'created_at'
];
if (!in_array($sortField, $allowedSortFields)) {
    $sortField = 'sort_order'; // 默认排序字段
}

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查is_fake_tpwd字段是否存在
    $checkFieldSql = "SHOW COLUMNS FROM taobao_products LIKE 'is_fake_tpwd'";
    $checkFieldStmt = $conn->prepare($checkFieldSql);
    $checkFieldStmt->execute();
    $fieldExists = $checkFieldStmt->rowCount() > 0;
    
    if (!$fieldExists) {
        // 如果字段不存在，记录错误并尝试添加字段
        error_log("错误：is_fake_tpwd字段不存在，尝试添加...");
        try {
            $addFieldSql = "ALTER TABLE `taobao_products` ADD COLUMN `is_fake_tpwd` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为模拟淘口令：1=是，0=否' AFTER `tpwd`";
            $conn->exec($addFieldSql);
            error_log("成功添加is_fake_tpwd字段");
        } catch (Exception $e) {
            error_log("添加is_fake_tpwd字段失败: " . $e->getMessage());
        }
    } else {
        // 字段存在，检查字段类型和默认值
        $fieldInfoSql = "SHOW COLUMNS FROM taobao_products WHERE Field = 'is_fake_tpwd'";
        $fieldInfoStmt = $conn->prepare($fieldInfoSql);
        $fieldInfoStmt->execute();
        $fieldInfo = $fieldInfoStmt->fetch(PDO::FETCH_ASSOC);
        
        error_log("is_fake_tpwd字段信息: " . json_encode($fieldInfo));
    }
    
    // 构建查询条件
    $conditions = ["status = 1"]; // 只返回上架商品
    $params = [];
    
    // 关键词搜索
    if (!empty($keyword)) {
        $conditions[] = "(title LIKE :keyword OR shop_title LIKE :keyword)";
        $params[':keyword'] = "%{$keyword}%";
    }
    
    // 物料ID筛选
    if (!empty($materialId)) {
        $conditions[] = "material_id = :material_id";
        $params[':material_id'] = $materialId;
    }
    
    // 分类筛选
    if (!empty($category)) {
        $conditions[] = "category = :category";
        $params[':category'] = $category;
    }
    
    // 价格范围筛选
    if ($minPrice > 0) {
        $conditions[] = "final_price >= :min_price";
        $params[':min_price'] = $minPrice;
    }
    
    if ($maxPrice > 0) {
        $conditions[] = "final_price <= :max_price";
        $params[':max_price'] = $maxPrice;
    }
    
    // 只显示有优惠券的商品
    if ($couponOnly) {
        $conditions[] = "coupon_amount > 0";
    }
    
    // 只显示推荐商品
    if ($isRecommend) {
        $conditions[] = "is_recommend = 1";
    }
    
    // 淘口令类型筛选
    if (!empty($tpwdType)) {
        // 记录日志，便于调试
        error_log("淘口令筛选类型: " . $tpwdType);
        
        switch ($tpwdType) {
            case 'real':
                // 真实淘口令：有淘口令且不是模拟的
                $conditions[] = "tpwd IS NOT NULL AND tpwd != '' AND is_fake_tpwd = 0";
                break;
            case 'fake':
                // 模拟淘口令：有淘口令且是模拟的
                $conditions[] = "tpwd IS NOT NULL AND tpwd != '' AND is_fake_tpwd = 1";
                break;
            case 'none':
                // 无淘口令：淘口令为空
                $conditions[] = "(tpwd IS NULL OR tpwd = '')";
                break;
        }
    }
    
    // 组合条件
    $conditionSql = implode(' AND ', $conditions);
    
    // 记录最终的SQL条件，便于调试
    error_log("SQL筛选条件: " . $conditionSql);
    
    // 查询总记录数
    $countSql = "SELECT COUNT(*) FROM taobao_products WHERE {$conditionSql}";
    $countStmt = $conn->prepare($countSql);
    foreach ($params as $key => $value) {
        $countStmt->bindValue($key, $value);
    }
    $countStmt->execute();
    $total = $countStmt->fetchColumn();
    
    // 计算总页数
    $totalPages = ceil($total / $pageSize);
    
    // 查询数据
    $offset = ($page - 1) * $pageSize;
    $sql = "SELECT * FROM taobao_products 
            WHERE {$conditionSql} 
            ORDER BY {$sortField} {$sortOrder}
            LIMIT :offset, :limit";
    
    $stmt = $conn->prepare($sql);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':limit', $pageSize, PDO::PARAM_INT);
    
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 调试：记录查询结果中的is_fake_tpwd字段值分布
    if (!empty($tpwdType)) {
        $realCount = 0;
        $fakeCount = 0;
        $noneCount = 0;
        
        foreach ($products as $product) {
            if (empty($product['tpwd'])) {
                $noneCount++;
            } else if ($product['is_fake_tpwd'] == 1) {
                $fakeCount++;
            } else {
                $realCount++;
            }
        }
        
        error_log("查询结果统计 - 总数: " . count($products) . 
                 ", 真实淘口令: " . $realCount . 
                 ", 模拟淘口令: " . $fakeCount . 
                 ", 无淘口令: " . $noneCount);
    }
    
    // 处理结果
    $formattedProducts = [];
    foreach ($products as $product) {
        // 处理小图列表，将JSON转换回数组
        if (!empty($product['small_images'])) {
            $product['small_images'] = json_decode($product['small_images'], true);
        } else {
            $product['small_images'] = [];
        }
        
        // 确保所有价格格式正确（两位小数）
        $product['original_price'] = number_format($product['original_price'], 2, '.', '');
        $product['zk_final_price'] = number_format($product['zk_final_price'], 2, '.', '');
        $product['final_price'] = number_format($product['final_price'], 2, '.', '');
        $product['coupon_amount'] = number_format($product['coupon_amount'], 2, '.', '');
        $product['commission_rate'] = number_format($product['commission_rate'], 2, '.', '');
        $product['commission_amount'] = number_format($product['commission_amount'], 2, '.', '');
        
        $formattedProducts[] = $product;
    }
    
    // 获取可用的分类列表
    $categorySql = "SELECT DISTINCT category FROM taobao_products WHERE status = 1 AND category IS NOT NULL ORDER BY category";
    $categoryStmt = $conn->prepare($categorySql);
    $categoryStmt->execute();
    $categories = $categoryStmt->fetchAll(PDO::FETCH_COLUMN);
    
    // 获取可用的物料ID列表
    $materialSql = "SELECT DISTINCT material_id, COUNT(*) as count FROM taobao_products WHERE status = 1 AND material_id IS NOT NULL GROUP BY material_id";
    $materialStmt = $conn->prepare($materialSql);
    $materialStmt->execute();
    $materials = $materialStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取淘口令类型统计
    $tpwdTypesSql = "SELECT 
        SUM(CASE WHEN tpwd IS NOT NULL AND tpwd != '' AND is_fake_tpwd = 0 THEN 1 ELSE 0 END) as real_count,
        SUM(CASE WHEN tpwd IS NOT NULL AND tpwd != '' AND is_fake_tpwd = 1 THEN 1 ELSE 0 END) as fake_count,
        SUM(CASE WHEN tpwd IS NULL OR tpwd = '' THEN 1 ELSE 0 END) as none_count
        FROM taobao_products WHERE status = 1";
    $tpwdTypesStmt = $conn->prepare($tpwdTypesSql);
    $tpwdTypesStmt->execute();
    $tpwdTypesCount = $tpwdTypesStmt->fetch(PDO::FETCH_ASSOC);
    
    // 记录淘口令类型统计
    error_log("淘口令类型统计 - 真实淘口令: " . $tpwdTypesCount['real_count'] . 
             ", 模拟淘口令: " . $tpwdTypesCount['fake_count'] . 
             ", 无淘口令: " . $tpwdTypesCount['none_count']);
    
    // 如果模拟淘口令数量为0，检查是否有数据需要更新
    if ($tpwdTypesCount['fake_count'] == 0) {
        // 查询所有包含"模拟"标记的淘口令
        $checkFakeTpwdSql = "SELECT COUNT(*) FROM taobao_products WHERE tpwd LIKE '%模拟%' AND is_fake_tpwd = 0";
        $checkFakeTpwdStmt = $conn->prepare($checkFakeTpwdSql);
        $checkFakeTpwdStmt->execute();
        $potentialFakeCount = $checkFakeTpwdStmt->fetchColumn();
        
        // 查询所有以"￥"开头且长度小于15的淘口令，这些通常是模拟淘口令
        $checkShortTpwdSql = "SELECT COUNT(*) FROM taobao_products WHERE tpwd LIKE '￥%' AND LENGTH(tpwd) < 15 AND is_fake_tpwd = 0";
        $checkShortTpwdStmt = $conn->prepare($checkShortTpwdSql);
        $checkShortTpwdStmt->execute();
        $potentialShortCount = $checkShortTpwdStmt->fetchColumn();
        
        $needsFix = $potentialFakeCount > 0 || $potentialShortCount > 0;
        
        if ($needsFix) {
            error_log("发现潜在的模拟淘口令数据: 包含'模拟'标记的" . $potentialFakeCount . "条，短淘口令" . $potentialShortCount . "条，但is_fake_tpwd字段值为0");
            
            // 自动修复：将包含"模拟"标记的淘口令的is_fake_tpwd字段设置为1
            try {
                $updateQueries = [];
                $updateCounts = [];
                
                if ($potentialFakeCount > 0) {
                    $updateFakeTpwdSql = "UPDATE taobao_products SET is_fake_tpwd = 1 WHERE tpwd LIKE '%模拟%' AND is_fake_tpwd = 0";
                    $updateCounts[] = $conn->exec($updateFakeTpwdSql);
                    $updateQueries[] = "包含'模拟'标记的淘口令";
                }
                
                if ($potentialShortCount > 0) {
                    $updateShortTpwdSql = "UPDATE taobao_products SET is_fake_tpwd = 1 WHERE tpwd LIKE '￥%' AND LENGTH(tpwd) < 15 AND is_fake_tpwd = 0";
                    $updateCounts[] = $conn->exec($updateShortTpwdSql);
                    $updateQueries[] = "短淘口令";
                }
                
                $totalUpdated = array_sum($updateCounts);
                error_log("自动修复完成：已更新" . $totalUpdated . "条模拟淘口令数据（" . implode("、", $updateQueries) . "）");
                
                // 重新获取淘口令类型统计
                $tpwdTypesStmt->execute();
                $tpwdTypesCount = $tpwdTypesStmt->fetch(PDO::FETCH_ASSOC);
                error_log("修复后淘口令类型统计 - 真实淘口令: " . $tpwdTypesCount['real_count'] . 
                         ", 模拟淘口令: " . $tpwdTypesCount['fake_count'] . 
                         ", 无淘口令: " . $tpwdTypesCount['none_count']);
            } catch (Exception $e) {
                error_log("自动修复失败: " . $e->getMessage());
            }
        }
    }
    
    // 添加物料名称
    $materialCategories = getTaobaoMaterialCategories();
    $materialMap = [];
    foreach ($materialCategories as $cat) {
        $materialMap[$cat['id']] = $cat['name'];
    }
    
    foreach ($materials as &$material) {
        $material['name'] = isset($materialMap[$material['material_id']]) ? 
                           $materialMap[$material['material_id']] : 
                           '未知分类(' . $material['material_id'] . ')';
    }
    
    // 构建分页信息
    $pagination = [
        'total' => (int)$total,
        'current_page' => $page,
        'page_size' => $pageSize,
        'total_pages' => $totalPages
    ];
    
    // 返回结果
    echo json_encode([
        'error' => false,
        'data' => $formattedProducts,
        'pagination' => $pagination,
        'filters' => [
            'categories' => $categories,
            'materials' => $materials,
            'tpwd_types' => [
                ['type' => 'real', 'name' => '真实淘口令', 'count' => (int)$tpwdTypesCount['real_count']],
                ['type' => 'fake', 'name' => '模拟淘口令', 'count' => (int)$tpwdTypesCount['fake_count']],
                ['type' => 'none', 'name' => '无淘口令', 'count' => (int)$tpwdTypesCount['none_count']]
            ]
        ]
    ]);
    
} catch (Exception $e) {
    // 记录错误
    error_log('获取淘宝商品数据失败: ' . $e->getMessage());
    
    // 返回错误信息
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '获取淘宝商品数据失败: ' . $e->getMessage()
    ]);
} 