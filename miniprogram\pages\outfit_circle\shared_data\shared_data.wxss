/* 圈子数据共享页面样式 */
/* 模块4：数据共享基础模块 */

.container {
  min-height: 100vh;
  background-color: #fff;
}

/* 页面头部 */
.header {
  background: #000;
  color: #fff;
  padding: 40rpx 30rpx 30rpx;
}

.circle-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.circle-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-right: 20rpx;
}

.circle-role {
  font-size: 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.stats-bar {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 数据同步区域 */
.sync-section {
  background-color: #fff;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.sync-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.sync-desc {
  font-size: 26rpx;
  color: #666;
}

.sync-actions {
  display: flex;
  justify-content: center;
}

.sync-btn {
  background-color: #000;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.sync-btn[disabled] {
  background: #ccc;
  color: #999;
}

/* 标签页 */
.tabs {
  display: flex;
  background-color: #fff;
  margin: 0 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  background-color: #fff;
  transition: all 0.3s;
}

.tab-item.active {
  color: #000;
  background-color: #f5f5f5;
  font-weight: 600;
}

/* 内容区域 */
.content-area {
  margin: 20rpx;
}

/* 过滤栏 */
.filter-bar {
  background-color: #fff;
  padding: 8rpx 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  border: 1rpx solid #e8e8e8;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
}

.filter-label {
  font-size: 24rpx;
  color: #333;
  margin-right: 12rpx;
  white-space: nowrap;
  font-weight: 500;
  flex-shrink: 0;
  line-height: 1.2;
}

.filter-options {
  display: flex;
  gap: 12rpx;
  overflow-x: auto;
  white-space: nowrap;
  flex: 1;
  padding-right: 16rpx; /* 为滚动留出空间 */
}

/* 隐藏滚动条 */
.filter-options::-webkit-scrollbar {
  display: none;
}

.filter-option {
  font-size: 24rpx;
  color: #666;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  background-color: #f8f8f8;
  border: 1rpx solid #e8e8e8;
  transition: all 0.3s;
  white-space: nowrap;
  flex-shrink: 0;
  display: inline-block;
  line-height: 1.2;
}

.filter-option.active {
  color: #fff;
  background-color: #000;
  border-color: #000;
  font-weight: 500;
}



/* 衣橱列表 */
.wardrobes-list {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.wardrobe-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s;
}

.wardrobe-item:last-child {
  border-bottom: none;
}

.wardrobe-item:active {
  background-color: #f8f8f8;
}

.wardrobe-info {
  flex: 1;
}

.wardrobe-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.wardrobe-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

.wardrobe-badges {
  display: flex;
  gap: 8rpx;
}

.badge {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.shared-badge {
  background-color: #e8f5e8;
  color: #52c41a;
}

.own-badge {
  background-color: #fff7e6;
  color: #fa8c16;
}

.default-badge {
  background-color: #f0f4ff;
  color: #667eea;
}

.wardrobe-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.wardrobe-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.creator {
  font-size: 24rpx;
  color: #999;
}

.clothes-count {
  font-size: 24rpx;
  color: #666;
}

.wardrobe-arrow {
  color: #ccc;
  font-size: 24rpx;
}

/* 衣物列表 */
.clothes-list {
  flex: 1;
  padding: 0;
}

.clothes-item {
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  padding: 24rpx 30rpx;
  transition: background-color 0.3s;
}

.clothes-item:active {
  background-color: #f8f8f8;
}

/* 衣物头部信息 */
.clothes-header {
  margin-bottom: 16rpx;
}

.clothes-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.clothes-meta {
  display: flex;
  gap: 16rpx;
  align-items: center;
  flex-wrap: wrap;
}

.clothes-category {
  font-size: 26rpx;
  color: #666;
}

.clothes-creator {
  font-size: 24rpx;
  color: #999;
}

/* 衣物内容区域 */
.clothes-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
  position: relative;
}

/* 衣物图片容器 */
.clothes-image-container {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  flex-shrink: 0;
  overflow: hidden;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
}

.clothes-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.clothes-badges {
  display: flex;
  gap: 8rpx;
  margin-left: auto;
}



/* 穿搭列表 */
.outfits-list {
  flex: 1;
  padding: 0;
}

.outfit-item {
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  padding: 24rpx 30rpx;
  transition: background-color 0.3s;
}

.outfit-item:active {
  background-color: #f8f8f8;
}

/* 穿搭头部信息 */
.outfit-header {
  margin-bottom: 16rpx;
}

.outfit-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.outfit-meta {
  display: flex;
  gap: 16rpx;
  align-items: center;
  flex-wrap: wrap;
}

.outfit-category {
  font-size: 26rpx;
  color: #666;
}

.outfit-creator {
  font-size: 24rpx;
  color: #999;
}

/* 穿搭内容区域 */
.outfit-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
  position: relative;
}

/* 衣物容器 */
.items-container {
  flex: 1;
  min-height: 120rpx;
  position: relative;
  overflow: hidden;
}

.items-scroll {
  width: 100%;
  height: 120rpx;
  white-space: nowrap;
}

.items-list {
  display: inline-flex;
  gap: 16rpx;
  align-items: center;
  padding: 8rpx 0;
  white-space: nowrap;
}

.clothing-item {
  flex-shrink: 0;
  width: 100rpx;
  height: 100rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  overflow: hidden;
  border: 1rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clothing-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 无衣物时的占位符 */
.no-items {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 120rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.no-items-text {
  font-size: 24rpx;
  color: #ccc;
}

.outfit-badges {
  display: flex;
  gap: 8rpx;
  margin-left: auto;
}

.outfit-clothes-count {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 18rpx;
  z-index: 10;
}



/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

/* 同步弹框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.sync-modal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #fff;
  border-radius: 20rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: calc(100% - 80rpx);
}

.modal-header {
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-content {
  padding: 30rpx 40rpx;
}

.modal-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 30rpx;
  display: block;
}

.sync-options {
  margin-bottom: 30rpx;
}

.sync-option {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.sync-option:last-child {
  border-bottom: none;
}

.sync-option checkbox {
  margin-right: 20rpx;
}

/* 修改checkbox选中颜色为黑色 */
checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: #333 !important;
  border-color: #333 !important;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
  color: #fff !important;
}

.sync-option text {
  font-size: 28rpx;
  color: #333;
}

.sync-type-options {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 30rpx;
}

.option-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.sync-type-option {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}

.sync-type-option radio {
  margin-right: 20rpx;
}

/* 修改radio选中颜色为黑色 */
radio .wx-radio-input.wx-radio-input-checked {
  background-color: #333 !important;
  border-color: #333 !important;
}

radio .wx-radio-input.wx-radio-input-checked::before {
  background-color: #fff !important;
}

.sync-type-option text {
  font-size: 26rpx;
  color: #333;
}

.modal-actions {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 30rpx;
  border: none;
  font-size: 28rpx;
  background-color: transparent;
}

.cancel-btn {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.confirm-btn {
  color: #333;
  font-weight: 600;
}

.confirm-btn[disabled] {
  color: #ccc;
}
