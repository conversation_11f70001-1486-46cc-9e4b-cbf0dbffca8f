<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>和风天气API调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .card {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow: auto;
            white-space: pre-wrap;
        }
        .success { color: green; }
        .error { color: red; }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 15px;
            background: #f1f1f1;
            cursor: pointer;
            border: 1px solid #ddd;
            border-bottom: none;
            margin-right: 5px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        .tab.active {
            background: #fff;
            border-bottom: 1px solid #fff;
            margin-bottom: -1px;
        }
        .tab-content {
            display: none;
            padding: 15px;
            border: 1px solid #ddd;
        }
        .tab-content.active {
            display: block;
        }
        .notice {
            background-color: #fff3cd;
            border-left: 4px solid #ffca2c;
            padding: 12px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>和风天气API调试工具</h1>
        
        <div class="notice">
            <h3>API状态说明</h3>
            <p><strong>重要提示：</strong> 我们通过本地代理访问和风天气API，因此即使"API测试"选项卡中的直接API测试失败，天气查询和城市查询功能仍然可以正常工作。</p>
            <p>这是因为：</p>
            <ol>
                <li>直接API访问需要域名授权，而您的域名可能未在和风天气开发者平台注册</li>
                <li>本地代理使用了备选API密钥和模拟数据功能，确保即使API不可用也能返回数据</li>
            </ol>
            <p>如果天气查询和城市查询功能正常，则无需担心API测试失败。</p>
        </div>
        
        <div class="tabs">
            <div class="tab active" data-tab="weather">天气查询</div>
            <div class="tab" data-tab="city">城市查询</div>
            <div class="tab" data-tab="test">API测试</div>
        </div>
        
        <div class="tab-content active" id="weather-tab">
            <div class="card">
                <h2>天气查询</h2>
                <form id="weather-form">
                    <label for="weather-location">位置 (城市名/ID/经纬度):</label>
                    <input type="text" id="weather-location" placeholder="例如: 北京 或 101010100 或 116.41,39.92">
                    <button type="submit">查询天气</button>
                </form>
            </div>
            
            <div class="card">
                <h3>天气查询结果</h3>
                <div id="weather-result"></div>
            </div>
        </div>
        
        <div class="tab-content" id="city-tab">
            <div class="card">
                <h2>城市查询</h2>
                <form id="city-form">
                    <label for="city-name">城市名称:</label>
                    <input type="text" id="city-name" placeholder="例如: 北京、上海、guangzhou">
                    <button type="submit">查询城市ID</button>
                </form>
            </div>
            
            <div class="card">
                <h3>城市查询结果</h3>
                <div id="city-result"></div>
            </div>
        </div>
        
        <div class="tab-content" id="test-tab">
            <div class="card">
                <h2>API测试工具</h2>
                <p>此工具会测试多种方式连接和风天气API，找出能正常工作的方法。</p>
                <p class="notice"><strong>注意：</strong> 直接API测试可能全部失败但本地功能仍然正常工作。这是由于我们使用了本地代理来访问天气API。详情请参考页面顶部的说明。</p>
                <form id="test-form">
                    <label for="test-location">测试位置 (城市ID):</label>
                    <input type="text" id="test-location" placeholder="例如: 101010100 (北京)" value="101010100">
                    <button type="submit">开始测试</button>
                </form>
                <p>注意: 测试过程可能需要几秒钟，请耐心等待。</p>
            </div>
            
            <div class="card">
                <h3>测试结果</h3>
                <div id="test-result">
                    <p>点击"开始测试"按钮运行API测试...</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 切换标签页
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // 移除所有active类
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // 添加active类到当前标签和内容
                tab.classList.add('active');
                document.getElementById(tab.dataset.tab + '-tab').classList.add('active');
            });
        });
        
        // 天气查询表单
        document.getElementById('weather-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            const location = document.getElementById('weather-location').value;
            const resultDiv = document.getElementById('weather-result');
            
            resultDiv.innerHTML = '<p>正在查询天气数据...</p>';
            
            try {
                const response = await fetch(`get_weather.php?location=${encodeURIComponent(location)}`);
                const data = await response.json();
                
                if (data.success) {
                    let html = '<div class="success"><p>查询成功!</p></div>';
                    html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="error"><p>查询失败: ${data.msg}</p></div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>请求出错: ${error.message}</p></div>`;
            }
        });
        
        // 城市查询表单
        document.getElementById('city-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            const cityName = document.getElementById('city-name').value;
            const resultDiv = document.getElementById('city-result');
            
            resultDiv.innerHTML = '<p>正在查询城市数据...</p>';
            
            try {
                const response = await fetch(`get_city.php?location=${encodeURIComponent(cityName)}`);
                const data = await response.json();
                
                if (data.success) {
                    let html = '<div class="success"><p>查询成功!</p></div>';
                    html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="error"><p>查询失败: ${data.msg}</p></div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>请求出错: ${error.message}</p></div>`;
            }
        });
        
        // API测试表单
        document.getElementById('test-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            const location = document.getElementById('test-location').value;
            const resultDiv = document.getElementById('test-result');
            
            resultDiv.innerHTML = '<p>正在测试API连接，请稍候...</p>';
            
            try {
                const testUrl = `test_weather_api.php?location=${encodeURIComponent(location)}`;
                
                // 使用iframe加载HTML结果
                resultDiv.innerHTML = `<iframe src="${testUrl}" style="width:100%; height:600px; border:none;"></iframe>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>请求出错: ${error.message}</p></div>`;
            }
        });
    </script>
</body>
</html> 