/**
 * 语法修复测试工具
 * 用于验证JavaScript语法修复是否成功
 */

// 测试页面模块加载
function testPageModules() {
  console.log('=== 测试页面模块加载 ===');
  
  const testPages = [
    'pages/outfits/edit/edit',
    'pages/outfits/detail/detail',
    'pages/clothing/detail/detail',
    'utils/permission'
  ];
  
  testPages.forEach(pagePath => {
    try {
      console.log(`测试加载: ${pagePath}`);
      
      // 尝试require模块
      if (pagePath.startsWith('utils/')) {
        const module = require(`../${pagePath}`);
        console.log(`✅ ${pagePath} 加载成功`);
        
        // 如果是权限模块，测试主要函数
        if (pagePath === 'utils/permission') {
          if (typeof module.checkCirclePermission === 'function') {
            console.log('  - checkCirclePermission 函数存在');
          }
          if (typeof module.canEdit === 'function') {
            console.log('  - canEdit 函数存在');
          }
          if (typeof module.showPermissionError === 'function') {
            console.log('  - showPermissionError 函数存在');
          }
        }
      } else {
        console.log(`✅ ${pagePath} 路径有效`);
      }
    } catch (error) {
      console.error(`❌ ${pagePath} 加载失败:`, error.message);
    }
  });
}

// 测试数据结构
function testDataStructures() {
  console.log('\n=== 测试数据结构 ===');
  
  // 模拟穿搭编辑页面的data结构
  try {
    const mockEditPageData = {
      outfit: null,
      loading: true,
      showClothesSelector: false,
      clothingList: [],
      wardrobes: [],
      selectedWardrobe: 'all',
      selectedCategory: 'all',
      loadingClothes: false,
      selectedClothingId: null,
      activeItemIndex: -1,
      canvasWidth: 375,
      canvasHeight: 500,
      scaleValue: 1,
      
      // 数据源相关
      dataSource: 'personal',
      showDataSourcePopup: false,
      dataSourceOptions: [
        { key: 'personal', name: '个人数据', icon: '👤' },
        { key: 'shared', name: '共享数据', icon: '👥' },
        { key: 'all', name: '全部数据', icon: '🌐' }
      ],
      rotateValue: 0,
      
      // 权限相关
      canEdit: true,
      canSave: true,
      isOwner: true,
      userRole: '',
      permissionLoading: false,
      
      // 分类相关数据
      categories: [],
      categoryIndex: 0,
      categoryId: null,
      categoryName: '默认分类',
      loadingCategories: false,
      showCategoryModal: false,
      
      // 下拉菜单状态
      showDropdown: false,
      
      // 衣物分类相关
      clothingCategories: []
    };
    
    console.log('✅ 穿搭编辑页面数据结构验证成功');
    console.log('  - 数据源选项数量:', mockEditPageData.dataSourceOptions.length);
    console.log('  - 权限字段完整性:', {
      canEdit: typeof mockEditPageData.canEdit,
      canSave: typeof mockEditPageData.canSave,
      isOwner: typeof mockEditPageData.isOwner,
      permissionLoading: typeof mockEditPageData.permissionLoading
    });
    
  } catch (error) {
    console.error('❌ 数据结构验证失败:', error.message);
  }
}

// 测试权限工具函数
function testPermissionUtils() {
  console.log('\n=== 测试权限工具函数 ===');
  
  try {
    const permission = require('../utils/permission');
    
    // 测试函数存在性
    const requiredFunctions = [
      'checkCirclePermission',
      'canEdit',
      'canDelete',
      'canView',
      'canCreate',
      'canEditByDataSource',
      'showPermissionError',
      'checkPermissionAndExecute',
      'getRoleDescription'
    ];
    
    requiredFunctions.forEach(funcName => {
      if (typeof permission[funcName] === 'function') {
        console.log(`✅ ${funcName} 函数存在`);
      } else {
        console.error(`❌ ${funcName} 函数缺失`);
      }
    });
    
    // 测试简单的数据源判断函数
    const mockItem1 = { data_source: 'personal', user_id: 1 };
    const mockItem2 = { data_source: 'shared', user_id: 2 };
    
    const result1 = permission.canEditByDataSource(mockItem1, 1);
    const result2 = permission.canEditByDataSource(mockItem2, 1);
    
    console.log('✅ canEditByDataSource 函数测试:');
    console.log('  - 个人数据，创建者:', result1); // 应该是 true
    console.log('  - 共享数据，非创建者:', result2); // 应该是 true（需要进一步权限检查）
    
  } catch (error) {
    console.error('❌ 权限工具函数测试失败:', error.message);
  }
}

// 测试API URL构建
function testApiUrls() {
  console.log('\n=== 测试API URL构建 ===');
  
  try {
    // 模拟app.globalData
    const mockApp = {
      globalData: {
        apiBaseUrl: 'https://example.com/api',
        token: 'mock_token_123'
      }
    };
    
    // 测试权限检查URL构建
    const permissionData = {
      data_type: 'clothes',
      data_id: '123',
      operation: 'edit'
    };
    
    const queryString = Object.keys(permissionData)
      .map(key => `${key}=${encodeURIComponent(permissionData[key])}`)
      .join('&');
    
    const permissionUrl = `${mockApp.globalData.apiBaseUrl}/check_circle_permission.php?${queryString}`;
    
    console.log('✅ 权限检查URL构建成功:');
    console.log('  URL:', permissionUrl);
    
    // 测试衣物数据URL构建
    const clothesParams = {
      include_circle_data: 'true',
      data_source: 'shared',
      category: 'tops'
    };
    
    const clothesQueryString = Object.keys(clothesParams)
      .map(key => `${key}=${encodeURIComponent(clothesParams[key])}`)
      .join('&');
    
    const clothesUrl = `${mockApp.globalData.apiBaseUrl}/get_clothes.php?${clothesQueryString}`;
    
    console.log('✅ 衣物数据URL构建成功:');
    console.log('  URL:', clothesUrl);
    
  } catch (error) {
    console.error('❌ API URL构建测试失败:', error.message);
  }
}

// 主测试函数
function runAllTests() {
  console.log('开始语法修复验证测试...\n');
  
  testPageModules();
  testDataStructures();
  testPermissionUtils();
  testApiUrls();
  
  console.log('\n=== 测试完成 ===');
  console.log('如果所有测试都通过，说明语法修复成功！');
}

// 导出测试函数
module.exports = {
  testPageModules,
  testDataStructures,
  testPermissionUtils,
  testApiUrls,
  runAllTests
};

// 如果在控制台中运行
if (typeof console !== 'undefined') {
  console.log('语法修复测试工具已加载');
  console.log('运行 runAllTests() 开始测试');
}
