/**
 * AI推荐管理模块
 */
const AIRecommendation = {
    // 存储当前推荐数据
    data: {
        weatherBased: [],
        clothingBased: [],
        preferenceBased: [],
        imageAnalysisBased: []
    },
    
    // 存储分页信息
    pagination: {
        weatherBased: {
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0,
            hasMore: false
        },
        clothingBased: {
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0,
            hasMore: false
        },
        preferenceBased: {
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0,
            hasMore: false
        },
        imageAnalysisBased: {
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0,
            hasMore: false
        }
    },
    
    // 存储搜索关键字
    searchKeyword: '',
    
    /**
     * 初始化模块
     */
    init: function() {
        // 初始化标签页切换
        this.initTabs();
        
        // 初始化搜索功能
        this.initSearch();
        
        // 初始化模态框
        this.initModal();
        
        // 检查ImageViewer是否可用
        this.checkImageViewer();
        
        // 测试API连接
        this.testApiConnection();
        
        // 检查分页控件
        this.checkPaginationElements();
        
        // 加载各类型推荐数据
        this.loadAllRecommendations();
    },
    
    /**
     * 初始化标签页切换
     */
    initTabs: function() {
        const tabs = document.querySelectorAll('.recommendation-tab');
        const contents = document.querySelectorAll('.recommendation-content');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // 移除所有tab和content的active类
                tabs.forEach(t => t.classList.remove('active'));
                contents.forEach(c => c.classList.remove('active'));
                
                // 添加点击tab的active类
                tab.classList.add('active');
                
                // 获取对应的content并添加active类
                const tabId = tab.getAttribute('data-tab');
                const content = document.getElementById(tabId);
                if (content) {
                    content.classList.add('active');
                }
            });
        });
    },
    
    /**
     * 初始化搜索功能
     */
    initSearch: function() {
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.searchKeyword = searchInput.value.trim();
                
                // 重置所有类型的分页到第一页
                Object.keys(this.pagination).forEach(type => {
                    this.pagination[type].page = 1;
                });
                
                // 重新加载所有推荐数据
                this.loadAllRecommendations();
            });
        }
        
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    searchBtn.click();
                }
            });
        }
    },
    
    /**
     * 初始化模态框
     */
    initModal: function() {
        const modal = document.getElementById('recommendationModal');
        const closeButtons = modal.querySelectorAll('.modal-close');
        
        // 关闭按钮事件
        closeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                modal.style.display = 'none';
            });
        });
        
        // 点击模态框外部关闭
        window.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    },
    
    /**
     * 检查分页控件是否存在
     */
    checkPaginationElements: function() {
        console.log('检查分页控件...');
        
        const types = ['weatherBased', 'clothingBased', 'preferenceBased', 'imageAnalysisBased'];
        const elementIds = {};
        
        types.forEach(type => {
            let pageInfoId, prevBtnId, nextBtnId;
            
            switch(type) {
                case 'weatherBased':
                    pageInfoId = 'weatherPageInfo';
                    prevBtnId = 'weatherPrevBtn';
                    nextBtnId = 'weatherNextBtn';
                    break;
                case 'clothingBased':
                    pageInfoId = 'clothingPageInfo';
                    prevBtnId = 'clothingPrevBtn';
                    nextBtnId = 'clothingNextBtn';
                    break;
                case 'preferenceBased':
                    pageInfoId = 'preferencePageInfo';
                    prevBtnId = 'preferencePrevBtn';
                    nextBtnId = 'preferenceNextBtn';
                    break;
                case 'imageAnalysisBased':
                    pageInfoId = 'imageAnalysisPageInfo';
                    prevBtnId = 'imageAnalysisPrevBtn';
                    nextBtnId = 'imageAnalysisNextBtn';
                    break;
            }
            
            const pageInfo = document.getElementById(pageInfoId);
            const prevBtn = document.getElementById(prevBtnId);
            const nextBtn = document.getElementById(nextBtnId);
            
            elementIds[type] = {
                pageInfoId,
                pageInfoExists: !!pageInfo,
                prevBtnId,
                prevBtnExists: !!prevBtn,
                nextBtnId,
                nextBtnExists: !!nextBtn
            };
        });
        
        console.log('分页控件检查结果:', elementIds);
    },
    
    /**
     * 加载所有类型的推荐数据
     */
    loadAllRecommendations: function() {
        this.showLoading(true);
        
        // 加载各类型推荐数据
        this.loadWeatherBasedRecommendations();
        this.loadClothingBasedRecommendations();
        this.loadPreferenceBasedRecommendations();
        this.loadImageAnalysisBasedRecommendations();
    },
    
    /**
     * 显示/隐藏加载指示器
     * @param {boolean} show 是否显示加载指示器
     */
    showLoading: function(show) {
        const loadingElem = document.getElementById('recommendationLoading');
        if (loadingElem) {
            loadingElem.style.display = show ? 'block' : 'none';
        }
    },
    
    /**
     * 显示错误信息
     * @param {string} message 错误信息
     * @param {string} type 可选，推荐类型
     */
    showError: function(message, type) {
        const errorElem = document.getElementById('recommendationError');
        if (errorElem) {
            errorElem.textContent = message;
            errorElem.style.display = 'block';
            
            // 5秒后自动隐藏错误信息
            setTimeout(() => {
                errorElem.style.display = 'none';
            }, 5000);
        }
        
        // 如果指定了类型，在对应的表格中显示错误信息
        if (type) {
            const tableId = type + 'Table';
            const tableBody = document.getElementById(tableId);
            
            if (tableBody) {
                tableBody.innerHTML = `<tr><td colspan="6" class="no-data">加载失败: ${message}</td></tr>`;
            }
        }
    },
    
    /**
     * 隐藏错误信息
     */
    hideError: function() {
        const errorElem = document.getElementById('recommendationError');
        if (errorElem) {
            errorElem.style.display = 'none';
        }
    },
    
    /**
     * 加载基于天气的推荐
     */
    loadWeatherBasedRecommendations: function() {
        const type = 'weatherBased';
        const page = this.pagination[type].page;
        const limit = this.pagination[type].limit;
        
        // 构建请求URL
        const url = new URL('../login_backend/get_recommendation_stats.php', window.location.origin);
        url.searchParams.append('page', page);
        url.searchParams.append('limit', limit);
        url.searchParams.append('type', 'weather');
        
        if (this.searchKeyword) {
            url.searchParams.append('search', this.searchKeyword);
        }
        
        console.log('Fetching weather-based recommendations:', url.toString());
        
        // 隐藏之前的错误信息
        this.hideError();
        
        fetch(url, {
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('获取基于天气的推荐数据失败');
            }
            return response.json();
        })
        .then(data => {
            console.log('Weather API response:', data);
            
            if (data.status === 'success') {
                // 保存数据
                if (data.data && data.data.latestRecommendations) {
                    this.data[type] = data.data.latestRecommendations[type] || [];
                    console.log(`获取到${type}数据:`, this.data[type].length, '条记录');
                    
                    // 更新分页信息
                    if (data.data.pagination && data.data.pagination[type]) {
                        console.log(`API返回了${type}的分页信息:`, data.data.pagination[type]);
                        const apiPagination = data.data.pagination[type];
                        
                        // 更新分页信息，包括API返回的totalPages
                        this.pagination[type].total = apiPagination.total || 0;
                        this.pagination[type].totalPages = apiPagination.totalPages || Math.ceil(apiPagination.total / this.pagination[type].limit);
                        this.pagination[type].hasMore = apiPagination.hasMore || false;
                        
                        console.log(`设置${type}的分页信息:`, this.pagination[type]);
                    } else if (data.data.stats && data.data.stats[type]) {
                        // 如果没有分页信息但有统计信息，使用统计信息中的总数
                        console.log(`API没有返回${type}的分页信息，使用统计信息:`, data.data.stats[type]);
                        this.pagination[type].total = data.data.stats[type].total || 0;
                        this.pagination[type].totalPages = Math.ceil(this.pagination[type].total / this.pagination[type].limit);
                        console.log(`设置${type}的总数为:`, this.pagination[type].total);
                    } else {
                        console.warn(`API没有返回${type}的分页信息或统计信息`);
                    }
                    
                    // 渲染表格
                    this.renderWeatherBasedTable();
                    
                    // 更新分页控件
                    this.updatePagination(type);
                } else {
                    console.warn('API返回成功但没有推荐数据');
                    this.showNoData(type);
                }
            } else {
                throw new Error(data.message || '获取基于天气的推荐数据失败');
            }
        })
        .catch(error => {
            console.error('获取基于天气的推荐数据失败:', error);
            this.showError(error.message || '获取基于天气的推荐数据失败', type);
            this.showNoData(type);
        })
        .finally(() => {
            this.showLoading(false);
        });
    },
    
    /**
     * 加载基于衣物的推荐
     */
    loadClothingBasedRecommendations: function() {
        const type = 'clothingBased';
        const page = this.pagination[type].page;
        const limit = this.pagination[type].limit;
        
        // 构建请求URL
        const url = new URL('../login_backend/get_recommendation_stats.php', window.location.origin);
        url.searchParams.append('page', page);
        url.searchParams.append('limit', limit);
        url.searchParams.append('type', 'clothing');
        
        if (this.searchKeyword) {
            url.searchParams.append('search', this.searchKeyword);
        }
        
        console.log('Fetching clothing-based recommendations:', url.toString());
        
        // 隐藏之前的错误信息
        this.hideError();
        
        fetch(url, {
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('获取基于衣物的推荐数据失败');
            }
            return response.json();
        })
        .then(data => {
            console.log('Clothing API response:', data);
            
            if (data.status === 'success') {
                // 保存数据
                if (data.data && data.data.latestRecommendations) {
                    this.data[type] = data.data.latestRecommendations[type] || [];
                    
                    // 更新分页信息
                    if (data.data.pagination && data.data.pagination[type]) {
                        const apiPagination = data.data.pagination[type];
                        
                        // 更新分页信息，包括API返回的totalPages
                        this.pagination[type].total = apiPagination.total || 0;
                        this.pagination[type].totalPages = apiPagination.totalPages || Math.ceil(apiPagination.total / this.pagination[type].limit);
                        this.pagination[type].hasMore = apiPagination.hasMore || false;
                    } else if (data.data.stats && data.data.stats[type]) {
                        // 如果没有分页信息但有统计信息，使用统计信息中的总数
                        this.pagination[type].total = data.data.stats[type].total || 0;
                        this.pagination[type].totalPages = Math.ceil(this.pagination[type].total / this.pagination[type].limit);
                    }
                    
                    // 渲染表格
                    this.renderClothingBasedTable();
                    
                    // 更新分页控件
                    this.updatePagination(type);
                } else {
                    console.warn('API返回成功但没有推荐数据');
                    this.showNoData(type);
                }
            } else {
                throw new Error(data.message || '获取基于衣物的推荐数据失败');
            }
        })
        .catch(error => {
            console.error('获取基于衣物的推荐数据失败:', error);
            this.showError(error.message || '获取基于衣物的推荐数据失败', type);
            this.showNoData(type);
        })
        .finally(() => {
            this.showLoading(false);
        });
    },
    
    /**
     * 加载基于喜好的推荐
     */
    loadPreferenceBasedRecommendations: function() {
        const type = 'preferenceBased';
        const page = this.pagination[type].page;
        const limit = this.pagination[type].limit;
        
        // 构建请求URL
        const url = new URL('../login_backend/get_recommendation_stats.php', window.location.origin);
        url.searchParams.append('page', page);
        url.searchParams.append('limit', limit);
        url.searchParams.append('type', 'preference');
        
        if (this.searchKeyword) {
            url.searchParams.append('search', this.searchKeyword);
        }
        
        console.log('Fetching preference-based recommendations:', url.toString());
        
        // 隐藏之前的错误信息
        this.hideError();
        
        fetch(url, {
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('获取基于喜好的推荐数据失败');
            }
            return response.json();
        })
        .then(data => {
            console.log('Preference API response:', data);
            
            if (data.status === 'success') {
                // 保存数据
                if (data.data && data.data.latestRecommendations) {
                    this.data[type] = data.data.latestRecommendations[type] || [];
                    
                    // 更新分页信息
                    if (data.data.pagination && data.data.pagination[type]) {
                        this.pagination[type].total = data.data.pagination[type].total || 0;
                    } else if (data.data.stats && data.data.stats[type]) {
                        // 如果没有分页信息但有统计信息，使用统计信息中的总数
                        this.pagination[type].total = data.data.stats[type].total || 0;
                    }
                    
                    // 渲染表格
                    this.renderPreferenceBasedTable();
                    
                    // 更新分页控件
                    this.updatePagination(type);
                } else {
                    console.warn('API返回成功但没有推荐数据');
                    this.showNoData(type);
                }
            } else {
                throw new Error(data.message || '获取基于喜好的推荐数据失败');
            }
        })
        .catch(error => {
            console.error('获取基于喜好的推荐数据失败:', error);
            this.showError(error.message || '获取基于喜好的推荐数据失败', type);
            this.showNoData(type);
        })
        .finally(() => {
            this.showLoading(false);
        });
    },
    
    /**
     * 加载基于形象分析的推荐
     */
    loadImageAnalysisBasedRecommendations: function() {
        const type = 'imageAnalysisBased';
        const page = this.pagination[type].page;
        const limit = this.pagination[type].limit;
        
        // 构建请求URL
        const url = new URL('../login_backend/get_recommendation_stats.php', window.location.origin);
        url.searchParams.append('page', page);
        url.searchParams.append('limit', limit);
        url.searchParams.append('type', 'image_analysis');
        
        if (this.searchKeyword) {
            url.searchParams.append('search', this.searchKeyword);
        }
        
        console.log('Fetching image-analysis-based recommendations:', url.toString());
        
        // 隐藏之前的错误信息
        this.hideError();
        
        fetch(url, {
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('获取基于形象分析的推荐数据失败');
            }
            return response.json();
        })
        .then(data => {
            console.log('Image Analysis API response:', data);
            
            if (data.status === 'success') {
                // 保存数据
                if (data.data && data.data.latestRecommendations) {
                    this.data[type] = data.data.latestRecommendations[type] || [];
                    
                    // 更新分页信息
                    if (data.data.pagination && data.data.pagination[type]) {
                        this.pagination[type].total = data.data.pagination[type].total || 0;
                    } else if (data.data.stats && data.data.stats[type]) {
                        // 如果没有分页信息但有统计信息，使用统计信息中的总数
                        this.pagination[type].total = data.data.stats[type].total || 0;
                    }
                    
                    // 渲染表格
                    this.renderImageAnalysisBasedTable();
                    
                    // 更新分页控件
                    this.updatePagination(type);
                } else {
                    console.warn('API返回成功但没有推荐数据');
                    this.showNoData(type);
                }
            } else {
                throw new Error(data.message || '获取基于形象分析的推荐数据失败');
            }
        })
        .catch(error => {
            console.error('获取基于形象分析的推荐数据失败:', error);
            this.showError(error.message || '获取基于形象分析的推荐数据失败', type);
            this.showNoData(type);
        })
        .finally(() => {
            this.showLoading(false);
        });
    },
    
    /**
     * 显示无数据提示
     * @param {string} type 推荐类型
     */
    showNoData: function(type) {
        const tableId = type + 'Table';
        const tableBody = document.getElementById(tableId);
        
        if (tableBody) {
            const typeNames = {
                'weatherBased': '基于天气的推荐',
                'clothingBased': '基于衣物的推荐',
                'preferenceBased': '基于喜好的推荐',
                'imageAnalysisBased': '基于形象分析的推荐'
            };
            
            const typeName = typeNames[type] || type;
            tableBody.innerHTML = `<tr><td colspan="8" class="no-data">暂无${typeName}数据</td></tr>`;
            
            // 更新分页信息为0/0
            const pageInfo = document.getElementById(type.slice(0, 1).toLowerCase() + type.slice(1) + 'PageInfo');
            if (pageInfo) {
                pageInfo.textContent = '第 0/0 页';
            }
            
            // 禁用分页按钮
            const prevBtn = document.getElementById(type.slice(0, 1).toLowerCase() + type.slice(1) + 'PrevBtn');
            const nextBtn = document.getElementById(type.slice(0, 1).toLowerCase() + type.slice(1) + 'NextBtn');
            
            if (prevBtn) prevBtn.disabled = true;
            if (nextBtn) nextBtn.disabled = true;
        }
    },
    
    /**
     * 渲染基于天气的推荐表格
     */
    renderWeatherBasedTable: function() {
        const type = 'weatherBased';
        const tableBody = document.getElementById(type + 'Table');
        
        if (!tableBody) return;
        
        if (!this.data[type] || this.data[type].length === 0) {
            this.showNoData(type);
            return;
        }
        
        let html = '';
        
        this.data[type].forEach(item => {
            const userName = item.nickname || `用户ID: ${item.user_id}`;
            const weatherData = (item.recommendation_data && item.recommendation_data.weather_data) || {};
            const weatherInfo = weatherData.city ? `${weatherData.city} ${weatherData.temp || '--'}°C ${weatherData.text || ''}` : '未知天气';
            
            // 获取推荐衣物缩略图
            const clothingThumbnail = this.getRecommendedClothingThumbnail(item.recommendation_data || {});
            
            // 获取推荐理由
            const reasonHtml = this.getRecommendationReasonHtml(item.recommendation_data || {});
            
            html += `
                <tr>
                    <td>${item.id || ''}</td>
                    <td>${userName}</td>
                    <td>${item.user_id || ''}</td>
                    <td>${weatherInfo}</td>
                    <td>${clothingThumbnail}</td>
                    <td>${reasonHtml}</td>
                    <td>${this.formatDateTime(item.created_at)}</td>
                    <td>
                        <button class="action-btn view-btn" onclick="AIRecommendation.showRecommendationDetail('${type}', ${item.id})">查看详情</button>
                    </td>
                </tr>
            `;
        });
        
        tableBody.innerHTML = html;
        
        // 绑定图片查看器
        this.bindImageViewer();
    },
    
    /**
     * 渲染基于衣物的推荐表格
     */
    renderClothingBasedTable: function() {
        const type = 'clothingBased';
        const tableBody = document.getElementById(type + 'Table');
        
        if (!tableBody) return;
        
        if (!this.data[type] || this.data[type].length === 0) {
            this.showNoData(type);
            return;
        }
        
        let html = '';
        
        this.data[type].forEach(item => {
            const userName = item.nickname || `用户ID: ${item.user_id}`;
            const clothingId = item.clothing_id || '未知';
            
            // 获取推荐衣物缩略图
            const clothingThumbnail = this.getRecommendedClothingThumbnail(item.recommendation_data || {});
            
            // 获取推荐理由
            const reasonHtml = this.getRecommendationReasonHtml(item.recommendation_data || {});
            
            html += `
                <tr>
                    <td>${item.id || ''}</td>
                    <td>${userName}</td>
                    <td>${item.user_id || ''}</td>
                    <td>${clothingId}</td>
                    <td>${clothingThumbnail}</td>
                    <td>${reasonHtml}</td>
                    <td>${this.formatDateTime(item.created_at)}</td>
                    <td>
                        <button class="action-btn view-btn" onclick="AIRecommendation.showRecommendationDetail('${type}', ${item.id})">查看详情</button>
                    </td>
                </tr>
            `;
        });
        
        tableBody.innerHTML = html;
        
        // 绑定图片查看器
        this.bindImageViewer();
    },
    
    /**
     * 渲染基于喜好的推荐表格
     */
    renderPreferenceBasedTable: function() {
        const type = 'preferenceBased';
        const tableBody = document.getElementById(type + 'Table');
        
        if (!tableBody) return;
        
        if (!this.data[type] || this.data[type].length === 0) {
            this.showNoData(type);
            return;
        }
        
        let html = '';
        
        this.data[type].forEach(item => {
            const userName = item.nickname || `用户ID: ${item.user_id}`;
            const preference = item.preference || '未知喜好';
            
            // 获取推荐衣物缩略图
            const clothingThumbnail = this.getRecommendedClothingThumbnail(item.recommendation_data || {});
            
            // 获取推荐理由
            const reasonHtml = this.getRecommendationReasonHtml(item.recommendation_data || {});
            
            html += `
                <tr>
                    <td>${item.id || ''}</td>
                    <td>${userName}</td>
                    <td>${item.user_id || ''}</td>
                    <td>${preference}</td>
                    <td>${clothingThumbnail}</td>
                    <td>${reasonHtml}</td>
                    <td>${this.formatDateTime(item.created_at)}</td>
                    <td>
                        <button class="action-btn view-btn" onclick="AIRecommendation.showRecommendationDetail('${type}', ${item.id})">查看详情</button>
                    </td>
                </tr>
            `;
        });
        
        tableBody.innerHTML = html;
        
        // 绑定图片查看器
        this.bindImageViewer();
    },
    
    /**
     * 渲染基于形象分析的推荐表格
     */
    renderImageAnalysisBasedTable: function() {
        const type = 'imageAnalysisBased';
        const tableBody = document.getElementById(type + 'Table');
        
        if (!tableBody) return;
        
        if (!this.data[type] || this.data[type].length === 0) {
            this.showNoData(type);
            return;
        }
        
        let html = '';
        
        this.data[type].forEach(item => {
            const userName = item.nickname || `用户ID: ${item.user_id}`;
            const analysisId = item.analysis_id || '未知';
            
            // 获取推荐衣物缩略图
            const clothingThumbnail = this.getRecommendedClothingThumbnail(item.recommendation_data || {});
            
            // 获取推荐理由
            const reasonHtml = this.getRecommendationReasonHtml(item.recommendation_data || {});
            
            html += `
                <tr>
                    <td>${item.id || ''}</td>
                    <td>${userName}</td>
                    <td>${item.user_id || ''}</td>
                    <td>${analysisId}</td>
                    <td>${clothingThumbnail}</td>
                    <td>${reasonHtml}</td>
                    <td>${this.formatDateTime(item.created_at)}</td>
                    <td>
                        <button class="action-btn view-btn" onclick="AIRecommendation.showRecommendationDetail('${type}', ${item.id})">查看详情</button>
                    </td>
                </tr>
            `;
        });
        
        tableBody.innerHTML = html;
        
        // 绑定图片查看器
        this.bindImageViewer();
    },
    
    /**
     * 更新分页控件
     * @param {string} type 推荐类型
     */
    updatePagination: function(type) {
        // 根据不同类型构造正确的ID
        let pageInfoId, prevBtnId, nextBtnId;
        
        switch(type) {
            case 'weatherBased':
                pageInfoId = 'weatherPageInfo';
                prevBtnId = 'weatherPrevBtn';
                nextBtnId = 'weatherNextBtn';
                break;
            case 'clothingBased':
                pageInfoId = 'clothingPageInfo';
                prevBtnId = 'clothingPrevBtn';
                nextBtnId = 'clothingNextBtn';
                break;
            case 'preferenceBased':
                pageInfoId = 'preferencePageInfo';
                prevBtnId = 'preferencePrevBtn';
                nextBtnId = 'preferenceNextBtn';
                break;
            case 'imageAnalysisBased':
                pageInfoId = 'imageAnalysisPageInfo';
                prevBtnId = 'imageAnalysisPrevBtn';
                nextBtnId = 'imageAnalysisNextBtn';
                break;
            default:
                console.error('未知的推荐类型:', type);
                return;
        }
        
        // 获取分页控件元素
        const pageInfo = document.getElementById(pageInfoId);
        const prevBtn = document.getElementById(prevBtnId);
        const nextBtn = document.getElementById(nextBtnId);
        
        console.log('分页控件元素:', {type, pageInfoId, pageInfo, prevBtnId, prevBtn, nextBtnId, nextBtn});
        
        if (!pageInfo || !prevBtn || !nextBtn) {
            console.error(`找不到分页控件元素: ${type}`, {pageInfoId, prevBtnId, nextBtnId});
            return;
        }
        
        const pagination = this.pagination[type];
        console.log('当前分页状态:', JSON.stringify(pagination));
        
        // 确保总数至少为实际获取的数据长度
        if (this.data[type] && this.data[type].length > 0) {
            if (pagination.total === 0) {
                // 如果API没有返回总数或总数为0，但有数据，则以实际数据数量作为总数的下限
                pagination.total = Math.max(pagination.total, this.data[type].length + (pagination.page - 1) * pagination.limit);
                console.log(`正在设置${type}的总数为: ${pagination.total}`);
            }
        }
        
        // 计算总页数，如果总数为0，至少有1页
        const totalPages = pagination.totalPages || Math.max(1, Math.ceil(pagination.total / pagination.limit));
        
        // 更新页面信息
        pageInfo.textContent = `第 ${pagination.page}/${totalPages} 页`;
        
        // 更新按钮状态
        prevBtn.disabled = pagination.page <= 1;
        nextBtn.disabled = pagination.page >= totalPages || pagination.total === 0;
        
        // 移除旧的事件监听器（避免重复绑定）
        prevBtn.onclick = null;
        nextBtn.onclick = null;
        
        // 添加按钮事件
        prevBtn.onclick = () => {
            console.log(`点击了${type}的上一页按钮, 当前页: ${pagination.page}`);
            if (pagination.page > 1) {
                pagination.page--;
                this.loadRecommendationsByType(type);
            }
        };
        
        nextBtn.onclick = () => {
            console.log(`点击了${type}的下一页按钮, 当前页: ${pagination.page}, 总页数: ${totalPages}`);
            if (pagination.page < totalPages) {
                pagination.page++;
                this.loadRecommendationsByType(type);
            }
        };
        
        // 调试信息
        console.log(`更新${type}分页 - 当前页: ${pagination.page}, 总页数: ${totalPages}, 每页数量: ${pagination.limit}, 总数据量: ${pagination.total}`);
    },
    
    /**
     * 根据类型加载推荐数据
     * @param {string} type 推荐类型
     */
    loadRecommendationsByType: function(type) {
        switch (type) {
            case 'weatherBased':
                this.loadWeatherBasedRecommendations();
                break;
            case 'clothingBased':
                this.loadClothingBasedRecommendations();
                break;
            case 'preferenceBased':
                this.loadPreferenceBasedRecommendations();
                break;
            case 'imageAnalysisBased':
                this.loadImageAnalysisBasedRecommendations();
                break;
        }
    },
    
    /**
     * 显示推荐详情
     * @param {string} type 推荐类型
     * @param {number} id 推荐ID
     */
    showRecommendationDetail: function(type, id) {
        // 查找对应的推荐数据
        const item = this.findRecommendationById(type, id);
        
        if (!item) {
            alert('未找到对应的推荐数据');
            return;
        }
        
        // 获取模态框元素
        const modal = document.getElementById('recommendationModal');
        const modalBody = document.getElementById('recommendationModalBody');
        
        if (!modal || !modalBody) return;
        
        // 生成详情HTML
        const detailHtml = this.generateDetailHtml(type, item);
        
        // 设置模态框内容
        modalBody.innerHTML = detailHtml;
        
        // 显示模态框
        modal.style.display = 'block';
    },
    
    /**
     * 根据ID查找推荐数据
     * @param {string} type 推荐类型
     * @param {number} id 推荐ID
     * @returns {Object|null} 推荐数据或null
     */
    findRecommendationById: function(type, id) {
        if (!this.data[type]) return null;
        
        return this.data[type].find(item => item.id == id) || null;
    },
    
    /**
     * 生成详情HTML
     * @param {string} type 推荐类型
     * @param {Object} item 推荐数据
     * @returns {string} HTML字符串
     */
    generateDetailHtml: function(type, item) {
        const userName = item.nickname || `用户ID: ${item.user_id}`;
        const userAvatar = item.avatar_url || 'https://images.alidog.cn/default/avatar.jpg';
        const timestamp = this.formatDateTime(item.created_at);
        
        // 提取推荐数据
        const data = item.recommendation_data || {};
        
        // 获取衣物图片和推荐理由
        const clothingItems = [];
        const categories = ['top', 'bottom', 'outerwear', 'shoes', 'accessories', 'bag'];
        
        categories.forEach(category => {
            if (data[category] && data[category].image_url) {
                clothingItems.push({
                    category: this.getCategoryName(category),
                    imageUrl: data[category].image_url,
                    name: data[category].name || '',
                    reason: data[category].reason || '无具体推荐理由' // 获取每件衣物单独的推荐理由
                });
            }
        });
        
        // 提取总体推荐理由、穿搭总结和总结
        let outfitSummary = data.outfit_summary || '';
        let overallReason = data.reason || '';
        let summary = data.summary || '';
        
        // 根据类型生成特定内容
        let typeSpecificHtml = '';
        
        if (type === 'weatherBased') {
            const weatherData = data.weather_data || {};
            const weatherInfo = weatherData ? `${weatherData.city || '未知城市'} ${weatherData.temp || '--'}°C ${weatherData.text || '未知天气'}` : '未知天气信息';
            typeSpecificHtml = `<div class="weather-info">天气条件: ${weatherInfo}</div>`;
        } else if (type === 'clothingBased') {
            typeSpecificHtml = `<div class="clothing-info">基准衣物ID: ${item.clothing_id || '未知'}</div>`;
        } else if (type === 'preferenceBased') {
            typeSpecificHtml = `<div class="preference-info">用户喜好: ${item.preference || '未指定'}</div>`;
        } else if (type === 'imageAnalysisBased') {
            typeSpecificHtml = `<div class="analysis-info">形象分析ID: ${item.analysis_id || '未知'}</div>`;
        }
        
        // 生成HTML
        let html = `
            <div class="recommendation-item">
                <div class="recommendation-header">
                    <img class="user-avatar" src="${userAvatar}" alt="${userName}">
                    <div class="user-info">
                        <div class="user-name">${userName}</div>
                        <div class="timestamp">${timestamp}</div>
                    </div>
                </div>
                ${typeSpecificHtml}
                
                <h4 style="margin-top:15px;margin-bottom:10px;">推荐的衣物</h4>
                <div class="recommendation-clothes" style="margin-bottom: 25px;">
                    ${this.renderClothingItemsWithReasons(clothingItems)}
                </div>
                
                ${outfitSummary ? `<div class="recommendation-reason"><h4>穿搭总结</h4>${outfitSummary}</div>` : ''}
                ${overallReason ? `<div class="recommendation-reason"><h4>总体推荐理由</h4>${overallReason}</div>` : ''}
                ${summary ? `<div class="recommendation-summary"><h4>推荐总结</h4>${summary}</div>` : ''}
            </div>
        `;
        
        return html;
    },
    
    /**
     * 渲染带有推荐理由的衣物项列表
     * @param {Array} items 衣物项目
     * @returns {string} HTML字符串
     */
    renderClothingItemsWithReasons: function(items) {
        if (!items || items.length === 0) {
            return '<div class="no-clothing-message">暂无衣物</div>';
        }
        
        // 使用本地默认图片路径，避免远程请求失败
        const defaultImagePath = 'images/default-cloth.png';
        
        let html = '';
        
        items.forEach(item => {
            html += `
                <div class="clothing-item" style="border:1px solid #eee; padding:10px; border-radius:5px; display:flex; margin-bottom:15px;">
                    <div style="flex:0 0 150px; margin-right:15px;">
                        <img src="${item.imageUrl}" alt="${item.name || item.category || '衣物'}" 
                             class="viewable-image"
                             data-origin="${item.imageUrl}"
                             onerror="this.onerror=null;this.src='${defaultImagePath}';"
                             style="width:150px; height:150px; object-fit:cover; cursor:pointer; border-radius:4px;">
                    </div>
                    <div style="flex:1; overflow:hidden;">
                        <div class="clothing-category" style="font-weight:bold; margin-bottom:8px; font-size:16px;">${item.category}</div>
                        <div class="clothing-reason" style="font-size:0.95em; color:#333; line-height:1.5; background:#f9f9f9; padding:10px; border-radius:4px; max-height:none; overflow-y:visible;">
                            <strong>推荐理由:</strong> ${item.reason}
                        </div>
                    </div>
                </div>
            `;
        });
        
        return html;
    },
    
    /**
     * 获取衣物类别名称
     * @param {string} category 类别代码
     * @returns {string} 类别名称
     */
    getCategoryName: function(category) {
        const categoryMap = {
            'top': '上装',
            'bottom': '下装',
            'outerwear': '外套',
            'shoes': '鞋子',
            'accessories': '配饰',
            'bag': '包包'
        };
        
        return categoryMap[category] || category;
    },
    
    /**
     * 格式化日期时间为可读字符串
     * @param {string} dateString ISO日期字符串
     * @returns {string} 格式化后的日期时间字符串
     */
    formatDateTime: function(dateString) {
        if (!dateString) return '未知时间';
        
        const date = new Date(dateString);
        
        if (isNaN(date.getTime())) {
            return dateString;
        }
        
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    },
    
    /**
     * 调试函数 - 测试API连接
     */
    testApiConnection: function() {
        console.log('Testing API connection...');
        
        // 构建请求URL
        const url = new URL('../login_backend/get_recommendation_stats.php', window.location.origin);
        
        fetch(url, {
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            }
        })
        .then(response => {
            console.log('API Response status:', response.status);
            console.log('API Response headers:', response.headers);
            return response.json();
        })
        .then(data => {
            console.log('API Response data:', data);
        })
        .catch(error => {
            console.error('API Connection test failed:', error);
        });
    },
    
    /**
     * 获取推荐衣物的缩略图HTML
     * @param {Object} data 推荐数据
     * @returns {string} 缩略图HTML
     */
    getRecommendedClothingThumbnail: function(data) {
        if (!data) return '<span>无数据</span>';
        
        // 获取所有有图片的衣物
        const categories = ['top', 'bottom', 'outerwear', 'shoes', 'accessories', 'bag'];
        let thumbnailsHtml = '';
        let hasImages = false;
        
        // 使用本地默认图片路径，避免远程请求失败
        const defaultImagePath = 'images/default-cloth.png';
        
        for (const category of categories) {
            if (data[category] && data[category].image_url) {
                hasImages = true;
                const imageUrl = data[category].image_url;
                const categoryName = this.getCategoryName(category);
                thumbnailsHtml += `<img src="${imageUrl}" alt="${categoryName}" 
                                      class="clothing-thumbnail viewable-image" 
                                      data-origin="${imageUrl}"
                                      onerror="this.onerror=null;this.src='${defaultImagePath}';" 
                                      title="${categoryName}" 
                                      style="margin-right: 5px; cursor: pointer;">`;
            }
        }
        
        if (!hasImages) return '<span>无图片</span>';
        
        return `<div style="display: flex; flex-wrap: wrap; gap: 5px;">${thumbnailsHtml}</div>`;
    },
    
    /**
     * 获取推荐理由的HTML
     * @param {Object} data 推荐数据
     * @returns {string} 推荐理由HTML
     */
    getRecommendationReasonHtml: function(data) {
        if (!data) return '<span>无推荐理由</span>';
        
        // 优先使用outfit_summary字段，其次是reason，最后是summary
        const outfitSummary = data.outfit_summary || '';
        const reason = data.reason || '';
        const summary = data.summary || '';
        
        if (!outfitSummary && !reason && !summary) return '<span>无推荐理由</span>';
        
        // 优先级：outfit_summary > reason > summary
        const displayText = outfitSummary || reason || summary;
        const shortText = displayText.length > 50 ? displayText.substring(0, 50) + '...' : displayText;
        
        return `
            <div class="recommendation-reason-tooltip">
                <div class="recommendation-reason-short">${shortText}</div>
                <div class="recommendation-reason-full">${displayText}</div>
            </div>
        `;
    },
    
    /**
     * 检查ImageViewer是否可用
     */
    checkImageViewer: function() {
        if (typeof ImageViewer === 'undefined') {
            console.error('ImageViewer 未加载，图片查看功能将不可用');
        } else {
            console.log('ImageViewer 已加载，图片查看功能可用');
        }
    },
    
    /**
     * 绑定图片查看器
     */
    bindImageViewer: function() {
        if (typeof ImageViewer === 'undefined') {
            console.error('ImageViewer 未加载，无法绑定图片查看功能');
            return;
        }
        
        console.log('正在绑定图片查看器...');
        
        // 延迟执行，确保DOM已完全渲染
        setTimeout(() => {
            // 绑定所有带有viewable-image类的图片
            ImageViewer.bindImages('.viewable-image');
        }, 300);
    }
}; 