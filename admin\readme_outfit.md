# 穿搭管理功能说明

## 功能概述

穿搭管理功能是次元衣柜后台系统的一个新增模块，用于管理用户创建的穿搭组合。该功能允许管理员查看所有用户的穿搭数据，包括穿搭基本信息和包含的衣物详情。

## 新增文件

1. `outfit_list.html` - 穿搭列表页面
2. `outfit_details.html` - 穿搭详情页面
3. `js/outfit_list.js` - 穿搭列表页面的JavaScript逻辑
4. `js/outfit_details.js` - 穿搭详情页面的JavaScript逻辑
5. `update_menu.js` - 菜单更新脚本（仅用于开发阶段）
6. `readme_outfit.md` - 功能说明文档（本文件）

## 功能说明

### 穿搭列表 (outfit_list.html)

穿搭列表页面展示所有用户创建的穿搭，包含以下功能：

- 分页显示穿搭列表
- 搜索功能：支持按穿搭名称或用户ID搜索
- 查看详情：点击查看按钮可跳转到穿搭详情页

列表展示字段：
- ID: 穿搭唯一标识
- 预览图: 穿搭缩略图
- 名称: 穿搭名称
- 用户ID: 创建该穿搭的用户ID
- 分类: 穿搭所属分类
- 衣物数: 该穿搭包含的衣物数量
- 创建时间: 穿搭创建的时间
- 操作: 提供查看详情的按钮

### 穿搭详情 (outfit_details.html)

穿搭详情页面展示特定穿搭的详细信息，包含以下功能：

- 显示穿搭基本信息（ID、名称、描述、用户ID、分类、创建时间等）
- 显示穿搭预览图
- 展示该穿搭包含的所有衣物列表
- 提供返回列表的链接

## 开发说明

### API使用

穿搭管理功能使用以下API：

1. `/admin_get_outfits.php` - 管理员接口，获取穿搭列表和穿搭详情
2. `/admin_get_outfit_clothes.php` - 管理员接口，获取穿搭中包含的衣物列表

这些API专为管理后台设计，需要管理员token才能访问。身份验证使用 `Auth` 类的 `verifyAdminToken` 方法。

### 安装说明

1. 复制所有新增文件到相应目录：
   - HTML和JS文件复制到`admin/`目录
   - PHP接口文件复制到`login_backend/`目录
2. 在各个页面的侧边栏菜单中添加穿搭管理入口
   - 可以手动添加，也可以使用提供的`update_menu.js`脚本批量处理
   - 如果使用脚本，请在Node.js环境下执行：`node update_menu.js`

### 更新日志

- 2025-05-14: 初始版本发布

## 注意事项

- 本功能仅提供查看功能，不支持创建、编辑或删除穿搭
- 穿搭管理的数据依赖于前端小程序用户创建的穿搭数据
- 页面中使用的默认图片可能需要替换为实际项目中的图片 