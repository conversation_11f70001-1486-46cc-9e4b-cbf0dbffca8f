/**
 * 推荐穿搭分类管理
 */
const CategoryManager = {
    // 当前页码
    currentPage: 1,
    
    // 每页显示数量
    perPage: 20,
    
    // 搜索关键词
    searchKeyword: '',
    
    // 状态过滤
    statusFilter: '',
    
    /**
     * 初始化分类管理功能
     */
    init: function() {
        // 检查登录状态
        if (!checkAdminLogin()) {
            return;
        }
        
        // 加载分类列表
        this.loadCategories();
        
        // 绑定事件
        this.bindEvents();
    },
    
    /**
     * 绑定事件处理
     */
    bindEvents: function() {
        // 搜索按钮点击
        $('#searchBtn').on('click', () => {
            this.searchKeyword = $('#searchInput').val().trim();
            this.currentPage = 1;
            this.loadCategories();
        });
        
        // 搜索框回车
        $('#searchInput').on('keypress', (e) => {
            if (e.which === 13) {
                this.searchKeyword = $('#searchInput').val().trim();
                this.currentPage = 1;
                this.loadCategories();
            }
        });
        
        // 状态过滤器变化
        $('#statusFilter').on('change', () => {
            this.statusFilter = $('#statusFilter').val();
            this.currentPage = 1;
            this.loadCategories();
        });
        
        // 重置过滤器
        $('#resetFilterBtn').on('click', () => {
            $('#searchInput').val('');
            $('#statusFilter').val('');
            this.searchKeyword = '';
            this.statusFilter = '';
            this.currentPage = 1;
            this.loadCategories();
        });
        
        // 删除分类确认
        $('#confirmDeleteBtn').on('click', () => {
            const categoryId = $('#confirmDeleteBtn').data('category-id');
            this.deleteCategory(categoryId);
        });
        
        // 添加分类按钮
        $('#addCategoryBtn').on('click', () => {
            window.location.href = 'recommended_category_edit.html';
        });
    },
    
    /**
     * 加载分类列表
     */
    loadCategories: function() {
        // 显示加载中
        $('#categoryTableBody').html('<tr><td colspan="8" class="text-center">加载中...</td></tr>');
        
        // 构建API请求URL
        let apiUrl = `${config.apiBaseUrl}/admin_get_recommended_categories.php?page=${this.currentPage}&per_page=${this.perPage}`;
        
        // 添加搜索条件
        if (this.searchKeyword) {
            apiUrl += `&search=${encodeURIComponent(this.searchKeyword)}`;
        }
        
        // 添加状态过滤
        if (this.statusFilter !== '') {
            apiUrl += `&status=${this.statusFilter}`;
        }
        
        // 请求分类数据
        fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Authorization': getAdminToken()
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应不正常');
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '获取分类数据失败');
            }
            
            this.renderCategories(data.data);
            this.renderPagination(data.pagination);
        })
        .catch(error => {
            console.error('获取分类列表失败:', error);
            $('#categoryTableBody').html(`<tr><td colspan="8" class="text-center text-danger">加载失败: ${error.message}</td></tr>`);
            $('#paginationInfo').text('加载失败');
            $('#pagination').empty();
        });
    },
    
    /**
     * 渲染分类数据到表格
     * @param {Array} categories 分类数据数组
     */
    renderCategories: function(categories) {
        const tableBody = $('#categoryTableBody');
        tableBody.empty();
        
        if (categories.length === 0) {
            tableBody.html('<tr><td colspan="8" class="text-center">没有找到匹配的分类</td></tr>');
            return;
        }
        
        categories.forEach(category => {
            const statusClass = category.status == 1 ? 'status-active' : 'status-inactive';
            const statusText = category.status == 1 ? '启用' : '禁用';
            const createDate = new Date(category.created_at).toLocaleString();
            
            const row = `
                <tr>
                    <td>${category.id}</td>
                    <td><strong>${category.name}</strong></td>
                    <td>${category.description || '-'}</td>
                    <td><span class="outfit-count">${category.outfit_count}</span></td>
                    <td>${category.sort_order}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td>${createDate}</td>
                    <td class="action-buttons">
                        <a href="recommended_category_edit.html?id=${category.id}" class="btn btn-sm btn-outline-primary">
                            <i class="fa fa-edit"></i> 编辑
                        </a>
                        <button class="btn btn-sm btn-outline-danger ml-1 delete-btn" data-id="${category.id}" data-name="${category.name}" ${category.outfit_count > 0 ? 'disabled' : ''}>
                            <i class="fa fa-trash"></i> 删除
                        </button>
                    </td>
                </tr>
            `;
            
            tableBody.append(row);
        });
        
        // 绑定删除按钮事件
        $('.delete-btn').on('click', (e) => {
            const btn = $(e.currentTarget);
            const categoryId = btn.data('id');
            const categoryName = btn.data('name');
            
            // 设置模态框内容
            $('#deleteCategoryName').text(categoryName);
            $('#confirmDeleteBtn').data('category-id', categoryId);
            
            // 显示确认模态框
            $('#deleteCategoryModal').modal('show');
        });
    },
    
    /**
     * 渲染分页
     * @param {Object} pagination 分页信息
     */
    renderPagination: function(pagination) {
        const paginationInfo = $('#paginationInfo');
        const paginationNav = $('#pagination');
        
        // 更新分页信息
        paginationInfo.text(`显示 ${pagination.total} 条结果中的 ${(pagination.current_page - 1) * pagination.per_page + 1} - ${Math.min(pagination.current_page * pagination.per_page, pagination.total)}`);
        
        // 清空分页导航
        paginationNav.empty();
        
        // 如果只有一页，不显示分页
        if (pagination.total_pages <= 1) {
            return;
        }
        
        // 添加上一页按钮
        paginationNav.append(`
            <li class="page-item ${pagination.current_page === 1 ? 'disabled' : ''}">
                <a class="page-link" href="javascript:void(0);" data-page="${pagination.current_page - 1}">
                    <i class="fa fa-angle-left"></i>
                </a>
            </li>
        `);
        
        // 计算分页范围
        let startPage = Math.max(1, pagination.current_page - 2);
        let endPage = Math.min(pagination.total_pages, startPage + 4);
        
        if (endPage - startPage < 4) {
            startPage = Math.max(1, endPage - 4);
        }
        
        // 添加页码按钮
        for (let i = startPage; i <= endPage; i++) {
            paginationNav.append(`
                <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                    <a class="page-link" href="javascript:void(0);" data-page="${i}">${i}</a>
                </li>
            `);
        }
        
        // 添加下一页按钮
        paginationNav.append(`
            <li class="page-item ${pagination.current_page === pagination.total_pages ? 'disabled' : ''}">
                <a class="page-link" href="javascript:void(0);" data-page="${pagination.current_page + 1}">
                    <i class="fa fa-angle-right"></i>
                </a>
            </li>
        `);
        
        // 绑定分页点击事件
        $('.page-link').on('click', (e) => {
            const page = $(e.currentTarget).data('page');
            if (page && page !== this.currentPage) {
                this.currentPage = page;
                this.loadCategories();
                // 滚动到顶部
                window.scrollTo(0, 0);
            }
        });
    },
    
    /**
     * 删除分类
     * @param {number} categoryId 分类ID
     */
    deleteCategory: function(categoryId) {
        fetch(`${config.apiBaseUrl}/admin_delete_recommended_category.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': getAdminToken()
            },
            body: JSON.stringify({
                category_id: categoryId
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应不正常');
            }
            return response.json();
        })
        .then(data => {
            // 隐藏模态框
            $('#deleteCategoryModal').modal('hide');
            
            if (data.success) {
                // 删除成功，显示提示
                showToast('删除成功', 'success');
                
                // 重新加载列表
                this.loadCategories();
            } else {
                throw new Error(data.error || '删除分类失败');
            }
        })
        .catch(error => {
            console.error('删除分类失败:', error);
            
            // 隐藏模态框
            $('#deleteCategoryModal').modal('hide');
            
            // 显示错误提示
            showToast(`删除失败: ${error.message}`, 'error');
        });
    }
}; 