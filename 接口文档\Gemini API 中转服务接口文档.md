# Gemini API 中转服务接口文档

## 接口概述

该接口为Gemini AI图片分析服务的中转API，可用于解析图片内容并以JSON格式返回结构化数据。

- **接口URL**: `https://www.furrywoo.com/gemini/api.php`
- **请求方式**: POST
- **数据格式**: multipart/form-data
- **编码要求**: UTF-8

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| image | File | 是 | 要分析的图片文件 |
| image_base64 | String | 否 | 图片的base64编码数据(与image二选一) |
| prompt | String | 否 | 自定义提示词，默认为"理解并帮我将图片中的内容按照json的格式反馈给我" |

## 响应数据

接口返回JSON格式数据，主要结构如下：

```json
{
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "JSON格式的识别结果"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0,
      "safetyRatings": [...]
    }
  ],
  "promptFeedback": {
    "safetyRatings": [...]
  }
}
```

其中，`candidates[0].content.parts[0].text`字段包含了图片分析的JSON结果，通常结构为：

```json
{
  "衣物类别": "上衣、裤子、裙子、外套、鞋子、包包、配饰中的一个",
  "衣物标签": ["春季/夏季/秋季/冬季", "休闲/通勤/派对/运动", "标签1", "标签2", "标签3", "标签4", "标签5"],
  "衣物信息": {
    "衣物名称": "名称",
    "颜色": "颜色"
  }
}
```

## 错误码

| 错误码 | 说明 |
| ----- | ---- |
| 400 | 请求参数错误，如未提供图片数据 |
| 405 | 请求方法不允许，仅支持POST |
| 500 | 服务器内部错误 |

## PHP后端调用示例

以下是PHP后端调用该接口的示例代码：

```php
<?php
// 设置允许上传的图片大小
ini_set('upload_max_filesize', '10M');
ini_set('post_max_size', '10M');

/**
 * 发送图片到Gemini API进行分析
 * 
 * @param string $imagePath 本地图片路径
 * @param string $prompt 可选提示词
 * @return array 解析后的响应数据
 */
function analyzeImageWithGemini($imagePath, $prompt = null) {
    // API端点
    $apiUrl = 'https://www.furrywoo.com/gemini/api.php';
    
    // 检查文件是否存在
    if (!file_exists($imagePath)) {
        return ['error' => '图片文件不存在'];
    }
    
    // 准备cURL会话
    $ch = curl_init();
    
    // 准备表单数据
    $postFields = [];
    
    // 添加图片文件
    $postFields['image'] = new CURLFile($imagePath);
    
    // 添加提示词(如果有)
    if ($prompt) {
        $postFields['prompt'] = $prompt;
    }
    
    // 设置cURL选项
    curl_setopt_array($ch, [
        CURLOPT_URL => $apiUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $postFields,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => true
    ]);
    
    // 执行请求
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    // 关闭cURL会话
    curl_close($ch);
    
    // 处理响应
    if ($error) {
        return ['error' => '请求错误: ' . $error];
    }
    
    if ($httpCode !== 200) {
        return ['error' => 'HTTP错误: ' . $httpCode, 'response' => $response];
    }
    
    // 解析JSON响应
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        return ['error' => 'JSON解析错误: ' . json_last_error_msg(), 'raw' => $response];
    }
    
    // 提取识别结果
    if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
        try {
            $textContent = $result['candidates'][0]['content']['parts'][0]['text'];
            // 尝试解析内容中的JSON (Gemini可能会在JSON外包含其他文本)
            if (preg_match('/{.*}/s', $textContent, $matches)) {
                $jsonContent = json_decode($matches[0], true);
                if ($jsonContent) {
                    return ['success' => true, 'data' => $jsonContent, 'raw' => $result];
                }
            }
            // 如果无法解析JSON，返回原始文本
            return ['success' => true, 'text' => $textContent, 'raw' => $result];
        } catch (Exception $e) {
            return ['error' => '处理响应出错: ' . $e->getMessage(), 'raw' => $result];
        }
    }
    
    return ['success' => false, 'raw' => $result, 'message' => '未找到有效的识别结果'];
}

/**
 * 使用示例
 */
// $imagePath = '/path/to/clothing_image.jpg';
// $result = analyzeImageWithGemini($imagePath);
// 
// if (isset($result['success']) && $result['success']) {
//     // 处理成功的情况
//     if (isset($result['data'])) {
//         $clothingData = $result['data'];
//         echo "衣物类别: " . $clothingData['衣物类别'] . "\n";
//         echo "衣物标签: " . implode(', ', $clothingData['衣物标签']) . "\n";
//         echo "衣物名称: " . $clothingData['衣物信息']['衣物名称'] . "\n";
//         echo "颜色: " . $clothingData['衣物信息']['颜色'] . "\n";
//     } else {
//         echo "文本响应: " . $result['text'] . "\n";
//     }
// } else {
//     // 处理错误情况
//     echo "错误: " . ($result['error'] ?? '未知错误') . "\n";
// }
?>
```

## Base64编码图片示例

如果您需要使用Base64编码的图片而非文件上传，可以使用以下代码：

```php
<?php
/**
 * 使用Base64编码图片发送到Gemini API
 * 
 * @param string $base64Image Base64编码的图片数据
 * @param string $prompt 可选提示词
 * @return array 解析后的响应数据
 */
function analyzeBase64ImageWithGemini($base64Image, $prompt = null) {
    // API端点
    $apiUrl = 'https://www.furrywoo.com/gemini/api.php';
    
    // 检查Base64数据
    if (empty($base64Image)) {
        return ['error' => 'Base64图片数据为空'];
    }
    
    // 准备cURL会话
    $ch = curl_init();
    
    // 准备POST数据
    $postData = [
        'image_base64' => $base64Image
    ];
    
    // 添加提示词(如果有)
    if ($prompt) {
        $postData['prompt'] = $prompt;
    }
    
    // 设置cURL选项
    curl_setopt_array($ch, [
        CURLOPT_URL => $apiUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $postData,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => true
    ]);
    
    // 执行请求
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    // 关闭cURL会话
    curl_close($ch);
    
    // 处理响应 (与文件上传版本相同的处理逻辑)
    if ($error) {
        return ['error' => '请求错误: ' . $error];
    }
    
    if ($httpCode !== 200) {
        return ['error' => 'HTTP错误: ' . $httpCode, 'response' => $response];
    }
    
    // 解析JSON响应
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        return ['error' => 'JSON解析错误: ' . json_last_error_msg(), 'raw' => $response];
    }
    
    // 提取识别结果
    if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
        try {
            $textContent = $result['candidates'][0]['content']['parts'][0]['text'];
            // 尝试解析内容中的JSON
            if (preg_match('/{.*}/s', $textContent, $matches)) {
                $jsonContent = json_decode($matches[0], true);
                if ($jsonContent) {
                    return ['success' => true, 'data' => $jsonContent, 'raw' => $result];
                }
            }
            // 如果无法解析JSON，返回原始文本
            return ['success' => true, 'text' => $textContent, 'raw' => $result];
        } catch (Exception $e) {
            return ['error' => '处理响应出错: ' . $e->getMessage(), 'raw' => $result];
        }
    }
    
    return ['success' => false, 'raw' => $result, 'message' => '未找到有效的识别结果'];
}

// 使用示例
// $imagePath = '/path/to/clothing_image.jpg';
// $imageData = file_get_contents($imagePath);
// $base64Image = base64_encode($imageData);
// 
// // 或者从Data URI中提取Base64部分
// // $dataUri = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQE...";
// // preg_match('/data:image\/([^;]+);base64,(.*)/', $dataUri, $matches);
// // $base64Image = $matches[2] ?? '';
// 
// $result = analyzeBase64ImageWithGemini($base64Image);
// 
// if (isset($result['success']) && $result['success']) {
//     if (isset($result['data'])) {
//         $clothingData = $result['data'];
//         echo "衣物类别: " . $clothingData['衣物类别'] . "\n";
//         echo "衣物标签: " . implode(', ', $clothingData['衣物标签']) . "\n";
//         echo "衣物名称: " . $clothingData['衣物信息']['衣物名称'] . "\n";
//         echo "颜色: " . $clothingData['衣物信息']['颜色'] . "\n";
//     } else {
//         echo "文本响应: " . $result['text'] . "\n";
//     }
// } else {
//     echo "错误: " . ($result['error'] ?? '未知错误') . "\n";
// }
?>
```

## 注意事项

1. 设置合理的超时时间，图片分析可能需要较长处理时间
2. 图片大小建议控制在2MB以内，过大的图片可能导致请求超时
3. 服务器配置需允许足够大的POST请求和文件上传大小
4. Gemini API偶尔可能无法严格按照指定格式返回JSON，需要做好错误处理
5. 建议在生产环境中添加额外的错误重试和日志记录机制

## 常见问题排查

- **返回400错误**: 检查是否正确提供了图片数据
- **返回500错误**: 服务器内部错误，可能是图片处理失败或Gemini API暂时不可用
- **请求超时**: 尝试减小图片尺寸或增加超时设置
- **返回结果不是JSON**: Gemini可能无法识别图片中的内容，或者返回的是文本描述而非结构化数据
