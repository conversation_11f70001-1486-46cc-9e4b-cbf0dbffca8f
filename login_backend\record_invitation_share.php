<?php
// 记录邀请分享行为API
// 模块3：邀请分享模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

// 验证必需参数
if (!isset($input['share_type']) || empty(trim($input['share_type']))) {
    echo json_encode([
        'status' => 'error',
        'message' => '分享类型不能为空'
    ]);
    exit;
}

$shareType = trim($input['share_type']);
$allowedTypes = ['wechat', 'timeline', 'copy'];

if (!in_array($shareType, $allowedTypes)) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效的分享类型'
    ]);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查找用户所在的圈子
    $findCircleSql = "SELECT c.id, c.invitation_code, cm.role
                      FROM circle_members cm 
                      JOIN outfit_circles c ON cm.circle_id = c.id 
                      WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
    $findCircleStmt = $conn->prepare($findCircleSql);
    $findCircleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $findCircleStmt->execute();
    
    $userCircle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$userCircle) {
        echo json_encode([
            'status' => 'error',
            'message' => '您还没有加入任何圈子'
        ]);
        exit;
    }
    
    // 只有创建者可以分享邀请
    if ($userCircle['role'] !== 'creator') {
        echo json_encode([
            'status' => 'error',
            'message' => '只有圈子创建者可以分享邀请'
        ]);
        exit;
    }
    
    // 检查是否已有记录
    $checkRecordSql = "SELECT id, share_count FROM circle_invitations 
                       WHERE circle_id = :circle_id AND inviter_id = :inviter_id AND invitation_code = :invitation_code";
    $checkRecordStmt = $conn->prepare($checkRecordSql);
    $checkRecordStmt->bindParam(':circle_id', $userCircle['id'], PDO::PARAM_INT);
    $checkRecordStmt->bindParam(':inviter_id', $userId, PDO::PARAM_INT);
    $checkRecordStmt->bindParam(':invitation_code', $userCircle['invitation_code']);
    $checkRecordStmt->execute();
    
    $existingRecord = $checkRecordStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existingRecord) {
        // 更新现有记录
        $updateSql = "UPDATE circle_invitations 
                      SET share_count = share_count + 1, 
                          share_type = :share_type,
                          last_shared_at = NOW(),
                          updated_at = NOW()
                      WHERE id = :record_id";
        $updateStmt = $conn->prepare($updateSql);
        $updateStmt->bindParam(':share_type', $shareType);
        $updateStmt->bindParam(':record_id', $existingRecord['id'], PDO::PARAM_INT);
        $updateStmt->execute();
        
        $newShareCount = $existingRecord['share_count'] + 1;
    } else {
        // 创建新记录
        $insertSql = "INSERT INTO circle_invitations 
                      (circle_id, inviter_id, invitation_code, share_type, share_count, last_shared_at) 
                      VALUES (:circle_id, :inviter_id, :invitation_code, :share_type, 1, NOW())";
        $insertStmt = $conn->prepare($insertSql);
        $insertStmt->bindParam(':circle_id', $userCircle['id'], PDO::PARAM_INT);
        $insertStmt->bindParam(':inviter_id', $userId, PDO::PARAM_INT);
        $insertStmt->bindParam(':invitation_code', $userCircle['invitation_code']);
        $insertStmt->bindParam(':share_type', $shareType);
        $insertStmt->execute();
        
        $newShareCount = 1;
    }
    
    echo json_encode([
        'status' => 'success',
        'message' => '分享记录已保存',
        'data' => [
            'share_count' => $newShareCount,
            'share_type' => $shareType
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '记录分享失败：' . $e->getMessage()
    ]);
}
?>
