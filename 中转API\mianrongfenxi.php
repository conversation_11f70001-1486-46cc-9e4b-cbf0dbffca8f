<?php
/**
 * 面容分析中转API
 * 
 * 功能：
 * 1. 接收本地API传来的面容照片URL或Base64编码和风格偏好
 * 2. 处理图片数据并转换为Base64（如果是URL）
 * 3. 构建提示词调用Gemini API进行分析
 * 4. 返回分析结果给本地API
 */

// 设置允许跨域访问
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type');

// 设置日志文件
$logFile = __DIR__ . '/logs/face_analysis.log';
if (!file_exists(dirname($logFile))) {
    mkdir(dirname($logFile), 0755, true);
}

/**
 * 记录日志函数
 */
function logDebug($message, $data = null) {
    global $logFile;
    $timestamp = date('[Y-m-d H:i:s]');
    $logMessage = $timestamp . ' ' . $message;
    
    if ($data !== null) {
        if (is_array($data) || is_object($data)) {
            $logMessage .= ' ' . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PARTIAL_OUTPUT_ON_ERROR);
        } else {
            $logMessage .= ' ' . $data;
        }
    }
    
    file_put_contents($logFile, $logMessage . PHP_EOL, FILE_APPEND);
    error_log($logMessage);
}

// 初始化响应数组
$response = [
    'error' => false,
    'msg' => '',
    'data' => null
];

// 获取POST数据
$json = file_get_contents('php://input');
$data = json_decode($json, true);

logDebug("接收到请求数据", isset($data) ? '请求解析成功' : '无效JSON数据');

// 验证必要参数
if (!$data) {
    $response['error'] = true;
    $response['msg'] = '无效的JSON请求数据';
    echo json_encode($response);
    exit;
}

// 处理请求 - 支持URL或Base64两种方式，优先使用Base64
$frontPhotoBase64 = null;
$sidePhotoBase64 = null;

// 优先检查是否为Base64方式（新的直传模式）
if (isset($data['front_photo_base64']) && !empty($data['front_photo_base64'])) {
    logDebug("处理Base64直传方式请求");
    $frontPhotoBase64 = $data['front_photo_base64'];
    $sidePhotoBase64 = isset($data['side_photo_base64']) ? $data['side_photo_base64'] : '';

    // 确保Base64数据已清理（移除前缀）
    $frontPhotoBase64 = cleanBase64($frontPhotoBase64);
    if ($sidePhotoBase64) {
        $sidePhotoBase64 = cleanBase64($sidePhotoBase64);
    }

    logDebug("Base64数据处理完成，正面照片长度: " . strlen($frontPhotoBase64) .
             ($sidePhotoBase64 ? ", 侧面照片长度: " . strlen($sidePhotoBase64) : ""));
}
// 检查是否为URL方式（向后兼容）
else if (isset($data['front_photo_url']) && !empty($data['front_photo_url'])) {
    logDebug("处理URL方式请求");
    $frontPhotoUrl = $data['front_photo_url'];
    $sidePhotoUrl = isset($data['side_photo_url']) ? $data['side_photo_url'] : '';

    try {
        // 下载照片并转换为Base64
        $frontPhotoBase64 = imageUrlToBase64($frontPhotoUrl);
        $sidePhotoBase64 = $sidePhotoUrl ? imageUrlToBase64($sidePhotoUrl) : '';
    } catch (Exception $e) {
        $response['error'] = true;
        $response['msg'] = '图片处理失败: ' . $e->getMessage();
        echo json_encode($response);
        exit;
    }
}
else {
    $response['error'] = true;
    $response['msg'] = '缺少必要参数：正面照片(URL或Base64)';
    echo json_encode($response);
    exit;
}

$preferredStyle = isset($data['preferred_style']) ? $data['preferred_style'] : '';

logDebug("处理分析请求，风格偏好: $preferredStyle");

try {
    // 构建Gemini API请求
    $analysisResult = callGeminiApi($frontPhotoBase64, $sidePhotoBase64, $preferredStyle);
    
    if ($analysisResult === false) {
        $response['error'] = true;
        $response['msg'] = 'Gemini API调用失败';
        echo json_encode($response);
        exit;
    }
    
    // 返回分析结果
    $response['data'] = $analysisResult;
    echo json_encode($response);
} catch (Exception $e) {
    logDebug("处理异常: " . $e->getMessage());
    $response['error'] = true;
    $response['msg'] = '处理异常: ' . $e->getMessage();
    echo json_encode($response);
}

/**
 * 测试URL是否可访问
 */
function testUrlAccess($url) {
    logDebug("测试URL访问: $url");

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true); // 只获取头部信息
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    logDebug("URL访问测试结果: HTTP代码: $httpCode, 错误: $error");
    return $httpCode === 200;
}

/**
 * 图片URL转Base64
 */
function imageUrlToBase64($url) {
    logDebug("下载图片: $url");

    // 先测试URL是否可访问
    if (!testUrlAccess($url)) {
        logDebug("URL访问测试失败，尝试直接下载");
    }

    // 使用curl下载图片（更可靠）
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

    $imageData = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($imageData === false || $httpCode !== 200 || $error) {
        logDebug("下载图片失败: $url, HTTP代码: $httpCode, 错误: $error");
        throw new Exception("下载图片失败: $url");
    }

    // 转换为Base64
    $base64 = base64_encode($imageData);
    logDebug("图片转换为Base64成功，长度: " . strlen($base64));

    return $base64;
}

/**
 * 调用Gemini API进行分析
 */
function callGeminiApi($frontPhotoBase64, $sidePhotoBase64, $preferredStyle) {
    logDebug("准备调用Gemini API");
    
    // 确保清理Base64字符串前缀
    $frontPhotoBase64 = cleanBase64($frontPhotoBase64);
    if ($sidePhotoBase64) {
        $sidePhotoBase64 = cleanBase64($sidePhotoBase64);
    }
    
    // 构建提示词
    $prompt = buildPrompt($preferredStyle);
    logDebug("构建的提示词长度: " . strlen($prompt));
    
    // 构建API请求数据
    $requestData = [
        'contents' => [
            'parts' => [
                [
                    'text' => $prompt
                ]
            ]
        ],
        'generation_config' => [
            'temperature' => 0.4,
            'top_p' => 0.95,
            'top_k' => 40,
            'max_output_tokens' => 2048,
        ]
    ];
    
    // 添加图片
    $images = [];
    if ($frontPhotoBase64) {
        $images[] = [
            'data' => $frontPhotoBase64,
            'mime_type' => 'image/jpeg'
        ];
    }
    
    if ($sidePhotoBase64) {
        $images[] = [
            'data' => $sidePhotoBase64,
            'mime_type' => 'image/jpeg'
        ];
    }
    
    // 将图片添加到请求中
    if (!empty($images)) {
        foreach ($images as $image) {
            $requestData['contents']['parts'][] = [
                'inline_data' => [
                    'mime_type' => $image['mime_type'],
                    'data' => $image['data']
                ]
            ];
        }
    }
    
    // Gemini API地址和密钥
    $apiKey = 'AIzaSyD1-g64EwoKNcvs0LeAn9hbyHJRuKj0Slg';  // 替换为实际的API密钥
    $apiUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=$apiKey";
    
    // 记录请求信息
    logDebug("API URL: $apiUrl");
    logDebug("请求数据结构: " . json_encode(array_keys($requestData), JSON_UNESCAPED_UNICODE));
    logDebug("请求内容部分数量: " . count($requestData['contents']['parts']));
    
    // 将请求数据转换为JSON
    $jsonData = json_encode($requestData);
    if (json_last_error() !== JSON_ERROR_NONE) {
        logDebug("JSON编码错误: " . json_last_error_msg());
        throw new Exception("JSON编码错误: " . json_last_error_msg());
    }
    
    // 发起POST请求
    $ch = curl_init($apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 120); // 设置120秒超时
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    // 检查请求是否成功
    if ($httpCode != 200 || $error) {
        logDebug("Gemini API请求失败，HTTP代码: $httpCode, 错误: $error");
        if ($response) {
            logDebug("错误响应内容: " . substr($response, 0, 1000) . "...");
        }
        return false;
    }
    
    // 解析响应
    $responseData = json_decode($response, true);
    logDebug("Gemini API响应成功，HTTP代码: $httpCode");
    
    // 提取分析结果文本
    $analysisText = '';
    if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
        $analysisText = $responseData['candidates'][0]['content']['parts'][0]['text'];
        logDebug("成功提取分析文本，长度: " . strlen($analysisText));
    } else {
        logDebug("无法提取分析文本，响应结构: " . json_encode(array_keys($responseData)));
        if (isset($responseData['error'])) {
            logDebug("API错误: " . json_encode($responseData['error']));
            throw new Exception("Gemini API错误: " . $responseData['error']['message']);
        }
    }
    
    // 结构化分析结果
    $formattedResult = formatAnalysisResult($analysisText, $preferredStyle);
    
    return $formattedResult;
}

/**
 * 清理Base64字符串，移除前缀
 */
function cleanBase64($base64String) {
    // 如果包含数据URL前缀，则移除
    if (strpos($base64String, ';base64,') !== false) {
        list(, $base64String) = explode(';base64,', $base64String);
    }
    return $base64String;
}

/**
 * 构建提示词
 */
function buildPrompt($preferredStyle) {
    // 基础提示词模板
    $basePrompt = "你是一位专业的面容分析师和形象顾问，请基于提供的照片，详细分析用户的面部特征，并给出专业的妆容和风格建议。

分析内容必须包含以下方面：
1. 脸型分析：如方形、圆形、心形、椭圆形、长形等，并给出适合该脸型的修容技巧
2. 三庭五眼分析：评估面部比例，包括额头、鼻子到嘴巴、嘴巴到下巴的比例，以及眼距、眼位和眼型
3. 五官特点：分析眉毛、眼睛、鼻子、嘴唇的形状和特点
4. 皮相骨相：评估骨骼结构和皮肤特点
5. 人中唇部分析：分析唇部形状、大小和人中长度
6. 整体面部特征：包括突出优点和需要修饰的地方

然后，请根据分析提供以下建议：
1. 适合的妆容技巧：包括底妆、眼妆、唇妆的具体建议
2. 发型发色推荐：适合脸型的发型和适合肤色的发色
3. 首饰和耳饰选择：适合脸型和气质的饰品推荐
4. 适合的服装领口款式：根据脸型和颈部特点推荐
5. 眼镜框型推荐：如果用户戴眼镜，推荐适合的款式

请根据照片中的真实特征进行分析，给出专业、具体且个性化的建议，避免泛泛而谈。";

    // 添加用户风格偏好
    if (!empty($preferredStyle)) {
        $basePrompt .= "\n\n用户表示偏好的风格是：" . $preferredStyle . "。请在你的建议中考虑这一偏好，但同时要确保建议与用户的面部特征相适合。";
    }
    
    // 添加输出格式要求
    $basePrompt .= "\n\n请以JSON格式输出结果，包含以下字段：
{
  \"face_shape\": \"脸型分析结果\",
  \"face_proportions\": \"三庭五眼分析结果\",
  \"facial_features\": \"五官特点分析\",
  \"bone_structure\": \"皮相骨相分析\",
  \"lip_analysis\": \"人中唇部分析\",
  \"overall_features\": \"整体面部特征总结\",
  \"makeup_recommendations\": {
    \"foundation\": \"底妆建议\",
    \"eyes\": \"眼妆建议\",
    \"lips\": \"唇妆建议\",
    \"contour\": \"修容建议\"
  },
  \"hairstyle_recommendations\": {
    \"style\": \"发型建议\",
    \"color\": \"发色建议\"
  },
  \"accessories_recommendations\": {
    \"earrings\": \"耳饰建议\",
    \"necklaces\": \"项链建议\",
    \"glasses\": \"眼镜框型建议\"
  },
  \"clothing_recommendations\": {
    \"necklines\": \"领口款式建议\",
    \"colors\": \"适合的颜色\"
  },
  \"overall_style_suggestions\": \"综合风格建议\"
}

请确保输出严格遵循JSON格式，避免使用不符合JSON规范的表述。";

    return $basePrompt;
}

/**
 * 格式化分析结果
 */
function formatAnalysisResult($analysisText, $preferredStyle) {
    logDebug("格式化分析结果，文本长度: " . strlen($analysisText));
    
    // 方法1: 直接尝试解析JSON
    try {
        $jsonData = json_decode($analysisText, true);
        if ($jsonData && json_last_error() === JSON_ERROR_NONE) {
            logDebug("直接解析JSON成功");
            return [
                'raw_text' => $analysisText,
                'analysis' => $jsonData,
                'preferred_style' => $preferredStyle
            ];
        }
    } catch (Exception $e) {
        logDebug("直接解析JSON失败: " . $e->getMessage());
    }
    
    // 方法2: 尝试提取JSON部分
    $jsonStartPos = strpos($analysisText, '{');
    $jsonEndPos = strrpos($analysisText, '}');
    
    if ($jsonStartPos !== false && $jsonEndPos !== false) {
        $jsonStr = substr($analysisText, $jsonStartPos, $jsonEndPos - $jsonStartPos + 1);
        
        try {
            // 处理可能的转义问题
            $jsonStr = str_replace('\\\\', '\\', $jsonStr);
            $jsonData = json_decode($jsonStr, true);
            
            if ($jsonData && json_last_error() === JSON_ERROR_NONE) {
                logDebug("提取JSON部分解析成功");
                return [
                    'raw_text' => $analysisText,
                    'analysis' => $jsonData,
                    'preferred_style' => $preferredStyle
                ];
            } else {
                logDebug("提取JSON部分解析失败: " . json_last_error_msg());
            }
        } catch (Exception $e) {
            logDebug("处理JSON字符串异常: " . $e->getMessage());
        }
    }
    
    // 方法3: 尝试从markdown代码块中提取JSON
    if (preg_match('/```(?:json)?\s*({[\s\S]*?})\s*```/m', $analysisText, $matches)) {
        try {
            $jsonStr = $matches[1];
            $jsonData = json_decode($jsonStr, true);
            
            if ($jsonData && json_last_error() === JSON_ERROR_NONE) {
                logDebug("从markdown代码块提取JSON成功");
                return [
                    'raw_text' => $analysisText,
                    'analysis' => $jsonData,
                    'preferred_style' => $preferredStyle
                ];
            } else {
                logDebug("从markdown代码块提取JSON失败: " . json_last_error_msg());
            }
        } catch (Exception $e) {
            logDebug("处理markdown代码块异常: " . $e->getMessage());
        }
    }
    
    // 如果所有方法都失败，返回原始文本
    logDebug("所有JSON解析方法都失败，返回原始文本");
    return [
        'raw_text' => $analysisText,
        'analysis' => null,
        'preferred_style' => $preferredStyle
    ];
} 