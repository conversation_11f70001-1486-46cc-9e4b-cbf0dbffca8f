# 次元衣柜系统图片存储说明

## 调查结果

经过对系统代码和服务器目录的检查，我们确认系统**已完全迁移到阿里云OSS存储图片**，服务器本地不再存储任何用户上传的图片文件。具体发现如下：

1. **服务器上没有用户上传的图片文件**：
   - `login_backend/uploads` 目录不存在，需要手动创建（为了保持代码兼容性）
   - 创建后的 `uploads` 目录为空

2. **小程序前端的静态资源图片**：
   - 位于 `miniprogram/images` 目录
   - 这些是UI界面需要的静态资源文件，不是用户上传的内容

3. **日志目录**：
   - `login_backend/logs` 目录不存在，需要手动创建
   - 该目录用于存储系统运行日志，不存储图片

## 系统架构分析

1. **直传OSS**：
   - 系统使用OSS直传功能，前端获取签名后直接将图片上传到OSS
   - `oss_upload_token.php` 生成上传凭证
   - `oss_upload_callback.php` 处理上传成功的回调

2. **CDN加速**：
   - 系统配置了CDN加速OSS访问
   - `config.php` 中配置了 `USE_CDN=true`
   - 数据库中存储的是CDN URL而非本地路径

3. **图片处理流程**：
   - 用户上传图片 → 直接传到OSS → OSS回调服务器 → 服务器调用抠图API → 结果存回OSS
   - 整个流程不在服务器本地存储任何永久文件

## 建议

1. **目录维护**：
   - 保持以下空目录以确保系统兼容性：
     - `login_backend/uploads`
     - `login_backend/logs`

2. **无需担心服务器图片**：
   - 服务器上不存在用户上传的图片文件
   - 所有用户上传的内容都存储在阿里云OSS中
   - 系统访问的是OSS/CDN URL，不依赖服务器上的文件

3. **系统迁移/备份时注意**：
   - 迁移系统时无需担心图片文件
   - 备份数据库即可保留图片URL信息
   - 若需要完整备份，请联系阿里云进行OSS存储桶备份

## 结论

**服务器上不存储任何用户上传的图片文件**，系统完全依赖阿里云OSS存储图片，并使用CDN加速访问。维护只需确保必要的空目录存在即可。 