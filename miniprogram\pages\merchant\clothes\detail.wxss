.container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 衣物图片 */
.clothes-image-container {
  width: 100%;
  height: 750rpx;
  background-color: #f0f0f0;
}

.clothes-image {
  width: 100%;
  height: 100%;
}

/* 衣物详情 */
.clothes-details {
  padding: 30rpx 20rpx;
  margin-bottom: 100rpx;
}

.info-section {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.clothes-name {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.info-item {
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.info-value {
  font-size: 30rpx;
  color: #333;
}

.info-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  font-size: 24rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 8rpx 16rpx;
  border-radius: 4rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.info-description {
  font-size: 30rpx;
  color: #333;
  line-height: 1.5;
}

/* 商家信息 */
.merchant-section {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.merchant-info {
  display: flex;
  align-items: center;
}

.merchant-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f0f0f0;
}

.merchant-details {
  flex: 1;
}

.merchant-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.status-tag {
  display: inline-block;
  font-size: 22rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 5rpx 15rpx;
  border-radius: 30rpx;
}

.status-share {
  color: #25c5a0;
  background-color: rgba(37, 197, 160, 0.1);
}

/* 加载中 */
.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 999;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部试穿按钮 */
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
}

.try-on-tips {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.try-on-btn {
  background-color: #25c5a0;
  color: #fff;
  font-size: 32rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
} 