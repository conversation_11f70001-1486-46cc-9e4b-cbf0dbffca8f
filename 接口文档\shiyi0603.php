<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 配置信息
$hf_token = '*************************************';
$upload_dir = 'uploads/'; // 确保该目录存在并可写
$api_endpoint = 'https://pawanratrung-virtual-try-on.hf.space/gradio_api/call/virtual_tryon';
$log_file = 'uploads/api_log.txt'; // 日志文件位置

// 超时设置（秒）
$connection_timeout = 10;
$request_timeout = 60;
$max_api_wait_time = 120; // 最长等待API处理时间

// 记录日志
function log_message($message) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message\n";
    file_put_contents($log_file, $log_entry, FILE_APPEND);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    log_message("错误: 不允许的方法 " . $_SERVER['REQUEST_METHOD']);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// 创建上传目录（如果不存在）
if (!file_exists($upload_dir)) {
    if (!mkdir($upload_dir, 0755, true)) {
        log_message("错误: 无法创建上传目录 $upload_dir");
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create upload directory']);
        exit;
    }
}

// 处理请求
try {
    log_message("接收到新请求");
    
    // 检查必需参数
    if (!isset($_FILES['person_image']) || !isset($_FILES['garment_image']) || !isset($_POST['garment_type'])) {
        log_message("错误: 缺少必需参数");
        throw new Exception('Missing required parameters');
    }

    $person_image = $_FILES['person_image'];
    $garment_image = $_FILES['garment_image'];
    $garment_type = $_POST['garment_type'];
    
    // 验证衣物类型
    $allowed_garment_types = ['upper_body', 'lower_body', 'dresses'];
    if (!in_array($garment_type, $allowed_garment_types)) {
        log_message("错误: 无效的衣物类型 $garment_type");
        throw new Exception('Invalid garment type');
    }
    
    log_message("参数: garment_type=$garment_type");

    // 验证文件类型
    $allowed_types = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];
    if (!in_array($person_image['type'], $allowed_types) || !in_array($garment_image['type'], $allowed_types)) {
        log_message("错误: 无效的文件类型 person_image=" . $person_image['type'] . ", garment_image=" . $garment_image['type']);
        throw new Exception('Invalid file type. Only JPG, PNG and WEBP are allowed.');
    }
    
    // 验证文件扩展名
    $allowed_extensions = ['jpg', 'jpeg', 'png', 'webp'];
    $person_extension = strtolower(pathinfo($person_image['name'], PATHINFO_EXTENSION));
    $garment_extension = strtolower(pathinfo($garment_image['name'], PATHINFO_EXTENSION));
    
    if (!in_array($person_extension, $allowed_extensions) || !in_array($garment_extension, $allowed_extensions)) {
        log_message("错误: 无效的文件扩展名 person_extension=$person_extension, garment_extension=$garment_extension");
        throw new Exception('Invalid file extension. Only JPG, JPEG, PNG and WEBP are allowed.');
    }
    
    // 验证文件大小（最大5MB）
    $max_size = 5 * 1024 * 1024; // 5MB
    if ($person_image['size'] > $max_size || $garment_image['size'] > $max_size) {
        log_message("错误: 文件大小超过限制 person_image=" . $person_image['size'] . ", garment_image=" . $garment_image['size']);
        throw new Exception('File size exceeds the limit (5MB)');
    }

    // 上传文件到临时位置
    $person_temp_path = $upload_dir . uniqid() . '_' . basename($person_image['name']);
    $garment_temp_path = $upload_dir . uniqid() . '_' . basename($garment_image['name']);

    if (!move_uploaded_file($person_image['tmp_name'], $person_temp_path) || 
        !move_uploaded_file($garment_image['tmp_name'], $garment_temp_path)) {
        log_message("错误: 无法上传文件");
        throw new Exception('Failed to upload files');
    }
    
    log_message("文件已上传: person=$person_temp_path, garment=$garment_temp_path");

    // 检查上传的文件是否真的是图片
    $person_image_info = @getimagesize($person_temp_path);
    $garment_image_info = @getimagesize($garment_temp_path);
    if (!$person_image_info || !$garment_image_info) {
        log_message("错误: 上传的文件不是有效图片");
        // 清理临时文件
        @unlink($person_temp_path);
        @unlink($garment_temp_path);
        throw new Exception('Uploaded files are not valid images');
    }

    // 准备请求Hugging Face API
    $server_protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $person_file_url = $server_protocol . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . '/' . $person_temp_path;
    $garment_file_url = $server_protocol . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . '/' . $garment_temp_path;
    
    $person_file_url = str_replace('\\', '/', $person_file_url);
    $garment_file_url = str_replace('\\', '/', $garment_file_url);
    
    log_message("图片URL: person=$person_file_url, garment=$garment_file_url");

    // 构建请求数据
    $data = [
        'data' => [
            [
                'path' => $person_file_url,
                'meta' => ['_type' => 'gradio.FileData']
            ],
            [
                'path' => $garment_file_url,
                'meta' => ['_type' => 'gradio.FileData']
            ],
            $garment_type
        ]
    ];
    
    log_message("发送数据到API: " . json_encode($data));

    // 步骤1: POST请求获取EVENT_ID
    $ch = curl_init($api_endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $hf_token
    ]);
    // 添加超时设置
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $connection_timeout);
    curl_setopt($ch, CURLOPT_TIMEOUT, $request_timeout);
    
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        $curl_error = curl_error($ch);
        log_message("POST请求失败: $curl_error");
        throw new Exception('POST request failed: ' . $curl_error);
    }
    
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    if ($http_code !== 200) {
        log_message("POST请求失败，状态码: $http_code, 响应: $response");
        throw new Exception('POST request failed with code: ' . $http_code . ', Response: ' . $response);
    }
    
    curl_close($ch);
    
    // 解析EVENT_ID
    $response_data = json_decode($response, true);
    if (!isset($response_data['event_id'])) {
        log_message("无效的响应格式: EVENT_ID未找到，响应: $response");
        throw new Exception('Invalid response format: EVENT_ID not found');
    }
    
    $event_id = $response_data['event_id'];
    log_message("获取到EVENT_ID: $event_id");
    
    // 步骤2: GET请求获取结果
    $result_url = $api_endpoint . '/' . $event_id;
    $ch = curl_init($result_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $hf_token
    ]);
    // 添加超时设置
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $connection_timeout);
    
    // 由于结果可能需要时间生成，我们需要轮询获取
    $max_attempts = 30; // 最大尝试次数
    $attempt = 0;
    $result_image_url = null;
    $wait_interval = 2; // 初始等待时间（秒）
    $total_wait_time = 0; // 总等待时间
    
    while ($attempt < $max_attempts && $total_wait_time < $max_api_wait_time) {
        $response = curl_exec($ch);
        
        if (curl_errno($ch)) {
            $curl_error = curl_error($ch);
            log_message("GET请求失败: $curl_error");
            throw new Exception('GET request failed: ' . $curl_error);
        }
        
        log_message("轮询尝试 #" . ($attempt+1) . " 结果: " . substr($response, 0, 100) . "...");
        
        // 解析响应
        if (strpos($response, 'event: complete') !== false) {
            // 提取 data 部分
            preg_match('/data: (.+)$/m', $response, $matches);
            if (isset($matches[1])) {
                $result_data = json_decode($matches[1], true);
                
                if (isset($result_data[0]['url'])) {
                    $result_image_url = $result_data[0]['url'];
                    log_message("获取到结果图片URL: " . $result_image_url);
                    break;
                }
            }
        } elseif (strpos($response, 'event: error') !== false) {
            log_message("API处理错误: $response");
            throw new Exception('Processing error from API');
        }
        
        $attempt++;
        $total_wait_time += $wait_interval;
        sleep($wait_interval); 
        
        // 逐渐增加等待时间，但不超过5秒
        $wait_interval = min($wait_interval * 1.5, 5);
    }
    
    curl_close($ch);
    
    if (!$result_image_url) {
        log_message("在 $max_attempts 次尝试后未能获取结果，总等待时间: {$total_wait_time}秒");
        throw new Exception("Failed to get result after {$total_wait_time} seconds");
    }
    
    // 下载结果图片并保存到服务器 - 使用CURL替代file_get_contents
    $result_filename = $upload_dir . uniqid() . '_result.webp';
    
    // 使用CURL下载图片
    $ch = curl_init($result_image_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 跟随重定向
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $hf_token
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 如果需要，禁用SSL验证
    // 添加超时设置
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $connection_timeout);
    curl_setopt($ch, CURLOPT_TIMEOUT, $request_timeout);
    
    $result_image_content = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch) || $http_code !== 200 || empty($result_image_content)) {
        $curl_error = curl_error($ch);
        log_message("下载图片失败: 错误=" . $curl_error . ", HTTP状态码=" . $http_code);
        curl_close($ch);
        
        // 尝试直接返回生成的图片URL，不再尝试本地存储
        log_message("返回远程图片URL作为备选方案");
        echo json_encode([
            'success' => true,
            'result_image' => $result_image_url,
            'is_remote' => true,
            'message' => '无法下载远程图片，使用远程图片URL代替'
        ]);
        exit;
    }
    
    curl_close($ch);
    
    // 保存图片内容到文件
    if (file_put_contents($result_filename, $result_image_content) === false) {
        log_message("无法保存结果图片到文件，尝试返回远程URL");
        // 如果无法保存到本地，直接返回远程URL
        echo json_encode([
            'success' => true,
            'result_image' => $result_image_url,
            'is_remote' => true,
            'message' => '无法保存图片到本地服务器，使用远程图片URL代替'
        ]);
        exit;
    }
    
    log_message("结果图片已保存: $result_filename");
    
    // 构建结果图片URL
    $result_image_local_url = $server_protocol . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . '/' . $result_filename;
    $result_image_local_url = str_replace('\\', '/', $result_image_local_url);
    
    log_message("完成处理，返回结果URL: $result_image_local_url");
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'result_image' => $result_image_local_url,
        'is_remote' => false,
        'processing_time' => $total_wait_time
    ]);
    
} catch (Exception $e) {
    log_message("异常: " . $e->getMessage() . "\n" . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// 尝试删除超过24小时的临时文件
try {
    // 只在10%的请求中执行清理，避免每次请求都进行清理操作
    if (rand(1, 10) == 1) {
        log_message("尝试清理超过24小时的临时文件");
        $files = glob($upload_dir . '*');
        $now = time();
        $deleted_count = 0;
        
        foreach ($files as $file) {
            if (is_file($file) && !strpos($file, 'api_log.txt')) {
                if ($now - filemtime($file) > 86400) { // 24小时 = 86400秒
                    if (@unlink($file)) {
                        $deleted_count++;
                    }
                }
            }
        }
        
        if ($deleted_count > 0) {
            log_message("已清理 {$deleted_count} 个过期文件");
        }
    }
} catch (Exception $e) {
    log_message("清理临时文件时出错: " . $e->getMessage());
    // 这里不中断请求处理，只记录错误
}
?> 