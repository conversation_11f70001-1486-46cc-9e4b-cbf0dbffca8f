const app = getApp();

Page({
  data: {
    donations: [], // 打赏记录
    loading: true, // 加载状态
    page: 1, // 当前页码
    perPage: 10, // 每页记录数
    hasMore: false, // 是否有更多数据
    showPopup: false, // 是否显示打赏弹窗
    selectedAmount: 0, // 选择的打赏金额
    isCustomAmount: false, // 是否选择自定义金额
    customAmountValue: '', // 自定义金额
    orderCheckInterval: null // 订单检查定时器
  },

  onLoad: function() {
    // 加载打赏记录
    this.loadDonations();
  },

  onShow: function() {
    // 页面显示时检查是否需要重新加载数据
  },

  onUnload: function() {
    // 清除订单状态检查定时器
    if (this.data.orderCheckInterval) {
      clearInterval(this.data.orderCheckInterval);
    }
  },

  // 加载打赏记录
  loadDonations: function(page = 1) {
    // 如果是第一页，显示加载状态
    if (page === 1) {
      this.setData({ loading: true });
    }

    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_donations.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      data: {
        page: page,
        per_page: this.data.perPage
      },
      success: (res) => {
        if (res.statusCode === 200 && !res.data.error) {
          const newDonations = res.data.data || [];
          
          // 格式化时间
          newDonations.forEach(item => {
            if (item.paid_at) {
              item.paid_at = this.formatDate(item.paid_at);
            }
            if (item.created_at) {
              item.created_at = this.formatDate(item.created_at);
            }
          });

          // 更新页面数据
          if (page === 1) {
            // 第一页，替换所有数据
            this.setData({
              donations: newDonations,
              loading: false,
              page: page,
              hasMore: res.data.pagination && res.data.pagination.current_page < res.data.pagination.total_pages
            });
          } else {
            // 加载更多，追加数据
            this.setData({
              donations: [...this.data.donations, ...newDonations],
              loading: false,
              page: page,
              hasMore: res.data.pagination && res.data.pagination.current_page < res.data.pagination.total_pages
            });
          }
        } else {
          wx.showToast({
            title: '获取打赏记录失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: (err) => {
        console.error('加载打赏记录失败', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  // 加载更多打赏记录
  loadMoreDonations: function() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadDonations(this.data.page + 1);
    }
  },

  // 格式化日期
  formatDate: function(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 打赏金额选择
  selectAmount: function(e) {
    const amount = parseInt(e.currentTarget.dataset.amount);
    this.setData({
      selectedAmount: amount,
      isCustomAmount: false,
      customAmountValue: ''
    });
  },

  // 选择自定义金额
  showCustomAmount: function() {
    this.setData({
      selectedAmount: 0,
      isCustomAmount: true,
      customAmountValue: ''
    });
  },

  // 自定义金额输入
  onCustomAmountInput: function(e) {
    let value = e.detail.value;
    
    // 只允许输入数字和小数点，且最多两位小数
    value = value.replace(/[^\d.]/g, '');
    
    // 确保只有一个小数点
    const parts = value.split('.');
    if (parts.length > 2) {
      value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // 限制小数点后最多两位
    if (parts.length === 2 && parts[1].length > 2) {
      value = parts[0] + '.' + parts[1].substring(0, 2);
    }

    this.setData({
      customAmountValue: value
    });
  },

  // 显示打赏弹窗
  showDonationPopup: function() {
    // 检查是否登录
    if (!app.globalData.token) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再进行打赏',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    // 体验账号不能打赏
    if (app.globalData.useMockUser) {
      wx.showToast({
        title: '体验账号不支持打赏',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showPopup: true,
      selectedAmount: 0,
      isCustomAmount: false,
      customAmountValue: ''
    });
  },

  // 隐藏打赏弹窗
  hideDonationPopup: function() {
    this.setData({
      showPopup: false
    });
  },

  // 创建打赏订单
  createDonation: function() {
    // 获取打赏金额
    let amount = this.data.selectedAmount;
    if (this.data.isCustomAmount) {
      amount = parseFloat(this.data.customAmountValue);
    }

    // 验证金额
    if (!amount || amount <= 0) {
      wx.showToast({
        title: '请选择或输入有效金额',
        icon: 'none'
      });
      return;
    }

    // 限制最大打赏金额
    if (amount > 1000) {
      wx.showToast({
        title: '单次打赏金额不能超过1000元',
        icon: 'none'
      });
      return;
    }

    // 显示加载中
    wx.showLoading({
      title: '正在创建订单...',
      mask: true
    });

    // 调用创建打赏订单API
    wx.request({
      url: `${app.globalData.apiBaseUrl}/create_donation_order.php`,
      method: 'POST',
      header: {
        'Authorization': app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: {
        amount: amount
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.statusCode === 200 && !res.data.error) {
          const orderData = res.data.data;
          
          // 保存订单ID
          const orderId = orderData.order_id;
          
          // 调用微信支付
          wx.requestPayment({
            timeStamp: orderData.pay_params.timeStamp,
            nonceStr: orderData.pay_params.nonceStr,
            package: orderData.pay_params.package,
            signType: orderData.pay_params.signType,
            paySign: orderData.pay_params.paySign,
            success: () => {
              // 隐藏弹窗
              this.hideDonationPopup();
              
              // 显示支付成功提示
              wx.showToast({
                title: '打赏成功，感谢您的支持！',
                icon: 'success',
                duration: 2000
              });
              
              // 延时重新加载打赏记录
              setTimeout(() => {
                this.loadDonations(1);
              }, 2000);
            },
            fail: (err) => {
              console.log('支付失败', err);
              if (err.errMsg.indexOf('cancel') > -1) {
                wx.showToast({
                  title: '支付已取消',
                  icon: 'none'
                });
              } else {
                wx.showToast({
                  title: '支付失败，请重试',
                  icon: 'none'
                });
              }
            }
          });
        } else {
          wx.showToast({
            title: res.data.msg || '创建订单失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('创建打赏订单失败', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 处理头像加载错误
  onAvatarError: function(e) {
    // 获取当前项的索引
    const index = e.currentTarget.dataset.index;
    // 设置默认头像
    this.setData({
      [`donations[${index}].avatar_url`]: '/images/default-avatar.png'
    });
  }
}); 