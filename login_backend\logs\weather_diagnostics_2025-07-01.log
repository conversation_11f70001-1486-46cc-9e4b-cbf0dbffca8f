{
    "timestamp": "2025-07-01 11:22:40",
    "source": "API",
    "client_ip": "unknown",
    "user_agent": "unknown",
    "request_info": {
        "longitude": null,
        "latitude": null,
        "location_param": "101210101",
        "api_key": "99b4a9...fb1d",
        "api_host": "kq2k5mg4v2.re.qweatherapi.com"
    },
    "response_info": {
        "http_code": 200,
        "error": "无"
    },
    "data": {
        "obsTime": "2025-07-01T11:16+08:00",
        "temp": "34",
        "feelsLike": "35",
        "icon": "100",
        "text": "晴",
        "wind360": "135",
        "windDir": "东南风",
        "windScale": "3",
        "windSpeed": "17",
        "humidity": "51",
        "precip": "0.0",
        "pressure": "1006",
        "vis": "30",
        "cloud": "10",
        "dew": "24",
        "updateTime": "2025-07-01T11:18+08:00",
        "fxLink": "https:\/\/www.qweather.com\/weather\/hangzhou-101210101.html",
        "_realtime": true,
        "_timestamp": 1751340160,
        "city": "hangzhou",
        "cityid": "101210101",
        "_debug": {
            "source": "api",
            "timestamp": 1751340160,
            "query_type": "city_id",
            "api_host": "kq2k5mg4v2.re.qweatherapi.com",
            "response_time_ms": 166.08,
            "api_version": "v7"
        }
    },
    "data_summary": {
        "success": null,
        "city": "hangzhou",
        "cityid": "101210101",
        "temp": "34",
        "text": "晴",
        "is_mock": false,
        "is_cached": false,
        "timestamp": 1751340160
    }
}
---
