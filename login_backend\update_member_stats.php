<?php
// 更新成员统计数据API
// 模块2：圈子成员管理模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

// 验证必需参数
if (!isset($input['action']) || empty($input['action'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少操作类型参数'
    ]);
    exit;
}

$action = $input['action']; // 'refresh' 或 'increment'
$targetUserId = isset($input['user_id']) ? intval($input['user_id']) : $userId;

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查找用户所在的圈子
    $findCircleSql = "SELECT cm.circle_id, cm.role
                      FROM circle_members cm 
                      JOIN outfit_circles c ON cm.circle_id = c.id 
                      WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
    $findCircleStmt = $conn->prepare($findCircleSql);
    $findCircleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $findCircleStmt->execute();
    
    $userCircle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$userCircle) {
        echo json_encode([
            'status' => 'error',
            'message' => '您当前未加入任何圈子'
        ]);
        exit;
    }
    
    $circleId = $userCircle['circle_id'];
    
    if ($action === 'refresh') {
        // 刷新所有成员的统计数据
        refreshAllMemberStats($conn, $circleId);
        
        echo json_encode([
            'status' => 'success',
            'message' => '统计数据已刷新'
        ]);
        
    } elseif ($action === 'increment') {
        // 增量更新特定用户的统计
        $type = isset($input['type']) ? $input['type'] : '';
        $count = isset($input['count']) ? intval($input['count']) : 1;
        
        if (empty($type)) {
            echo json_encode([
                'status' => 'error',
                'message' => '缺少统计类型参数'
            ]);
            exit;
        }
        
        incrementMemberStats($conn, $circleId, $targetUserId, $type, $count);
        
        echo json_encode([
            'status' => 'success',
            'message' => '统计数据已更新'
        ]);
        
    } else {
        echo json_encode([
            'status' => 'error',
            'message' => '不支持的操作类型'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '更新统计数据失败：' . $e->getMessage()
    ]);
}

// 刷新所有成员统计数据的函数
function refreshAllMemberStats($conn, $circleId) {
    // 获取圈子中的所有活跃成员
    $membersSql = "SELECT user_id FROM circle_members 
                   WHERE circle_id = :circle_id AND status = 'active'";
    $membersStmt = $conn->prepare($membersSql);
    $membersStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $membersStmt->execute();
    
    while ($member = $membersStmt->fetch(PDO::FETCH_ASSOC)) {
        refreshSingleMemberStats($conn, $circleId, $member['user_id']);
    }
}

// 刷新单个成员统计数据的函数
function refreshSingleMemberStats($conn, $circleId, $userId) {
    // 这里应该根据实际的数据表来计算统计
    // 由于模块4还未开发，暂时使用模拟数据
    
    // 插入或更新统计记录
    $upsertSql = "INSERT INTO circle_member_stats 
                  (circle_id, user_id, wardrobe_count, clothes_count, outfit_count, 
                   clothing_category_count, outfit_category_count, tag_count, last_contribution_at)
                  VALUES (:circle_id, :user_id, 0, 0, 0, 0, 0, 0, NOW())
                  ON DUPLICATE KEY UPDATE
                  wardrobe_count = 0,
                  clothes_count = 0,
                  outfit_count = 0,
                  clothing_category_count = 0,
                  outfit_category_count = 0,
                  tag_count = 0,
                  updated_at = NOW()";
    
    $upsertStmt = $conn->prepare($upsertSql);
    $upsertStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $upsertStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $upsertStmt->execute();
}

// 增量更新成员统计的函数
function incrementMemberStats($conn, $circleId, $userId, $type, $count) {
    $validTypes = ['wardrobe_count', 'clothes_count', 'outfit_count', 
                   'clothing_category_count', 'outfit_category_count', 'tag_count'];
    
    if (!in_array($type, $validTypes)) {
        throw new Exception('无效的统计类型');
    }
    
    // 插入或更新统计记录
    $upsertSql = "INSERT INTO circle_member_stats 
                  (circle_id, user_id, {$type}, last_contribution_at)
                  VALUES (:circle_id, :user_id, :count, NOW())
                  ON DUPLICATE KEY UPDATE
                  {$type} = {$type} + :count,
                  last_contribution_at = NOW(),
                  updated_at = NOW()";
    
    $upsertStmt = $conn->prepare($upsertSql);
    $upsertStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $upsertStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $upsertStmt->bindParam(':count', $count, PDO::PARAM_INT);
    $upsertStmt->execute();
}
?>
