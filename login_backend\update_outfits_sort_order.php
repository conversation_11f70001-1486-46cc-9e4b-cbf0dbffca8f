<?php
/**
 * Update Outfits Sort Order API
 * 
 * Updates the sort order of outfits for a user
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * POST Parameters:
 * - outfits_order: Array of outfit IDs in the desired order
 * 
 * Response:
 * {
 *   "error": false,
 *   "message": "Sort order updated successfully"
 * }
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// Set response content type
header('Content-Type: application/json');

// Handle CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if Authorization header exists
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'message' => 'Authorization header is required'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// 处理Bearer前缀，与其他API保持一致
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7); // 去除 "Bearer " 前缀
}

// Verify token
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    echo json_encode([
        'error' => true,
        'message' => 'Invalid or expired token'
    ]);
    exit;
}

// Get user ID from token data
$user_id = $tokenData['sub'];

// Only allow POST method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => true, 'message' => 'Only POST method allowed']);
    exit;
}

// Get POST data
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['outfits_order']) || !is_array($input['outfits_order'])) {
    echo json_encode(['error' => true, 'message' => 'outfits_order parameter is required and must be an array']);
    exit;
}

$outfits_order = $input['outfits_order'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Start transaction
    $conn->beginTransaction();
    
    // Update sort order for each outfit item
    $stmt = $conn->prepare("UPDATE outfits SET sort_order = ? WHERE id = ? AND user_id = ?");
    
    foreach ($outfits_order as $index => $outfit_id) {
        $sort_order = $index + 1; // Start from 1
        $stmt->execute([$sort_order, $outfit_id, $user_id]);
    }
    
    // Commit transaction
    $conn->commit();
    
    echo json_encode([
        'error' => false,
        'message' => 'Sort order updated successfully'
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    if ($conn->inTransaction()) {
        $conn->rollback();
    }
    
    echo json_encode([
        'error' => true,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
