<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>隐私政策 - 次元衣帽间</title>
    <meta name="description" content="次元衣帽间隐私政策，我们致力于保护用户的个人信息和数据安全。">
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="images/logo.png" type="image/png">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5B6EF9',
                        secondary: '#F664B8',
                        dark: '#333333',
                        light: '#F9FAFC'
                    }
                }
            }
        }
    </script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom styles -->
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        .hero-gradient {
            background: linear-gradient(135deg, #5B6EF9 0%, #F664B8 100%);
        }
        .logo-container {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 5px;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Header/Navigation -->
    <header class="sticky top-0 bg-white shadow-sm z-50">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <a href="index.html" class="flex items-center space-x-2">
                <div class="logo-container">
                    <img src="images/logo.png" alt="次元衣帽间" class="h-10 w-auto">
                </div>
                <span class="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">次元衣帽间</span>
            </a>
            <nav class="hidden md:flex space-x-8">
                <a href="index.html#features" class="text-dark hover:text-primary font-medium transition-colors">功能特点</a>
                <a href="index.html#how-it-works" class="text-dark hover:text-primary font-medium transition-colors">使用流程</a>
                <a href="index.html#about" class="text-dark hover:text-primary font-medium transition-colors">关于我们</a>
            </nav>
            <div class="md:hidden">
                <button id="mobile-menu-button" class="text-dark p-2">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-md">
            <div class="container mx-auto px-4 py-2 flex flex-col space-y-3">
                <a href="index.html#features" class="text-dark hover:text-primary font-medium transition-colors py-2">功能特点</a>
                <a href="index.html#how-it-works" class="text-dark hover:text-primary font-medium transition-colors py-2">使用流程</a>
                <a href="index.html#about" class="text-dark hover:text-primary font-medium transition-colors py-2">关于我们</a>
            </div>
        </div>
    </header>

    <!-- Privacy Policy Content -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h1 class="text-3xl md:text-4xl font-bold mb-8 text-center">隐私政策</h1>
                
                <div class="bg-light p-6 md:p-10 rounded-xl shadow-sm mb-8">
                    <p class="text-gray-700 mb-6">最后更新日期：2025年1月1日</p>
                    
                    <div class="space-y-8">
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">1. 引言</h2>
                            <p class="text-gray-700">次元衣帽间（以下简称"我们"）非常重视用户的隐私和个人信息保护。本隐私政策旨在向您说明我们如何收集、使用、存储和共享您的个人信息，以及您享有的相关权利。请您在使用我们的服务前，仔细阅读并了解本隐私政策的全部内容。</p>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">2. 信息收集</h2>
                            <p class="text-gray-700 mb-4">我们可能收集以下类型的信息：</p>
                            <ul class="list-disc pl-6 space-y-2 text-gray-700">
                                <li>您提供的信息：包括但不限于您在注册账户、使用功能、参与活动等过程中提供的个人信息，如微信账号、个人照片等。</li>
                                <li>设备信息：如设备型号、操作系统版本、唯一设备标识符、网络信息等。</li>
                                <li>使用信息：如您使用我们服务的方式、频率和时长，上传的服装图片，创建的穿搭搭配等。</li>
                            </ul>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">3. 信息使用</h2>
                            <p class="text-gray-700 mb-4">我们使用收集的信息主要用于：</p>
                            <ul class="list-disc pl-6 space-y-2 text-gray-700">
                                <li>提供、维护和改进我们的服务，如虚拟试衣、衣橱管理等功能。</li>
                                <li>开发新功能和服务。</li>
                                <li>响应您的请求和提供客户支持。</li>
                                <li>进行数据分析以优化用户体验。</li>
                                <li>发送服务通知和更新信息。</li>
                            </ul>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">4. 信息存储</h2>
                            <p class="text-gray-700">我们将通过安全技术和措施保护您的个人信息，防止信息的丢失、不当使用、未经授权访问或泄露。您的个人信息将存储在中国境内的服务器上，并遵守中国相关法律法规。</p>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">5. 信息共享</h2>
                            <p class="text-gray-700 mb-4">除以下情况外，我们不会与任何第三方分享您的个人信息：</p>
                            <ul class="list-disc pl-6 space-y-2 text-gray-700">
                                <li>获得您的明确同意。</li>
                                <li>遵循法律法规要求或强制性的政府要求。</li>
                                <li>与我们的合作伙伴共享必要信息以提供服务（这些合作伙伴无权将信息用于其他目的）。</li>
                            </ul>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">6. 您的权利</h2>
                            <p class="text-gray-700 mb-4">根据相关法律法规，您对个人信息享有以下权利：</p>
                            <ul class="list-disc pl-6 space-y-2 text-gray-700">
                                <li>访问、更正或删除您的个人信息。</li>
                                <li>限制或反对我们处理您的个人信息。</li>
                                <li>数据可携带权。</li>
                                <li>撤回同意（如果处理基于您的同意）。</li>
                            </ul>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">7. 儿童隐私</h2>
                            <p class="text-gray-700">我们的服务不面向16岁以下的儿童。如果我们发现收集了16岁以下儿童的个人信息，我们会立即采取措施删除相关信息。</p>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">8. 隐私政策的变更</h2>
                            <p class="text-gray-700">我们可能会不时更新本隐私政策。如有重大变更，我们会通过在小程序内公告或其他适当方式通知您。</p>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">9. 联系我们</h2>
                            <p class="text-gray-700">如您对本隐私政策有任何疑问或建议，或想行使您的个人信息权利，请通过以下方式联系我们：</p>
                            <p class="text-gray-700 mt-2">电子邮件：<EMAIL></p>
                            <p class="text-gray-700">电话：18606539135</p>
                            <p class="text-gray-700">微信：shawii</p>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <a href="index.html" class="bg-primary hover:bg-opacity-90 text-white px-8 py-3 rounded-full font-medium inline-flex items-center justify-center transition-all">
                        <i class="fas fa-arrow-left mr-2"></i> 返回首页
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-12">
        <div class="container mx-auto px-4">
            <div class="border-t border-gray-800 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-500 mb-4 md:mb-0">&copy; 2025 次元衣帽间 - 版权所有</p>
                <div class="flex space-x-6">
                    <a href="privacy.html" class="text-gray-500 hover:text-white transition-colors">隐私政策</a>
                    <a href="terms.html" class="text-gray-500 hover:text-white transition-colors">服务条款</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
    </script>
</body>
</html> 