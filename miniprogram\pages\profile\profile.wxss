/* 页面容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f9f9f9;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  background-color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

/* 顶部标题栏 */
.title-bar {
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  position: fixed;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.title {
  font-size: 17px;
  font-weight: 500;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 20px; /* 减小底部内边距 */
  padding-top: 10px;
}

/* 用户卡片 */
.user-card {
  background-color: #fff;
  border-radius: 12px;
  margin: 15px 15px 10px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.mb-4 {
  margin-bottom: 16px;
}

.avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
  background-color: #f5f5f5;
}

.default-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: #f0f0f000;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  color: #aaa;
  font-size: 30px;
}

.nickname {
  font-size: 18px;
  font-weight: 600;
}

.slogan {
  font-size: 14px;
  color: #888;
}

.login-btn {
  width: 100%;
  height: 44px;
  background-color: #000;
  color: #fff;
  border-radius: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  margin-top: 15px;
}

/* 数据统计 */
.stats-container {
  display: flex;
  justify-content: space-around;
  padding: 15px 0 5px;
  margin-top: 15px;
  border-top: 1px solid rgba(0,0,0,0.05);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #888;
}

/* 功能列表 */
.feature-container {
  background-color: #fff;
  margin: 10px 15px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  padding: 15px 5px; /* 减小左右内边距 */
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px; /* 进一步增加间距 */
}

/* 基础功能项样式 */
.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 2px; /* 减小内边距 */
  border-bottom: none;
  text-align: center;
  min-width: 0; /* 允许子元素收缩 */
  position: relative; /* 添加相对定位 */
  box-sizing: border-box;
  height: 60px; /* 固定高度 */
}

/* 包含按钮的功能项 */
.feature-item-button {
  padding: 0;
}

/* 普通功能项 */
.feature-item-normal {
  padding: 0;
}

/* 功能图标 */
.feature-icon {
  width: 30px; /* 减小图标尺寸 */
  height: 30px;
  border-radius: 8px;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 0;
  margin-bottom: 6px;
  color: #4a6bdf;
  transition: all 0.2s ease;
}

/* 功能文本 */
.feature-text {
  color: #666;
  font-size: 10px; /* 减小字体大小 */
  text-align: center;
  width: 100%;
  white-space: nowrap; /* 防止文字换行 */
  overflow: hidden; /* 隐藏溢出部分 */
  text-overflow: ellipsis; /* 显示省略号 */
}

/* 联系客服按钮样式 */
.contact-button {
  background: none !important;
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
  font-size: 10px; /* 减小字体大小 */
  line-height: normal !important;
  color: inherit;
  text-align: center;
  border-radius: 0;
  width: 100% !important;
  height: 100% !important;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  min-height: auto !important;
}

/* 专门针对联系客服按钮中的文本 */
.contact-button .feature-text {
  color: #666;
  font-weight: normal;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 意见反馈按钮样式 */
.feedback-button {
  background: none !important;
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
  font-size: 10px; /* 减小字体大小 */
  line-height: normal !important;
  color: inherit;
  text-align: center;
  border-radius: 0;
  width: 100% !important;
  height: 100% !important;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  min-height: auto !important;
}

/* 专门针对意见反馈按钮中的文本 */
.feedback-button .feature-text {
  color: #666;
  font-weight: normal;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 联系作者模块的微信号样式 */
.wechat-id {
  font-size: 9px;
  color: #999;
  margin-left: 0;
  margin-top: 1px;
  text-align: center;
  width: 100%;
}

/* 退出登录按钮 */
.logout-container {
  margin: 20px 15px 30px;
}

.logout-btn {
  width: 100%;
  height: 50px;
  background-color: #fff;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #ff3b30;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

/* 底部导航栏 */
.tab-bar {
  height: 56px;
  background-color: #fff;
  border-top: 1px solid #f1f1f1;
  display: flex;
  justify-content: space-around;
  padding: 8px 0;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 10px;
  color: #8e8e93;
}

.tab-item.active {
  color: #000;
}

.tab-icon {
  font-size: 22px;
  margin-bottom: 4px;
}

/* 图标样式 */
.icon-user::before {
  content: '\f007';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.icon-tshirt::before {
  content: '\f553';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.icon-user-circle::before {
  content: '\f2bd';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.icon-question::before {
  content: '\f128';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  font-size: 20px;
}

.icon-chevron-right::before {
  content: '\f054';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  color: #ccc;
  font-size: 12px;
}

.icon-image {
  width: 24px;
  height: 24px;
}

/* 商家入驻弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(3px);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 320px;
  background-color: #fff;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 22px;
  color: #999;
  padding: 0 5px;
  line-height: 22px;
}

.modal-body {
  padding: 20px;
}

.modal-tips {
  font-size: 15px;
  color: #555;
  line-height: 1.6;
}

.modal-footer {
  display: flex;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.modal-btn {
  flex: 1;
  height: 48px;
  line-height: 48px;
  text-align: center;
  font-size: 16px;
  border-radius: 0;
  background: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-btn::after {
  border: none;
}

.modal-cancel-btn {
  color: #666;
  font-weight: normal;
  border-right: 1px solid rgba(0, 0, 0, 0.05);
}

.modal-confirm-btn {
  color: #ffffff;
  background-color: #000000;
  font-weight: 600;
}

/* 用户信息部分 */
.user-info {
  flex: 1;
}

.nickname-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

/* 商家入驻/退出按钮 */
.nickname-row .merchant-action {
  display: flex;
  align-items: center;
  background-color: #000000;
  padding: 3px 10px;
  border-radius: 12px;
  margin-left: 10px;
}

.nickname-row .merchant-action text {
  font-size: 12px;
  color: #ffffff;
  font-weight: 500;
}

/* 商家入驻样式 */
.merchant-section {
  margin-top: 16px;
  border-top: 1px solid rgba(0,0,0,0.05);
  padding-top: 16px;
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 15px;
}

.merchant-status {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.merchant-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
}

.merchant-title {
  font-size: 15px;
  color: #000000;
  font-weight: 500;
}

.merchant-badge {
  font-size: 14px;
  color: #000000;
  font-weight: 600;
  background-color: #f0f0f0;
  padding: 2px 10px;
  border-radius: 10px;
  position: relative;
  display: flex;
  align-items: center;
}

.merchant-badge::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #000000;
  margin-right: 6px;
}

.share-control {
  display: flex;
  align-items: center;
}

.share-status {
  font-size: 14px;
  color: #000000;
  margin-right: 10px;
}

.share-switch {
  transform: scale(0.8);
}

/* 试衣次数显示样式 */
.count-badge {
  margin-left: auto;
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
}

.free-count {
  color: #4caf50;
  font-weight: 500;
}

.count-separator {
  margin: 0 4px;
  color: #999;
}

.paid-count {
  color: #ff9800;
  font-weight: 500;
}

/* 组合分类模块特殊样式 */
.outfit-count-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 4px;
}

.outfit-count {
  font-size: 12px;
  color: #666;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 10px;
}

/* 个人形象分析Banner */
.image-analysis-banner {
  margin: 15px;
  background: linear-gradient(135deg, #333 0%, #000 100%);
  border-radius: 12px;
  padding: 10px; /* 减小padding */
  display: flex;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.banner-content {
  flex: 1;
  color: #fff;
  z-index: 1;
}

.banner-title {
  font-size: 16px; /* 减小字体 */
  font-weight: bold;
  margin-bottom: 3px; /* 减小间距 */
}

.banner-subtitle {
  font-size: 10px; /* 减小字体 */
  opacity: 0.9;
  margin-bottom: 6px; /* 减小间距 */
  line-height: 1.4;
}

.banner-price {
  font-size: 20px; /* 减小字体 */
  font-weight: bold;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
  display: flex;
  align-items: center;
}

.original-price {
  font-size: 12px;
  font-weight: normal;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 8px;
  text-decoration: line-through;
}

.banner-image-container {
  width: 60px; /* 减小宽度 */
  height: 60px; /* 减小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 添加关闭按钮样式 */
.banner-close-btn {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 60rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background-color: rgba(255,255,255,0.3);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  z-index: 10;
}

/* 按钮边框修复 */
.feedback-button::after,
.contact-button::after {
  border: none;
}

/* 功能列表中特定的button处理 */
button.contact-button, button.feedback-button {
  background: none !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: normal !important;
  overflow: visible !important;
  min-height: auto !important;
}

/* 商务合作和其他普通功能项按钮样式 */
.business-contact {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
} 

/* 确保按钮内的feature-text占满整个区域 */
button.contact-button .feature-text, 
button.feedback-button .feature-text {
  display: block;
  width: 100%;
  max-width: 100%;
}

/* 移除所有的空白 */
.feature-list, .feature-item, button.contact-button, button.feedback-button {
  margin: 0 !important;
  box-sizing: border-box !important;
} 