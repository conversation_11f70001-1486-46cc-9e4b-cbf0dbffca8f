<?php
/**
 * API向后兼容性测试脚本
 * 用于验证新增的圈子数据功能不会影响现有API的使用
 */

require_once 'config.php';

// 测试配置
$testUserId = 3; // 使用测试用户ID
$testToken = 'your_test_token_here'; // 需要替换为实际的测试token

echo "=== API向后兼容性测试 ===\n\n";

// 测试1: 原有衣物API（不传递新参数）
echo "1. 测试原有衣物API（向后兼容）\n";
$clothesUrl = "http://localhost/login_backend/get_clothes.php";
$clothesHeaders = [
    "Authorization: Bearer $testToken",
    "Content-Type: application/json"
];

$clothesResponse = makeRequest($clothesUrl, $clothesHeaders);
echo "响应状态: " . ($clothesResponse ? "成功" : "失败") . "\n";
if ($clothesResponse) {
    $clothesData = json_decode($clothesResponse, true);
    echo "数据结构检查: " . (isset($clothesData['data']) ? "正常" : "异常") . "\n";
    echo "向后兼容字段检查: " . (isset($clothesData['data'][0]['data_source']) ? "已添加" : "缺失") . "\n";
}
echo "\n";

// 测试2: 新功能衣物API（传递新参数）
echo "2. 测试新功能衣物API（包含圈子数据）\n";
$newClothesUrl = $clothesUrl . "?include_circle_data=true&data_source=all";
$newClothesResponse = makeRequest($newClothesUrl, $clothesHeaders);
echo "响应状态: " . ($newClothesResponse ? "成功" : "失败") . "\n";
if ($newClothesResponse) {
    $newClothesData = json_decode($newClothesResponse, true);
    echo "新功能检查: " . (isset($newClothesData['meta']['include_circle_data']) ? "正常" : "异常") . "\n";
}
echo "\n";

// 测试3: 原有穿搭API（不传递新参数）
echo "3. 测试原有穿搭API（向后兼容）\n";
$outfitsUrl = "http://localhost/login_backend/get_outfits.php";
$outfitsHeaders = [
    "Authorization: Bearer $testToken",
    "Content-Type: application/json"
];

$outfitsResponse = makeRequest($outfitsUrl, $outfitsHeaders);
echo "响应状态: " . ($outfitsResponse ? "成功" : "失败") . "\n";
if ($outfitsResponse) {
    $outfitsData = json_decode($outfitsResponse, true);
    echo "数据结构检查: " . (isset($outfitsData['data']) ? "正常" : "异常") . "\n";
    echo "向后兼容字段检查: " . (isset($outfitsData['data'][0]['data_source']) ? "已添加" : "缺失") . "\n";
}
echo "\n";

// 测试4: 新功能穿搭API（传递新参数）
echo "4. 测试新功能穿搭API（包含圈子数据）\n";
$newOutfitsUrl = $outfitsUrl . "?include_circle_data=true&data_source=all";
$newOutfitsResponse = makeRequest($newOutfitsUrl, $outfitsHeaders);
echo "响应状态: " . ($newOutfitsResponse ? "成功" : "失败") . "\n";
if ($newOutfitsResponse) {
    $newOutfitsData = json_decode($newOutfitsResponse, true);
    echo "新功能检查: " . (isset($newOutfitsData['meta']['include_circle_data']) ? "正常" : "异常") . "\n";
}
echo "\n";

echo "=== 测试完成 ===\n";
echo "总结: 如果所有测试都显示'成功'和'正常'，则API向后兼容性良好\n";

/**
 * 发送HTTP请求的辅助函数
 */
function makeRequest($url, $headers = []) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return ($httpCode === 200) ? $response : false;
}
?>
