<?php
/**
 * 圈子权限验证API
 * 检查用户对特定数据的操作权限
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

// 修复：从payload中正确获取用户ID
$userId = $payload['user_id'] ?? $payload['sub'] ?? null;

if (!$userId) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Token中缺少用户ID信息',
        'debug' => [
            'payload' => $payload
        ]
    ]);
    exit;
}

// 获取请求参数
$dataType = $_GET['data_type'] ?? $_POST['data_type'] ?? ''; // clothes, outfits, wardrobes, categories
$dataId = $_GET['data_id'] ?? $_POST['data_id'] ?? '';
$operation = $_GET['operation'] ?? $_POST['operation'] ?? ''; // view, edit, delete, create

if (empty($dataType) || empty($operation)) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少必要参数：data_type 和 operation',
        'debug' => [
            'data_type' => $dataType,
            'data_id' => $dataId,
            'operation' => $operation,
            'user_id' => $userId
        ]
    ]);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();

    // 添加调试信息
    error_log("权限检查请求: user_id=$userId, data_type=$dataType, data_id=$dataId, operation=$operation");

    $permission = checkCirclePermission($conn, $userId, $dataType, $dataId, $operation);

    echo json_encode([
        'status' => 'success',
        'data' => $permission,
        'debug' => [
            'user_id' => $userId,
            'data_type' => $dataType,
            'data_id' => $dataId,
            'operation' => $operation
        ]
    ]);

} catch (Exception $e) {
    error_log("权限检查异常: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
    echo json_encode([
        'status' => 'error',
        'message' => '权限检查失败：' . $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}

/**
 * 检查圈子权限
 */
function checkCirclePermission($conn, $userId, $dataType, $dataId, $operation) {
    $result = [
        'allowed' => false,
        'reason' => '',
        'user_role' => '',
        'is_owner' => false,
        'is_circle_member' => false
    ];
    
    // 如果是创建操作，只需要检查用户是否在圈子中
    if ($operation === 'create') {
        return checkCreatePermission($conn, $userId, $result);
    }
    
    // 如果没有指定数据ID，无法检查权限
    if (empty($dataId)) {
        $result['reason'] = '缺少数据ID';
        return $result;
    }
    
    // 根据数据类型获取数据信息
    $dataInfo = getDataInfo($conn, $dataType, $dataId);
    
    if (!$dataInfo) {
        $result['reason'] = '数据不存在';
        return $result;
    }
    
    // 检查是否是数据的创建者
    $result['is_owner'] = ($dataInfo['user_id'] == $userId);

    // 对于数据创建者，始终允许编辑和删除（即使退出圈子）
    if ($result['is_owner'] && ($operation === 'edit' || $operation === 'delete')) {
        $result['allowed'] = true;
        $result['reason'] = '数据创建者权限（优先级最高）';
        error_log("权限检查: 数据创建者权限生效，用户ID=$userId, 数据ID=$dataId, 操作=$operation");
        return $result;
    }

    // 如果是个人数据（没有circle_id），只有创建者可以操作
    if (empty($dataInfo['circle_id'])) {
        if ($result['is_owner']) {
            $result['allowed'] = true;
            $result['reason'] = '数据创建者';
        } else {
            $result['reason'] = '非数据创建者，无权操作个人数据';
        }
        return $result;
    }
    
    // 检查用户在圈子中的角色
    $circleRole = getUserCircleRole($conn, $userId, $dataInfo['circle_id']);
    
    if (!$circleRole) {
        $result['reason'] = '用户不在该圈子中';
        return $result;
    }
    
    $result['is_circle_member'] = true;
    $result['user_role'] = $circleRole['role'];
    
    // 权限规则：
    // 1. 创建者（creator）：拥有所有权限
    // 2. 成员（member）：可以查看、编辑，不能删除
    // 3. 数据的原创建者：可以编辑和删除自己的数据
    
    switch ($operation) {
        case 'view':
            // 圈子成员都可以查看
            $result['allowed'] = true;
            $result['reason'] = '圈子成员查看权限';
            break;
            
        case 'edit':
            // 所有圈子成员都可以编辑共享数据
            $result['allowed'] = true;
            if ($circleRole['role'] === 'creator') {
                $result['reason'] = '圈子创建者权限';
            } elseif ($result['is_owner']) {
                $result['reason'] = '数据创建者权限';
            } else {
                $result['reason'] = '圈子成员共同管理权限';
            }
            break;
            
        case 'delete':
            // 只有圈子创建者或数据创建者可以删除
            if ($circleRole['role'] === 'creator' || $result['is_owner']) {
                $result['allowed'] = true;
                $result['reason'] = $circleRole['role'] === 'creator' ? '圈子创建者权限' : '数据创建者权限';
            } else {
                $result['reason'] = '只有圈子创建者或数据创建者可以删除';
            }
            break;
            
        default:
            $result['reason'] = '未知操作类型';
    }
    
    return $result;
}

/**
 * 检查创建权限
 */
function checkCreatePermission($conn, $userId, $result) {
    // 检查用户是否在任何活跃圈子中
    $stmt = $conn->prepare("
        SELECT cm.circle_id, cm.role, c.name as circle_name
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'
        LIMIT 1
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $circle = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($circle) {
        $result['allowed'] = true;
        $result['is_circle_member'] = true;
        $result['user_role'] = $circle['role'];
        $result['reason'] = '圈子成员创建权限';
    } else {
        $result['reason'] = '用户不在任何圈子中';
    }
    
    return $result;
}

/**
 * 获取数据信息
 */
function getDataInfo($conn, $dataType, $dataId) {
    $table = '';
    switch ($dataType) {
        case 'clothes':
            $table = 'clothes';
            break;
        case 'outfits':
            $table = 'outfits';
            break;
        case 'wardrobes':
            $table = 'wardrobes';
            break;
        case 'categories':
            $table = 'clothing_categories';
            break;
        default:
            return null;
    }
    
    $stmt = $conn->prepare("SELECT user_id, circle_id FROM $table WHERE id = :id");
    $stmt->bindParam(':id', $dataId, PDO::PARAM_INT);
    $stmt->execute();
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * 获取用户在圈子中的角色
 */
function getUserCircleRole($conn, $userId, $circleId) {
    $stmt = $conn->prepare("
        SELECT role, status 
        FROM circle_members 
        WHERE user_id = :user_id AND circle_id = :circle_id AND status = 'active'
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->execute();
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}
?>
