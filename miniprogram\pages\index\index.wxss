.container {
  padding: 0;
  background-color: #fff;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止整体滚动 */
}

/* 顶部标题栏 */
.title-bar {
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  position: relative;
  z-index: 10; /* 确保在滚动内容上方 */
}

.title {
  font-size: 17px;
  font-weight: 500;
}

/* 衣橱选择器 */
.wardrobe-switcher {
  background-color: #fff;
  padding: 12px 15px;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 10; /* 确保在滚动内容上方 */
}

.selected-wardrobe {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 8px 15px;
  border-radius: 18px;
  font-size: 13px;
  color: #333;
  border: 1px solid rgba(0,0,0,0.1);
}

.left-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.wardrobe-controls {
  display: flex;
  align-items: center;
}

.dropdown-icon {
  width: 8px;
  height: 8px;
  border-right: 1.5px solid #666;
  border-bottom: 1.5px solid #666;
  transform: rotate(45deg);
  margin-left: 8px;
  margin-top: -2px;
}

.wardrobe-scroll {
  white-space: nowrap;
  flex: 1;
  overflow-x: auto;
}

.wardrobe-option {
  display: inline-block;
  padding: 8px 16px;
  margin-right: 8px;
  border-radius: 16px;
  font-size: 14px;
  color: #333;
  background-color: #fff;
  border: 1px solid rgba(0,0,0,0.1);
  transition: all 0.3s;
}

.wardrobe-option.active {
  background-color: #000;
  color: #fff;
  border-color: #000;
}

/* 分类选项卡容器，用于定位 */
.category-tabs-container {
  position: sticky;
  top: 0; /* 在scrollable-content中的顶部 */
  width: 100%;
  background-color: #fff;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  z-index: 9; /* 低于衣橱选择器，但高于内容 */
  margin-top: 18rpx; /* 添加上边距，为feature-modules的阴影留出空间 */
}

.category-tabs {
  display: flex;
  background-color: #fff;
  padding: 12px 15px;
  overflow-x: auto;
  white-space: nowrap;
  align-items: center;
  border-bottom: none; /* 移除边框，由容器统一提供 */
}

.tab-item {
  padding: 8px 16px;
  margin-right: 8px;
  border-radius: 16px;
  font-size: 13px;
  white-space: nowrap;
  color: #666;
  background-color: #f5f5f5;
  display: inline-block;
}

.tab-item.active {
  background-color: #000;
  color: #fff;
}

.tab-count {
  font-size: 24rpx;
  color: inherit; /* 继承父元素的颜色 */
  margin-left: 4rpx;
  font-weight: normal;
}

/* 衣物容器 */
.clothes-container {
  padding: 10px 15px 70px; /* 修改上下内边距，为底部导航留出空间 */
}

/* 创建一个包含所有可滚动内容的容器 */
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
  position: relative;
}

.clothes-grid {
  display: grid;
  gap: 15px;
}

.clothes-grid.layout-mode-2 {
  grid-template-columns: repeat(2, 1fr);
}

.clothes-grid.layout-mode-3 {
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.clothes-grid.layout-mode-4 {
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.clothes-item {
  aspect-ratio: 3/4;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 5px 5px 0;
  box-sizing: border-box;
  position: relative;
}

/* 无名称状态下的衣物项目 */
.clothes-item.no-name {
  justify-content: center;
  padding: 5px;
}

/* 改变布局模式时对无名称样式的调整 */
.layout-mode-3 .clothes-item.no-name .clothing-image,
.layout-mode-4 .clothes-item.no-name .clothing-image {
  width: 85%;
  height: 85%; /* 恢复为原来的比例 */
}

.clothes-item.no-name .clothing-image {
  width: 90%;
  height: 90%; /* 恢复为原来的比例 */
}

.layout-mode-3 .clothes-item,
.layout-mode-3 .add-clothes-item {
  aspect-ratio: 2/3;
}

.layout-mode-4 .clothes-item,
.layout-mode-4 .add-clothes-item {
  aspect-ratio: 1/1.5;
}

.layout-mode-3 .clothing-image,
.layout-mode-4 .clothing-image {
  width: 85%;
  height: 75%;
}

.layout-mode-3 .add-icon,
.layout-mode-4 .add-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
}

.layout-mode-4 .text-sm {
  font-size: 12px;
}

.clothing-image {
  width: 90%;
  height: 80%;
  object-fit: contain;
  border-radius: 8px;
}

/* 衣物名称样式 */
.clothes-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 6px;
  background-color: #ffffff;
  color: #333;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
}

/* 新增：数据源标识样式 */
.data-source-badge {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: #4CAF50;
  border-radius: 8px;
  padding: 2px 6px;
  z-index: 10;
}

.badge-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
}

/* 新增：创建者信息样式 */
.creator-info {
  position: absolute;
  bottom: 30px;
  left: 5px;
  right: 5px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  padding: 2px 4px;
  text-align: center;
}

.creator-name {
  font-size: 10px;
  color: white;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.add-clothes-item {
  aspect-ratio: 3/4;
  border-radius: 12px;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px dashed #ddd;
}

.add-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #333;
  margin-bottom: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.floating-manage-btn {
  position: fixed;
  right: 20px;
  bottom: 75px;
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: #000;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 100;
  opacity: 0.8;
}

.manage-icon {
  font-size: 20px;
}

/* 空状态容器 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  height: 400rpx;
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background-color:rgb(255, 255, 255);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  color: #aaa;
  font-size: 36px;
}

.empty-image {
  width: 60px;
  height: 60px;
  object-fit: contain;
  display: block;
  margin: 0 auto;
}

.empty-text {
  font-size: 16px;
  color: #888;
  margin-bottom: 10px;
  text-align: center;
}

.login-btn {
  width: 200px;
  height: 44px;
  background-color: #000;
  color: #fff;
  border-radius: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  margin-top: 20px;
  font-size: 16px;
}

/* 图标定义 */
.icon-tshirt::before {
  content: '\f553';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.icon-plus::before {
  content: '\f067';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.icon-cog::before {
  content: '\f013';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

/* 工具类 */
.text-sm {
  font-size: 14px;
}

.text-gray-500 {
  color: #6b7280;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.object-cover {
  object-fit: cover;
}

/* 用户卡片 */
.user-card {
  background-color: #fff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  margin-bottom: 15px;
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  margin-right: 15px;
  background-color: #f5f5f5;
}

.stats {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  flex: 1;
}

.stat-num {
  color: #333;
  margin-bottom: 5px;
}

/* 功能卡片 */
.feature-section {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.section-title {
  margin-bottom: 15px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #ffffff00;
  border-radius: 8px;
  padding: 15px 0;
}

.feature-icon {
  font-size: 28px;
  margin-bottom: 10px;
  height: 30px;
  display: flex;
  align-items: center;
}

.feature-name {
  font-size: 14px;
  color: #333;
}

/* 穿搭列表 */
.outfit-list {
  padding: 20px 0;
}

.empty-state {
  padding: 30px 0;
}

.empty-icon {
  font-size: 40px;
  color: #ddd;
  display: block;
  margin-bottom: 10px;
}

.empty-text {
  margin-bottom: 15px;
}

.empty-btn {
  width: 60%;
  margin: 0 auto;
}

/* 图标 */
.icon-tshirt::before {
  content: '\f553';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.icon-pants::before {
  content: '\f6b6';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.icon-skirt::before {
  content: '\f50d';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.icon-shoe::before {
  content: '\f54b';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.icon-accessory::before {
  content: '\f5e9';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.icon-all::before {
  content: '\f0c9';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.icon-outfit::before {
  content: '\f490';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.manage-btn {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: #000000;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 100;
  opacity: 0.8;
  padding: 0; /* 确保没有内边距影响居中 */
}

.manage-icon {
  font-size: 24px; /* 更改为偶数值 */
  height: 24px; /* 保持宽高一致 */
  width: 24px;
  display: flex; /* 添加flex布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  margin: 0; /* 确保没有外边距影响居中 */
  line-height: 1; /* 确保文本垂直居中 */
}

.icon-image {
  width: 24px;
  height: 24px;
}

/* 添加刷新提示样式 */
.refresh-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0;
  font-size: 12px;
  color: #999;
  background-color: #f9f9f9;
  flex-direction: column;
}

.refresh-time {
  margin-bottom: 2px;
}

.refresh-hint {
  color: #bbb;
  font-size: 11px;
}

/* 体验账号提示条 */
.mock-user-banner {
  padding: 12px 20px;
  background-color: #f2f2f2;
  color: #333;
  font-size: 15px;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mock-banner-text {
  flex: 1;
  text-align: left;
  display: flex;
  align-items: center;
}

.mock-login-btn {
  background-color: #333;
  color: #fff;
  padding: 6px 18px;
  border-radius: 18px;
  font-size: 14px;
  margin-left: 12px;
  font-weight: 500;
}

/* 布局切换按钮，移到wardrobe-switcher中 */
.layout-switch-btn {
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 18px;
  background-color: #fff;
  margin-right: 5px;
  flex-shrink: 0;
  border: 1px solid rgba(0,0,0,0.1);
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.layout-switch-btn:active {
  background-color: #f5f5f5;
}

/* 新增：数据源切换按钮样式 */
.data-source-btn {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 8px 15px;
  border-radius: 18px;
  font-size: 13px;
  color: #333;
  border: 1px solid rgba(0,0,0,0.1);
  flex-shrink: 0;
}

.data-source-btn:active {
  background-color: #f5f5f5;
}



.data-source-dropdown-icon {
  width: 8px;
  height: 8px;
  border-right: 1.5px solid #666;
  border-bottom: 1.5px solid #666;
  transform: rotate(45deg);
  margin-left: 8px;
  margin-top: -2px;
}

/* 添加搜索按钮样式 */
.search-btn {
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 18px;
  background-color: #fff;
  margin-right: 5px;
  flex-shrink: 0;
  border: 1px solid rgba(0,0,0,0.1);
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.search-btn:active {
  background-color: #f5f5f5;
}

.search-icon {
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>');
}

/* 布局图标 */
.layout-icon {
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.layout-2 {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="black"><rect x="3" y="3" width="8" height="8" rx="1" /><rect x="13" y="3" width="8" height="8" rx="1" /><rect x="3" y="13" width="8" height="8" rx="1" /><rect x="13" y="13" width="8" height="8" rx="1" /></svg>');
}

.layout-3 {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="black"><rect x="1" y="3" width="6" height="8" rx="1" /><rect x="9" y="3" width="6" height="8" rx="1" /><rect x="17" y="3" width="6" height="8" rx="1" /><rect x="1" y="13" width="6" height="8" rx="1" /><rect x="9" y="13" width="6" height="8" rx="1" /><rect x="17" y="13" width="6" height="8" rx="1" /></svg>');
}

.layout-4 {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="black"><rect x="1" y="6" width="5" height="5" rx="1" /><rect x="7" y="6" width="5" height="5" rx="1" /><rect x="13" y="6" width="5" height="5" rx="1" /><rect x="19" y="6" width="5" height="5" rx="1" /><rect x="1" y="13" width="5" height="5" rx="1" /><rect x="7" y="13" width="5" height="5" rx="1" /><rect x="13" y="13" width="5" height="5" rx="1" /><rect x="19" y="13" width="5" height="5" rx="1" /></svg>');
}

/* 公告弹窗样式 */
.announcement-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.announcement-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1001;
}

.announcement-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
  padding: 30px 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1002;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.announcement-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  text-align: center;
}

.announcement-body {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  text-align: left;
  margin-bottom: 25px;
  width: 100%;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.announcement-btn {
  background-color: #333;
  color: #fff;
  padding: 8px 30px;
  border-radius: 20px;
  font-size: 15px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 上传选项弹出菜单 */
.upload-options-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.upload-options-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.upload-options-content {
  background-color: #fff;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  padding: 25px 20px 40px;
  z-index: 1001;
  display: flex;
  justify-content: space-around;
}

.upload-option-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
}

.upload-option-icon {
  font-size: 36px;
  margin-bottom: 12px;
}

.upload-option-text {
  font-size: 14px;
  color: #333;
}

/* 智能穿搭选项弹出菜单样式更新 */
.outfit-options-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.outfit-options-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
}

.outfit-options-content {
  position: relative;
  width: 100%;
  background-color: #fff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 20px;
  z-index: 1001;
  animation: slideUp 0.3s ease;
}

.outfit-options-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
  text-align: center;
  color: #333;
}

.outfit-option-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.outfit-option-item:last-child {
  border-bottom: none;
}

.outfit-option-icon {
  font-size: 24px;
  margin-right: 16px;
  width: 30px;
  text-align: center;
}

.outfit-option-info {
  flex: 1;
}

.outfit-option-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.outfit-option-desc {
  font-size: 13px;
  color: #666;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 个人形象分析选项弹出菜单样式 */
.personal-analysis-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.personal-analysis-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
}

.personal-analysis-content {
  position: relative;
  width: 100%;
  background-color: #fff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 20px;
  z-index: 1001;
  animation: slideUp 0.3s ease;
}

.personal-analysis-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
  text-align: center;
  color: #333;
}

.personal-analysis-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.personal-analysis-item:last-child {
  border-bottom: none;
}

.personal-analysis-icon {
  font-size: 24px;
  margin-right: 16px;
  width: 30px;
  text-align: center;
  margin-top: 2px;
}

.personal-analysis-info {
  flex: 1;
  padding-right: 8px;
}

.personal-analysis-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.personal-analysis-desc {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  white-space: normal;
  word-wrap: break-word;
  max-width: 100%;
}

/* 合并后的功能模块 */
.combined-features-module {
  background: #f7f7f7;
  border-radius: 8px;
  padding: 8px 5px;
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: none;
}

/* 功能项样式 */
.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 3px;
  transition: opacity 0.2s ease;
}

.feature-item:active {
  opacity: 0.7;
}

/* 功能图标样式 */
.feature-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 5px;
}

.feature-icon-image {
  width: 20px;
  height: 20px;
}

/* 功能标题样式 */
.feature-title {
  font-size: 12px;
  color: #333333;
  text-align: center;
  font-weight: normal;
}

/* 修改AI推荐穿搭模块样式适配三等分宽 */
.ai-recommendation, .outfit-rating, .image-analysis-banner.third-width {
  background: #ffffff;
  border-radius: 8px;
  padding: 8px 0;
  height: 85px;
  position: relative;
  border: none;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  margin: 0;
  transition: opacity 0.2s ease;
}

.ai-recommendation:active, .outfit-rating:active, .image-analysis-banner.third-width:active {
  opacity: 0.7;
}

/* 统一所有模块的内容样式 */
.banner-content, .ai-recommendation-content, .outfit-rating-content {
  width: 100%;
  z-index: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  text-align: center;
  margin-top: 6px;
}

/* 统一所有模块的标题样式 */
.banner-title, .ai-recommendation-title, .outfit-rating-title {
  font-size: 14px;
  font-weight: normal;
  margin: 0;
  color: #333333;
  text-align: center;
}

/* 统一图标位置和样式 */
.ai-recommendation-icon {
  position: relative;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
  border: none;
  box-shadow: none;
}

.image-analysis-banner .ai-recommendation-icon {
  background-color: #e6f2ff;
}

.ai-recommendation .ai-recommendation-icon {
  background-color: #e6f2ff;
}

.outfit-rating .ai-recommendation-icon {
  background-color: #e6f2ff;
}

.ai-icon-image {
  width: 22px;
  height: 22px;
}

/* 个人形象分析Banner */
.image-analysis-banner {
  background: #f8f8f8;
  border-radius: 15px;
  padding: 15px;
  position: relative;
  height: 90px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* 调整个人形象分析Banner */
.image-analysis-banner.half-width {
  margin: 0;
}

/* 为个人形象分析Banner中的图标添加与智能穿搭模块相同的样式 */
.image-analysis-banner.half-width .ai-recommendation-icon {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background-color: #333333;
  margin-right: 0;
}

.image-analysis-banner.half-width .ai-icon-image {
  width: 26px;
  height: 26px;
}

/* 添加关闭按钮样式 */
.banner-close-btn {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: rgba(255,255,255,0.3);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  z-index: 10;
  line-height: 1;
  padding: 0;
  text-align: center;
}

/* 衣橱弹出框 */
.wardrobe-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.wardrobe-popup {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 1001;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.wardrobe-popup-show {
  transform: translateY(0);
}

/* 新增：数据源弹出框样式 */
.data-source-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.data-source-popup {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  border-radius: 20px 20px 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 1001;
  max-height: 50vh;
}

.data-source-popup-show {
  transform: translateY(0);
}

.data-source-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.data-source-popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.data-source-popup-close {
  font-size: 24px;
  color: #999;
  cursor: pointer;
}

.data-source-popup-content {
  padding: 10px 0;
}

.data-source-popup-item {
  display: flex;
  flex-direction: column;
  padding: 30rpx 40rpx;
  cursor: pointer;
  border-bottom: 1rpx solid #f0f0f0;
}

.data-source-popup-item:last-child {
  border-bottom: none;
}

.data-source-popup-item:active {
  background-color: #f5f5f5;
}

.data-source-popup-item.active {
  background-color: #f8f8f8;
}

.data-source-main {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.data-source-popup-item .data-source-icon {
  font-size: 40rpx;
  margin-right: 30rpx;
}

.data-source-popup-item .data-source-name {
  font-size: 32rpx;
  color: #000;
  font-weight: 500;
}

.data-source-desc {
  font-size: 26rpx;
  color: #666;
  margin-left: 70rpx;
  line-height: 1.4;
}

.wardrobe-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.wardrobe-popup-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.wardrobe-popup-actions {
  display: flex;
  align-items: center;
}

.wardrobe-popup-action {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 8px;
}

.action-icon {
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.ranking-icon-small {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 12l2 2 4-4M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"></path><path d="M8 14l2 2 4-4"></path></svg>');
}

.sort-icon-small {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18M7 12h10m-7 6h4"></path></svg>');
}

.add-icon-small {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>');
}

.manage-icon-small {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>');
}

.wardrobe-popup-close {
  font-size: 22px;
  color: #999;
  padding: 0 5px;
  margin-left: 8px;
}

.wardrobe-popup-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
  max-height: calc(70vh - 50px);
}

.wardrobe-popup-item {
  padding: 14px 20px;
  font-size: 15px;
  color: #333;
}

.wardrobe-popup-item.active {
  color: #000;
  font-weight: 500;
  background-color: #f5f5f5;
}

.wardrobe-popup-item:active {
  background-color: #f0f0f0;
}

/* 添加搜索框弹出层样式 */
.search-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.search-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 1001;
  transform: translateY(-100%);
  transition: transform 0.3s ease;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.search-popup-show {
  transform: translateY(0);
}

.search-popup-header {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 18px;
  padding: 6px 12px;
  margin-right: 10px;
  position: relative;
}

.search-icon-small {
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-right: 8px;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  height: 30px;
  font-size: 14px;
}

.search-clear-btn {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #ccc;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  line-height: 1;
}

.search-popup-close {
  color: #333;
  font-size: 14px;
  padding: 5px;
}

.search-results {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.search-result-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.search-result-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background-color: #f5f5f5;
  object-fit: contain;
  margin-right: 12px;
}

.search-result-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.search-result-name {
  font-size: 15px;
  color: #333;
  margin-bottom: 5px;
}

.search-result-category {
  font-size: 12px;
  color: #999;
}

.search-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.search-empty-icon {
  font-size: 40px;
  color: #ddd;
  margin-bottom: 10px;
}

.search-empty-text {
  font-size: 14px;
  color: #999;
}

.search-tips {
  display: flex;
  justify-content: center;
  padding: 30px 0;
}

.search-tip-text {
  font-size: 14px;
  color: #999;
}

/* 名称显示切换按钮 */
.name-toggle {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: bold;
  color: #666;
  border: 1px solid rgba(0,0,0,0.1);
  margin-right: 5px;
  transition: all 0.2s ease;
}

.name-toggle-active {
  background-color: #000;
  color: #fff;
  border-color: #000;
}

/* 重新定义悬浮添加按钮样式，覆盖所有已存在的样式 */
.add-btn {
  position: fixed !important;
  right: 30rpx !important;
  z-index: 1000 !important;
  background-color: #000000 !important;
  width: 100rpx !important;
  height: 100rpx !important;
  border-radius: 50% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
}

/* 体验账号状态下按钮位置 */
.add-btn-mock-user {
  bottom: 140rpx !important; /* 避开mock-user-banner */
}

/* 登录状态下按钮位置 */
.add-btn-logged-in {
  bottom: 80rpx !important; /* 减小与底部tab栏的距离 */
}

/* 悬浮添加按钮中的加号图标 */
.add-btn .add-icon {
  color: white !important;
  font-size: 50rpx !important;
  font-weight: bold !important;
  line-height: 1 !important; /* 确保行高为1，更好地控制垂直居中 */
  text-align: center !important; /* 水平居中 */
  height: 50rpx !important; /* 与字体大小一致 */
  display: flex !important; /* 使用flex布局 */
  justify-content: center !important; /* 水平居中 */
  align-items: center !important; /* 垂直居中 */
  padding-bottom: 4rpx !important; /* 微调垂直位置，视觉上更居中 */
  /* 确保没有被其他样式覆盖 */
  background-color: transparent !important;
  width: 50rpx !important; /* 明确宽度，与高度相等 */
  border-radius: 0 !important;
  margin: 0 !important;
  box-shadow: none !important;
}

/* 功能模块样式 */
.feature-module {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 24rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06), 0 2rpx 6rpx rgba(0,0,0,0.03);
  flex: 1;
  max-width: calc(50% - 16rpx);
  box-sizing: border-box;
  border: 1rpx solid rgba(0,0,0,0.02);
}

.feature-module:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04), 0 1rpx 4rpx rgba(0,0,0,0.02);
}

/* 左侧模块样式 */
.feature-module-left {
  margin-right: 8rpx;
  margin-left: 16rpx;
}

/* 右侧模块样式 */
.feature-module-right {
  margin-left: 8rpx;
  margin-right: 16rpx;
}

.feature-icon {
  width: 72rpx;
  height: 72rpx;
  margin-right: 16rpx;
  border-radius: 16rpx;
}

.feature-info {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding-left: 4rpx;
}

.feature-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 4rpx;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  text-align: left;
}

.feature-desc {
  font-size: 11px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  text-align: left;
}

/* 推荐模块容器 */
.recommendation-container {
  display: flex;
  justify-content: center;
  margin: 10px 0 0;
  width: 100%;
  padding: 0;
}

/* 两大功能模块的容器 */
.feature-modules {
  display: flex;
  flex-direction: row;
  gap: 16rpx;
  margin-bottom: 0rpx;
  width: 100%;
  box-sizing: border-box;
  justify-content: center;
}