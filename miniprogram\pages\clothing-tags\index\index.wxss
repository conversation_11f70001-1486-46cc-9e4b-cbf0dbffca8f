.container {
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  min-height: 100vh;
  box-sizing: border-box;
  background-color: #f8f8f8;
}

.header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #888;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.error-text {
  font-size: 30rpx;
  color: #666;
  margin: 30rpx 0;
}

.retry-button {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #fff;
  color: #000000;
  font-size: 28rpx;
  border-radius: 40rpx;
  border: 1px solid #ddd;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.empty-tip {
  font-size: 28rpx;
  color: #999;
  text-align: center;
}

/* 标签分组 */
.tag-groups-container {
  flex: 1;
  padding-bottom: 120rpx;
}

.tag-group {
  margin-bottom: 40rpx;
}

.group-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 24rpx;
}

.group-title:before {
  content: '';
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #000000;
  border-radius: 4rpx;
}

.group-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 32rpx;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 1px solid #eee;
}

.tag-name {
  font-size: 28rpx;
  color: #333;
}

.tag-count {
  font-size: 24rpx;
  color: #888;
  background-color: #f2f2f2;
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
  margin-left: 10rpx;
}

/* 底部按钮 */
.bottom-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20rpx 30rpx 50rpx;
  background: linear-gradient(to top, rgba(248, 248, 248, 1), rgba(248, 248, 248, 0.9));
  box-sizing: border-box;
  z-index: 10;
}

.update-button {
  height: 88rpx;
  background-color: #000000;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.2);
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  box-sizing: border-box !important;
}

/* 覆盖微信按钮的默认样式 */
button.update-button::after {
  border: none !important;
}

/* 确保按钮内容居中 */
.button-text {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  width: 100% !important;
  height: 100% !important;
} 