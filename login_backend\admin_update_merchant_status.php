<?php
header("Content-Type: application/json");
require_once './db.php';
require_once './auth.php';
require_once './config.php';

// 初始化响应数组
$response = [
    'code' => 0,
    'message' => 'success',
    'data' => []
];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['code'] = 405;
    $response['message'] = 'Method Not Allowed';
    echo json_encode($response);
    exit;
}

// 获取认证头
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';

// 验证Bearer令牌格式
if (empty($authHeader) || strpos($authHeader, 'Bearer ') !== 0) {
    $response['code'] = 401;
    $response['message'] = '无效的认证头';
    echo json_encode($response);
    exit;
}

// 提取令牌
$token = substr($authHeader, 7); // 去掉'Bearer '前缀

// 获取POST参数
$postData = json_decode(file_get_contents("php://input"), true) ?: [];
$action = isset($postData['action']) ? trim($postData['action']) : '';
$merchantId = isset($postData['merchant_id']) ? intval($postData['merchant_id']) : 0;

// 记录日志，帮助调试
error_log("admin_update_merchant_status.php - 请求参数: " . json_encode($postData));

// 验证参数
if (empty($action) || empty($merchantId)) {
    $response['code'] = 400;
    $response['message'] = '缺少必要参数';
    echo json_encode($response);
    exit;
}

// 验证action参数值
if ($action !== 'join' && $action !== 'exit') {
    $response['code'] = 400;
    $response['message'] = '无效的action参数，必须为join或exit';
    echo json_encode($response);
    exit;
}

// 验证管理员令牌
$auth = new Auth();
$adminInfo = $auth->verifyAdminToken($token);

if ($adminInfo === false) {
    $response['code'] = 401;
    $response['message'] = '无效或已过期的管理员令牌';
    echo json_encode($response);
    exit;
}

// 记录日志，帮助调试
error_log("admin_update_merchant_status.php - 管理员信息: " . json_encode($adminInfo));

$db = new Database();
$conn = $db->getConnection();

try {
    // 开始事务
    $conn->beginTransaction();
    
    // 先检查用户是否存在
    $checkUserSql = "SELECT id, merchant_status FROM users WHERE id = :user_id";
    $checkUserStmt = $conn->prepare($checkUserSql);
    $checkUserStmt->bindValue(':user_id', $merchantId, PDO::PARAM_INT);
    $checkUserStmt->execute();
    $user = $checkUserStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception('用户不存在');
    }
    
    $currentStatus = $user['merchant_status'] ?? 'no';
    
    // 如果当前状态已经是要设置的状态，直接返回成功
    if (($action === 'join' && $currentStatus === 'yes') || 
        ($action === 'exit' && $currentStatus === 'no')) {
        $response['message'] = $action === 'join' ? '该用户已是商户' : '该用户已不是商户';
        $conn->commit();
        echo json_encode($response);
        exit;
    }
    
    // 更新商户状态
    $newStatus = $action === 'join' ? 'yes' : 'no';
    $updateSql = "UPDATE users SET merchant_status = :status, updated_at = NOW() WHERE id = :user_id";
    $updateStmt = $conn->prepare($updateSql);
    $updateStmt->bindValue(':status', $newStatus, PDO::PARAM_STR);
    $updateStmt->bindValue(':user_id', $merchantId, PDO::PARAM_INT);
    $updateStmt->execute();
    
    // 如果是禁用商户，同时关闭共享试穿点数
    if ($action === 'exit') {
        $updateShareSql = "UPDATE users SET share_try_on_credits = 0 WHERE id = :user_id";
        $updateShareStmt = $conn->prepare($updateShareSql);
        $updateShareStmt->bindValue(':user_id', $merchantId, PDO::PARAM_INT);
        $updateShareStmt->execute();
    }
    
    // 提交事务
    $conn->commit();
    
    $response['message'] = $action === 'join' ? '商户入驻成功' : '商户已禁用';
} catch (Exception $e) {
    // 回滚事务
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    $response['code'] = 500;
    $response['message'] = '处理请求时发生错误: ' . $e->getMessage();
    error_log("admin_update_merchant_status.php - 错误: " . $e->getMessage());
} finally {
    // PDO connections are closed automatically when the variable is unset
    $conn = null;
}

echo json_encode($response); 