<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>淘宝商品管理 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 淘宝商品管理页面样式 */
        .filter-form {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        
        .form-group {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }
        
        .form-group label {
            margin-right: 10px;
            white-space: nowrap;
        }
        
        .form-control {
            padding: 6px 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            min-width: 100px;
        }
        
        .btn-search {
            padding: 6px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn-search:hover {
            background-color: #40a9ff;
        }
        
        .btn-reset {
            padding: 6px 16px;
            background-color: #f2f2f2;
            color: #333;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn-reset:hover {
            background-color: #e6e6e6;
        }
        
        .btn-sync {
            padding: 6px 16px;
            background-color: #52c41a;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: auto;
        }
        
        .btn-sync:hover {
            background-color: #73d13d;
        }
        
        /* 表格样式 */
        .product-list {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .product-list th,
        .product-list td {
            padding: 10px;
            border: 1px solid #e8e8e8;
            text-align: left;
        }
        
        .product-list th {
            background-color: #fafafa;
            font-weight: 500;
        }
        
        .product-list tr:hover {
            background-color: #f5f5f5;
        }
        
        .product-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        .product-title {
            max-width: 300px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .price {
            color: #ff4d4f;
            font-weight: bold;
        }
        
        .discount {
            color: #52c41a;
            font-size: 0.85em;
        }
        
        .action-btns {
            display: flex;
            gap: 5px;
        }
        
        .btn-view {
            padding: 4px 10px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85em;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-view:hover {
            background-color: #40a9ff;
        }
        
        .btn-recommend {
            padding: 4px 10px;
            background-color: #faad14;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85em;
        }
        
        .btn-recommend:hover {
            background-color: #ffc53d;
        }
        
        .btn-remove-recommend {
            padding: 4px 10px;
            background-color: #8c8c8c;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85em;
        }
        
        .btn-remove-recommend:hover {
            background-color: #a6a6a6;
        }
        
        /* 淘口令样式 */
        .tpwd-container {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .tpwd-text {
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .btn-copy {
            padding: 2px 6px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
        }
        
        .btn-copy:hover {
            background-color: #40a9ff;
        }
        
        .tpwd-fake {
            color: #faad14;
            font-size: 0.8em;
            margin-left: 3px;
        }
        
        .tpwd-real {
            color: #52c41a;
            font-size: 0.8em;
            margin-left: 3px;
        }
        
        .tpwd-none {
            color: #8c8c8c;
            font-style: italic;
        }
        
        /* 标签样式 */
        .tags-container {
            max-width: 150px;
        }
        
        .tags-list {
            list-style: none;
            padding: 0;
            margin: 0 0 5px 0;
        }
        
        .tag-item {
            display: inline-block;
            background-color: #e6f7ff;
            color: #1890ff;
            padding: 2px 6px;
            border-radius: 3px;
            margin: 0 3px 3px 0;
            font-size: 0.8em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 140px;
        }
        
        .tag-category {
            color: #52c41a;
            font-size: 0.85em;
            margin-top: 3px;
            font-weight: bold;
        }
        
        .tags-none {
            color: #8c8c8c;
            font-style: italic;
        }
        
        .tags-error {
            color: #ff4d4f;
            font-style: italic;
        }
        
        .tags-raw {
            font-size: 0.8em;
            color: #8c8c8c;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 150px;
        }
        
        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
        }
        
        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            background-color: #fff;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .pagination-btn:hover {
            background-color: #e6f7ff;
        }
        
        .pagination-btn:disabled {
            color: #d9d9d9;
            cursor: not-allowed;
            background-color: #f5f5f5;
        }
        
        .pagination-active {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        
        .pagination-active:hover {
            background-color: #40a9ff;
        }
        
        .pagination-info {
            color: #666;
        }
        
        /* 空数据状态 */
        .empty-state {
            text-align: center;
            padding: 40px 0;
            color: #8c8c8c;
        }
        
        /* 同步状态模态框 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
        }
        
        .modal-content {
            background-color: #fff;
            margin: 15% auto;
            padding: 20px;
            border-radius: 4px;
            width: 400px;
            max-width: 80%;
        }
        
        .modal-title {
            font-size: 18px;
            margin-bottom: 15px;
        }
        
        .modal-body {
            margin-bottom: 20px;
        }
        
        .sync-progress {
            height: 6px;
            background-color: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .sync-progress-fill {
            height: 100%;
            background-color: #1890ff;
            border-radius: 3px;
            transition: width 0.3s ease;
        }
        
        .sync-status {
            color: #8c8c8c;
            font-size: 14px;
        }
        
        .modal-footer {
            text-align: right;
        }
        
        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 40px 0;
            color: #8c8c8c;
        }
        
        /* 错误消息 */
        .error-message {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #f5222d;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>淘宝商品管理</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <!-- 过滤表单 -->
            <div class="filter-form">
                <div class="form-group">
                    <label for="keyword">关键词</label>
                    <input type="text" id="keyword" class="form-control" placeholder="标题/店铺名">
                </div>
                <div class="form-group">
                    <label for="material_id">物料分类</label>
                    <select id="material_id" class="form-control">
                        <option value="">全部</option>
                        <!-- 物料分类选项将通过JS动态加载 -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="category">商品分类</label>
                    <select id="category" class="form-control">
                        <option value="">全部</option>
                        <!-- 商品分类选项将通过JS动态加载 -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="price_range">价格区间</label>
                    <input type="number" id="min_price" class="form-control" placeholder="最低价" style="width: 80px;">
                    <span>-</span>
                    <input type="number" id="max_price" class="form-control" placeholder="最高价" style="width: 80px;">
                </div>
                <div class="form-group">
                    <label for="tpwd_type">淘口令类型</label>
                    <select id="tpwd_type" class="form-control">
                        <option value="">全部</option>
                        <option value="real">真实淘口令</option>
                        <option value="fake">模拟淘口令</option>
                        <option value="none">无淘口令</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="coupon_only">
                        仅显示有优惠券
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="is_recommend">
                        仅显示推荐商品
                    </label>
                </div>
                <button id="searchBtn" class="btn-search">搜索</button>
                <button id="resetBtn" class="btn-reset">重置</button>
                <button id="syncBtn" class="btn-sync">手动同步</button>
            </div>
            
            <!-- 错误消息 -->
            <div id="errorMessage" class="error-message" style="display: none;"></div>
            
            <!-- 加载状态 -->
            <div id="loading" class="loading">
                <p>加载中，请稍候...</p>
            </div>
            
            <!-- 商品列表 -->
            <div id="productTableContainer" style="display: none;">
                <table class="product-list">
                    <thead>
                        <tr>
                            <th width="70">图片</th>
                            <th>商品名称</th>
                            <th>价格</th>
                            <th>标签</th>
                            <th>佣金比例</th>
                            <th>淘口令</th>
                            <th>店铺</th>
                            <th>分类</th>
                            <th>更新时间</th>
                            <th width="120">操作</th>
                        </tr>
                    </thead>
                    <tbody id="productList">
                        <!-- 商品列表将通过JS动态加载 -->
                    </tbody>
                </table>
                
                <!-- 分页控件 -->
                <div class="pagination" id="pagination">
                    <button id="prevBtn" class="pagination-btn">上一页</button>
                    <div id="pageNumbers"></div>
                    <button id="nextBtn" class="pagination-btn">下一页</button>
                    <div class="pagination-info" id="paginationInfo"></div>
                </div>
            </div>
            
            <!-- 空状态 -->
            <div id="emptyState" class="empty-state" style="display: none;">
                <p>暂无商品数据，请检查筛选条件或进行同步</p>
            </div>
            
            <!-- 同步状态模态框 -->
            <div id="syncModal" class="modal">
                <div class="modal-content">
                    <div class="modal-title">
                        <h3>商品数据同步</h3>
                    </div>
                    <div class="modal-body">
                        <div class="sync-progress">
                            <div id="syncProgressBar" class="sync-progress-fill" style="width: 0;"></div>
                        </div>
                        <div id="syncStatus" class="sync-status">正在准备同步...</div>
                    </div>
                    <div class="modal-footer">
                        <button id="closeSyncModal" class="btn-reset" disabled>关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <!-- <script src="js/taobao_product_list.js"></script> -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为taobao_product
            Sidebar.init('taobao_product');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 创建TaobaoProductList对象，提供完整的功能
            window.TaobaoProductList = {
                // 状态数据
                data: {
                    products: [],
                    page: 1,
                    limit: 10,
                    total: 0,
                    totalPages: 1,
                filters: {
                    keyword: '',
                        material_id: '',
                    category: '',
                        min_price: '',
                        max_price: '',
                        tpwd_type: '',
                        coupon_only: false,
                        is_recommend: false
                    }
                },
                
                init: function() {
                    // 绑定事件处理器
                    document.getElementById('searchBtn').addEventListener('click', this.handleSearch.bind(this));
                    document.getElementById('resetBtn').addEventListener('click', this.handleReset.bind(this));
                    document.getElementById('syncBtn').addEventListener('click', this.handleSync.bind(this));
                    document.getElementById('prevBtn').addEventListener('click', this.prevPage.bind(this));
                    document.getElementById('nextBtn').addEventListener('click', this.nextPage.bind(this));
                    document.getElementById('closeSyncModal').addEventListener('click', this.closeSyncModal.bind(this));
                    
                    // 加载物料分类和商品分类选项
                    this.loadCategoryOptions();
                    
                    // 加载数据
                    this.loadData();
                },
                
                loadCategoryOptions: function() {
                    const materialSelect = document.getElementById('material_id');
                    const categorySelect = document.getElementById('category');
                    
                    // 使用现有的分类数据
                    const materials = [
                        { id: '86623', name: '品牌女装' },
                        { id: '86595', name: '品牌精选' },
                        { id: '86620', name: '鞋包配饰' },
                        { id: '86619', name: '美妆个护' }
                    ];
                    
                    const categories = [
                        { id: 'tops', name: '上衣' },
                        { id: 'bottoms', name: '裤子' },
                        { id: 'dresses', name: '连衣裙' },
                        { id: 'outerwear', name: '外套' },
                        { id: 'shoes', name: '鞋子' },
                        { id: 'accessories', name: '配饰' }
                    ];
                    
                    // 添加物料分类选项
                    materials.forEach(material => {
                        const option = document.createElement('option');
                        option.value = material.id;
                        option.textContent = material.name;
                        materialSelect.appendChild(option);
                    });
                    
                    // 添加商品分类选项
                    categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        categorySelect.appendChild(option);
                    });
                },
                
                handleSearch: function() {
                    // 获取过滤条件
                    this.data.filters.keyword = document.getElementById('keyword').value.trim();
                    this.data.filters.material_id = document.getElementById('material_id').value;
                    this.data.filters.category = document.getElementById('category').value;
                    this.data.filters.min_price = document.getElementById('min_price').value;
                    this.data.filters.max_price = document.getElementById('max_price').value;
                    
                    // 获取淘口令类型，确保与后端API参数匹配
                    const tpwdType = document.getElementById('tpwd_type').value;
                    this.data.filters.tpwd_type = tpwdType; // 直接使用选中的值（real/fake/none）
                    
                    this.data.filters.coupon_only = document.getElementById('coupon_only').checked;
                    this.data.filters.is_recommend = document.getElementById('is_recommend').checked;
                    
                    // 重置页码
                    this.data.page = 1;
                    
                    // 加载数据
                    this.loadData();
                },
                
                handleReset: function() {
                    // 重置所有过滤条件
                    document.getElementById('keyword').value = '';
                    document.getElementById('material_id').value = '';
                    document.getElementById('category').value = '';
                    document.getElementById('min_price').value = '';
                    document.getElementById('max_price').value = '';
                    document.getElementById('tpwd_type').value = '';
                    document.getElementById('coupon_only').checked = false;
                    document.getElementById('is_recommend').checked = false;
                    
                    // 重置过滤条件和页码
                    this.data.filters = {
                        keyword: '',
                        material_id: '',
                        category: '',
                        min_price: '',
                        max_price: '',
                        tpwd_type: '',
                        coupon_only: false,
                        is_recommend: false
                    };
                    this.data.page = 1;
                    
                    // 加载数据
                    this.loadData();
                },
                
                handleSync: function() {
                    // 显示同步模态框
                    document.getElementById('syncModal').style.display = 'block';
                    document.getElementById('syncStatus').textContent = '正在同步商品数据...';
                    document.getElementById('syncProgressBar').style.width = '0%';
                    document.getElementById('closeSyncModal').disabled = true;
                    
                    // 获取token
                    const token = Auth.getToken();
                    if (!token) {
                        document.getElementById('syncStatus').textContent = '授权失败，请重新登录';
                        document.getElementById('syncProgressBar').style.width = '100%';
                        document.getElementById('closeSyncModal').disabled = false;
                        return;
                    }
                    
                    // 发起API请求
                    fetch('../login_backend/trigger_taobao_sync.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer ' + token
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('网络响应异常: ' + response.status);
                        }
                        return response.text();
                    })
                    .then(text => {
                        try {
                            // 尝试解析JSON
                            return JSON.parse(text);
                        } catch (e) {
                            // 如果不是JSON，直接返回文本
                            return { error: false, msg: '同步过程已启动，请等待完成' };
                        }
                    })
                    .then(data => {
                        document.getElementById('syncProgressBar').style.width = '100%';
                        
                        if (data.error === false) {
                            document.getElementById('syncStatus').textContent = '同步请求已发送，正在后台处理...';
                        } else {
                            document.getElementById('syncStatus').textContent = '同步请求发送成功: ' + (data.msg || '未知信息');
                        }
                        
                        document.getElementById('closeSyncModal').disabled = false;
                        
                        // 模拟同步过程 - 实际上同步操作是在后台进行的
                        setTimeout(() => {
                            // 重新加载数据
                            this.loadData();
                        }, 5000);  // 5秒后刷新数据
                    })
                    .catch(error => {
                        console.error('同步请求失败:', error);
                        document.getElementById('syncProgressBar').style.width = '100%';
                        document.getElementById('syncStatus').textContent = '同步失败: ' + error.message;
                        document.getElementById('closeSyncModal').disabled = false;
                    });
                },
                
                closeSyncModal: function() {
                    document.getElementById('syncModal').style.display = 'none';
                },
                
                loadData: function() {
                    // 显示加载中
                    document.getElementById('loading').style.display = 'block';
                    document.getElementById('productTableContainer').style.display = 'none';
                    document.getElementById('emptyState').style.display = 'none';
                    document.getElementById('errorMessage').style.display = 'none';
                    
                    // 获取token
                    const token = Auth.getToken();
                    if (!token) {
                        console.error('未找到授权令牌');
                        document.getElementById('errorMessage').style.display = 'block';
                        document.getElementById('errorMessage').textContent = '授权失败，请重新登录';
                        document.getElementById('loading').style.display = 'none';
                        this.loadMockData();
                        return;
                    }
                    
                    // 发起API请求
                    fetch('../login_backend/get_stored_taobao_products.php?page=' + this.data.page + '&page_size=' + this.data.limit + this.buildQueryParams(), {
                        method: 'GET',
                        headers: {
                            'Authorization': 'Bearer ' + token
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            if (response.status === 401) {
                                throw new Error('授权失败，请重新登录');
                            } else {
                                throw new Error('网络响应异常: ' + response.status);
                            }
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('API响应:', data);
                        
                        if (data.error === false) {
                            // 检查是否有数据
                            if (data.data && Array.isArray(data.data) && data.data.length > 0) {
                                this.data.products = data.data;
                                
                                // 获取分页信息
                                if (data.pagination) {
                                    this.data.total = data.pagination.total || 0;
                                    this.data.page = data.pagination.current_page || 1;
                                    this.data.totalPages = data.pagination.total_pages || 1;
                                } else {
                                    this.data.total = data.data.length;
                                    this.data.totalPages = 1;
                                }
                                
                                // 渲染数据
                                this.renderProducts();
                                this.updatePagination();
                        
                                // 显示表格
                                document.getElementById('loading').style.display = 'none';
                                document.getElementById('productTableContainer').style.display = 'block';
                            } else {
                                // 没有数据
                                document.getElementById('loading').style.display = 'none';
                                document.getElementById('emptyState').style.display = 'block';
                            }
                        } else {
                            throw new Error(data.msg || '获取数据失败');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('loading').style.display = 'none';
                        document.getElementById('errorMessage').style.display = 'block';
                        document.getElementById('errorMessage').textContent = '加载数据失败: ' + error.message;
                        
                        // 如果失败，使用模拟数据
                        if (error.message.includes('授权失败') || error.message.includes('网络响应异常')) {
                            this.loadMockData();
                        }
                    });
                },
                
                // 构建查询参数
                buildQueryParams: function() {
                    let params = '';
                    
                    if (this.data.filters.keyword) {
                        params += '&keyword=' + encodeURIComponent(this.data.filters.keyword);
                    }
                    
                    if (this.data.filters.material_id) {
                        params += '&material_id=' + this.data.filters.material_id;
                    }
                    
                    if (this.data.filters.category) {
                        params += '&category=' + encodeURIComponent(this.data.filters.category);
                    }
                    
                    if (this.data.filters.min_price) {
                        params += '&min_price=' + this.data.filters.min_price;
                    }
                    
                    if (this.data.filters.max_price) {
                        params += '&max_price=' + this.data.filters.max_price;
                    }
                    
                    if (this.data.filters.tpwd_type) {
                        params += '&tpwd_type=' + this.data.filters.tpwd_type;
                    }
                    
                    if (this.data.filters.coupon_only) {
                        params += '&coupon_only=1';
                    }
                    
                    if (this.data.filters.is_recommend) {
                        params += '&is_recommend=1';
                    }
                    
                    return params;
                },
                
                // 加载模拟数据（API失败时的后备方案）
                loadMockData: function() {
                    console.log('使用模拟数据');
                    
                    // 生成模拟数据
                    const mockProducts = [];
                    
                    for (let i = 1; i <= 20; i++) {
                        const productId = 1000 + i;
                        const materialId = this.data.filters.material_id || String(Math.floor(Math.random() * 5) + 1);
                        const categories = ['tops', 'bottoms', 'dresses', 'outerwear', 'shoes', 'accessories'];
                        const category = this.data.filters.category || categories[Math.floor(Math.random() * categories.length)];
                        
                        mockProducts.push({
                            id: productId,
                            title: `测试商品${productId} - ${category}` + (this.data.filters.keyword ? ` ${this.data.filters.keyword}` : ''),
                            image_url: `https://via.placeholder.com/60x60?text=${productId}`,
                            price: Math.floor(Math.random() * 300) + 50,
                            original_price: Math.floor(Math.random() * 500) + 100,
                            coupon_amount: Math.random() > 0.7 ? Math.floor(Math.random() * 50) + 10 : 0,
                            commission_rate: Math.floor(Math.random() * 30) + 5,
                            tpwd: `￥ABCDE${productId}￥`,
                            tpwd_type: Math.random() > 0.5 ? 'real' : 'fake',
                            shop: `测试店铺${Math.floor(Math.random() * 10) + 1}`,
                            category: category,
                            material_id: materialId,
                            tags: ['新款', '热销', '限时'],
                            is_recommend: Math.random() > 0.8,
                            updated_at: new Date(Date.now() - Math.floor(Math.random() * 30) * 86400000).toISOString()
                        });
                    }
                    
                    // 保存模拟数据
                    this.data.products = mockProducts;
                    this.data.total = 100;
                    this.data.totalPages = 5;
                    
                    // 渲染数据
                    this.renderProducts();
                    this.updatePagination();
                    
                    // 显示表格
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('productTableContainer').style.display = 'block';
                },
                
                renderProducts: function() {
                    const productList = document.getElementById('productList');
                    productList.innerHTML = '';
                    
                    this.data.products.forEach(product => {
                        const row = document.createElement('tr');
                        
                        // 格式化价格
                        const price = product.final_price ? parseFloat(product.final_price).toFixed(2) : 
                                     (product.zk_final_price ? parseFloat(product.zk_final_price).toFixed(2) : "0.00");
                        const originalPrice = product.original_price ? parseFloat(product.original_price).toFixed(2) : null;
                        const discount = originalPrice && price ? Math.round((1 - parseFloat(price) / parseFloat(originalPrice)) * 100) : null;
                        
                        // 格式化更新时间 - 兼容两种字段名
                        let formattedDate = '';
                        if (product.last_sync_time) {
                            const updatedDate = new Date(product.last_sync_time);
                            formattedDate = `${updatedDate.getFullYear()}-${String(updatedDate.getMonth() + 1).padStart(2, '0')}-${String(updatedDate.getDate()).padStart(2, '0')}`;
                        } else if (product.updated_at) {
                            const updatedDate = new Date(product.updated_at);
                            formattedDate = `${updatedDate.getFullYear()}-${String(updatedDate.getMonth() + 1).padStart(2, '0')}-${String(updatedDate.getDate()).padStart(2, '0')} ${String(updatedDate.getHours()).padStart(2, '0')}:${String(updatedDate.getMinutes()).padStart(2, '0')}`;
                        }
                        
                        // 淘口令类型
                        let tpwdHtml = '';
                        if (product.tpwd) {
                            // 优先使用is_fake_tpwd字段（与老版本兼容）
                            const isFakeTpwd = product.is_fake_tpwd === 1 || product.is_fake_tpwd === '1' || product.tpwd_type === 'fake';
                            const tpwdClass = !isFakeTpwd ? 'tpwd-real' : 'tpwd-fake';
                            const tpwdLabel = !isFakeTpwd ? '真实' : '模拟';
                            tpwdHtml = `
                                <div class="tpwd-container">
                                    <div class="tpwd-text">${product.tpwd}</div>
                                    <button class="btn-copy" onclick="TaobaoProductList.copyTpwd('${product.tpwd}')">复制</button>
                                    <span class="${tpwdClass}">${tpwdLabel}</span>
                                </div>
                            `;
                        } else {
                            tpwdHtml = '<span class="tpwd-none">无淘口令</span>';
                        }
                        
                        // 处理标签 - 兼容两种可能的标签格式
                        let tagsHtml = '';
                        if (product.tags) {
                            try {
                                // 尝试解析JSON格式的标签
                                const tagsData = typeof product.tags === 'object' ? product.tags : JSON.parse(product.tags);
                                if (tagsData) {
                                    tagsHtml = '<div class="tags-container">';
                                    
                                    // 根据不同的商品分类，显示不同的标签内容
                                    if (product.category === '品牌女装' && tagsData['衣物标签']) {
                                        // 女装类商品显示衣物标签
                                        tagsHtml += '<ul class="tags-list">';
                                        tagsData['衣物标签'].slice(0, 3).forEach(tag => {
                                            tagsHtml += `<li class="tag-item">${tag}</li>`;
                                        });
                                        tagsHtml += '</ul>';
                                        if (tagsData['衣物类别']) {
                                            tagsHtml += `<div class="tag-category">${tagsData['衣物类别']}</div>`;
                                        }
                                    } else if (tagsData['商品标签'] || tagsData['tags']) {
                                        // 通用商品标签
                                        const tags = tagsData['商品标签'] || tagsData['tags'] || [];
                                        if (tags.length > 0) {
                                            tagsHtml += '<ul class="tags-list">';
                                            tags.slice(0, 3).forEach(tag => {
                                                tagsHtml += `<li class="tag-item">${tag}</li>`;
                                            });
                                            tagsHtml += '</ul>';
                                        }
                                        
                                        const category = tagsData['商品类别'] || tagsData['category'] || product.category;
                                        if (category) {
                                            tagsHtml += `<div class="tag-category">${category}</div>`;
                                        }
                                    } else if (Array.isArray(tagsData)) {
                                        // 如果标签是简单数组
                                        tagsHtml += '<ul class="tags-list">';
                                        tagsData.slice(0, 3).forEach(tag => {
                                            tagsHtml += `<li class="tag-item">${tag}</li>`;
                                        });
                                        tagsHtml += '</ul>';
                                    } else {
                                        // 如果没有找到标准格式的标签，直接显示JSON内容的前30个字符
                                        const tagsStr = JSON.stringify(tagsData);
                                        tagsHtml = `<div class="tags-raw" title="${tagsStr}">${tagsStr.substring(0, 30)}...</div>`;
                                    }
                                    
                                    tagsHtml += '</div>';
                                }
                            } catch (e) {
                                console.error('解析标签JSON失败:', e);
                                // 如果解析失败，尝试显示原始标签内容
                                if (typeof product.tags === 'string' && product.tags.length > 0) {
                                    tagsHtml = `<div class="tags-raw" title="${product.tags}">${product.tags.substring(0, 30)}...</div>`;
                                } else {
                                    tagsHtml = '<span class="tags-error">标签格式错误</span>';
                                }
                            }
                        }
                        
                        if (!tagsHtml) {
                            tagsHtml = '<span class="tags-none">无标签</span>';
                        }
                        
                        // 计算佣金比例和金额
                        let commissionRate = product.commission_rate || 0;
                        if (commissionRate > 1) {
                            commissionRate = commissionRate / 100; // 如果大于1，假设是百分比形式
                        }
                        const commissionAmount = parseFloat(price) * commissionRate;
                        const formattedCommissionRate = (commissionRate * 100).toFixed(1);
                        const formattedCommissionAmount = commissionAmount.toFixed(2);

                        row.innerHTML = `
                            <td><img src="${product.image_url}" alt="${product.title}" class="product-image"></td>
                            <td class="product-title" title="${product.title}">${product.title}</td>
                            <td>
                                <div class="price">￥${price}</div>
                                ${originalPrice ? `
                                    <div class="original-price">￥${originalPrice}</div>
                                    <div class="discount">${discount}%折扣</div>
                                ` : ''}
                                ${product.coupon_amount > 0 ? `<div class="coupon">券￥${product.coupon_amount}</div>` : ''}
                            </td>
                            <td>${tagsHtml}</td>
                            <td>${formattedCommissionRate}%<br>(￥${formattedCommissionAmount})</td>
                            <td>${tpwdHtml}</td>
                            <td>${product.shop_title || product.shop || ''}</td>
                            <td>${this.getCategoryName(product.category)}</td>
                            <td>${formattedDate}</td>
                            <td>
                                <div class="action-btns">
                                    <a href="#" class="btn-view" onclick="TaobaoProductList.viewProduct('${product.id}')">查看</a>
                                    ${product.is_recommend ? 
                                        `<button class="btn-remove-recommend" onclick="TaobaoProductList.toggleRecommend('${product.id}', false)">取消推荐</button>` : 
                                        `<button class="btn-recommend" onclick="TaobaoProductList.toggleRecommend('${product.id}', true)">推荐</button>`
                                    }
                                </div>
                            </td>
                        `;
                        
                        productList.appendChild(row);
                    });
                },
                
                updatePagination: function() {
                    const pageNumbers = document.getElementById('pageNumbers');
                    const paginationInfo = document.getElementById('paginationInfo');
                    const prevBtn = document.getElementById('prevBtn');
                    const nextBtn = document.getElementById('nextBtn');
                    
                    // 更新分页信息
                    paginationInfo.textContent = `共 ${this.data.total} 条，第 ${this.data.page}/${this.data.totalPages} 页`;
                    
                    // 更新按钮状态
                    prevBtn.disabled = this.data.page <= 1;
                    nextBtn.disabled = this.data.page >= this.data.totalPages;
                    
                    // 清空页码按钮
                    pageNumbers.innerHTML = '';
                    
                    // 生成页码按钮
                    const maxButtons = 5;
                    let startPage = Math.max(1, this.data.page - Math.floor(maxButtons / 2));
                    let endPage = Math.min(this.data.totalPages, startPage + maxButtons - 1);
                    
                    if (endPage - startPage + 1 < maxButtons) {
                        startPage = Math.max(1, endPage - maxButtons + 1);
                    }
                    
                    for (let i = startPage; i <= endPage; i++) {
                        const pageButton = document.createElement('button');
                        pageButton.className = 'pagination-btn' + (i === this.data.page ? ' pagination-active' : '');
                        pageButton.textContent = i;
                        pageButton.onclick = () => this.goToPage(i);
                        pageNumbers.appendChild(pageButton);
                    }
                },
                
                prevPage: function() {
                    if (this.data.page > 1) {
                        this.data.page--;
                        this.loadData();
                    }
                },
                
                nextPage: function() {
                    if (this.data.page < this.data.totalPages) {
                        this.data.page++;
                        this.loadData();
                    }
                },
                
                goToPage: function(page) {
                    if (this.data.page !== page) {
                        this.data.page = page;
                        this.loadData();
                    }
                },
                
                viewProduct: function(id) {
                    // 获取token
                    const token = Auth.getToken();
                    if (!token) {
                        alert('授权失败，请重新登录');
                        return;
                    }
                    
                    // 通过淘口令API获取商品链接
                    fetch(`../login_backend/get_taobao_link.php?item_id=${id}&need_tpwd=1`, {
                        method: 'GET',
                        headers: {
                            'Authorization': token
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.error === false && data.data) {
                            // 在新窗口打开商品链接
                            window.open(data.data.promotion_url, '_blank');
                        } else {
                            alert('获取商品链接失败: ' + (data.msg || '未知错误'));
                        }
                    })
                    .catch(error => {
                        console.error('获取商品链接失败:', error);
                        alert('获取商品链接失败: ' + error.message);
                    });
                },
                
                toggleRecommend: function(id, isRecommend) {
                    alert(`${isRecommend ? '设置' : '取消'}推荐商品（ID: ${id}）操作已发送`);
                    
                    // 模拟操作成功后重新加载数据
                    setTimeout(() => {
                        // 实际应用中，这里应该发起API请求更新推荐状态
                        // 修改本地数据以便立即看到效果
                        this.data.products.forEach(product => {
                            if (product.id == id) {
                                product.is_recommend = isRecommend;
                            }
                        });
                        
                        this.renderProducts();
                    }, 500);
                },
                
                copyTpwd: function(tpwd) {
                    // 创建临时文本区域
                            const textarea = document.createElement('textarea');
                    textarea.value = tpwd;
                            document.body.appendChild(textarea);
                            textarea.select();
                            
                    try {
                        // 执行复制命令
                        document.execCommand('copy');
                        alert('淘口令已复制到剪贴板');
                    } catch (err) {
                        console.error('复制淘口令失败:', err);
                        alert('复制失败，请手动复制');
                    }
                    
                    // 移除临时元素
                            document.body.removeChild(textarea);
                },
                
                getCategoryName: function(category) {
                    const categoryMap = {
                        'tops': '上衣',
                        'bottoms': '裤子',
                        'dresses': '连衣裙',
                        'outerwear': '外套',
                        'shoes': '鞋子',
                        'accessories': '配饰'
                    };
                    
                    return categoryMap[category] || category;
                }
            };
            
            // 初始化淘宝商品列表
            TaobaoProductList.init();
        });
    </script>
</body>
</html> 