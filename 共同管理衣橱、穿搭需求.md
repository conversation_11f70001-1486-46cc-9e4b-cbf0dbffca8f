1.功能需求
  穿搭页面加一个 共同管理衣橱穿搭 功能，放在穿搭列表页面中，放在 穿搭广场 模块的右边 UI排版样式可以复用首页的形象分析与智能穿搭的一行双列样式
  点击进入共同管理衣橱穿搭页面，进入页面后需要判断：用户是否为创建者，还是成员，还是没有创建或者绑定关系，不同角色显示的内容不一样

  未创建或未绑定圈子用户的页面显示：我要当创建者、我要加入其他圈子，两个模块配套对应的文字描述，点击 我要当创建者 按钮，在页面底部弹出创建圈子弹框，弹框中需要显示文字描述：加入者将与你共享衣橱、衣物分类、衣物信息、穿搭分类、穿搭信息，输入圈子名称，备注内容，点击确定即可创建成功，点击我要加入其他圈子，在页面底部弹出 圈子邀请码 弹框，弹框中需要显示文字描述：加入圈子后将于圈子内人员共享衣橱、衣物分类、衣物信息、穿搭分类、穿搭信息，输入邀请码，邀请码验证通过后即可成功加入圈子

  创建者页面显示：圈子名称，圈子邀请码（右边带个复制图表，点击即可复制邀请码） 当前绑定成员数量 衣橱数量 穿搭分类数量 衣物分类数量 衣物总数 穿搭总数 衣物标签数 界面下方为成员列表，成员列表中显示用户头像、名称、绑定时间、上传衣物数量、搭配穿搭数量，还有一个解除绑定的按钮，点击解除绑定的按钮可以将用户踢出圈子，提出后，被踢者需要在 未创建或未绑定圈子用户的页面显示 页面中显示 什么时间 被 什么用户 解除绑定的消息弹框，用户点击我知道了即可关闭弹框，页面底部显示一个固定在底部的按钮：邀请好友 点击后将分享到微信，分享信息中带上圈子邀请码，被分享者点击分享信息进入小程序共同管理穿搭的邀请界面，可以看到邀请描述，点击底部确定加入按钮，即可加入创建者的穿搭圈子

  成员页面显示：加入穿搭圈子名称 圈子成员数、衣橱数量 穿搭分类数量 衣物分类数量 衣物总数 穿搭总数 衣物标签数、加入时间 下方显示一个按钮：退出圈子 点击即可解绑 解绑后可以回到未创建或未绑定圈子用户的页面，点击解除绑定的时候，需要有弹框提示：退出后将解绑衣物穿搭信息，请再次确认，点击确认后即可解绑成功

2.共同管理衣橱页面中用户角色权限
  圈子创建者：可查看已经接收邀请用户、可以在已邀请用户列表解除邀请，创建/分享邀请码，可对衣橱、衣物分类、衣物信息、穿搭分类、穿搭信息进行增删改查的操作
  加入圈子用户：可查看已经加入穿搭圈子，可以退出圈子，可对衣橱、衣物分类、衣物信息、穿搭分类、穿搭信息进行增改查的操作，退出圈子后不影响现有圈子中的衣橱、衣物分类、衣物信息、穿搭分类、穿搭信息。

3.加入圈子方式
  1.发送邀请码给其他用户 被邀请用户在穿搭圈子输入邀请码，即可加入穿搭圈子
  2.邀请码页面点击邀请按钮 分享给用户，被分享用户点同意，即可加入穿搭圈子

4.退出圈子
    圈子主人：解散圈子时，弹框提示解散模式：选项1：衣物穿搭数据只保留给自己，选项2：衣物穿搭数据保留给所有成员（包含创建者） 两个选项单选
    加入圈子用户：
    限制1.预设不限制邀请人数
    限制2.已经加入共同管理衣橱圈子后，不可再加入其他衣橱圈子，只能退出后再进入

5.开发方案：
    方案一：基于圈子概念的独立表设计
创建独立的圈子表(outfit_circles)，通过圈子成员关系表(circle_members)管理用户关系，所有共享数据通过circle_id关联。这种方案数据隔离性好，但需要修改现有大量表结构。
    方案二：基于用户组的虚拟共享
在现有表基础上增加共享标识字段，通过用户组关系表管理共享权限。这种方案对现有系统影响较小，但数据权限控制相对复杂。
    方案三：混合式设计
结合前两种方案的优点，创建圈子管理表，但尽量复用现有的衣物、穿搭等表结构，通过权限控制实现数据共享。
考虑到需求中明确提到"退出圈子后不影响现有圈子中的衣橱、衣物分类、衣物信息、穿搭分类、穿搭信息"，以及要求UI与现有系统保持一致，我倾向于采用方案三。
这种方案的核心思路是：
创建圈子管理相关的核心表
在现有衣物、穿搭等表中增加圈子关联字段
通过API层面的权限控制实现数据共享
前端页面复用现有组件和样式
这样既能满足功能需求，又能最大程度保持系统的稳定性和一致性。