<?php
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

try {
    $auth = new Auth();
    
    // 验证用户token
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? null;
    
    if (!$authHeader || strpos($authHeader, 'Bearer ') !== 0) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => '未授权访问']);
        exit;
    }
    
    $token = substr($authHeader, 7);
    $tokenData = $auth->verifyToken($token);
    
    if (!$tokenData) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => 'Token无效']);
        exit;
    }
    
    $userId = $tokenData['user_id'] ?? $tokenData['sub'];
    
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => true, 'msg' => '无效的请求数据']);
        exit;
    }
    
    $name = trim($input['name'] ?? '');
    $code = trim($input['code'] ?? '');
    $sortOrder = (int)($input['sort_order'] ?? 0);
    
    // 验证必填字段
    if (empty($name)) {
        http_response_code(400);
        echo json_encode(['error' => true, 'msg' => '分类名称不能为空']);
        exit;
    }
    
    if (empty($code)) {
        // 如果没有提供code，自动生成
        $code = 'custom_' . time() . '_' . $userId;
    }
    
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查该用户是否已有同名分类
    $checkSql = "SELECT id FROM clothing_categories WHERE name = :name AND (user_id = :user_id OR is_system = 1)";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bindParam(':name', $name, PDO::PARAM_STR);
    $checkStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkStmt->execute();
    
    if ($checkStmt->rowCount() > 0) {
        http_response_code(400);
        echo json_encode(['error' => true, 'msg' => '分类名称已存在']);
        exit;
    }
    
    // 检查code是否已存在
    $checkCodeSql = "SELECT id FROM clothing_categories WHERE code = :code";
    $checkCodeStmt = $conn->prepare($checkCodeSql);
    $checkCodeStmt->bindParam(':code', $code, PDO::PARAM_STR);
    $checkCodeStmt->execute();
    
    if ($checkCodeStmt->rowCount() > 0) {
        // 如果code重复，重新生成
        $code = 'custom_' . time() . '_' . $userId . '_' . rand(1000, 9999);
    }
    
    // 插入新分类
    $insertSql = "INSERT INTO clothing_categories (user_id, name, code, is_system, sort_order) VALUES (:user_id, :name, :code, 0, :sort_order)";
    $insertStmt = $conn->prepare($insertSql);
    $insertStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $insertStmt->bindParam(':name', $name, PDO::PARAM_STR);
    $insertStmt->bindParam(':code', $code, PDO::PARAM_STR);
    $insertStmt->bindParam(':sort_order', $sortOrder, PDO::PARAM_INT);
    
    if ($insertStmt->execute()) {
        $categoryId = $conn->lastInsertId();
        
        echo json_encode([
            'error' => false,
            'msg' => '分类添加成功',
            'data' => [
                'id' => (int)$categoryId,
                'user_id' => $userId,
                'name' => $name,
                'code' => $code,
                'is_system' => false,
                'sort_order' => $sortOrder,
                'editable' => true
            ]
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => true, 'msg' => '添加分类失败']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '服务器错误: ' . $e->getMessage()
    ]);
} 