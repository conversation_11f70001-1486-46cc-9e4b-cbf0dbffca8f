const app = getApp();

Page({
  data: {
    categories: [], // 动态生成衣物分类
    currentCategory: '', // 默认显示的分类会自动设置
    loading: true,
    allClothes: [], // 所有衣物
    currentClothes: [], // 当前类别的衣物
    selectedClothes: {}, // 已选择的衣物 {id: true}
    canProceed: false, // 是否可以继续下一步
    selectedCount: 0, // 已选择的衣物数量
    showSkirtTip: false, // 是否显示裙子提示框
    wardrobeList: [], // 衣柜列表
    selectedWardrobeId: null, // 选中的衣柜ID，null表示全部
    originalAllClothes: [] // 所有衣柜的所有衣物
  },

  onLoad: function() {
    // 初始化变量，标记是否已显示过裙子提示框
    this.hasShownSkirtTip = false;
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '选择试穿衣物'
    });
    
    // 先加载分类列表，再加载衣柜列表
    this.loadCategories();
  },
  
  // 加载所有衣物分类
  loadCategories: function() {
    wx.showLoading({
      title: '加载中...',
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothing_categories.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log("获取衣物分类响应:", res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          // 处理分类数据，转换为界面需要的格式
          const categories = res.data.data.map(item => ({
            id: item.code,  // 使用分类代码作为ID
            name: item.name // 直接使用后端返回的中文名称
          }));
          
          this.setData({
            categories: categories
          });
          
          console.log("从API获取的分类列表:", categories);
        } else {
          console.error("获取衣物分类失败:", res.data.msg || "未知错误");
          // 失败时先继续加载衣物，之后会使用备用方法生成分类
        }
        
        // 无论成功失败，继续加载衣柜列表
        this.loadWardrobeList();
      },
      fail: (err) => {
        console.error("获取衣物分类网络错误:", err);
        // 继续加载衣柜列表
        this.loadWardrobeList();
      }
    });
  },
  
  // 加载衣柜列表
  loadWardrobeList: function() {
    wx.showLoading({
      title: '加载中...',
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_wardrobes.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log("获取衣柜列表响应:", res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          this.setData({
            wardrobeList: res.data.data || []
          });
          
          // 加载衣物数据
          this.loadClothingData();
        } else {
          console.error("获取衣柜列表失败:", res.data.msg || "未知错误");
          
          // 即使获取衣柜列表失败，也继续加载衣物数据
          this.loadClothingData();
          
          wx.showToast({
            title: res.data.msg || '获取衣柜列表失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error("获取衣柜列表网络错误:", err);
        
        // 即使获取衣柜列表失败，也继续加载衣物数据
        this.loadClothingData();
        
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 切换衣柜
  switchWardrobe: function(e) {
    const wardrobeId = e.currentTarget.dataset.id;
    
    // 如果点击的是当前选中的衣柜，不做任何操作
    if (this.data.selectedWardrobeId === wardrobeId) {
      return;
    }
    
    this.setData({
      selectedWardrobeId: wardrobeId
    });
    
    // 根据选中的衣柜筛选衣物
    this.filterClothingByWardrobe();
  },
  
  // 根据选中的衣柜筛选衣物
  filterClothingByWardrobe: function() {
    const { selectedWardrobeId, originalAllClothes } = this.data;
    
    if (selectedWardrobeId === null) {
      // 如果选择的是"全部衣物"，则显示所有衣物
      this.setData({
        allClothes: originalAllClothes
      }, () => {
        // 更新当前类别的衣物
        this.filterByCategory();
      });
    } else {
      // 筛选特定衣柜的衣物
      const filteredClothes = originalAllClothes.filter(item => 
        item.wardrobe_id == selectedWardrobeId
      );
      
      this.setData({
        allClothes: filteredClothes
      }, () => {
        // 更新当前类别的衣物
        this.filterByCategory();
      });
    }
  },
  
  // 加载衣物数据
  loadClothingData: function() {
    wx.showLoading({
      title: '加载中...',
    });
    
    console.log("开始获取衣物数据, URL:", `${app.globalData.apiBaseUrl}/get_clothes.php?all_clothes=1`);
    console.log("Authorization Token:", app.globalData.token ? "已设置" : "未设置");
    
    // 调用API获取衣物数据
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothes.php?all_clothes=1`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        
        console.log("获取衣物数据响应:", res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          const clothes = res.data.data;
          
          // 保存所有衣物，不做分类过滤
          const allClothes = clothes;
          
          console.log("所有衣物:", allClothes);
          
          // 保存所有衣柜的所有衣物
          this.setData({
            originalAllClothes: allClothes,
            allClothes: allClothes,
            loading: false
          });
          
          // 如果之前未成功获取分类，使用备用方法生成分类
          if (this.data.categories.length === 0) {
            this.generateCategoriesFromClothes();
          }
          
          // 应用衣柜筛选和分类筛选
          this.filterClothingByWardrobe();
          
          // 自动选择有衣物的分类
          this.autoSelectCategory();
        } else {
          console.error("获取衣物失败:", res.data.msg || "未知错误");
          
          this.setData({
            loading: false
          });
          
          wx.showToast({
            title: res.data.msg || '获取衣物失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error("获取衣物网络错误:", err);
        
        this.setData({
          loading: false
        });
        
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 切换衣物类别
  switchCategory: function(e) {
    const categoryId = e.currentTarget.dataset.id;
    
    // 更新当前类别
    this.setData({
      currentCategory: categoryId
    });
    
    // 过滤显示当前类别的衣物
    this.filterByCategory();
  },
  
  // 根据分类过滤衣物
  filterByCategory: function() {
    const { currentCategory, allClothes } = this.data;
    
    // 获取当前类别的衣物
    const currentCategoryClothes = allClothes.filter(
      item => item.category === currentCategory
    );
    
    console.log("当前类别的衣物:", currentCategoryClothes);
    
    this.setData({
      currentClothes: currentCategoryClothes
    });
    
    // 如果当前类别没有衣物，显示提示
    if (currentCategoryClothes.length === 0) {
      wx.showToast({
        title: '当前类别暂无衣物',
        icon: 'none'
      });
    }
  },
  
  // 选择衣物
  selectClothing: function(e) {
    const id = e.currentTarget.dataset.id;
    const category = e.currentTarget.dataset.category;
    const selectedItem = this.findClothingById(id);
    
    if (!selectedItem) return;
    
    // 复制当前已选衣物对象
    const newSelectedClothes = {...this.data.selectedClothes};
    
    // 检查是否已经选择了该衣物（再次点击表示取消选择）
    const isAlreadySelected = newSelectedClothes[id];
    
    if (isAlreadySelected) {
      // 取消选择
      delete newSelectedClothes[id];
    } else {
      // 如果选择的是裙子类别的衣物，且还没显示过提示，显示提示框
      if (category === 'skirts' && !this.hasShownSkirtTip) {
        this.setData({
          showSkirtTip: true
        });
      }
      
      // 检查同类型是否已经选择
      const hasSelectedSameCategory = Object.keys(newSelectedClothes).some(key => {
        const item = this.findClothingById(key);
        return item && item.category === category;
      });
      
      // 如果已经选择了同类型的衣物，则替换
      if (hasSelectedSameCategory) {
        Object.keys(newSelectedClothes).forEach(key => {
          const item = this.findClothingById(key);
          if (item && item.category === category) {
            delete newSelectedClothes[key];
          }
        });
      }
      
      // 添加新选择的衣物
      newSelectedClothes[id] = true;
    }
    
    // 更新已选衣物
    this.setData({
      selectedClothes: newSelectedClothes
    });
    
    // 检查是否可以继续下一步
    this.checkCanProceed();
  },
  
  // 检查是否可以继续下一步
  checkCanProceed: function() {
    const selectedClothes = this.data.selectedClothes;
    const selectedCount = Object.keys(selectedClothes).length;
    
    // 至少选择了一件衣物才能继续
    const canProceed = selectedCount > 0;
    
    this.setData({
      canProceed: canProceed,
      selectedCount: selectedCount
    });
  },
  
  // 根据ID查找衣物
  findClothingById: function(id) {
    return this.data.originalAllClothes.find(item => item.id == id);
  },
  
  // 关闭裙子提示框
  closeSkirtTip: function() {
    this.setData({
      showSkirtTip: false
    });
    // 标记已经显示过提示框
    this.hasShownSkirtTip = true;
  },
  
  // 获取已选衣物数组
  getSelectedClothesArray: function() {
    const selectedIds = Object.keys(this.data.selectedClothes);
    return this.data.originalAllClothes.filter(item => selectedIds.includes(item.id.toString()));
  },
  
  // 继续下一步，选择照片
  proceedToSelectPhoto: function() {
    if (!this.data.canProceed) {
      wx.showToast({
        title: '请至少选择一件衣物',
        icon: 'none'
      });
      return;
    }
    
    // 存储已选的衣物
    app.globalData.selectedClothes = this.getSelectedClothesArray();
    
    // 跳转到照片选择页面
    wx.navigateTo({
      url: '/pages/photos/select/index?mode=try_on'
    });
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },
  
  // 自动选择有衣物的分类
  autoSelectCategory: function() {
    const { allClothes, categories } = this.data;
    
    // 获取有衣物的分类
    const categoriesWithClothes = categories.filter(category => {
      return allClothes.some(item => item.category === category.id);
    });
    
    console.log("有衣物的分类:", categoriesWithClothes);
    
    if (categoriesWithClothes.length > 0) {
      // 自动选择第一个有衣物的分类
      this.setData({
        currentCategory: categoriesWithClothes[0].id
      });
      
      // 过滤显示当前类别的衣物
      this.filterByCategory();
    } else if (categories.length > 0) {
      // 如果没有找到有衣物的分类，但有分类列表，则选择第一个分类
      this.setData({
        currentCategory: categories[0].id
      });
      
      // 过滤显示当前类别的衣物
      this.filterByCategory();
    } else {
      // 如果既没有有衣物的分类，也没有分类列表，则显示提示
      wx.showToast({
        title: '没有可用的衣物分类',
        icon: 'none'
      });
    }
  },
  
  // 重命名原来的生成分类函数作为备用方法
  generateCategoriesFromClothes: function() {
    const { allClothes } = this.data;
    
    // 获取所有不重复的分类
    const uniqueCategories = [...new Set(allClothes.map(item => item.category))];
    
    // 分类名称映射
    const categoryNameMap = {
      'tops': '上衣',
      'pants': '裤子',
      'skirts': '裙子',
      'coats': '外套',
      'shoes': '鞋子',
      'bags': '包包',
      'accessories': '配饰',
      'dresses': '连衣裙',
      'suits': '套装',
      'underwear': '内衣',
      'swimwear': '泳装',
      'sportswear': '运动服',
      'outerwear': '外套'
    };
    
    // 生成分类列表
    const categories = uniqueCategories.map(category => {
      return {
        id: category,
        name: categoryNameMap[category] || category // 如果没有映射名称，就使用原始分类名
      };
    });
    
    console.log("根据衣物生成的分类列表:", categories);
    
    // 更新分类列表
    this.setData({
      categories: categories
    });
  }
}); 