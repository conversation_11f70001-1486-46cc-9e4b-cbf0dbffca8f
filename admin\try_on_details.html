<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>试衣详情 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* 试衣详情页面样式 */
        .back-link {
            display: inline-flex;
            align-items: center;
            color: #1890ff;
            margin-bottom: 15px;
            text-decoration: none;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 10px;
        }
        
        .tryOn-details {
            display: flex;
            margin-bottom: 20px;
        }
        
        .tryOn-image-container {
            width: 300px;
            height: 300px;
            overflow: hidden;
            border-radius: 4px;
            margin-right: 20px;
            border: 1px solid #f0f0f0;
        }
        
        .tryOn-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .tryOn-info {
            flex: 1;
        }
        
        .tryOn-id {
            color: #999;
            margin-bottom: 15px;
        }
        
        .tryOn-meta {
            margin-bottom: 15px;
        }
        
        .meta-item {
            margin-bottom: 8px;
        }
        
        .meta-label {
            display: inline-block;
            width: 100px;
            color: #666;
        }
        
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
        }
        
        .status-success {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .status-failed {
            background-color: #fff2f0;
            color: #f5222d;
            border: 1px solid #ffccc7;
        }
        
        .tryOn-actions {
            margin-top: 20px;
        }
        
        .action-btn {
            padding: 6px 15px;
            border-radius: 4px;
            border: none;
            margin-right: 10px;
            cursor: pointer;
            color: white;
            font-size: 14px;
        }
        
        .delete-btn {
            background-color: #f5222d;
        }
        
        .delete-btn:hover {
            background-color: #ff4d4f;
        }
        
        .user-info-section {
            background-color: #fafafa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .user-info-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
            object-fit: cover;
        }
        
        .user-name {
            font-size: 16px;
            font-weight: 500;
        }
        
        .user-meta {
            margin-bottom: 15px;
        }
        
        .user-meta-item {
            margin-bottom: 5px;
        }
        
        .view-btn {
            background-color: #1890ff;
        }
        
        .view-btn:hover {
            background-color: #40a9ff;
        }
        
        .empty-text {
            color: #999;
            text-align: center;
            padding: 20px 0;
        }
        
        /* 衣物列表样式 */
        .clothes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .clothes-item {
            border: 1px solid #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            background-color: white;
            transition: all 0.3s;
        }
        
        .clothes-item:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
        }
        
        .clothes-img {
            width: 100%;
            height: 100px;
            object-fit: cover;
        }
        
        .clothes-info {
            padding: 8px;
        }
        
        .clothes-name {
            font-size: 14px;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .clothes-category {
            font-size: 12px;
            color: #888;
        }
        
        /* 照片信息样式 */
        .photo-info-section {
            background-color: #fafafa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .photo-img {
            width: 120px;
            height: 120px;
            border-radius: 4px;
            margin-right: 15px;
            object-fit: cover;
        }
        
        .photo-detail {
            display: flex;
            margin-bottom: 10px;
        }
        
        .photo-detail-info {
            flex: 1;
        }
        
        .photo-type {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            background-color: #e6f7ff;
            color: #1890ff;
            margin-bottom: 5px;
        }
        
        .photo-description {
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .photo-date {
            color: #888;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>试衣详情</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="card">
                <a href="try_on_list.html" class="back-link">
                    &lt; 返回试衣历史列表
                </a>
                
                <div id="tryOnDetailContainer">
                    <div class="empty-text">加载中...</div>
                </div>
            </div>
            
            <div class="card" id="userInfoContainer">
                <div class="empty-text">加载中...</div>
            </div>
            
            <div class="card">
                <h3 class="section-title">使用的衣物</h3>
                <div id="clothesContainer" class="clothes-grid">
                    <div class="empty-text">加载中...</div>
                </div>
            </div>
            
            <div class="card" id="photoContainer">
                <h3 class="section-title">关联的照片</h3>
                <div id="photoInfoContainer">
                    <div class="empty-text">加载中...</div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/try_on_details.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为try_on
            Sidebar.init('try_on');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化试衣详情页面
            if (typeof TryOnDetails !== 'undefined') {
                TryOnDetails.init();
            }
        });
    </script>
</body>
</html> 