<?php
// 淘宝商品标签信息分析接口，部署在中转api

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method Not Allowed']);
    exit;
}

// 设置Gemini API密钥和模型
$apiKey = 'AIzaSyD1-g64EwoKNcvs0LeAn9hbyHJRuKj0Slg'; // 替换为您的实际API密钥
$model = 'gemini-1.5-flash';

// 构建Gemini API URL
$url = "https://generativelanguage.googleapis.com/v1beta/models/{$model}:generateContent?key={$apiKey}";

// 初始化请求数据
$geminiRequestData = [];

// 获取POST数据
$postData = json_decode(file_get_contents('php://input'), true);

// 检查必要参数
if (!isset($postData['title']) || empty($postData['title'])) {
    http_response_code(400);
    echo json_encode(['error' => '缺少商品标题']);
    exit;
}

$title = $postData['title'];
$category = $postData['category'] ?? '品牌女装'; // 默认分类为品牌女装

// 根据商品分类构建不同的提示词
$prompt = "分析以下淘宝商品标题，提取商品标签信息：\"{$title}\"";

// 根据不同商品分类设置不同的提示词模板
$jsonTemplate = "";
switch ($category) {
    case '品牌女装':
        $jsonTemplate = '{
          "衣物类别": "上衣、裤子、裙子、外套、连衣裙、套装中的一个或多个",
          "衣物标签": [
            "季节标签(春季/夏季/秋季/冬季中的一个或多个)",
            "场合标签(休闲/通勤/派对/约会/运动/居家中的一个或多个)",
            "风格标签(如简约/复古/优雅/甜美/街头/民族风等)",
            "材质标签(如棉/麻/丝/毛呢/针织等)",
            "流行元素标签(如荷叶边/泡泡袖/高腰/格纹等)"
          ],
          "衣物信息": {
            "衣物名称": "具体名称",
            "颜色": "颜色",
            "适合人群": "适合人群描述"
          }
        }';
        break;
        
    case '鞋包配饰':
        $jsonTemplate = '{
          "商品类别": "鞋子、包包、帽子、围巾、手套、配饰中的一个或多个",
          "商品标签": [
            "季节标签(春季/夏季/秋季/冬季中的一个或多个)",
            "场合标签(休闲/商务/派对/约会/运动/日常中的一个或多个)",
            "风格标签(如简约/复古/优雅/运动/户外等)",
            "材质标签(如皮革/帆布/尼龙/金属/布艺等)",
            "功能标签(如防水/轻便/耐磨/多功能等)"
          ],
          "商品信息": {
            "商品名称": "具体名称",
            "颜色": "颜色",
            "适合人群": "适合人群描述"
          }
        }';
        break;
        
    case '美妆个护':
        $jsonTemplate = '{
          "商品类别": "护肤品、彩妆、香水、洗护、美容工具中的一个或多个",
          "商品标签": [
            "功效标签(保湿/美白/抗老化/控油/清洁等)",
            "肤质适用标签(干性/油性/混合性/敏感性皮肤等)",
            "成分标签(如玻尿酸/维生素C/烟酰胺/视黄醇等)",
            "使用场合标签(日常/旅行/派对/职场等)",
            "产品形态标签(如乳液/面霜/精华/喷雾等)"
          ],
          "商品信息": {
            "商品名称": "具体名称",
            "品牌": "品牌名称",
            "适合人群": "适合人群描述"
          }
        }';
        break;
        
    case '品牌精选':
    case '天猫爆款':
    case '直营补贴':
    case '天天特卖':
    default:
        $jsonTemplate = '{
          "商品类别": "具体商品类别",
          "商品标签": [
            "功能标签(描述商品主要功能)",
            "品质标签(描述商品品质特点)",
            "适用场景标签(描述商品适用场景)",
            "目标人群标签(描述商品适用人群)",
            "特色标签(描述商品独特卖点)"
          ],
          "商品信息": {
            "商品名称": "具体名称",
            "品牌": "品牌名称",
            "主要特点": "商品主要特点描述"
          }
        }';
        break;
}

// 增强提示词以获得结构化输出
$enhanced_prompt = $prompt . " 请严格按照以下JSON格式返回结果，不要有任何额外的文本：" . $jsonTemplate;

// 构建请求
$geminiRequestData = [
    'contents' => [
        [
            'parts' => [
                [
                    'text' => $enhanced_prompt
                ]
            ]
        ]
    ],
    // 定义生成配置
    'generationConfig' => [
        'temperature' => 0.2,
        'topP' => 0.8,
        'topK' => 40
    ]
];

// 初始化cURL会话
$ch = curl_init($url);

// 设置cURL选项
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($geminiRequestData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

// 执行cURL请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);

// 关闭cURL会话
curl_close($ch);

// 检查cURL错误
if ($curlError) {
    http_response_code(500);
    echo json_encode(['error' => 'cURL Error: ' . $curlError]);
    exit;
}

// 检查HTTP状态码
if ($httpCode !== 200) {
    http_response_code($httpCode);
    echo $response; // 直接返回Gemini API的错误响应
    exit;
}

// 处理Gemini返回的结果，提取JSON部分
$responseData = json_decode($response, true);
$tags = '';

if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
    $text = $responseData['candidates'][0]['content']['parts'][0]['text'];
    
    // 尝试直接解析JSON文本
    $tags = json_decode($text, true);
    
    // 如果解析失败，尝试提取JSON部分
    if (json_last_error() !== JSON_ERROR_NONE) {
        // 尝试从文本中提取JSON部分
        if (preg_match('/\{.*\}/s', $text, $matches)) {
            $tags = json_decode($matches[0], true);
        }
    }
}

// 返回结果
if (empty($tags)) {
    echo json_encode([
        'success' => false,
        'message' => '无法解析标签信息',
        'raw_response' => $responseData
    ]);
} else {
    echo json_encode([
        'success' => true,
        'tags' => $tags
    ]);
}
?> 