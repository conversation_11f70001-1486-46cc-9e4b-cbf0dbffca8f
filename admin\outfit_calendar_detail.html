<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>穿搭日历详情 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* 页面主容器 */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px 0;
        }
        
        /* 时间线容器 */
        .timeline-container {
            position: relative;
            padding-left: 40px;
        }
        
        /* 时间线 */
        .timeline {
            position: absolute;
            top: 0;
            left: 20px;
            height: 100%;
            width: 2px;
            background-color: #e8e8e8;
        }
        
        /* 时间线节点 */
        .timeline-node {
            position: relative;
            margin-bottom: 40px;
        }
        
        .node-marker {
            position: absolute;
            left: -50px;
            top: 15px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #1890ff;
            border: 4px solid white;
            box-shadow: 0 0 0 2px #e8e8e8;
            z-index: 1;
        }
        
        .node-content {
            position: relative;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 0;
            width: 100%;
        }

        /* 节点头部 */
        .node-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .node-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }
        
        .node-date {
            font-size: 14px;
            color: #666;
            display: flex;
            align-items: center;
        }
        
        .node-date i {
            margin-right: 5px;
        }
        
        /* 节点内容 */
        .node-body {
            padding: 20px;
        }
        
        /* 穿搭信息 */
        .outfit-info {
            display: flex;
            margin-bottom: 20px;
            gap: 20px;
        }
        
        .outfit-meta {
            flex: 1;
        }
        
        .outfit-name {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 10px;
            color: #333;
        }
        
        .meta-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .meta-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 14px;
            color: #666;
        }
        
        .meta-label {
            width: 80px;
            color: #888;
        }
        
        .meta-value {
            flex: 1;
            color: #333;
        }
        
        .outfit-description {
            background-color: #f9f9f9;
            padding: 12px 15px;
            border-radius: 6px;
            margin-top: 15px;
            font-size: 14px;
            color: #555;
            line-height: 1.5;
        }
        
        /* 多图显示样式 */
        .outfit-images-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 20px;
        }
        
        .outfit-image-item {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 6px;
            border: 1px solid #eee;
            transition: all 0.2s;
            cursor: pointer;
        }
        
        .outfit-image-item:hover {
            transform: scale(1.03);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .outfit-items-title {
            font-size: 16px;
            font-weight: 500;
            margin: 25px 0 15px;
            padding-bottom: 8px;
            border-bottom: 1px dashed #eee;
            color: #333;
        }
        
        /* 穿搭物品网格 */
        .outfit-items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 15px;
        }
        
        .clothing-item {
            border: 1px solid #f0f0f0;
            border-radius: 6px;
            overflow: hidden;
            transition: all 0.2s;
        }
        
        .clothing-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .clothing-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        
        .clothing-info {
            padding: 10px;
        }
        
        .clothing-name {
            font-weight: 500;
            margin-bottom: 5px;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .clothing-category {
            color: #666;
            font-size: 12px;
        }
        
        /* 日历导航条 */
        .calendar-nav-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px 20px;
            margin-bottom: 30px;
        }
        
        .calendar-nav-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .calendar-nav {
            display: flex;
            overflow-x: auto;
            gap: 10px;
            padding: 5px 0;
            scrollbar-width: thin;
        }
        
        .calendar-item {
            min-width: 90px;
            text-align: center;
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            border: 1px solid #f0f0f0;
            transition: all 0.3s;
            flex-shrink: 0;
        }
        
        .calendar-item:hover {
            border-color: #1890ff;
            background-color: #f0f8ff;
        }
        
        .calendar-item.active {
            border-color: #1890ff;
            background-color: #e6f7ff;
            color: #1890ff;
            box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
        }
        
        .calendar-date {
            font-weight: 500;
            font-size: 12px;
            margin-bottom: 5px;
        }
        
        .calendar-thumbnail {
            width: 60px;
            height: 80px;
            object-fit: cover;
            border-radius: 4px;
            margin: 0 auto;
            display: block;
        }
        
        /* 按钮样式 */
        .action-btn {
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            transition: all 0.2s;
        }
        
        .back-btn {
            background-color: white;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .back-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
            background-color: #f0f8ff;
        }
        
        /* 加载状态 */
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 50px 0;
        }
        
        .loading-spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left: 4px solid #1890ff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .timeline-container {
                padding-left: 30px;
            }
            
            .node-marker {
                left: -40px;
                width: 16px;
                height: 16px;
            }
            
            .outfit-info {
                flex-direction: column;
            }
            
            .outfit-image-item {
                width: 90px;
                height: 90px;
            }
            
            .outfit-items-grid {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>穿搭日历详情</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="actions">
                <button id="backBtn" class="action-btn back-btn">返回列表</button>
            </div>
            
            <div id="loadingContainer" class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载数据...</p>
            </div>
            
            <div id="detailContainer" class="main-container" style="display: none;">
                <!-- 日历导航 -->
                <div class="calendar-nav-container">
                    <div class="calendar-nav-title">
                        <span>用户穿搭日历</span>
                        <span class="calendar-count" id="calendarCount">共 0 条记录</span>
                    </div>
                    <div class="calendar-nav" id="calendarNav">
                        <!-- 日历导航项将通过JavaScript动态填充 -->
                    </div>
                </div>
                
                <!-- 时间线容器 -->
                <div class="timeline-container">
                    <div class="timeline"></div>
                    
                    <!-- 所有穿搭节点将通过JavaScript动态填充 -->
                    <div id="allOutfits">
                        <!-- 穿搭节点将在JS中动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/outfit_calendar_detail.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为outfit_calendar
            Sidebar.init('outfit_calendar');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化穿搭日历详情
            OutfitCalendarDetail.init();
        });
    </script>
</body>
</html> 