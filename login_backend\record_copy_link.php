<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证小程序用户权限
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证用户token
$userData = $auth->verifyToken($token);
if (!$userData) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取数据库连接
$db = new Database();
$conn = $db->getConnection();

// 获取并验证POST数据
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '无效的JSON数据']);
    exit();
}

// 验证必要字段
if (!isset($input['outfit_id']) || !isset($input['item_id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少必要参数：outfit_id、item_id']);
    exit();
}

$outfitId = intval($input['outfit_id']);
$itemId = intval($input['item_id']);

try {
    // 验证推荐穿搭和商品是否存在
    $checkQuery = "SELECT ro.id FROM recommended_outfits ro 
                  JOIN recommended_outfit_items roi ON ro.id = roi.outfit_id 
                  WHERE ro.id = ? AND roi.id = ? AND ro.status = 1";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bindValue(1, $outfitId);
    $checkStmt->bindValue(2, $itemId);
    $checkStmt->execute();
    
    if (!$checkStmt->fetch()) {
        http_response_code(404);
        echo json_encode(['error' => true, 'msg' => '找不到指定的推荐穿搭或商品']);
        exit();
    }
    
    // 检查是否存在统计记录
    $checkStatsQuery = "SELECT id FROM recommended_outfit_stats WHERE outfit_id = ?";
    $checkStatsStmt = $conn->prepare($checkStatsQuery);
    $checkStatsStmt->bindValue(1, $outfitId);
    $checkStatsStmt->execute();
    $statsRecord = $checkStatsStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($statsRecord) {
        // 更新现有记录
        $updateQuery = "UPDATE recommended_outfit_stats 
                       SET copy_link_count = copy_link_count + 1 
                       WHERE outfit_id = ?";
        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bindValue(1, $outfitId);
        $updateStmt->execute();
    } else {
        // 创建新记录
        $insertQuery = "INSERT INTO recommended_outfit_stats 
                       (outfit_id, view_count, copy_link_count, created_at) 
                       VALUES (?, 0, 1, NOW())";
        $insertStmt = $conn->prepare($insertQuery);
        $insertStmt->bindValue(1, $outfitId);
        $insertStmt->execute();
    }
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'msg' => '复制链接记录成功'
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '数据库错误: ' . $e->getMessage()]);
} 