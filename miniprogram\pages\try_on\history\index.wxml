<view class="container">
  <view class="photos-grid" wx:if="{{!isLoading && histories.length > 0}}">
    <!-- 左列 -->
    <view class="column">
      <view class="photo-item" wx:for="{{leftItems}}" wx:key="id" bindtap="goToResultPage" data-id="{{item.id}}" data-url="{{item.result_image_url}}" data-clothes="{{item.clothes_ids}}">
        <image class="photo-image" src="{{item.result_image_url}}" mode="widthFix" lazy-load="true"></image>
      </view>
    </view>
    
    <!-- 右列 -->
    <view class="column">
      <view class="photo-item" wx:for="{{rightItems}}" wx:key="id" bindtap="goToResultPage" data-id="{{item.id}}" data-url="{{item.result_image_url}}" data-clothes="{{item.clothes_ids}}">
        <image class="photo-image" src="{{item.result_image_url}}" mode="widthFix" lazy-load="true"></image>
      </view>
    </view>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!isLoading && histories.length === 0}}">
    <text>还没有试穿历史记录</text>
  </view>
  
  <!-- 加载中 -->
  <view class="loading" wx:if="{{isLoading}}">
    <view class="loading-icon"></view>
  </view>
  
  <!-- 试穿历史详情弹框 -->
  <view class="photo-modal" wx:if="{{showModal}}" bindtap="hideHistoryModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-photo-container">
        <image src="{{currentHistory.result_image_url}}" mode="widthFix" class="modal-photo-image" lazy-load="true" show-menu-by-longpress="true" bindload="onImageLoad"></image>
        <view class="photo-type">试穿结果</view>
      </view>
      <!-- 衣物信息 -->
      <view class="clothes-info" wx:if="{{currentHistory.clothes_ids.length > 0}}">
        <text>使用衣物ID: {{currentHistory.clothes_ids}}</text>
      </view>
      <!-- 时间信息 -->
      <view class="time-info">
        <text>{{currentHistory.created_at}}</text>
      </view>
    </view>
  </view>
</view> 