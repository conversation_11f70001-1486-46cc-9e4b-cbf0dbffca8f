<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面容分析API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .image-preview {
            max-width: 300px;
            max-height: 300px;
            margin-top: 10px;
            border: 1px solid #ddd;
        }
        .result {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 15px;
            cursor: pointer;
            border: 1px solid transparent;
        }
        .tab.active {
            border: 1px solid #ddd;
            border-bottom-color: #fff;
            border-radius: 4px 4px 0 0;
            margin-bottom: -1px;
            background-color: #fff;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>面容分析API测试</h1>
        
        <div class="tabs">
            <div class="tab active" data-tab="analyze">面容分析</div>
            <div class="tab" data-tab="history">历史记录</div>
            <div class="tab" data-tab="result">查询结果</div>
        </div>
        
        <!-- 面容分析表单 -->
        <div class="tab-content active" id="analyze">
            <div class="card">
                <h2>上传面容照片进行分析</h2>
                <div class="form-group">
                    <label for="token">授权Token：</label>
                    <input type="text" id="token" placeholder="请输入授权Token">
                </div>
                <div class="form-group">
                    <label for="front-photo">正面照片：</label>
                    <input type="file" id="front-photo" accept="image/*">
                    <div id="front-preview-container">
                        <img id="front-preview" class="image-preview" style="display: none;">
                    </div>
                </div>
                <div class="form-group">
                    <label for="side-photo">侧面照片（可选）：</label>
                    <input type="file" id="side-photo" accept="image/*">
                    <div id="side-preview-container">
                        <img id="side-preview" class="image-preview" style="display: none;">
                    </div>
                </div>
                <div class="form-group">
                    <label for="preferred-style">风格偏好：</label>
                    <textarea id="preferred-style" rows="3" placeholder="请描述您喜欢的风格，如：自然、优雅、活力等"></textarea>
                </div>
                <button id="analyze-btn">提交分析</button>
            </div>
            
            <div class="card">
                <h2>分析结果</h2>
                <div id="analyze-result" class="result">未有分析结果</div>
            </div>
        </div>
        
        <!-- 历史记录 -->
        <div class="tab-content" id="history">
            <div class="card">
                <h2>历史分析记录</h2>
                <div class="form-group">
                    <label for="history-token">授权Token：</label>
                    <input type="text" id="history-token" placeholder="请输入授权Token">
                </div>
                <button id="history-btn">获取历史记录</button>
                <div id="history-result" class="result">未有历史记录</div>
            </div>
        </div>
        
        <!-- 查询结果 -->
        <div class="tab-content" id="result">
            <div class="card">
                <h2>查询分析结果</h2>
                <div class="form-group">
                    <label for="result-token">授权Token：</label>
                    <input type="text" id="result-token" placeholder="请输入授权Token">
                </div>
                <div class="form-group">
                    <label for="analysis-id">分析ID：</label>
                    <input type="text" id="analysis-id" placeholder="请输入分析ID">
                </div>
                <button id="result-btn">查询结果</button>
                <div id="result-result" class="result">未有查询结果</div>
            </div>
        </div>
    </div>

    <script>
        // API地址
        const apiUrl = 'face_analysis.php';
        
        // Tab切换
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有active类
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // 添加active类到当前tab
                this.classList.add('active');
                
                // 显示对应内容
                const tabName = this.getAttribute('data-tab');
                document.getElementById(tabName).classList.add('active');
            });
        });
        
        // 同步Token值
        document.getElementById('token').addEventListener('input', function() {
            document.getElementById('history-token').value = this.value;
            document.getElementById('result-token').value = this.value;
        });
        
        // 显示图片预览
        document.getElementById('front-photo').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('front-preview');
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });
        
        document.getElementById('side-photo').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('side-preview');
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });
        
        // 提交分析请求
        document.getElementById('analyze-btn').addEventListener('click', function() {
            const token = document.getElementById('token').value;
            if (!token) {
                alert('请输入授权Token');
                return;
            }
            
            const frontPhotoInput = document.getElementById('front-photo');
            if (!frontPhotoInput.files || frontPhotoInput.files.length === 0) {
                alert('请选择正面照片');
                return;
            }
            
            const preferredStyle = document.getElementById('preferred-style').value;
            
            // 读取正面照片
            const frontPhotoReader = new FileReader();
            frontPhotoReader.onload = function(e) {
                const frontPhotoBase64 = e.target.result;
                
                // 读取侧面照片（如果有）
                const sidePhotoInput = document.getElementById('side-photo');
                if (sidePhotoInput.files && sidePhotoInput.files.length > 0) {
                    const sidePhotoReader = new FileReader();
                    sidePhotoReader.onload = function(e) {
                        const sidePhotoBase64 = e.target.result;
                        submitAnalysis(token, frontPhotoBase64, sidePhotoBase64, preferredStyle);
                    };
                    sidePhotoReader.readAsDataURL(sidePhotoInput.files[0]);
                } else {
                    // 没有侧面照片
                    submitAnalysis(token, frontPhotoBase64, '', preferredStyle);
                }
            };
            frontPhotoReader.readAsDataURL(frontPhotoInput.files[0]);
        });
        
        // 获取历史记录
        document.getElementById('history-btn').addEventListener('click', function() {
            const token = document.getElementById('history-token').value;
            if (!token) {
                alert('请输入授权Token');
                return;
            }
            
            fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Authorization': token
                }
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('history-result').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                console.error('获取历史记录失败:', error);
                document.getElementById('history-result').textContent = '获取历史记录失败: ' + error.message;
            });
        });
        
        // 查询分析结果
        document.getElementById('result-btn').addEventListener('click', function() {
            const token = document.getElementById('result-token').value;
            if (!token) {
                alert('请输入授权Token');
                return;
            }
            
            const analysisId = document.getElementById('analysis-id').value;
            if (!analysisId) {
                alert('请输入分析ID');
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'get_result');
            formData.append('analysis_id', analysisId);
            
            fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': token
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('result-result').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                console.error('查询分析结果失败:', error);
                document.getElementById('result-result').textContent = '查询分析结果失败: ' + error.message;
            });
        });
        
        // 提交分析
        function submitAnalysis(token, frontPhotoBase64, sidePhotoBase64, preferredStyle) {
            document.getElementById('analyze-btn').disabled = true;
            document.getElementById('analyze-btn').textContent = '分析中...';
            document.getElementById('analyze-result').textContent = '正在分析，请稍候...';
            
            const formData = new FormData();
            formData.append('action', 'analyze');
            formData.append('front_photo', frontPhotoBase64);
            if (sidePhotoBase64) {
                formData.append('side_photo', sidePhotoBase64);
            }
            formData.append('preferred_style', preferredStyle);
            
            fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': token
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('analyze-result').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                console.error('分析请求失败:', error);
                document.getElementById('analyze-result').textContent = '分析请求失败: ' + error.message;
            })
            .finally(() => {
                document.getElementById('analyze-btn').disabled = false;
                document.getElementById('analyze-btn').textContent = '提交分析';
            });
        }
    </script>
</body>
</html> 