<?php
/**
 * TOP API: taobao.tbk.dg.material.optional request
 * 
 * <AUTHOR> create
 * @since 1.0, 2022.05.16
 */
class TbkDgMaterialOptionalRequest
{
    /**
     * 推广位id，mm_xxx_xxx_xxx中的第三位
     **/
    private $adzoneId;
    
    /**
     * 商品筛选-类目ID
     **/
    private $cat;
    
    /**
     * 设备号加密类型：MD5
     **/
    private $deviceEncrypt;
    
    /**
     * 设备号类型：IMEI，或者IDFA，或者UTDID
     **/
    private $deviceType;
    
    /**
     * 设备号加密后的值
     **/
    private $deviceValue;
    
    /**
     * 商品筛选-最高价格
     **/
    private $endPrice;
    
    /**
     * 商品筛选-是否有券，true表示有券，false或不设置表示不限
     **/
    private $hasCoupon;
    
    /**
     * 是否有店铺券，1表示有，0表示没有
     **/
    private $hasShopCoupon;
    
    /**
     * 商品筛选-是否包邮，true表示包邮，false或不设置表示不限
     **/
    private $isP4p;
    
    /**
     * 商品筛选-店铺dsr评分，筛选高于等于当前设置的店铺dsr评分的商品0-50000之间
     **/
    private $itemlocationCity;
    
    /**
     * 锁佣结束时间
     **/
    private $lockRateEndTime;
    
    /**
     * 锁佣开始时间
     **/
    private $lockRateStartTime;
    
    /**
     * 物料id，不传时默认取普通物料，可以通过查询物料来源获取，如：2836
     **/
    private $materialId;
    
    /**
     * 不传时默认物料第一页，若物料id为参与渠道ID，则该字段传递的为渠道专属物料的页数
     **/
    private $pageNo;
    
    /**
     * 页大小，默认20，1~100
     **/
    private $pageSize;
    
    /**
     * 平台类型：1-PC, 2-无线
     **/
    private $platform;
    
    /**
     * 商品筛选-查询词
     **/
    private $q;
    
    /**
     * 排序_des（降序），排序_asc（升序），销量（total_sales），淘客佣金比率（commission_rate）， 累计推广量（commission_num），总支出佣金（commission_volume），价格（price）
     **/
    private $sort;
    
    /**
     * 商品筛选-最低价格
     **/
    private $startPrice;
    
    /**
     * 商品筛选-是否天猫商品，true表示属于天猫商品，false或不设置表示不限
     **/
    private $isTmall;
    
    /**
     * 商品筛选-是否电商小镇，true表示商品属于电商小镇，false或不设置表示不限
     **/
    private $isEcommerce;
    
    /**
     * ip参数影响邮费获取，如果不传或者传入不准确，邮费无法精准提供
     **/
    private $ip;
    
    /**
     * 商品筛选-店铺dsr评分，筛选高于等于当前设置的店铺dsr评分的商品0-50000之间
     **/
    private $startDsr;
    
    /**
     * 会员运营id
     **/
    private $specialId;
    
    /**
     * 渠道关系ID，仅适用于渠道推广场景
     **/
    private $relationId;
    
    /**
     * 1-动态ID转链场景，2-消费者比价场景，默认为1
     **/
    private $useHeadDealCache;
    
    /**
     * 选品库投放id
     **/
    private $ucrowdId;
    
    /**
     * 选品库投放id列表，多个值用英文逗号隔开，同时筛选多个标签，同ucrowd_id筛选逻辑一致
     **/
    private $ucrowdIdList;
    
    /**
     * 物料来源，同materialId相同，但是可以不传，表示使用默认物料
     **/
    private $sourceType;
    
    /**
     * 商品筛选，是否加入商品库true或false
     **/
    private $isJhs;
    
    /**
     * 商品筛选-退款率是否低于行业均值，true表示大于等于，false或不设置表示不限
     **/
    private $isJhsLowRefund;
    
    /**
     * 商品筛选-好评率是否高于行业均值，true表示大于等于，false或不设置表示不限
     **/
    private $isJhsHighGoodrate;
    
    /**
     * 商品筛选-KA媒体淘客佣金率是否高于行业均值，true表示大于等于，false或不设置表示不限
     **/
    private $isKaHighCommission;
    
    /**
     * 物料权限验证
     **/
    private $jhs;
    
    /**
     * 淘宝客商品id，用于相似商品推荐
     **/
    private $itemId;
    
    /**
     * 是否精选好货，1-精选，0-非精选
     **/
    private $type;
    
    /**
     * 人群ID，仅适用于物料评估场景material_id=41377
     **/
    private $groupId;
    
    /**
     * 智能匹配-设备号加密类型：MD5；使用智能推荐请先签署协议https://pub.alimama.com/fourth/protocol/common.htm?key=hangye_laxin
     **/
    private $getTopnRate;
    
    /**
     * 是否获取前N件佣金信息，0否，1是，其他值否
     **/
    private $needPrepay;
    
    /**
     * 是否加入消费者保障，1为加入，0为否
     **/
    private $needFreeshipment;
    
    /**
     * 商品筛选-偏远地区包邮，1-是，0-否
     **/
    private $includeRfdRate;
    
    /**
     * 是否包含退款率，1-包含，0-不包含
     **/
    private $includeGoodRate;
    
    /**
     * 是否包含评价信息，1-包含，0-不包含
     **/
    private $includePayRate30;
    
    /**
     * 成交转化是否高于行业均值，1-高于，0-不是
     **/
    private $npxLevel;
    
    /**
     * 商家id，仅支持饿了么卡券商家ID，支持批量指定，多个商家ID以英文逗号分隔
     **/
    private $merIds;
    
    /**
     * 本地化卡券身份ID，仅在物料为本地化卡券物料时需要传入
     **/
    private $paimaiInfoId;
    
    /**
     * 排序_des（降序），排序_asc（升序），佣金比率（comm_rate）， 价格（price），卖家等级（shop_level），淘客评分（tk_rate），淘客佣金（tk_comm），销量（sale）
     **/
    private $outer;
    
    /**
     * 需返回的字段列表
     **/
    private $fields;
    
    private $apiParas = array();
    
    public function setAdzoneId($adzoneId)
    {
        $this->adzoneId = $adzoneId;
        $this->apiParas["adzone_id"] = $adzoneId;
    }

    public function getAdzoneId()
    {
        return $this->adzoneId;
    }

    public function setCat($cat)
    {
        $this->cat = $cat;
        $this->apiParas["cat"] = $cat;
    }

    public function getCat()
    {
        return $this->cat;
    }

    public function setDeviceEncrypt($deviceEncrypt)
    {
        $this->deviceEncrypt = $deviceEncrypt;
        $this->apiParas["device_encrypt"] = $deviceEncrypt;
    }

    public function getDeviceEncrypt()
    {
        return $this->deviceEncrypt;
    }

    public function setDeviceType($deviceType)
    {
        $this->deviceType = $deviceType;
        $this->apiParas["device_type"] = $deviceType;
    }

    public function getDeviceType()
    {
        return $this->deviceType;
    }

    public function setDeviceValue($deviceValue)
    {
        $this->deviceValue = $deviceValue;
        $this->apiParas["device_value"] = $deviceValue;
    }

    public function getDeviceValue()
    {
        return $this->deviceValue;
    }

    public function setEndPrice($endPrice)
    {
        $this->endPrice = $endPrice;
        $this->apiParas["end_price"] = $endPrice;
    }

    public function getEndPrice()
    {
        return $this->endPrice;
    }

    public function setHasCoupon($hasCoupon)
    {
        $this->hasCoupon = $hasCoupon;
        $this->apiParas["has_coupon"] = $hasCoupon;
    }

    public function getHasCoupon()
    {
        return $this->hasCoupon;
    }

    public function setHasShopCoupon($hasShopCoupon)
    {
        $this->hasShopCoupon = $hasShopCoupon;
        $this->apiParas["has_shop_coupon"] = $hasShopCoupon;
    }

    public function getHasShopCoupon()
    {
        return $this->hasShopCoupon;
    }

    public function setIsP4p($isP4p)
    {
        $this->isP4p = $isP4p;
        $this->apiParas["is_p4p"] = $isP4p;
    }

    public function getIsP4p()
    {
        return $this->isP4p;
    }

    public function setItemlocationCity($itemlocationCity)
    {
        $this->itemlocationCity = $itemlocationCity;
        $this->apiParas["itemlocation.city"] = $itemlocationCity;
    }

    public function getItemlocationCity()
    {
        return $this->itemlocationCity;
    }

    public function setLockRateEndTime($lockRateEndTime)
    {
        $this->lockRateEndTime = $lockRateEndTime;
        $this->apiParas["lock_rate_end_time"] = $lockRateEndTime;
    }

    public function getLockRateEndTime()
    {
        return $this->lockRateEndTime;
    }

    public function setLockRateStartTime($lockRateStartTime)
    {
        $this->lockRateStartTime = $lockRateStartTime;
        $this->apiParas["lock_rate_start_time"] = $lockRateStartTime;
    }

    public function getLockRateStartTime()
    {
        return $this->lockRateStartTime;
    }

    public function setMaterialId($materialId)
    {
        $this->materialId = $materialId;
        $this->apiParas["material_id"] = $materialId;
    }

    public function getMaterialId()
    {
        return $this->materialId;
    }

    public function setPageNo($pageNo)
    {
        $this->pageNo = $pageNo;
        $this->apiParas["page_no"] = $pageNo;
    }

    public function getPageNo()
    {
        return $this->pageNo;
    }

    public function setPageSize($pageSize)
    {
        $this->pageSize = $pageSize;
        $this->apiParas["page_size"] = $pageSize;
    }

    public function getPageSize()
    {
        return $this->pageSize;
    }

    public function setPlatform($platform)
    {
        $this->platform = $platform;
        $this->apiParas["platform"] = $platform;
    }

    public function getPlatform()
    {
        return $this->platform;
    }

    public function setQ($q)
    {
        $this->q = $q;
        $this->apiParas["q"] = $q;
    }

    public function getQ()
    {
        return $this->q;
    }

    public function setSort($sort)
    {
        $this->sort = $sort;
        $this->apiParas["sort"] = $sort;
    }

    public function getSort()
    {
        return $this->sort;
    }

    public function setStartPrice($startPrice)
    {
        $this->startPrice = $startPrice;
        $this->apiParas["start_price"] = $startPrice;
    }

    public function getStartPrice()
    {
        return $this->startPrice;
    }

    public function setFields($fields)
    {
        $this->fields = $fields;
        $this->apiParas["fields"] = $fields;
    }

    public function getFields()
    {
        return $this->fields;
    }

    public function getApiMethodName()
    {
        return "taobao.tbk.dg.material.optional";
    }

    public function getApiParas()
    {
        return $this->apiParas;
    }
    
    /**
     * 检查参数合法性
     */
    public function check()
    {
        // 必填参数检查
        RequestCheckUtil::checkNotNull($this->adzoneId, "adzone_id");
        
        // 其他可选参数的条件检查
        if (!is_null($this->pageSize)) {
            RequestCheckUtil::checkMaxValue($this->pageSize, 100, "page_size");
            RequestCheckUtil::checkMinValue($this->pageSize, 1, "page_size");
        }
    }
} 