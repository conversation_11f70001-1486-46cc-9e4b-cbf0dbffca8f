<?php
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

try {
    $auth = new Auth();
    $userId = null;
    
    // 检查是否有token，有则验证
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? null;
    
    if ($authHeader) {
        $token = $authHeader;
        
        // 处理Bearer前缀
        if (strpos($authHeader, 'Bearer ') === 0) {
            $token = substr($authHeader, 7);
        }
        
        // 验证token
        $tokenData = $auth->verifyToken($token);
        if ($tokenData) {
            $userId = $tokenData['user_id'] ?? $tokenData['sub'];
        }
    }
    
    $db = new Database();
    $conn = $db->getConnection();

    // 新增：圈子数据相关参数（向后兼容）
    $includeCircleData = isset($_GET['include_circle_data']) ? $_GET['include_circle_data'] === 'true' : false;
    $dataSource = isset($_GET['data_source']) ? $_GET['data_source'] : 'personal'; // personal, shared, all

    // 添加详细的调试日志
    error_log("分类API请求 - 用户ID: $userId, 包含圈子数据: " . ($includeCircleData ? 'true' : 'false') . ", 数据源: $dataSource");

    // 先检查用户所在的圈子
    $circleCheckSql = "SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'";
    $circleCheckStmt = $conn->prepare($circleCheckSql);
    $circleCheckStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $circleCheckStmt->execute();
    $userCircles = $circleCheckStmt->fetchAll(PDO::FETCH_COLUMN);
    error_log("用户所在圈子: " . json_encode($userCircles));

    // 如果用户已登录，检查是否需要为用户创建系统分类的副本
    if ($userId) {
        // 获取全局系统分类
        $systemCategoriesSql = "SELECT name, code, sort_order FROM clothing_categories WHERE user_id IS NULL AND is_system = 1";
        $systemStmt = $conn->prepare($systemCategoriesSql);
        $systemStmt->execute();
        $systemCategories = $systemStmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 获取用户已有的系统分类副本代码
        $userCategoriesSql = "SELECT code FROM clothing_categories WHERE user_id = :user_id AND is_system = 1";
        $userCatStmt = $conn->prepare($userCategoriesSql);
        $userCatStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $userCatStmt->execute();
        $userCategories = $userCatStmt->fetchAll(PDO::FETCH_COLUMN);
        
        // 检查用户是否首次登录（没有任何系统分类）
        $isFirstLogin = count($userCategories) === 0;
        
        // 创建一个记录删除过的系统分类代码的表，避免重复创建
        // 尝试获取用户已删除的系统分类记录
        $deletedCategoriesSql = "CREATE TABLE IF NOT EXISTS deleted_system_categories (
            id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            code VARCHAR(50) NOT NULL,
            deleted_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_code (user_id, code)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $conn->exec($deletedCategoriesSql);
        
        // 获取用户已删除的系统分类
        $deletedSql = "SELECT code FROM deleted_system_categories WHERE user_id = :user_id";
        $deletedStmt = $conn->prepare($deletedSql);
        $deletedStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $deletedStmt->execute();
        $deletedCategories = $deletedStmt->fetchAll(PDO::FETCH_COLUMN);
        
        // 为用户创建缺少的系统分类副本
        if (!empty($systemCategories)) {
            $conn->beginTransaction();
            try {
                foreach ($systemCategories as $sysCategory) {
                    $categoryCode = $sysCategory['code'];
                    
                    // 只有满足以下条件才创建:
                    // 1. 用户没有这个代码的分类
                    // 2. 这个分类代码不在已删除记录中 或者 这是用户首次登录
                    if (!in_array($categoryCode, $userCategories) && 
                        ($isFirstLogin || !in_array($categoryCode, $deletedCategories))) {
                        
                        $insertSql = "INSERT INTO clothing_categories 
                                    (user_id, name, code, is_system, sort_order, created_at)
                                    VALUES (:user_id, :name, :code, 1, :sort_order, NOW())";
                        $insertStmt = $conn->prepare($insertSql);
                        $insertStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
                        $insertStmt->bindParam(':name', $sysCategory['name'], PDO::PARAM_STR);
                        $insertStmt->bindParam(':code', $categoryCode, PDO::PARAM_STR);
                        $insertStmt->bindParam(':sort_order', $sysCategory['sort_order'], PDO::PARAM_INT);
                        $insertStmt->execute();
                    }
                }
                $conn->commit();
            } catch (Exception $e) {
                $conn->rollBack();
                throw $e;
            }
        }
    }
    
    // 构建查询SQL - 获取系统分类和用户自定义分类
    if ($userId) {
        if ($includeCircleData && $dataSource !== 'personal') {
            // 新功能：包含圈子数据的查询
            if ($dataSource === 'shared') {
                // 查询共享数据源的分类：
                // 1. 当前用户的系统分类（用于显示系统分类下的所有用户衣物）
                // 2. 其他用户的自定义分类（排除自己的自定义分类）

                // 使用已经查询过的用户圈子数据
                if (empty($userCircles)) {
                    // 如果用户不在任何圈子中，只返回系统分类
                    $sql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                                   u.nickname as creator_nickname, 'system' as data_source
                            FROM clothing_categories c
                            LEFT JOIN users u ON c.user_id = u.id
                            WHERE c.is_system = 1 AND c.user_id = :user_id
                            ORDER BY c.sort_order ASC, c.created_at ASC";
                } else {
                    $circleIds = implode(',', $userCircles);
                    $sql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                                   u.nickname as creator_nickname,
                                   CASE
                                       WHEN c.is_system = 1 THEN 'system'
                                       WHEN c.circle_id IS NULL THEN 'personal'
                                       ELSE 'shared'
                                   END as data_source
                            FROM clothing_categories c
                            LEFT JOIN users u ON c.user_id = u.id
                            WHERE (
                                -- 当前用户的系统分类（用于显示系统分类下的共享衣物）
                                (c.is_system = 1 AND c.user_id = :user_id) OR
                                -- 其他用户的自定义分类（已同步到圈子的）
                                (c.is_system = 0 AND c.user_id != :user_id AND c.circle_id IN ($circleIds))
                            )
                            ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC";
                }
            } else { // $dataSource === 'all'
                // 查询个人分类 + 圈子共享分类
                // 修复：使用与衣物API相同的查询逻辑，但保留系统分类的特殊处理
                $sql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                               u.nickname as creator_nickname,
                               CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                        FROM clothing_categories c
                        LEFT JOIN users u ON c.user_id = u.id
                        WHERE (
                            -- 当前用户的系统分类（每个用户都有自己的系统分类副本）
                            (c.is_system = 1 AND c.user_id = :user_id) OR
                            -- 所有自定义分类：个人的 + 圈子共享的
                            (c.is_system = 0 AND ((c.user_id = :user_id AND c.circle_id IS NULL) OR
                             (c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'))))
                        )
                        ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC";
            }
        } else {
            // 原有逻辑：已登录用户获取个人分类
            $sql = "SELECT id, user_id, name, code, is_system, sort_order, created_at
                    FROM clothing_categories
                    WHERE (is_system = 1 AND user_id = :user_id) OR (is_system = 0 AND user_id = :user_id)
                    ORDER BY sort_order ASC, is_system DESC, created_at ASC";
        }
        // 添加调试日志
        error_log("分类查询 - 用户ID: $userId, 数据源: $dataSource, 包含圈子数据: " . ($includeCircleData ? 'true' : 'false'));
        error_log("分类SQL: $sql");

        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    } else {
        // 未登录用户：只获取默认系统分类
        $sql = "SELECT id, user_id, name, code, is_system, sort_order, created_at
                FROM clothing_categories
                WHERE is_system = 1 AND user_id IS NULL
                ORDER BY sort_order ASC, created_at ASC";
        $stmt = $conn->prepare($sql);
    }
    
    $stmt->execute();

    // 添加查询结果调试信息
    $rowCount = $stmt->rowCount();
    error_log("分类查询结果数量: $rowCount");

    $categories = [];

    // 如果是圈子数据查询，检查是否有缺失的分类定义
    if ($includeCircleData && $dataSource !== 'personal' && !empty($userCircles)) {
        $missingCategories = checkForMissingCategories($conn, $userId, $userCircles);
        if (!empty($missingCategories)) {
            error_log("发现缺失的分类定义: " . json_encode($missingCategories));
        }
    }
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $category = [
            'id' => (int)$row['id'],
            'user_id' => $row['user_id'] ? (int)$row['user_id'] : null,
            'name' => $row['name'],
            'code' => $row['code'],
            'is_system' => (bool)$row['is_system'],
            'sort_order' => (int)$row['sort_order'],
            'created_at' => $row['created_at'],
            'editable' => true // 所有分类都可编辑，系统分类只能修改排序
        ];

        // 添加数据源相关字段
        if ($includeCircleData && isset($row['data_source'])) {
            $category['data_source'] = $row['data_source'];
            $category['creator_nickname'] = $row['creator_nickname'];
        } else {
            // 向后兼容：为原有API添加默认值
            $category['data_source'] = 'personal';
            $category['creator_nickname'] = null;
        }

        // 调试每个分类的数据源
        error_log("分类: {$category['name']} (ID: {$category['id']}) - 数据源: {$category['data_source']}, circle_id: " . ($row['circle_id'] ?? 'NULL'));

        $categories[] = $category;
    }

    // 添加调试日志
    error_log("分类查询结果 - 总数: " . count($categories) . ", 数据源: $dataSource");
    foreach ($categories as $cat) {
        error_log("分类: " . $cat['name'] . " (code: " . $cat['code'] . ", data_source: " . $cat['data_source'] . ")");
    }

    echo json_encode([
        'error' => false,
        'msg' => '获取分类成功',
        'data' => $categories,
        'meta' => [
            'include_circle_data' => $includeCircleData,
            'data_source' => $dataSource,
            'total_count' => count($categories)
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '服务器错误: ' . $e->getMessage()
    ]);
}

/**
 * 检查圈子中是否有缺失的分类定义
 * 返回在衣物中使用但在分类表中不存在的分类代码
 */
function checkForMissingCategories($conn, $userId, $userCircles) {
    $circleIds = implode(',', $userCircles);

    // 获取圈子中衣物使用的所有分类
    $stmt = $conn->prepare("
        SELECT DISTINCT category
        FROM clothes
        WHERE circle_id IN ($circleIds) AND category IS NOT NULL AND category != ''
    ");
    $stmt->execute();
    $usedCategories = $stmt->fetchAll(PDO::FETCH_COLUMN);

    if (empty($usedCategories)) {
        return [];
    }

    // 获取当前用户可见的所有分类代码
    $stmt = $conn->prepare("
        SELECT DISTINCT code
        FROM clothing_categories
        WHERE (
            (is_system = 1 AND user_id = :user_id) OR
            (is_system = 0 AND ((user_id = :user_id AND circle_id IS NULL) OR
             (circle_id IS NOT NULL AND circle_id IN ($circleIds))))
        )
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $availableCategories = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // 找出缺失的分类
    $missingCategories = array_diff($usedCategories, $availableCategories);

    return array_values($missingCategories);
}
?>