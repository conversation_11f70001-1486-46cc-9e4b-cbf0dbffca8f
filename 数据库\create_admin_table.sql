-- 创建管理员表
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(50) NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NULL,
    last_login DATETIME NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建默认管理员账号 (密码: 4711329.Jiang)
INSERT INTO admin_users (username, password, real_name, created_at) 
VALUES ('shaw', '$2y$10$9QVFB0U1r5mLK4Ow0QCzguLYHGK0WARwBBEyQKiOTP9aSMG6hEP8u', '管理员', NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW(); 