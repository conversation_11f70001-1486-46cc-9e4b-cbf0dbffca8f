<?php
// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method Not Allowed']);
    exit;
}

// 设置Gemini API密钥和备用密钥
$primaryApiKey = 'AIzaSyCkt3alFLjYSjG1nkYeM3UuXHktr3NMPKQ';
$backupApiKey = 'AIzaSyBtcfvTk8jNVB1I7kLzhKQQKChtbw-Rqmg';
$model = 'gemini-1.5-flash';

// 日志记录函数
function writeLog($message, $data = null) {
    // 确保日志目录存在
    $logDir = __DIR__ . '/logs';
    if (!file_exists($logDir)) {
        mkdir($logDir, 0777, true);
    }
    
    // 创建日志文件名（按日期）
    $logFile = $logDir . '/gemini_outfit_api_' . date('Y-m-d') . '.log';
    
    // 格式化日志消息
    $logMessage = '[' . date('Y-m-d H:i:s') . '] ' . $message;
    if ($data !== null) {
        $logMessage .= "\nData: " . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    $logMessage .= "\n--------------------------------------------------\n";
    
    // 写入日志
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// API密钥使用情况日志记录函数
function writeApiKeyUsageLog($apiKey, $isSuccess, $errorMessage = null) {
    // 确保日志目录存在
    $logDir = __DIR__ . '/logs';
    if (!file_exists($logDir)) {
        mkdir($logDir, 0777, true);
    }
    
    // 创建API密钥使用日志文件（按日期）
    $logFile = $logDir . '/api_key_usage_' . date('Y-m-d') . '.log';
    
    // 识别使用的是哪个API密钥
    global $primaryApiKey, $backupApiKey;
    $keyType = ($apiKey === $primaryApiKey) ? 'PRIMARY' : 'BACKUP';
    
    // 格式化日志消息
    $status = $isSuccess ? 'SUCCESS' : 'FAILED';
    $logMessage = '[' . date('Y-m-d H:i:s') . '] ' . $keyType . '_KEY_USED: ' . substr($apiKey, 0, 8) . '...' . ' - Status: ' . $status;
    
    if (!$isSuccess && $errorMessage) {
        $logMessage .= ' - Error: ' . $errorMessage;
    }
    
    $logMessage .= "\n";
    
    // 写入日志
    file_put_contents($logFile, $logMessage, FILE_APPEND);
    
    // 更新每日API密钥使用计数
    updateApiKeyCounter($keyType);
}

// 更新API密钥使用计数
function updateApiKeyCounter($keyType) {
    $counterFile = __DIR__ . '/logs/api_key_counter_' . date('Y-m-d') . '.json';
    
    // 初始化或读取当前计数
    if (file_exists($counterFile)) {
        $counterData = json_decode(file_get_contents($counterFile), true);
    } else {
        $counterData = [
            'PRIMARY' => 0,
            'BACKUP' => 0
        ];
    }
    
    // 更新计数
    $counterData[$keyType]++;
    
    // 保存更新后的计数
    file_put_contents($counterFile, json_encode($counterData));
}

// 记录请求开始
writeLog('接收穿搭推荐请求', $_POST);

// 验证和获取必要参数
if (!isset($_POST['clothes_data']) || empty($_POST['clothes_data'])) {
    writeLog('缺少衣物数据');
    http_response_code(400);
    echo json_encode(['error' => 'Missing clothes data']);
    exit;
}

// 解析衣物数据
$clothesData = json_decode($_POST['clothes_data'], true);
if (json_last_error() !== JSON_ERROR_NONE) {
    writeLog('无法解析衣物数据JSON', ['error' => json_last_error_msg()]);
    http_response_code(400);
    echo json_encode(['error' => 'Invalid clothes data JSON']);
    exit;
}

// 获取天气和季节信息（如果有）
$weather = isset($_POST['weather']) ? json_decode($_POST['weather'], true) : null;
$season = isset($_POST['season']) ? $_POST['season'] : '';

writeLog('解析的请求数据', [
    'clothes' => $clothesData,
    'weather' => $weather,
    'season' => $season
]);

// 构建穿搭推荐提示词
$prompt = "作为一名专业的穿搭顾问，请根据以下服装信息";

// 添加天气信息（如果有）
if ($weather) {
    $prompt .= "和天气情况（" . 
        (isset($weather['text']) ? "天气: " . $weather['text'] : "") . 
        (isset($weather['temp']) ? ", 温度: " . $weather['temp'] . "°C" : "") . 
        (isset($weather['humidity']) ? ", 湿度: " . $weather['humidity'] . "%" : "") .
        (isset($weather['windDir']) ? ", 风向: " . $weather['windDir'] : "") .
        (isset($weather['windScale']) ? ", 风力: " . $weather['windScale'] . "级" : "") .
        ")";
}

// 添加季节信息（如果有）
if (!empty($season)) {
    $prompt .= "，季节为" . $season;
}

$prompt .= "，推荐一套合适的穿搭。\n\n可用的服装列表:\n";

// 添加衣物类别
$categoryNames = [
    'tops' => '上衣',
    'pants' => '裤子',
    'skirts' => '裙子',
    'outerwears' => '外套',
    'shoes' => '鞋子',
    'accessories' => '配饰',
    'bags' => '包包'
];

foreach ($categoryNames as $category => $categoryName) {
    if (isset($clothesData[$category]) && !empty($clothesData[$category])) {
        $prompt .= "\n{$categoryName}:\n";
        foreach ($clothesData[$category] as $item) {
            $itemName = isset($item['name']) ? $item['name'] : '未命名衣物';
            $itemId = isset($item['id']) ? $item['id'] : 'unknown';
            $itemTags = isset($item['tags']) && !empty($item['tags']) ? $item['tags'] : '';
            $itemDescription = isset($item['description']) && !empty($item['description']) ? $item['description'] : '';
            
            // 提取颜色信息
            $colorInfo = '';
            if (!empty($itemDescription)) {
                // 检查$itemDescription的类型
                $descData = null;
                if (is_string($itemDescription)) {
                    // 如果是字符串，尝试解析JSON
                    $descData = json_decode($itemDescription, true);
                } else if (is_array($itemDescription)) {
                    // 如果已经是数组，直接使用
                    $descData = $itemDescription;
                }
                
                // 从描述数据中提取颜色信息
                if (is_array($descData)) {
                    // 检查各种可能的颜色字段名
                    if (isset($descData['color'])) {
                        $colorInfo = $descData['color'];
                    } elseif (isset($descData['颜色'])) {
                        $colorInfo = $descData['颜色'];
                    }
                }
            }
            
            $prompt .= "- {$itemName} (ID: {$itemId}";
            
            // 添加标签信息
            if (!empty($itemTags)) {
                $prompt .= ", 标签: {$itemTags}";
            }
            
            // 添加颜色信息
            if (!empty($colorInfo)) {
                $prompt .= ", 颜色: {$colorInfo}";
            }
            
            $prompt .= ")\n";
        }
    }
}

// 添加连衣裙说明
$prompt .= "\n请注意：连衣裙(全身裙)不需要搭配上衣，而半身裙需要搭配上衣。";

// 指定返回格式
$prompt .= "\n请以JSON格式返回推荐结果，应包含所选的衣物ID和推荐理由。格式如下：
{
  \"outfit\": {
    \"top\": \"ID号码\",  // 如果选择连衣裙则不需要上衣
    \"bottom\": \"ID号码\",
    \"shoes\": \"ID号码\",
    \"outerwear\": \"ID号码\",  // 可选
    \"accessories\": \"ID号码\",  // 可选
    \"bag\": \"ID号码\"  // 可选
  },
  \"reasons\": {
    \"top\": \"推荐理由\",
    \"bottom\": \"推荐理由\",
    \"shoes\": \"推荐理由\",
    \"outerwear\": \"推荐理由\",
    \"accessories\": \"推荐理由\",
    \"bag\": \"推荐理由\"
  },
  \"outfit_summary\": \"整体穿搭的简短总结，包括风格特点和适合场合等\"
}";

writeLog('生成的穿搭推荐提示词', ['prompt' => $prompt]);

// 封装API请求函数
function callGeminiApi($apiKey, $model, $prompt) {
    // 构建Gemini API URL
    $url = "https://generativelanguage.googleapis.com/v1beta/models/{$model}:generateContent?key={$apiKey}";
    
    // 构建Gemini API请求数据
    $geminiRequestData = [
        'contents' => [
            [
                'parts' => [
                    [
                        'text' => $prompt
                    ]
                ]
            ]
        ],
        // 定义生成配置
        'generationConfig' => [
            'temperature' => 0.4,
            'topP' => 0.9,
            'topK' => 40,
            'maxOutputTokens' => 1024
        ]
    ];
    
    // 初始化cURL会话
    $ch = curl_init($url);
    
    // 设置cURL选项
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($geminiRequestData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 30秒超时
    
    writeLog('发送请求到Gemini API', [
        'url' => $url,
        'request_data' => json_encode($geminiRequestData)
    ]);
    
    // 执行cURL请求
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    
    // 关闭cURL会话
    curl_close($ch);
    
    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'curlError' => $curlError
    ];
}

// 首先尝试使用主API密钥
$apiResult = callGeminiApi($primaryApiKey, $model, $prompt);
$responseData = null;
$generatedText = '';
$usedApiKey = $primaryApiKey;

// 检查主API密钥结果
if ($apiResult['curlError'] || $apiResult['httpCode'] !== 200) {
    // 记录主API密钥失败
    $errorMessage = $apiResult['curlError'] ?: "HTTP错误: " . $apiResult['httpCode'];
    writeLog('主API密钥请求失败，尝试备用API密钥', [
        'error' => $errorMessage,
        'response' => $apiResult['response']
    ]);
    writeApiKeyUsageLog($primaryApiKey, false, $errorMessage);
    
    // 尝试使用备用API密钥
    $apiResult = callGeminiApi($backupApiKey, $model, $prompt);
    $usedApiKey = $backupApiKey;
    
    // 仍然失败
    if ($apiResult['curlError']) {
        writeLog('备用API密钥cURL错误', ['error' => $apiResult['curlError']]);
        writeApiKeyUsageLog($backupApiKey, false, $apiResult['curlError']);
        http_response_code(500);
        echo json_encode(['error' => 'cURL Error: ' . $apiResult['curlError']]);
        exit;
    }
    
    if ($apiResult['httpCode'] !== 200) {
        writeLog('备用API密钥返回错误', [
            'http_code' => $apiResult['httpCode'],
            'response' => $apiResult['response']
        ]);
        writeApiKeyUsageLog($backupApiKey, false, "HTTP错误: " . $apiResult['httpCode']);
        http_response_code($apiResult['httpCode']);
        echo $apiResult['response']; // 直接返回Gemini API的错误响应
        exit;
    }
    
    // 备用API密钥调用成功
    writeApiKeyUsageLog($backupApiKey, true);
} else {
    // 主API密钥调用成功
    writeApiKeyUsageLog($primaryApiKey, true);
}

// 解析JSON响应
$responseData = json_decode($apiResult['response'], true);
writeLog('Gemini API响应', ['response' => $responseData]);

// 提取生成的文本
if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
    $generatedText = $responseData['candidates'][0]['content']['parts'][0]['text'];
    writeLog('提取的生成文本', ['text' => $generatedText]);
} else {
    writeLog('无法从Gemini API响应中提取文本');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to extract text from API response']);
    exit;
}

// 尝试提取JSON对象
$outfitRecommendation = null;
if (preg_match('/\{.*\}/s', $generatedText, $matches)) {
    $jsonStr = $matches[0];
    $outfitRecommendation = json_decode($jsonStr, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        writeLog('无法解析穿搭推荐JSON', [
            'error' => json_last_error_msg(),
            'text' => $generatedText
        ]);
    } else {
        writeLog('成功解析穿搭推荐JSON', ['recommendation' => $outfitRecommendation]);
    }
} else {
    writeLog('在生成文本中未找到JSON对象', ['text' => $generatedText]);
}

// 准备最终响应
$finalResponse = [
    'success' => $outfitRecommendation !== null,
    'data' => $outfitRecommendation ?: $generatedText,
    'raw_response' => $responseData,
    'api_key_used' => substr($usedApiKey, 0, 8) . '...' // 只返回密钥前8位，保持安全
];

writeLog('发送最终响应', $finalResponse);
echo json_encode($finalResponse);
?> 