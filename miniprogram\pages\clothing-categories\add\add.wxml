<!--pages/clothing-categories/add/add.wxml-->
<view class="container">
  <!-- 表单 -->
  <view class="form-container">
    <!-- 名称输入 -->
    <view class="form-group">
      <view class="form-label">分类名称 <text class="required">*</text></view>
      <input class="form-input" type="text" placeholder="给分类起个名字" value="{{name}}" bindinput="onNameInput" maxlength="30" />
      <view class="character-count">{{name.length}}/30</view>
    </view>
    
    <!-- 排序输入 -->
    <view class="form-group">
      <view class="form-label">排序序号</view>
      <input class="form-input" type="number" placeholder="数字越小越靠前（可选）" value="{{sortOrder}}" bindinput="onSortOrderInput" />
      <view class="form-tip">数字越小排序越靠前，系统分类排序为1-7</view>
    </view>
  </view>
  
  <!-- 提交按钮 -->
  <view class="button-container">
    <button class="cancel-btn" bindtap="onCancel">取消</button>
    <button class="submit-btn" bindtap="onSubmit" disabled="{{!name}}">创建</button>
  </view>
</view> 