/* 页面容器 */
page {
  background-color: #f9f9f9;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 滚动区域 */
.page-scroll {
  flex: 1;
  height: 100%;
}

.container {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 230rpx; /* 增加底部间距，避免被底部操作栏遮挡 */
}

/* 页面头部 */
.header {
  padding: 30rpx 20rpx;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 照片网格 */
.photos-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.photo-item {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

/* 图片容器 */
.photo-container {
  position: relative;
  width: 100%;
  height: 400rpx;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

/* 图片样式 */
.photo-image {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 确保图片完整显示 */
  background-color: #f5f5f5;
}

/* 图片加载状态 */
.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(245, 245, 245, 0.7);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #ddd;
  border-top: 4rpx solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 图片加载失败状态 */
.image-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f8f8;
  color: #999;
  font-size: 26rpx;
}

.photo-info {
  padding: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.photo-type {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.photo-date {
  font-size: 22rpx;
  color: #999;
}

.select-mark {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background-color: #000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.check-icon-container {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
}

.photo-item.selected {
  border: 4rpx solid #000;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

/* 添加照片按钮 */
.add-photo {
  height: 460rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border: 2rpx dashed #ccc;
}

.add-icon {
  font-size: 60rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.add-text {
  font-size: 26rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 150rpx;
  height: 150rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.upload-btn {
  padding: 20rpx 60rpx;
  background-color: #000;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
}

/* 加载状态 */
.loading {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部按钮 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  padding: 10px 15px;
  padding-bottom: calc(env(safe-area-inset-bottom) + 10px);
  z-index: 100;
  /* 确保底部边框连接到底部 */
  border-top: 1rpx solid #eee;
}

.bottom-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.proceed-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  background-color: #ccc;
  color: #666;
}

.proceed-btn.active {
  background-color: #000;
  color: #fff;
}

.nav-back-btn {
  background-color: transparent;
  border: 1px solid #000000;
  color: #000000;
  font-size: 13px;
  padding: 0 15px;
  border-radius: 22px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  margin-right: 12px;
}

.next-icon {
  margin-right: 8px;
} 