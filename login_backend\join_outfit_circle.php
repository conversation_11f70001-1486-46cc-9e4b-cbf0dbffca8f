<?php
// 加入穿搭圈子API
// 模块1：圈子基础管理模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

// 验证必需参数
if (!isset($input['invitation_code']) || empty(trim($input['invitation_code']))) {
    echo json_encode([
        'status' => 'error',
        'message' => '邀请码不能为空'
    ]);
    exit;
}

$invitationCode = trim(strtoupper($input['invitation_code']));

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查用户是否已经在其他圈子中
    $checkUserSql = "SELECT c.id, c.name, cm.role 
                     FROM circle_members cm 
                     JOIN outfit_circles c ON cm.circle_id = c.id 
                     WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
    $checkUserStmt = $conn->prepare($checkUserSql);
    $checkUserStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkUserStmt->execute();
    
    $existingCircle = $checkUserStmt->fetch(PDO::FETCH_ASSOC);
    if ($existingCircle) {
        echo json_encode([
            'status' => 'error',
            'message' => '您已经在圈子"' . $existingCircle['name'] . '"中，请先退出后再加入其他圈子'
        ]);
        exit;
    }
    
    // 查找圈子
    $findCircleSql = "SELECT id, name, description, creator_id, member_count 
                      FROM outfit_circles 
                      WHERE invitation_code = :invitation_code AND status = 'active'";
    $findCircleStmt = $conn->prepare($findCircleSql);
    $findCircleStmt->bindParam(':invitation_code', $invitationCode);
    $findCircleStmt->execute();
    
    $circle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);
    if (!$circle) {
        echo json_encode([
            'status' => 'error',
            'message' => '邀请码无效或圈子不存在'
        ]);
        exit;
    }
    
    // 检查用户是否已经是该圈子的成员
    $checkMemberSql = "SELECT id, status FROM circle_members 
                       WHERE circle_id = :circle_id AND user_id = :user_id";
    $checkMemberStmt = $conn->prepare($checkMemberSql);
    $checkMemberStmt->bindParam(':circle_id', $circle['id'], PDO::PARAM_INT);
    $checkMemberStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkMemberStmt->execute();
    
    $existingMember = $checkMemberStmt->fetch(PDO::FETCH_ASSOC);
    if ($existingMember) {
        if ($existingMember['status'] === 'active') {
            echo json_encode([
                'status' => 'error',
                'message' => '您已经是该圈子的成员'
            ]);
            exit;
        } else {
            // 如果之前被移除，重新激活成员身份
            $reactivateSql = "UPDATE circle_members 
                              SET status = 'active', joined_at = NOW(), removed_at = NULL, removed_by = NULL 
                              WHERE id = :member_id";
            $reactivateStmt = $conn->prepare($reactivateSql);
            $reactivateStmt->bindParam(':member_id', $existingMember['id'], PDO::PARAM_INT);
            $reactivateStmt->execute();
        }
    } else {
        // 添加新成员
        $addMemberSql = "INSERT INTO circle_members (circle_id, user_id, role) 
                         VALUES (:circle_id, :user_id, 'member')";
        $addMemberStmt = $conn->prepare($addMemberSql);
        $addMemberStmt->bindParam(':circle_id', $circle['id'], PDO::PARAM_INT);
        $addMemberStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $addMemberStmt->execute();
    }
    
    // 更新圈子成员数量
    $updateCountSql = "UPDATE outfit_circles 
                       SET member_count = (
                           SELECT COUNT(*) FROM circle_members 
                           WHERE circle_id = :circle_id AND status = 'active'
                       ) 
                       WHERE id = :circle_id";
    $updateCountStmt = $conn->prepare($updateCountSql);
    $updateCountStmt->bindParam(':circle_id', $circle['id'], PDO::PARAM_INT);
    $updateCountStmt->execute();
    
    // 获取创建者信息
    $creatorSql = "SELECT nickname, avatar_url FROM users WHERE id = :creator_id";
    $creatorStmt = $conn->prepare($creatorSql);
    $creatorStmt->bindParam(':creator_id', $circle['creator_id'], PDO::PARAM_INT);
    $creatorStmt->execute();
    $creator = $creatorStmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'status' => 'success',
        'message' => '成功加入圈子',
        'data' => [
            'circle_id' => $circle['id'],
            'name' => $circle['name'],
            'description' => $circle['description'],
            'member_count' => $circle['member_count'] + 1,
            'creator' => [
                'nickname' => $creator['nickname'] ?? '未知用户',
                'avatar_url' => $creator['avatar_url']
            ]
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '加入圈子失败：' . $e->getMessage()
    ]);
}
?>
