.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 30px 20px;
  box-sizing: border-box;
  background-color: #fff;
  justify-content: space-between;
}

/* 图标样式 */
.icon-tshirt::before {
  content: '\f553';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  font-size: 80rpx;
  color: #999;
}

.icon-wechat::before {
  content: '\f1d7';
  font-family: "Font Awesome 5 Brands";
  font-weight: 400;
  margin-right: 6px;
}

.icon-check::before {
  content: '\f00c';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  font-size: 10px;
  color: white;
}

.icon-user::before {
  content: '\f007';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  color: #333;
}

.icon-mobile::before {
  content: '\f10b';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  color: #333;
}

/* Logo部分 */
.logo-section {
  text-align: center;
  margin-bottom: 40px;
}

.logo-img {
  width: 120px;
  height: 120px;
  margin: 0 auto;
  background-color: #ffffff;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-image {
  width: 90px;
  height: 90px;
  object-fit: contain;
}

.app-name {
  margin-top: 15px;
  font-size: 18px;
  font-weight: 600;
}

.auth-desc {
  margin-top: 10px;
  font-size: 14px;
  color: #888;
  line-height: 1.5;
}

/* 按钮部分 */
.btn-container {
  margin-top: 50px;
}

.login-btn {
  height: 40px;
  background-color: #000;
  color: #fff;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 15px;
  border: none;
}

.login-btn::after {
  border: none;
}

/* 隐私政策勾选 */
.privacy-container {
  display: flex;
  align-items: flex-start;
  margin-top: 15px;
  justify-content: center;
}

.checkbox-wrapper {
  position: relative;
  margin-right: 8px;
  flex-shrink: 0;
  height: 18px;
  margin-top: 0px;
}

.custom-checkbox {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 1px solid #ddd;
  background-color: #fff;
}

.custom-checkbox.checked {
  border-color: #000;
  background-color: #fff;
}

.checkbox-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #000;
}

.privacy-text {
  font-size: 12px;
  color: #888;
  line-height: 1.5;
}

.privacy-link {
  color: #576b95;
}

/* 隐私政策弹窗 */
.privacy-policy-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.privacy-policy-modal.show {
  display: flex;
}

.privacy-modal-content {
  width: 90%;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.privacy-modal-header {
  padding: 20px;
  border-bottom: 1px solid #f1f1f1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.privacy-modal-title {
  font-size: 18px;
  font-weight: 600;
}

.privacy-close-btn {
  width: 30px;
  height: 30px;
  border-radius: 15px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-close::before {
  content: '\f00d';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  font-size: 14px;
  color: #333;
}

.privacy-modal-body {
  padding: 20px;
  flex: 1;
  max-height: 50vh;
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;
}

.content-section {
  margin-bottom: 20px;
  width: 100%;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.section-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  text-align: justify;
  width: 100%;
  box-sizing: border-box;
}

.section-text view {
  margin-bottom: 8px;
}

.section-text .indent {
  padding-left: 20px;
}

.privacy-modal-footer {
  padding: 15px 20px;
  display: flex;
  border-top: 1px solid #f1f1f1;
}

.privacy-btn {
  flex: 1;
  padding: 12px;
  text-align: center;
  border-radius: 8px;
  font-weight: 600;
}

.privacy-btn-cancel {
  background-color: #f5f5f5;
  color: #333;
  margin-right: 10px;
}

.privacy-btn-agree {
  background-color: #000;
  color: #fff;
}

/* 页脚 */
.footer {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin-bottom: 20px;
}

/* 授权弹窗 */
.auth-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  justify-content: center;
  align-items: center;
}

.auth-modal.show {
  display: flex;
}

.auth-modal-content {
  width: 85%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.auth-modal-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #f1f1f1;
}

.auth-modal-title {
  font-size: 17px;
  font-weight: 600;
}

.auth-modal-body {
  padding: 20px;
}

.modal-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.auth-item {
  display: flex;
  margin-bottom: 15px;
}

.auth-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.auth-icon-image {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.auth-text {
  flex: 1;
}

.auth-item-title {
  font-size: 14px;
  margin-bottom: 2px;
}

.auth-item-desc {
  font-size: 12px;
  color: #888;
}

.auth-modal-footer {
  display: flex;
  border-top: 1px solid #f1f1f1;
}

.auth-btn {
  flex: 1;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
}

.auth-btn-cancel {
  border-right: 0.5px solid #f1f1f1;
  color: #333;
}

.auth-btn-confirm {
  border-left: 0.5px solid #f1f1f1;
  color: #000;
  font-weight: 500;
}

/* 用户信息弹窗 */
.user-info-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.user-info-modal.show {
  display: flex;
}

.user-info-content {
  width: 85%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.user-info-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #f1f1f1;
}

.user-info-title {
  font-size: 17px;
  font-weight: 600;
}

.user-info-body {
  padding: 20px;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.avatar-btn {
  padding: 0;
  background: none;
  border: none;
  line-height: normal;
  border-radius: 0;
  width: 100px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-btn::after {
  border: none;
}

.avatar-img {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background-color: #f5f5f5;
  margin-bottom: 8px;
}

.avatar-text {
  font-size: 12px;
  color: #576b95;
}

.nickname-container {
  margin-bottom: 10px;
}

.nickname-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.nickname-input {
  height: 44px;
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  background-color: #f9f9f9;
}

.user-info-footer {
  display: flex;
  border-top: 1px solid #f1f1f1;
}

.user-info-btn {
  flex: 1;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
}

.user-info-btn-cancel {
  border-right: 0.5px solid #f1f1f1;
  color: #333;
}

.user-info-btn-confirm {
  border-left: 0.5px solid #f1f1f1;
  color: #000;
  font-weight: 500;
} 