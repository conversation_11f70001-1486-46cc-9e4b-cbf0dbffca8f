<?php
// 引入必要的文件
require_once 'auth.php';
require_once 'db.php';
require_once 'config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 只接受POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'error' => '不支持的请求方法'
    ]);
    exit;
}

// 获取请求体数据
$data = json_decode(file_get_contents('php://input'), true);

// 验证必要参数
if (!isset($data['outfit_id']) || !isset($data['is_public'])) {
    echo json_encode([
        'success' => false,
        'error' => '缺少必要参数'
    ]);
    exit;
}

$outfitId = intval($data['outfit_id']);
$isPublic = intval($data['is_public']) ? 1 : 0;

// 验证token获取用户ID
$auth = new Auth();

// 获取token
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 如果有Bearer前缀，去掉它
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
}

if (!$token) {
    echo json_encode([
        'success' => false,
        'error' => '未提供授权Token'
    ]);
    exit;
}

// 使用Auth类验证token
$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'success' => false,
        'error' => '未授权，请先登录'
    ]);
    exit;
}

$userId = $payload['sub']; // 从payload中获取用户ID

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();
    
    // 先检查穿搭是否存在并且属于当前用户
    $checkStmt = $conn->prepare("SELECT id FROM outfits WHERE id = :outfit_id AND user_id = :user_id LIMIT 1");
    $checkStmt->bindParam(':outfit_id', $outfitId, PDO::PARAM_INT);
    $checkStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkStmt->execute();
    
    if ($checkStmt->rowCount() === 0) {
        echo json_encode([
            'success' => false,
            'error' => '穿搭不存在或您无权修改'
        ]);
        exit;
    }
    
    // 更新穿搭公开状态
    $updateStmt = $conn->prepare("UPDATE outfits SET is_public = :is_public, updated_at = NOW() WHERE id = :outfit_id AND user_id = :user_id");
    $updateStmt->bindParam(':is_public', $isPublic, PDO::PARAM_INT);
    $updateStmt->bindParam(':outfit_id', $outfitId, PDO::PARAM_INT);
    $updateStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $updateStmt->execute();
    
    if ($updateStmt->rowCount() > 0) {
        echo json_encode([
            'success' => true,
            'message' => $isPublic ? '穿搭已公开' : '穿搭已设为私有',
            'data' => [
                'outfit_id' => $outfitId,
                'is_public' => $isPublic
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => '更新失败，请稍后重试'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => '服务器错误: ' . $e->getMessage()
    ]);
} 