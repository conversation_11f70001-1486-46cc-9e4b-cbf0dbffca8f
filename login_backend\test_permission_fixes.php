<?php
/**
 * 测试权限修复功能
 * 验证衣物编辑和穿搭编辑的权限控制是否正常工作
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    echo "=== 权限修复功能测试 ===\n";
    echo "当前用户ID: $userId\n\n";
    
    $db = new Database();
    $conn = $db->getConnection();
    
    // 1. 测试衣物权限
    echo "1. 测试衣物权限控制\n";
    
    // 获取用户可见的衣物
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.user_id, c.circle_id,
               CASE 
                   WHEN c.circle_id IS NULL THEN 'personal'
                   ELSE 'shared'
               END as data_source,
               u.nickname as creator_nickname
        FROM clothes c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.user_id = :user_id OR c.circle_id IN (
            SELECT circle_id FROM circle_members 
            WHERE user_id = :user_id AND status = 'active'
        )
        ORDER BY c.created_at DESC
        LIMIT 5
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($clothes as $item) {
        $isOwner = $item['user_id'] == $userId;
        $ownerLabel = $isOwner ? '[自己的]' : '[他人的]';
        
        echo "\n衣物: {$item['name']} (ID: {$item['id']}) $ownerLabel\n";
        echo "- 数据源: {$item['data_source']}\n";
        echo "- 创建者: {$item['creator_nickname']}\n";
        
        // 测试权限
        foreach (['view', 'edit', 'delete'] as $operation) {
            $permission = testClothesPermission($conn, $userId, $item['id'], $operation);
            $status = $permission['allowed'] ? '✅' : '❌';
            echo "- $operation: $status {$permission['reason']}\n";
        }
    }
    
    // 2. 测试穿搭权限
    echo "\n2. 测试穿搭权限控制\n";
    
    // 获取用户可见的穿搭
    $stmt = $conn->prepare("
        SELECT o.id, o.name, o.user_id, o.circle_id,
               CASE 
                   WHEN o.circle_id IS NULL THEN 'personal'
                   ELSE 'shared'
               END as data_source,
               u.nickname as creator_nickname
        FROM outfits o
        LEFT JOIN users u ON o.user_id = u.id
        WHERE o.user_id = :user_id OR o.circle_id IN (
            SELECT circle_id FROM circle_members 
            WHERE user_id = :user_id AND status = 'active'
        )
        ORDER BY o.created_at DESC
        LIMIT 5
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $outfits = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($outfits as $item) {
        $isOwner = $item['user_id'] == $userId;
        $ownerLabel = $isOwner ? '[自己的]' : '[他人的]';
        
        echo "\n穿搭: {$item['name']} (ID: {$item['id']}) $ownerLabel\n";
        echo "- 数据源: {$item['data_source']}\n";
        echo "- 创建者: {$item['creator_nickname']}\n";
        
        // 测试权限
        foreach (['view', 'edit', 'delete'] as $operation) {
            $permission = testOutfitPermission($conn, $userId, $item['id'], $operation);
            $status = $permission['allowed'] ? '✅' : '❌';
            echo "- $operation: $status {$permission['reason']}\n";
        }
    }
    
    // 3. 测试穿搭保存权限修复
    echo "\n3. 测试穿搭保存权限修复\n";
    
    if (!empty($outfits)) {
        $testOutfit = $outfits[0];
        echo "测试穿搭: {$testOutfit['name']} (ID: {$testOutfit['id']})\n";
        
        // 模拟保存操作的权限检查
        $canEdit = checkOutfitEditPermission($conn, $userId, $testOutfit['id']);
        
        if ($canEdit['allowed']) {
            echo "✅ 可以编辑此穿搭\n";
            echo "- 权限原因: {$canEdit['reason']}\n";
            echo "- 是否为创建者: " . ($canEdit['is_owner'] ? '是' : '否') . "\n";
            echo "- 用户角色: {$canEdit['user_role']}\n";
            
            // 检查保存时是否会保持原始创建者信息
            if (!$canEdit['is_owner']) {
                echo "⚠️ 注意：编辑他人穿搭时，应保持原始创建者信息\n";
                echo "- 原始创建者ID: {$testOutfit['user_id']}\n";
                echo "- 当前编辑者ID: $userId\n";
            }
        } else {
            echo "❌ 无法编辑此穿搭\n";
            echo "- 拒绝原因: {$canEdit['reason']}\n";
        }
    }
    
    // 4. 测试前端权限检查API
    echo "\n4. 测试前端权限检查API\n";
    
    if (!empty($clothes)) {
        $testClothes = $clothes[0];
        echo "测试衣物权限API (ID: {$testClothes['id']}):\n";
        
        $apiUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/check_circle_permission.php";
        
        foreach (['edit', 'delete'] as $operation) {
            $params = http_build_query([
                'data_type' => 'clothes',
                'data_id' => $testClothes['id'],
                'operation' => $operation
            ]);
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "$apiUrl?$params");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: ' . $_SERVER['HTTP_AUTHORIZATION']
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200) {
                $data = json_decode($response, true);
                if ($data && $data['status'] === 'success') {
                    $permission = $data['data'];
                    $status = $permission['allowed'] ? '✅' : '❌';
                    echo "- API $operation: $status {$permission['reason']}\n";
                } else {
                    echo "- API $operation: ❌ 响应错误\n";
                }
            } else {
                echo "- API $operation: ❌ HTTP错误 ($httpCode)\n";
            }
        }
    }
    
    echo "\n=== 测试完成 ===\n";
    echo "\n💡 前端测试建议:\n";
    echo "1. 打开衣物详情页，检查编辑/删除按钮是否根据权限显示\n";
    echo "2. 尝试编辑共享衣物，验证权限提示是否正确\n";
    echo "3. 打开穿搭编辑页，检查保存按钮是否根据权限显示\n";
    echo "4. 编辑共享穿搭并保存，验证创建者信息是否保持不变\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

/**
 * 测试衣物权限
 */
function testClothesPermission($conn, $userId, $clothesId, $operation) {
    // 简化的权限检查逻辑
    $stmt = $conn->prepare("SELECT user_id, circle_id FROM clothes WHERE id = :id");
    $stmt->bindParam(':id', $clothesId, PDO::PARAM_INT);
    $stmt->execute();
    $clothes = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$clothes) {
        return ['allowed' => false, 'reason' => '衣物不存在'];
    }
    
    $isOwner = $clothes['user_id'] == $userId;
    
    if ($operation === 'view') {
        return ['allowed' => true, 'reason' => '查看权限'];
    }
    
    if ($isOwner) {
        return ['allowed' => true, 'reason' => '衣物创建者'];
    }
    
    // 检查圈子权限（简化）
    if ($clothes['circle_id']) {
        $stmt = $conn->prepare("
            SELECT role FROM circle_members 
            WHERE user_id = :user_id AND circle_id = :circle_id AND status = 'active'
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindParam(':circle_id', $clothes['circle_id'], PDO::PARAM_INT);
        $stmt->execute();
        $role = $stmt->fetchColumn();
        
        if ($role === 'creator') {
            return ['allowed' => true, 'reason' => '圈子创建者'];
        }
        
        if ($role === 'member' && $operation === 'edit') {
            return ['allowed' => false, 'reason' => '圈子成员无编辑权限'];
        }
    }
    
    return ['allowed' => false, 'reason' => '权限不足'];
}

/**
 * 测试穿搭权限
 */
function testOutfitPermission($conn, $userId, $outfitId, $operation) {
    // 使用save_outfit.php中的权限检查函数
    return checkOutfitEditPermission($conn, $userId, $outfitId);
}

/**
 * 检查穿搭编辑权限（从save_outfit.php复制）
 */
function checkOutfitEditPermission($conn, $userId, $outfitId) {
    $result = [
        'allowed' => false,
        'reason' => '',
        'is_owner' => false,
        'user_role' => ''
    ];
    
    // 获取穿搭信息
    $stmt = $conn->prepare("SELECT user_id, circle_id FROM outfits WHERE id = :id");
    $stmt->bindParam(':id', $outfitId, PDO::PARAM_INT);
    $stmt->execute();
    $outfit = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$outfit) {
        $result['reason'] = '穿搭不存在';
        return $result;
    }
    
    // 检查是否是穿搭的创建者
    $result['is_owner'] = $outfit['user_id'] == $userId;
    
    // 如果是个人穿搭（没有circle_id），只有创建者可以编辑
    if (empty($outfit['circle_id'])) {
        if ($result['is_owner']) {
            $result['allowed'] = true;
            $result['reason'] = '穿搭创建者';
        } else {
            $result['reason'] = '非穿搭创建者，无权编辑个人穿搭';
        }
        return $result;
    }
    
    // 检查用户在圈子中的角色
    $stmt = $conn->prepare("
        SELECT role, status 
        FROM circle_members 
        WHERE user_id = :user_id AND circle_id = :circle_id AND status = 'active'
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':circle_id', $outfit['circle_id'], PDO::PARAM_INT);
    $stmt->execute();
    $circleRole = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$circleRole) {
        $result['reason'] = '用户不在该圈子中';
        return $result;
    }
    
    $result['user_role'] = $circleRole['role'];
    
    // 权限规则：圈子创建者或穿搭创建者可以编辑
    if ($circleRole['role'] === 'creator' || $result['is_owner']) {
        $result['allowed'] = true;
        $result['reason'] = $circleRole['role'] === 'creator' ? '圈子创建者权限' : '穿搭创建者权限';
    } else {
        $result['reason'] = '只有圈子创建者或穿搭创建者可以编辑';
    }
    
    return $result;
}
?>
