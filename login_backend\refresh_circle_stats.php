<?php
/**
 * 刷新圈子统计数据API
 * 用于手动更新所有圈子成员的统计数据
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

// 获取圈子ID参数
$circleId = isset($_GET['circle_id']) ? intval($_GET['circle_id']) : 0;

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 如果没有指定圈子ID，查找用户所在的圈子
    if ($circleId <= 0) {
        $findCircleSql = "SELECT cm.circle_id, cm.role
                          FROM circle_members cm 
                          JOIN outfit_circles c ON cm.circle_id = c.id 
                          WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'
                          LIMIT 1";
        $findCircleStmt = $conn->prepare($findCircleSql);
        $findCircleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $findCircleStmt->execute();
        
        $userCircle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$userCircle) {
            echo json_encode([
                'status' => 'error',
                'message' => '您当前未加入任何圈子'
            ]);
            exit;
        }
        
        $circleId = $userCircle['circle_id'];
    } else {
        // 验证用户是否有权限操作该圈子
        $checkPermissionSql = "SELECT cm.role
                              FROM circle_members cm 
                              WHERE cm.circle_id = :circle_id AND cm.user_id = :user_id AND cm.status = 'active'";
        $checkPermissionStmt = $conn->prepare($checkPermissionSql);
        $checkPermissionStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
        $checkPermissionStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $checkPermissionStmt->execute();
        
        $permission = $checkPermissionStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$permission) {
            echo json_encode([
                'status' => 'error',
                'message' => '您没有权限操作该圈子'
            ]);
            exit;
        }
    }
    
    // 获取圈子中的所有活跃成员
    $membersSql = "SELECT user_id FROM circle_members 
                   WHERE circle_id = :circle_id AND status = 'active'";
    $membersStmt = $conn->prepare($membersSql);
    $membersStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $membersStmt->execute();
    
    $members = $membersStmt->fetchAll(PDO::FETCH_COLUMN);
    $updatedCount = 0;
    
    // 更新每个成员的统计数据
    foreach ($members as $memberId) {
        $stats = calculateMemberStats($conn, $circleId, $memberId);
        
        // 插入或更新统计记录
        $upsertSql = "INSERT INTO circle_member_stats 
                      (circle_id, user_id, wardrobe_count, clothes_count, outfit_count, 
                       clothing_category_count, outfit_category_count, tag_count, last_contribution_at)
                      VALUES (:circle_id, :user_id, :wardrobe_count, :clothes_count, :outfit_count, 
                              :clothing_category_count, 0, 0, NOW())
                      ON DUPLICATE KEY UPDATE
                      wardrobe_count = :wardrobe_count,
                      clothes_count = :clothes_count,
                      outfit_count = :outfit_count,
                      clothing_category_count = :clothing_category_count,
                      updated_at = NOW()";
        
        $upsertStmt = $conn->prepare($upsertSql);
        $upsertStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
        $upsertStmt->bindParam(':user_id', $memberId, PDO::PARAM_INT);
        $upsertStmt->bindParam(':wardrobe_count', $stats['wardrobe_count'], PDO::PARAM_INT);
        $upsertStmt->bindParam(':clothes_count', $stats['clothes_count'], PDO::PARAM_INT);
        $upsertStmt->bindParam(':outfit_count', $stats['outfit_count'], PDO::PARAM_INT);
        $upsertStmt->bindParam(':clothing_category_count', $stats['clothing_category_count'], PDO::PARAM_INT);
        $upsertStmt->execute();
        
        $updatedCount++;
    }
    
    echo json_encode([
        'status' => 'success',
        'message' => "已成功更新 $updatedCount 名成员的统计数据",
        'data' => [
            'circle_id' => $circleId,
            'updated_members' => $updatedCount
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '更新统计数据失败：' . $e->getMessage()
    ]);
}

/**
 * 计算用户在圈子中的实际统计数据
 */
function calculateMemberStats($conn, $circleId, $userId) {
    $stats = [
        'wardrobe_count' => 0,
        'clothes_count' => 0,
        'outfit_count' => 0,
        'clothing_category_count' => 0
    ];
    
    // 统计衣橱数量
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM wardrobes WHERE circle_id = :circle_id AND user_id = :user_id");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['wardrobe_count'] = intval($result['count']);
    
    // 统计衣物数量
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM clothes WHERE circle_id = :circle_id AND user_id = :user_id");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['clothes_count'] = intval($result['count']);
    
    // 统计穿搭数量
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM outfits WHERE circle_id = :circle_id AND user_id = :user_id");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['outfit_count'] = intval($result['count']);
    
    // 统计分类数量
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM clothing_categories WHERE circle_id = :circle_id AND user_id = :user_id AND is_system = 0");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['clothing_category_count'] = intval($result['count']);
    
    return $stats;
}
?>
