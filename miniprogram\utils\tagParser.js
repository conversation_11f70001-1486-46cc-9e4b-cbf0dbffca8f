/**
 * 标签解析工具函数
 * 用于统一处理各种格式的标签字符串
 */

/**
 * 解析标签字符串为数组
 * 
 * @param {string} tagsStr - 标签字符串，可能是JSON格式或逗号分隔格式
 * @returns {Array} - 解析后的标签数组
 */
function parseTags(tagsStr) {
  if (!tagsStr) return [];
  
  let tags = [];
  
  try {
    // 移除可能的混合格式
    // 例如 ["标签1","标签2"],标签3,标签4
    if (tagsStr.indexOf('[') !== -1 && tagsStr.indexOf(']') !== -1) {
      // 尝试提取JSON部分
      const jsonMatch = tagsStr.match(/\[.*?\]/);
      if (jsonMatch) {
        const jsonPart = jsonMatch[0];
        const restPart = tagsStr.replace(jsonPart, '');
        
        // 处理JSON部分
        try {
          const parsedJson = JSON.parse(jsonPart);
          if (Array.isArray(parsedJson)) {
            parsedJson.forEach(tag => {
              if (tag && typeof tag === 'string') {
                tags.push(tag.trim());
              }
            });
          }
        } catch (e) {
          console.warn("JSON部分解析失败:", e);
        }
        
        // 处理剩余部分
        if (restPart) {
          const restTags = restPart.split(',')
            .map(tag => tag.trim())
            .filter(tag => tag);
          tags = [...tags, ...restTags];
        }
        
        return tags;
      }
    }
    
    // 尝试作为标准JSON解析
    if (tagsStr.startsWith('[') || tagsStr.startsWith('{')) {
      try {
        const parsedTags = JSON.parse(tagsStr);
        if (Array.isArray(parsedTags)) {
          return parsedTags.map(tag => tag.trim()).filter(Boolean);
        } else if (typeof parsedTags === 'object') {
          return Object.keys(parsedTags)
            .filter(key => parsedTags[key])
            .map(key => key.trim());
        }
      } catch (e) {
        console.warn("作为标准JSON解析失败:", e);
        // 解析失败时不返回，继续尝试其他方法
      }
    }
    
    // 作为普通逗号分隔字符串处理
    return tagsStr.split(',')
      .map(tag => tag.trim())
      .filter(tag => tag);
      
  } catch (e) {
    console.error("标签解析失败:", e, "原始标签:", tagsStr);
    // 发生任何异常，尝试作为普通逗号分隔字符串处理
    const fallbackTags = tagsStr.split(',')
      .map(tag => tag.trim())
      .filter(tag => tag);
    return fallbackTags;
  }
}

module.exports = {
  parseTags
}; 