<view class="container">
  <!-- 页面头部说明 -->
  <view class="header-info">
    <text class="info-text">长按衣物可拖动调整排序，调整后点击保存按钮更新排序</text>
  </view>

  <!-- 衣物列表 -->
  <view class="clothes-list">
    <view wx:for="{{clothesList}}" wx:key="id" class="clothes-item" data-index="{{index}}" data-id="{{item.id}}">
      <view class="clothes-content">
        <!-- 衣物图片 -->
        <image class="clothes-image" src="{{item.imageUrl}}" mode="aspectFit"></image>

        <!-- 衣物信息 -->
        <view class="clothes-info">
          <text class="clothes-name">{{item.name}}</text>
          <text class="clothes-category">{{item.categoryName || item.category}}</text>
        </view>

        <!-- 排序按钮 -->
        <view class="sort-controls">
          <view class="sort-btn" bindtap="moveUp" data-index="{{index}}">
            <text class="sort-icon">↑</text>
          </view>
          <view class="sort-btn" bindtap="moveDown" data-index="{{index}}">
            <text class="sort-icon">↓</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{clothesList.length === 0 && !loading}}">
    <text class="empty-text">暂无衣物数据</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 底部保存按钮 -->
<view class="save-btn-container">
  <button class="save-btn" bindtap="saveSortOrder" disabled="{{saving}}">
    {{saving ? '保存中...' : '保存'}}
  </button>
</view>
