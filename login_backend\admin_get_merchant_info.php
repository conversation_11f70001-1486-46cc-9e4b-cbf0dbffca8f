<?php
header("Content-Type: application/json");
require_once './db.php';
require_once './auth.php';
require_once './config.php';

// 初始化响应数组
$response = [
    'code' => 0,
    'message' => 'success',
    'data' => []
];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['code'] = 405;
    $response['message'] = 'Method Not Allowed';
    echo json_encode($response);
    exit;
}

// 获取认证头
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';

// 验证Bearer令牌格式
if (empty($authHeader) || strpos($authHeader, 'Bearer ') !== 0) {
    $response['code'] = 401;
    $response['message'] = '无效的认证头';
    echo json_encode($response);
    exit;
}

// 提取令牌
$token = substr($authHeader, 7); // 去掉'Bearer '前缀

// 获取POST参数
$postData = json_decode(file_get_contents("php://input"), true) ?: [];
$merchantId = isset($postData['merchant_id']) ? intval($postData['merchant_id']) : 0;

// 记录日志，帮助调试
error_log("admin_get_merchant_info.php - 请求参数: " . json_encode($postData));

// 验证参数
if (empty($merchantId)) {
    $response['code'] = 400;
    $response['message'] = '缺少商户ID参数';
    echo json_encode($response);
    exit;
}

// 验证管理员令牌
$auth = new Auth();
$adminInfo = $auth->verifyAdminToken($token);

if ($adminInfo === false) {
    $response['code'] = 401;
    $response['message'] = '无效或已过期的管理员令牌';
    echo json_encode($response);
    exit;
}

// 记录日志，帮助调试
error_log("admin_get_merchant_info.php - 管理员信息: " . json_encode($adminInfo));

$db = new Database();
$conn = $db->getConnection();

try {
    // 查询商户基本信息
    $sql = "SELECT id, nickname, avatar_url, merchant_status, share_try_on_credits, paid_try_on_count 
            FROM users 
            WHERE id = :merchant_id";
    
    $stmt = $conn->prepare($sql);
    $stmt->bindValue(':merchant_id', $merchantId, PDO::PARAM_INT);
    $stmt->execute();
    
    $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$merchant) {
        $response['code'] = 404;
        $response['message'] = '商户不存在';
        echo json_encode($response);
        exit;
    }
    
    // 获取该商户的衣物数量
    $clothesCountSql = "SELECT COUNT(*) as count FROM clothes WHERE user_id = :merchant_id";
    $clothesCountStmt = $conn->prepare($clothesCountSql);
    $clothesCountStmt->bindValue(':merchant_id', $merchantId, PDO::PARAM_INT);
    $clothesCountStmt->execute();
    $clothesCount = $clothesCountStmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
    
    // 获取商户的注册时间和最近活跃时间
    $activitySql = "SELECT created_at, updated_at FROM users WHERE id = :merchant_id";
    $activityStmt = $conn->prepare($activitySql);
    $activityStmt->bindValue(':merchant_id', $merchantId, PDO::PARAM_INT);
    $activityStmt->execute();
    $activityData = $activityStmt->fetch(PDO::FETCH_ASSOC);
    
    // 组装返回数据
    $response['data'] = [
        'id' => $merchant['id'],
        'nickname' => $merchant['nickname'] ?: '未设置昵称',
        'avatar_url' => $merchant['avatar_url'],
        'merchant_status' => $merchant['merchant_status'] ?: 'no',
        'share_try_on_credits' => (int)$merchant['share_try_on_credits'],
        'paid_try_on_count' => (int)$merchant['paid_try_on_count'],
        'clothes_count' => (int)$clothesCount,
        'created_at' => $activityData['created_at'] ?? null,
        'updated_at' => $activityData['updated_at'] ?? null
    ];
} catch (Exception $e) {
    $response['code'] = 500;
    $response['message'] = '处理请求时发生错误: ' . $e->getMessage();
    error_log("admin_get_merchant_info.php - 错误: " . $e->getMessage());
} finally {
    // PDO connections are closed automatically when the variable is unset
    $conn = null;
}

echo json_encode($response); 