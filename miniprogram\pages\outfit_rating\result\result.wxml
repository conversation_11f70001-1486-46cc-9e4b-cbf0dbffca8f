<!-- pages/outfit_rating/result/result.wxml -->
<view class="container">
  <view class="rating-card">
    <view class="photo-section">
      <image class="outfit-photo" src="{{photoUrl}}" mode="aspectFit"></image>
      <view class="overall-score-badge">
        <view class="score-value">{{ratingData.rating_details.overall_score}}</view>
        <view class="score-label">总分</view>
      </view>
    </view>
    
    <view class="result-content">
      <!-- 细项评分模块 -->
      <view class="result-module">
        <view class="section-header">
          <view class="section-title">细项评分</view>
        </view>
        <view class="score-grid">
          <view class="score-item">
            <view class="score-item-value">{{ratingData.rating_details.coordination_score}}</view>
            <view class="score-item-label">协调性</view>
          </view>
          <view class="score-item">
            <view class="score-item-value">{{ratingData.rating_details.occasion_score}}</view>
            <view class="score-item-label">场合适宜</view>
          </view>
          <view class="score-item">
            <view class="score-item-value">{{ratingData.rating_details.style_match_score}}</view>
            <view class="score-item-label">气质匹配</view>
          </view>
          <view class="score-item">
            <view class="score-item-value">{{ratingData.rating_details.fashion_score}}</view>
            <view class="score-item-label">时尚度</view>
          </view>
          <view class="score-item">
            <view class="score-item-value">{{ratingData.rating_details.detail_score}}</view>
            <view class="score-item-label">细节处理</view>
          </view>
        </view>
      </view>
      
      <!-- 穿搭分析模块 -->
      <view class="result-module">
        <view class="section-header">
          <view class="section-title">穿搭分析</view>
        </view>
        <view class="section-content">{{ratingData.rating_details.outfit_analysis}}</view>
      </view>
      
      <!-- 优点模块 -->
      <view class="result-module">
        <view class="section-header">
          <view class="section-title">优点</view>
        </view>
        <view class="section-content">{{ratingData.rating_details.strength}}</view>
      </view>
      
      <!-- 改进建议模块 -->
      <view class="result-module">
        <view class="section-header">
          <view class="section-title">改进建议</view>
        </view>
        <view class="section-content">{{ratingData.rating_details.improvement}}</view>
      </view>
      
      <!-- 适合场合模块 -->
      <view class="result-module">
        <view class="section-header">
          <view class="section-title">适合场合</view>
        </view>
        <view class="section-content">{{ratingData.rating_details.occasion_recommendation}}</view>
      </view>
    </view>
  </view>
  
  <view class="action-buttons">
    <button class="action-button share-button" open-type="share">分享结果</button>
  </view>
  
  <view class="footer-tips">
    <view class="tips-text">AI穿搭评分仅供参考，个人风格更重要</view>
  </view>
</view> 