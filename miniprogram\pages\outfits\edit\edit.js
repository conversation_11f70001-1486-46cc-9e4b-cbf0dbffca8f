const app = getApp();
const permission = require('../../../utils/permission');

Page({
  data: {
    outfit: null,
    loading: true,
    showClothesSelector: false,
    clothingList: [],
    wardrobes: [],
    selectedWardrobe: 'all',
    selectedCategory: 'all',
    loadingClothes: false,
    selectedClothingId: null,
    activeItemIndex: -1, // 当前选中的衣物索引
    canvasWidth: 375, // 默认画布宽度
    canvasHeight: 500, // 默认画布高度
    scaleValue: 1, // 缩放值

    // 新增：数据源相关
    dataSource: 'personal', // personal, shared, all
    showDataSourcePopup: false,
    dataSourceOptions: [
      { key: 'personal', name: '个人数据', icon: '👤' },
      { key: 'shared', name: '共享数据', icon: '👥' },
      { key: 'all', name: '全部数据', icon: '🌐' }
    ],
    rotateValue: 0, // 旋转值

    // 新增：权限相关
    canEdit: true, // 默认可以编辑
    canSave: true, // 默认可以保存
    isOwner: true, // 默认是创建者
    userRole: '',
    permissionLoading: false,

    // 添加分类相关数据
    categories: [], // 分类列表
    categoryIndex: 0, // 选中的分类索引
    categoryId: null, // 选中的分类ID
    categoryName: '默认分类', // 选中的分类名称
    loadingCategories: false, // 分类加载状态
    // 新增：分类弹窗相关数据
    showCategoryModal: false,
    
    // 添加下拉菜单状态
    showDropdown: false,
    
    // 新增：衣物分类相关
    clothingCategories: [], // 衣物分类列表
    loadingClothingCategories: false,

    newCategory: {
      name: '',
      description: ''
    },
    categoryNameLength: 0,
    categoryDescLength: 0,
    submittingCategory: false,
    fromCalendar: false,
    
    // 对齐线相关状态
    showHorizontalLine: false,      // 是否显示水平对齐线
    showVerticalLine: false,        // 是否显示垂直对齐线
    horizontalLinePosition: 0,      // 水平对齐线位置
    verticalLinePosition: 0,        // 垂直对齐线位置
    snapThreshold: 10               // 磁吸吸附阈值（像素）
  },
  
  onLoad: function(options) {
    // 获取穿搭ID
    const outfitId = options.id;
    if (!outfitId) {
      wx.showToast({
        title: '穿搭ID无效',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }
    
    // 检查是否从日历页面跳转而来
    const fromCalendar = options.fromCalendar === 'true';
    this.setData({
      fromCalendar: fromCalendar
    });
    
    // 加载穿搭信息
    this.loadOutfit(outfitId);
    
    // 获取屏幕信息，调整画布尺寸
    wx.getSystemInfo({
      success: (res) => {
        const canvasWidth = res.windowWidth;
        const canvasHeight = canvasWidth * 1.33; // 高宽比例约4:3
        
        this.setData({
          canvasWidth: canvasWidth,
          canvasHeight: canvasHeight
        });
      }
    });
    
    // 加载衣橱列表
    this.loadWardrobes();
    
    // 加载分类列表
    this.loadCategories();
    
    // 加载衣物分类列表
    this.loadClothingCategories();
    
    // 添加性能优化标志
    this._isMoving = false;
    this._positionCache = {};
  },
  
  onShow: function() {
    // 当从创建分类页面返回时，重新加载分类列表
    if (app.globalData.needRefreshOutfitCategories) {
      this.loadCategories();
      app.globalData.needRefreshOutfitCategories = false;
    }
  },
  
  // 加载穿搭信息
  loadOutfit: function(outfitId) {
    this.setData({ loading: true });
    
    // 从本地存储获取穿搭数据
    const outfits = app.getOutfits();
    const outfit = outfits.find(item => item.id === outfitId);
    
    if (!outfit) {
      wx.showToast({
        title: '未找到穿搭信息',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }
    
    // 确保穿搭中的临时衣物能正确显示
    if (outfit.items && outfit.items.length > 0) {
      outfit.items.forEach(item => {
        // 检查是否为临时衣物（ID以temp_开头）
        if (item.clothing_id && typeof item.clothing_id === 'string' && 
            item.clothing_id.startsWith('temp_')) {
          console.log('发现临时衣物:', item.clothing_id);
          // 确保临时衣物有正确的clothing_data
          if (!item.clothing_data) {
            item.clothing_data = {
              name: '临时衣物',
              category: 'tops',
              image_url: item.image_url || ''
            };
          }
        }
      });
    }
    
    // 设置数据
    this.setData({
      outfit: outfit,
      loading: false,
      categoryId: outfit.category_id || null,
      categoryName: outfit.category_name || '默认分类'
    });

    // 检查编辑权限
    this.checkEditPermissions(outfitId);
  },
  
  // 检查编辑权限
  checkEditPermissions: function(outfitId) {
    if (!outfitId) return;

    this.setData({ permissionLoading: true });

    permission.checkCirclePermission('outfits', outfitId, 'edit')
      .then(editPermission => {
        this.setData({
          canEdit: editPermission.allowed,
          canSave: editPermission.allowed,
          isOwner: editPermission.is_owner,
          userRole: editPermission.user_role,
          permissionLoading: false
        });

        console.log('穿搭编辑权限检查结果:', {
          canEdit: editPermission.allowed,
          isOwner: editPermission.is_owner,
          userRole: editPermission.user_role,
          reason: editPermission.reason
        });

        // 如果没有编辑权限，显示提示
        if (!editPermission.allowed) {
          wx.showModal({
            title: '权限提示',
            content: `您没有编辑此穿搭的权限。\n原因：${editPermission.reason}`,
            showCancel: true,
            cancelText: '返回',
            confirmText: '仅查看',
            success: (res) => {
              if (res.cancel) {
                wx.navigateBack();
              }
            }
          });
        }
      })
      .catch(error => {
        console.error('权限检查失败:', error);
        this.setData({
          canEdit: false,
          canSave: false,
          permissionLoading: false
        });
      });
  },

  // 加载分类列表
  loadCategories: function() {
    // 如果已经在加载中，直接返回
    if (this.data.loadingCategories) {
      return;
    }
    
    this.setData({ loadingCategories: true });
    
    // 检查登录状态
    if (!app.globalData.token) {
      this.setData({ 
        categories: [],
        loadingCategories: false
      });
      return;
    }
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_outfit_categories.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      data: {
        page: 1,
        per_page: 100 // 获取足够多的分类
      },
      success: (res) => {
        console.log('获取穿搭分类列表:', res.data);
        
        if (res.statusCode === 200 && res.data.success) {
          const categories = res.data.data || [];
          
          // 记录当前穿搭的分类
          const currentCategoryId = this.data.outfit ? this.data.outfit.category_id : null;
          
          if (currentCategoryId) {
            // 查找当前分类的索引
            const index = categories.findIndex(item => item.id.toString() === currentCategoryId.toString());
            
            if (index !== -1) {
              this.setData({
                categories: categories,
                categoryIndex: index,
                categoryId: categories[index].id,
                categoryName: categories[index].name
              });
            } else {
              // 如果没找到对应分类，默认选第一个
              this.setData({
                categories: categories,
                categoryIndex: 0,
                categoryId: categories.length > 0 ? categories[0].id : null,
                categoryName: categories.length > 0 ? categories[0].name : '默认分类'
              });
            }
          } else {
            // 查找默认分类
            const defaultCategory = categories.find(item => item.is_default == 1);
            
            if (defaultCategory) {
              // 如果有默认分类，设置为选中状态
              const defaultIndex = categories.findIndex(item => item.id === defaultCategory.id);
              
              this.setData({
                categories: categories,
                categoryIndex: defaultIndex >= 0 ? defaultIndex : 0,
                categoryId: defaultCategory.id,
                categoryName: defaultCategory.name
              });
            } else if (categories.length > 0) {
              // 如果没有默认分类但有其他分类，选择第一个
              this.setData({
                categories: categories,
                categoryIndex: 0,
                categoryId: categories[0].id,
                categoryName: categories[0].name
              });
            } else {
              // 没有任何分类
              this.setData({
                categories: categories,
                categoryIndex: 0,
                categoryId: null,
                categoryName: '默认分类'
              });
            }
          }
        }
      },
      fail: (err) => {
        console.error('获取分类列表失败:', err);
      },
      complete: () => {
        this.setData({ loadingCategories: false });
      }
    });
  },
  
  // 分类选择改变事件
  onCategoryChange: function(e) {
    const index = Number(e.detail.value);
    const category = this.data.categories[index];
    
    this.setData({
      categoryIndex: index,
      categoryId: category.id,
      categoryName: category.name
    });
  },
  
  // 修改：跳转到新建分类页面
  goToAddCategory() {
    wx.navigateTo({
      url: '/pages/outfit_categories/add/add'
    });
  },
  
  // 添加：显示/隐藏下拉菜单
  toggleDropdown() {
    this.setData({
      showDropdown: !this.data.showDropdown
    });
  },
  
  // 添加：隐藏下拉菜单
  hideDropdown() {
    this.setData({
      showDropdown: false
    });
  },
  
  // 添加：选择分类
  onCategorySelect(e) {
    const index = e.currentTarget.dataset.index;
    const category = this.data.categories[index];
    
    this.setData({
      categoryIndex: index,
      categoryId: category.id || category._id,
      categoryName: category.name,
      showDropdown: false
    });
  },
  
  // 新增：关闭分类弹窗
  closeCategoryModal() {
    this.setData({
      showCategoryModal: false,
      newCategory: {
        name: '',
        description: ''
      },
      categoryNameLength: 0,
      categoryDescLength: 0,
      submittingCategory: false
    });
  },
  
  // 新增：监听分类名称输入
  onCategoryNameInput(e) {
    const value = e.detail.value;
    this.setData({
      'newCategory.name': value,
      categoryNameLength: value.length
    });
  },
  
  // 新增：监听分类描述输入
  onCategoryDescInput(e) {
    const value = e.detail.value;
    this.setData({
      'newCategory.description': value,
      categoryDescLength: value.length
    });
  },
  
  // 新增：阻止事件冒泡
  stopPropagation(e) {
    // 阻止事件冒泡，防止点击弹窗内容时关闭弹窗
    return false;
  },
  
  // 新增：创建分类的方法
  createCategory() {
    // 表单验证
    if (!this.data.newCategory.name.trim()) {
      wx.showToast({
        title: '请输入分类名称',
        icon: 'none'
      });
      return;
    }

    this.setData({ submittingCategory: true });
    
    wx.cloud.callFunction({
      name: 'outfitCategoryFunctions',
      data: {
        action: 'createCategory',
        category: this.data.newCategory
      }
    })
    .then(res => {
      if (res.result && res.result.success) {
        wx.showToast({
          title: '创建成功',
          icon: 'success'
        });
        
        // 添加新分类到列表并选中
        const newCategory = {
          _id: res.result.categoryId,
          name: this.data.newCategory.name,
          description: this.data.newCategory.description,
          create_time: new Date()
        };
        
        const updatedCategories = [...this.data.categories, newCategory];
        const newIndex = updatedCategories.length - 1;
        
        this.setData({
          categories: updatedCategories,
          categoryIndex: newIndex,
          categoryId: newCategory._id,
          categoryName: newCategory.name
        });
        
        // 关闭弹窗
        this.closeCategoryModal();
      } else {
        wx.showToast({
          title: '创建失败',
          icon: 'none'
        });
      }
    })
    .catch(err => {
      console.error('创建分类失败:', err);
      wx.showToast({
        title: '创建失败，请重试',
        icon: 'none'
      });
    })
    .finally(() => {
      this.setData({ submittingCategory: false });
    });
  },
  
  // 加载衣橱列表
  loadWardrobes: function() {
    if (!app.globalData.token) {
      console.log('用户未登录，无法加载衣橱');
      return;
    }
    
    // 调用API获取衣橱列表
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_wardrobes.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          const wardrobes = res.data.data || [];
          
          // 添加"全部衣橱"选项
          wardrobes.unshift({
            id: 'all',
            name: '全部衣橱'
          });
          
          this.setData({
            wardrobes: wardrobes
          });
          
          // 默认加载全部衣物
          this.loadClothes('all', 'all');
        } else {
          console.error('获取衣橱列表失败:', res);
        }
      },
      fail: (err) => {
        console.error('获取衣橱请求失败:', err);
      }
    });
  },
  
  // 加载衣物列表
  loadClothes: function(wardrobeId, category) {
    if (!app.globalData.token) {
      console.log('用户未登录，无法加载衣物');
      return;
    }

    this.setData({ loadingClothes: true });

    // 构建API请求参数
    let url = `${app.globalData.apiBaseUrl}/get_clothes.php`;
    let params = {};

    if (wardrobeId && wardrobeId !== 'all') {
      params.wardrobe_id = wardrobeId;
    }

    if (category && category !== 'all') {
      params.category = category;
    }

    // 新增：添加圈子数据参数
    if (this.data.dataSource !== 'personal') {
      params.include_circle_data = 'true';
      params.data_source = this.data.dataSource;
    }

    // 将参数添加到URL
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');

    if (queryString) {
      url += `?${queryString}`;
    }

    console.log('加载衣物 - 数据源:', this.data.dataSource, 'URL:', url);
    
    // 调用API获取衣物列表
    wx.request({
      url: url,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        if (res.statusCode === 200 && !res.data.error) {
          // 转换字段名称 - 后端使用下划线命名，前端使用驼峰命名
          const clothes = (res.data.data || []).map(item => {
            return {
              id: item.id,
              name: item.name,
              category: item.category,
              imageUrl: item.image_url,
              tags: item.tags,
              description: item.description,
              createdAt: item.created_at,
              wardrobeId: item.wardrobe_id,
              sortOrder: item.sort_order || item.id,
              // 保留其他可能的字段
              ...item
            };
          });

          // 按sort_order排序
          clothes.sort((a, b) => {
            return a.sortOrder - b.sortOrder;
          });

          this.setData({
            clothingList: clothes,
            loadingClothes: false
          });
        } else {
          console.error('获取衣物列表失败:', res);
          this.setData({ loadingClothes: false });
        }
      },
      fail: (err) => {
        console.error('获取衣物请求失败:', err);
        this.setData({ loadingClothes: false });
      }
    });
  },
  
  // 显示衣物选择器
  showClothesSelector: function() {
    this.setData({
      showClothesSelector: true
    });
  },
  
  // 隐藏衣物选择器
  hideClothesSelector: function() {
    this.setData({
      showClothesSelector: false
    });
  },
  
  // 切换衣橱
  changeWardrobe: function(e) {
    const wardrobeId = e.currentTarget.dataset.id;
    this.setData({ selectedWardrobe: wardrobeId });
    this.loadClothes(wardrobeId, this.data.selectedCategory);
  },
  
  // 切换分类
  changeCategory: function(e) {
    const item = e.currentTarget.dataset.item;
    const category = item ? item.code : e.currentTarget.dataset.category;
    this.setData({ selectedCategory: category });
    this.loadClothes(this.data.selectedWardrobe, category);
  },

  // 新增：数据源切换相关方法
  toggleDataSourcePopup: function() {
    this.setData({
      showDataSourcePopup: !this.data.showDataSourcePopup
    });
  },

  closeDataSourcePopup: function() {
    this.setData({
      showDataSourcePopup: false
    });
  },

  switchDataSource: function(e) {
    const dataSource = e.currentTarget.dataset.source;
    console.log("切换数据源到:", dataSource);

    this.setData({
      dataSource: dataSource,
      showDataSourcePopup: false,
      loadingClothes: true
    });

    // 重新加载衣物数据
    this.loadClothes(this.data.selectedWardrobe, this.data.selectedCategory);
  },
  
  // 显示上传选项
  showUploadOptions: function() {
    wx.showActionSheet({
      itemList: ['拍照', '从相册选择'],
      success: (res) => {
        const sourceType = res.tapIndex === 0 ? 'camera' : 'album';
        this.chooseAndUploadImage(sourceType);
      }
    });
  },
  
  // 选择并上传图片
  chooseAndUploadImage: function(sourceType) {
    wx.showLoading({
      title: '准备选择图片...',
      mask: true
    });
    
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: [sourceType],
      success: (res) => {
        console.log('选择图片成功:', res);
        
        if (!res.tempFiles || res.tempFiles.length === 0) {
          wx.hideLoading();
          wx.showToast({
            title: '未选择图片',
            icon: 'none'
          });
          return;
        }
        
        const tempFilePath = res.tempFiles[0].tempFilePath;
        const size = res.tempFiles[0].size;
        
        // 检查文件大小，不能超过10MB
        if (size > 10 * 1024 * 1024) {
          wx.hideLoading();
          wx.showToast({
            title: '图片不能超过10MB',
            icon: 'none'
          });
          return;
        }
        
        // 立即隐藏选择中的loading，上传时会显示新的loading
        wx.hideLoading();
        
        // 上传图片
        this.uploadClothingImage(tempFilePath);
      },
      fail: (err) => {
        wx.hideLoading();
        console.log('选择图片失败', err);
        
        // 只有在非用户取消的情况下才提示错误
        if (err.errMsg !== 'chooseMedia:fail cancel') {
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      }
    });
  },
  
  // 上传衣物图片并添加到衣橱
  uploadClothingImage: function(tempFilePath) {
    if (!app.globalData.token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    if (!tempFilePath) {
      console.error('临时文件路径无效');
      wx.showToast({
        title: '图片无效',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '上传中...',
      mask: true
    });
    
    // 上传图片
    wx.uploadFile({
      url: `${app.globalData.apiBaseUrl}/upload_image.php`,
      filePath: tempFilePath,
      name: 'image',
      header: {
        'Authorization': app.globalData.token
      },
      formData: {
        'segment_image': 'true' // 需要抠图
      },
      success: (res) => {
        console.log('上传图片响应:', res);
        
        try {
          // 检查状态码
          if (res.statusCode !== 200) {
            wx.hideLoading();
            console.error('上传图片请求失败，状态码:', res.statusCode);
            wx.showToast({
              title: `上传失败(${res.statusCode})`,
              icon: 'none'
            });
            return;
          }
          
          // 解析上传响应
          let response;
          try {
            response = JSON.parse(res.data);
          } catch (e) {
            wx.hideLoading();
            console.error('解析上传响应失败:', e, '原始响应:', res.data);
            wx.showToast({
              title: '服务器响应解析失败',
              icon: 'none'
            });
            return;
          }
          
          if (!response.error) {
            console.log('图片上传成功，获取到URL:', response.data.image_url);
            const imageUrl = response.data.image_url;
            
            if (!imageUrl) {
              wx.hideLoading();
              console.error('图片URL为空');
              wx.showToast({
                title: '图片处理失败',
                icon: 'none'
              });
              return;
            }
            
            // 将图片上传到OSS以获取CDN URL
            console.log('上传图片到OSS...');
            wx.request({
              url: `${app.globalData.apiBaseUrl}/upload_image_to_oss.php`,
              method: 'POST',
              header: {
                'Authorization': app.globalData.token,
                'Content-Type': 'application/json'
              },
              data: {
                image_url: imageUrl
              },
              success: (ossRes) => {
                console.log('上传到OSS响应:', ossRes.data);
                
                // 处理可能的连接JSON对象问题
                let responseData = ossRes.data;
                
                // 如果返回的是字符串且包含两个相连的JSON对象
                if (typeof responseData === 'string' && responseData.indexOf('}{') > 0) {
                  console.log('检测到连接的多个JSON对象，尝试提取最后一个');
                  try {
                    // 找到最后一个完整的JSON对象
                    const lastJsonStartPos = responseData.lastIndexOf('{');
                    if (lastJsonStartPos >= 0) {
                      const lastJsonStr = responseData.substring(lastJsonStartPos);
                      const parsedJson = JSON.parse(lastJsonStr);
                      responseData = parsedJson;
                      console.log('成功提取最后一个JSON对象:', parsedJson);
                    }
                  } catch (e) {
                    console.error('提取最后一个JSON对象失败:', e);
                    // 保留原始响应
                    responseData = ossRes.data;
                  }
                }
                
                // 如果成功获取到了CDN URL
                if (typeof responseData === 'object' && !responseData.error && responseData.data && responseData.data.image_url) {
                  const ossImageUrl = responseData.data.image_url;
                  console.log('获取到OSS图片URL:', ossImageUrl);
                  
                  wx.hideLoading();
                  
                  // 弹出确认框询问用户是否保存到衣橱
                  wx.showModal({
                    title: '保存到衣橱',
                    content: '是否将此衣物保存到衣橱中？保存后可在其他穿搭中使用',
                    confirmText: '保存',
                    cancelText: '不保存',
                    success: (modalRes) => {
                      if (modalRes.confirm) {
                        // 用户点击"保存"，将衣物保存到衣橱，使用已上传到OSS的图片URL
                        this.saveToWardrobe(ossImageUrl);
                      } else {
                        // 用户点击"不保存"，直接添加临时衣物，同样使用已上传到OSS的图片URL
                        // 创建一个临时衣物对象
                        const tempClothing = {
                          id: 'temp_' + Date.now(), // 创建一个临时ID
                          name: '临时衣物',
                          category: 'tops', // 默认分类为上衣
                          image_url: ossImageUrl, // 使用OSS URL
                          tags: '手动上传'
                        };
                        
                        // 将临时衣物添加到穿搭中
                        this.addClothingToOutfit(tempClothing, {
                          closeSelector: true,
                          immediateClose: true
                        });
                        
                        wx.showToast({
                          title: '已添加到穿搭',
                          icon: 'success'
                        });
                      }
                    }
                  });
                } else {
                  // 如果获取CDN URL失败，仍然使用原始URL
                  console.error('上传到OSS失败:', responseData);
                  wx.hideLoading();
                  
                  // 弹出确认框询问用户是否保存到衣橱
                  wx.showModal({
                    title: '保存到衣橱',
                    content: '是否将此衣物保存到衣橱中？保存后可在其他穿搭中使用',
                    confirmText: '保存',
                    cancelText: '不保存',
                    success: (modalRes) => {
                      if (modalRes.confirm) {
                        // 用户点击"保存"，将衣物保存到衣橱
                        this.saveToWardrobe(imageUrl);
                      } else {
                        // 用户点击"不保存"，直接添加临时衣物
                        this.addTemporaryClothingWithOriginalUrl(imageUrl);
                      }
                    }
                  });
                }
              },
              fail: (err) => {
                // 上传到OSS失败，回退使用原始URL
                console.error('上传到OSS请求失败:', err);
                wx.hideLoading();
                
                // 弹出确认框询问用户是否保存到衣橱
                wx.showModal({
                  title: '保存到衣橱',
                  content: '是否将此衣物保存到衣橱中？保存后可在其他穿搭中使用',
                  confirmText: '保存',
                  cancelText: '不保存',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      // 用户点击"保存"，将衣物保存到衣橱
                      this.saveToWardrobe(imageUrl);
                    } else {
                      // 用户点击"不保存"，直接添加临时衣物
                      this.addTemporaryClothingWithOriginalUrl(imageUrl);
                    }
                  }
                });
              }
            });
          } else {
            wx.hideLoading();
            console.error('上传图片API返回错误:', response.error, response.msg);
            wx.showToast({
              title: response.msg || '上传失败',
              icon: 'none'
            });
          }
        } catch (e) {
          wx.hideLoading();
          console.error('处理上传响应时发生异常:', e);
          wx.showToast({
            title: '上传处理失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('上传图片请求失败:', err);
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 使用原始URL添加临时衣物（当OSS上传失败时使用）
  addTemporaryClothingWithOriginalUrl: function(imageUrl) {
    // 创建一个临时衣物对象
    const tempClothing = {
      id: 'temp_' + Date.now(), // 创建一个临时ID
      name: '临时衣物',
      category: 'tops', // 默认分类为上衣
      image_url: imageUrl, // 使用原始URL
      tags: '手动上传'
    };
    
    // 将临时衣物添加到穿搭中
    this.addClothingToOutfit(tempClothing, {
      closeSelector: true,
      immediateClose: true
    });
    
    wx.showToast({
      title: '已添加到穿搭',
      icon: 'success'
    });
  },
  
  // 新增：将衣物保存到衣橱的方法
  saveToWardrobe: function(imageUrl) {
    console.log('开始将衣物保存到衣橱:', imageUrl);
    
    // 构建衣物数据
    const clothingData = {
      name: '手动上传的衣物',
      category: 'tops', // 默认分类为上衣
      image_url: imageUrl,
      tags: '手动上传'
    };
    
    console.log('准备保存的衣物数据:', clothingData);
    
    // 调用API保存衣物
    wx.request({
      url: `${app.globalData.apiBaseUrl}/add_clothing.php`,
      method: 'POST',
      header: {
        'Authorization': app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: clothingData,
      success: (res) => {
        wx.hideLoading();
        console.log('保存衣物响应:', res.data);
        
        if (res.statusCode === 200) {
          // 检查响应格式并提取衣物数据
          let clothing = this.extractClothingFromResponse(res.data);
          
          if (clothing) {
            console.log('衣物保存成功，准备添加到穿搭:', clothing);
            
            wx.showToast({
              title: '保存成功',
              icon: 'success',
              duration: 1000
            });
            
            // 保存当前选择的分类和衣橱
            const currentCategory = this.data.selectedCategory;
            const currentWardrobe = this.data.selectedWardrobe;
            
            // 刷新衣物列表，确保能看到新上传的衣物
            // 如果当前选中的是上衣分类或全部分类，直接刷新当前分类
            if (currentCategory === 'tops' || currentCategory === 'all') {
              console.log('刷新当前分类的衣物列表，确保显示新上传的衣物');
              this.loadClothes(currentWardrobe, currentCategory);
            } else {
              // 如果当前不是上衣分类，则先切换到上衣分类查看新上传的衣物
              console.log('切换到上衣分类并刷新衣物列表');
              this.setData({ selectedCategory: 'tops' });
              this.loadClothes(currentWardrobe, 'tops');
            }
            
            // 允许用户查看新上传的衣物，然后再关闭选择器
            setTimeout(() => {
              // 将新衣物添加到穿搭中，使用延迟关闭选择器选项
              this.addClothingToOutfit(clothing, {
                closeSelector: true,
                immediateClose: false  // 不立即关闭，让用户有时间查看新上传的衣物
              });
            }, 800); // 延迟0.8秒执行，确保衣物列表已经刷新
          } else {
            console.error('无法从响应中提取有效的衣物数据:', res.data);
            wx.showToast({
              title: '衣物数据解析失败',
              icon: 'none'
            });
          }
        } else {
          console.error('保存衣物失败:', res.data);
          wx.showToast({
            title: res.data && res.data.msg ? res.data.msg : '保存衣物失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('保存衣物请求失败:', err);
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 从响应中提取衣物数据
  extractClothingFromResponse: function(response) {
    // 如果是对象类型，尝试直接获取data字段
    if (typeof response === 'object' && !response.error && response.data) {
      return response.data;
    }
    
    // 如果是字符串类型，可能是连接的多个JSON对象
    if (typeof response === 'string') {
      console.log('响应是字符串类型，尝试解析');
      
      // 尝试直接解析整个响应
      try {
        const jsonObj = JSON.parse(response);
        if (!jsonObj.error && jsonObj.data) {
          return jsonObj.data;
        }
      } catch (e) {
        console.log('直接解析整个响应失败，尝试处理连接的JSON');
      }
      
      // 检查是否存在多个JSON对象连接的情况
      if (response.indexOf('}{') > 0) {
        console.log('检测到多个JSON对象连接在一起');
        
        // 尝试找到包含衣物数据的JSON对象
        try {
          // 分割响应字符串
          const parts = response.split('}{');
          if (parts.length >= 2) {
            // 尝试解析第二部分（通常包含衣物数据）
            const secondPart = '{' + parts[1];
            const secondJson = JSON.parse(secondPart);
            
            if (!secondJson.error && secondJson.data) {
              console.log('从第二个JSON对象中提取到衣物数据:', secondJson.data);
              return secondJson.data;
            }
          }
        } catch (e) {
          console.error('解析分割的JSON对象失败:', e);
        }
        
        // 如果上面的方法失败，尝试更激进的方法
        try {
          // 查找最后一个包含id的JSON对象
          const matches = response.match(/\{"id":"(\d+)","name":"([^"]+)","category":"([^"]+)","image_url":"([^"]+)"/);
          
          if (matches && matches.length >= 5) {
            console.log('使用正则表达式提取衣物数据');
            return {
              id: matches[1],
              name: matches[2],
              category: matches[3],
              image_url: matches[4],
              tags: '手动上传'
            };
          }
        } catch (e) {
          console.error('使用正则表达式提取数据失败:', e);
        }
      }
    }
    
    // 所有方法都失败，返回null
    return null;
  },
  
  // 将衣物添加到穿搭中
  addClothingToOutfit: function(clothing, options = {}) {
    console.log('开始添加衣物到穿搭:', clothing);
    
    // 默认选项
    const defaultOptions = {
      closeSelector: true,    // 是否关闭选择器
      immediateClose: true    // 是否立即关闭选择器
    };
    
    // 合并选项
    const finalOptions = { ...defaultOptions, ...options };
    console.log('添加衣物选项:', finalOptions);
    
    // 检查clothing对象是否有效
    if (!clothing) {
      console.error('无效的衣物数据: undefined');
      wx.showToast({
        title: '添加衣物失败',
        icon: 'none'
      });
      return;
    }
    
    if (!clothing.id) {
      console.error('衣物数据缺少ID:', clothing);
      wx.showToast({
        title: '衣物ID无效',
        icon: 'none'
      });
      return;
    }
    
    if (!clothing.image_url) {
      console.error('衣物数据缺少图片URL:', clothing);
      wx.showToast({
        title: '衣物图片无效',
        icon: 'none'
      });
      return;
    }
    
    // 计算合适的初始位置，确保在画布中央
    const centerX = (this.data.canvasWidth - 150) / 2;
    const centerY = (this.data.canvasHeight - 200) / 2;
    
    console.log('画布尺寸:', this.data.canvasWidth, this.data.canvasHeight);
    console.log('计算的中心位置:', centerX, centerY);
    
    // 确保ID使用正确的格式
    let clothingId = clothing.id;
    // 检查是否是临时ID（以temp_开头）
    const isTemp = typeof clothingId === 'string' && clothingId.startsWith('temp_');
    
    // 如果不是临时ID，尝试转换为数字
    if (!isTemp) {
      try {
        const numId = parseInt(clothingId);
        if (!isNaN(numId)) {
          clothingId = numId;
        }
      } catch (e) {
        console.error('衣物ID无法转换为数字:', clothingId, e);
        // 保留原始ID
      }
    }
    
    // 创建新的衣物项
    const newItem = {
      clothing_id: clothingId,
      clothing_data: {
        name: clothing.name || (isTemp ? '临时衣物' : '手动上传的衣物'),
        category: clothing.category || 'tops',
        image_url: clothing.image_url
      },
      position: {
        x: centerX,
        y: centerY
      },
      size: {
        width: 150, // 默认宽度
        height: 200 // 默认高度
      },
      rotation: 0, // 确保rotation属性初始化为0
      z_index: this.data.outfit && this.data.outfit.items ? this.data.outfit.items.length + 1 : 1 // 新添加的衣物在最上层
    };
    
    console.log('创建的新衣物项:', newItem);
    console.log('当前穿搭数据:', this.data.outfit);
    
    // 确保outfit和items存在
    if (!this.data.outfit) {
      console.error('当前穿搭数据不存在');
      wx.showToast({
        title: '穿搭数据无效',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.outfit.items) {
      console.log('当前穿搭items数组不存在，初始化为空数组');
      this.setData({
        'outfit.items': []
      });
    }
    
    // 创建新的items数组
    const newItems = [...(this.data.outfit.items || []), newItem];
    const newActiveIndex = newItems.length - 1;
    
    console.log('更新后的items数组:', newItems);
    console.log('新选中的衣物索引:', newActiveIndex);
    
    // 在更新数据前先设置选择相关的状态
    this.setData({
      scaleValue: 1,
      rotateValue: 0
    });
    
    const updateData = {
      'outfit.items': newItems,
      'outfit.updated_at': new Date().toISOString(),
      activeItemIndex: newActiveIndex // 选中新添加的衣物
    };
    
    // 如果需要关闭选择器并且是立即关闭
    if (finalOptions.closeSelector && finalOptions.immediateClose) {
      updateData.showClothesSelector = false;
    }
    
    // 一次性更新所有需要变化的数据
    this.setData(updateData, () => {
      // 回调确认数据已更新
      console.log('数据已更新:', 
        '当前items数量:', this.data.outfit.items.length,
        '选择器状态:', this.data.showClothesSelector, 
        '选中索引:', this.data.activeItemIndex
      );
      
      // 允许用户立即调整新添加的衣物
      wx.showToast({
        title: '衣物已添加',
        icon: 'success',
        duration: 1000
      });
      
      // 如果需要关闭选择器但不是立即关闭，则延迟关闭
      if (finalOptions.closeSelector && !finalOptions.immediateClose) {
        setTimeout(() => {
          this.setData({ showClothesSelector: false });
          console.log('延迟关闭选择器');
        }, 1500); // 延迟1.5秒关闭，给用户时间查看
      }
    });
  },
  
  // 选择衣物添加到穿搭
  selectClothing: function(e) {
    const clothingId = e.currentTarget.dataset.id;
    console.log('选择衣物添加到穿搭，衣物ID:', clothingId);
    
    if (!clothingId) {
      console.error('无效的衣物ID');
      wx.showToast({
        title: '衣物ID无效',
        icon: 'none'
      });
      return;
    }
    
    const clothing = this.data.clothingList.find(item => item.id == clothingId);
    
    if (!clothing) {
      console.error('未找到ID为' + clothingId + '的衣物');
      wx.showToast({
        title: '衣物不存在',
        icon: 'none'
      });
      return;
    }
    
    if (!clothing.image_url) {
      console.error('衣物缺少图片URL:', clothing);
      wx.showToast({
        title: '衣物图片无效',
        icon: 'none'
      });
      return;
    }
    
    // 确保outfit和items存在
    if (!this.data.outfit) {
      console.error('当前穿搭数据不存在');
      wx.showToast({
        title: '穿搭数据无效',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.outfit.items) {
      console.log('当前穿搭items数组不存在，初始化为空数组');
      this.setData({
        'outfit.items': []
      });
    }
    
    // 确保ID是数字类型
    let parsedClothingId;
    try {
      parsedClothingId = parseInt(clothingId);
      if (isNaN(parsedClothingId)) {
        throw new Error('ID转换为数字失败');
      }
    } catch (e) {
      console.error('衣物ID无法转换为数字:', clothingId, e);
      // 仍然使用原始ID，可能是字符串
      parsedClothingId = clothingId;
    }
    
    // 计算合适的初始位置，确保在画布中央
    const centerX = (this.data.canvasWidth - 150) / 2;
    const centerY = (this.data.canvasHeight - 200) / 2;
    
    // 创建新的衣物项
    const newItem = {
      clothing_id: parsedClothingId,
      clothing_data: {
        name: clothing.name || '未命名衣物',
        category: clothing.category || 'tops',
        image_url: clothing.image_url
      },
      position: {
        x: centerX, // 居中
        y: centerY
      },
      size: {
        width: 150, // 默认宽度
        height: 200 // 默认高度
      },
      rotation: 0, // 确保rotation属性初始化为0
      z_index: this.data.outfit && this.data.outfit.items ? this.data.outfit.items.length + 1 : 1 // 新添加的衣物在最上层
    };
    
    // 创建新的items数组
    const newItems = [...(this.data.outfit.items || []), newItem];
    const newActiveIndex = newItems.length - 1;
    
    console.log('选择衣物：更新后的items数组长度:', newItems.length);
    console.log('选择衣物：新选中的衣物索引:', newActiveIndex);
    
    // 在更新数据前先设置选择相关的状态
    this.setData({
      scaleValue: 1,
      rotateValue: 0
    });
    
    // 使用新的addClothingToOutfit函数添加衣物，使用立即关闭选择器选项
    // 直接使用新衣物对象调用addClothingToOutfit，保持与手动上传的一致性
    this.addClothingToOutfit({
      id: parsedClothingId,
      name: clothing.name || '未命名衣物',
      category: clothing.category || 'tops',
      image_url: clothing.image_url
    }, {
      closeSelector: true,
      immediateClose: true // 立即关闭选择器
    });
  },
  
  // 选中衣物
  selectItem: function(e) {
    const index = e.currentTarget.dataset.index;
    
    // 处理取消选择的情况
    if (index === "-1" || index < 0) {
      this.setData({
        activeItemIndex: -1,
        scaleValue: 1,
        rotateValue: 0
      });
      return;
    }
    
    // 正常选择衣物的情况
    if (this.data.outfit && this.data.outfit.items && this.data.outfit.items[index]) {
      const rotation = this.data.outfit.items[index].rotation || 0;
      
      this.setData({
        activeItemIndex: index,
        scaleValue: 1,
        rotateValue: rotation
      });
    } else {
      // 如果找不到对应的衣物，重置选择状态
      this.setData({
        activeItemIndex: -1,
        scaleValue: 1,
        rotateValue: 0
      });
    }
  },
  
  // 计算对齐点和显示对齐线
  calculateAlignmentLines: function(currentIndex, currentX, currentY, currentWidth, currentHeight) {
    // 如果没有衣物或者正在移动的衣物索引无效，不显示对齐线
    if (!this.data.outfit || !this.data.outfit.items || currentIndex < 0) {
      return { x: currentX, y: currentY, snapX: false, snapY: false };
    }
    
    let snapX = false;  // 是否水平方向发生了磁吸
    let snapY = false;  // 是否垂直方向发生了磁吸
    let snapToX = currentX; // 最终吸附后的X坐标
    let snapToY = currentY; // 最终吸附后的Y坐标
    
    const threshold = this.data.snapThreshold; // 磁吸阈值
    const currentItem = this.data.outfit.items[currentIndex];
    
    // 当前衣物边界
    const currentLeft = currentX;
    const currentRight = currentX + currentWidth;
    const currentTop = currentY;
    const currentBottom = currentY + currentHeight;
    const currentCenterX = currentX + currentWidth / 2;
    const currentCenterY = currentY + currentHeight / 2;
    
    // 画布边界
    const canvasLeft = 0;
    const canvasRight = this.data.canvasWidth;
    const canvasTop = 0;
    const canvasBottom = this.data.canvasHeight;
    
    // 对齐坐标和距离
    let minHorizontalDiff = threshold;
    let minVerticalDiff = threshold;
    let horizontalLine = null;
    let verticalLine = null;
    let isRightBorder = false; // 标记是否是右边界对齐
    let isBottomBorder = false; // 标记是否是底部边界对齐
    
    // 与画布边缘对齐
    // 左边缘对齐
    if (Math.abs(currentLeft - canvasLeft) < minHorizontalDiff) {
      minHorizontalDiff = Math.abs(currentLeft - canvasLeft);
      snapToX = canvasLeft;
      verticalLine = canvasLeft;
      snapX = true;
      isRightBorder = false;
    }
    
    // 右边缘对齐
    if (Math.abs(currentRight - canvasRight) < minHorizontalDiff) {
      minHorizontalDiff = Math.abs(currentRight - canvasRight);
      snapToX = canvasRight - currentWidth;
      verticalLine = canvasRight;
      snapX = true;
      isRightBorder = true; // 标记为右边界对齐
    }
    
    // 上边缘对齐
    if (Math.abs(currentTop - canvasTop) < minVerticalDiff) {
      minVerticalDiff = Math.abs(currentTop - canvasTop);
      snapToY = canvasTop;
      horizontalLine = canvasTop;
      snapY = true;
      isBottomBorder = false;
    }
    
    // 下边缘对齐
    if (Math.abs(currentBottom - canvasBottom) < minVerticalDiff) {
      minVerticalDiff = Math.abs(currentBottom - canvasBottom);
      snapToY = canvasBottom - currentHeight;
      horizontalLine = canvasBottom;
      snapY = true;
      isBottomBorder = true; // 标记为底部边界对齐
    }
    
    // 与其他衣物对齐
    for (let i = 0; i < this.data.outfit.items.length; i++) {
      // 跳过当前移动的衣物
      if (i === currentIndex) continue;
      
      const item = this.data.outfit.items[i];
      
      // 其他衣物的边界
      const itemLeft = item.position.x;
      const itemRight = item.position.x + item.size.width;
      const itemTop = item.position.y;
      const itemBottom = item.position.y + item.size.height;
      const itemCenterX = item.position.x + item.size.width / 2;
      const itemCenterY = item.position.y + item.size.height / 2;
      
      // 水平对齐检测
      
      // 左边缘对左边缘
      if (Math.abs(currentLeft - itemLeft) < minHorizontalDiff) {
        minHorizontalDiff = Math.abs(currentLeft - itemLeft);
        snapToX = itemLeft;
        verticalLine = itemLeft;
        snapX = true;
        isRightBorder = false;
      }
      
      // 右边缘对右边缘
      if (Math.abs(currentRight - itemRight) < minHorizontalDiff) {
        minHorizontalDiff = Math.abs(currentRight - itemRight);
        snapToX = itemRight - currentWidth;
        verticalLine = itemRight;
        snapX = true;
        isRightBorder = false; // 不是画布右边界，而是其他元素的右边缘
      }
      
      // 左边缘对右边缘
      if (Math.abs(currentLeft - itemRight) < minHorizontalDiff) {
        minHorizontalDiff = Math.abs(currentLeft - itemRight);
        snapToX = itemRight;
        verticalLine = itemRight;
        snapX = true;
        isRightBorder = false;
      }
      
      // 右边缘对左边缘
      if (Math.abs(currentRight - itemLeft) < minHorizontalDiff) {
        minHorizontalDiff = Math.abs(currentRight - itemLeft);
        snapToX = itemLeft - currentWidth;
        verticalLine = itemLeft;
        snapX = true;
        isRightBorder = false;
      }
      
      // 中心线对中心线
      if (Math.abs(currentCenterX - itemCenterX) < minHorizontalDiff) {
        minHorizontalDiff = Math.abs(currentCenterX - itemCenterX);
        snapToX = itemCenterX - currentWidth / 2;
        verticalLine = itemCenterX;
        snapX = true;
        isRightBorder = false;
      }
      
      // 垂直对齐检测
      
      // 上边缘对上边缘
      if (Math.abs(currentTop - itemTop) < minVerticalDiff) {
        minVerticalDiff = Math.abs(currentTop - itemTop);
        snapToY = itemTop;
        horizontalLine = itemTop;
        snapY = true;
        isBottomBorder = false;
      }
      
      // 下边缘对下边缘
      if (Math.abs(currentBottom - itemBottom) < minVerticalDiff) {
        minVerticalDiff = Math.abs(currentBottom - itemBottom);
        snapToY = itemBottom - currentHeight;
        horizontalLine = itemBottom;
        snapY = true;
        isBottomBorder = false; // 不是画布底部边界
      }
      
      // 上边缘对下边缘
      if (Math.abs(currentTop - itemBottom) < minVerticalDiff) {
        minVerticalDiff = Math.abs(currentTop - itemBottom);
        snapToY = itemBottom;
        horizontalLine = itemBottom;
        snapY = true;
        isBottomBorder = false;
      }
      
      // 下边缘对上边缘
      if (Math.abs(currentBottom - itemTop) < minVerticalDiff) {
        minVerticalDiff = Math.abs(currentBottom - itemTop);
        snapToY = itemTop - currentHeight;
        horizontalLine = itemTop;
        snapY = true;
        isBottomBorder = false;
      }
      
      // 中心线对中心线
      if (Math.abs(currentCenterY - itemCenterY) < minVerticalDiff) {
        minVerticalDiff = Math.abs(currentCenterY - itemCenterY);
        snapToY = itemCenterY - currentHeight / 2;
        horizontalLine = itemCenterY;
        snapY = true;
        isBottomBorder = false;
      }
    }
    
    // 更新对齐线状态
    this.setData({
      showHorizontalLine: snapY,
      showVerticalLine: snapX,
      horizontalLinePosition: horizontalLine,
      verticalLinePosition: verticalLine,
      isRightBorder: isRightBorder,
      isBottomBorder: isBottomBorder
    });
    
    // 返回吸附坐标和是否吸附的状态
    return {
      x: snapToX,
      y: snapToY,
      snapX: snapX,
      snapY: snapY
    };
  },
  
  // 隐藏所有对齐线
  hideAlignmentLines: function() {
    this.setData({
      showHorizontalLine: false,
      showVerticalLine: false
    });
  },
  
  // 移动衣物 - 拖动过程中
  onItemMove: function(e) {
    // 获取移动的衣物索引和位置
    const index = e.currentTarget.dataset.index;
    const x = e.detail.x;
    const y = e.detail.y;
    
    // 标记正在移动
    this._isMoving = true;
    
    // 缓存当前位置
    if (!this._positionCache) {
      this._positionCache = {};
    }
    
    // 获取当前衣物的尺寸
    const item = this.data.outfit.items[index];
    const width = item.size.width;
    const height = item.size.height;
    
    // 计算对齐点和显示对齐线
    const snapResult = this.calculateAlignmentLines(index, x, y, width, height);
    
    // 更新位置，使用磁吸效果的位置
    this._positionCache[index] = { 
      x: snapResult.snapX ? snapResult.x : x, 
      y: snapResult.snapY ? snapResult.y : y 
    };
    
    // 防止频繁更新导致抖动
    if (this._moveUpdateTimer) {
      clearTimeout(this._moveUpdateTimer);
    }
    
    // 每60ms最多更新一次UI，避免过度渲染
    this._moveUpdateTimer = setTimeout(() => {
      if (this._isMoving && this._positionCache[index]) {
        const cachedPos = this._positionCache[index];
        // 只更新数据而不重新渲染整个视图
        const outfit = this.data.outfit;
        outfit.items[index].position.x = cachedPos.x;
        outfit.items[index].position.y = cachedPos.y;
      }
    }, 60);
  },
  
  // 移动衣物结束 - 拖动松手时
  onItemMoveEnd: function(e) {
    // 获取索引
    const index = e.currentTarget.dataset.index;
    
    // 设置不在移动状态
    this._isMoving = false;
    
    // 如果没有缓存位置，直接返回
    if (!this._positionCache || !this._positionCache[index]) {
      return;
    }
    
    // 确保任何待处理的更新被取消
    if (this._moveUpdateTimer) {
      clearTimeout(this._moveUpdateTimer);
      this._moveUpdateTimer = null;
    }
    
    // 从缓存获取最终位置
    const { x, y } = this._positionCache[index];
    
    // 一次性更新到视图
    this.setData({
      [`outfit.items[${index}].position.x`]: x,
      [`outfit.items[${index}].position.y`]: y
    });
    
    // 清除位置缓存
    delete this._positionCache[index];
    
    // 隐藏对齐线
    this.hideAlignmentLines();
  },
  
  // 缩放衣物
  scaleItem: function(e) {
    const value = e.detail.value;
    const index = this.data.activeItemIndex;
    
    if (index < 0 || !this.data.outfit || !this.data.outfit.items[index]) return;
    
    // 计算新的尺寸
    const item = this.data.outfit.items[index];
    const originalWidth = item.size.width / this.data.scaleValue;
    const originalHeight = item.size.height / this.data.scaleValue;
    
    // 使用setData更新尺寸，确保UI和数据同步
    this.setData({
      [`outfit.items[${index}].size.width`]: originalWidth * value,
      [`outfit.items[${index}].size.height`]: originalHeight * value,
      scaleValue: value
    });
  },
  
  // 旋转衣物
  rotateItem: function(e) {
    const value = e.detail.value;
    const index = this.data.activeItemIndex;
    
    if (index < 0 || !this.data.outfit || !this.data.outfit.items || !this.data.outfit.items[index]) return;
    
    // 使用setData更新旋转角度，确保UI和数据同步
    this.setData({
      [`outfit.items[${index}].rotation`]: value,
      rotateValue: value
    });
  },
  
  // 用更安全的方式刷新视图
  forceRefresh: function() {
    // 只刷新关键状态值以避免重置位置
    this.setData({
      forceUpdate: Date.now()
    });
  },
  
  // 删除当前选中的衣物
  deleteItem: function(e) {
    // 获取被点击元素的索引
    const index = e.currentTarget.dataset.index !== undefined ? 
                e.currentTarget.dataset.index : this.data.activeItemIndex;
    
    if (index < 0) {
      wx.showToast({
        title: '请先选择衣物',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: '确定要从穿搭中移除此衣物吗？',
      success: (res) => {
        if (res.confirm) {
          // 创建新的items数组，避免直接修改原数组
          const newItems = [...this.data.outfit.items];
          
          // 移除衣物
          newItems.splice(index, 1);
          
          // 更新z-index
          newItems.forEach((item, i) => {
            item.z_index = i + 1;
          });
          
          // 更新界面，使用完整路径更新
          this.setData({
            'outfit.items': newItems,
            activeItemIndex: -1
          });
        }
      }
    });
  },
  
  // 将衣物移到上层
  bringForward: function() {
    const index = this.data.activeItemIndex;
    
    if (index < 0 || index >= this.data.outfit.items.length - 1) {
      return; // 已经是最上层或未选中
    }
    
    // 创建临时数组用于操作
    const items = [...this.data.outfit.items];
    
    // 交换当前项与上一项的z-index
    const currentZIndex = items[index].z_index;
    const nextZIndex = items[index + 1].z_index;
    
    items[index].z_index = nextZIndex;
    items[index + 1].z_index = currentZIndex;
    
    // 重新排序数组
    items.sort((a, b) => a.z_index - b.z_index);
    
    // 更新activeItemIndex
    const newIndex = index + 1;
    
    // 更新界面
    this.setData({
      'outfit.items': items,
      activeItemIndex: newIndex
    });
  },
  
  // 将衣物移到下层
  sendBackward: function() {
    const index = this.data.activeItemIndex;
    
    if (index <= 0) {
      return; // 已经是最下层或未选中
    }
    
    // 创建临时数组用于操作
    const items = [...this.data.outfit.items];
    
    // 交换当前项与下一项的z-index
    const currentZIndex = items[index].z_index;
    const prevZIndex = items[index - 1].z_index;
    
    items[index].z_index = prevZIndex;
    items[index - 1].z_index = currentZIndex;
    
    // 重新排序数组
    items.sort((a, b) => a.z_index - b.z_index);
    
    // 更新activeItemIndex
    const newIndex = index - 1;
    
    // 更新界面
    this.setData({
      'outfit.items': items,
      activeItemIndex: newIndex
    });
  },
  
  // 新增：输入穿搭名称
  onNameInput: function(e) {
    this.setData({
      'outfit.name': e.detail.value
    });
  },
  
  // 保存穿搭
  saveOutfit: function() {
    // 检查保存权限
    if (!this.data.canSave) {
      wx.showModal({
        title: '权限不足',
        content: '您没有保存此穿搭的权限',
        showCancel: false,
        confirmText: '我知道了'
      });
      return;
    }

    // 处理临时衣物
    if (this.data.outfit && this.data.outfit.items) {
      // 遍历穿搭中的所有衣物项
      const items = this.data.outfit.items.map(item => {
        // 如果是临时衣物（ID以temp_开头），确保保存数据结构正确
        if (item.clothing_id && String(item.clothing_id).startsWith('temp_')) {
          console.log('处理临时衣物:', item.clothing_id);
          // 确保临时衣物的数据结构保持不变
          return {
            ...item,
            // 保持临时ID不变
            clothing_id: item.clothing_id
          };
        }
        return item; // 非临时衣物不变
      });
      
      // 更新穿搭数据
      this.setData({
        'outfit.items': items
      });
    }
    
    // 更新上次修改时间和分类信息
    this.setData({
      'outfit.category_id': this.data.categoryId,
      'outfit.category_name': this.data.categoryName,
      'outfit.updated_at': new Date().toISOString()
    });
    
    // 保存到服务器和本地存储
    app.saveOutfit(this.data.outfit, (result) => {
      if (result.success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
        
        // 设置全局刷新标志
        app.globalData.needRefreshOutfits = true;
        
        // 如果来自日历页面，保存后返回日历页面
        if (this.data.fromCalendar) {
          // 将穿搭信息存入全局变量，以便日历页面获取
          app.globalData.selectedOutfitForCalendar = this.data.outfit;
          
          // 获取日历选中的日期
          const selectedDate = app.globalData.selectedCalendarDate;
          
          if (selectedDate) {
            // 显示加载状态
            wx.showLoading({
              title: '保存日期关联...',
              mask: true
            });
    
            // 发送请求，保存穿搭日期关联
            wx.request({
              url: `${app.globalData.apiBaseUrl}/save_calendar_outfit.php`,
              method: 'POST',
              header: {
                'Authorization': app.globalData.token,
                'Content-Type': 'application/json'
              },
              data: {
                outfit_id: this.data.outfit.id,
                calendar_date: selectedDate,
                action: 'add'
              },
              success: (res) => {
      wx.hideLoading();
                console.log('保存穿搭日历关联响应:', res.data);
      
                if (res.statusCode === 200 && !res.data.error) {
                  // 更新全局变量，确保日历页面知道需要显示哪个穿搭
                  app.globalData.calendarOutfitUpdated = true;
                  app.globalData.calendarOutfitData = {
                    outfitId: this.data.outfit.id,
                    date: selectedDate,
                    outfit: this.data.outfit
                  };
                  
        wx.showToast({
                    title: '穿搭已安排',
                    icon: 'success'
                  });
                } else {
                  console.error('保存穿搭日期关联失败:', res.data);
                  wx.showToast({
                    title: '日期关联失败',
                    icon: 'none'
                  });
                }
              },
              fail: (err) => {
                wx.hideLoading();
                console.error('保存日期关联请求失败:', err);
                wx.showToast({
                  title: '网络错误',
                  icon: 'none'
                });
              },
              complete: () => {
                // 返回日历页面
                wx.switchTab({
                  url: '/pages/calendar/index/index'
                });
          }
        });
      } else {
            // 如果没有选中日期，直接返回日历页面
            wx.switchTab({
              url: '/pages/calendar/index/index'
            });
          }
        } else {
          // 如果不是从日历页面进入，保存后跳转到穿搭详情页面
          setTimeout(() => {
            const outfitId = this.data.outfit.id;
            if (outfitId) {
              // 跳转到穿搭详情页面
              wx.redirectTo({
                url: `/pages/outfits/detail/detail?id=${outfitId}`
              });
            } else {
              // 如果没有ID，降级到穿搭列表页面
              wx.switchTab({
                url: '/pages/outfits/index/index'
              });
            }
          }, 500); // 延迟500ms，确保Toast显示完成
        }
      } else {
        wx.showToast({
          title: result.error || '保存失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 取消编辑并返回
  cancelEdit: function() {
    wx.showModal({
      title: '确认取消',
      content: '取消后将丢失未保存的更改，确定要退出吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },
  
  // 添加：加载衣物分类列表
  loadClothingCategories: function() {
    // 默认的系统分类，当API请求失败时使用
    const defaultCategories = [
      { id: 'all', code: 'all', name: '全部', is_system: false },
      { id: 'tops', code: 'tops', name: '上衣', is_system: true },
      { id: 'pants', code: 'pants', name: '裤子', is_system: true },
      { id: 'skirts', code: 'skirts', name: '裙子', is_system: true },
      { id: 'outerwear', code: 'outerwear', name: '外套', is_system: true },
      { id: 'shoes', code: 'shoes', name: '鞋子', is_system: true },
      { id: 'bags', code: 'bags', name: '包包', is_system: true },
      { id: 'accessories', code: 'accessories', name: '配饰', is_system: true }
    ];

    if (!app.globalData.token) {
      console.log('用户未登录，使用默认衣物分类');
      this.setData({
        clothingCategories: defaultCategories,
        loadingClothingCategories: false
      });
      return;
    }
    
    this.setData({ loadingClothingCategories: true });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothing_categories.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log('获取衣物分类列表:', res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          const categories = res.data.data || [];
          
          // 添加"全部"选项
          const allCategories = [
            { id: 'all', code: 'all', name: '全部', is_system: false }
          ].concat(categories);
          
          this.setData({
            clothingCategories: allCategories
          });
        } else {
          console.error('获取衣物分类列表失败:', res);
          // 使用默认分类
          this.setData({
            clothingCategories: defaultCategories
          });
        }
      },
      fail: (err) => {
        console.error('获取衣物分类请求失败:', err);
        // 使用默认分类
        this.setData({
          clothingCategories: defaultCategories
        });
      },
      complete: () => {
        this.setData({ loadingClothingCategories: false });
      }
    });
  }
}); 