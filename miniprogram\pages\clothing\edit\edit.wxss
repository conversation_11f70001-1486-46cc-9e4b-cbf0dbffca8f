/* 容器样式 */
.container {
  width: 100%;
  min-height: 100vh;
  background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
}

/* 主内容区 */
.main-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 80px; /* 为底部按钮留出空间 */
  padding-top: 10px; /* 调整为适合官方标题栏的间距 */
}

/* 上传区域 */
.upload-area {
  aspect-ratio: 3/4;
  background-color: #fff;
  border: 2px dashed #e0e0e0;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 20px;
  padding: 8px;
  box-sizing: border-box;
  overflow: hidden; /* 确保放大的图片不会溢出容器 */
  position: relative; /* 添加定位上下文 */
  z-index: 1; /* 确保在图片上层 */
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  object-fit: contain;
  transition: transform 0.3s ease; /* 添加旋转动画效果 */
}

.icon-camera::before {
  content: '\f030';
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  font-size: 40px;
  color: #ccc;
}

.upload-text {
  font-size: 16px;
  color: #666;
  margin-top: 15px;
}

.upload-desc {
  font-size: 13px;
  color: #999;
  margin-top: 5px;
}

/* 内容区块 */
.section {
  background-color: #fff;
  padding: 15px;
  margin: 15px 15px 0 15px;
  border-radius: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
}

/* 标签样式 */
.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  background-color: #f1f1f1;
  color: #333;
  display: inline-block;
}

.tag.selected {
  background-color: #000;
  color: white;
}

.tag-delete {
  margin-left: 5px;
  font-size: 14px;
}

/* 自定义标签输入 */
.custom-tag-input {
  background-color: #fff;
  padding: 15px;
  margin: 10px 15px 0 15px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
  border-radius: 12px;
}

.tag-input {
  flex: 1;
  height: 36px;
  border: 1px solid #ddd;
  border-radius: 18px;
  padding: 0 15px;
  font-size: 14px;
}

.tag-btn {
  height: 36px;
  padding: 0 15px;
  background-color: #000;
  color: #fff;
  border-radius: 18px;
  line-height: 36px;
  font-size: 14px;
  margin: 0;
}

.tag-btn.cancel {
  background-color: #f1f1f1;
  color: #333;
}

/* 输入组 */
.input-group {
  display: flex;
  flex-direction: column;
}

.input-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 0;
}

.input-item:last-child {
  border-bottom: none;
}

.input-item input {
  width: 100%;
  height: 24px;
  font-size: 15px;
}

/* 底部保存按钮 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top: 1px solid rgba(0,0,0,0.1);
  padding: 0 20px;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.save-btn {
  width: 100%;
  height: 44px;
  background-color: #000;
  color: #fff;
  border-radius: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.save-btn[disabled] {
  background-color: #ccc;
  color: #fff;
}

/* 衣橱选择器 */
.wardrobe-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  margin-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.wardrobe-label {
  font-size: 14px;
  color: #333;
}

.wardrobe-picker {
  display: flex;
  align-items: center;
  color: #333;
  font-size: 14px;
}

.picker-arrow {
  font-size: 12px;
  color: #999;
  margin-left: 5px;
}

/* 抠图开关样式 */
.segment-switch-container {
  border-radius: 0;
  margin: 0;
  box-shadow: none;
  padding: 15px;
  background-color: #fff;
}

.segment-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.switch-label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.segment-desc {
  font-size: 13px;
  color: #999;
  display: block;
  margin-top: 5px;
}

/* 旋转控制器样式 */
.rotation-control {
  padding: 15px;
  margin: 0;
  background-color: #fff;
  border-radius: 0;
  box-shadow: none;
}

/* 新增：水平模块导航栏 */
.modules-nav {
  display: flex;
  background-color: #fff;
  border-radius: 10px;
  margin: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.module-tab {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  position: relative;
  transition: all 0.3s;
}

.module-tab.active {
  background-color: #f0f0f0;
  font-weight: 500;
}

.module-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background-color: #000;
  border-radius: 3px;
}

.module-tab-text {
  font-size: 15px;
  color: #333;
}

/* 模块内容区域 */
.modules-content {
  margin: 0 15px;
  position: relative;
}

.module-content-item {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 15px;
  padding: 15px;
  opacity: 1;
  transition: opacity 0.3s, max-height 0.3s, transform 0.3s;
  max-height: 500px; /* 确保足够大以容纳内容 */
  transform: translateY(0);
  overflow: visible;
}

.module-content-item.hidden {
  opacity: 0;
  max-height: 0;
  padding: 0;
  margin: 0;
  transform: translateY(-10px);
  overflow: hidden;
  pointer-events: none; /* 隐藏时禁用交互 */
}

.module-content-item.visible {
  opacity: 1;
  z-index: 10;
}

/* 旋转控制器样式 */
.rotation-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 500;
}

.rotation-angle {
  color: #000;
  font-weight: bold;
}

.rotation-tip {
  display: block;
  font-size: 13px;
  color: #ff6b6b;
  margin-bottom: 12px;
  text-align: center;
}

.rotation-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.rotation-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
}

.rotation-icon {
  font-size: 24px;
  color: #000;
  line-height: 1;
}

.rotation-text {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.rotation-slider {
  flex: 1;
  margin: 0 10px;
}

.reset-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0;
  background-color: #f1f1f1;
  border-radius: 20px;
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.reset-btn:active {
  background-color: #e0e0e0;
}

/* 缩放控制样式 */
.scale-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0 10px;
  font-size: 16px;
  font-weight: 500;
}

.scale-value {
  color: #000;
  font-weight: bold;
}

.scale-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.scale-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
}

.scale-icon {
  font-size: 22px;
  color: #000;
  line-height: 1;
}

.scale-text {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.scale-slider {
  flex: 1;
  margin: 0 10px;
} 