/* pages/outfit_rating/result/result.wxss */
.container {
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #f8f8f8;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.rating-card {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
}

/* 照片区域样式优化 */
.photo-section {
  width: 100%;
  height: 600rpx;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
}

.outfit-photo {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.overall-score-badge {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
  z-index: 10;
}

.score-value {
  font-size: 40rpx;
  font-weight: bold;
  line-height: 1;
}

.score-label {
  font-size: 20rpx;
  margin-top: 5rpx;
}

/* 结果内容区域样式优化 */
.result-content {
  padding: 20rpx;
}

/* 模块化样式 */
.result-module {
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.section-header {
  margin-bottom: 15rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  height: 30rpx;
  width: 8rpx;
  background-color: #333;
  border-radius: 4rpx;
}

.section-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 评分网格样式优化 */
.score-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rpx;
}

.score-item {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 15rpx;
  text-align: center;
}

.score-item-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.score-item-label {
  font-size: 22rpx;
  color: #666;
  margin-top: 5rpx;
}

.action-buttons {
  display: flex;
  justify-content: center;
  padding: 0 20rpx;
  margin-top: auto;
  margin-bottom: 40rpx;
}

.action-button {
  width: 60%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0;
  padding: 0;
}

.share-button {
  background-color: #333;
  color: #fff;
}

.footer-tips {
  text-align: center;
  padding: 20rpx 0;
}

.tips-text {
  font-size: 22rpx;
  color: #999;
} 