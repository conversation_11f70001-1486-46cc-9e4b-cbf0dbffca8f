<?php
/**
 * 管理员获取穿搭日历详情API
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

// 检查是否有Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '需要授权头']);
    exit;
}

// 验证管理员token
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或过期的令牌']);
    exit;
}

// 检查是否提供了日历ID
$calendarId = isset($_GET['id']) ? intval($_GET['id']) : null;
$outfitId = isset($_GET['outfit_id']) ? intval($_GET['outfit_id']) : null;
$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : null;
$date = isset($_GET['date']) ? $_GET['date'] : null;

if (!$calendarId && (!$outfitId || !$userId || !$date)) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少必要参数，需要提供calendar_id或者outfit_id+user_id+date']);
    exit;
}

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

try {
    // 构建查询条件
    if ($calendarId) {
        $whereClause = "oc.id = :calendar_id";
        $params = [':calendar_id' => $calendarId];
    } else {
        $whereClause = "oc.outfit_id = :outfit_id AND oc.user_id = :user_id AND oc.calendar_date = :date";
        $params = [
            ':outfit_id' => $outfitId,
            ':user_id' => $userId,
            ':date' => $date
        ];
    }
    
    // 查询穿搭日历详情
    $sql = "
        SELECT 
            oc.id as calendar_id, 
            oc.calendar_date, 
            oc.user_id, 
            oc.created_at as calendar_created_at,
            oc.updated_at as calendar_updated_at,
            o.id as outfit_id, 
            o.name as outfit_name, 
            o.description, 
            o.thumbnail_url, 
            o.outfit_data,
            o.category_id, 
            c.name as category_name,
            u.nickname, 
            u.avatar_url,
            u.gender
        FROM outfit_calendar oc
        JOIN outfits o ON oc.outfit_id = o.id
        LEFT JOIN outfit_categories c ON o.category_id = c.id
        JOIN users u ON oc.user_id = u.id
        WHERE $whereClause
    ";
    
    $stmt = $conn->prepare($sql);
    
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    
    $stmt->execute();
    $calendar = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$calendar) {
        http_response_code(404);
        echo json_encode(['error' => true, 'msg' => '未找到穿搭日历记录']);
        exit;
    }
    
    // 解析穿搭数据
    if (!empty($calendar['outfit_data'])) {
        $calendar['outfit_data'] = json_decode($calendar['outfit_data'], true);
    } else {
        $calendar['outfit_data'] = null;
    }
    
    // 获取用户所有的穿搭日历记录（用于前端导航）
    $userCalendarSql = "
        SELECT oc.id as calendar_id, oc.calendar_date, o.name as outfit_name, o.thumbnail_url
        FROM outfit_calendar oc
        JOIN outfits o ON oc.outfit_id = o.id
        WHERE oc.user_id = :user_id
        ORDER BY oc.calendar_date DESC
        LIMIT 30
    ";
    
    $userCalendarStmt = $conn->prepare($userCalendarSql);
    $userCalendarStmt->bindValue(':user_id', $calendar['user_id']);
    $userCalendarStmt->execute();
    $userCalendar = $userCalendarStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 返回结果
    echo json_encode([
        'error' => false,
        'data' => [
            'calendar' => $calendar,
            'user_calendars' => $userCalendar
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '获取穿搭日历详情失败: ' . $e->getMessage()
    ]);
} 