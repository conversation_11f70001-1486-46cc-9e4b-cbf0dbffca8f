<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'cyyg');
define('DB_USER', 'root');
define('DB_PASS', '59b93187ca17e677');

// WeChat Mini Program configuration
define('WX_APPID', 'wxaf69ea21891dac29');
define('WX_SECRET', '82f729b85482d7629bedeaff5d1d59c2');
define('API_DOMAIN', 'https://cyyg.alidog.cn');

// 淘宝客配置
define('TAOBAO_APPKEY', '35071809');
define('TAOBAO_APPSECRET', '794d757e710f7a16f252cfeec8f8ffac');
define('TAOBAO_CALLBACK_URL', 'https://cyyg.alidog.cn');
define('TAOBAO_SDK_PATH', __DIR__ . '/../taobaosdk');
define('TAOBAO_MATERIAL_ID', '13367'); // 全部商品通用物料
define('TAOBAO_MEDIA_ID', '3297950500'); // 媒体ID
define('TAOBAO_PID', 'mm_16911270_3297950500_116053050499'); // 推广位PID
define('TAOBAO_PAGE_SIZE', 20); // 每页商品数量
define('TAOBAO_SORT_TYPE', '14'); // 排序类型: 14-降序,0-综合排序

// 淘宝物料分类列表
$TAOBAO_MATERIAL_CATEGORIES = [
    ['id' => '86589', 'name' => '天猫爆款'],
    ['id' => '86623', 'name' => '品牌女装'],
    ['id' => '86591', 'name' => '直营补贴'],
    ['id' => '86595', 'name' => '品牌精选'],
    ['id' => '86594', 'name' => '天天特卖'],
    ['id' => '86617', 'name' => '内衣推荐'],
    ['id' => '86616', 'name' => '母婴用品'],
    ['id' => '86620', 'name' => '鞋包配饰'],
    ['id' => '86619', 'name' => '美妆个护']
];
// 全局函数：获取淘宝物料分类列表
function getTaobaoMaterialCategories() {
    global $TAOBAO_MATERIAL_CATEGORIES;
    return $TAOBAO_MATERIAL_CATEGORIES;
}

// Gemini API proxy service configuration
define('GEMINI_API_PROXY_URL', 'https://www.furrywoo.com/gemini/api.php');

// 和风天气API配置
define('WEATHER_API_KEY', '99b4a915afcc4ac2ba654f1a436cfb1d'); // API密钥，确保是最新的
define('WEATHER_API_HOST', 'kq2k5mg4v2.re.qweatherapi.com'); // 开发环境API Host
define('WEATHER_API_HOST_PROD', 'kq2k5mg4v2.re.qweatherapi.com'); // 正式环境API Host（需要替换为您的正式环境域名）
define('WEATHER_API_USE_PROD', true); // 是否使用正式环境API，设置为true时使用WEATHER_API_HOST_PROD
define('WEATHER_DEFAULT_LOCATION', '101210101'); // 默认杭州，原值为101010100（北京）
define('WEATHER_API_URL', API_DOMAIN . '/login_backend/proxy_weather_api.php'); // 使用自己的代理URL(旧版，保留向后兼容)
define('WEATHER_API_DOMAIN', 'https://api.qweather.com'); // 商业版API域名(旧版)
define('WEATHER_API_FREE_DOMAIN', 'https://devapi.qweather.com'); // 免费版API域名(旧版)
define('WEATHER_API_PATH', '/v7/weather/now'); // API路径
define('WEATHER_GEO_API_PATH', '/geo/v2/city/lookup'); // 地理编码API路径
define('WEATHER_GEO_API_HOST', 'kq2k5mg4v2.re.qweatherapi.com'); // 默认地理编码API Host
define('WEATHER_API_AUTH_TYPE', 'key'); // key: URL参数方式，bearer: Bearer Token方式
// 新增城市查询API配置
define('WEATHER_CITY_API_URL', API_DOMAIN . '/login_backend/get_city_by_location.php'); // 城市查询API URL
define('WEATHER_CITY_SEARCH_LANG', 'zh'); // 城市查询语言，默认中文
define('WEATHER_CITY_MAX_RESULTS', 10); // 城市查询最大结果数
// 天气图标配置
define('WEATHER_ICON_URL', 'https://a.hecdn.net/img/common/icon/202007/'); // 天气图标URL路径 (旧版，可能受限制)
define('WEATHER_ICON_CSS_URL', 'https://cdn.jsdelivr.net/npm/qweather-icons@1.6.0/font/qweather-icons.css'); // 天气图标CSS (新版，推荐)
define('WEATHER_ICON_USE_FONT', true); // 是否使用字体图标而不是图片URL

// 和风天气API备选配置（如果默认配置失败可尝试使用）
define('WEATHER_API_KEY_ALTERNATIVE', '7075eb1f12c545e58f91ab888c208e96'); // 备选API密钥
define('WEATHER_API_CUSTOM_DOMAIN', API_DOMAIN); // 自定义域名，使用自己的API代理

// 管理员相关配置
define('ADMIN_SECRET_KEY', 'cyymj_admin_M6aVh9bQfEzL7Rk8PwS3'); // 用于管理员Token签名的密钥

// 微信客服消息验证TOKEN
define('WX_TOKEN', 'aPxncbLpjTy70sByzgu51dTzPbwQ8fvA'); // 用于验证微信服务器的请求
// 微信消息加密密钥
define('WX_ENCODING_AES_KEY', 'TA97gmjNK3wCH82xXvuWI7LOPxMmxJz6DOXEKK8tsxB'); // 消息加解密密钥，43位

// 试衣次数限制模式配置
define('TRY_ON_COUNT_MODE', 'dual'); // 'daily' - 每日一次并在8点重置(默认), 'database' - 使用数据库中的try_on_count字段, 'dual' - 使用双层计数系统(免费次数+付费次数)
define('DEFAULT_FREE_TRY_ON_COUNT', 1); // 每日免费试衣次数的默认值

// 广告观看限制
define('AD_WATCH_DAILY_LIMIT', 1); // 每日通过广告获取免费试衣次数的上限

// 阿里云API配置
define('ALIYUN_ACCESS_KEY_ID', 'LTAI5tBWHfXfp1pSxUFZ1GGB');  // 替换为实际的阿里云AccessKey ID
define('ALIYUN_ACCESS_KEY_SECRET', '******************************');  // 替换为实际的阿里云AccessKey Secret
define('ALIYUN_OUTFIT_API_KEY', 'sk-6dcb9653ba6d48d9905223257fff4c3a');  // 阿里云OutfitAnyone API密钥

// 抠图API配置
define('SEGMENT_API_TYPE', 'common'); // 'cloth'使用服饰分割, 'common'使用通用分割
define('SEGMENT_COMMON_RETURN_FORM', 'crop'); // 通用分割返回格式: crop, mask, whiteBK

// 试衣API配置
define('TRY_ON_API_TYPE', 'aliyun'); // 'aliyun'使用阿里云API, 'shiyi'使用石衣API
define('ALIYUN_TRY_ON_MODEL', 'aitryon-plus'); // 阿里云试衣模型: 'aitryon'使用基础版, 'aitryon-plus'使用Plus版
define('SHIYI_API_URL', 'https://www.furrywoo.com/geminicdai/shiyi0603.php'); // 石衣API地址
define('SHIYI_API_KEY', WX_ENCODING_AES_KEY); // 石衣API鉴权密钥

// 阿里云OSS配置
define('ALIYUN_OSS_ENDPOINT', 'oss-cn-shanghai.aliyuncs.com');  // OSS服务的Endpoint
define('ALIYUN_OSS_BUCKET', 'cyymj');  // OSS Bucket名称
define('ALIYUN_OSS_BUCKET_DOMAIN', 'cyymj.oss-cn-shanghai.aliyuncs.com');  // Bucket域名

// CDN配置
define('ALIYUN_CDN_DOMAIN', 'images.alidog.cn');  // CDN域名
define('USE_CDN', true);  // 控制是否使用CDN（默认关闭，待测试成功后启用）

// OSS存储路径前缀
define('OSS_PATH_CLOTHES', 'clothes/');  // 衣物图片存储路径
define('OSS_PATH_PHOTOS', 'photos/');    // 用户照片存储路径
define('OSS_PATH_TRY_ON', 'try_on/');    // 试衣结果图片存储路径

// 微信支付配置
define('WX_MCH_ID', '1681102299');  // 微信支付商户号
define('WX_PAY_API_KEY', 'J1E8nfsm6A4oTCJ42Gr5J3aWaYXi2BQw');  // 微信支付API密钥
define('WX_PAY_SERIAL_NO', '1BDE678D8A901FDF12C04794DCCADFBD19B22779');  // 证书序列号

// 使用相对路径，确保在所有环境中都能找到证书文件
define('WX_PAY_PRIVATE_KEY_PATH', __DIR__ . '/wechatcert/apiclient_key.pem');  // 私钥路径
define('WX_PAY_CERT_PATH', __DIR__ . '/wechatcert/wechatpay_5A7488520D47AEBF5D455407AE11A02CDAAD8E21.pem');  // 微信支付平台证书路径

// 确保日志目录存在
$logDir = __DIR__ . '/logs';
if (!file_exists($logDir)) {
    mkdir($logDir, 0755, true);
}

define('WX_PAY_NOTIFY_URL', API_DOMAIN . '/login_backend/wx_pay_notify.php');  // 支付回调通知地址
define('WX_PAY_REFUND_NOTIFY_URL', API_DOMAIN . '/login_backend/wx_pay_refund_notify.php');  // 退款回调通知地址

// Error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

/**
 * 获取微信配置数组
 * 为WXBizMsgCrypt类提供的配置数组
 */
function getWxConfig() {
    return [
        'token' => WX_TOKEN,
        'encodingAesKey' => WX_ENCODING_AES_KEY,
        'appId' => WX_APPID
    ];
} 