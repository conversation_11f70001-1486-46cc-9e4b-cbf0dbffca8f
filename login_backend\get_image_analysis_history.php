<?php
/**
 * 获取个人形象分析历史API
 * 获取用户的所有形象分析记录历史
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => 'Method Not Allowed']);
    exit;
}

// 检查是否有Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => 'Authorization header is required']);
    exit;
}

// 验证token
$token = $_SERVER['HTTP_AUTHORIZATION'];
$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => 'Invalid or expired token']);
    exit;
}

// 获取用户ID
$userId = $payload['sub'];

// 获取分页参数
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = isset($_GET['limit']) ? min(50, max(1, intval($_GET['limit']))) : 10;
$offset = ($page - 1) * $limit;

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

// 查询总记录数
$countStmt = $conn->prepare("
    SELECT COUNT(*) as total FROM user_image_analysis 
    WHERE user_id = :user_id
");
$countStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
$countStmt->execute();
$totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

// 查询分析记录，按创建时间倒序排列
$stmt = $conn->prepare("
    SELECT 
        id, created_at, updated_at, status, payment_status, amount, 
        order_id, analysis_time 
    FROM user_image_analysis 
    WHERE user_id = :user_id 
    ORDER BY created_at DESC 
    LIMIT :limit OFFSET :offset
");
$stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
$stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
$stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();
$analyses = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 计算总页数
$totalPages = ceil($totalCount / $limit);

// 处理结果
$results = [];
foreach ($analyses as $analysis) {
    // 只返回基本信息，不包含详细分析结果
    $results[] = [
        'id' => $analysis['id'],
        'status' => $analysis['status'],
        'payment_status' => $analysis['payment_status'],
        'amount' => $analysis['amount'],
        'order_id' => $analysis['order_id'],
        'created_at' => $analysis['created_at'],
        'updated_at' => $analysis['updated_at'],
        'analysis_time' => $analysis['analysis_time']
    ];
}

// 返回结果
echo json_encode([
    'error' => false,
    'data' => [
        'total' => $totalCount,
        'page' => $page,
        'limit' => $limit,
        'total_pages' => $totalPages,
        'results' => $results
    ]
]); 