const app = getApp();
const API_BASE_URL = 'https://cyyg.alidog.cn/login_backend'; // 替换为你的API地址

Page({
  data: {
    product: null,
    currentImageIndex: 0,
    loading: true, // 默认显示加载状态
    error: false,
    errorMsg: '',
    requestedProductId: '', // 存储请求的商品ID
    cachedItemUrl: '', // 存储列表页传递的原始商品链接
    cachedCouponUrl: '' // 存储列表页传递的优惠券链接
  },

  onLoad: function(options) {
    console.log('淘宝商品详情页加载，参数:', options);
    
    try {
      let productId = '';
      
      // 优先使用URL参数中的ID
      if (options && options.id) {
        productId = options.id;
        this.setData({ requestedProductId: productId });
        console.log('从URL获取商品ID:', productId);
        
        // 直接使用URL参数中的ID加载商品详情
        this.loadProductDetail(productId);
      } else {
        // URL中没有ID参数，尝试使用缓存
        const cachedProduct = wx.getStorageSync('currentProduct');
        
        if (cachedProduct && cachedProduct.id) {
          console.log('没有URL ID参数，使用缓存数据');
          
          // 提取实际商品ID并保存
          const cachedId = typeof cachedProduct.id === 'object' && cachedProduct.id[0] 
            ? cachedProduct.id[0] : cachedProduct.id;
            
          // 保存列表页传递的原始链接，用于后续淘口令生成
          if (cachedProduct.item_url) {
            this.setData({ cachedItemUrl: cachedProduct.item_url });
            console.log('保存列表页的商品链接:', cachedProduct.item_url);
          }
          if (cachedProduct.coupon_click_url) {
            this.setData({ cachedCouponUrl: cachedProduct.coupon_click_url });
            console.log('保存列表页的优惠券链接:', cachedProduct.coupon_click_url);
          }
            
          this.setData({ 
            product: cachedProduct,
            loading: false,
            requestedProductId: cachedId
          });
        } else {
          // 既没有URL参数又没有缓存，显示错误
          this.setData({
            loading: false,
            error: true,
            errorMsg: '商品信息不存在'
          });
          
          console.error('加载商品详情失败: 缺少ID参数且无缓存');
          wx.showToast({
            title: '商品信息不存在',
            icon: 'none'
          });
          
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      }
    } catch (error) {
      console.error('初始化商品详情页出错:', error);
      this.setData({
        loading: false,
        error: true,
        errorMsg: '加载异常，请稍后重试'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },
  
  // 获取商品详情
  loadProductDetail: function(id) {
    if (!id) {
      console.error('加载商品详情失败: ID参数为空');
      this.setData({
        loading: false,
        error: true,
        errorMsg: '商品ID不存在'
      });
      return;
    }
    
    // 保存当前已缓存的原始链接，避免被API返回的链接覆盖
    const cachedItemUrl = this.data.cachedItemUrl;
    const cachedCouponUrl = this.data.cachedCouponUrl;
    
    this.setData({ loading: true });
    console.log('开始加载商品ID详情:', id);
    
    // 直接使用商品ID调用API获取详情
    wx.request({
      url: `${API_BASE_URL}/get_taobao_products.php`,
      method: 'GET',
      data: { 
        item_id: id
      },
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log('获取商品详情响应:', res);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data && res.data.data.length > 0) {
          // 获取API返回的商品数据
          const product = res.data.data[0];
          console.log('API返回的商品:', product);
          
          if (product && product.id) {
            // 处理API返回的商品数据
            const normalizedProduct = this.normalizeProductData(product, cachedItemUrl, cachedCouponUrl);
            
            this.setData({
              product: normalizedProduct,
              loading: false
            });
            
            // 更新缓存
            wx.setStorageSync('currentProduct', normalizedProduct);
          } else {
            this.handleProductError('商品数据格式错误');
          }
        } else {
          this.handleProductError(res.data?.msg || '获取商品详情失败');
        }
      },
      fail: (err) => {
        console.error('请求商品详情API失败:', err);
        this.handleProductError('网络请求失败');
      }
    });
  },
  
  // 查找匹配的商品
  findMatchingProduct: function(products, targetId) {
    if (!products || !products.length || !targetId) return null;
    
    // 日志输出商品ID列表，便于调试
    const productIds = products.map(p => {
      const id = p.id && p.id[0] ? p.id[0] : p.id;
      return id;
    });
    console.log('API返回的商品ID列表:', productIds);
    console.log('目标商品ID:', targetId);
    
    // 查找完全匹配的商品
    for (let i = 0; i < products.length; i++) {
      const product = products[i];
      const productId = product.id && product.id[0] ? product.id[0] : product.id;
      
      if (productId && productId === targetId) {
        console.log('找到完全匹配的商品:', i);
        return product;
      }
    }
    
    console.log('未找到完全匹配的商品');
    return null;
  },
  
  // 统一错误处理
  handleProductError: function(errorMsg) {
    this.setData({
      loading: false,
      error: true,
      errorMsg: errorMsg
    });
    
    wx.showToast({
      title: errorMsg,
      icon: 'none',
      duration: 2000
    });
  },
  
  // 辅助函数：规范化商品数据，确保所有字段格式一致
  normalizeProductData: function(product, cachedItemUrl, cachedCouponUrl) {
    if (!product) return null;
    
    // 工具函数：确保图片URL格式正确
    const normalizeImageUrl = (url) => {
      if (!url) return '';
      if (typeof url === 'object') {
        if (url[0]) return this.ensureHttpPrefix(url[0]);
        return '';
      }
      return this.ensureHttpPrefix(url);
    };
    
    // 工具函数：提取可能嵌套的值
    const extractValue = (field) => {
      if (field === undefined || field === null) return '';
      if (typeof field === 'object') {
        if (Array.isArray(field) && field.length > 0) return field[0];
        if (field[0] !== undefined) return field[0];
        const values = Object.values(field);
        if (values.length > 0) return values[0];
      }
      return field;
    };
    
    // 规范化价格值
    const normalizePrice = (price) => {
      if (!price) return 0;
      const numPrice = Number(price);
      return isNaN(numPrice) ? 0 : numPrice;
    };
    
    // 使用缓存的链接（如果有）
    const itemUrl = cachedItemUrl || this.ensureHttpPrefix(product.item_url || '');
    const couponUrl = cachedCouponUrl || this.ensureHttpPrefix(product.coupon_click_url || '');
    
    // 创建标准化的商品对象
    return {
      id: extractValue(product.id) || '',
      title: extractValue(product.title) || '商品标题',
      image_url: normalizeImageUrl(product.image_url) || '',
      original_price: normalizePrice(product.original_price || product.zk_final_price),
      final_price: normalizePrice(product.final_price || product.zk_final_price),
      coupon_amount: normalizePrice(product.coupon_amount),
      coupon_click_url: couponUrl,
      item_url: itemUrl,
      shop_title: extractValue(product.shop_title) || '淘宝店铺',
      volume: extractValue(product.volume) || "0",
      small_images: this.normalizeSmallImages(product.small_images),
      // 添加原始链接标记，便于调试
      _has_original_link: !!cachedItemUrl
    };
  },
  
  // 辅助函数：处理small_images字段的各种格式
  normalizeSmallImages: function(smallImages) {
    const result = [];
    
    if (!smallImages) return result;
    
    console.log('处理small_images，原始数据类型:', typeof smallImages);
    
    try {
      if (Array.isArray(smallImages)) {
        // 如果已经是数组，直接使用
        smallImages.forEach(url => {
          if (url && typeof url === 'string') {
            result.push(this.ensureHttpPrefix(url));
          }
        });
      } else if (typeof smallImages === 'object') {
        // 如果是对象，可能是{string: [url1, url2, ...]}格式
        if (smallImages.string && Array.isArray(smallImages.string)) {
          smallImages.string.forEach(url => {
            if (url && typeof url === 'string') {
              result.push(this.ensureHttpPrefix(url));
            }
          });
        } else {
          // 其他对象格式，尝试提取所有值
          Object.values(smallImages).forEach(item => {
            if (typeof item === 'string') {
              result.push(this.ensureHttpPrefix(item));
            } else if (Array.isArray(item)) {
              item.forEach(subItem => {
                if (typeof subItem === 'string') {
                  result.push(this.ensureHttpPrefix(subItem));
                }
              });
            }
          });
        }
      }
    } catch (error) {
      console.error('处理small_images时出错:', error);
    }
    
    console.log('处理后的small_images数组:', result, '长度:', result.length);
    return result;
  },
  
  // 辅助函数：确保URL有https:前缀
  ensureHttpPrefix: function(url) {
    if (!url) return '';
    if (typeof url !== 'string') return '';
    
    if (url.startsWith('//')) {
      return 'https:' + url;
    } else if (!url.startsWith('http')) {
      return 'https://' + url;
    }
    return url;
  },
  
  // 辅助函数：提取可能嵌套的值 (已被normalizeProductData取代，但为了向后兼容保留)
  extractNestedValue: function(field) {
    if (!field) return '';
    
    if (typeof field === 'object') {
      // 如果是对象，尝试获取第一个值
      if (field[0]) return field[0];
      
      // 或者尝试获取对象的第一个值
      const values = Object.values(field);
      if (values.length > 0) return values[0];
    }
    
    // 如果不是对象或无法获取嵌套值，返回原值
    return field;
  },
  
  // 滑动图片
  onSwiperChange: function(e) {
    this.setData({
      currentImageIndex: e.detail.current
    });
  },
  
  // 查看大图
  previewImage: function(e) {
    const { product } = this.data;
    const images = [product.image_url];
    
    if (product.small_images && product.small_images.length > 0) {
      images.push(...product.small_images);
    }
    
    wx.previewImage({
      current: images[this.data.currentImageIndex],
      urls: images
    });
  },
  
  // 复制淘口令 - 修改使用列表页的逻辑
  copyPromotionUrl: function() {
    const { product } = this.data;
    
    if (!product) {
      wx.showToast({
        title: '商品信息不存在',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '获取淘口令中...',
    });
    
    // 确保获取正确的 itemId
    let actualItemId = typeof product.id === 'object' && product.id[0] ? product.id[0] : product.id;
    
    // 以下URL优先级：优惠券链接 > 商品链接 > 生成的标准链接
    
    // 获取可用的商品链接
    let itemUrl = '';
    
    // 1. 优先使用优惠券链接
    if (product.coupon_click_url) {
      itemUrl = product.coupon_click_url;
      if (!itemUrl.startsWith('http')) {
        itemUrl = 'https:' + itemUrl;
      }
      console.log('使用优惠券链接:', itemUrl);
    }
    // 2. 其次使用商品链接
    else if (product.item_url) {
      // 检查是否是有效的淘宝链接格式
      itemUrl = product.item_url;
      if (!itemUrl.startsWith('http')) {
        itemUrl = 'https:' + itemUrl;
      }
      
      // 检查是否是s.click.taobao.com格式 - 这是最理想的淘口令源
      if (itemUrl.includes('s.click.taobao.com') || itemUrl.includes('item.taobao.com')) {
        console.log('使用标准淘宝商品链接:', itemUrl);
      }
      // 检测到uland格式链接，通常不能直接转淘口令
      else if (itemUrl.includes('uland.taobao.com')) {
        console.log('检测到uland格式链接，可能无法直接转换淘口令');
      }
    }
    
    // 尝试提取数字ID用于备用
    let numericId = null;
    if (product.item_url) {
      const idMatch = product.item_url.match(/[\?&]id=(\d+)/);
      if (idMatch && idMatch[1]) {
        console.log('从URL提取的数字商品ID:', idMatch[1]);
        numericId = idMatch[1];
      }
    }
    
    // 如果有有效链接，尝试转换为淘口令（即便ID非数字格式）
    if (itemUrl) {
      console.log('尝试使用链接生成淘口令:', itemUrl);
      // 使用与列表页相同的API进行转换
      this.convertLinkToTpwdByAPI(itemUrl, actualItemId);
      return;
    }
    
    // 如果有数字ID，但没有有效链接，尝试构建标准链接
    if (numericId) {
      const standardUrl = `https://item.taobao.com/item.htm?id=${numericId}`;
      console.log('使用提取的数字ID构建标准链接:', standardUrl);
      this.convertLinkToTpwdByAPI(standardUrl, numericId);
      return;
    }
    
    // 最后，如果没有有效链接也没有数字ID，则使用备用方法
    console.log('没有有效链接和数字ID，使用备用方法');
    this.getOriginalLink(actualItemId);
  },
  
  // 新增：使用列表页相同的API直接转换淘口令
  convertLinkToTpwdByAPI: function(url, itemId) {
    console.log('直接调用转换API生成淘口令:', {
      url: url,
      itemId: itemId,
      isOriginalLink: url.includes('s.click.taobao.com'),
      isCached: this.data.product && this.data.product._has_original_link
    });
    
    // 直接使用专用的淘口令转换API
    wx.request({
      url: `${API_BASE_URL}/convert_to_tpwd.php`,
      method: 'POST',
      data: {
        url: url,
        item_id: itemId,
        text: '好物推荐'  // 淘口令文案
      },
      header: {
        'Content-Type': 'application/json',
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        console.log('淘口令转换API响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data && res.data.data.model) {
          // 获取淘口令
          const tpwd = res.data.data.model;
          
          if (tpwd && (tpwd.includes('￥') || tpwd.includes('¥'))) {
            console.log('成功获取到官方淘口令:', tpwd.substring(0, 20) + '...');
            this.copyUrlToClipboard(tpwd, true);
          } else {
            console.log('未获取到淘口令，使用备用方法');
            this.getOriginalLink(itemId);
          }
        } else {
          console.error('转换淘口令失败:', res.data?.msg || '未知错误');
          this.getOriginalLink(itemId);
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('转换淘口令请求失败:', err);
        this.getOriginalLink(itemId);
      }
    });
  },
  
  // 将链接转换为淘口令 (保留旧方法作为备用)
  convertLinkToTpwd: function(url, itemId) {
    console.log('开始转换链接为淘口令:', url, '商品ID:', itemId);
    
    // 检查ID是否为非数字ID（包含字母或特殊字符）
    const isNonNumericId = itemId && !/^\d+$/.test(itemId);
    if (isNonNumericId) {
      console.log('检测到非数字ID，跳过转换直接使用备用方法');
      this.getOriginalLink(itemId);
      return;
    }
    
    // 如果有数字ID并且缺少链接格式，直接使用备用方法
    if (!url || url.includes('uland.taobao.com/item/edetail')) {
      console.log('检测到不规范的链接格式，使用备用方法');
      this.getOriginalLink(itemId);
      return;
    }
    
    // 直接使用专用的淘口令转换API
    wx.request({
      url: `${API_BASE_URL}/convert_to_tpwd.php`,
      method: 'POST',
      data: {
        url: url,
        item_id: itemId,
        text: '好物推荐'  // 淘口令文案
      },
      header: {
        'Content-Type': 'application/json',
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        console.log('淘口令转换响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          // 获取淘口令
          const tpwd = res.data.data.model;
          
          if (tpwd && (tpwd.includes('￥') || tpwd.includes('¥'))) {
            console.log('成功获取到淘口令');
            this.copyUrlToClipboard(tpwd, true);
          } else {
            console.log('未获取到淘口令，使用备用方法');
            // 备用: 使用原始链接
            this.getOriginalLink(itemId);
          }
        } else {
          // 如果API调用失败或未返回淘口令，使用备用方法
          console.error('转换淘口令失败:', res.data);
          this.getOriginalLink(itemId);
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('转换淘口令请求失败:', err);
        // 请求失败，尝试备用方法
        this.getOriginalLink(itemId);
      }
    });
  },
  
  // 获取普通链接（备用方法）
  getOriginalLink: function(itemId, attemptCount = 0) {
    // 避免无限循环尝试
    if (attemptCount > 2) {
      wx.hideLoading();
      wx.showToast({
        title: '无法生成淘口令，请稍后再试',
        icon: 'none',
        duration: 2500
      });
      return;
    }
    
    wx.showLoading({
      title: '使用备用方式获取...',
    });
    
    // 确保itemId是有效的
    if (!itemId) {
      const { product } = this.data;
      if (product && product.id) {
        itemId = typeof product.id === 'object' && product.id[0] ? product.id[0] : product.id;
      }
    }
    
    console.log('使用备用方法获取链接，商品ID:', itemId, '尝试次数:', attemptCount);
    
    // 检查是否为非数字ID（包含字母或特殊字符）
    const isNonNumericId = itemId && !/^\d+$/.test(itemId);
    if (isNonNumericId) {
      console.log('检测到非数字ID，使用特殊分享模式');
    }
    
    wx.request({
      url: `${API_BASE_URL}/get_taobao_link.php`,
      method: 'GET',
      data: {
        item_id: itemId,
        need_tpwd: 1,  // 请求淘口令
        share_mode: isNonNumericId ? 'no_convert' : 'default' // 告知API这是非数字ID
      },
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        console.log('备用API响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          // 优先使用淘口令，其次使用普通链接
          const link = res.data.data.tpwd || res.data.data.model || res.data.data.promotion_url;
          if (link) {
            // 确定是否为淘口令
            const isTpwd = link.includes('￥') || link.includes('¥');
            const isFake = res.data.data.is_fake === true;
            
            console.log('获取到链接类型:', isTpwd ? '淘口令' : '普通链接', isFake ? '(模拟)' : '');
            
            let customToastMsg = null;
            if (isTpwd && isFake && isNonNumericId) {
              customToastMsg = '已复制链接，非标准ID无法生成官方淘口令';
            }
            
            this.copyUrlToClipboard(link, isTpwd, customToastMsg);
          } else {
            wx.showToast({
              title: '获取商品链接失败',
              icon: 'none',
              duration: 2000
            });
          }
        } else {
          wx.showToast({
            title: '获取商品链接失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('备用API请求失败:', err);
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 复制URL到剪贴板
  copyUrlToClipboard: function(url, isTpwd = false, customToastMsg = null) {
    wx.setClipboardData({
      data: url,
      success: () => {
        let toastMsg = '链接已复制，请打开淘宝APP';
        
        // 如果是淘口令，使用特定的提示
        if (isTpwd || url.includes('￥') || url.includes('¥')) {
          toastMsg = '淘口令已复制，请打开淘宝APP粘贴';
        }
        
        if (customToastMsg) {
          toastMsg = customToastMsg;
        }
        
        wx.showToast({
          title: toastMsg,
          icon: 'none',
          duration: 2500
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制链接失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 返回列表
  goBack: function() {
    wx.navigateBack();
  },
  
  // 重试加载
  retry: function() {
    const id = this.data.product?.id;
    if (id) {
      this.loadProductDetail(id);
    } else {
      wx.navigateBack();
    }
  },
  
  // 分享
  onShareAppMessage: function() {
    const { product } = this.data;
    return {
      title: product ? product.title : '优惠好物分享',
      path: `/pages/taobao/detail/index?id=${product ? product.id : ''}`,
      imageUrl: product ? product.image_url : ''
    };
  }
}); 