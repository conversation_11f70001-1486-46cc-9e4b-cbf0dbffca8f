-- Create database if not exists
CREATE DATABASE IF NOT EXISTS cyyg;

-- Use the database
USE cyyg;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    openid VARCHAR(100) NOT NULL UNIQUE,
    unionid VARCHAR(100) NULL,
    session_key VARCHAR(100) NOT NULL,
    nickname VARCHAR(100) NULL,
    avatar_url TEXT NULL,
    gender TINYINT NULL COMMENT '0: unknown, 1: male, 2: female',
    created_at DATETIME NOT NULL,
    updated_at DATETIME NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create index on openid for faster lookups
CREATE INDEX idx_openid ON users(openid);

-- Create index on unionid for faster lookups (if present)
CREATE INDEX idx_unionid ON users(unionid);

-- Create clothes table
CREATE TABLE IF NOT EXISTS clothes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL COMMENT 'tops, pants, skirts, coats, shoes, bags, accessories',
    image_url TEXT NOT NULL,
    tags VARCHAR(255) NULL COMMENT 'Comma-separated list of tags',
    description TEXT NULL COMMENT 'JSON object with additional properties like color, brand, price',
    created_at DATETIME NOT NULL,
    updated_at DATETIME NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create index on user_id for faster lookups
CREATE INDEX idx_user_id ON clothes(user_id);

-- Create index on category for filtering
CREATE INDEX idx_category ON clothes(category); 