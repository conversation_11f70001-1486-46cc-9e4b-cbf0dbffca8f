<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>穿搭评分历史 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        .search-box {
            display: flex;
            margin-bottom: 20px;
        }
        
        .search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px 0 0 4px;
            outline: none;
        }
        
        .search-btn {
            padding: 8px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
        }
        
        .search-btn:hover {
            background-color: #40a9ff;
        }
        
        .rating-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .rating-table th,
        .rating-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .rating-table th {
            background-color: #fafafa;
            font-weight: 500;
        }
        
        .rating-table tr:hover {
            background-color: #f5f5f5;
        }
        
        .thumbnail {
            width: 60px;
            height: 60px;
            border-radius: 4px;
            object-fit: cover;
            background-color: #f5f5f5;
        }
        
        .action-btn {
            padding: 4px 10px;
            border-radius: 4px;
            border: 1px solid;
            background-color: transparent;
            cursor: pointer;
            margin-right: 5px;
            font-size: 13px;
        }
        
        .view-btn {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .view-btn:hover {
            background-color: #e6f7ff;
        }
        
        .pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        .pagination button {
            padding: 5px 10px;
            margin: 0 5px;
            border: 1px solid #d9d9d9;
            background-color: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .pagination button:hover:not(:disabled) {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .pagination button:disabled {
            color: #d9d9d9;
            cursor: not-allowed;
        }
        
        .pagination .current-page {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .no-data {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        
        .loading-indicator {
            text-align: center;
            padding: 20px;
            color: #666;
            display: none;
        }
        
        .error-message {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #f5222d;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            overflow-y: auto;
        }
        
        .modal-content {
            background-color: #fff;
            margin: 50px auto;
            width: 80%;
            max-width: 800px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            position: relative;
        }
        
        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 500;
            margin: 0;
        }
        
        .modal-close {
            font-size: 22px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
            background: none;
            border: none;
            padding: 0;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #f0f0f0;
            text-align: right;
        }

        /* 详情页样式 */
        .rating-detail {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* 照片样式 - 修改为顶部显示 */
        .rating-photo {
            text-align: center;
            margin-bottom: 10px;
            border-radius: 8px;
            overflow: hidden;
            background-color: #f5f5f5;
        }

        .rating-photo img {
            max-width: 100%;
            max-height: 500px;
            object-fit: contain;
            border-radius: 0;
            border: none;
        }

        .rating-score {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
            color: #1890ff;
            background-color: #f0f7ff;
            padding: 10px;
            border-radius: 8px;
        }

        /* 统一所有模块的样式 */
        .rating-comments {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .rating-comments h4 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #333;
            font-size: 16px;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }

        /* 评分明细样式 */
        .rating-breakdown {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-top: 10px;
        }

        .rating-category {
            flex: 1;
            min-width: 140px;
            padding: 10px;
            background-color: #fff;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #eee;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .rating-category-name {
            font-weight: 500;
            margin-bottom: 5px;
            color: #666;
            font-size: 14px;
        }

        .rating-category-value {
            font-size: 18px;
            font-weight: bold;
            color: #1890ff;
        }

        .user-info {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .user-info .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
            border: 1px solid #eee;
        }

        .user-info .name {
            font-weight: 500;
            font-size: 16px;
        }

        .user-info .date {
            font-size: 14px;
            color: #999;
            margin-left: auto;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>穿搭评分历史数据</h2>
                <div class="user-info">
                    <span id="userName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div id="ratingError" class="error-message"></div>
            <div id="ratingLoading" class="loading-indicator">正在加载评分历史数据...</div>
            
            <div class="card">
                <div class="search-box">
                    <input type="text" id="searchInput" class="search-input" placeholder="搜索用户ID或昵称..." />
                    <button id="searchBtn" class="search-btn">搜索</button>
                </div>
                
                <div class="rating-content">
                    <table class="rating-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户昵称</th>
                                <th>用户ID</th>
                                <th>照片</th>
                                <th>评分</th>
                                <th>评分时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="ratingHistoryTable">
                            <tr>
                                <td colspan="7" class="no-data">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="pagination" id="ratingPagination">
                        <button id="ratingPrevBtn" disabled>&lt; 上一页</button>
                        <span id="ratingPageInfo">第 0/0 页</span>
                        <button id="ratingNextBtn" disabled>下一页 &gt;</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 评分详情模态框 -->
    <div id="ratingDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">穿搭评分详情</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="ratingDetailModalBody">
                <!-- 评分详情内容将动态插入这里 -->
            </div>
            <div class="modal-footer">
                <button class="action-btn view-btn modal-close">关闭</button>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/image_viewer.js"></script>
    <script src="js/outfit_rating_history.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为outfit_rating_history
            Sidebar.init('outfit_rating_history');
            
            // 显示用户信息
            const userName = document.getElementById('userName');
            const user = Auth.getCurrentUser();
            if (user) {
                userName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化穿搭评分历史模块
            OutfitRatingHistory.init();
        });
    </script>
</body>
</html> 