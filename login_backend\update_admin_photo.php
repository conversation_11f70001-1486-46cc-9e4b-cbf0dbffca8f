<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证管理员身份
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取POST数据
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

// 检查必要参数
if (!isset($input['user_id']) || empty($input['user_id']) || 
    !isset($input['image_url']) || empty($input['image_url'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少必要参数: user_id, image_url']);
    exit();
}

// 提取数据
$photoId = isset($input['id']) ? (int)$input['id'] : null;
$userId = (int)$input['user_id'];
$imageUrl = trim($input['image_url']);
$type = isset($input['type']) ? trim($input['type']) : 'other';
$description = isset($input['description']) ? trim($input['description']) : '';

// 验证类型
$allowedTypes = ['full', 'half', 'other'];
if (!in_array($type, $allowedTypes)) {
    $type = 'other';
}

// 验证图片URL格式
if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '无效的图片URL']);
    exit();
}

// 验证用户存在
$db = new Database();
$conn = $db->getConnection();

$userQuery = "SELECT id FROM users WHERE id = :user_id";
$userStmt = $conn->prepare($userQuery);
$userStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
$userStmt->execute();

if (!$userStmt->fetch()) {
    http_response_code(404);
    echo json_encode(['error' => true, 'msg' => '用户不存在']);
    exit();
}

// 准备更新或插入
$isNewRecord = $photoId === null;
$now = date('Y-m-d H:i:s');

try {
    // 开始事务
    $conn->beginTransaction();
    
    if ($isNewRecord) {
        // 插入新记录
        $insertQuery = "INSERT INTO photos (user_id, image_url, type, description, created_at, updated_at) 
                       VALUES (:user_id, :image_url, :type, :description, :created_at, :updated_at)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindValue(':image_url', $imageUrl);
        $stmt->bindValue(':type', $type);
        $stmt->bindValue(':description', $description);
        $stmt->bindValue(':created_at', $now);
        $stmt->bindValue(':updated_at', $now);
        $stmt->execute();
        
        $photoId = $conn->lastInsertId();
        $message = '照片添加成功';
    } else {
        // 检查记录是否存在
        $checkQuery = "SELECT id FROM photos WHERE id = :id";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bindValue(':id', $photoId, PDO::PARAM_INT);
        $checkStmt->execute();
        
        if (!$checkStmt->fetch()) {
            throw new Exception('照片不存在');
        }
        
        // 更新现有记录
        $updateQuery = "UPDATE photos 
                       SET user_id = :user_id, 
                           image_url = :image_url, 
                           type = :type, 
                           description = :description, 
                           updated_at = :updated_at 
                       WHERE id = :id";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bindValue(':id', $photoId, PDO::PARAM_INT);
        $stmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindValue(':image_url', $imageUrl);
        $stmt->bindValue(':type', $type);
        $stmt->bindValue(':description', $description);
        $stmt->bindValue(':updated_at', $now);
        $stmt->execute();
        
        $message = '照片更新成功';
    }
    
    // 提交事务
    $conn->commit();
    
    // 查询更新后的记录
    $query = "SELECT id, user_id, image_url, type, description, created_at, updated_at 
              FROM photos 
              WHERE id = :id";
    $stmt = $conn->prepare($query);
    $stmt->bindValue(':id', $photoId, PDO::PARAM_INT);
    $stmt->execute();
    
    $photo = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'msg' => $message,
        'data' => $photo
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    // 返回错误响应
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '操作失败: ' . $e->getMessage()
    ]);
} 