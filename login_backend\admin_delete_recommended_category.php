<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => '不支持的请求方法']);
    exit();
}

// 验证管理员权限
$headers = getallheaders();
if (!isset($headers['Authorization'])) {
    http_response_code(401);
    echo json_encode(['error' => '未提供授权令牌']);
    exit();
}

$token = str_replace('Bearer ', '', $headers['Authorization']);
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);
if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => '无效的授权令牌']);
    exit();
}
$admin_id = $payload['admin_id'];

// 获取POST数据
$data = json_decode(file_get_contents('php://input'), true);
if (!isset($data['category_id'])) {
    http_response_code(400);
    echo json_encode(['error' => '缺少必要参数']);
    exit();
}

$category_id = $data['category_id'];

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // 检查分类是否存在
    $stmt = $db->prepare('SELECT id FROM recommended_outfit_categories WHERE id = ?');
    $stmt->execute([$category_id]);
    if (!$stmt->fetch()) {
        http_response_code(404);
        echo json_encode(['error' => '分类不存在']);
        exit();
    }
    
    // 检查分类下是否有穿搭
    $stmt = $db->prepare('SELECT COUNT(*) FROM recommended_outfits WHERE category_id = ?');
    $stmt->execute([$category_id]);
    if ($stmt->fetchColumn() > 0) {
        http_response_code(400);
        echo json_encode(['error' => '该分类下存在穿搭，无法删除']);
        exit();
    }
    
    // 删除分类
    $stmt = $db->prepare('DELETE FROM recommended_outfit_categories WHERE id = ?');
    $stmt->execute([$category_id]);
    
    echo json_encode([
        'success' => true,
        'message' => '分类删除成功'
    ]);
    
} catch (PDOException $e) {
    error_log('Database error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => '数据库错误']);
} catch (Exception $e) {
    error_log('Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => '服务器错误']);
} 