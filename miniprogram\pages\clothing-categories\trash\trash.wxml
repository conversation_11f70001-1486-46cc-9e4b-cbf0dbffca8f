<view class="container">

  <!-- 已删除分类列表 -->
  <view class="category-list" wx:if="{{deletedCategories.length > 0}}">
    <view class="category-item" wx:for="{{deletedCategories}}" wx:key="id">
      <view class="category-info">
        <view class="category-name">
          {{item.name}}
          <text class="system-tag" wx:if="{{item.is_system}}">系统</text>
          <text class="custom-tag" wx:else>自定义</text>
        </view>
        <view class="delete-time">删除时间: {{item.deleted_at}}</view>
      </view>
      <view class="category-actions">
        <view class="restore-btn" catchtap="restoreCategory" 
              data-code="{{item.code}}" 
              data-name="{{item.name}}">
          <image src="/images/restore.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <image class="empty-image" src="/images/empty-trash.png" mode="aspectFit"></image>
    <view class="empty-text">回收站为空</view>
    <view class="empty-desc">删除的分类将显示在这里</view>
  </view>

  <!-- 加载状态 -->
  <view class="load-more" wx:if="{{isLoading}}">
    <view>加载中...</view>
  </view>
</view> 