<?php
/**
 * 天气API测试脚本
 * 
 * 用于测试天气API的直接调用和配置是否正确
 */

// 加载配置
require_once 'config.php';

// 输出测试信息
echo "<h1>天气API配置测试</h1>\n";
echo "<pre>\n";
echo "API Host: " . WEATHER_API_HOST . "\n";
echo "API Key: " . substr(WEATHER_API_KEY, 0, 5) . "...\n";
echo "Default Location: " . WEATHER_DEFAULT_LOCATION . "\n";
echo "</pre>\n";

// 测试直接调用和风天气API
echo "<h2>测试直接调用和风天气API</h2>\n";
echo "<pre>\n";

// 构建API请求URL
$apiHost = 'https://' . WEATHER_API_HOST;
$apiPath = WEATHER_API_PATH;
$queryParams = [
    'location' => WEATHER_DEFAULT_LOCATION,
    'key' => WEATHER_API_KEY,
    'lang' => 'zh',
    'unit' => 'm'
];
$queryString = http_build_query($queryParams);
$weatherApiUrl = $apiHost . $apiPath . '?' . $queryString;

echo "请求URL: " . $weatherApiUrl . "\n";

// 设置请求头部
$headers = [
    'Accept: application/json',
    'Accept-Encoding: gzip',
    'Referer: https://cyyg.alidog.cn',  // 修改为白名单域名
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36'
];

echo "请求头: " . json_encode($headers, JSON_UNESCAPED_SLASHES) . "\n";

// 发送请求
$ch = curl_init($weatherApiUrl);
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_SSL_VERIFYPEER => true,
    CURLOPT_ENCODING => 'gzip',
    CURLOPT_HTTPHEADER => $headers,
    CURLOPT_FOLLOWLOCATION => true, // 跟随重定向
    CURLOPT_VERBOSE => true // 启用详细输出
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

// 输出结果
echo "HTTP状态码: " . $httpCode . "\n";
if ($error) {
    echo "错误信息: " . $error . "\n";
} else {
    echo "响应数据:\n";
    $result = json_decode($response, true);
    if ($result) {
        echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    } else {
        echo "无法解析JSON响应: " . $response;
    }
}

echo "\n</pre>\n";

// 测试通过本地get_weather.php调用
echo "<h2>测试通过本地get_weather.php调用</h2>\n";
echo "<pre>\n";

// 构建本地API请求URL
$localApiUrl = API_DOMAIN . "/login_backend/get_weather.php?cityid=" . WEATHER_DEFAULT_LOCATION;
echo "请求URL: " . $localApiUrl . "\n";

// 发送请求
$ch = curl_init($localApiUrl);
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_SSL_VERIFYPEER => true
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

// 输出结果
echo "HTTP状态码: " . $httpCode . "\n";
if ($error) {
    echo "错误信息: " . $error . "\n";
} else {
    echo "响应数据:\n";
    $result = json_decode($response, true);
    if ($result) {
        echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    } else {
        echo "无法解析JSON响应: " . $response;
    }
}

echo "\n</pre>\n";
?> 