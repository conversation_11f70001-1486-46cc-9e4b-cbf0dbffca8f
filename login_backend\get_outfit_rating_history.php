<?php
/**
 * 获取穿搭评分历史数据API
 * 
 * 用于管理后台查询所有用户的穿搭评分历史记录
 * 
 * Headers:
 * - Authorization: <admin_token>
 * 
 * GET Parameters:
 * - page: 页码 (默认为1)
 * - limit: 每页记录数 (默认为10)
 * - search: 搜索关键词 (可选，用于搜索用户ID或昵称)
 * 
 * Response:
 * {
 *   "error": false,
 *   "data": {
 *     "ratings": [...],
 *     "pagination": {
 *       "page": 1,
 *       "limit": 10,
 *       "total": 100,
 *       "totalPages": 10
 *     }
 *   }
 * }
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit();
}

// 检查Authorization头是否存在
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证管理员令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取请求参数
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
$search = isset($_GET['search']) ? $_GET['search'] : '';

// 验证参数
if ($page < 1) $page = 1;
if ($limit < 1 || $limit > 50) $limit = 10;

// 计算偏移量
$offset = ($page - 1) * $limit;

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 构建查询语句 - 修改表别名：将 'or' 改为 'o_r'，因为 'or' 是MySQL保留字
    $query = "SELECT 
                o_r.id,
                o_r.user_id,
                o_r.photo_url,
                o_r.score,
                o_r.rating_details,
                o_r.ai_comments,
                o_r.improvement_suggestions,
                o_r.created_at,
                u.nickname,
                u.avatar_url
              FROM outfit_ratings o_r
              LEFT JOIN users u ON o_r.user_id = u.id";
    
    // 添加搜索条件
    $whereClause = '';
    $params = [];
    
    if (!empty($search)) {
        $whereClause = " WHERE u.id LIKE :search OR u.nickname LIKE :search";
        $params['search'] = "%{$search}%";
    }
    
    $query .= $whereClause . " ORDER BY o_r.created_at DESC LIMIT :offset, :limit";
    
    // 准备查询
    $stmt = $conn->prepare($query);
    
    // 绑定参数
    if (!empty($search)) {
        $stmt->bindParam(':search', $params['search'], PDO::PARAM_STR);
    }
    
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    
    // 执行查询
    $stmt->execute();
    $ratings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取总记录数 - 同样修改表别名
    $countQuery = "SELECT COUNT(*) as total FROM outfit_ratings o_r LEFT JOIN users u ON o_r.user_id = u.id" . $whereClause;
    $countStmt = $conn->prepare($countQuery);
    
    if (!empty($search)) {
        $countStmt->bindParam(':search', $params['search'], PDO::PARAM_STR);
    }
    
    $countStmt->execute();
    $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 处理结果
    $ratingList = [];
    foreach ($ratings as $rating) {
        // 解析评分详情
        $ratingDetails = json_decode($rating['rating_details'], true);
        
        $ratingList[] = [
            'id' => $rating['id'],
            'user_id' => $rating['user_id'],
            'user_nickname' => $rating['nickname'] ?: '未知用户',
            'user_avatar' => $rating['avatar_url'],
            'photo_url' => $rating['photo_url'],
            'score' => $rating['score'],
            'rating_details' => $ratingDetails,
            'ai_comments' => $rating['ai_comments'],
            'improvement_suggestions' => $rating['improvement_suggestions'],
            'created_at' => $rating['created_at']
        ];
    }
    
    // 计算总页数
    $totalPages = ceil($totalCount / $limit);
    
    // 返回结果
    echo json_encode([
        'error' => false,
        'data' => [
            'ratings' => $ratingList,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $totalCount,
                'totalPages' => $totalPages
            ]
        ]
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '数据库错误: ' . $e->getMessage()]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '服务器错误: ' . $e->getMessage()]);
}
?> 