<view class="container">
  <!-- 判断是否登录 -->
  <block wx:if="{{hasUserInfo}}">
    <!-- 加载中状态 -->
    <block wx:if="{{loading}}">
      <view class="loading-container">
        <view class="loading-icon"></view>
        <view class="loading-text">正在智能生成推荐...</view>
      </view>
    </block>
    
    <!-- 已加载内容 -->
    <block wx:else>
      <!-- 天气信息卡片 - 隐藏原有卡片 -->
      <view class="weather-card" style="display: none;">
        <!-- 四列展示天气信息 -->
        <view class="weather-details">
          <!-- 天气列 -->
          <view class="weather-detail-item">
            <view class="weather-detail-label">天气</view>
            <view class="weather-detail-value">{{weather.text || '未知'}}</view>
          </view>
          
          <!-- 温度列 -->
          <view class="weather-detail-item">
            <view class="weather-detail-label">温度</view>
            <view class="weather-detail-value">{{weather.temp || '--'}}°C</view>
          </view>
          
          <!-- 风向/风力列 -->
          <view class="weather-detail-item">
            <view class="weather-detail-label">风向/风力</view>
            <view class="weather-detail-value">{{weather.windDir || '未知'}} {{weather.windScale || '--'}}级</view>
          </view>
          
          <!-- 湿度列 -->
          <view class="weather-detail-item">
            <view class="weather-detail-label">湿度</view>
            <view class="weather-detail-value">{{weather.humidity || '--'}}%</view>
          </view>
        </view>
        
        <!-- 位置信息 -->
        <view class="weather-location">
          <text class="location-text">{{(weather.city === 'beijing' ? '北京' : weather.city) || '未知位置'}}</text>
          <view class="location-icon" bindtap="regetLocation">↻</view>
        </view>
      </view>
      
      <!-- 穿搭推荐卡片 -->
      <view class="outfit-card">
        <!-- 修改后的标题区域，集成天气信息 -->
        <view class="outfit-title-container">
          <view class="outfit-title-left">智能穿搭推荐</view>
          <view class="outfit-title-right">
            <view class="weather-info-compact">
              <text class="weather-city">{{(weather.city === 'beijing' ? '北京' : weather.city) || '未知位置'}}</text>
              <text class="weather-temp-text">{{weather.temp || '--'}}°C</text>
              <text class="weather-condition">{{weather.text || '未知'}}</text>
              <view class="location-icon-small" bindtap="regetLocation">↻</view>
            </view>
          </view>
        </view>
        
        <!-- 上衣 -->
        <view class="outfit-item" wx:if="{{outfit.top}}" data-id="{{outfit.top.id}}" bindtap="viewClothingDetail">
          <view class="outfit-item-image-container">
            <image src="{{outfit.top.image_url}}" class="outfit-item-image" mode="aspectFit"></image>
          </view>
          <view class="outfit-item-info">
            <view class="outfit-item-category">上衣</view>
            <view class="outfit-item-name">{{outfit.top.name || '时尚上衣'}}</view>
            <view class="outfit-item-desc" wx:if="{{outfit.top.description && outfit.top.description['颜色']}}">颜色：{{outfit.top.description['颜色']}}</view>
            <view class="outfit-item-reason" wx:if="{{outfit.top.reason}}">{{outfit.top.reason}}</view>
          </view>
        </view>
        
        <!-- 外套，如果天气需要 -->
        <view class="outfit-item" wx:if="{{outfit.outerwear}}" data-id="{{outfit.outerwear.id}}" bindtap="viewClothingDetail">
          <view class="outfit-item-image-container">
            <image src="{{outfit.outerwear.image_url}}" class="outfit-item-image" mode="aspectFit"></image>
          </view>
          <view class="outfit-item-info">
            <view class="outfit-item-category">外套</view>
            <view class="outfit-item-name">{{outfit.outerwear.name || '时尚外套'}}</view>
            <view class="outfit-item-desc" wx:if="{{outfit.outerwear.description && outfit.outerwear.description['颜色']}}">颜色：{{outfit.outerwear.description['颜色']}}</view>
            <view class="outfit-item-reason" wx:if="{{outfit.outerwear.reason}}">{{outfit.outerwear.reason}}</view>
          </view>
        </view>
        
        <!-- 下装 -->
        <view class="outfit-item" wx:if="{{outfit.bottom}}" data-id="{{outfit.bottom.id}}" bindtap="viewClothingDetail">
          <view class="outfit-item-image-container">
            <image src="{{outfit.bottom.image_url}}" class="outfit-item-image" mode="aspectFit"></image>
          </view>
          <view class="outfit-item-info">
            <view class="outfit-item-category">{{outfit.bottom.category === 'pants' ? '裤子' : '裙子'}}</view>
            <view class="outfit-item-name">{{outfit.bottom.name || '时尚下装'}}</view>
            <view class="outfit-item-desc" wx:if="{{outfit.bottom.description && outfit.bottom.description['颜色']}}">颜色：{{outfit.bottom.description['颜色']}}</view>
            <view class="outfit-item-reason" wx:if="{{outfit.bottom.reason}}">{{outfit.bottom.reason}}</view>
          </view>
        </view>
        
        <!-- 鞋子 -->
        <view class="outfit-item" wx:if="{{outfit.shoes}}" data-id="{{outfit.shoes.id}}" bindtap="viewClothingDetail">
          <view class="outfit-item-image-container">
            <image src="{{outfit.shoes.image_url}}" class="outfit-item-image" mode="aspectFit"></image>
          </view>
          <view class="outfit-item-info">
            <view class="outfit-item-category">鞋子</view>
            <view class="outfit-item-name">{{outfit.shoes.name || '时尚鞋子'}}</view>
            <view class="outfit-item-desc" wx:if="{{outfit.shoes.description && outfit.shoes.description['颜色']}}">颜色：{{outfit.shoes.description['颜色']}}</view>
            <view class="outfit-item-reason" wx:if="{{outfit.shoes.reason}}">{{outfit.shoes.reason}}</view>
          </view>
        </view>
        
        <!-- 配饰 -->
        <view class="outfit-item" wx:if="{{outfit.accessories}}" data-id="{{outfit.accessories.id}}" bindtap="viewClothingDetail">
          <view class="outfit-item-image-container">
            <image src="{{outfit.accessories.image_url}}" class="outfit-item-image" mode="aspectFit"></image>
          </view>
          <view class="outfit-item-info">
            <view class="outfit-item-category">配饰</view>
            <view class="outfit-item-name">{{outfit.accessories.name || '时尚配饰'}}</view>
            <view class="outfit-item-desc" wx:if="{{outfit.accessories.description && outfit.accessories.description['颜色']}}">颜色：{{outfit.accessories.description['颜色']}}</view>
            <view class="outfit-item-reason" wx:if="{{outfit.accessories.reason}}">{{outfit.accessories.reason}}</view>
          </view>
        </view>
        
        <!-- 包包 -->
        <view class="outfit-item" wx:if="{{outfit.bag}}" data-id="{{outfit.bag.id}}" bindtap="viewClothingDetail">
          <view class="outfit-item-image-container">
            <image src="{{outfit.bag.image_url}}" class="outfit-item-image" mode="aspectFit"></image>
          </view>
          <view class="outfit-item-info">
            <view class="outfit-item-category">包包</view>
            <view class="outfit-item-name">{{outfit.bag.name || '时尚包包'}}</view>
            <view class="outfit-item-desc" wx:if="{{outfit.bag.description && outfit.bag.description['颜色']}}">颜色：{{outfit.bag.description['颜色']}}</view>
            <view class="outfit-item-reason" wx:if="{{outfit.bag.reason}}">{{outfit.bag.reason}}</view>
          </view>
        </view>
        
        <!-- 穿搭总结 -->
        <view class="outfit-summary" wx:if="{{outfit.outfit_summary}}">
          <view class="summary-title">穿搭总结</view>
          <view class="summary-content">{{outfit.outfit_summary}}</view>
        </view>
        
        <!-- 商品推荐部分 -->
        <view class="product-section" wx:if="{{recommendedProducts && recommendedProducts.length > 0}}">
          <view class="section-divider"></view>
          <view class="outfit-title-container">
            <view class="outfit-title-left">推荐配备</view>
            <view class="outfit-title-right"></view>
          </view>
          
          <!-- 商品加载中状态 -->
          <block wx:if="{{productsLoading}}">
            <view class="loading-mini-container">
              <view class="loading-mini-icon"></view>
              <view class="loading-mini-text">加载推荐商品中...</view>
            </view>
          </block>
          
          <!-- 商品列表 -->
          <block wx:else>
            <view class="product-grid">
              <view class="product-item" wx:for="{{recommendedProducts}}" wx:key="id" data-id="{{item.id}}" data-index="{{index}}" bindtap="viewProductDetail">
                <view class="product-image-container">
                  <image src="{{item.image_url}}" class="product-image" mode="aspectFill"></image>
                </view>
                <view class="product-info">
                  <view class="product-name">{{item.title}}</view>
                  <view class="product-price-container">
                    <text class="product-price">¥{{item.final_price || item.zk_final_price}}</text>
                    <text class="product-original-price">¥{{item.original_price}}</text>
                  </view>
                  <view class="product-coupon" wx:if="{{item.coupon_amount > 0}}">
                    <text class="coupon-text">券 ¥{{item.coupon_amount}}</text>
                  </view>
                </view>
              </view>
            </view>
            
            <!-- 商品推荐提示 -->
            <view class="outfit-summary product-tips">
              <view class="tip-item">* 点击商品可复制淘口令购买</view>
              <view class="tip-item">* 商品推荐基于当前天气和季节需求</view>
              <view class="tip-item" wx:if="{{recommendedProducts.length === 0 && !productsLoading}}">* 当前暂无符合条件的推荐商品</view>
            </view>
          </block>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="action-btn refresh-btn {{refreshing ? 'refreshing' : ''}}" bindtap="refreshRecommendation">换一批</button>
        <button class="action-btn save-btn" bindtap="saveOutfit">保存穿搭</button>
      </view>
      
      <!-- 穿搭推荐提示 -->
      <view class="recommendation-tips">
        <view class="tip-item">* 点击衣物可查看详情</view>
        <view class="tip-item">* 推荐基于当前天气和您的衣物</view>
      </view>
      
      <!-- 体验账号提示
      <view wx:if="{{isUsingMockUser}}" class="mock-user-banner">
        <text class="mock-banner-text">当前为体验账号数据，推荐登入后使用 →</text>
        <view class="mock-login-btn" bindtap="goToLogin">登录</view>
      </view> -->
    </block>
  </block>
  
  <!-- 未登录状态 -->
  <block wx:else>
    <view class="empty-state">
      <view class="empty-icon">
        <image src="/images/emtpy.png" mode="aspectFit" class="empty-image"></image>
      </view>
      <view class="empty-text">登录后即可获取智能穿搭推荐</view>
      <view class="empty-text text-sm">智能穿搭基于您的衣物和当前天气</view>
      <button class="login-btn" bindtap="goToLogin">
        立即登录
      </button>
    </view>
  </block>
</view>

<!-- 分享提示弹窗 -->
<view class="share-tip-modal" wx:if="{{showShareTip}}">
  <view class="share-tip-overlay" bindtap="closeShareTip"></view>
  <view class="share-tip-content">
    <view class="share-tip-title">今日推荐次数已用完</view>
    <view class="share-tip-icon">🔄</view>
    <view class="share-tip-message">分享给好友即可获得1次推荐机会</view>
    <view class="share-tip-buttons">
      <button class="share-tip-btn cancel-btn" bindtap="closeShareTip">取消</button>
      <button class="share-tip-btn share-btn" open-type="share" bindtap="shareToGetMoreQuota">
        去分享
      </button>
    </view>
  </view>
</view> 