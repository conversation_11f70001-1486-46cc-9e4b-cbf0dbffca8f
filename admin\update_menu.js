/**
 * 批量更新侧边栏菜单，添加穿搭管理链接
 */
const fs = require('fs');
const path = require('path');

// 获取admin目录下的所有HTML文件
const adminDir = __dirname;
const files = fs.readdirSync(adminDir).filter(file => 
    file.endsWith('.html') && 
    file !== 'outfit_list.html' && 
    file !== 'outfit_details.html'
);

// 处理每个文件
files.forEach(file => {
    const filePath = path.join(adminDir, file);
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 检查文件中是否已经包含穿搭管理链接
    if (content.includes('href="outfit_list.html"')) {
        console.log(`文件 ${file} 中已有穿搭管理链接，跳过处理。`);
        return;
    }
    
    // 查找试衣历史菜单项的位置
    const tryOnMenuPattern = /<li class="menu-item[^>]*><a href="try_on_list\.html">试衣历史<\/a><\/li>/;
    const match = content.match(tryOnMenuPattern);
    
    if (match) {
        // 添加穿搭管理菜单项在试衣历史后面
        const outfitMenuItem = '<li class="menu-item"><a href="outfit_list.html">穿搭管理</a></li>';
        const updatedContent = content.replace(
            match[0],
            `${match[0]}\n                <li class="menu-item"><a href="outfit_list.html">穿搭管理</a></li>`
        );
        
        // 写回文件
        fs.writeFileSync(filePath, updatedContent, 'utf8');
        console.log(`成功更新文件 ${file}`);
    } else {
        console.log(`文件 ${file} 中未找到试衣历史菜单项，无法更新。`);
    }
});

console.log('菜单更新完成！'); 