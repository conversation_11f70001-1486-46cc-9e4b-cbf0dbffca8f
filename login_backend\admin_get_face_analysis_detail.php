<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查Authorization头是否存在
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证管理员令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '无效或已过期的令牌']);
    exit();
}

// 检查是否提供了分析ID
if (!isset($_GET['id']) || empty($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => '缺少分析ID']);
    exit();
}

$analysisId = intval($_GET['id']);

// 获取数据库连接
$db = new Database();
$conn = $db->getConnection();

try {
    // 查询面部分析详情，包含用户信息
    $sql = "SELECT fa.*, u.nickname, u.avatar_url 
            FROM face_analysis fa 
            LEFT JOIN users u ON fa.user_id = u.id 
            WHERE fa.id = :id";
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':id', $analysisId);
    $stmt->execute();
    
    $analysis = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$analysis) {
        http_response_code(404);
        echo json_encode(['status' => 'error', 'message' => '未找到指定的分析记录']);
        exit();
    }
    
    // 确保CDN URL字段存在，即使为空
    if (!isset($analysis['cdn_front_photo_url'])) {
        $analysis['cdn_front_photo_url'] = '';
    }
    
    if (!isset($analysis['cdn_side_photo_url'])) {
        $analysis['cdn_side_photo_url'] = '';
    }
    
    // 使用CDN URL替换本地URL，如果CDN URL存在
    if (!empty($analysis['cdn_front_photo_url'])) {
        $analysis['display_front_photo_url'] = $analysis['cdn_front_photo_url'];
    } else {
        $analysis['display_front_photo_url'] = $analysis['front_photo_url'];
    }
    
    if (!empty($analysis['cdn_side_photo_url'])) {
        $analysis['display_side_photo_url'] = $analysis['cdn_side_photo_url'];
    } else {
        $analysis['display_side_photo_url'] = $analysis['side_photo_url'];
    }
    
    // 处理分析结果JSON
    if (isset($analysis['analysis_result']) && !empty($analysis['analysis_result'])) {
        // 尝试解析JSON
        $resultData = json_decode($analysis['analysis_result'], true);
        
        if ($resultData) {
            // 保留原始JSON字符串以备前端需要
            $analysis['analysis_result_json'] = $analysis['analysis_result'];
            
            // 如果解析成功，使用解析后的数据替换原始字符串
            $analysis['analysis_result'] = $resultData;
            
            // 提取摘要（如果存在）
            if (isset($resultData['data']['summary'])) {
                $analysis['summary'] = $resultData['data']['summary'];
            }
        }
    }
    
    // 格式化日期
    if (isset($analysis['created_at'])) {
        $analysis['created_at'] = date('Y-m-d H:i:s', strtotime($analysis['created_at']));
    }
    if (isset($analysis['updated_at'])) {
        $analysis['updated_at'] = date('Y-m-d H:i:s', strtotime($analysis['updated_at']));
    }
    
    // 返回结果
    echo json_encode([
        'status' => 'success',
        'data' => $analysis
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error', 
        'message' => '数据库查询错误', 
        'error' => $e->getMessage()
    ]);
    exit();
} 