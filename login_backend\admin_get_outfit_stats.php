<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证管理员权限
$auth = new Auth();

// 检查是否存在Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证管理员token
$adminData = $auth->verifyAdminToken($token);
if (!$adminData) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的管理员令牌']);
    exit();
}

// 获取数据库连接
$db = new Database();
$conn = $db->getConnection();

// 获取查询参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 10;
$sort_by = isset($_GET['sort_by']) ? strtolower($_GET['sort_by']) : 'views';
$category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : null;

// 验证分页参数
if ($page < 1) $page = 1;
if ($per_page < 1) $per_page = 10;
if ($per_page > 100) $per_page = 100;

// 验证排序参数
$allowed_sort_options = ['views', 'copies', 'newest', 'oldest'];
if (!in_array($sort_by, $allowed_sort_options)) {
    $sort_by = 'views';
}

// 计算偏移量
$offset = ($page - 1) * $per_page;

try {
    // 构建SQL查询 - 先获取记录总数
    $countQuery = "SELECT COUNT(*) as total FROM recommended_outfits ro 
                  JOIN recommended_outfit_stats ros ON ro.id = ros.outfit_id 
                  WHERE 1=1";
    $paramsCount = [];

    // 添加分类过滤条件
    if ($category_id !== null) {
        $countQuery .= " AND ro.category_id = ?";
        $paramsCount[] = $category_id;
    }

    // 执行总数查询
    $countStmt = $conn->prepare($countQuery);
    if (!empty($paramsCount)) {
        $i = 1;
        foreach ($paramsCount as $param) {
            $countStmt->bindValue($i++, $param);
        }
    }
    $countStmt->execute();
    $totalRecords = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

    // 构建SQL查询 - 查询具体数据
    $query = "SELECT ro.id, ro.name, ro.status, ro.category_id, oc.name as category_name, 
              ros.view_count, ros.copy_link_count, ro.created_at,
              (SELECT COUNT(*) FROM recommended_outfit_items WHERE outfit_id = ro.id) as item_count
              FROM recommended_outfits ro 
              JOIN recommended_outfit_stats ros ON ro.id = ros.outfit_id 
              LEFT JOIN outfit_categories oc ON ro.category_id = oc.id 
              WHERE 1=1";
    $params = [];

    // 添加分类过滤条件
    if ($category_id !== null) {
        $query .= " AND ro.category_id = ?";
        $params[] = $category_id;
    }

    // 添加排序
    switch ($sort_by) {
        case 'views':
            $query .= " ORDER BY ros.view_count DESC";
            break;
        case 'copies':
            $query .= " ORDER BY ros.copy_link_count DESC";
            break;
        case 'newest':
            $query .= " ORDER BY ro.created_at DESC";
            break;
        case 'oldest':
            $query .= " ORDER BY ro.created_at ASC";
            break;
    }

    // 添加分页
    $query .= " LIMIT ? OFFSET ?";
    $params[] = $per_page;
    $params[] = $offset;

    // 执行数据查询
    $stmt = $conn->prepare($query);
    if (!empty($params)) {
        $i = 1;
        foreach ($params as $index => $param) {
            // 为LIMIT和OFFSET参数指定整数类型
            if ($index >= count($params) - 2) {
                $stmt->bindValue($i++, $param, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($i++, $param);
            }
        }
    }
    $stmt->execute();
    $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 计算总页数
    $totalPages = ceil($totalRecords / $per_page);
    
    // 构建分页信息
    $pagination = [
        'total' => $totalRecords,
        'per_page' => $per_page,
        'current_page' => $page,
        'total_pages' => $totalPages,
        'has_more' => $page < $totalPages
    ];
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'data' => $stats,
        'pagination' => $pagination
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '数据库错误: ' . $e->getMessage()]);
} 