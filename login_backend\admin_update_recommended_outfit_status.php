<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证管理员权限
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证管理员token
$adminData = $auth->verifyAdminToken($token);
if (!$adminData) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取数据库连接
$db = new Database();
$conn = $db->getConnection();

// 获取并验证POST数据
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '无效的JSON数据']);
    exit();
}

// 验证必要字段
if (!isset($input['id']) || empty($input['name']) || empty($input['image_url'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少必要参数：id、name、image_url']);
    exit();
}

// 准备数据
$id = intval($input['id']);
$name = trim($input['name']);
$description = isset($input['description']) ? trim($input['description']) : '';
$recommendationReason = isset($input['recommendation_reason']) ? trim($input['recommendation_reason']) : '';
$categoryId = isset($input['category_id']) ? intval($input['category_id']) : null;
$imageUrl = trim($input['image_url']);
$status = isset($input['status']) ? intval($input['status']) : 1;
$sortOrder = isset($input['sort_order']) ? intval($input['sort_order']) : 0;
$items = isset($input['items']) && is_array($input['items']) ? $input['items'] : [];

// 验证穿搭是否存在
$checkQuery = "SELECT id FROM recommended_outfits WHERE id = ?";
$checkStmt = $conn->prepare($checkQuery);
$checkStmt->bindValue(1, $id);
$checkStmt->execute();

if (!$checkStmt->fetch()) {
    http_response_code(404);
    echo json_encode(['error' => true, 'msg' => '找不到指定的推荐穿搭']);
    exit();
}

// 验证分类ID是否存在
if ($categoryId !== null) {
    $categoryQuery = "SELECT id FROM outfit_categories WHERE id = ?";
    $categoryStmt = $conn->prepare($categoryQuery);
    $categoryStmt->bindValue(1, $categoryId);
    $categoryStmt->execute();
    
    if (!$categoryStmt->fetch()) {
        $categoryId = null; // 分类ID不存在，将其设为null
    }
}

// 开始数据库事务
try {
    $conn->beginTransaction();
    
    // 更新推荐穿搭记录
    $query = "UPDATE recommended_outfits 
              SET category_id = ?, name = ?, image_url = ?, description = ?, 
                  recommendation_reason = ?, status = ?, sort_order = ?, updated_at = NOW() 
              WHERE id = ?";
    
    $stmt = $conn->prepare($query);
    $stmt->bindValue(1, $categoryId);
    $stmt->bindValue(2, $name);
    $stmt->bindValue(3, $imageUrl);
    $stmt->bindValue(4, $description);
    $stmt->bindValue(5, $recommendationReason);
    $stmt->bindValue(6, $status);
    $stmt->bindValue(7, $sortOrder);
    $stmt->bindValue(8, $id);
    $stmt->execute();
    
    // 处理商品项
    if (isset($input['items'])) {
        // 删除旧的商品项
        $deleteQuery = "DELETE FROM recommended_outfit_items WHERE outfit_id = ?";
        $deleteStmt = $conn->prepare($deleteQuery);
        $deleteStmt->bindValue(1, $id);
        $deleteStmt->execute();
        
        // 插入新的商品项
        if (!empty($items)) {
            $itemQuery = "INSERT INTO recommended_outfit_items 
                         (outfit_id, name, image_url, price, purchase_url, sort_order, created_at) 
                         VALUES (?, ?, ?, ?, ?, ?, NOW())";
            
            $itemStmt = $conn->prepare($itemQuery);
            
            foreach ($items as $index => $item) {
                // 验证商品必要字段
                if (empty($item['name']) || empty($item['image_url']) || !isset($item['price']) || empty($item['purchase_url'])) {
                    continue; // 跳过不完整的商品
                }
                
                $itemName = trim($item['name']);
                $itemImageUrl = trim($item['image_url']);
                $itemPrice = floatval($item['price']);
                $itemPurchaseUrl = trim($item['purchase_url']);
                $itemSortOrder = isset($item['sort_order']) ? intval($item['sort_order']) : $index;
                
                $itemStmt->bindValue(1, $id);
                $itemStmt->bindValue(2, $itemName);
                $itemStmt->bindValue(3, $itemImageUrl);
                $itemStmt->bindValue(4, $itemPrice);
                $itemStmt->bindValue(5, $itemPurchaseUrl);
                $itemStmt->bindValue(6, $itemSortOrder);
                $itemStmt->execute();
            }
        }
    }
    
    // 确保统计记录存在
    $checkStatsQuery = "SELECT id FROM recommended_outfit_stats WHERE outfit_id = ?";
    $checkStatsStmt = $conn->prepare($checkStatsQuery);
    $checkStatsStmt->bindValue(1, $id);
    $checkStatsStmt->execute();
    
    if (!$checkStatsStmt->fetch()) {
        $statsQuery = "INSERT INTO recommended_outfit_stats (outfit_id, created_at) VALUES (?, NOW())";
        $statsStmt = $conn->prepare($statsQuery);
        $statsStmt->bindValue(1, $id);
        $statsStmt->execute();
    }
    
    // 提交事务
    $conn->commit();
    
    // 获取更新后的穿搭详情以返回
    $outfitQuery = "SELECT ro.*, oc.name as category_name 
                  FROM recommended_outfits ro 
                  LEFT JOIN outfit_categories oc ON ro.category_id = oc.id 
                  WHERE ro.id = ?";
    
    $outfitStmt = $conn->prepare($outfitQuery);
    $outfitStmt->bindValue(1, $id);
    $outfitStmt->execute();
    $outfit = $outfitStmt->fetch(PDO::FETCH_ASSOC);
    
    // 查询穿搭商品列表
    $itemsQuery = "SELECT * FROM recommended_outfit_items 
                 WHERE outfit_id = ? 
                 ORDER BY sort_order ASC, id ASC";
    
    $itemsStmt = $conn->prepare($itemsQuery);
    $itemsStmt->bindValue(1, $id);
    $itemsStmt->execute();
    $outfitItems = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 将商品列表添加到穿搭对象
    $outfit['items'] = $outfitItems;
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'msg' => '推荐穿搭更新成功',
        'data' => $outfit
    ]);
} catch (PDOException $e) {
    // 回滚事务
    $conn->rollBack();
    
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '数据库错误: ' . $e->getMessage()]);
}