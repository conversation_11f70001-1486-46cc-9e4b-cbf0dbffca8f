<view class="container">
  <!-- 主要标签页选择 -->
  <view class="main-tabs">
    <view class="tab-item {{activeTab === 'deals' ? 'active' : ''}}" bindtap="onTabChange" data-tab="deals">优惠推荐</view>
    <view class="tab-item {{activeTab === 'outfits' ? 'active' : ''}}" bindtap="onTabChange" data-tab="outfits">穿搭推荐</view>
  </view>

  <!-- 淘宝优惠商品区域 -->
  <block wx:if="{{activeTab === 'deals'}}">
    <!-- 搜索框 
    <view class="search-box">
      <input type="text" class="search-input" placeholder="搜索优惠商品" bindinput="onSearchInput" bindconfirm="onSearchConfirm" value="{{searchKeyword}}" confirm-type="search" />
      <view class="search-btn" bindtap="onSearchConfirm">搜索</view>
    </view>-->
    
    <!-- 淘宝分类选项卡 -->
    <view class="categories-bar">
      <scroll-view scroll-x="true" class="category-tabs" enable-flex="true" show-scrollbar="false">
        <!-- 分类列表 -->
        <view 
          wx:for="{{taobaoCategories}}" 
          wx:key="id" 
          class="category-item {{activeTaobaoCategory === item.id ? 'active' : ''}}" 
          bindtap="onTaobaoCategoryChange" 
          data-id="{{item.id}}">
          {{item.name}}
        </view>
      </scroll-view>
    </view>
    
    <!-- 商品列表 -->
    <view class="products-list" wx:if="{{products.length > 0}}">
      <view class="product-item" wx:for="{{products}}" wx:key="id" bindtap="goToProductDetail" data-id="{{item.id}}" data-index="{{index}}">
        <view class="product-image-container">
          <image class="product-image" src="{{item.image_url}}" mode="aspectFill" lazy-load />
          <view class="product-coupon" wx:if="{{item.coupon_amount > 0}}">
            <text class="coupon-text">{{item.coupon_amount}}元券</text>
          </view>
        </view>
        <view class="product-info">
          <view class="product-title">{{item.title}}</view>
          <view class="product-price-info">
            <view class="product-price">
              <text class="price-symbol">¥</text>
              <text class="price-value">{{item.final_price}}</text>
              <text class="original-price" wx:if="{{item.original_price > 0}}">¥{{item.original_price}}</text>
            </view>
            <!--<view class="product-sales">{{item.volume}}人已购</view>-->
          </view>
          <view class="shop-detail-container">
          <view class="product-shop">{{item.shop_title}}</view>
            <!--<view class="detail-btn" catchtap="goToTaobaoDetail" data-id="{{item.id}}" data-index="{{index}}">详情</view>-->
          </view>
        </view>
      </view>
      
      <!-- 查看更多按钮 -->
      <view class="view-more-btn" bindtap="goToTaobaoList">
        查看更多优惠商品
      </view>
    </view>
    
    <!-- 加载中状态 -->
    <view class="loading-container" wx:if="{{productsLoading && !isProductsRefreshing}}">
      <view class="loading-icon"></view>
      <view class="loading-text">加载中...</view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{products.length === 0 && !productsLoading}}">
      <view class="empty-icon">暂无优惠商品</view>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMoreProducts && !productsLoading && products.length > 0}}">
      <view class="loading-icon-small"></view>
      <text>加载更多...</text>
    </view>
    
    <view class="load-more" wx:if="{{!hasMoreProducts && products.length > 0}}">
      <text class="load-more-text">—— 已经到底啦 ——</text>
    </view>
  </block>

  <!-- 穿搭推荐区域 -->
  <block wx:else>
    <!-- 分类选项卡 -->
    <view class="categories-bar">
      <scroll-view scroll-x="true" class="category-tabs" enable-flex="true" show-scrollbar="false">
        <!-- 全部分类选项 -->
        <view 
          class="category-item {{activeCategory === '' ? 'active' : ''}}" 
          bindtap="onCategoryChange" 
          data-name="">全部</view>
        
        <!-- 分类列表 -->
        <view 
          wx:for="{{categories}}" 
          wx:key="id" 
          class="category-item {{activeCategory === item.id ? 'active' : ''}}" 
          bindtap="onCategoryChange" 
          data-name="{{item.id}}">
          {{item.name}}
        </view>
      </scroll-view>
    </view>

    <!-- 加载中状态 -->
    <view class="loading-container" wx:if="{{loading && !isRefreshing}}">
      <view class="loading-icon"></view>
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{outfits.length === 0 && !loading}}">
      <view class="empty-icon">暂无推荐穿搭</view>
    </view>

    <!-- 穿搭列表 -->
    <view class="outfit-list" wx:else>
      <view class="outfit-item" wx:for="{{outfits}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
        <view class="outfit-image-container">
          <image class="outfit-image" src="{{item.image_url}}" mode="aspectFill" lazy-load />
        </view>
        <view class="outfit-info">
          <view class="outfit-name">{{item.name}}</view>
          <view class="outfit-meta">
            <view class="outfit-category" wx:if="{{item.category_name}}">{{item.category_name}}</view>
            <view class="outfit-views">{{item.view_count || 0}}人浏览</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && !loading && outfits.length > 0}}">
      <view class="loading-icon-small"></view>
      <text>加载更多...</text>
    </view>
    
    <view class="load-more" wx:if="{{!hasMore && outfits.length > 0}}">
      <text class="load-more-text">—— 已经到底啦 ——</text>
    </view>
  </block>
</view> 