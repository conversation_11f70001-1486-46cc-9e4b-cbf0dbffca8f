<?php
/**
 * 和风天气城市查询API
 * 通过经纬度查询城市信息
 */

header('Content-Type: application/json;charset=utf-8');

// 加载配置
require_once 'config.php';

// 日志目录
$logDir = __DIR__ . '/logs';
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

/**
 * 记录日志
 * @param string $message 日志消息
 * @param array $data 附加数据
 */
function writeLog($message, $data = null) {
    global $logDir;
    $logFile = $logDir . '/city_api_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    
    $logMessage = "[$timestamp] $message";
    if ($data !== null) {
        $logMessage .= " " . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    file_put_contents($logFile, $logMessage . PHP_EOL, FILE_APPEND);
    error_log("[城市API] " . $message);
}

// 获取请求参数
$latitude = isset($_GET['latitude']) ? $_GET['latitude'] : null;
$longitude = isset($_GET['longitude']) ? $_GET['longitude'] : null;
$city = isset($_GET['city']) ? $_GET['city'] : null;
$format = isset($_GET['format']) ? $_GET['format'] : 'json';
$debug = isset($_GET['debug']) ? (bool)$_GET['debug'] : false;

// 支持location参数（格式：经度,纬度）
$location = isset($_GET['location']) ? $_GET['location'] : null;
if (!$latitude || !$longitude) {
    if ($location) {
        $locationParts = explode(',', $location);
        if (count($locationParts) === 2) {
            $longitude = trim($locationParts[0]);
            $latitude = trim($locationParts[1]);
            writeLog("从location参数解析经纬度", [
                'location' => $location,
                'parsed_longitude' => $longitude,
                'parsed_latitude' => $latitude
            ]);
        }
    }
}

// 记录请求
writeLog("收到请求", [
    'latitude' => $latitude,
    'longitude' => $longitude,
    'city' => $city,
    'format' => $format,
    'debug' => $debug,
    'location' => $location
]);

// 验证参数
if ((!$latitude || !$longitude) && !$city) {
    $response = [
        'success' => false,
        'message' => '缺少必要参数：请提供经纬度或城市名称',
        'code' => 400
    ];
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    writeLog("参数错误：缺少经纬度或城市名称");
    exit;
}

// 构建请求和风天气API的参数
$params = [];

if ($city) {
    $params['location'] = $city;
    writeLog("使用城市名称查询", ['city' => $city]);
} else {
    // 使用经纬度查询（注意：经度在前，纬度在后）
    $params['location'] = $longitude . ',' . $latitude;
    writeLog("使用经纬度查询", ['location' => $params['location']]);
}

// 添加API密钥
$params['key'] = WEATHER_API_KEY;

// 设置语言为中文
$params['lang'] = 'zh';

// 添加防缓存参数
$params['_'] = time();

// 构建API URL
$geoApiHost = defined('WEATHER_GEO_API_HOST') ? WEATHER_GEO_API_HOST : 'geoapi.qweather.com';
$geoApiPath = defined('WEATHER_GEO_API_PATH') ? WEATHER_GEO_API_PATH : '/geo/v2/city/lookup';

$apiUrl = 'https://' . $geoApiHost . $geoApiPath . '?' . http_build_query($params);
writeLog("请求和风天气地理编码API", ['url' => $apiUrl, 'host' => $geoApiHost, 'path' => $geoApiPath]);

try {
    // 使用curl替代file_get_contents
    $ch = curl_init();
    
    // 设置CURL选项
    curl_setopt_array($ch, [
        CURLOPT_URL => $apiUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HEADER => true, // 获取响应头
        CURLOPT_ENCODING => 'gzip, deflate', // 自动处理压缩
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => 0,
        CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4, // 强制使用IPv4
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
            'Referer: ' . API_DOMAIN
        ]
    ]);
    
    // 发送请求
    $startTime = microtime(true);
    $response = curl_exec($ch);
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    // 获取状态码和错误信息
    $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    
    // 分离响应头和响应体
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    // 关闭curl连接
    curl_close($ch);
    
    // 记录请求结果
    writeLog("API请求完成", [
        'duration' => $duration . 'ms',
        'status' => $statusCode,
        'error' => $curlError ?: 'none'
    ]);
    
    // 解析响应头
    $responseHeaders = [];
    $headerLines = explode("\n", $headers);
    foreach ($headerLines as $line) {
        $line = trim($line);
        if (preg_match('/^([^:]+):\s*(.+)$/', $line, $matches)) {
            $responseHeaders[$matches[1]] = $matches[2];
        }
    }
    writeLog("响应头信息", $responseHeaders);
    
    // 记录原始响应
    if ($debug) {
        writeLog("原始响应", ['response' => substr($body, 0, 1000), 'length' => strlen($body)]);
    }
    
    if ($statusCode !== 200) {
        writeLog("API请求失败", ['status' => $statusCode, 'error' => $curlError]);
        echo json_encode([
            'success' => false,
            'message' => '城市信息API请求失败：HTTP ' . $statusCode,
            'code' => $statusCode,
            'debug_info' => $debug ? ['response' => $body, 'error' => $curlError] : null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 清理响应中可能存在的BOM和控制字符
    $body = preg_replace('/[\x00-\x1F\x7F]/u', '', $body);
    if (substr($body, 0, 3) === "\xEF\xBB\xBF") {
        $body = substr($body, 3); // 移除UTF-8 BOM
        writeLog("已移除UTF-8 BOM");
    }
    
    // 尝试解析响应
    $data = json_decode($body, true);
    $jsonError = json_last_error();
    
    if ($jsonError !== JSON_ERROR_NONE) {
        writeLog("API响应解析失败", [
            'error' => json_last_error_msg(),
            'error_code' => $jsonError,
            'response_sample' => substr($body, 0, 100) . '...'
        ]);
        
        // 尝试使用不同的解码方式
        if ($jsonError === JSON_ERROR_UTF8) {
            $body = utf8_encode($body);
            writeLog("尝试UTF8编码转换");
            $data = json_decode($body, true);
            $jsonError = json_last_error();
        }
        
        // 如果仍然失败，尝试移除所有非ASCII字符
        if ($jsonError !== JSON_ERROR_NONE) {
            $body = preg_replace('/[^\x20-\x7E]/', '', $body);
            writeLog("尝试移除所有非ASCII字符");
            $data = json_decode($body, true);
            $jsonError = json_last_error();
        }
        
        // 如果仍然失败
        if ($jsonError !== JSON_ERROR_NONE) {
            echo json_encode([
                'success' => false,
                'message' => '解析城市信息失败：' . json_last_error_msg(),
                'code' => 500,
                'debug_info' => $debug ? [
                    'response_sample' => substr($body, 0, 200),
                    'error_code' => $jsonError
                ] : null
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }
    
    // 检查API返回状态
    if ($data['code'] !== '200') {
        writeLog("API返回错误", ['code' => $data['code'], 'response' => $data]);
        echo json_encode([
            'success' => false,
            'message' => '城市信息API返回错误：' . $data['code'],
            'code' => intval($data['code']),
            'response' => $debug ? $data : null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 检查是否有结果
    if (empty($data['location']) || !is_array($data['location'])) {
        writeLog("API未返回城市信息", ['response' => $data]);
        echo json_encode([
            'success' => false,
            'message' => '未找到匹配的城市信息',
            'code' => 404,
            'response' => $debug ? $data : null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 获取第一个匹配的城市（通常是最佳匹配）
    $cityInfo = $data['location'][0];
    
    // 记录找到的城市信息
    writeLog("成功获取城市信息", [
        'name' => $cityInfo['name'],
        'id' => $cityInfo['id'],
        'adm1' => $cityInfo['adm1'],
        'adm2' => $cityInfo['adm2']
    ]);
    
    // 验证城市ID格式，确保其可直接用于天气API查询
    // 和风天气城市ID通常以"101"开头，例如101210101
    if (isset($cityInfo['id']) && !preg_match('/^[0-9]{9}$/', $cityInfo['id'])) {
        writeLog("警告：城市ID格式可能不正确", ['id' => $cityInfo['id']]);
    }
    
    // 格式化响应
    $result = [
        'success' => true,
        'city' => [
            'id' => $cityInfo['id'],
            'name' => $cityInfo['name'],
            'fullName' => $cityInfo['adm1'] === $cityInfo['name'] ? 
                $cityInfo['name'] : $cityInfo['adm1'] . ' ' . $cityInfo['name'],
            'lat' => $cityInfo['lat'],
            'lon' => $cityInfo['lon'],
            'adm1' => $cityInfo['adm1'],
            'adm2' => $cityInfo['adm2'],
            'country' => $cityInfo['country'],
            'timezone' => $cityInfo['tz'],
        ],
        'original_location' => [
            'latitude' => $latitude,
            'longitude' => $longitude,
            'city' => $city
        ],
        'timestamp' => time(),
        'full_response' => $debug ? $data : null
    ];
    
    // 返回结果
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // 处理异常
    writeLog("请求发生异常", ['error' => $e->getMessage()]);
    echo json_encode([
        'success' => false,
        'message' => '城市信息查询异常：' . $e->getMessage(),
        'code' => 500
    ], JSON_UNESCAPED_UNICODE);
    exit;
} 