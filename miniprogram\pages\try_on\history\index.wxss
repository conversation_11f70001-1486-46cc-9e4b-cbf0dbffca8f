.container {
  padding: 20rpx;
  background-color: #f9f9f9;
  min-height: 100vh;
  box-sizing: border-box;
}

.photos-grid {
  display: flex;
  justify-content: space-between;
  margin: 0 -10rpx;
}

.column {
  width: 50%;
  padding: 0 10rpx;
  box-sizing: border-box;
}

.photo-item {
  width: 100%;
  margin-bottom: 20rpx;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  font-size: 0;
  line-height: 0;
}

.photo-image {
  width: 100%;
  display: block;
  margin: 0;
  padding: 0;
  vertical-align: top;
}

.empty-state {
  margin-top: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
  font-size: 28rpx;
  line-height: 2;
}

.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 弹框样式 */
.photo-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx;
}

.modal-content {
  width: 90%;
  max-width: 650rpx;
  height: auto;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  padding: 16rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  max-height: 90vh; /* 限制最大高度 */
}

.modal-photo-container {
  position: relative;
  width: 100%;
  background-color: #f9f9f9;
  overflow: hidden;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200rpx; /* 设置最小高度 */
  max-height: calc(80vh - 100rpx); /* 预留删除按钮的空间 */
}

.modal-photo-image {
  width: 100%;
  display: block;
  object-fit: contain;
  max-height: calc(80vh - 100rpx);
}

.photo-type {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background-color: rgba(59, 130, 246, 0.8);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  z-index: 10;
}

/* 试穿历史特有样式 */
.clothes-info, .time-info {
  padding: 16rpx 8rpx;
  font-size: 24rpx;
  color: #666;
  border-top: 1px solid #f0f0f0;
  text-align: center;
} 