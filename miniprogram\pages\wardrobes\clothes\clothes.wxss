/* 全局容器 */
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #ffffff;
  box-sizing: border-box;
  position: relative;
}

/* 衣橱信息 */
.wardrobe-info {
  text-align: center;
  padding: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background-color: #f9f9f9;
}

.wardrobe-name {
  font-size: 18px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 5px;
}

.wardrobe-hint {
  font-size: 13px;
  color: #666666;
}

/* 分类标签栏 */
.category-tabs {
  white-space: nowrap;
  background-color: #ffffff;
  padding: 12px 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.tabs-container {
  display: inline-flex;
}

.tab-item {
  display: inline-block;
  padding: 8px 16px;
  margin-right: 8px;
  border-radius: 16px;
  font-size: 13px;
  color: #666666;
  background-color: #f5f5f5;
  transition: all 0.3s;
}

.tab-item.active {
  background-color: #000000;
  color: #ffffff;
}

/* 衣物容器 */
.clothes-container {
  flex: 1;
  padding: 15px;
  margin-bottom: 65px; /* 为底部工具栏留出空间 */
  box-sizing: border-box;
  height: calc(100vh - 90px - 45px - 65px); /* 调整高度计算：视口高度减去信息栏、标签栏和底部工具栏的高度 */
}

.clothes-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

/* 四列网格布局 */
.clothes-grid.layout-mode-4 {
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.clothes-item {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  aspect-ratio: 3/4;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  box-sizing: border-box;
}

/* 四列布局下的衣物项 */
.layout-mode-4 .clothes-item {
  aspect-ratio: 1/1.5;
}

.clothes-img {
  width: 90%;
  height: 90%;
  object-fit: contain;
  border-radius: 10px;
}

/* 四列布局下的图片尺寸 */
.layout-mode-4 .clothes-img {
  width: 85%;
  height: 75%;
}

/* 四列布局下选择图标的尺寸调整 */
.layout-mode-4 .select-icon {
  width: 20px;
  height: 20px;
  border-radius: 10px;
  top: 5px;
  right: 5px;
  font-size: 10px;
}

/* 四列布局下衣物名称的样式调整 */
.layout-mode-4 .clothes-name {
  padding: 4px;
  font-size: 10px;
}

/* 衣物名称样式 */
.clothes-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
  max-width: 100%;
}

.clothes-item.selected::after {
  content: '';
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.3);
  border: 2px solid #000000;
  border-radius: 12px;
  box-sizing: border-box;
}

.clothes-item.selected .select-icon {
  display: flex;
}

.select-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #000000;
  color: white;
  display: none;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.check-icon {
  color: #ffffff;
  font-weight: bold;
}

/* 空状态提示 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 40px;
  margin-bottom: 15px;
}

.empty-text {
  color: #999;
  margin-bottom: 20px;
}

.add-btn {
  background-color: #000000;
  color: #ffffff;
  font-size: 14px;
  padding: 10px 20px;
  border-radius: 20px;
}

/* 加载提示 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #000000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666666;
  font-size: 14px;
}

/* 编辑工具栏 */
.edit-toolbar {
  height: 65px;
  background-color: #ffffff;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 0 20px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
}

.edit-btn, .move-btn, .delete-btn {
  padding: 10px 25px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.edit-btn, .move-btn {
  background-color: #f5f5f5;
  color: #333333;
}

.delete-btn {
  background-color: #000000;
  color: #ffffff;
}

.icon-edit, .icon-move, .icon-delete {
  margin-right: 5px;
}

/* 底部按钮 */
.bottom-btn-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px 20px;
  background-color: #ffffff;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  z-index: 99;
}

.add-clothing-btn {
  background-color: #000000;
  color: #ffffff;
  font-size: 14px;
  padding: 10px 0;
  width: 90%;
  border-radius: 24px;
  text-align: center;
}

/* 衣柜选择弹窗 */
.wardrobe-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.wardrobe-picker-content {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  margin-top: auto;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.wardrobe-picker-header {
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f5f5f5;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.wardrobe-picker-close {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: -16rpx;
}

.wardrobe-picker-body {
  flex: 1;
  overflow-y: auto;
  padding: 24rpx 32rpx;
  max-height: 60vh;
}

.loading-wardrobes {
  padding: 48rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.loading-wardrobes .icon-loading {
  font-size: 48rpx;
  color: #333;
}

.no-wardrobes {
  text-align: center;
  padding: 48rpx 24rpx;
  color: #999;
  font-size: 28rpx;
}

.wardrobe-item {
  padding: 32rpx 24rpx;
  background: #f8f8f8;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s;
}

.wardrobe-item:active {
  background: #f0f0f0;
  transform: scale(0.98);
}

.wardrobe-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.wardrobe-count {
  font-size: 24rpx;
  color: #666;
  background: #fff;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
}

/* 分类选择弹窗 */
.category-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.3s ease-out;
}

.category-picker-content {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  margin-top: auto;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

.category-picker-header {
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f5f5f5;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.category-picker-close {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: -16rpx;
}

.category-picker-body {
  flex: 1;
  overflow-y: auto;
  padding: 24rpx 32rpx;
  max-height: 60vh;
}

.loading-categories {
  padding: 48rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.loading-categories .icon-loading {
  font-size: 48rpx;
  color: #333;
}

.no-categories {
  text-align: center;
  padding: 48rpx 24rpx;
  color: #999;
  font-size: 28rpx;
}

.category-item {
  padding: 32rpx 24rpx;
  background: #f8f8f8;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s;
}

.category-item:active {
  background: #f0f0f0;
  transform: scale(0.98);
}

.category-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.category-tag {
  font-size: 20rpx;
  color: #666;
  background: #e8e8e8;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.category-tag.custom-tag {
  background: #e6f7ff;
  color: #1890ff;
}

@font-face {
  font-family: 'iconfont';
  src: url('data:font/woff2;charset=utf-8;base64,d09GMgABAAAAAALcAAsAAAAABpQAAAKPAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACDHAqBEIEYATYCJAMQCwoABCAFhGcHSxuXBciemjwJKAWKAIqD4Pm39t7dfxGRJJo0s0QiQSKRxEuFRoMQCZVK6IT/f27/c2EmQ6bKPvk8ZVahqUv3wlJIgkn4P8dMl8+D5besuaRGS/fRA44SGtjYI6uABOaNYXcRD3CeQLtxndiOo7MrUCjjXoG4DYU+UOSUcsltqDW1JWsW8QxNbXqQXgOP0ffj31IUktqMu/TkRtcIjX+VvhqJ+P/InogQ4OZzwGyQsQ4oxM1S/zlhwThBO8ZU9boCvkqD+Cr9/88jdhWr/ccjJVHjbtkNxqCq4atUUXyVSUXxjVQEeAvtgHaXJMnuVuO+4c3N3tn13tn1wfn10cXN+ZWXp3u7J1fHlxe3FwDw8Qkg0xWp0lWrzk4TXz3Lrr16fJ0Fg6CvV+lKU626OL8+v0rWXz0+efvw/Ob8+PL0+PJsC1hZDnT1BwZc9IFb/0HvD+D5MwAePr0FH95/+vz+w7v3b+Dx+/uHt+8+3H/6Cf8/fv7fEL4B/x++fvt5//nHt/uf3wHPn7/9vH0EkJZfzp3KzJnVlF/OnszMnVX7S1UCiB/+r3oFIP1vRQr8VB4gLOQvqWUZZIWtQ6PDBrTU7UG7NcPLO4wICxktWLVIQLjkGJJzXiG75BaywnagcckhtFwKhe3OIrpjhxlISxxFqcSEuhVyhlxN5mRrkVj0j0JtM+XKXoj8UEjfQ6NcTIo+sEbsoUX2xRQpJXJMJuA+OI5M04AjMoE8owFxqXkxz6qpyNUkZ0gVxVFIUhQjpLYCcQw5NTJvni0i8/v/kJCtRqpyT7n0ByXS7kEjC2ZAPzCtsXqvtSj7xCgkScJhZASwHjgcYjQa4L6PCOQxZEBwUmqvyKNSpaI6v8p/3w60c19xpMhRos3NKrCeaUJNqlxNtipPWZQBAA==') format('woff2');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-close:before {
  content: "\e6a7";
}

.icon-loading:before {
  content: "\e6a8";
  display: inline-block;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
} 