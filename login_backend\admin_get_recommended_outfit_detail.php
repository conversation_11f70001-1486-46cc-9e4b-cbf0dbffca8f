<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 验证管理员权限
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证管理员token
$adminData = $auth->verifyAdminToken($token);
if (!$adminData) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取数据库连接
$db = new Database();
$conn = $db->getConnection();

// 验证是否提供了穿搭ID
if (!isset($_GET['id']) || empty($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少穿搭ID']);
    exit();
}

$outfitId = intval($_GET['id']);

try {
    // 查询穿搭基本信息
    $query = "SELECT ro.*, oc.name as category_name 
              FROM recommended_outfits ro 
              LEFT JOIN outfit_categories oc ON ro.category_id = oc.id 
              WHERE ro.id = ?";
    
    $stmt = $conn->prepare($query);
    $stmt->bindValue(1, $outfitId);
    $stmt->execute();
    $outfit = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$outfit) {
        http_response_code(404);
        echo json_encode(['error' => true, 'msg' => '找不到指定的推荐穿搭']);
        exit();
    }
    
    // 查询穿搭商品列表
    $itemsQuery = "SELECT * FROM recommended_outfit_items 
                   WHERE outfit_id = ? 
                   ORDER BY sort_order ASC, id ASC";
    
    $itemsStmt = $conn->prepare($itemsQuery);
    $itemsStmt->bindValue(1, $outfitId);
    $itemsStmt->execute();
    $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 查询统计数据
    $statsQuery = "SELECT view_count, copy_link_count, last_viewed_at 
                  FROM recommended_outfit_stats 
                  WHERE outfit_id = ? LIMIT 1";
    
    $statsStmt = $conn->prepare($statsQuery);
    $statsStmt->bindValue(1, $outfitId);
    $statsStmt->execute();
    $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($stats) {
        $outfit['view_count'] = $stats['view_count'];
        $outfit['copy_link_count'] = $stats['copy_link_count'];
        $outfit['last_viewed_at'] = $stats['last_viewed_at'];
    } else {
        $outfit['view_count'] = 0;
        $outfit['copy_link_count'] = 0;
        $outfit['last_viewed_at'] = null;
    }
    
    // 将商品列表添加到穿搭对象中
    $outfit['items'] = $items;
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'data' => $outfit
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '数据库错误: ' . $e->getMessage()]);
} 