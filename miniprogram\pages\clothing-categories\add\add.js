const app = getApp();

Page({
  data: {
    name: '',
    sortOrder: 8 // 默认排序为8，在系统分类之后
  },

  onLoad: function (options) {
    // 页面加载
  },

  onReady: function () {
    // 页面初次渲染完成
  },

  onShow: function () {
    // 页面显示
  },

  onHide: function () {
    // 页面隐藏
  },

  onUnload: function () {
    // 页面卸载
  },

  // 输入分类名称
  onNameInput: function(e) {
    this.setData({
      name: e.detail.value.trim()
    });
  },

  // 输入排序序号
  onSortOrderInput: function(e) {
    const value = parseInt(e.detail.value) || 8;
    this.setData({
      sortOrder: value
    });
  },

  // 取消
  onCancel: function() {
    wx.navigateBack();
  },

  // 提交
  onSubmit: function() {
    const { name, sortOrder } = this.data;

    // 验证数据
    if (!name) {
      wx.showToast({
        title: '请输入分类名称',
        icon: 'none'
      });
      return;
    }

    if (name.length > 30) {
      wx.showToast({
        title: '分类名称不能超过30个字符',
        icon: 'none'
      });
      return;
    }

    const token = app.globalData.token;
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '创建中...'
    });

    wx.request({
      url: `${app.globalData.apiBaseUrl}/add_clothing_category.php`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      data: {
        name: name,
        sort_order: sortOrder
      },
      success: (res) => {
        wx.hideLoading();
        
        console.log('添加分类响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          wx.showToast({
            title: '创建成功',
            icon: 'success'
          });
          
          // 设置全局刷新标志
          app.globalData.needRefreshCategories = true;
          
          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1000);
        } else {
          wx.showToast({
            title: res.data.msg || '创建失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('添加分类失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  }
}); 