const app = getApp();
// 创建激励视频广告实例
// let rewardedVideoAd = null;

Page({
  data: {
    progressStatus: 'queued',
    progressPercent: 0,
    progressPercentStr: '0%',
    progressMessage: '排队中...',
    countdownTime: 30,
    merchantId: null
  },

  onLoad: function(options) {
    console.log("试穿页面接收到参数:", options);
    console.log("试穿页面接收到商家ID参数:", options ? options.merchant_id : "无");
    
    // 清除上一次的试衣结果，避免显示旧数据
    app.globalData.tryOnResult = null;
    
    // 如果URL参数包含merchant_id，保存到data中
    if (options && options.merchant_id) {
      this.setData({
        merchantId: parseInt(options.merchant_id, 10)
      });
      console.log("试穿页面保存商家ID到this.data:", parseInt(options.merchant_id, 10));
    } else {
      // 尝试从全局状态获取商家ID
      if (app.globalData.selectedMerchantId) {
        this.setData({
          merchantId: parseInt(app.globalData.selectedMerchantId, 10)
        });
        console.log("试穿页面从全局状态获取商家ID:", parseInt(app.globalData.selectedMerchantId, 10));
      }
    }
    
    // 确保选择了照片和衣物
    if (!app.globalData.selectedTryOnPhoto || !app.globalData.selectedClothes || app.globalData.selectedClothes.length === 0) {
      wx.showToast({
        title: '缺少必要参数',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    
    // 初始化激励视频广告
    // this.initRewardedVideoAd();
    
    // 检查是否需要直接显示广告
    if (options && options.showAd === 'true') {
      console.log("检测到showAd参数，直接显示广告");
      // setTimeout(() => {
      //   this.showRewardedVideoAd();
      // }, 500);
      // 直接返回，不显示广告
      wx.showToast({
        title: '广告功能已停用',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return; // 先不开始试衣流程，等广告观看完成后再开始
    }
    
    // 开始试穿流程
    this.startTryOn();
  },
  
  // 初始化激励视频广告
  initRewardedVideoAd: function() {
    if (wx.createRewardedVideoAd) {
      rewardedVideoAd = wx.createRewardedVideoAd({
        adUnitId: 'adunit-feb43eda47b7263e'
      });
      
      // 监听加载事件
      rewardedVideoAd.onLoad(() => {
        console.log('激励视频 广告加载成功');
      });
      
      // 监听错误事件
      rewardedVideoAd.onError((err) => {
        console.error('激励视频 广告加载失败', err);
      });
      
      // 监听关闭事件
      rewardedVideoAd.onClose((res) => {
        // 用户点击了【关闭广告】按钮
        if (res && res.isEnded) {
          // 正常播放结束，可以下发游戏奖励
          console.log('激励视频广告完整观看，发放奖励');
          this.addTryOnCountFromAd();
        } else {
          // 播放中途退出，不下发游戏奖励
          console.log('激励视频广告未完整观看，不发放奖励');
          wx.showToast({
            title: '需要完整观看广告才能获得奖励',
            icon: 'none'
          });
          
          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      });
    }
  },
  
  // 显示激励视频广告
  showRewardedVideoAd: function() {
    if (rewardedVideoAd) {
      rewardedVideoAd.show().catch(() => {
        // 失败重试
        rewardedVideoAd.load()
          .then(() => rewardedVideoAd.show())
          .catch(err => {
            console.log('激励视频 广告显示失败');
            wx.showToast({
              title: '广告加载失败，请稍后重试',
              icon: 'none'
            });
          });
      });
    } else {
      wx.showToast({
        title: '广告组件未初始化',
        icon: 'none'
      });
    }
  },
  
  // 通过观看广告增加试衣次数
  addTryOnCountFromAd: function() {
    wx.showLoading({
      title: '正在获取奖励...',
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/watch_ad_for_try_on.php`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': app.globalData.token
      },
      data: {
        completed: 1
      },
      success: (res) => {
        wx.hideLoading();
        console.log("获取广告奖励响应:", res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          wx.showToast({
            title: res.data.msg || '获得1次试衣机会',
            icon: 'success'
          });
          
          // 如果是从选择页面跳转来观看广告，则获取奖励后返回上一页
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];
          const options = currentPage.options || {};
          
          if (options.showAd === 'true') {
            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          } else {
            // 否则重新开始试衣流程
            setTimeout(() => {
              this.startTryOn();
            }, 1500);
          }
        } else {
          wx.showToast({
            title: res.data.msg || '获取奖励失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error("获取广告奖励请求失败:", err);
        
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 开始试穿流程
  startTryOn: function() {
    const photo = app.globalData.selectedTryOnPhoto;
    const clothes = app.globalData.selectedClothes;
    
    // 确保在新的试衣流程开始前清除上一次的结果
    app.globalData.tryOnResult = null;
    
    // 验证图片和衣物数据
    if (!photo || !photo.id || !photo.image_url) {
      console.error("照片数据无效:", photo);
      wx.showModal({
        title: '试穿失败',
        content: '照片数据无效，请重新选择照片',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    if (!clothes || !clothes.length || !clothes[0].id || !clothes[0].image_url) {
      console.error("衣物数据无效:", clothes);
      wx.showModal({
        title: '试穿失败',
        content: '衣物数据无效，请重新选择衣物',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    // 启动进度动画
    this.startProgressAnimation();
    
    // 输出详细日志
    console.log("试穿请求数据:");
    console.log("- 照片ID:", photo.id);
    console.log("- 照片URL:", photo.image_url);
    console.log("- 衣物IDs:", clothes.map(item => item.id));
    console.log("- 衣物URLs:", clothes.map(item => item.image_url));
    
    // 检查是否有图片信息
    if (photo._image_info || clothes[0]._image_info) {
      console.log("图片格式信息:");
      console.log("- 照片:", photo._image_info ? photo._image_info.type : "未知");
      console.log("- 衣物:", clothes[0]._image_info ? clothes[0]._image_info.type : "未知");
    }
    
    // 首先调用衣物分类API识别衣物类型
    console.log("开始调用衣物分类API...");
    this.identifyClothingType(clothes[0].image_url, (clothingType) => {
      console.log("AI识别的衣物类型:", clothingType);
      
      // 构建请求数据
      const requestData = {
        photo_id: photo.id,
        clothes_ids: clothes.map(item => item.id),
        // 添加额外信息帮助后端处理
        photo_url: photo.image_url,
        clothes_url: clothes[0].image_url,
        photo_format: photo._image_info ? photo._image_info.type : '',
        clothes_format: clothes[0]._image_info ? clothes[0]._image_info.type : '',
        // 添加AI识别的衣物类型
        clothing_type: clothingType
      };
      
      // 输出当前商家ID状态
      console.log("构建请求前商家ID状态:", {
        dataStateId: this.data.merchantId,
        globalStateId: app.globalData.selectedMerchantId,
        clothingMerchantId: clothes[0].merchant ? clothes[0].merchant.id : null
      });
      
      // 判断衣物所有权 - 检查衣物是否属于商家
      const isMerchantClothing = clothes[0].merchant && clothes[0].merchant.id || 
                                 clothes[0].is_merchant_clothes === true || 
                                 this.data.merchantId || 
                                 app.globalData.selectedMerchantId;
                                 
      console.log("衣物所有权判断:", isMerchantClothing ? "商家衣物" : "用户自己的衣物");
      
      // 仅在确认是商家衣物时添加merchant_id参数
      if (isMerchantClothing) {
        // 确定使用哪个商家ID (优先级: 衣物数据 > 页面数据 > 全局数据)
        let merchantId = null;
        
        if (clothes[0].merchant && clothes[0].merchant.id) {
          merchantId = parseInt(clothes[0].merchant.id, 10);
          console.log("使用衣物数据中的商家ID:", merchantId);
        } else if (this.data.merchantId) {
          merchantId = parseInt(this.data.merchantId, 10);
          console.log("使用页面数据中的商家ID:", merchantId);
        } else if (app.globalData.selectedMerchantId) {
          merchantId = parseInt(app.globalData.selectedMerchantId, 10);
          console.log("使用全局状态中的商家ID:", merchantId);
        }
        
        if (merchantId) {
          requestData.merchant_id = merchantId;
          console.log("添加商家ID到试穿请求:", merchantId);
        }
      } else {
        console.log("试穿用户自己的衣物，不添加商家ID参数");
        // 确保清除全局状态中的商家ID，避免影响后续试穿
        app.globalData.selectedMerchantId = null;
      }
      
      console.log("试穿API请求数据:", JSON.stringify(requestData));
      
      // 调用试衣API
      wx.request({
        url: `${app.globalData.apiBaseUrl}/try_on.php`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': app.globalData.token
        },
        data: requestData,
        success: (res) => {
          console.log("试衣API响应:", res.data);
          
          if (res.statusCode === 200 && !res.data.error) {
            // 保存试衣结果
            app.globalData.tryOnResult = res.data.data;
            
            // 如果进度已到100%，跳转到结果页
            if (this.data.progressPercent >= 100) {
              this.navigateToResult();
            }
          } else {
            // 处理错误
            let errorMsg = res.data.msg || '试穿失败，请重试';
            let showPurchaseButton = false;
            let showAdButton = false;
            
            // 判断是否显示购买按钮
            if (res.data.limit_exceeded && res.data.show_purchase_button) {
              showPurchaseButton = true;
              // 不再显示广告按钮
              // showAdButton = true; // 显示广告按钮
              showAdButton = false;
              // 只有当API没有返回有意义的错误信息时，才显示通用的次数不足提示
              if (!res.data.msg || res.data.msg.includes('次数已用完')) {
                errorMsg = '试衣次数已用完，请购买更多次数';
              }
            }
            
            // 如果收到特定的图片格式错误，提供更具体的提示
            if (res.data.msg && res.data.msg.includes('图片') && 
                (res.data.msg.includes('无效') || res.data.msg.includes('格式'))) {
              errorMsg = '图片格式不支持，请确保使用JPG、PNG或WEBP格式的图片，并且图片清晰可见';
            }
            
            // 如果需要显示购买按钮和广告按钮，使用showActionSheet显示多个选项
            if (showPurchaseButton && showAdButton) {
              // 先显示错误信息
              wx.showModal({
                title: '试穿失败',
                content: errorMsg,
                showCancel: false,
                confirmText: '我知道了',
                success: () => {
                  // 判断是否已达到广告观看限制
                  console.log("操作菜单广告限制详情:", {
                    ad_limit_reached: res.data.ad_limit_reached,
                    show_ad_button: res.data.show_ad_button
                  });
                  
                  // 统一显示单一选项
                  wx.showModal({
                    title: '购买次数',
                    content: '您今日的试衣次数已用完，可以购买更多次数继续使用。',
                    showCancel: true,
                    cancelText: '取消',
                    confirmText: '购买次数',
                    success: (result) => {
                      if (result.confirm) {
                        // 选择购买次数
                        wx.navigateTo({
                          url: '/pages/purchase/try_on_count/index'
                        });
                      } else {
                        // 选择返回
                        wx.navigateBack();
                      }
                    }
                  });
                }
              });
            } else {
              // 使用常规的showModal弹框
              wx.showModal({
                title: '试穿失败',
                content: errorMsg,
                showCancel: showPurchaseButton,
                cancelText: '返回',
                confirmText: showPurchaseButton ? '购买次数' : '确定',
                success: (result) => {
                  if (result.confirm && showPurchaseButton) {
                    // 跳转到购买页面
                    wx.navigateTo({
                      url: '/pages/purchase/try_on_count/index'
                    });
                  } else {
                    // 返回上一页
                    wx.navigateBack();
                  }
                }
              });
            }
          }
        },
        fail: (err) => {
          console.error("试穿请求失败:", err);
          
          wx.showModal({
            title: '网络错误',
            content: '请检查网络连接后重试',
            showCancel: false,
            success: () => {
              wx.navigateBack();
            }
          });
        }
      });
    });
  },

  // 调用衣物分类API识别衣物类型
  identifyClothingType: function(imageUrl, callback) {
    // 显示加载提示
    wx.showLoading({
      title: '识别衣物类型...',
      mask: true
    });

    console.log("正在识别衣物类型:", imageUrl);

    // 调用衣物分类API
    wx.request({
      url: 'https://www.furrywoo.com/geminicdai/ywlx.php',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        image_url: imageUrl
      },
      success: (res) => {
        wx.hideLoading();
        console.log("衣物分类API响应:", res.data);

        if (res.statusCode === 200 && res.data.success && res.data.clothing_type) {
          // 返回识别到的衣物类型
          callback(res.data.clothing_type);
        } else {
          console.error("衣物分类失败:", res);
          // 如果识别失败，默认为上装
          callback('Upper');
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error("衣物分类请求失败:", err);
        // 记录更详细的错误信息，帮助诊断
        if (err.errMsg && err.errMsg.includes('url not in domain list')) {
          console.error("请在小程序管理后台添加域名: https://www.furrywoo.com");
        }
        // 如果请求失败，默认为上装
        callback('Upper');
      }
    });
  },
  
  // 进度条动画
  startProgressAnimation: function() {
    // 初始状态
    let status = 'queued';
    let percent = 0;
    
    // 按照阶段更新进度
    const updateProgress = () => {
      // 根据当前状态决定下一步
      switch (status) {
        case 'queued':
          // 排队阶段持续2秒，然后进入上传阶段
          percent = 25;
          status = 'uploading';
          this.setData({
            progressStatus: status,
            progressPercent: percent,
            progressPercentStr: percent + '%',
            progressMessage: '提交数据...'
          });
          setTimeout(updateProgress, 8000);
          break;
          
        case 'uploading':
          // 上传阶段持续2秒，然后进入处理阶段
          percent = 50;
          status = 'processing';
          this.setData({
            progressStatus: status,
            progressPercent: percent,
            progressPercentStr: percent + '%',
            progressMessage: '合成中...'
          });
          setTimeout(updateProgress, 12000);
          break;
          
        case 'processing':
          // 处理阶段持续3秒，然后进入完成阶段
          percent = 80;
          status = 'finishing';
          this.setData({
            progressStatus: status,
            progressPercent: percent,
            progressPercentStr: percent + '%',
            progressMessage: '即将完成...'
          });
          setTimeout(updateProgress, 8000);
          break;
          
        case 'finishing':
          // 完成阶段
          percent = 100;
          status = 'completed';
          this.setData({
            progressStatus: status,
            progressPercent: percent,
            progressPercentStr: percent + '%',
            progressMessage: '完成'
          });
          
          // 检查结果是否已经准备好
          if (app.globalData.tryOnResult) {
            this.navigateToResult();
          }
          break;
      }
    };
    
    // 启动进度更新
    setTimeout(updateProgress, 1500);
  },
  
  // 跳转到结果页
  navigateToResult: function() {
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/try_on/result/index'
      });
    }, 500);
  }
}); 