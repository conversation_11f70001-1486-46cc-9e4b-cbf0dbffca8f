<?php
/**
 * 深度调试分类显示问题
 * 全面检查数据库状态和API执行流程
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "=== 深度调试分类显示问题 ===\n";
    echo "当前用户ID: $userId\n";
    echo "调试时间: " . date('Y-m-d H:i:s') . "\n\n";
    
    // 1. 检查数据库表结构
    echo "1. 检查数据库表结构:\n";
    $stmt = $conn->prepare("DESCRIBE clothing_categories");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "clothing_categories表结构:\n";
    foreach ($columns as $col) {
        echo "- {$col['Field']}: {$col['Type']} " . ($col['Null'] === 'YES' ? 'NULL' : 'NOT NULL') . "\n";
    }
    echo "\n";
    
    // 2. 检查用户圈子状态
    echo "2. 检查用户圈子状态:\n";
    $stmt = $conn->prepare("
        SELECT cm.circle_id, cm.status, cm.role, cm.joined_at,
               c.name as circle_name, c.status as circle_status, c.created_by
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id
        ORDER BY cm.joined_at DESC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $userCircles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($userCircles)) {
        echo "用户不在任何圈子中\n";
        exit;
    }
    
    foreach ($userCircles as $circle) {
        echo "- 圈子ID: {$circle['circle_id']}\n";
        echo "  名称: {$circle['circle_name']}\n";
        echo "  用户状态: {$circle['status']}\n";
        echo "  用户角色: {$circle['role']}\n";
        echo "  圈子状态: {$circle['circle_status']}\n";
        echo "  创建者: {$circle['created_by']}\n";
        echo "  加入时间: {$circle['joined_at']}\n\n";
    }
    
    // 获取活跃圈子
    $activeCircles = [];
    foreach ($userCircles as $circle) {
        if ($circle['status'] === 'active' && $circle['circle_status'] === 'active') {
            $activeCircles[] = $circle['circle_id'];
        }
    }
    
    if (empty($activeCircles)) {
        echo "用户没有活跃的圈子\n";
        exit;
    }
    
    echo "活跃圈子ID: " . implode(', ', $activeCircles) . "\n\n";
    $circleId = $activeCircles[0];
    
    // 3. 检查圈子成员和他们的分类
    echo "3. 检查圈子成员和他们的分类:\n";
    $stmt = $conn->prepare("
        SELECT cm.user_id, cm.status, cm.role, u.nickname
        FROM circle_members cm
        LEFT JOIN users u ON cm.user_id = u.id
        WHERE cm.circle_id = :circle_id
        ORDER BY cm.role DESC, cm.joined_at ASC
    ");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($members as $member) {
        $memberId = $member['user_id'];
        $memberName = $member['nickname'];
        $isCurrent = $memberId == $userId;
        
        echo "3.{$memberId} 用户: $memberName" . ($isCurrent ? ' (当前用户)' : '') . "\n";
        echo "  状态: {$member['status']}, 角色: {$member['role']}\n";
        
        // 检查该用户的所有分类
        $stmt = $conn->prepare("
            SELECT c.id, c.name, c.code, c.is_system, c.circle_id, c.created_at
            FROM clothing_categories c
            WHERE c.user_id = :user_id
            ORDER BY c.is_system DESC, c.created_at DESC
        ");
        $stmt->bindParam(':user_id', $memberId, PDO::PARAM_INT);
        $stmt->execute();
        $userCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($userCategories)) {
            echo "  - 没有任何分类\n";
        } else {
            foreach ($userCategories as $cat) {
                $type = $cat['is_system'] ? '系统' : '自定义';
                $syncStatus = $cat['circle_id'] ? "圈子{$cat['circle_id']}" : "个人";
                echo "  - [$type] {$cat['name']} (code: {$cat['code']}, ID: {$cat['id']}) - $syncStatus\n";
            }
        }
        echo "\n";
    }
    
    // 4. 直接查询数据库中的分类数据
    echo "4. 直接查询数据库中的分类数据:\n";
    
    // 4.1 所有分类
    $stmt = $conn->prepare("
        SELECT COUNT(*) as total_count,
               COUNT(CASE WHEN is_system = 1 THEN 1 END) as system_count,
               COUNT(CASE WHEN is_system = 0 THEN 1 END) as custom_count
        FROM clothing_categories
    ");
    $stmt->execute();
    $totalStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "4.1 全局分类统计:\n";
    echo "- 总分类数: {$totalStats['total_count']}\n";
    echo "- 系统分类: {$totalStats['system_count']}\n";
    echo "- 自定义分类: {$totalStats['custom_count']}\n\n";
    
    // 4.2 圈子中的分类
    $stmt = $conn->prepare("
        SELECT COUNT(*) as total_count,
               COUNT(CASE WHEN is_system = 1 THEN 1 END) as system_count,
               COUNT(CASE WHEN is_system = 0 THEN 1 END) as custom_count,
               COUNT(CASE WHEN is_system = 0 AND user_id = :user_id THEN 1 END) as own_custom_count,
               COUNT(CASE WHEN is_system = 0 AND user_id != :user_id THEN 1 END) as others_custom_count
        FROM clothing_categories
        WHERE circle_id = :circle_id
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->execute();
    $circleStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "4.2 圈子{$circleId}中的分类统计:\n";
    echo "- 总分类数: {$circleStats['total_count']}\n";
    echo "- 系统分类: {$circleStats['system_count']}\n";
    echo "- 自定义分类: {$circleStats['custom_count']}\n";
    echo "- 自己的自定义分类: {$circleStats['own_custom_count']}\n";
    echo "- 其他用户的自定义分类: {$circleStats['others_custom_count']}\n\n";
    
    // 4.3 列出圈子中的具体分类
    echo "4.3 圈子中的具体分类列表:\n";
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.code, c.is_system, c.user_id, c.created_at, u.nickname as creator
        FROM clothing_categories c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.circle_id = :circle_id
        ORDER BY c.is_system DESC, c.user_id, c.created_at DESC
    ");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->execute();
    $circleCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($circleCategories)) {
        echo "圈子中没有任何分类\n";
    } else {
        foreach ($circleCategories as $cat) {
            $type = $cat['is_system'] ? '系统' : '自定义';
            $isOwner = $cat['user_id'] == $userId ? ' (自己)' : '';
            echo "- [$type] {$cat['name']} (code: {$cat['code']}, ID: {$cat['id']}) - 创建者: {$cat['creator']}{$isOwner}\n";
        }
    }
    echo "\n";
    
    // 5. 模拟API的完整执行流程
    echo "5. 模拟API的完整执行流程:\n";
    
    // 5.1 模拟获取圈子ID的查询
    echo "5.1 获取用户活跃圈子:\n";
    $circleQuery = "SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'";
    echo "SQL: $circleQuery\n";
    echo "参数: user_id = $userId\n";
    
    $circleStmt = $conn->prepare($circleQuery);
    $circleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $circleStmt->execute();
    $apiUserCircles = $circleStmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "结果: " . json_encode($apiUserCircles) . "\n\n";
    
    // 5.2 构建和执行分类查询
    echo "5.2 构建分类查询:\n";
    
    if (empty($apiUserCircles)) {
        echo "用户不在任何活跃圈子中，只返回系统分类\n";
        $apiSql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                          u.nickname as creator_nickname, 'system' as data_source
                   FROM clothing_categories c
                   LEFT JOIN users u ON c.user_id = u.id
                   WHERE c.is_system = 1 AND c.user_id = :user_id
                   ORDER BY c.sort_order ASC, c.created_at ASC";
    } else {
        $apiCircleIds = implode(',', $apiUserCircles);
        echo "用户在圈子中，圈子ID: $apiCircleIds\n";
        $apiSql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                          u.nickname as creator_nickname,
                          CASE
                              WHEN c.is_system = 1 THEN 'system'
                              WHEN c.circle_id IS NULL THEN 'personal'
                              ELSE 'shared'
                          END as data_source
                   FROM clothing_categories c
                   LEFT JOIN users u ON c.user_id = u.id
                   WHERE (
                       (c.is_system = 1 AND c.user_id = :user_id) OR
                       (c.is_system = 0 AND c.user_id != :user_id AND c.circle_id IN ($apiCircleIds))
                   )
                   ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC";
    }
    
    echo "SQL查询:\n$apiSql\n\n";
    echo "参数: user_id = $userId\n\n";
    
    // 5.3 执行查询
    echo "5.3 执行查询:\n";
    try {
        $apiStmt = $conn->prepare($apiSql);
        $apiStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $apiStmt->execute();
        $apiResult = $apiStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "查询成功，返回 " . count($apiResult) . " 条记录\n";
        
        $systemCount = 0;
        $customCount = 0;
        
        foreach ($apiResult as $cat) {
            if ($cat['is_system']) {
                $systemCount++;
                echo "- [系统] {$cat['name']} (code: {$cat['code']}, data_source: {$cat['data_source']})\n";
            } else {
                $customCount++;
                echo "- [自定义] {$cat['name']} (code: {$cat['code']}, 创建者: {$cat['creator_nickname']}, data_source: {$cat['data_source']})\n";
            }
        }
        
        echo "\n查询结果统计:\n";
        echo "- 系统分类: $systemCount 个\n";
        echo "- 自定义分类: $customCount 个\n\n";
        
    } catch (Exception $e) {
        echo "查询执行失败: " . $e->getMessage() . "\n\n";
    }
    
    // 6. 问题分析和诊断
    echo "6. 问题分析和诊断:\n";
    
    if ($circleStats['others_custom_count'] == 0) {
        echo "❌ 根本问题：圈子中没有其他用户的自定义分类\n";
        echo "可能原因:\n";
        echo "1. 其他用户没有创建自定义分类\n";
        echo "2. 其他用户的自定义分类没有同步到圈子\n";
        echo "3. 数据同步过程有问题\n\n";
        
        echo "建议检查:\n";
        echo "- 运行 fix_category_sync.php?action=check 检查同步状态\n";
        echo "- 让其他用户创建一些自定义分类并同步\n";
        
    } elseif (isset($customCount) && $customCount == 0) {
        echo "❌ API查询问题：圈子中有其他用户的自定义分类，但API查询返回0\n";
        echo "可能原因:\n";
        echo "1. SQL查询逻辑有错误\n";
        echo "2. 参数绑定有问题\n";
        echo "3. 权限检查有问题\n\n";
        
    } elseif (isset($customCount) && $customCount > 0) {
        echo "✅ API查询正常：返回了 $customCount 个自定义分类\n";
        echo "如果前端仍然看不到，可能是前端处理问题\n\n";
        
    } else {
        echo "⚠️ 需要进一步检查API执行状态\n\n";
    }
    
    // 7. 提供修复建议
    echo "7. 修复建议:\n";
    
    if ($circleStats['others_custom_count'] == 0) {
        echo "1. 检查数据同步状态: GET /fix_category_sync.php?action=check\n";
        echo "2. 如果需要，执行同步修复: GET /fix_category_sync.php?action=fix\n";
        echo "3. 让其他圈子成员创建一些自定义分类进行测试\n";
    } else {
        echo "1. 检查API日志，查看是否有错误信息\n";
        echo "2. 直接调用分类API进行测试\n";
        echo "3. 检查前端的分类数据处理逻辑\n";
    }
    
    echo "\n=== 深度调试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
?>
