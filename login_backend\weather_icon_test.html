<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>和风天气图标测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/qweather-icons@1.1.1/font/qweather-icons.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            max-width: 960px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f7f7f7;
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            margin-bottom: 10px;
        }
        
        .section {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.05);
        }
        
        .weather-display {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }
        
        .weather-icon {
            font-size: 60px;
            margin-right: 20px;
        }
        
        .weather-info {
            display: flex;
            flex-direction: column;
        }
        
        .weather-temp {
            font-size: 36px;
            font-weight: 600;
        }
        
        .weather-text {
            font-size: 16px;
            color: #666;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 10px;
            transition: all 0.3s;
        }
        
        .icon-item:hover {
            background-color: #f9f9f9;
            transform: translateY(-2px);
            box-shadow: 0 3px 10px rgba(0,0,0,0.06);
        }
        
        .icon-display {
            font-size: 36px;
            margin-bottom: 10px;
        }
        
        .icon-code {
            font-size: 12px;
            color: #666;
        }
        
        .form {
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-button {
            background-color: #000;
            color: #fff;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            font-size: 14px;
            cursor: pointer;
        }
        
        .form-button:hover {
            background-color: #333;
        }
        
        .notes {
            margin-top: 20px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>和风天气图标测试</h1>
        <p>测试显示API返回的天气图标</p>
    </div>
    
    <div class="section">
        <h2>获取当前天气</h2>
        <div class="form">
            <div class="form-group">
                <label class="form-label" for="location">位置</label>
                <input type="text" id="location" class="form-input" placeholder="输入城市名称，如：北京" value="北京">
            </div>
            <button id="getWeather" class="form-button">获取天气数据</button>
        </div>
        
        <div id="weatherDisplay" class="weather-display" style="display: none;">
            <div id="weatherIcon" class="weather-icon"></div>
            <div class="weather-info">
                <div id="weatherTemp" class="weather-temp">--°C</div>
                <div id="weatherText" class="weather-text">天气状况</div>
                <div id="iconInfo" class="notes"></div>
            </div>
        </div>
        
        <div class="notes">
            <p>注意：此页面会调用本站API获取天气数据，并显示对应的图标。</p>
            <p>如果图标无法显示，请查看控制台错误信息。</p>
        </div>
    </div>
    
    <div class="section">
        <h2>常用天气图标示例</h2>
        <div class="icon-grid">
            <div class="icon-item">
                <i class="qi-100 icon-display"></i>
                <span class="icon-code">qi-100: 晴天</span>
            </div>
            <div class="icon-item">
                <i class="qi-101 icon-display"></i>
                <span class="icon-code">qi-101: 多云</span>
            </div>
            <div class="icon-item">
                <i class="qi-104 icon-display"></i>
                <span class="icon-code">qi-104: 阴</span>
            </div>
            <div class="icon-item">
                <i class="qi-305 icon-display"></i>
                <span class="icon-code">qi-305: 小雨</span>
            </div>
            <div class="icon-item">
                <i class="qi-306 icon-display"></i>
                <span class="icon-code">qi-306: 中雨</span>
            </div>
            <div class="icon-item">
                <i class="qi-307 icon-display"></i>
                <span class="icon-code">qi-307: 大雨</span>
            </div>
            <div class="icon-item">
                <i class="qi-401 icon-display"></i>
                <span class="icon-code">qi-401: 中雪</span>
            </div>
            <div class="icon-item">
                <i class="qi-501 icon-display"></i>
                <span class="icon-code">qi-501: 雾</span>
            </div>
        </div>
    </div>
    
    <script>
        document.getElementById('getWeather').addEventListener('click', function() {
            const location = document.getElementById('location').value.trim();
            if (!location) {
                alert('请输入位置信息');
                return;
            }
            
            // 显示加载状态
            document.getElementById('weatherIcon').innerHTML = '<span style="font-size:16px;">加载中...</span>';
            document.getElementById('weatherDisplay').style.display = 'flex';
            document.getElementById('weatherTemp').textContent = '--°C';
            document.getElementById('weatherText').textContent = '正在获取...';
            document.getElementById('iconInfo').textContent = '';
            
            // 发起API请求
            fetch(`get_weather.php?location=${encodeURIComponent(location)}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误! 状态码: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('API返回的数据:', data);
                    
                    if (data.success) {
                        const weather = data.data;
                        
                        // 更新天气温度和文本
                        document.getElementById('weatherTemp').textContent = `${weather.temp}°C`;
                        document.getElementById('weatherText').textContent = weather.text;
                        
                        // 显示天气图标
                        if (weather.icon_use_font && weather.icon_font_class) {
                            // 使用字体图标
                            document.getElementById('weatherIcon').innerHTML = `<i class="${weather.icon_font_class}"></i>`;
                            document.getElementById('iconInfo').textContent = `使用图标字体: ${weather.icon_font_class}`;
                        } else {
                            // 使用图片URL
                            document.getElementById('weatherIcon').innerHTML = `<img src="${weather.icon_url}" style="width:60px;height:60px;" alt="天气图标">`;
                            document.getElementById('iconInfo').textContent = `使用图片URL: ${weather.icon_url}`;
                        }
                    } else {
                        throw new Error(data.msg || '获取天气数据失败');
                    }
                })
                .catch(error => {
                    console.error('获取天气数据错误:', error);
                    document.getElementById('weatherIcon').innerHTML = '<span style="color:red;font-size:16px;">错误</span>';
                    document.getElementById('weatherTemp').textContent = '--°C';
                    document.getElementById('weatherText').textContent = `获取失败: ${error.message}`;
                    document.getElementById('iconInfo').textContent = '';
                });
        });
    </script>
</body>
</html> 