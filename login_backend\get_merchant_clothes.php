<?php
header("Content-Type: application/json");
require_once './db.php';
require_once './auth.php';
require_once './config.php';

// 添加调试日志函数
function debug_log($message) {
    error_log('[get_merchant_clothes.php] ' . $message);
}

// 初始化响应数组
$response = [
    'code' => 0,
    'message' => 'success',
    'data' => [
        'list' => [],
        'total' => 0
    ]
];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['code'] = 405;
    $response['message'] = 'Method Not Allowed';
    echo json_encode($response);
    exit;
}

// 获取POST参数
$postData = json_decode(file_get_contents("php://input"), true);
$token = isset($postData['token']) ? $postData['token'] : '';
$merchantId = isset($postData['merchant_id']) ? intval($postData['merchant_id']) : 0;
$wardrobeId = isset($postData['wardrobe_id']) ? intval($postData['wardrobe_id']) : 0;
$categoryId = isset($postData['category_id']) ? intval($postData['category_id']) : null;
$keyword = isset($postData['keyword']) ? trim($postData['keyword']) : '';
$page = isset($postData['page']) ? max(1, intval($postData['page'])) : 1;
$limit = isset($postData['limit']) ? min(100, max(1, intval($postData['limit']))) : 20;

// 处理 demo_mode 参数
$demoMode = false;
if (isset($postData['demo_mode'])) {
    // 支持各种可能的true值: true, "true", "1", 1
    if ($postData['demo_mode'] === true || 
        $postData['demo_mode'] === "true" || 
        $postData['demo_mode'] === "1" || 
        $postData['demo_mode'] === 1 ||
        $postData['demo_mode'] == true) {
        $demoMode = true;
    }
}

// 记录请求参数
debug_log("请求参数: " . json_encode([
    'merchantId' => $merchantId,
    'wardrobeId' => $wardrobeId,
    'categoryId' => $categoryId, 
    'keyword' => $keyword,
    'page' => $page,
    'limit' => $limit,
    'demoMode' => $demoMode
]));

if (empty($token) && !$demoMode) {
    $response['code'] = 400;
    $response['message'] = 'Missing required parameters';
    echo json_encode($response);
    exit;
}

if ($merchantId <= 0 || $wardrobeId <= 0) {
    $response['code'] = 400;
    $response['message'] = 'Missing required parameters';
    echo json_encode($response);
    exit;
}

// 验证用户token，除非是体验模式
$userId = null;
if (!$demoMode) {
    $auth = new Auth();
    $verifyResult = $auth->verifyToken($token);

    if (!$verifyResult) {
        $response['code'] = 401;
        $response['message'] = '无效或已过期的令牌';
        echo json_encode($response);
        exit;
    }
    
    $userId = $verifyResult['sub'];
} else {
    // 在体验模式下，使用默认用户ID
    $userId = 1;
    debug_log("使用体验模式，跳过token验证，使用默认用户ID=1");
}

$db = new Database();
$conn = $db->getConnection();

try {
    // 首先验证商家是否存在且已入驻
    $checkStmt = $conn->prepare("
        SELECT merchant_status 
        FROM users 
        WHERE id = :merchant_id AND merchant_status = 'yes'
    ");
    $checkStmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_INT);
    $checkStmt->execute();
    debug_log("商家验证结果: " . $checkStmt->rowCount() . " 行");
    
    if ($checkStmt->rowCount() === 0) {
        $response['code'] = 404;
        $response['message'] = '商家不存在或未入驻';
        echo json_encode($response);
        exit;
    }
    
    // 验证衣橱是否属于该商家
    $wardrobeStmt = $conn->prepare("
        SELECT id 
        FROM wardrobes 
        WHERE id = :wardrobe_id AND user_id = :merchant_id
    ");
    $wardrobeStmt->bindParam(':wardrobe_id', $wardrobeId, PDO::PARAM_INT);
    $wardrobeStmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_INT);
    $wardrobeStmt->execute();
    debug_log("衣橱验证结果: " . $wardrobeStmt->rowCount() . " 行");
    
    if ($wardrobeStmt->rowCount() === 0) {
        $response['code'] = 404;
        $response['message'] = '衣橱不存在或不属于该商家';
        echo json_encode($response);
        exit;
    }
    
    // 检查分类是否存在（如果指定了分类）
    if ($categoryId !== null && $categoryId > 0) {
        $catStmt = $conn->prepare("
            SELECT id, name, is_system
            FROM clothing_categories 
            WHERE id = :category_id
        ");
        $catStmt->bindParam(':category_id', $categoryId, PDO::PARAM_INT);
        $catStmt->execute();
        
        if ($catStmt->rowCount() > 0) {
            $catInfo = $catStmt->fetch(PDO::FETCH_ASSOC);
            debug_log("找到分类: ID=" . $catInfo['id'] . ", 名称=" . $catInfo['name'] . ", 是否系统分类=" . $catInfo['is_system']);
        } else {
            debug_log("警告：未找到ID为 " . $categoryId . " 的分类");
        }
    }
    
    // 构建查询语句
    $sql = "SELECT c.*, cc.name as category_name 
            FROM clothes c 
            LEFT JOIN clothing_categories cc ON c.category_id = cc.id
            WHERE c.user_id = :merchant_id AND c.wardrobe_id = :wardrobe_id";
    $countSql = "SELECT COUNT(*) as total FROM clothes c WHERE c.user_id = :merchant_id AND c.wardrobe_id = :wardrobe_id";
    
    $params = [
        ':merchant_id' => $merchantId,
        ':wardrobe_id' => $wardrobeId
    ];
    
    // 添加分类条件
    if ($categoryId !== null && $categoryId > 0) {
        // 检查该分类是否有code字段，如果有则也通过code匹配
        $catCodeStmt = $conn->prepare("
            SELECT id, code 
            FROM clothing_categories 
            WHERE id = :category_id
        ");
        $catCodeStmt->bindParam(':category_id', $categoryId, PDO::PARAM_INT);
        $catCodeStmt->execute();
        $catInfo = $catCodeStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($catInfo && !empty($catInfo['code'])) {
            debug_log("找到分类code: " . $catInfo['code']);
            // 通过category_id或者category字段匹配
            $sql .= " AND (c.category_id = :category_id OR c.category = :category_code)";
            $countSql .= " AND (c.category_id = :category_id OR c.category = :category_code)";
            $params[':category_id'] = $categoryId;
            $params[':category_code'] = $catInfo['code'];
        } else {
            // 只通过category_id匹配
            $sql .= " AND c.category_id = :category_id";
            $countSql .= " AND c.category_id = :category_id";
            $params[':category_id'] = $categoryId;
        }
    }
    
    // 添加关键字搜索条件
    if (!empty($keyword)) {
        $sql .= " AND (c.name LIKE :keyword_name OR c.tags LIKE :keyword_tags)";
        $countSql .= " AND (c.name LIKE :keyword_name OR c.tags LIKE :keyword_tags)";
        $searchKeyword = "%{$keyword}%";
        $params[':keyword_name'] = $searchKeyword;
        $params[':keyword_tags'] = $searchKeyword;
    }
    
    // 添加排序和分页
    $sql .= " ORDER BY c.created_at DESC";
    $sql .= " LIMIT :limit OFFSET :offset";
    $offset = ($page - 1) * $limit;
    
    // 记录完整SQL语句
    debug_log("执行SQL: " . $sql);
    debug_log("参数: " . json_encode($params));
    
    // 执行查询
    $stmt = $conn->prepare($sql);
    
    // 绑定所有参数
    foreach ($params as $key => $value) {
        $paramType = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
        $stmt->bindValue($key, $value, $paramType);
    }
    
    // 单独绑定LIMIT和OFFSET，因为在预处理语句中不能直接在LIMIT和OFFSET后面使用命名参数
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $stmt->execute();
    debug_log("查询执行完成");
    
    $clothes = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        // 处理JSON数据
        if (isset($row['description']) && !empty($row['description'])) {
            $description = json_decode($row['description'], true);
            if (is_array($description)) {
                $row['description_obj'] = $description;
            }
        }
        
        $clothes[] = $row;
    }
    debug_log("查询结果: 找到 " . count($clothes) . " 件衣物");
    
    // 获取总数
    $countStmt = $conn->prepare($countSql);
    
    // 绑定除了LIMIT和OFFSET之外的所有参数
    foreach ($params as $key => $value) {
        $paramType = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
        $countStmt->bindValue($key, $value, $paramType);
    }
    
    $countStmt->execute();
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    debug_log("总数统计: " . $total);
    
    // 检查clothes表中是否有对应分类的衣物
    if ($categoryId !== null && $categoryId > 0 && count($clothes) === 0) {
        $checkClothesStmt = $conn->prepare("
            SELECT COUNT(*) as count FROM clothes 
            WHERE category_id = :category_id
        ");
        $checkClothesStmt->bindParam(':category_id', $categoryId, PDO::PARAM_INT);
        $checkClothesStmt->execute();
        $clothesInCategory = $checkClothesStmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        debug_log("检查分类 " . $categoryId . " 在整个系统中是否有衣物: " . $clothesInCategory . " 件");
        
        // 检查分类下是否有商家的衣物
        $checkMerchantClothesStmt = $conn->prepare("
            SELECT COUNT(*) as count FROM clothes 
            WHERE category_id = :category_id AND user_id = :merchant_id
        ");
        $checkMerchantClothesStmt->bindParam(':category_id', $categoryId, PDO::PARAM_INT);
        $checkMerchantClothesStmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_INT);
        $checkMerchantClothesStmt->execute();
        $merchantClothesInCategory = $checkMerchantClothesStmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        debug_log("检查分类 " . $categoryId . " 下商家 " . $merchantId . " 的衣物: " . $merchantClothesInCategory . " 件");
    }
    
    $response['data'] = [
        'list' => $clothes,
        'total' => $total,
        'page' => $page,
        'limit' => $limit
    ];
} catch (Exception $e) {
    debug_log("错误: " . $e->getMessage());
    $response['code'] = 500;
    $response['message'] = '处理请求时发生错误: ' . $e->getMessage();
} finally {
    // PDO不需要手动关闭连接
    $conn = null;
}

echo json_encode($response); 