/* 页面容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #fff;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  background-color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  box-shadow: 0 1px 5px rgba(0,0,0,0.05);
}

.status-bar-content {
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 0 15px 8px;
  font-size: 12px;
  font-weight: 600;
  color: #000;
}

.status-icons {
  display: flex;
  align-items: center;
}

.icon-signal, .icon-wifi, .icon-battery {
  margin-left: 5px;
}

/* 顶部标题栏 */
.title-bar {
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  position: fixed;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 0 15px;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.title {
  font-size: 17px;
  font-weight: 500;
  text-align: center;
  flex: 1;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-icons {
  display: flex;
  align-items: center;
}

.icon-dots, .icon-refresh {
  margin-left: 15px;
}

/* 导航菜单 */
.nav-menu {
  position: fixed;
  left: 0;
  right: 0;
  height: 40px;
  background-color: #fff;
  z-index: 99;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.nav-scroll {
  width: 100%;
  height: 100%;
  white-space: nowrap;
}

.nav-items {
  display: inline-flex;
  padding: 0 10px;
  height: 100%;
  width: auto; /* 确保宽度足够显示所有项 */
}

.nav-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0 10px; /* 减小内边距以适应更多项目 */
  font-size: 13px; /* 稍微减小字体大小 */
  color: #666;
  position: relative;
  transition: color 0.2s, font-weight 0.2s;
  flex-shrink: 0; /* 防止项目被压缩 */
}

.nav-item.active {
  color: #000;
  font-weight: 600;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 10px;
  right: 10px;
  height: 3px;
  background-color: #000;
  border-radius: 1.5px;
}

/* 教程内容区域 */
.tutorial-container {
  flex: 1;
  padding: 15px;
  background-color: #f8f8f8;
  margin-bottom: 20px;
  padding-bottom: 50px;
}

/* 分段样式 */
.section {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  scroll-margin-top: 150px; /* 添加滚动边距，确保内容不被顶部栏遮挡 */
}

.section-header {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 步骤样式 */
.step {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.step:last-child {
  border-bottom: none;
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #000;
  color: #fff;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
  font-weight: 500;
}

.step-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.step-content {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-left: 34px;
}

/* 子步骤样式 */
.sub-step {
  margin-top: 8px;
  line-height: 1.5;
  padding-left: 8px;
}

.step-image {
  margin-top: 10px;
  margin-left: 34px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.image-placeholder {
  background-color: #f5f5f5;
  height: 180px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ccc;
  font-size: 24px;
}

.tutorial-img {
  width: 100%;
  height: 180px;
  border-radius: 8px;
  background-color: #f5f5f5; /* 图片加载前的背景色 */
  object-fit: cover;
}

/* 图标样式 */
.icon-back::before {
  content: '〈';
  font-size: 18px;
}

.icon-dots::before {
  content: '•••';
  font-size: 18px;
}

.icon-refresh::before {
  content: '⟳';
  font-size: 18px;
}

.icon-signal::before {
  content: '📶';
  font-size: 12px;
}

.icon-wifi::before {
  content: '📡';
  font-size: 12px;
}

.icon-battery::before {
  content: '🔋';
  font-size: 12px;
}

/* 响应式调整 */
@media screen and (min-width: 375px) {
  .step-content {
    font-size: 15px;
  }
  
  .section-title {
    font-size: 17px;
  }
  
  .step-title {
    font-size: 16px;
  }
  
  .nav-item {
    font-size: 15px;
    padding: 0 15px;
  }
} 