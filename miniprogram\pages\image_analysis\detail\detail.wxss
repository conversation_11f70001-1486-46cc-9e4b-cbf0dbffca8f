.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

/* 添加分享横幅样式 */
.share-banner {
  padding: 16rpx;
  background-color: #fff8e1;
  text-align: center;
  border-bottom: 1px solid #ffe082;
}

.share-text {
  font-size: 26rpx;
  color: #ff8f00;
  font-weight: 500;
}

.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30rpx;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.status-bar {
  padding: 30rpx;
  text-align: center;
}

.status-bar.pending {
  background-color: #f5f5f5;
}

.status-bar.processing {
  background-color: #e3f2fd;
}

.status-bar.completed {
  background-color: #e8f5e9;
}

.status-bar.failed {
  background-color: #ffebee;
}

.status-text {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.status-bar.pending .status-text {
  color: #757575;
}

.status-bar.processing .status-text {
  color: #1976d2;
}

.status-bar.completed .status-text {
  color: #388e3c;
}

.status-bar.failed .status-text {
  color: #d32f2f;
}

.status-desc {
  font-size: 26rpx;
  display: block;
}

.status-bar.pending .status-desc {
  color: #9e9e9e;
}

.status-bar.processing .status-desc {
  color: #42a5f5;
}

.status-bar.completed .status-desc {
  color: #66bb6a;
}

.status-bar.failed .status-desc {
  color: #ef5350;
}

.content {
  flex: 1;
  width: 100%;
  padding: 20px;
  padding-bottom: 80px;
  overflow-y: auto;
  box-sizing: border-box;
}

/* 为内容区域的各个部分设置居中样式 */
.content .section,
.content .photo-section,
.content .waiting-message,
.content .analysis-result {
  max-width: 680rpx;
  margin-left: auto;
  margin-right: auto;
}

.photo-section {
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.photo-scroll {
  width: 100%;
  white-space: nowrap;
}

.photo-list {
  display: inline-flex;
}

.photo-item {
  width: 220rpx;
  height: 280rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
  border: 1px solid rgba(0,0,0,0.05);
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.section {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 15rpx;
}

.section-content {
  padding: 10rpx 0;
}

.analysis-item {
  margin-bottom: 30rpx;
}

.analysis-item:last-child {
  margin-bottom: 0;
}

.item-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.item-value {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.item-list {
  margin-left: 20rpx;
}

.list-item {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 10rpx;
  position: relative;
  padding-left: 30rpx;
}

.list-item:before {
  content: "•";
  position: absolute;
  left: 0;
  color: #000;
}

.waiting-content {
  padding: 60rpx 0;
}

.waiting-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.waiting-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.waiting-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 15rpx;
}

.waiting-subtext {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.retry-btn {
  background-color: #000;
  color: white;
  height: 70rpx;
  line-height: 70rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: 500;
  padding: 0 40rpx;
  min-width: auto;
  display: inline-block;
}

.retry-btn:after {
  border: none;
}

.error-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30rpx;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.error-text {
  font-size: 30rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
  z-index: 10;
}

.action-btn {
  width: 100%;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
  border-radius: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  background-color: #000;
  border: none;
}

.action-btn::after {
  border: none;
}

.action-btn[disabled] {
  background-color: #ccc;
  color: #fff;
  opacity: 0.6;
}

.refresh-btn {
  background-color: #000;
}

.share-btn {
  background-color: #000;
}

.button-group {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-top: 20rpx;
  flex-wrap: wrap;
}

.pay-btn {
  background-color: #000;
  color: white;
  height: 70rpx;
  line-height: 70rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: 500;
  padding: 0 40rpx;
  min-width: auto;
  display: inline-block;
}

.pay-btn:after {
  border: none;
} 

.button-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 20rpx;
  width: 100%;
}

.button-row .action-btn {
  flex: 1;
  width: auto;
  min-width: 0;
  font-size: 14px;
  margin: 0;
}

.recommend-btn {
  background-color: #000;
} 