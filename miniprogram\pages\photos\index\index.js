const app = getApp();

Page({
  data: {
    photos: [],
    leftItems: [], // 左列项目
    rightItems: [], // 右列项目
    isLoading: true,
    showModal: false, // 是否显示弹框
    currentPhoto: {}, // 当前查看的照片
    selectMode: false,
    clothesId: '',
    merchantId: '',
    selectedPhotoId: null, // 当前选中的照片ID
    selectedPhoto: null   // 当前选中的照片完整信息
  },
  
  onLoad: function (options) {
    wx.setNavigationBarTitle({
      title: '我的照片'
    });

    console.log("照片页面 options:", options);

    // 检查是否处于选择模式（用于试穿）
    if (options.select_mode === 'true') {
      // 设置为选择模式
      this.setData({
        selectMode: true,
        clothesId: options.clothes_id,
        merchantId: options.merchant_id
      });
      wx.setNavigationBarTitle({
        title: '选择模特照片'
      });
      console.log("进入照片选择模式，clothesId:", options.clothes_id, "merchantId:", options.merchant_id);
    }
    
    this.loadPhotos();
  },
  
  onShow: function () {
    // 每次显示页面时重新加载数据
    this.loadPhotos();
  },
  
  // 分配照片到两列，不再包含添加按钮
  distributePhotos: function() {
    const photos = this.data.photos;
    const leftItems = [];
    const rightItems = [];
    
    // 从左到右、从上到下的顺序分配照片
    photos.forEach((photo, index) => {
      // 按顺序填充：左1、右1、左2、右2...
      if (index % 2 === 0) {
        leftItems.push(photo);
      } else {
        rightItems.push(photo);
      }
    });
    
    this.setData({
      leftItems,
      rightItems
    });
  },
  
  // 加载照片列表
  loadPhotos: function () {
    const token = app.globalData.token;
    if (!token) {
      console.error('未登录');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      isLoading: true
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_photos.php`,
      method: 'GET',
      header: {
        'Authorization': token
      },
      success: (res) => {
        console.log('获取照片列表响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          let photos = res.data.data || [];
          
          // 确保照片数据结构一致
          photos = photos.map(photo => ({
            ...photo,
            url: photo.image_url || photo.url // 统一使用url字段
          }));
          
          this.setData({
            photos: photos
          }, () => {
            // 在照片数据设置完成后，分配照片到两列
            this.distributePhotos();
          });
        } else {
          wx.showToast({
            title: res.data.msg || '获取照片失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('获取照片列表请求失败:', err);
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({
          isLoading: false
        });
      }
    });
  },
  
  // 点击添加照片
  onAddPhoto: function () {
    wx.showActionSheet({
      itemList: ['拍照', '从相册选择'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 选择拍照
          this.chooseImage('camera');
        } else if (res.tapIndex === 1) {
          // 选择从相册选择
          this.chooseImage('album');
        }
      }
    });
  },
  
  // 选择图片
  chooseImage: function (sourceType) {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: [sourceType],
      sizeType: ['compressed'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        const size = res.tempFiles[0].size;
        
        // 检查文件大小，不能超过10MB
        if (size > 10 * 1024 * 1024) {
          wx.showToast({
            title: '图片不能超过10MB',
            icon: 'none'
          });
          return;
        }
        
        // 显示确认弹框
        this.showPhotoConfirmDialog(tempFilePath);
      },
      fail: (err) => {
        console.log('选择图片失败', err);
      }
    });
  },
  
  // 显示照片确认弹框
  showPhotoConfirmDialog: function (tempFilePath) {
    wx.showModal({
      title: '选择照片类型',
      content: '请选择这是全身照还是半身照',
      showCancel: true,
      cancelText: '全身照',
      confirmText: '半身照',
      success: (res) => {
        const photoType = res.confirm ? 'half' : 'full';
        this.uploadPhoto(tempFilePath, photoType);
      }
    });
  },
  
  // 上传照片
  uploadPhoto: function (tempFilePath, photoType) {
    const token = app.globalData.token;
    if (!token) {
      console.error('未登录');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '上传中...',
      mask: true
    });
    
    console.log('开始上传照片，路径:', tempFilePath, '类型:', photoType);
    
    wx.uploadFile({
      url: `${app.globalData.apiBaseUrl}/upload_photo.php`,
      filePath: tempFilePath,
      name: 'image', // 改为'image'，与后端PHP中$_FILES['image']对应
      formData: {
        type: photoType
      },
      header: {
        'Authorization': token
      },
      success: (res) => {
        console.log('上传照片响应:', res);
        
        let response;
        try {
          // 尝试检测和修复JSON格式问题
          let responseText = res.data;
          
          // 如果字符串中包含多个JSON对象（如结尾有}{ 这种格式），则尝试提取最后一个JSON对象
          const lastJsonStartIndex = responseText.lastIndexOf('{"error":');
          if (lastJsonStartIndex > 0) {
            console.log('检测到可能有多个JSON对象，提取最后一个');
            responseText = responseText.substring(lastJsonStartIndex);
          }
          
          response = JSON.parse(responseText);
          console.log('解析后的响应:', response);
        } catch (e) {
          console.error('解析上传响应失败:', e, '原始响应:', res.data);
          wx.showToast({
            title: '上传成功，但解析响应失败',
            icon: 'none'
          });
          
          // 即使解析失败也重新加载照片列表，因为上传可能已经成功
          this.loadPhotos();
          return;
        }
        
        if (res.statusCode === 200 && !response.error) {
          wx.showToast({
            title: '上传成功',
            icon: 'success'
          });
          
          // 重新加载照片列表
          this.loadPhotos();
        } else {
          wx.showToast({
            title: response.msg || '上传失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('上传照片请求失败:', err);
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },
  
  // 点击照片显示弹框或在选择模式下选择此照片
  showPhotoModal: function (e) {
    const photoId = e.currentTarget.dataset.id;
    const photoUrl = e.currentTarget.dataset.url;
    const photoType = e.currentTarget.dataset.type;
    
    console.log('点击照片:', { id: photoId, url: photoUrl, type: photoType });
    
    // 如果是选择模式，则选择此照片
    if (this.data.selectMode) {
      const selectedPhoto = {
        id: photoId,
        url: photoUrl,
        type: photoType
      };
      
      // 更新选中状态
      this.setData({
        selectedPhotoId: photoId,
        selectedPhoto: selectedPhoto
      });
      
      // 全局变量也更新
      app.globalData.selectedTryOnPhoto = selectedPhoto;
      return;
    }
    
    // 非选择模式下，显示正常的照片弹框
    if (photoId && photoUrl) {
      this.setData({
        currentPhoto: {
          id: photoId,
          url: photoUrl,
          type: photoType
        },
        showModal: true
      }, () => {
        // 确保弹框打开后数据已更新
        console.log('弹框已打开，照片信息:', this.data.currentPhoto);
      });
    } else {
      console.error('照片信息不完整，无法显示弹框');
      wx.showToast({
        title: '照片信息不完整',
        icon: 'none'
      });
    }
  },
  
  // 隐藏照片弹框
  hidePhotoModal: function () {
    this.setData({
      showModal: false
    });
  },
  
  // 阻止事件冒泡
  stopPropagation: function (e) {
    // 阻止点击内容区域时关闭弹框
  },
  
  // 删除当前照片
  deleteCurrentPhoto: function () {
    const token = app.globalData.token;
    const photoId = this.data.currentPhoto.id;
    
    console.log('准备删除照片，ID:', photoId);
    
    if (!token) {
      console.error('未登录');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    if (!photoId) {
      console.error('照片ID为空，无法删除');
      wx.showToast({
        title: '照片信息不完整',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张照片吗？删除后不可恢复。',
      confirmText: '删除',
      confirmColor: '#ff3b30',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
            mask: true
          });
          
          console.log('发送删除请求，ID:', photoId);
          
          wx.request({
            url: `${app.globalData.apiBaseUrl}/delete_photo.php?id=${photoId}`,
            method: 'POST',
            header: {
              'Authorization': token,
              'Content-Type': 'application/x-www-form-urlencoded'
            },
            success: (res) => {
              console.log('删除照片响应:', res.data);
              
              if (res.statusCode === 200 && !res.data.error) {
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                
                // 关闭弹框
                this.setData({
                  showModal: false
                });
                
                // 重新加载照片列表
                this.loadPhotos();
              } else {
                wx.showToast({
                  title: res.data.msg || '删除失败',
                  icon: 'none'
                });
              }
            },
            fail: (err) => {
              console.error('删除照片请求失败:', err);
              wx.showToast({
                title: '网络错误，请稍后重试',
                icon: 'none'
              });
            },
            complete: () => {
              wx.hideLoading();
            }
          });
        }
      }
    });
  },
  
  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadPhotos();
    wx.stopPullDownRefresh();
  },
  
  // 图片加载完成时的处理函数
  onImageLoad: function(e) {
    // 图片加载完成后可以在这里处理图片的实际尺寸
    console.log('图片加载完成，尺寸信息:', e.detail);
  },
  
  startTryOn: function() {
    console.log('开始试穿按钮点击');
    // 检查是否有选中的照片
    if (!this.data.selectedPhotoId || !this.data.selectedPhoto) {
      wx.showToast({
        title: '请选择一张照片',
        icon: 'none'
      });
      return;
    }
    
    // 确保全局变量已设置
    app.globalData.selectedTryOnPhoto = this.data.selectedPhoto;

    // 显示加载提示
    wx.showLoading({
      title: '准备试穿...',
      mask: true
    });
    
    // 构建请求数据
    const requestData = {
      photo_id: this.data.selectedPhoto.id,
      clothes_ids: [this.data.clothesId] // 将单个ID包装为数组
    };
    
    // 如果是商家衣物，添加merchant_id参数
    if (this.data.merchantId) {
      requestData.merchant_id = this.data.merchantId;
      console.log('商家模式试穿，商家ID:', this.data.merchantId);
    }

    // 保存请求参数到全局变量
    app.globalData.currentTryOnRequest = requestData;
    
    console.log('已保存试穿请求参数:', app.globalData.currentTryOnRequest);
    
    wx.hideLoading();
    
    // 引入试穿进度弹框
    // 定义进度弹框参数
    const progressParams = {
      showProgress: true,
      progressStatus: 'queued',
      progressPercent: 0,
      progressMessage: '排队中...'
    };
    
    // 将进度参数保存到全局变量
    app.globalData.tryOnProgressParams = progressParams;
    
    // 跳转到进度展示页面，并传递参数
    let url = '/pages/try_on/progress/index';
    if (this.data.merchantId) {
      url += `?merchant_id=${this.data.merchantId}&clothes_id=${this.data.clothesId}`;
    }
    
    wx.navigateTo({
      url: url
    });
  }
})
