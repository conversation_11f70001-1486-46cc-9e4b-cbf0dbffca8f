<!--pages/face_analysis/result/index.wxml-->
<!-- 引入Markdown处理模块 -->
<wxs src="./markdown.wxs" module="md" />

<view class="container">
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-icon"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <block wx:elif="{{!error}}">
    <view class="status-bar {{analysis.status}}">
      <text class="status-text">{{statusText[analysis.status]}}</text>
      <text class="status-desc" wx:if="{{analysis.status === 'pending'}}">您的分析请求已提交，正在等待处理</text>
      <text class="status-desc" wx:elif="{{analysis.status === 'processing'}}">正在为您生成面容分析报告</text>
      <text class="status-desc" wx:elif="{{analysis.status === 'completed'}}">分析完成</text>
      <text class="status-desc" wx:elif="{{analysis.status === 'failed'}}">很抱歉，分析失败。请重新提交</text>
    </view>
    
    <scroll-view scroll-y class="content">
      <view wx:if="{{analysis.status === 'completed' && analysisResult}}" class="analysis-result">
        <!-- 照片展示 -->
        <view class="photo-section">
          <view class="section-title">您的照片</view>
          <view class="photo-list">
            <view class="photo-item" wx:if="{{analysis.display_front_photo_url || analysis.cdn_front_photo_url || analysis.front_photo_url}}">
              <image src="{{analysis.display_front_photo_url || analysis.cdn_front_photo_url || analysis.front_photo_url}}" mode="aspectFill" class="photo-image" bindtap="previewPhoto" data-url="{{analysis.display_front_photo_url || analysis.cdn_front_photo_url || analysis.front_photo_url}}"></image>
              <view class="photo-label">正面照</view>
            </view>
            <view class="photo-item" wx:if="{{analysis.display_side_photo_url || analysis.cdn_side_photo_url || analysis.side_photo_url}}">
              <image src="{{analysis.display_side_photo_url || analysis.cdn_side_photo_url || analysis.side_photo_url}}" mode="aspectFill" class="photo-image" bindtap="previewPhoto" data-url="{{analysis.display_side_photo_url || analysis.cdn_side_photo_url || analysis.side_photo_url}}"></image>
              <view class="photo-label">侧面照</view>
            </view>
          </view>
        </view>
        
        <!-- 面部特征分析 -->
        <view class="result-section" wx:if="{{analysisResult.face_shape || analysisResult.face_proportions}}">
          <view class="section-title">面部特征分析</view>
          <view class="section-content">
            <view class="item" wx:if="{{analysisResult.face_shape}}">
              <view class="item-label">脸型分析</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.face_shape)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.face_shape).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
            
            <view class="item" wx:if="{{analysisResult.face_proportions}}">
              <view class="item-label">面部比例</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.face_proportions)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.face_proportions).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
            
            <!-- 修改五官特点部分 -->
            <view class="item" wx:if="{{analysisResult.facial_features}}">
              <view class="item-label">五官特点</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.facial_features)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.facial_features).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
            
            <view class="item" wx:if="{{analysisResult.bone_structure}}">
              <view class="item-label">骨骼结构</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.bone_structure)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.bone_structure).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
            
            <view class="item" wx:if="{{analysisResult.lip_analysis}}">
              <view class="item-label">唇部分析</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.lip_analysis)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.lip_analysis).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 整体特征 -->
        <view class="result-section" wx:if="{{analysisResult.overall_features}}">
          <view class="section-title">整体特征</view>
          <view class="section-content">
            <view class="item">
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.overall_features)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.overall_features).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 妆容推荐 -->
        <view class="result-section" wx:if="{{analysisResult.makeup_recommendations}}">
          <view class="section-title">妆容推荐</view>
          <view class="section-content">
            <view class="item" wx:if="{{analysisResult.makeup_recommendations.foundation}}">
              <view class="item-label">底妆</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.makeup_recommendations.foundation)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.makeup_recommendations.foundation).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
            
            <view class="item" wx:if="{{analysisResult.makeup_recommendations.eyes}}">
              <view class="item-label">眼妆</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.makeup_recommendations.eyes)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.makeup_recommendations.eyes).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
            
            <view class="item" wx:if="{{analysisResult.makeup_recommendations.lips}}">
              <view class="item-label">唇妆</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.makeup_recommendations.lips)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.makeup_recommendations.lips).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
            
            <view class="item" wx:if="{{analysisResult.makeup_recommendations.contour}}">
              <view class="item-label">修容</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.makeup_recommendations.contour)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.makeup_recommendations.contour).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 发型推荐 -->
        <view class="result-section" wx:if="{{analysisResult.hairstyle_recommendations}}">
          <view class="section-title">发型推荐</view>
          <view class="section-content">
            <view class="item" wx:if="{{analysisResult.hairstyle_recommendations.style}}">
              <view class="item-label">发型</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.hairstyle_recommendations.style)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.hairstyle_recommendations.style).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
            
            <view class="item" wx:if="{{analysisResult.hairstyle_recommendations.color}}">
              <view class="item-label">发色</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.hairstyle_recommendations.color)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.hairstyle_recommendations.color).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 饰品推荐 -->
        <view class="result-section" wx:if="{{analysisResult.accessories_recommendations}}">
          <view class="section-title">饰品推荐</view>
          <view class="section-content">
            <view class="item" wx:if="{{analysisResult.accessories_recommendations.earrings}}">
              <view class="item-label">耳饰</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.accessories_recommendations.earrings)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.accessories_recommendations.earrings).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
            
            <view class="item" wx:if="{{analysisResult.accessories_recommendations.necklaces}}">
              <view class="item-label">项链</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.accessories_recommendations.necklaces)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.accessories_recommendations.necklaces).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
            
            <view class="item" wx:if="{{analysisResult.accessories_recommendations.glasses}}">
              <view class="item-label">眼镜</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.accessories_recommendations.glasses)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.accessories_recommendations.glasses).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 服装推荐 -->
        <view class="result-section" wx:if="{{analysisResult.clothing_recommendations}}">
          <view class="section-title">服装推荐</view>
          <view class="section-content">
            <view class="item" wx:if="{{analysisResult.clothing_recommendations.necklines}}">
              <view class="item-label">领口款式</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.clothing_recommendations.necklines)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.clothing_recommendations.necklines).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
            
            <view class="item" wx:if="{{analysisResult.clothing_recommendations.colors}}">
              <view class="item-label">适合颜色</view>
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.clothing_recommendations.colors)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.clothing_recommendations.colors).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 整体风格建议 -->
        <view class="result-section" wx:if="{{analysisResult.overall_style_suggestions}}">
          <view class="section-title">整体风格建议</view>
          <view class="section-content">
            <view class="item">
              <view class="item-value">
                <block wx:for="{{md.splitIntoParagraphs(analysisResult.overall_style_suggestions)}}" wx:key="index">
                  <rich-text nodes="{{md.formatBoldText(item)}}" space="nbsp"></rich-text>
                  <view wx:if="{{index < md.splitIntoParagraphs(analysisResult.overall_style_suggestions).length - 1}}" style="height: 8rpx;"></view>
                </block>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 如果没有结果但状态是已完成 -->
        <view class="no-result" wx:if="{{analysis.status === 'completed' && !analysisResult}}">
          <view class="no-result-icon">😢</view>
          <view class="no-result-text">无法解析分析结果</view>
        </view>
      </view>
      
      <!-- 未完成状态的显示 -->
      <view wx:else class="waiting-content">
        <view wx:if="{{analysis.status === 'pending'}}" class="waiting-message">
          <view class="waiting-icon">⏳</view>
          <view class="waiting-text">您的分析请求已提交，正在等待处理</view>
          <view class="waiting-subtext">请稍后刷新查看结果</view>
        </view>
        
        <view wx:elif="{{analysis.status === 'processing'}}" class="waiting-message">
          <view class="waiting-icon">⚙️</view>
          <view class="waiting-text">正在为您生成面容分析报告</view>
          <view class="waiting-subtext">分析完成后将自动显示结果</view>
        </view>
        
        <view wx:elif="{{analysis.status === 'failed'}}" class="waiting-message">
          <view class="waiting-icon">❌</view>
          <view class="waiting-text">很抱歉，分析失败</view>
          <view class="waiting-subtext">请重新提交分析请求</view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部操作按钮 -->
    <view class="footer">
      <block wx:if="{{analysis.status === 'completed'}}">
        <button class="action-btn" open-type="share">分享好友</button>
      </block>
      
      <block wx:elif="{{analysis.status === 'pending' || analysis.status === 'processing'}}">
        <button class="action-btn refresh-btn" bindtap="refreshResult">刷新</button>
      </block>
      
      <block wx:elif="{{analysis.status === 'failed'}}">
        <button class="action-btn" bindtap="goToIndex">返回</button>
      </block>
    </view>
  </block>
  
  <!-- 错误提示 -->
  <view wx:else class="error-container">
    <view class="error-icon">😢</view>
    <view class="error-text">{{errorMsg}}</view>
    <button class="action-btn" bindtap="goBack">返回</button>
  </view>
</view>