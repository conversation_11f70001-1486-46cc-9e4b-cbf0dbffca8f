const app = getApp();

Page({
  data: {
    categories: [],
    page: 1,
    per_page: 10,
    hasMoreData: false,
    isLoading: false
  },

  // 页面加载时
  onLoad: function() {
    this.loadCategories(true);
  },

  // 页面显示时
  onShow: function() {
    // 如果需要刷新数据
    if (app.globalData.needRefreshOutfitCategories) {
      this.setData({ page: 1 });
      this.loadCategories(true);
      app.globalData.needRefreshOutfitCategories = false;
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.setData({ page: 1 });
    this.loadCategories(true, () => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载穿搭分类列表
  loadCategories: function(refresh = false, callback) {
    // 检查登录状态
    if (!app.globalData.token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      if (typeof callback === 'function') callback();
      return;
    }

    this.setData({ isLoading: true });

    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_outfit_categories.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      data: {
        page: this.data.page,
        per_page: this.data.per_page
      },
      success: (res) => {
        console.log('获取穿搭分类列表:', res.data);

        if (res.statusCode === 200 && res.data.success) {
          const newCategories = res.data.data || [];
          const pagination = res.data.pagination || {};
          
          // 更新数据
          this.setData({
            categories: refresh ? newCategories : [...this.data.categories, ...newCategories],
            hasMoreData: pagination.current_page < pagination.total_pages
          });
        } else {
          wx.showToast({
            title: res.data.message || '获取穿搭分类列表失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isLoading: false });
        if (typeof callback === 'function') callback();
      }
    });
  },

  // 加载更多
  loadMore: function() {
    if (this.data.hasMoreData && !this.data.isLoading) {
      this.setData({ page: this.data.page + 1 });
      this.loadCategories();
    }
  },

  // 跳转到分类下的穿搭列表
  viewOutfits: function(e) {
    const id = e.currentTarget.dataset.id;
    const name = e.currentTarget.dataset.name;
    
    console.log('查看分类:', id, name);
    
    // 跳转到穿搭列表页并传递分类ID作为过滤条件
    wx.navigateTo({
      url: `/pages/outfits/index/index?category_id=${id}&category_name=${encodeURIComponent(name)}`
    });
  },

  // 跳转到添加分类
  goToAddCategory: function() {
    wx.navigateTo({
      url: '/pages/outfit_categories/add/add'
    });
  },

  // 编辑分类
  editCategory: function(e) {
    const category = e.currentTarget.dataset.category;
    wx.navigateTo({
      url: `/pages/outfit_categories/edit/edit?id=${category.id}&name=${encodeURIComponent(category.name)}&description=${encodeURIComponent(category.description || '')}&sort_order=${category.sort_order || 0}`
    });
  },

  // 删除分类
  deleteCategory: function(e) {
    const { id, isDefault } = e.currentTarget.dataset;
    
    console.log('分类ID:', id, '是否默认:', isDefault, '类型:', typeof isDefault);
    
    // 不允许删除默认分类
    if (isDefault === true || isDefault === 'true' || isDefault === 1 || isDefault === '1') {
      wx.showToast({
        title: '默认分类不能删除',
        icon: 'none'
      });
      return;
    }

    // 显示确认弹窗
    wx.showModal({
      title: '确认删除',
      content: '确定要删除该穿搭分类吗？其中的穿搭将被移动到默认分类。',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '正在删除...' });
          
          // 调用删除API
          wx.request({
            url: `${app.globalData.apiBaseUrl}/delete_outfit_category.php`,
            method: 'POST',
            header: {
              'content-type': 'application/json',
              'Authorization': app.globalData.token
            },
            data: {
              id: id
            },
            success: (res) => {
              wx.hideLoading();
              console.log('删除穿搭分类响应:', res.data);
              
              if (res.statusCode === 200 && res.data.success) {
                // 删除成功
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                
                // 设置全局刷新标记
                app.globalData.needRefreshOutfitCategories = true;
                
                // 刷新分类列表
                this.setData({ page: 1 });
                this.loadCategories(true);
              } else if (res.data.error === 'Cannot delete default category') {
                // 默认分类
                wx.showToast({
                  title: '默认分类不能删除',
                  icon: 'none'
                });
              } else {
                // 其他错误
                wx.showToast({
                  title: res.data.message || '删除失败',
                  icon: 'none'
                });
              }
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('请求失败:', err);
              wx.showToast({
                title: '网络请求失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  }
}); 