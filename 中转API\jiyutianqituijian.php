<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 日志函数
function logDebug($message, $data = null) {
    $log_file = __DIR__ . '/weather_outfit_api_debug.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] {$message}";
    
    if ($data !== null) {
        $log_message .= ': ' . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    
    file_put_contents($log_file, $log_message . PHP_EOL, FILE_APPEND);
}

logDebug("接收到新的基于天气的穿搭推荐API请求", ['method' => $_SERVER['REQUEST_METHOD']]);

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    logDebug("OPTIONS预检请求，直接返回");
    exit;
}

// 检查请求数据
$json = file_get_contents('php://input');
if (empty($json)) {
    logDebug("错误: 缺少请求数据");
    echo json_encode(['error' => '缺少请求数据']);
    exit;
}

// 记录原始输入数据
logDebug("接收到的原始输入数据", [
    'length' => strlen($json),
    'preview' => substr($json, 0, 500)
]);

$data = json_decode($json, true);
if (!$data) {
    logDebug("错误: 无效的JSON数据", [
        'raw_input' => substr($json, 0, 1000),
        'json_error' => json_last_error_msg(),
        'json_error_code' => json_last_error()
    ]);
    echo json_encode(['error' => '无效的JSON数据']);
    exit;
}

// 记录解析后的数据结构
logDebug("解析后的数据结构", [
    'keys' => array_keys($data),
    'clothes_data_type' => isset($data['clothes_data']) ? gettype($data['clothes_data']) : 'missing',
    'weather_type' => isset($data['weather']) ? gettype($data['weather']) : 'missing'
]);

// 验证必要参数
if (!isset($data['clothes_data']) || !isset($data['weather'])) {
    logDebug("错误: 缺少必要参数", ['keys' => array_keys($data)]);
    echo json_encode(['error' => '缺少必要参数']);
    exit;
}

// 解析数据
$clothes_data = isset($data['clothes_data']) ? $data['clothes_data'] : null;
$weather = isset($data['weather']) ? $data['weather'] : null;
$season = isset($data['season']) ? $data['season'] : null;
$custom_category_hint = isset($data['custom_category_hint']) ? $data['custom_category_hint'] : '';
$clothing_stats = isset($data['clothing_stats']) ? $data['clothing_stats'] : [];

// 确保clothes_data是一个数组
if (is_string($clothes_data)) {
    $clothes_data = json_decode($clothes_data, true);
}

// 确保weather是一个数组
if (is_string($weather)) {
    $weather = json_decode($weather, true);
}

// 确保clothing_stats是一个数组
if (is_string($clothing_stats)) {
    $clothing_stats = json_decode($clothing_stats, true);
}

// 检查数据有效性
if (!$clothes_data || !$weather) {
    logDebug("错误: 解析clothes_data或weather失败", [
        'clothes_data_valid' => is_array($clothes_data),
        'clothes_data_type' => gettype($clothes_data),
        'weather_valid' => is_array($weather),
        'weather_type' => gettype($weather)
    ]);
    echo json_encode(['error' => '解析clothes_data或weather失败']);
    exit;
}

logDebug("接收到的衣物数据统计", [
    'tops' => count($clothes_data['tops'] ?? []),
    'pants' => count($clothes_data['pants'] ?? []),
    'skirts' => count($clothes_data['skirts'] ?? []),
    'outerwears' => count($clothes_data['outerwears'] ?? []),
    'shoes' => count($clothes_data['shoes'] ?? []),
    'accessories' => count($clothes_data['accessories'] ?? []),
    'bags' => count($clothes_data['bags'] ?? [])
]);

// 检查是否有足够的衣物用于推荐
$totalTopItems = count($clothes_data['tops'] ?? []);
$totalBottomItems = count($clothes_data['pants'] ?? []) + count($clothes_data['skirts'] ?? []);

if ($totalTopItems < 1 || $totalBottomItems < 1) {
    $errorMessage = "没有足够的衣物用于生成穿搭推荐";
    logDebug("错误: " . $errorMessage, [
        'tops_count' => $totalTopItems,
        'bottoms_count' => $totalBottomItems
    ]);
    echo json_encode([
        'error' => $errorMessage,
        'recommendation_hint' => '您需要至少添加一件上衣和一件下装(裤子或裙子)才能生成推荐',
        'debug_info' => [
            'tops_count' => $totalTopItems,
            'bottoms_count' => $totalBottomItems
        ]
    ]);
    exit;
}

logDebug("接收到的天气数据", [
    'temp' => $weather['temp'] ?? 'unknown',
    'text' => $weather['text'] ?? 'unknown',
    'city' => $weather['city'] ?? 'unknown',
    'humidity' => $weather['humidity'] ?? 'unknown',
    'windDir' => $weather['windDir'] ?? 'unknown',
    'windScale' => $weather['windScale'] ?? 'unknown'
]);

// 配置Gemini API密钥
// 注意：实际部署时，应使用环境变量或安全配置存储API密钥
$apiKey = 'AIzaSyD1-g64EwoKNcvs0LeAn9hbyHJRuKj0Slg'; // 实际API密钥
$model = 'gemini-1.5-flash'; // 使用的模型名称

// 创建衣物ID映射表，用于后续查找
$clothes_by_id = [];
foreach ($clothes_data as $category => $items) {
    foreach ($items as $item) {
        if (isset($item['id'])) {
            $clothes_by_id[$item['id']] = [
                'category' => $category,
                'name' => $item['name'],
                'tags' => $item['tags'] ?? '',
                'description' => $item['description'] ?? []
            ];
        }
    }
}

// 将季节转换为中文
$season_map = [
    'spring' => '春季',
    'summer' => '夏季',
    'autumn' => '秋季',
    'winter' => '冬季'
];
$season_cn = isset($season_map[$season]) ? $season_map[$season] : '未知季节';

// 构建提示词
$prompt = "你是一位专业的穿搭顾问，我想请你根据我的衣物库和当前天气情况，创建一套完整的穿搭搭配。\n\n";

// 天气信息部分
$prompt .= "【当前天气情况】\n";
$prompt .= "- 城市：" . ($weather['city'] ?? '未知城市') . "\n";
$prompt .= "- 温度：" . ($weather['temp'] ?? '未知') . "℃\n";
$prompt .= "- 天气状况：" . ($weather['text'] ?? '未知') . "\n";
$prompt .= "- 湿度：" . ($weather['humidity'] ?? '未知') . "%\n";
$prompt .= "- 风向：" . ($weather['windDir'] ?? '未知') . "\n";
$prompt .= "- 风力等级：" . ($weather['windScale'] ?? '未知') . "\n";
$prompt .= "- 季节：" . $season_cn . "\n\n";

// 添加衣物库信息
$prompt .= "【我的衣物库】\n";

// 上衣
if (!empty($clothes_data['tops'])) {
    $prompt .= "上衣（" . count($clothes_data['tops']) . "件）：\n";
    foreach ($clothes_data['tops'] as $index => $top) {
        $color = '';
        if (is_array($top['description']) && isset($top['description']['颜色'])) {
            $color = $top['description']['颜色'];
        } elseif (is_string($top['description'])) {
            $desc = json_decode($top['description'], true);
            if ($desc && isset($desc['颜色'])) {
                $color = $desc['颜色'];
            }
        }
        $prompt .= "- ID:" . $top['id'] . ", " . $top['name'] . ($color ? ", 颜色:" . $color : "") . "\n";
        // 限制数量，避免提示词过长
        if ($index >= 9) {
            $prompt .= "- ... 等共" . count($clothes_data['tops']) . "件上衣\n";
            break;
        }
    }
    $prompt .= "\n";
}

// 裤子
if (!empty($clothes_data['pants'])) {
    $prompt .= "裤子（" . count($clothes_data['pants']) . "件）：\n";
    foreach ($clothes_data['pants'] as $index => $pant) {
        $color = '';
        if (is_array($pant['description']) && isset($pant['description']['颜色'])) {
            $color = $pant['description']['颜色'];
        } elseif (is_string($pant['description'])) {
            $desc = json_decode($pant['description'], true);
            if ($desc && isset($desc['颜色'])) {
                $color = $desc['颜色'];
            }
        }
        $prompt .= "- ID:" . $pant['id'] . ", " . $pant['name'] . ($color ? ", 颜色:" . $color : "") . "\n";
        // 限制数量，避免提示词过长
        if ($index >= 9) {
            $prompt .= "- ... 等共" . count($clothes_data['pants']) . "件裤子\n";
            break;
        }
    }
    $prompt .= "\n";
}

// 裙子
if (!empty($clothes_data['skirts'])) {
    $prompt .= "裙子（" . count($clothes_data['skirts']) . "件）：\n";
    foreach ($clothes_data['skirts'] as $index => $skirt) {
        $color = '';
        if (is_array($skirt['description']) && isset($skirt['description']['颜色'])) {
            $color = $skirt['description']['颜色'];
        } elseif (is_string($skirt['description'])) {
            $desc = json_decode($skirt['description'], true);
            if ($desc && isset($desc['颜色'])) {
                $color = $desc['颜色'];
            }
        }
        $prompt .= "- ID:" . $skirt['id'] . ", " . $skirt['name'] . ($color ? ", 颜色:" . $color : "") . "\n";
        // 限制数量，避免提示词过长
        if ($index >= 9) {
            $prompt .= "- ... 等共" . count($clothes_data['skirts']) . "件裙子\n";
            break;
        }
    }
    $prompt .= "\n";
}

// 外套
if (!empty($clothes_data['outerwears'])) {
    $prompt .= "外套（" . count($clothes_data['outerwears']) . "件）：\n";
    foreach ($clothes_data['outerwears'] as $index => $outerwear) {
        $color = '';
        if (is_array($outerwear['description']) && isset($outerwear['description']['颜色'])) {
            $color = $outerwear['description']['颜色'];
        } elseif (is_string($outerwear['description'])) {
            $desc = json_decode($outerwear['description'], true);
            if ($desc && isset($desc['颜色'])) {
                $color = $desc['颜色'];
            }
        }
        $prompt .= "- ID:" . $outerwear['id'] . ", " . $outerwear['name'] . ($color ? ", 颜色:" . $color : "") . "\n";
        // 限制数量，避免提示词过长
        if ($index >= 9) {
            $prompt .= "- ... 等共" . count($clothes_data['outerwears']) . "件外套\n";
            break;
        }
    }
    $prompt .= "\n";
}

// 鞋子
if (!empty($clothes_data['shoes'])) {
    $prompt .= "鞋子（" . count($clothes_data['shoes']) . "双）：\n";
    foreach ($clothes_data['shoes'] as $index => $shoe) {
        $color = '';
        if (is_array($shoe['description']) && isset($shoe['description']['颜色'])) {
            $color = $shoe['description']['颜色'];
        } elseif (is_string($shoe['description'])) {
            $desc = json_decode($shoe['description'], true);
            if ($desc && isset($desc['颜色'])) {
                $color = $desc['颜色'];
            }
        }
        $prompt .= "- ID:" . $shoe['id'] . ", " . $shoe['name'] . ($color ? ", 颜色:" . $color : "") . "\n";
        // 限制数量，避免提示词过长
        if ($index >= 9) {
            $prompt .= "- ... 等共" . count($clothes_data['shoes']) . "双鞋子\n";
            break;
        }
    }
    $prompt .= "\n";
}

// 配饰
if (!empty($clothes_data['accessories'])) {
    $prompt .= "配饰（" . count($clothes_data['accessories']) . "件）：\n";
    foreach ($clothes_data['accessories'] as $index => $accessory) {
        $prompt .= "- ID:" . $accessory['id'] . ", " . $accessory['name'] . "\n";
        // 限制数量，避免提示词过长
        if ($index >= 4) {
            $prompt .= "- ... 等共" . count($clothes_data['accessories']) . "件配饰\n";
            break;
        }
    }
    $prompt .= "\n";
}

// 包包
if (!empty($clothes_data['bags'])) {
    $prompt .= "包包（" . count($clothes_data['bags']) . "个）：\n";
    foreach ($clothes_data['bags'] as $index => $bag) {
        $prompt .= "- ID:" . $bag['id'] . ", " . $bag['name'] . "\n";
        // 限制数量，避免提示词过长
        if ($index >= 4) {
            $prompt .= "- ... 等共" . count($clothes_data['bags']) . "个包包\n";
            break;
        }
    }
    $prompt .= "\n";
}

// 添加天气穿搭建议
$prompt .= "【任务】\n";
$prompt .= "请根据上述天气情况和我的衣物库，为我推荐一套最适合今天穿着的完整穿搭组合。\n";
$prompt .= "考虑以下因素：\n";
$prompt .= "1. 温度适应性：选择适合" . ($weather['temp'] ?? '未知') . "℃温度的衣物组合\n";
$prompt .= "2. 天气应对：考虑" . ($weather['text'] ?? '未知') . "天气的特点，如是否需要防雨、防晒等\n";
$prompt .= "3. 季节感：符合" . $season_cn . "的穿搭风格\n";
$prompt .= "4. 颜色搭配：选择颜色协调的衣物组合\n";
$prompt .= "5. 整体风格：保持整体穿搭风格的一致性\n\n";

// 输出格式要求
$prompt .= "【输出格式要求】\n";
$prompt .= "请以JSON格式返回推荐结果，包含以下字段：\n";
$prompt .= "```json\n";
$prompt .= "{\n";
$prompt .= '  "outfit": {\n';
$prompt .= '    "top": "上衣ID",\n';
$prompt .= '    "bottom": "下装ID（裤子或裙子）",\n';
$prompt .= '    "outerwear": "外套ID（如果需要）",\n';
$prompt .= '    "shoes": "鞋子ID",\n';
$prompt .= '    "accessories": "配饰ID（可选）",\n';
$prompt .= '    "bag": "包包ID（可选）"\n';
$prompt .= "  },\n";
$prompt .= '  "reasons": {\n';
$prompt .= '    "top": "选择这件上衣的理由，考虑天气因素",\n';
$prompt .= '    "bottom": "选择这件下装的理由，考虑天气因素",\n';
$prompt .= '    "outerwear": "选择这件外套的理由，考虑天气因素",\n';
$prompt .= '    "shoes": "选择这双鞋的理由，考虑天气因素",\n';
$prompt .= '    "accessories": "选择这件配饰的理由（如果有）",\n';
$prompt .= '    "bag": "选择这个包包的理由（如果有）"\n';
$prompt .= "  },\n";
$prompt .= '  "outfit_summary": "整体穿搭总结，解释为什么这套穿搭适合当前天气和季节"\n';
$prompt .= "}\n";
$prompt .= "```\n\n";

$prompt .= "请确保每个ID都是我的衣物库中实际存在的ID。如果某类衣物缺失或不适合当前天气，可以省略该类别。但上衣和下装（裤子或裙子）是必须的，除非我的衣物库中没有这些类别的衣物。\n";
$prompt .= "每个理由应该包含对天气因素的考虑，例如如何应对当前的温度、天气状况等。\n";
$prompt .= "重要提示：必须返回一套完整的穿搭组合，而不是只推荐一件衣物。请确保推荐的穿搭包含多件衣物，至少包括上衣和下装。如果有合适的外套、鞋子等，也请一并推荐。\n";

logDebug("构建的提示词", ['prompt_length' => strlen($prompt), 'prompt_preview' => substr($prompt, 0, 500) . '...']);

// 调用Gemini API
$apiUrl = "https://generativelanguage.googleapis.com/v1beta/models/{$model}:generateContent?key={$apiKey}";

// 构建API请求数据
$requestData = [
    'contents' => [
        [
            'parts' => [
                [
                    'text' => $prompt
                ]
            ]
        ]
    ],
    'generationConfig' => [
        'temperature' => 0.7,
        'topK' => 40,
        'topP' => 0.95,
        'maxOutputTokens' => 2048,
    ]
];

// 发送请求
$ch = curl_init($apiUrl);
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($requestData),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json'
    ],
    CURLOPT_TIMEOUT => 30
]);

logDebug("正在调用Gemini API");
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

// 检查请求是否成功
if ($error) {
    logDebug("API调用失败", ['curl_error' => $error]);
    echo json_encode(['error' => 'API调用失败: ' . $error]);
    exit;
}

if ($httpCode !== 200) {
    logDebug("API返回非200状态码", ['http_code' => $httpCode, 'response' => substr($response, 0, 1000)]);
    echo json_encode(['error' => 'API返回错误状态码: ' . $httpCode]);
    exit;
}

// 解析响应
$responseData = json_decode($response, true);
logDebug("API响应解析结果", ['response_structure' => array_keys($responseData)]);

if (!isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
    logDebug("API响应格式不符合预期", ['response' => substr($response, 0, 1000)]);
    echo json_encode(['error' => 'API响应格式不符合预期']);
    exit;
}

// 获取生成的文本
$generatedText = $responseData['candidates'][0]['content']['parts'][0]['text'];
logDebug("生成的文本", ['text_length' => strlen($generatedText), 'text_preview' => substr($generatedText, 0, 500) . '...']);

// 从文本中提取JSON
preg_match('/```json\s*(.*?)\s*```/s', $generatedText, $matches);
$jsonText = isset($matches[1]) ? $matches[1] : $generatedText;

// 尝试解析JSON
try {
    $outfit = json_decode($jsonText, true);
    
    if (!$outfit || json_last_error() !== JSON_ERROR_NONE) {
        // 如果解析失败，尝试提取更宽松的JSON格式
        preg_match('/\{.*\}/s', $generatedText, $matches);
        if (isset($matches[0])) {
            $outfit = json_decode($matches[0], true);
        }
    }
    
    if (!$outfit || json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("JSON解析失败: " . json_last_error_msg());
    }
} catch (Exception $e) {
    logDebug("解析生成的JSON失败", ['error' => $e->getMessage(), 'text' => $jsonText]);
    echo json_encode(['error' => '解析生成的JSON失败: ' . $e->getMessage()]);
    exit;
}

logDebug("解析后的穿搭数据", $outfit);

// 返回结果
echo json_encode([
    'success' => true,
    'data' => $outfit
]);

/**
 * 将分类代码转换为中文名称
 * @param string $category 分类代码
 * @return string 中文名称
 */
function translateCategory($category) {
    $categoryMap = [
        'tops' => '上衣',
        'pants' => '裤子',
        'skirts' => '裙子',
        'outerwear' => '外套',
        'outerwears' => '外套',
        'shoes' => '鞋子',
        'accessories' => '配饰',
        'bags' => '包包'
    ];
    
    return isset($categoryMap[$category]) ? $categoryMap[$category] : $category;
} 