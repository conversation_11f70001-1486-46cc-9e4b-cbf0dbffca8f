<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公告管理 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        .search-box {
            display: flex;
            margin-bottom: 20px;
            align-items: center;
        }
        
        .search-input {
            flex: 1;
            height: 36px;
            padding: 0 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            margin-right: 10px;
        }
        
        .search-btn {
            height: 36px;
            padding: 0 15px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .search-btn:hover {
            background-color: #40a9ff;
        }
        
        .status-filter {
            margin-left: 10px;
            height: 36px;
            padding: 0 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        
        .add-btn {
            height: 36px;
            padding: 0 15px;
            background-color: #52c41a;
            color: white;
            border: none;
            border-radius: 4px;
            margin-left: auto;
            cursor: pointer;
        }
        
        .add-btn:hover {
            background-color: #73d13d;
        }
        
        .announcement-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .announcement-table th, 
        .announcement-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .announcement-table th {
            background-color: #fafafa;
            font-weight: 500;
        }
        
        .announcement-table tr:hover {
            background-color: #f5f5f5;
        }
        
        .action-btn {
            padding: 5px 10px;
            border-radius: 4px;
            border: none;
            margin-right: 5px;
            cursor: pointer;
            color: white;
            font-size: 12px;
        }
        
        .view-btn {
            background-color: #1890ff;
        }
        
        .view-btn:hover {
            background-color: #40a9ff;
        }
        
        .edit-btn {
            background-color: #faad14;
        }
        
        .edit-btn:hover {
            background-color: #ffc53d;
        }
        
        .delete-btn {
            background-color: #f5222d;
        }
        
        .delete-btn:hover {
            background-color: #ff4d4f;
        }
        
        .toggle-btn {
            background-color: #13c2c2;
        }
        
        .toggle-btn:hover {
            background-color: #36cfc9;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
        }
        
        .pagination button {
            padding: 5px 12px;
            margin: 0 5px;
            border: 1px solid #d9d9d9;
            background-color: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .pagination button:disabled {
            color: #d9d9d9;
            cursor: not-allowed;
        }
        
        .pagination button:hover:not(:disabled) {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .page-info {
            margin: 0 10px;
        }
        
        .no-data {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
        }
        
        .status-active {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        
        .status-inactive {
            background-color: #fff2e8;
            color: #fa8c16;
        }
        
        .status-expired {
            background-color: #f5f5f5;
            color: #999;
        }
        
        .content-preview {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .alert {
            padding: 10px 15px;
            margin-bottom: 15px;
            border-radius: 4px;
            display: none;
        }
        
        .alert-success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        
        .alert-error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #f5222d;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>公告管理</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="card">
                <div id="successAlert" class="alert alert-success">
                    操作成功！
                </div>
                
                <div id="errorAlert" class="alert alert-error">
                    操作失败：<span id="errorMessage"></span>
                </div>
                
                <div class="search-box">
                    <input type="text" id="searchInput" class="search-input" placeholder="搜索公告标题...">
                    <button id="searchBtn" class="search-btn">搜索</button>
                    <select id="statusFilter" class="status-filter">
                        <option value="">所有状态</option>
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                    <button id="addBtn" class="add-btn">添加公告</button>
                </div>
                
                <table class="announcement-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>标题</th>
                            <th>内容预览</th>
                            <th>开始时间</th>
                            <th>结束时间</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="announcementTableBody">
                        <!-- 公告列表将通过JavaScript动态加载 -->
                    </tbody>
                </table>
                
                <div id="noData" class="no-data" style="display: none;">
                    暂无公告数据
                </div>
                
                <div class="pagination">
                    <button id="prevBtn" disabled>上一页</button>
                    <div class="page-info">第 <span id="currentPage">1</span> 页，共 <span id="totalPages">1</span> 页</div>
                    <button id="nextBtn" disabled>下一页</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化侧边栏，设置当前活动项为announcement
            Sidebar.init('announcement');
            
            // 获取DOM元素
            const announcementTableBody = document.getElementById('announcementTableBody');
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');
            const currentPageSpan = document.getElementById('currentPage');
            const totalPagesSpan = document.getElementById('totalPages');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const addBtn = document.getElementById('addBtn');

            // 从localStorage获取token
            const token = localStorage.getItem('admin_token');
            if (!token) {
                window.location.href = 'index.html';
                return;
            }
            
            // 设置管理员名称
            const adminInfo = JSON.parse(localStorage.getItem('admin_info')) || {};
            const adminName = document.getElementById('adminName');
            adminName.textContent = adminInfo.username || '管理员';
            
            // 注销功能
            const logoutBtn = document.getElementById('logoutBtn');
            logoutBtn.addEventListener('click', function() {
                localStorage.removeItem('admin_token');
                localStorage.removeItem('admin_info');
                window.location.href = 'index.html';
            });
            
            // 分页变量
            let currentPage = 1;
            let limit = 10;
            let totalItems = 0;
            let search = '';
            let status = '';
            
            // 初始化页面
            initPage();
            
            // 初始化页面
            function initPage() {
                // 加载公告列表
                loadAnnouncements();
                
                // 搜索按钮点击事件
                const searchBtn = document.getElementById('searchBtn');
                searchBtn.addEventListener('click', function() {
                    search = searchInput.value.trim();
                    status = statusFilter.value;
                    currentPage = 1;
                    loadAnnouncements();
                });
                
                // 搜索输入框回车事件
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        search = searchInput.value.trim();
                        status = statusFilter.value;
                        currentPage = 1;
                        loadAnnouncements();
                    }
                });
                
                // 状态筛选器变更事件
                statusFilter.addEventListener('change', function() {
                    search = searchInput.value.trim();
                    status = statusFilter.value;
                    currentPage = 1;
                    loadAnnouncements();
                });
                
                // 添加公告按钮点击事件
                addBtn.addEventListener('click', function() {
                    window.location.href = 'announcement_edit.html';
                });
                
                // 分页按钮事件
                prevBtn.addEventListener('click', function() {
                    if (currentPage > 1) {
                        currentPage--;
                        loadAnnouncements();
                    }
                });
                
                nextBtn.addEventListener('click', function() {
                    if (currentPage < Math.ceil(totalItems / limit)) {
                        currentPage++;
                        loadAnnouncements();
                    }
                });
            }
            
            // 加载公告列表
            function loadAnnouncements() {
                // 构建URL
                let url = `../login_backend/get_announcements_list.php?page=${currentPage}&limit=${limit}`;
                if (search) {
                    url += `&search=${encodeURIComponent(search)}`;
                }
                if (status !== '') {
                    url += `&status=${encodeURIComponent(status)}`;
                }
                
                // 显示加载中
                announcementTableBody.innerHTML = '<tr><td colspan="8" class="text-center">加载中...</td></tr>';
                
                // 发送请求
                fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': token
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        throw new Error(data.msg || '获取公告列表失败');
                    }
                    
                    // 清空表格
                    announcementTableBody.innerHTML = '';
                    
                    // 渲染表格
                    const announcements = data.data.list;
                    if (announcements.length === 0) {
                        announcementTableBody.innerHTML = '<tr><td colspan="8" class="text-center">暂无数据</td></tr>';
                    } else {
                        announcements.forEach(item => {
                            const tr = document.createElement('tr');
                            
                            // 格式化状态
                            const statusText = parseInt(item.status) === 1 ? 
                                '<span class="badge badge-success">已启用</span>' : 
                                '<span class="badge badge-danger">已禁用</span>';
                            
                            // 格式化时间
                            const startTime = formatDateTime(item.start_time);
                            const endTime = formatDateTime(item.end_time);
                            const createdAt = formatDateTime(item.created_at);
                            
                            // 构建操作按钮
                            const viewBtn = `<a href="announcement_edit.html?id=${item.id}&view=1" class="btn btn-sm btn-info mr-1">查看</a>`;
                            const editBtn = `<a href="announcement_edit.html?id=${item.id}" class="btn btn-sm btn-primary mr-1">编辑</a>`;
                            const toggleBtn = `<button class="btn btn-sm ${parseInt(item.status) === 1 ? 'btn-warning' : 'btn-success'} mr-1" onclick="toggleStatus(${item.id}, ${item.status})">${parseInt(item.status) === 1 ? '禁用' : '启用'}</button>`;
                            const deleteBtn = `<button class="btn btn-sm btn-danger" onclick="deleteAnnouncement(${item.id})">删除</button>`;
                            
                            tr.innerHTML = `
                                <td>${item.id}</td>
                                <td>${item.title}</td>
                                <td>${item.content_preview || item.content}</td>
                                <td>${startTime}</td>
                                <td>${endTime}</td>
                                <td><span class="status-badge ${parseInt(item.status) === 1 ? 'status-active' : 'status-inactive'}">${statusText}</span></td>
                                <td>${createdAt}</td>
                                <td>
                                    <button class="action-btn view-btn" onclick="window.location.href='announcement_edit.html?id=${item.id}&view=1'">查看</button>
                                    <button class="action-btn edit-btn" onclick="window.location.href='announcement_edit.html?id=${item.id}'">编辑</button>
                                    <button class="action-btn toggle-btn" data-id="${item.id}" data-status="${item.status}" onclick="toggleStatus(${item.id}, ${item.status})">
                                        ${parseInt(item.status) === 1 ? '禁用' : '启用'}
                                    </button>
                                    <button class="action-btn delete-btn" onclick="deleteAnnouncement(${item.id})">删除</button>
                                </td>
                            `;
                            
                            announcementTableBody.appendChild(tr);
                        });
                    }
                    
                    // 更新分页信息
                    totalItems = data.data.total;
                    currentPageSpan.textContent = currentPage;
                    totalPagesSpan.textContent = Math.ceil(totalItems / limit);
                    
                    // 更新分页按钮状态
                    updatePaginationButtons();
                })
                .catch(err => {
                    console.error('获取公告列表失败:', err);
                    announcementTableBody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">获取数据失败: ${err.message}</td></tr>`;
                });
            }
            
            // 更新分页按钮状态
            function updatePaginationButtons() {
                const maxPage = Math.ceil(totalItems / limit);
                
                prevBtn.disabled = currentPage <= 1;
                nextBtn.disabled = currentPage >= maxPage;
            }
            
            // 格式化日期时间
            function formatDateTime(dateTimeStr) {
                if (!dateTimeStr) return '';
                
                const date = new Date(dateTimeStr);
                if (isNaN(date.getTime())) return dateTimeStr;
                
                const year = date.getFullYear();
                const month = padZero(date.getMonth() + 1);
                const day = padZero(date.getDate());
                const hours = padZero(date.getHours());
                const minutes = padZero(date.getMinutes());
                const seconds = padZero(date.getSeconds());
                
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            }
            
            // 补零函数
            function padZero(num) {
                return num < 10 ? '0' + num : num;
            }
        });
        
        // 切换公告状态
        function toggleStatus(id, currentStatus) {
            if (!confirm('确定要' + (parseInt(currentStatus) === 1 ? '禁用' : '启用') + '该公告吗？')) {
                return;
            }
            
            const token = localStorage.getItem('admin_token');
            if (!token) {
                window.location.href = 'index.html';
                return;
            }
            
            // 新状态是当前状态的反向
            const newStatus = parseInt(currentStatus) === 1 ? 0 : 1;
            
            // 发送请求
            fetch('../login_backend/admin_save_announcement.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': token
                },
                body: JSON.stringify({
                    id: id,
                    status: newStatus
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.msg || '操作失败');
                }
                
                alert('操作成功');
                // 刷新列表
                window.location.reload();
            })
            .catch(err => {
                console.error('操作失败:', err);
                alert('操作失败: ' + err.message);
            });
        }
        
        // 删除公告
        function deleteAnnouncement(id) {
            if (!confirm('确定要删除该公告吗？此操作不可恢复。')) {
                return;
            }
            
            const token = localStorage.getItem('admin_token');
            if (!token) {
                window.location.href = 'index.html';
                return;
            }
            
            // 发送请求
            fetch('../login_backend/delete_announcement.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': token
                },
                body: JSON.stringify({
                    id: id
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.msg || '删除失败');
                }
                
                alert('删除成功');
                // 刷新列表
                window.location.reload();
            })
            .catch(err => {
                console.error('删除失败:', err);
                alert('删除失败: ' + err.message);
            });
        }
    </script>
</body>
</html> 