<?php
/**
 * 管理员获取形象分析详情API
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

// 检查是否有Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '需要授权头']);
    exit;
}

// 验证管理员token
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或过期的令牌']);
    exit;
}

// 检查是否提供了分析ID
if (!isset($_GET['id']) || empty($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少分析ID']);
    exit;
}

$analysisId = intval($_GET['id']);

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

// 查询分析记录和用户信息
$sql = "SELECT a.*, u.nickname, u.avatar_url 
        FROM user_image_analysis a
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.id = :id";
$stmt = $conn->prepare($sql);
$stmt->bindParam(':id', $analysisId, PDO::PARAM_INT);
$stmt->execute();
$analysis = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$analysis) {
    http_response_code(404);
    echo json_encode(['error' => true, 'msg' => '未找到形象分析记录']);
    exit;
}

// 处理结果
$result = $analysis;

// 处理照片URL
if (!empty($analysis['photo_urls'])) {
    $result['photo_urls'] = json_decode($analysis['photo_urls'], true);
} else {
    $result['photo_urls'] = [];
}

// 处理分析结果
if ($analysis['status'] === 'completed' && !empty($analysis['analysis_result'])) {
    $result['analysis_result'] = json_decode($analysis['analysis_result'], true);
} else {
    $result['analysis_result'] = null;
}

// 添加默认值
$result['nickname'] = $result['nickname'] ?? '未知用户';

// 返回结果
echo json_encode([
    'error' => false,
    'data' => $result
]); 