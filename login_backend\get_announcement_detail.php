<?php
// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入数据库连接
require_once('db.php');
require_once('auth.php');

// 检查授权
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => '未授权访问',
        'code' => 401
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// 验证管理员token
try {
    $auth = new Auth();
    $payload = $auth->verifyAdminToken($token);
    
    if (!$payload) {
        echo json_encode([
            'error' => true,
            'msg' => '无效的授权令牌',
            'code' => 401
        ]);
        exit;
    }
    
    $db = new Database();
    $conn = $db->getConnection();
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => $e->getMessage(),
        'code' => 500
    ]);
    exit;
}

// 获取公告ID
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// 验证参数
if ($id <= 0) {
    echo json_encode([
        'error' => true,
        'msg' => '无效的公告ID',
        'code' => 400
    ]);
    exit;
}

try {
    // 查询公告详情
    $sql = "SELECT id, title, content, start_time, end_time, status, created_at FROM announcements WHERE id = :id";
    $stmt = $conn->prepare($sql);
    $stmt->bindValue(':id', $id, PDO::PARAM_INT);
    $stmt->execute();
    
    // 获取结果
    $announcement = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$announcement) {
        echo json_encode([
            'error' => true,
            'msg' => '公告不存在',
            'code' => 404
        ]);
        exit;
    }
    
    // 返回结果
    echo json_encode([
        'error' => false,
        'msg' => 'success',
        'data' => $announcement
    ]);
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => $e->getMessage(),
        'code' => 500
    ]);
}
?> 