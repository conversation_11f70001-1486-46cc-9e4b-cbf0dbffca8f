<?php
/**
 * 获取个人形象分析结果API
 * 获取指定ID的形象分析结果
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 日志功能
function writeAnalysisLog($message, $data = null) {
    $logDir = __DIR__ . '/logs';
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/image_analysis_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $logData = "[{$timestamp}] {$message}";
    
    if ($data !== null) {
        $logData .= " - " . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    file_put_contents($logFile, $logData . PHP_EOL, FILE_APPEND);
}

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

writeAnalysisLog("收到获取分析请求", ['request' => $_SERVER['REQUEST_URI'], 'params' => $_GET]);

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    writeAnalysisLog("请求方法错误", ['method' => $_SERVER['REQUEST_METHOD']]);
    echo json_encode(['error' => true, 'msg' => 'Method Not Allowed']);
    exit;
}

// 检查是否有Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    writeAnalysisLog("缺少Authorization头");
    echo json_encode(['error' => true, 'msg' => 'Authorization header is required']);
    exit;
}

// 验证token
$token = $_SERVER['HTTP_AUTHORIZATION'];
$isShareToken = false;

// 检查是否是分享token (以SHARE_开头或从微信分享链接访问)
if (strpos($token, 'SHARE_') === 0) {
    $isShareToken = true;
    writeAnalysisLog("检测到分享Token访问", ['token' => substr($token, 0, 20) . '...']);
}

$auth = new Auth();
$payload = $auth->verifyToken($token);

// 如果是分享token但验证失败，我们仍然允许访问
// 因为分享token可能是使用特殊格式的标记而不是真正的JWT
if (!$payload && $isShareToken) {
    writeAnalysisLog("分享Token验证失败但允许继续", ['token_start' => substr($token, 0, 20) . '...']);
    // 为分享视图创建一个模拟的payload
    $payload = ['sub' => 0, 'is_share_view' => true];
} else if (!$payload) {
    http_response_code(401);
    writeAnalysisLog("无效或过期的Token");
    echo json_encode(['error' => true, 'msg' => 'Invalid or expired token']);
    exit;
}

// 记录访问类型
$isShareView = $isShareToken || (isset($payload['is_share_view']) && $payload['is_share_view']);
writeAnalysisLog("访问类型", ['is_share_view' => $isShareView ? 'yes' : 'no']);

// 获取用户ID
$userId = $payload['sub'];
writeAnalysisLog("用户ID", ['user_id' => $userId]);

// 检查是否提供了分析ID
if (!isset($_GET['id']) || empty($_GET['id'])) {
    http_response_code(400);
    writeAnalysisLog("缺少分析ID");
    echo json_encode(['error' => true, 'msg' => '缺少分析ID']);
    exit;
}

$analysisId = intval($_GET['id']);
writeAnalysisLog("请求分析ID", ['analysis_id' => $analysisId]);

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

// 查询分析记录 - 如果是分享查看，则不限制用户ID
if ($isShareView) {
    $stmt = $conn->prepare("
        SELECT * FROM user_image_analysis 
        WHERE id = :analysis_id
    ");
    $stmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
    writeAnalysisLog("分享查看模式，不限制用户ID");
} else {
    $stmt = $conn->prepare("
        SELECT * FROM user_image_analysis 
        WHERE id = :analysis_id AND user_id = :user_id
    ");
    $stmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
}

$stmt->execute();
$analysis = $stmt->fetch(PDO::FETCH_ASSOC);

writeAnalysisLog("分析记录查询结果", ['found' => !empty($analysis), 'payment_status' => $analysis ? $analysis['payment_status'] : 'n/a']);

if (!$analysis) {
    http_response_code(404);
    writeAnalysisLog("未找到分析记录", ['analysis_id' => $analysisId, 'user_id' => $userId]);
    echo json_encode(['error' => true, 'msg' => '未找到形象分析记录']);
    exit;
}

// 记录原始数据
writeAnalysisLog("分析记录详情", [
    'id' => $analysis['id'],
    'user_id' => $analysis['user_id'],
    'payment_status' => $analysis['payment_status'],
    'status' => $analysis['status'],
    'created_at' => $analysis['created_at'],
    'access_type' => $isShareView ? 'shared_view' : 'owner_view'
]);

// 检查支付状态 - 如果是分享查看且已完成，则不检查支付状态
if ($analysis['payment_status'] !== 'paid' && (!$isShareView || $analysis['status'] !== 'completed')) {
    http_response_code(402);
    writeAnalysisLog("分析未支付", ['analysis_id' => $analysisId, 'payment_status' => $analysis['payment_status']]);
    echo json_encode(['error' => true, 'msg' => '该分析尚未支付']);
    exit;
}

// 处理结果
$result = [];
$result['id'] = $analysis['id'];
$result['status'] = $analysis['status'];
$result['created_at'] = $analysis['created_at'];
$result['analysis_time'] = $analysis['analysis_time'];
$result['payment_status'] = $analysis['payment_status'];
$result['is_shared_view'] = $isShareView;

// 将用户数据添加到结果中
$result['user_data'] = [
    'gender' => $analysis['gender'],
    'height' => $analysis['height'],
    'weight' => $analysis['weight'],
    'bust' => $analysis['bust'],
    'waist' => $analysis['waist'],
    'hips' => $analysis['hips'],
    'shoulder_width' => $analysis['shoulder_width'],
    'skin_tone' => $analysis['skin_tone'],
    'face_shape' => $analysis['face_shape'],
    'body_shape' => $analysis['body_shape']
];

// 处理照片URL
if (!empty($analysis['photo_urls'])) {
    $result['photo_urls'] = json_decode($analysis['photo_urls'], true);
}

// 处理分析结果
if ($analysis['status'] === 'completed' && !empty($analysis['analysis_result'])) {
    $result['analysis_result'] = json_decode($analysis['analysis_result'], true);
} else {
    // 如果分析未完成或结果为空，返回状态信息
    $result['analysis_result'] = null;
}

writeAnalysisLog("返回分析结果成功", ['analysis_id' => $analysisId, 'is_shared_view' => $isShareView]);

// 返回结果
echo json_encode([
    'error' => false,
    'data' => $result
]); 