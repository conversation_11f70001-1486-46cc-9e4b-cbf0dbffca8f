const app = getApp();

Page({
  data: {
    userId: null,
    userName: '匿名用户',
    userAvatar: null,
    outfits: [],
    loading: true,
    isEmpty: false,
    currentPage: 1,
    hasMoreData: true,
    loadingMore: false
  },
  
  onLoad: function(options) {
    // 获取传入的用户ID和名称
    const userId = options.user_id;
    const userName = options.user_name ? decodeURIComponent(options.user_name) : '匿名用户';
    
    console.log('用户穿搭列表页面接收到参数:', options);
    
    if (!userId) {
      console.error('未传入用户ID，无法加载数据');
      wx.showToast({
        title: '无效的用户ID',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }
    
    console.log('加载用户ID:', userId, '用户名:', userName, '的穿搭列表');
    
    this.setData({
      userId: userId,
      userName: userName
    });
    
    // 获取用户头像和穿搭列表
    this.loadUserOutfits();
  },
  
  // 下拉刷新
  onPullDownRefresh: function() {
    this.setData({
      currentPage: 1,
      hasMoreData: true
    });
    this.loadUserOutfits().then(() => {
      wx.stopPullDownRefresh();
    });
  },
  
  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMoreData && !this.data.loading && !this.data.loadingMore) {
      this.loadMoreOutfits();
    }
  },
  
  // 加载用户共享的穿搭
  loadUserOutfits: function() {
    this.setData({ loading: true });
    
    console.log('开始请求用户ID:', this.data.userId, '的穿搭列表');
    
    return new Promise((resolve) => {
      wx.request({
        url: `${app.globalData.apiBaseUrl}/get_public_outfits.php`,
        method: 'GET',
        data: {
          user_id: this.data.userId,
          page: 1,
          per_page: 20
        },
        success: (res) => {
          console.log('获取用户穿搭列表响应:', res.data);
          
          if (res.statusCode === 200 && res.data.success) {
            const allOutfits = res.data.data || [];
            
            // 确保只显示指定用户的穿搭
            const outfits = allOutfits.filter(outfit => {
              const outfitUserId = outfit.user_id || outfit.creator_id;
              return outfitUserId && outfitUserId.toString() === this.data.userId.toString();
            });
            
            console.log('成功获取穿搭数据', allOutfits.length, '条, 过滤后', outfits.length, '条');
            
            // 预处理穿搭数据
            this.processOutfitsData(outfits);
            
            this.setData({
              outfits: outfits,
              isEmpty: outfits.length === 0,
              hasMoreData: allOutfits.length >= 20 && outfits.length > 0, // 根据API返回的总数和过滤后的数量判断
              currentPage: 2 // 下一页为2
            });
            
            // 从第一个穿搭获取用户头像
            if (outfits.length > 0 && outfits[0].creator_avatar) {
              this.setData({
                userAvatar: outfits[0].creator_avatar
              });
            }
          } else {
            console.error('获取穿搭失败，响应:', res);
            this.setData({
              outfits: [],
              isEmpty: true,
              hasMoreData: false
            });
            
            wx.showToast({
              title: res.data.msg || '获取穿搭失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          console.error('请求失败:', err);
          this.setData({
            outfits: [],
            isEmpty: true,
            hasMoreData: false
          });
          
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          });
        },
        complete: () => {
          this.setData({ loading: false });
          resolve();
        }
      });
    });
  },
  
  // 加载更多穿搭
  loadMoreOutfits: function() {
    if (this.data.loadingMore || !this.data.hasMoreData) {
      return;
    }
    
    this.setData({ loadingMore: true });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_public_outfits.php`,
      method: 'GET',
      data: {
        user_id: this.data.userId,
        page: this.data.currentPage,
        per_page: 20
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data.success) {
          const allNewOutfits = res.data.data || [];
          
          // 确保只显示指定用户的穿搭
          const newOutfits = allNewOutfits.filter(outfit => {
            const outfitUserId = outfit.user_id || outfit.creator_id;
            return outfitUserId && outfitUserId.toString() === this.data.userId.toString();
          });
          
          if (newOutfits.length > 0) {
            // 预处理穿搭数据
            this.processOutfitsData(newOutfits);
            
            // 合并穿搭数据
            const allOutfits = [...this.data.outfits, ...newOutfits];
            
            this.setData({
              outfits: allOutfits,
              currentPage: this.data.currentPage + 1,
              hasMoreData: newOutfits.length >= 20
            });
          } else {
            this.setData({
              hasMoreData: false
            });
          }
        } else {
          this.setData({
            hasMoreData: false
          });
        }
      },
      fail: (err) => {
        console.error('加载更多失败:', err);
      },
      complete: () => {
        this.setData({ loadingMore: false });
      }
    });
  },
  
  // 处理穿搭数据，生成预览位置
  processOutfitsData: function(outfits) {
    if (!outfits || !outfits.length) return;
    
    outfits.forEach(outfit => {
      // 确保likes_count字段是数字
      outfit.likes_count = parseInt(outfit.likes_count) || 0;
      
      // 格式化日期
      if (outfit.created_at) {
        try {
          const dateObj = new Date(outfit.created_at);
          if (!isNaN(dateObj.getTime())) {
            const year = dateObj.getFullYear();
            const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
            const day = dateObj.getDate().toString().padStart(2, '0');
            outfit.formatted_date = `${year}/${month}/${day}`;
          }
        } catch (e) {
          console.error('日期格式化错误:', e);
        }
      }
      
      // 如果有衣物项目，计算预览位置
      if (outfit.items && outfit.items.length > 0) {
        // 计算所有衣物的边界框
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
        
        // 计算边界
        outfit.items.forEach(item => {
          if (!item.position) {
            item.position = { x: 50, y: 50 };
          }
          if (!item.size) {
            item.size = { width: 100, height: 120 };
          }
          
          const left = item.position.x;
          const top = item.position.y;
          const right = left + item.size.width;
          const bottom = top + item.size.height;
          
          minX = Math.min(minX, left);
          minY = Math.min(minY, top);
          maxX = Math.max(maxX, right);
          maxY = Math.max(maxY, bottom);
        });
        
        // 计算边界框宽高和中心点
        const boundingWidth = maxX - minX;
        const boundingHeight = maxY - minY;
        const centerX = minX + boundingWidth / 2;
        const centerY = minY + boundingHeight / 2;
        
        // 计算缩放比例
        const maxDimension = Math.max(boundingWidth, boundingHeight);
        const maxAllowedSize = 160;
        const scale = maxDimension > 0 ? Math.min(maxAllowedSize / maxDimension, 0.5) : 0.5;
        
        // 计算预览位置
        outfit.items.forEach(item => {
          item.previewPosition = {
            x: 90 + (item.position.x - centerX) * scale,
            y: 90 + (item.position.y - centerY) * scale,
            scale: scale
          };
        });
      }
    });
  },
  
  // 查看穿搭详情
  viewOutfit: function(e) {
    const outfitId = e.currentTarget.dataset.id;
    
    // 跳转到穿搭详情页，并传入fromSquare参数，表示来自穿搭广场
    wx.navigateTo({
      url: `/pages/outfits/detail/detail?id=${outfitId}&fromSquare=true`
    });
  },
  
  // 分享功能
  onShareAppMessage: function() {
    return {
      title: `查看${this.data.userName}的共享穿搭`,
      path: `/pages/user_outfits/index/index?user_id=${this.data.userId}&user_name=${encodeURIComponent(this.data.userName)}`
    };
  }
}); 