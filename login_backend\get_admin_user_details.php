<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] != 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证管理员身份
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 检查用户ID参数
if (!isset($_GET['user_id']) || empty($_GET['user_id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少必要参数: user_id']);
    exit();
}

$userId = (int)$_GET['user_id'];

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

// 查询用户基本信息
$userQuery = "SELECT id, openid, unionid, nickname, avatar_url, gender, status, created_at, updated_at 
              FROM users 
              WHERE id = :user_id";
$userStmt = $conn->prepare($userQuery);
$userStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
$userStmt->execute();
$user = $userStmt->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    http_response_code(404);
    echo json_encode(['error' => true, 'msg' => '用户不存在']);
    exit();
}

// 查询用户衣物统计
$clothesCountQuery = "SELECT COUNT(*) AS total_clothes FROM clothes WHERE user_id = :user_id";
$clothesStmt = $conn->prepare($clothesCountQuery);
$clothesStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
$clothesStmt->execute();
$clothesCount = $clothesStmt->fetch(PDO::FETCH_ASSOC)['total_clothes'];

// 查询用户照片统计
$photosCountQuery = "SELECT COUNT(*) AS total_photos FROM photos WHERE user_id = :user_id";
$photosStmt = $conn->prepare($photosCountQuery);
$photosStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
$photosStmt->execute();
$photosCount = $photosStmt->fetch(PDO::FETCH_ASSOC)['total_photos'];

// 查询用户试衣历史统计
$tryOnCountQuery = "SELECT COUNT(*) AS total_try_on FROM try_on_history WHERE user_id = :user_id";
$tryOnStmt = $conn->prepare($tryOnCountQuery);
$tryOnStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
$tryOnStmt->execute();
$tryOnCount = $tryOnStmt->fetch(PDO::FETCH_ASSOC)['total_try_on'];

// 最近的5条衣物记录
$recentClothesQuery = "SELECT id, name, category, image_url, created_at 
                      FROM clothes 
                      WHERE user_id = :user_id 
                      ORDER BY created_at DESC 
                      LIMIT 5";
$recentClothesStmt = $conn->prepare($recentClothesQuery);
$recentClothesStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
$recentClothesStmt->execute();
$recentClothes = $recentClothesStmt->fetchAll(PDO::FETCH_ASSOC);

// 最近的5条照片记录
$recentPhotosQuery = "SELECT id, image_url, type, created_at 
                     FROM photos 
                     WHERE user_id = :user_id 
                     ORDER BY created_at DESC 
                     LIMIT 5";
$recentPhotosStmt = $conn->prepare($recentPhotosQuery);
$recentPhotosStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
$recentPhotosStmt->execute();
$recentPhotos = $recentPhotosStmt->fetchAll(PDO::FETCH_ASSOC);

// 组合结果
$result = [
    'error' => false,
    'data' => [
        'user' => $user,
        'statistics' => [
            'total_clothes' => (int)$clothesCount,
            'total_photos' => (int)$photosCount,
            'total_try_on' => (int)$tryOnCount
        ],
        'recent_clothes' => $recentClothes,
        'recent_photos' => $recentPhotos
    ]
];

echo json_encode($result); 