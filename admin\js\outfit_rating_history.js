/**
 * 穿搭评分历史模块
 */
const OutfitRatingHistory = {
    // 存储当前评分数据
    data: {
        ratings: []
    },
    
    // 存储分页信息
    pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0
    },
    
    // 存储搜索关键字
    searchKeyword: '',
    
    // API基础路径
    apiBasePath: '../login_backend/',
    
    /**
     * 初始化模块
     */
    init: function() {
        console.log('初始化穿搭评分历史模块');
        
        // 初始化搜索功能
        this.initSearch();
        
        // 初始化模态框
        this.initModal();
        
        // 初始化分页控件
        this.initPagination();
        
        // 加载评分历史数据
        this.loadRatingHistory();
    },
    
    /**
     * 初始化搜索功能
     */
    initSearch: function() {
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.searchKeyword = searchInput.value.trim();
                this.pagination.page = 1; // 重置到第一页
                this.loadRatingHistory();
            });
        }
        
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    searchBtn.click();
                }
            });
        }
    },
    
    /**
     * 初始化模态框
     */
    initModal: function() {
        const modal = document.getElementById('ratingDetailModal');
        const closeButtons = modal.querySelectorAll('.modal-close');
        
        // 关闭按钮事件
        closeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                modal.style.display = 'none';
            });
        });
        
        // 点击模态框外部关闭
        window.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    },
    
    /**
     * 初始化分页控件
     */
    initPagination: function() {
        const prevBtn = document.getElementById('ratingPrevBtn');
        const nextBtn = document.getElementById('ratingNextBtn');
        
        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                if (this.pagination.page > 1) {
                    this.pagination.page--;
                    this.loadRatingHistory();
                }
            });
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                if (this.pagination.page < this.pagination.totalPages) {
                    this.pagination.page++;
                    this.loadRatingHistory();
                }
            });
        }
    },
    
    /**
     * 加载评分历史数据
     */
    loadRatingHistory: function() {
        this.showLoading(true);
        
        // 构建API请求URL
        let url = `${this.apiBasePath}get_outfit_rating_history.php?page=${this.pagination.page}&limit=${this.pagination.limit}`;
        
        if (this.searchKeyword) {
            url += `&search=${encodeURIComponent(this.searchKeyword)}`;
        }
        
        // 获取admin token
        const token = Auth.getToken();
        
        // 发送请求
        fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': token
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败');
            }
            return response.json();
        })
        .then(result => {
            this.showLoading(false);
            
            if (result.error) {
                this.showError(result.msg || '获取评分历史数据失败');
                return;
            }
            
            // 存储数据
            this.data.ratings = result.data.ratings;
            this.pagination = result.data.pagination;
            
            // 更新表格
            this.updateTable();
            // 更新分页信息
            this.updatePagination();
        })
        .catch(error => {
            this.showLoading(false);
            this.showError('获取评分历史数据失败: ' + error.message);
            console.error('获取评分历史数据失败:', error);
        });
    },
    
    /**
     * 更新表格数据
     */
    updateTable: function() {
        const tableBody = document.getElementById('ratingHistoryTable');
        
        if (!tableBody) {
            console.error('找不到表格元素');
            return;
        }
        
        // 清空表格
        tableBody.innerHTML = '';
        
        // 如果没有数据
        if (this.data.ratings.length === 0) {
            const noDataRow = document.createElement('tr');
            noDataRow.innerHTML = `<td colspan="7" class="no-data">暂无评分历史数据</td>`;
            tableBody.appendChild(noDataRow);
            return;
        }
        
        // 填充表格
        this.data.ratings.forEach(rating => {
            const row = document.createElement('tr');
            
            // 格式化日期
            const dateStr = this.formatDate(rating.created_at);
            
            row.innerHTML = `
                <td>${rating.id}</td>
                <td>${this.escapeHtml(rating.user_nickname)}</td>
                <td>${rating.user_id}</td>
                <td>
                    <img 
                        src="${rating.photo_url}" 
                        alt="穿搭照片" 
                        class="thumbnail" 
                        onerror="this.src='images/placeholder.png'"
                    >
                </td>
                <td>${rating.score}</td>
                <td>${dateStr}</td>
                <td>
                    <button 
                        class="action-btn view-btn" 
                        data-id="${rating.id}"
                    >
                        查看详情
                    </button>
                </td>
            `;
            
            tableBody.appendChild(row);
            
            // 为当前行的详情按钮添加事件监听器
            const detailBtn = row.querySelector('.view-btn');
            if (detailBtn) {
                // 使用闭包保存当前评分ID
                detailBtn.addEventListener('click', () => {
                    this.showRatingDetail(rating.id);
                });
            }
        });
        
        // 为表格中的缩略图绑定图片查看器
        if (typeof ImageViewer !== 'undefined') {
            // 确保ImageViewer已初始化
            if (!ImageViewer.isInitialized) {
                ImageViewer.init();
            }
            
            // 延迟一点点时间绑定图片，确保DOM已完全渲染
            setTimeout(() => {
                const thumbnails = tableBody.querySelectorAll('.thumbnail');
                if (thumbnails && thumbnails.length > 0) {
                    console.log(`绑定图片查看器到${thumbnails.length}个缩略图`);
                    ImageViewer.bindImages(thumbnails);
                }
            }, 200);
        } else {
            console.error('ImageViewer未定义，无法绑定图片查看器');
        }
    },
    
    /**
     * 更新分页信息
     */
    updatePagination: function() {
        const pageInfo = document.getElementById('ratingPageInfo');
        const prevBtn = document.getElementById('ratingPrevBtn');
        const nextBtn = document.getElementById('ratingNextBtn');
        
        if (pageInfo) {
            pageInfo.textContent = `第 ${this.pagination.page}/${this.pagination.totalPages} 页`;
        }
        
        if (prevBtn) {
            prevBtn.disabled = this.pagination.page <= 1;
        }
        
        if (nextBtn) {
            nextBtn.disabled = this.pagination.page >= this.pagination.totalPages;
        }
    },
    
    /**
     * 显示评分详情
     * @param {number} id 评分ID
     */
    showRatingDetail: function(id) {
        // 查找对应的评分数据
        const rating = this.data.ratings.find(r => r.id === id);
        
        if (!rating) {
            this.showError('找不到对应的评分记录');
            return;
        }
        
        // 获取模态框元素
        const modal = document.getElementById('ratingDetailModal');
        const modalBody = document.getElementById('ratingDetailModalBody');
        
        if (!modal || !modalBody) {
            console.error('找不到模态框元素');
            return;
        }
        
        // 格式化日期
        const dateStr = this.formatDate(rating.created_at);
        
        // 用户信息HTML
        const userHtml = `
            <div class="user-info">
                <img class="avatar" src="${rating.user_avatar || 'images/default-avatar.png'}" 
                    alt="${this.escapeHtml(rating.user_nickname)}" 
                    onerror="this.src='images/default-avatar.png'">
                <span class="name">${this.escapeHtml(rating.user_nickname)}</span>
                <span class="date">${dateStr}</span>
            </div>
        `;
        
        // 评分细节HTML - 修改为与评论模块相同的样式
        let ratingDetailsHtml = '';
        if (rating.rating_details) {
            const details = rating.rating_details;
            // 获取评分类别（排除overall_score）
            const categories = Object.keys(details).filter(key => key !== 'overall_score' && key !== 'outfit_analysis' && key !== 'improvement');
            
            if (categories.length > 0) {
                ratingDetailsHtml = '<div class="rating-comments">';
                ratingDetailsHtml += '<h4>评分明细</h4>';
                ratingDetailsHtml += '<div class="rating-breakdown">';
                categories.forEach(category => {
                    // 转换类别名称为更友好的显示
                    let displayName;
                    switch (category) {
                        case 'color_coordination':
                            displayName = '色彩搭配';
                            break;
                        case 'style_coherence':
                            displayName = '风格协调';
                            break;
                        case 'proportion':
                            displayName = '比例平衡';
                            break;
                        case 'occasion_appropriateness':
                            displayName = '场合适宜性';
                            break;
                        case 'creativity':
                            displayName = '创意性';
                            break;
                        default:
                            displayName = category.replace(/_/g, ' ');
                            // 首字母大写
                            displayName = displayName.charAt(0).toUpperCase() + displayName.slice(1);
                    }
                    
                    ratingDetailsHtml += `
                        <div class="rating-category">
                            <div class="rating-category-name">${displayName}</div>
                            <div class="rating-category-value">${details[category]}</div>
                        </div>
                    `;
                });
                ratingDetailsHtml += '</div></div>';
            }
        }
        
        // 填充模态框内容 - 重新排序，照片放在顶部
        modalBody.innerHTML = `
            <div class="rating-detail">
                <!-- 1. 照片放在顶部 -->
                <div class="rating-photo">
                    <img src="${rating.photo_url}" alt="穿搭照片" onerror="this.src='images/placeholder.png'">
                </div>
                
                <!-- 2. 用户信息 -->
                ${userHtml}
                
                <!-- 3. 总评分 -->
                <div class="rating-score">
                    总评分: ${rating.score} / 10
                </div>
                
                <!-- 4. 评分明细 -->
                ${ratingDetailsHtml}
                
                <!-- 5. AI点评 -->
                ${rating.ai_comments ? `
                <div class="rating-comments">
                    <h4>AI点评</h4>
                    <p>${this.escapeHtml(rating.ai_comments)}</p>
                </div>
                ` : ''}
                
                <!-- 6. 改进建议 -->
                ${rating.improvement_suggestions ? `
                <div class="rating-comments">
                    <h4>改进建议</h4>
                    <p>${this.escapeHtml(rating.improvement_suggestions)}</p>
                </div>
                ` : ''}
            </div>
        `;
        
        // 显示模态框
        modal.style.display = 'block';
        
        // 为详情模态框中的图片也绑定图片查看器
        if (typeof ImageViewer !== 'undefined' && ImageViewer.isInitialized) {
            setTimeout(() => {
                const detailImg = modal.querySelector('.rating-photo img');
                if (detailImg) {
                    ImageViewer.bindImages([detailImg]);
                }
            }, 100);
        }
    },
    
    /**
     * 格式化日期
     * @param {string} dateString 日期字符串
     * @returns {string} 格式化后的日期字符串
     */
    formatDate: function(dateString) {
        if (!dateString) return '';
        
        const date = new Date(dateString);
        
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
            return dateString;
        }
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}`;
    },
    
    /**
     * 显示/隐藏加载指示器
     * @param {boolean} show 是否显示加载指示器
     */
    showLoading: function(show) {
        const loadingElem = document.getElementById('ratingLoading');
        if (loadingElem) {
            loadingElem.style.display = show ? 'block' : 'none';
        }
    },
    
    /**
     * 显示错误信息
     * @param {string} message 错误信息
     */
    showError: function(message) {
        const errorElem = document.getElementById('ratingError');
        if (errorElem) {
            errorElem.textContent = message;
            errorElem.style.display = 'block';
            
            // 5秒后自动隐藏错误信息
            setTimeout(() => {
                errorElem.style.display = 'none';
            }, 5000);
        }
    },
    
    /**
     * 转义HTML字符
     * @param {string} unsafe 不安全的字符串
     * @returns {string} 转义后的字符串
     */
    escapeHtml: function(unsafe) {
        if (!unsafe) return '';
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }
}; 