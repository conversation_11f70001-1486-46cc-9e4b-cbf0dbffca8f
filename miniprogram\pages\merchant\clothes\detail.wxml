<view class="container">
  <!-- 衣物图片 -->
  <view class="clothes-image-container" bindtap="previewImage">
    <image class="clothes-image" src="{{clothes.image_url}}" mode="aspectFill"></image>
  </view>
  
  <view class="clothes-details">
    <!-- 基本信息 -->
    <view class="info-section">
      <view class="clothes-name">{{clothes.name}}</view>
      
      <view class="info-item">
        <view class="info-label">分类</view>
        <view class="info-value">{{clothes.category_name || clothes.category}}</view>
      </view>
      
      <view class="info-item" wx:if="{{clothes.tags}}">
        <view class="info-label">标签</view>
        <view class="info-tags">
          <view class="tag" wx:for="{{clothes.tags.split(',')}}" wx:key="index">{{item}}</view>
        </view>
      </view>
      
      <view class="info-item" wx:if="{{clothes.description}}">
        <view class="info-label">描述</view>
        <view class="info-description">{{clothes.description}}</view>
      </view>
    </view>
    
    <!-- 商家信息 -->
    <view class="merchant-section">
      <view class="section-title">商家信息</view>
      <view class="merchant-info">
        <image class="merchant-avatar" src="{{merchant.avatar_url || '/images/default-avatar.png'}}"></image>
        <view class="merchant-details">
          <view class="merchant-name">{{merchant.nickname || '商家用户'}}</view>
          <view class="merchant-status">
            <view class="status-tag {{isShareCredits ? 'status-share' : ''}}">
              {{isShareCredits ? '已开启共享试穿点数' : '未开启共享试穿点数'}}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading" wx:if="{{loading}}">
    <image class="loading-icon" src="/images/icons/loading.gif"></image>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 底部试穿按钮 -->
  <view class="bottom-actions">
    <view class="try-on-tips" wx:if="{{!isShareCredits}}">
      本次试穿将消耗您的试穿点数
    </view>
    <view class="try-on-tips" wx:else>
      本次试穿使用商家共享点数，不消耗您的点数
    </view>
    <button class="try-on-btn" bindtap="tryOnClothes">试穿此衣物</button>
  </view>
</view> 