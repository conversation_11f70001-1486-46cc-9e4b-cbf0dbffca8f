<?php
/**
 * 获取试穿历史API
 * 
 * 获取当前用户的试穿历史记录，支持分页
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * GET 参数:
 * - page: 页码（默认1）
 * - per_page: 每页数量（默认10）
 * 
 * 返回:
 * {
 *   "error": false,
 *   "data": [
 *     {
 *       "id": 1,
 *       "result_image_url": "https://example.com/result.jpg",
 *       "clothes_ids": [39, 44],
 *       "created_at": "2023-04-01 12:00:00"
 *     },
 *     ...
 *   ],
 *   "pagination": {
 *     "total": 20,
 *     "page": 1,
 *     "per_page": 10,
 *     "total_pages": 2
 *   }
 * }
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 设置响应内容类型
header('Content-Type: application/json');

// 处理CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否存在Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// 验证token
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid or expired token'
    ]);
    exit;
}

// 获取用户ID
$userId = $tokenData['sub'];

// 获取分页参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$perPage = isset($_GET['per_page']) ? intval($_GET['per_page']) : 10;

// 验证分页参数
if ($page < 1) {
    $page = 1;
}
if ($perPage < 1 || $perPage > 50) {
    $perPage = 10;
}

// 计算偏移量
$offset = ($page - 1) * $perPage;

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查try_on_history表是否存在
    $tableExists = false;
    $tablesStmt = $conn->query("SHOW TABLES LIKE 'try_on_history'");
    if ($tablesStmt->rowCount() > 0) {
        $tableExists = true;
    }
    
    // 如果表不存在，尝试创建
    if (!$tableExists) {
        $createTableSql = file_get_contents(__DIR__ . '/create_try_on_history_table.sql');
        if ($createTableSql) {
            $conn->exec($createTableSql);
            // 再次检查表是否成功创建
            $tablesStmt = $conn->query("SHOW TABLES LIKE 'try_on_history'");
            $tableExists = ($tablesStmt->rowCount() > 0);
        }
    }
    
    // 如果表仍然不存在，返回错误
    if (!$tableExists) {
        echo json_encode([
            'error' => true,
            'msg' => '试穿历史功能暂未开启',
            'data' => []
        ]);
        exit;
    }
    
    // 获取记录总数
    $countStmt = $conn->prepare("SELECT COUNT(*) FROM try_on_history WHERE user_id = ? AND status = 'success'");
    $countStmt->execute([$userId]);
    $total = (int)$countStmt->fetchColumn();
    
    // 计算总页数
    $totalPages = ceil($total / $perPage);
    
    // 获取试穿历史记录
    $stmt = $conn->prepare("
        SELECT * FROM try_on_history
        WHERE user_id = ? AND status = 'success'
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
    ");
    // 确保将perPage和offset作为整数绑定，避免被当作字符串
    $stmt->bindParam(1, $userId, PDO::PARAM_INT);
    $stmt->bindParam(2, $perPage, PDO::PARAM_INT);
    $stmt->bindParam(3, $offset, PDO::PARAM_INT);
    $stmt->execute();
    
    $historyRecords = [];
    
    while ($record = $stmt->fetch(PDO::FETCH_ASSOC)) {
        // 解析clothes_ids为数组
        $clothesIds = json_decode($record['clothes_ids'], true);
        if (!is_array($clothesIds)) {
            $clothesIds = [];
        }
        
        // 格式化返回数据
        $historyRecords[] = [
            'id' => (int)$record['id'],
            'user_id' => (int)$record['user_id'],
            'result_image_url' => $record['result_image_url'],
            'photo_id' => $record['photo_id'] ? (int)$record['photo_id'] : null,
            'clothes_ids' => $clothesIds,
            'task_id' => $record['task_id'],
            'status' => $record['status'],
            'created_at' => $record['created_at'],
            'updated_at' => $record['updated_at']
        ];
    }
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'data' => $historyRecords,
        'pagination' => [
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => $totalPages
        ]
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'error' => true,
        'msg' => '数据库操作失败: ' . $e->getMessage(),
        'data' => []
    ]);
} 