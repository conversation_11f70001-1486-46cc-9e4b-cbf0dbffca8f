<view class="container">
  <!-- 顶部固定区域 -->
  <view class="fixed-header">
    <!-- 分类选择器 -->
    <view class="category-switcher">
      <view class="selected-category" bindtap="toggleCategoryPopup">
        <text>{{currentCategoryIndex === -1 ? '全部穿搭' : categories[currentCategoryIndex].name}}</text>
        <view class="selected-category-dropdown-icon"></view>
      </view>
      <!-- 新增：数据源切换按钮 -->
      <view class="data-source-btn" bindtap="toggleDataSourcePopup">
        <text>{{dataSourceOptions[dataSource === 'personal' ? 0 : dataSource === 'shared' ? 1 : 2].name}}</text>
        <view class="data-source-dropdown-icon"></view>
      </view>
      <!-- 搜索框 -->
      <view class="outfit-search-box">
        <image class="outfit-search-icon" src="/images/search.png"></image>
        <input 
          class="outfit-search-input" 
          placeholder="搜索穿搭名称" 
          placeholder-class="outfit-search-placeholder"
          bindinput="onOutfitSearchInput"
          bindconfirm="searchOutfits"
          confirm-type="search"
          value="{{outfitSearchKeyword}}"
        />
        <view class="outfit-search-clear" bindtap="clearOutfitSearch" wx:if="{{outfitSearchKeyword}}">×</view>
      </view>
    </view>
  </view>
  
  <!-- 分类选择弹出框 -->
  <view class="category-popup-mask" bindtap="closeCategoryPopup" wx:if="{{showCategoryPopup}}"></view>
  <view class="category-popup {{showCategoryPopup ? 'category-popup-show' : ''}}">
    <view class="category-popup-header">
      <text class="category-popup-title">选择穿搭分类</text>
      <view class="category-popup-actions">
        <view class="category-popup-action" bindtap="goToAddCategory">
          <image class="action-icon" src="/images/addcd.png" mode="aspectFit"></image>
        </view>
        <view class="category-popup-action" bindtap="goToCategoryManage">
          <image class="action-icon" src="/images/manage.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
    <scroll-view class="category-popup-content" scroll-y>
      <view class="category-popup-item {{currentCategoryIndex === -1 ? 'active' : ''}}" 
            bindtap="switchCategory" 
            data-index="{{-1}}">
        全部
      </view>
      <view wx:for="{{categories}}" 
            wx:key="id" 
            class="category-popup-item {{currentCategoryIndex === index ? 'active' : ''}}"
            bindtap="switchCategory"
            data-index="{{index}}">
        {{item.name}}
      </view>
    </scroll-view>
  </view>

  <!-- 新增：数据源选择弹出框 -->
  <view class="data-source-popup-mask" bindtap="closeDataSourcePopup" wx:if="{{showDataSourcePopup}}"></view>
  <view class="data-source-popup {{showDataSourcePopup ? 'data-source-popup-show' : ''}}">
    <view class="data-source-popup-header">
      <text class="data-source-popup-title">选择数据源</text>
      <view class="data-source-popup-close" bindtap="closeDataSourcePopup">×</view>
    </view>
    <view class="data-source-popup-content">
      <view wx:for="{{dataSourceOptions}}"
            wx:key="key"
            class="data-source-popup-item {{dataSource === item.key ? 'active' : ''}}"
            bindtap="switchDataSource"
            data-source="{{item.key}}">
        <text class="data-source-icon">{{item.icon}}</text>
        <text class="data-source-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 双banner区域 -->
  <view class="banner-section">
    <!-- 穿搭广场banner -->
    <view class="banner-item banner-left" bindtap="navigateToSquare">
      <image class="banner-icon" src="/images/guangchang.png" mode="aspectFit"></image>
      <view class="banner-info">
        <view class="banner-title">穿搭广场</view>
        <view class="banner-description">探索更多精彩穿搭</view>
      </view>
    </view>

    <!-- 穿搭圈子banner -->
    <view class="banner-item banner-right" bindtap="navigateToCircle">
      <image class="banner-icon" src="/images/quanzi.png" mode="aspectFit"></image>
      <view class="banner-info">
        <view class="banner-title">穿搭圈子</view>
        <view class="banner-description">共同管理衣橱穿搭</view>
      </view>
    </view>
  </view>

  <!-- 穿搭列表 -->
  <view 
    class="outfits-container" 
    wx:if="{{!loading && !isEmpty}}">
    <view class="outfits-grid">
      <view 
        wx:for="{{outfits}}" 
        wx:key="id" 
        class="outfit-item" 
        data-id="{{item.id}}" 
        data-name="{{item.name}}"
        bindtap="onOutfitItemTap"
        bindlongpress="onLongPress">
        
        <!-- 穿搭预览图 -->
        <view class="outfit-preview">
          <block wx:if="{{item.thumbnail}}">
            <image src="{{item.thumbnail}}" mode="aspectFill" class="outfit-thumbnail"></image>
          </block>
          <block wx:else>
            <!-- 穿搭视图，显示所有衣物 -->
            <view class="outfit-placeholder" wx:if="{{item.items.length > 0}}">
              <!-- 创建一个与详情页类似的outfit-view -->
              <view class="outfit-view-mini">
                <view 
                  wx:for="{{item.items}}" 
                  wx:for-item="clothingItem" 
                  wx:key="clothing_id"
                  class="outfit-item-mini"
                  style="left: {{clothingItem.previewPosition.x}}px; top: {{clothingItem.previewPosition.y}}px; width: {{clothingItem.size.width * clothingItem.previewPosition.scale}}px; height: {{clothingItem.size.height * clothingItem.previewPosition.scale}}px; transform: rotate({{clothingItem.rotation}}deg); z-index: {{clothingItem.z_index}};">
                  <image src="{{clothingItem.clothing_data.image_url}}" mode="aspectFit" class="item-image-mini"></image>
                </view>
              </view>

            </view>
            <view class="outfit-no-items" wx:else>
              <text class="iconfont icon-outfit"></text>
            </view>
          </block>
        </view>
        
        <!-- 穿搭信息 -->
        <view class="outfit-info">
          <view class="outfit-name">{{item.name}}</view>
          <!-- 新增：创建者信息 -->
          <view class="creator-info" wx:if="{{item.data_source === 'shared' && item.creator_nickname}}">
            <text class="creator-label">创建者：</text>
            <text class="creator-name">{{item.creator_nickname}}</text>
          </view>
          <view class="outfit-bottom-row">
            <view class="outfit-date">{{item.formatted_date || item.created_at}}</view>
            <view class="outfit-like-info" wx:if="{{item.is_public}}">
              <image src="/images/dianzan.png" class="like-icon-small"></image>
              <text class="like-count-small">{{item.likes_count || 0}}</text>
            </view>
            <view class="outfit-private-tag" wx:else>
              <text class="private-tag-text">未共享</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view class="loading-more" wx:if="{{loadingMore}}">
      <view class="loading-spinner-small"></view>
      <view class="loading-more-text">加载更多...</view>
    </view>
    <!-- 手动加载更多按钮 -->
    <view class="manual-load-more" wx:if="{{hasMoreData && !loadingMore && outfits.length >= 20}}" bindtap="loadMore">
      <view class="manual-load-text">点击加载更多</view>
    </view>
    <view class="loading-end" wx:if="{{!hasMoreData && outfits.length > 0 && !loadingMore}}">
      已显示全部穿搭
    </view>
    
    <!-- 底部安全区域占位 -->
    <view class="safe-area-bottom"></view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{isEmpty && !loading}}">
    <view class="empty-icon">👔</view>
    <view class="empty-text">{{isSearchMode ? '未找到匹配穿搭' : '暂无穿搭'}}</view>
    <view class="empty-desc" wx:if="{{isSearchMode}}">没有找到与"{{outfitSearchKeyword}}"相关的穿搭</view>
    <view class="empty-desc" wx:else>点击下方按钮创建你的第一个穿搭</view>
    <view class="empty-action" wx:if="{{isSearchMode}}" bindtap="clearOutfitSearch">清除搜索</view>
  </view>
  
  <!-- 添加按钮 -->
  <view class="add-btn {{isUsingMockUser ? 'add-btn-mock-user' : 'add-btn-logged-in'}}" bindtap="navigateToAdd">
    <text class="add-icon">+</text>
  </view>
</view> 