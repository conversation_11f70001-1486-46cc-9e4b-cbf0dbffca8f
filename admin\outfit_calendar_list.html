<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>穿搭日历管理 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .data-table th {
            background-color: #f7f7f7;
            padding: 12px 15px;
            text-align: left;
            font-weight: 500;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .data-table tr:last-child td {
            border-bottom: none;
        }
        
        .data-table tr:hover {
            background-color: #f9f9f9;
        }
        
        /* 操作按钮 */
        .action-btn {
            padding: 5px 10px;
            margin-right: 5px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background-color: white;
            color: #1890ff;
            cursor: pointer;
            font-size: 14px;
        }
        
        .action-btn:hover {
            border-color: #1890ff;
            background-color: #e6f7ff;
        }
        
        /* 搜索和筛选区域 */
        .filter-section {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .date-range {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .date-input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-label {
            font-size: 14px;
            color: #666;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            min-width: 120px;
        }
        
        .search-btn {
            padding: 8px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .reset-btn {
            padding: 8px 16px;
            background-color: white;
            color: #666;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        /* 分页控件 */
        .pagination {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-top: 20px;
        }
        
        .pagination-info {
            margin-right: 15px;
            color: #666;
            font-size: 14px;
        }
        
        .pagination-btn {
            padding: 6px 10px;
            margin: 0 2px;
            border: 1px solid #d9d9d9;
            background-color: white;
            color: #666;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .pagination-btn.active {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        
        .pagination-btn:hover:not(.active) {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .pagination-btn:disabled {
            color: #d9d9d9;
            cursor: not-allowed;
        }
        
        /* 加载状态 */
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 50px 0;
        }
        
        .loading-spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left: 4px solid #1890ff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 缩略图样式 */
        .thumbnail {
            width: 60px;
            height: 80px;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-right: 8px;
            vertical-align: middle;
        }
        
        /* 用户信息样式 */
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-name {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        /* 日历格式化日期 */
        .calendar-date {
            font-weight: 500;
            color: #333;
        }
        
        /* 空数据提示 */
        .empty-data {
            text-align: center;
            padding: 50px 0;
            background-color: white;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        /* 多图显示样式 */
        .outfit-images-container {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            max-width: 250px;
        }
        
        .outfit-image-item {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
            border: 1px solid #eee;
            cursor: pointer;
        }
        
        .outfit-image-item:hover {
            border-color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>穿搭日历管理</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="filter-section">
                <div class="date-range">
                    <div class="filter-label">日期范围:</div>
                    <input type="date" id="startDate" class="date-input">
                    <span>至</span>
                    <input type="date" id="endDate" class="date-input">
                </div>
                
                <div class="filter-group">
                    <span class="filter-label">用户:</span>
                    <select id="userFilter" class="filter-select">
                        <option value="">全部用户</option>
                        <!-- 用户选项将通过JavaScript动态填充 -->
                    </select>
                </div>
                
                <button id="searchBtn" class="search-btn">搜索</button>
                <button id="resetBtn" class="reset-btn">重置</button>
            </div>
            
            <div id="loadingContainer" class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载数据...</p>
            </div>
            
            <div id="dataContainer" style="display: none;">
                <table class="data-table" id="calendarTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>日期</th>
                            <th>用户</th>
                            <th>穿搭名称</th>
                            <th>分类</th>
                            <th>穿搭预览</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- 数据将通过JavaScript动态填充 -->
                    </tbody>
                </table>
                
                <div class="pagination" id="pagination">
                    <div class="pagination-info">
                        共 <span id="totalRecords">0</span> 条记录，第 <span id="currentPage">1</span>/<span id="totalPages">1</span> 页
                    </div>
                    <button id="firstPageBtn" class="pagination-btn" disabled>首页</button>
                    <button id="prevPageBtn" class="pagination-btn" disabled>上一页</button>
                    <div id="pageButtons">
                        <!-- 页码按钮将通过JavaScript动态填充 -->
                    </div>
                    <button id="nextPageBtn" class="pagination-btn">下一页</button>
                    <button id="lastPageBtn" class="pagination-btn">末页</button>
                </div>
            </div>
            
            <div id="emptyData" style="display: none;" class="empty-data">
                <p>暂无穿搭日历记录</p>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <!-- <script src="js/outfit_calendar_list.js"></script> -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为outfit_calendar
            Sidebar.init('outfit_calendar');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 创建OutfitCalendarList对象，提供基本的功能
            window.OutfitCalendarList = {
                // 状态数据
                data: {
                    records: [],
                    users: [],
                    page: 1,
                    limit: 10,
                    total: 0,
                    totalPages: 1,
                    startDate: '',
                    endDate: '',
                    userId: '',
                    recordDetails: {} // 存储获取的详细数据
                },
                
                init: function() {
                    // 设置默认日期范围（当月）
                    const now = new Date();
                    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
                    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            
                    const startInput = document.getElementById('startDate');
                    const endInput = document.getElementById('endDate');
                    
                    startInput.value = this.formatDateForInput(firstDay);
                    endInput.value = this.formatDateForInput(lastDay);
                    
                    this.data.startDate = startInput.value;
                    this.data.endDate = endInput.value;
            
                    // 绑定事件处理器
                    document.getElementById('searchBtn').addEventListener('click', this.handleSearch.bind(this));
                    document.getElementById('resetBtn').addEventListener('click', this.handleReset.bind(this));
                    document.getElementById('prevPageBtn').addEventListener('click', this.prevPage.bind(this));
                    document.getElementById('nextPageBtn').addEventListener('click', this.nextPage.bind(this));
                    document.getElementById('firstPageBtn').addEventListener('click', this.firstPage.bind(this));
                    document.getElementById('lastPageBtn').addEventListener('click', this.lastPage.bind(this));
            
                    // 加载数据
                    this.loadData();
                },
                
                formatDateForInput: function(date) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                },
                
                handleSearch: function() {
                    const startDate = document.getElementById('startDate').value;
                    const endDate = document.getElementById('endDate').value;
                    const userId = document.getElementById('userFilter').value;
                    
                    this.data.startDate = startDate;
                    this.data.endDate = endDate;
                    this.data.userId = userId;
                    this.data.page = 1;
                    
                    this.loadData();
                },
                
                handleReset: function() {
                    // 重置所有过滤条件
                    const now = new Date();
                    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
                    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                    
                    document.getElementById('startDate').value = this.formatDateForInput(firstDay);
                    document.getElementById('endDate').value = this.formatDateForInput(lastDay);
                    document.getElementById('userFilter').value = '';
                    
                    this.data.startDate = this.formatDateForInput(firstDay);
                    this.data.endDate = this.formatDateForInput(lastDay);
                    this.data.userId = '';
                    this.data.page = 1;
                    
                    this.loadData();
                },
                
                loadData: function() {
                    // 显示加载中
                    document.getElementById('loadingContainer').style.display = 'flex';
                    document.getElementById('dataContainer').style.display = 'none';
                    document.getElementById('emptyData').style.display = 'none';
                    
                    // 构建API URL
                    let url = `../login_backend/admin_get_calendar_outfits.php?page=${this.data.page}&limit=${this.data.limit}`;
                    
                    if (this.data.startDate) {
                        url += `&start_date=${this.data.startDate}`;
                    }
                    
                    if (this.data.endDate) {
                        url += `&end_date=${this.data.endDate}`;
                    }
                    
                    if (this.data.userId) {
                        url += `&user_id=${this.data.userId}`;
                    }
                
                    // 发起请求
                    fetch(url, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer ' + Auth.getToken()
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('网络请求失败');
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('API Response:', data);
                    
                        if (data.error === false && data.data) {
                            // 保存数据
                            this.data.records = data.data.records || [];
                            this.data.users = data.data.users || [];
                            this.data.total = data.data.total || 0;
                            this.data.page = data.data.page || 1;
                            this.data.limit = data.data.limit || 10;
                            this.data.totalPages = data.data.total_pages || 1;
                    
                            // 更新用户下拉框
                            this.updateUserFilter();
                            
                            // 逐个加载详细数据，用于获取衣物图片
                            this.loadRecordDetails();
                            
                            // 渲染数据
                            this.renderTable();
                            this.updatePagination();
                    
                            // 显示表格或空状态
                            document.getElementById('loadingContainer').style.display = 'none';
                            
                            if (this.data.records.length > 0) {
                                document.getElementById('dataContainer').style.display = 'block';
                            } else {
                                document.getElementById('emptyData').style.display = 'block';
                            }
                        } else {
                            throw new Error(data.msg || '获取数据失败');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('loadingContainer').style.display = 'none';
                        document.getElementById('emptyData').style.display = 'block';
                        document.getElementById('emptyData').innerHTML = `<p>加载数据失败: ${error.message}</p>`;
                    });
                },
                
                // 加载记录详情，以获取衣物图片
                loadRecordDetails: function() {
                    // 记录当前页面所有ID，以便后续渲染使用
                    this.data.records.forEach(record => {
                        const calendarId = record.calendar_id;
                        
                        // 构建API URL
                        const url = `../login_backend/admin_get_outfit_calendar_detail.php?id=${calendarId}`;
                        
                        // 发起请求
                        fetch(url, {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': 'Bearer ' + Auth.getToken()
                            }
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('获取详情失败');
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data.error === false && data.data) {
                                // 保存详情数据
                                this.data.recordDetails[calendarId] = data.data.calendar;
                                
                                // 更新单条记录的展示
                                this.updateRecordDisplay(calendarId);
                            }
                        })
                        .catch(error => {
                            console.error(`Error loading details for record ${calendarId}:`, error);
                        });
                    });
                },
                
                // 更新单条记录的显示
                updateRecordDisplay: function(calendarId) {
                    const recordDetail = this.data.recordDetails[calendarId];
                    if (!recordDetail) return;
                    
                    const row = document.getElementById(`outfit-row-${calendarId}`);
                    if (!row) return;
                    
                    const imageCell = row.querySelector('.outfit-images');
                    if (!imageCell) return;
                    
                    // 生成多图展示HTML
                    imageCell.innerHTML = this.renderOutfitImages(recordDetail);
                },
                
                updateUserFilter: function() {
                    const select = document.getElementById('userFilter');
                    
                    // 保存当前选中值
                    const currentValue = select.value;
                
                    // 清空选项
                    while (select.options.length > 1) {
                        select.remove(1);
                    }
                
                    // 添加用户选项
                    this.data.users.forEach(user => {
                        const option = document.createElement('option');
                        option.value = user.id;
                        option.textContent = user.nickname || `用户ID: ${user.id}`;
                        select.appendChild(option);
                    });
                
                    // 恢复选中值
                    if (currentValue) {
                        select.value = currentValue;
                    }
                },
                
                // 渲染多图显示
                renderOutfitImages: function(record) {
                    // 确保图片URL有效
                    const ensureValidImageUrl = (url, defaultUrl) => {
                        if (!url) {
                            return defaultUrl;
                        }
                        
                        // 如果是相对URL，添加基础路径
                        if (url.startsWith('/')) {
                            return url;
                        } else if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('data:')) {
                            // 相对路径处理，添加../，确保能从admin目录正确访问
                            if (!url.startsWith('../')) {
                                return '../' + url;
                            }
                            return url;
                        }
                        
                        return url;
                    };
                    
                    // 使用远程默认图片
                    const defaultOutfitImage = 'https://cyyg.alidog.cn/admin/images/default-cloth.png';
                    const defaultClothImage = 'https://cyyg.alidog.cn/admin/images/default-cloth.png';
                    
                    // 当thumbnail_url为空时，需要显示默认图片
                    let thumbnailUrl = defaultOutfitImage;
                    if (record.thumbnail_url && record.thumbnail_url.trim() !== '') {
                        thumbnailUrl = ensureValidImageUrl(record.thumbnail_url, defaultOutfitImage);
                    }
                    
                    // 创建HTML容器
                    let html = `<div class="outfit-images-container">`;
                    
                    // 添加主缩略图
                    html += `<img src="${thumbnailUrl}" alt="${record.outfit_name || '穿搭预览'}" class="outfit-image-item" onclick="OutfitCalendarList.viewDetail(${record.calendar_id})" onerror="this.src='${defaultOutfitImage}'">`;
                    
                    // 添加衣物图片
                    if (record.outfit_data && record.outfit_data.items && record.outfit_data.items.length > 0) {
                        // 最多显示4张衣物图片
                        const maxImages = Math.min(4, record.outfit_data.items.length);
                        
                        for (let i = 0; i < maxImages; i++) {
                            const item = record.outfit_data.items[i];
                            if (item.clothing_data && item.clothing_data.image_url) {
                                const imageUrl = ensureValidImageUrl(
                                    item.clothing_data.image_url, 
                                    defaultClothImage
                                );
                                
                                html += `<img src="${imageUrl}" 
                                              alt="${item.clothing_data.name || '衣物'}" 
                                              class="outfit-image-item" 
                                              onclick="OutfitCalendarList.viewDetail(${record.calendar_id})"
                                              onerror="this.src='${defaultClothImage}'">`;
                            }
                        }
                    }
                    
                    html += `</div>`;
                    return html;
                },
                
                renderTable: function() {
                    const tableBody = document.getElementById('tableBody');
                    tableBody.innerHTML = '';
                
                    this.data.records.forEach(record => {
                        const row = document.createElement('tr');
                        row.id = `outfit-row-${record.calendar_id}`;
                        
                        // 日期格式化
                        const date = new Date(record.calendar_date);
                        const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
                    
                        // 创建时间格式化
                        const createdDate = new Date(record.calendar_created_at);
                        const formattedCreatedDate = `${createdDate.getFullYear()}-${String(createdDate.getMonth() + 1).padStart(2, '0')}-${String(createdDate.getDate()).padStart(2, '0')} ${String(createdDate.getHours()).padStart(2, '0')}:${String(createdDate.getMinutes()).padStart(2, '0')}`;
                        
                        // 确保图片URL有效
                        const ensureValidImageUrl = (url, defaultUrl) => {
                            if (!url) {
                                return defaultUrl;
                            }
                            
                            // 如果是相对URL，添加基础路径
                            if (url.startsWith('/')) {
                                return url;
                            } else if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('data:')) {
                                // 相对路径处理，添加../，确保能从admin目录正确访问
                                if (!url.startsWith('../')) {
                                    return '../' + url;
                                }
                                return url;
                            }
                            
                            return url;
                        };
                        
                        // 使用远程默认图片
                        const defaultAvatarImage = 'https://cyyg.alidog.cn/admin/images/default-cloth.png';
                        const defaultOutfitImage = 'https://cyyg.alidog.cn/admin/images/default-cloth.png';
                        
                        // 确保图片URL是完整的
                        const avatarUrl = ensureValidImageUrl(record.avatar_url, defaultAvatarImage);
                        
                        // 初始状态只显示loading占位符，后续异步加载详情后再更新
                        const outfitImagesCell = `<td class="outfit-images"><div class="outfit-images-container"><img src="${defaultOutfitImage}" class="outfit-image-item" alt="加载中..."></div></td>`;
                        
                        row.innerHTML = `
                            <td>${record.calendar_id || '-'}</td>
                            <td class="calendar-date">${formattedDate}</td>
                            <td>
                                <div class="user-info">
                                    <img src="${avatarUrl}" alt="${record.nickname}" class="avatar" onerror="this.src='${defaultAvatarImage}'">
                                    <span class="user-name">${record.nickname || '未知用户'}</span>
                                </div>
                            </td>
                            <td>${record.outfit_name || '未命名穿搭'}</td>
                            <td>${record.category_name || '未分类'}</td>
                            ${outfitImagesCell}
                            <td>${formattedCreatedDate}</td>
                            <td>
                                <button class="action-btn" onclick="OutfitCalendarList.viewDetail(${record.calendar_id})">查看详情</button>
                            </td>
                        `;
                        
                        tableBody.appendChild(row);
                        
                        // 如果已经加载了详情数据，立即更新显示
                        if (this.data.recordDetails[record.calendar_id]) {
                            this.updateRecordDisplay(record.calendar_id);
                        }
                    });
                },
                
                updatePagination: function() {
                    document.getElementById('totalRecords').textContent = this.data.total;
                    document.getElementById('currentPage').textContent = this.data.page;
                    document.getElementById('totalPages').textContent = this.data.totalPages;
                
                    // 更新按钮状态
                    document.getElementById('prevPageBtn').disabled = this.data.page <= 1;
                    document.getElementById('nextPageBtn').disabled = this.data.page >= this.data.totalPages;
                    document.getElementById('firstPageBtn').disabled = this.data.page <= 1;
                    document.getElementById('lastPageBtn').disabled = this.data.page >= this.data.totalPages;
                    
                    // 清空页码按钮
                    const pageButtons = document.getElementById('pageButtons');
                    pageButtons.innerHTML = '';
                
                    // 生成页码按钮
                    const maxPages = 5; // 最多显示的页码数
                    let startPage = Math.max(1, this.data.page - Math.floor(maxPages / 2));
                    let endPage = Math.min(this.data.totalPages, startPage + maxPages - 1);
                    
                    if (endPage - startPage + 1 < maxPages) {
                        startPage = Math.max(1, endPage - maxPages + 1);
                    }
                    
                    for (let i = startPage; i <= endPage; i++) {
                        const button = document.createElement('button');
                        button.className = 'pagination-btn' + (i === this.data.page ? ' active' : '');
                        button.textContent = i;
                        button.onclick = () => this.goToPage(i);
                        pageButtons.appendChild(button);
                    }
                },
                
                viewDetail: function(id) {
                    window.location.href = `outfit_calendar_detail.html?id=${id}`;
                },
                
                prevPage: function() {
                    if (this.data.page > 1) {
                        this.data.page--;
                        this.loadData();
                    }
                },
                
                nextPage: function() {
                    if (this.data.page < this.data.totalPages) {
                        this.data.page++;
                        this.loadData();
                    }
                },
                
                firstPage: function() {
                    if (this.data.page !== 1) {
                        this.data.page = 1;
                        this.loadData();
                    }
                },
                
                lastPage: function() {
                    if (this.data.page !== this.data.totalPages) {
                        this.data.page = this.data.totalPages;
                        this.loadData();
                    }
                },
                
                goToPage: function(page) {
                    if (this.data.page !== page) {
                        this.data.page = page;
                        this.loadData();
                    }
                }
            };
            
            // 初始化穿搭日历列表
            OutfitCalendarList.init();
        });
    </script>
</body>
</html> 