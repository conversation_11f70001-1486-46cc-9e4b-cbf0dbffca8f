<?php
/**
 * 快速调试分类问题
 * 简单检查圈子中的衣物分类和分类定义
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "=== 分类问题快速调试 ===\n";
    echo "用户ID: $userId\n\n";
    
    // 1. 检查用户所在的圈子
    $stmt = $conn->prepare("SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $userCircles = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "1. 用户所在圈子: " . implode(', ', $userCircles) . "\n\n";
    
    if (empty($userCircles)) {
        echo "用户不在任何圈子中，无法测试共享分类\n";
        exit;
    }
    
    $circleIds = implode(',', $userCircles);
    
    // 2. 检查圈子中衣物使用的分类
    echo "2. 圈子中衣物使用的分类:\n";
    $stmt = $conn->prepare("
        SELECT category, COUNT(*) as count, 
               GROUP_CONCAT(DISTINCT user_id) as user_ids
        FROM clothes 
        WHERE circle_id IN ($circleIds) AND category IS NOT NULL AND category != ''
        GROUP BY category
        ORDER BY count DESC
    ");
    $stmt->execute();
    $clothesCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($clothesCategories as $cat) {
        echo "- {$cat['category']}: {$cat['count']}件衣物 (用户: {$cat['user_ids']})\n";
    }
    echo "\n";
    
    // 3. 检查分类定义表中的分类
    echo "3. 当前用户可见的分类定义:\n";
    $stmt = $conn->prepare("
        SELECT c.id, c.user_id, c.name, c.code, c.is_system, c.circle_id,
               CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
        FROM clothing_categories c
        WHERE (
            (c.is_system = 1 AND c.user_id = :user_id) OR
            (c.is_system = 0 AND ((c.user_id = :user_id AND c.circle_id IS NULL) OR 
             (c.circle_id IS NOT NULL AND c.circle_id IN ($circleIds))))
        )
        ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $availableCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($availableCategories as $cat) {
        echo "- {$cat['code']} ({$cat['name']}): {$cat['data_source']}, 用户{$cat['user_id']}, circle_id={$cat['circle_id']}\n";
    }
    echo "\n";
    
    // 4. 检查缺失的分类
    echo "4. 分析:\n";
    $usedCategoryCodes = array_column($clothesCategories, 'category');
    $availableCategoryCodes = array_column($availableCategories, 'code');
    $missingCategories = array_diff($usedCategoryCodes, $availableCategoryCodes);
    
    if (!empty($missingCategories)) {
        echo "发现缺失的分类定义: " . implode(', ', $missingCategories) . "\n";
        echo "这些分类在衣物中被使用，但在分类定义表中不存在\n";
    } else {
        echo "所有使用的分类都有对应的定义\n";
    }
    
    // 5. 检查其他用户的分类
    echo "\n5. 圈子中其他用户的自定义分类:\n";
    $stmt = $conn->prepare("
        SELECT c.id, c.user_id, c.name, c.code, c.circle_id,
               u.nickname as creator_nickname
        FROM clothing_categories c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.is_system = 0 AND c.circle_id IN ($circleIds) AND c.user_id != :user_id
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $otherUsersCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($otherUsersCategories)) {
        echo "没有找到其他用户的自定义分类\n";
        echo "可能原因：\n";
        echo "- 其他用户没有创建自定义分类\n";
        echo "- 其他用户没有同步分类到圈子\n";
        echo "- 其他用户的衣物使用的是系统分类\n";
    } else {
        foreach ($otherUsersCategories as $cat) {
            echo "- {$cat['code']} ({$cat['name']}): 创建者 {$cat['creator_nickname']} (用户{$cat['user_id']})\n";
        }
    }
    
    echo "\n=== 调试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
