<view class="container">
  <!-- 顶部标题栏（使用系统导航栏，非原生实现） -->
  <view class="title-bar" wx:if="{{!useSystemNavBar}}">
    <view class="nav-back" bindtap="navigateBack">
      <text class="icon-back"></text>
    </view>
    <view class="title">添加衣物</view>
  </view>
  
  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 上传区域 -->
    <view class="upload-area" bindtap="chooseImage">
      <block wx:if="{{!tempImagePath}}">
        <image src="/images/addimage.png" mode="aspectFit" class="upload-icon"></image>
        <view class="upload-text">上传衣物照片</view>
        <view class="upload-desc">点击下方开关可开启抠图功能</view>
      </block>
      <block wx:else>
        <view class="image-container">
          <image src="{{tempImagePath}}" 
                 mode="aspectFit" 
                 class="preview-image" 
                 style="transform: rotate({{rotationAngle}}deg) scale({{scaleValue}});">
          </image>
        </view>
      </block>
    </view>
    
    <!-- 图片编辑功能模块导航 -->
    <view class="modules-nav" wx:if="{{tempImagePath}}">
      <view class="module-tab {{isSegmentExpanded ? 'active' : ''}}" bindtap="toggleSegmentExpand">
        <text class="module-tab-text">抠图</text>
      </view>
      <view class="module-tab {{isRotationExpanded ? 'active' : ''}}" bindtap="toggleRotationExpand">
        <text class="module-tab-text">旋转</text>
      </view>
      <view class="module-tab {{isScaleExpanded ? 'active' : ''}}" bindtap="toggleScaleExpand">
        <text class="module-tab-text">缩放</text>
      </view>
    </view>
    
    <!-- 模块内容区域 -->
    <view class="modules-content" wx:if="{{tempImagePath}}">
      <!-- 抠图模块内容 -->
      <view class="module-content-item {{isSegmentExpanded ? 'visible' : 'hidden'}}">
        <view class="segment-switch-container">
          <view class="segment-switch">
            <text class="switch-label">抠图开关</text>
            <switch 
              checked="{{segmentEnabled}}" 
              bindchange="toggleSegment" 
              color="#000"
            />
          </view>
          <text class="segment-desc">{{segmentEnabled ? '已开启抠图' : '开启抠图可去除背景，建议抠图后再旋转'}}</text>
        </view>
      </view>
      
      <!-- 旋转模块内容 -->
      <view class="module-content-item {{isRotationExpanded ? 'visible' : 'hidden'}}">
        <view class="rotation-control">
          <view class="rotation-title">
            <text>图片旋转调整</text>
            <text class="rotation-angle">{{rotationAngle}}°</text>
          </view>
          
          <view class="rotation-actions">
            <view class="rotation-btn" bindtap="rotateLeft">
              <text class="rotation-icon">⟲</text>
              <text class="rotation-text">向左转</text>
            </view>
            
            <slider 
              class="rotation-slider" 
              min="0" 
              max="359" 
              value="{{rotationAngle}}" 
              bindchange="onRotationChange" 
              show-value="{{false}}"
              step="1"
              activeColor="#000"
              block-size="20"
            />
            
            <view class="rotation-btn" bindtap="rotateRight">
              <text class="rotation-icon">⟳</text>
              <text class="rotation-text">向右转</text>
            </view>
          </view>
          
          <view class="reset-btn" bindtap="resetRotation">
            <text>重置角度</text>
          </view>
        </view>
      </view>
      
      <!-- 缩放模块内容 -->
      <view class="module-content-item {{isScaleExpanded ? 'visible' : 'hidden'}}">
        <view class="rotation-control">
          <view class="scale-title">
            <text>图片缩放调整</text>
            <text class="scale-value">{{scaleValue}}x</text>
          </view>
          
          <view class="scale-actions">
            <view class="scale-btn" bindtap="scaleDown">
              <text class="scale-icon">－</text>
              <text class="scale-text">缩小</text>
            </view>
            
            <slider 
              class="scale-slider" 
              min="0.5" 
              max="2.0" 
              value="{{scaleValue}}" 
              bindchange="onScaleChange" 
              show-value="{{false}}"
              step="0.1"
              activeColor="#000"
              block-size="20"
            />
            
            <view class="scale-btn" bindtap="scaleUp">
              <text class="scale-icon">＋</text>
              <text class="scale-text">放大</text>
            </view>
          </view>
          
          <view class="reset-btn" bindtap="resetScale">
            <text>重置缩放</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 衣物类别 -->
    <view class="section">
      <view class="section-title">选择衣物类别</view>
      <view class="tag-container">
        <view 
          wx:for="{{categories}}" 
          wx:key="value" 
          class="tag {{selectedCategory === item.value ? 'selected' : ''}}"
          bindtap="selectCategory"
          data-value="{{item.value}}"
        >
          {{item.name}}
        </view>
        <view class="tag custom-category-btn" bindtap="goToClothingCategories">+ 自定义</view>
      </view>
    </view>
    
    <!-- 衣物标签 -->
    <view class="section">
      <view class="section-title">添加衣物标签</view>
      <view class="tag-container">
        <view 
          wx:for="{{tags}}" 
          wx:key="value" 
          class="tag {{selectedTags[item.value] ? 'selected' : ''}}"
          bindtap="toggleTag"
          data-value="{{item.value}}"
        >
          {{item.name}}
        </view>
        <view 
          wx:for="{{customTags}}" 
          wx:key="value" 
          class="tag {{selectedTags[item.value] ? 'selected' : ''}}"
          bindtap="toggleTag"
          data-value="{{item.value}}"
        >
          {{item.name}}
        </view>
        <view class="tag" bindtap="showCustomTagInput">+ 自定义</view>
      </view>
    </view>
    
    <!-- 自定义标签输入框 -->
    <view class="custom-tag-input" wx:if="{{showCustomTagInput}}">
      <input 
        class="tag-input" 
        placeholder="请输入自定义标签" 
        value="{{customTagText}}" 
        bindinput="onCustomTagInput"
        bindconfirm="addCustomTag"
        focus="{{showCustomTagInput}}"
      />
      <button class="tag-btn" bindtap="addCustomTag">添加</button>
      <button class="tag-btn cancel" bindtap="cancelCustomTag">取消</button>
    </view>
    
    <!-- 衣物信息 -->
    <view class="section">
      <view class="section-title">衣物信息（选填）</view>
      
      <!-- 衣橱选择 -->
      <view class="wardrobe-selector">
        <view class="wardrobe-label">添加到衣橱:</view>
        <picker bindchange="bindWardrobeChange" range="{{wardrobeList}}" range-key="name">
          <view class="wardrobe-picker">
            <text>{{selectedWardrobeName}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="input-group">
        <view class="input-item">
          <input type="text" placeholder="名称" value="{{name}}" bindinput="onNameInput" />
        </view>
        <view class="input-item">
          <input type="text" placeholder="颜色" value="{{color}}" bindinput="onColorInput" />
        </view>
        <view class="input-item">
          <input type="text" placeholder="品牌" value="{{brand}}" bindinput="onBrandInput" />
        </view>
        <view class="input-item">
          <input type="text" placeholder="价格" value="{{price}}" bindinput="onPriceInput" />
        </view>
        <view class="input-item">
          <input type="text" placeholder="备注" value="{{notes}}" bindinput="onNotesInput" />
        </view>
      </view>
    </view>
  </view>
  
  <!-- 旋转用的隐藏canvas -->
  <canvas type="2d" id="rotateCanvas" style="position: absolute; width: 500px; height: 500px; left: -9999px; top: 0px; z-index: -100;"></canvas>
  
  <!-- 底部保存按钮 -->
  <view class="bottom-bar">
    <button class="save-btn" bindtap="saveClothing" disabled="{{!tempImagePath || !selectedCategory}}">保存</button>
  </view>
</view> 