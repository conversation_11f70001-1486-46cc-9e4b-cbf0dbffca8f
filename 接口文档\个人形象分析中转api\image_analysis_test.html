<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人形象分析测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-container {
            background: #f9f9f9;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 12px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            width: 100%;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        .preview-images {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .preview-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 6px;
            border: 2px solid #ddd;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background-color: #f5f5f5;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-radius: 50%;
            border-top: 4px solid #3498db;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 30px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .hidden {
            display: none;
        }
        pre {
            overflow-x: auto;
            background: #f0f0f0;
            padding: 15px;
            border-radius: 6px;
        }
        .result-section {
            margin-bottom: 20px;
        }
        .result-section h3 {
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .nav-tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background: #eee;
            cursor: pointer;
            border-radius: 6px 6px 0 0;
            margin-right: 5px;
        }
        .tab.active {
            background: #4CAF50;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>个人形象分析测试</h1>
    
    <div id="form-container" class="form-container">
        <div class="form-group">
            <label for="gender">性别</label>
            <select id="gender" name="gender">
                <option value="1">男</option>
                <option value="2">女</option>
            </select>
        </div>
        <div class="form-group">
            <label for="height">身高 (cm)</label>
            <input type="number" id="height" name="height" placeholder="例如：170">
        </div>
        <div class="form-group">
            <label for="weight">体重 (kg)</label>
            <input type="number" id="weight" name="weight" placeholder="例如：60">
        </div>
        <div class="form-group">
            <label for="bust">胸围 (cm)</label>
            <input type="number" id="bust" name="bust" placeholder="例如：90">
        </div>
        <div class="form-group">
            <label for="waist">腰围 (cm)</label>
            <input type="number" id="waist" name="waist" placeholder="例如：70">
        </div>
        <div class="form-group">
            <label for="hips">臀围 (cm)</label>
            <input type="number" id="hips" name="hips" placeholder="例如：90">
        </div>
        <div class="form-group">
            <label for="shoulder_width">肩宽 (cm)</label>
            <input type="number" id="shoulder_width" name="shoulder_width" placeholder="例如：40">
        </div>
        <div class="form-group">
            <label for="skin_tone">肤色</label>
            <input type="text" id="skin_tone" name="skin_tone" placeholder="例如：偏黄、偏白、小麦色等">
        </div>
        <div class="form-group">
            <label for="face_shape">脸型</label>
            <input type="text" id="face_shape" name="face_shape" placeholder="例如：圆形、方形、瓜子脸等">
        </div>
        <div class="form-group">
            <label for="body_shape">体型</label>
            <input type="text" id="body_shape" name="body_shape" placeholder="例如：梨形、苹果型、沙漏型等">
        </div>
        <div class="form-group">
            <label for="remarks">其他备注</label>
            <textarea id="remarks" name="remarks" rows="4" placeholder="请输入任何其他想告诉形象分析师的信息"></textarea>
        </div>
        <div class="form-group">
            <label for="photos">上传照片（可多选）</label>
            <input type="file" id="photos" name="photos" multiple accept="image/*">
            <div class="preview-images" id="preview-container"></div>
        </div>
        <button id="submit-btn">提交分析</button>
    </div>
    
    <div id="loading" class="spinner hidden"></div>
    
    <div id="result-container" class="result hidden">
        <div class="nav-tabs">
            <div class="tab active" data-tab="formatted">格式化结果</div>
            <div class="tab" data-tab="raw">原始JSON</div>
        </div>
        
        <div id="formatted-result" class="tab-content active">
            <!-- 将在JS中动态填充 -->
        </div>
        
        <div id="raw-result" class="tab-content">
            <pre id="result-json"></pre>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 照片预览功能
            const photoInput = document.getElementById('photos');
            const previewContainer = document.getElementById('preview-container');
            
            photoInput.addEventListener('change', function(e) {
                previewContainer.innerHTML = '';
                const files = e.target.files;
                
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    if (!file.type.match('image.*')) {
                        continue;
                    }
                    
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.className = 'preview-image';
                        previewContainer.appendChild(img);
                    };
                    reader.readAsDataURL(file);
                }
            });
            
            // 标签切换功能
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有标签的active类
                    tabs.forEach(t => t.classList.remove('active'));
                    // 移除所有内容的active类
                    document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                    
                    // 添加当前标签的active类
                    this.classList.add('active');
                    // 显示对应的内容
                    const tabContent = document.getElementById(this.dataset.tab + '-result');
                    tabContent.classList.add('active');
                });
            });
            
            // 提交表单
            const submitBtn = document.getElementById('submit-btn');
            const formContainer = document.getElementById('form-container');
            const loading = document.getElementById('loading');
            const resultContainer = document.getElementById('result-container');
            const formattedResult = document.getElementById('formatted-result');
            const resultJson = document.getElementById('result-json');
            
            submitBtn.addEventListener('click', function() {
                // 收集表单数据
                const userData = {
                    gender: document.getElementById('gender').value,
                    height: document.getElementById('height').value,
                    weight: document.getElementById('weight').value,
                    bust: document.getElementById('bust').value,
                    waist: document.getElementById('waist').value,
                    hips: document.getElementById('hips').value,
                    shoulder_width: document.getElementById('shoulder_width').value,
                    skin_tone: document.getElementById('skin_tone').value,
                    face_shape: document.getElementById('face_shape').value,
                    body_shape: document.getElementById('body_shape').value,
                    remarks: document.getElementById('remarks').value
                };
                
                // 验证必填字段
                if (!userData.height || !userData.weight) {
                    alert('请至少填写身高和体重信息！');
                    return;
                }
                
                // 显示加载状态
                formContainer.classList.add('hidden');
                loading.classList.remove('hidden');
                resultContainer.classList.add('hidden');
                
                // 处理照片上传
                const photoUrls = []; // 在实际场景中，这里应该是已上传图片的URL
                
                // 模拟照片上传（实际项目中应替换为真实上传逻辑）
                const files = document.getElementById('photos').files;
                const uploadPromises = [];
                
                for (let i = 0; i < files.length; i++) {
                    // 这里只是模拟，实际应该将文件上传到服务器并获取URL
                    uploadPromises.push(new Promise(resolve => {
                        setTimeout(() => {
                            // 模拟URL，实际应该是服务器返回的URL
                            resolve(`https://example.com/uploads/photo_${i+1}.jpg`);
                        }, 500);
                    }));
                }
                
                Promise.all(uploadPromises).then(urls => {
                    photoUrls.push(...urls);
                    
                    // 发送分析请求
                    fetch('https://www.furrywoo.com/gemini/gerenxx.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            userData: userData,
                            photoUrls: photoUrls,
                            analysisId: 'test-' + Date.now()
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        // 隐藏加载状态
                        loading.classList.add('hidden');
                        
                        // 显示结果
                        resultContainer.classList.remove('hidden');
                        
                        // 显示原始JSON
                        resultJson.textContent = JSON.stringify(data, null, 2);
                        
                        // 格式化显示结果
                        if (data.error) {
                            formattedResult.innerHTML = `<div class="error">分析失败: ${data.msg}</div>`;
                            return;
                        }
                        
                        const result = data.data;
                        
                        // 构建格式化的HTML
                        let html = '';
                        
                        // 身形分析
                        if (result['身形分析']) {
                            html += `
                                <div class="result-section">
                                    <h3>身形分析</h3>
                                    <p><strong>体型分类：</strong>${result['身形分析']['体型分类']}</p>
                                    <p><strong>身材比例：</strong>${result['身形分析']['身材比例']}</p>
                                    
                                    <p><strong>优势特点：</strong></p>
                                    <ul>
                                        ${result['身形分析']['优势特点'].map(item => `<li>${item}</li>`).join('')}
                                    </ul>
                                    
                                    <p><strong>需要注意的部位：</strong></p>
                                    <ul>
                                        ${result['身形分析']['需要注意的部位'].map(item => `<li>${item}</li>`).join('')}
                                    </ul>
                                </div>
                            `;
                        }
                        
                        // 肤色分析
                        if (result['肤色分析']) {
                            html += `
                                <div class="result-section">
                                    <h3>肤色分析</h3>
                                    <p><strong>色调：</strong>${result['肤色分析']['色调']}</p>
                                    
                                    <p><strong>适合色系：</strong></p>
                                    <ul>
                                        ${result['肤色分析']['适合色系'].map(item => `<li>${item}</li>`).join('')}
                                    </ul>
                                    
                                    <p><strong>避免色系：</strong></p>
                                    <ul>
                                        ${result['肤色分析']['避免色系'].map(item => `<li>${item}</li>`).join('')}
                                    </ul>
                                </div>
                            `;
                        }
                        
                        // 面部特征
                        if (result['面部特征']) {
                            html += `
                                <div class="result-section">
                                    <h3>面部特征</h3>
                                    <p><strong>脸型：</strong>${result['面部特征']['脸型']}</p>
                                    
                                    <p><strong>适合发型：</strong></p>
                                    <ul>
                                        ${result['面部特征']['适合发型'].map(item => `<li>${item}</li>`).join('')}
                                    </ul>
                                    
                                    <p><strong>适合妆容：</strong>${result['面部特征']['适合妆容']}</p>
                                </div>
                            `;
                        }
                        
                        // 风格定位
                        if (result['风格定位']) {
                            html += `
                                <div class="result-section">
                                    <h3>风格定位</h3>
                                    
                                    <p><strong>适合风格：</strong></p>
                                    <ul>
                                        ${result['风格定位']['适合风格'].map(item => `<li>${item}</li>`).join('')}
                                    </ul>
                                    
                                    <p><strong>不适合风格：</strong></p>
                                    <ul>
                                        ${result['风格定位']['不适合风格'].map(item => `<li>${item}</li>`).join('')}
                                    </ul>
                                    
                                    <p><strong>个性化建议：</strong>${result['风格定位']['个性化建议']}</p>
                                </div>
                            `;
                        }
                        
                        // 显示其他部分（穿搭建议、搭配技巧、场合建议、形象提升计划）
                        const remainingSections = ['穿搭建议', '搭配技巧', '场合建议', '形象提升计划'];
                        remainingSections.forEach(section => {
                            if (result[section]) {
                                html += `<div class="result-section"><h3>${section}</h3>`;
                                
                                Object.keys(result[section]).forEach(key => {
                                    const value = result[section][key];
                                    
                                    if (Array.isArray(value)) {
                                        html += `
                                            <p><strong>${key}：</strong></p>
                                            <ul>
                                                ${value.map(item => `<li>${item}</li>`).join('')}
                                            </ul>
                                        `;
                                    } else {
                                        html += `<p><strong>${key}：</strong>${value}</p>`;
                                    }
                                });
                                
                                html += `</div>`;
                            }
                        });
                        
                        // 总结
                        if (result['总结']) {
                            html += `
                                <div class="result-section">
                                    <h3>总结</h3>
                                    <p>${result['总结']}</p>
                                </div>
                            `;
                        }
                        
                        formattedResult.innerHTML = html;
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        loading.classList.add('hidden');
                        formContainer.classList.remove('hidden');
                        alert('请求失败，请稍后再试');
                    });
                });
            });
        });
    </script>
</body>
</html> 