<?php
/**
 * 管理员获取形象分析列表API
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 添加日志记录函数
function logDebug($message, $data = null) {
    $logFile = __DIR__ . '/debug_analysis_list.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message";
    
    if ($data !== null) {
        $logMessage .= ": " . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    file_put_contents($logFile, $logMessage . PHP_EOL, FILE_APPEND);
}

// 记录请求信息
logDebug("接收到形象分析列表请求", $_GET);

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

// 检查是否有Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '需要授权头']);
    exit;
}

// 验证管理员token
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或过期的令牌']);
    exit;
}

// 获取分页参数
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 10;
$offset = ($page - 1) * $limit;

// 获取搜索参数
$search = isset($_GET['search']) ? $_GET['search'] : '';
$status = isset($_GET['status']) ? $_GET['status'] : '';
$payment_status = isset($_GET['payment_status']) ? $_GET['payment_status'] : '';

// 记录筛选参数
logDebug("筛选参数", [
    'page' => $page,
    'limit' => $limit,
    'search' => $search,
    'status' => $status,
    'payment_status' => $payment_status
]);

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

// 构建查询条件
$whereClause = "1=1";
$params = [];

if (!empty($search)) {
    // 支持按用户ID、订单ID搜索
    if (is_numeric($search)) {
        $whereClause .= " AND (a.user_id = :search OR a.id = :search)";
        $params[':search'] = $search;
    } else {
        $whereClause .= " AND (a.order_id LIKE :search)";
        $params[':search'] = "%$search%";
    }
}

if (!empty($status)) {
    // 检查状态值是否在允许的范围内
    $allowedStatuses = ['pending', 'processing', 'completed', 'failed'];
    if (in_array($status, $allowedStatuses)) {
        $whereClause .= " AND a.status = :status";
        $params[':status'] = $status;
    } else {
        logDebug("无效的状态值", $status);
    }
}

if (!empty($payment_status)) {
    $whereClause .= " AND a.payment_status = :payment_status";
    $params[':payment_status'] = $payment_status;
}

// 记录SQL查询条件
logDebug("SQL WHERE子句", $whereClause);
logDebug("SQL参数", $params);

try {
    // 查询总记录数
    $countSql = "SELECT COUNT(*) as total FROM user_image_analysis a WHERE $whereClause";
    logDebug("计数SQL", $countSql);
    
    $countStmt = $conn->prepare($countSql);
    
    foreach ($params as $key => $value) {
        $countStmt->bindValue($key, $value);
    }
    
    $countStmt->execute();
    $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    logDebug("总记录数", $totalCount);
    
    // 查询形象分析记录
    $sql = "SELECT a.*, u.nickname, u.avatar_url 
            FROM user_image_analysis a 
            LEFT JOIN users u ON a.user_id = u.id 
            WHERE $whereClause 
            ORDER BY a.created_at DESC 
            LIMIT :limit OFFSET :offset";
    logDebug("查询SQL", $sql);
    
    $stmt = $conn->prepare($sql);
    
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
    logDebug("查询结果记录数", count($records));
    
    // 处理结果，确保数据格式一致
    foreach ($records as &$record) {
        // 处理可能的NULL值
        $record['nickname'] = $record['nickname'] ?? '未知用户';
        $record['photo_urls'] = !empty($record['photo_urls']) ? json_decode($record['photo_urls'], true) : [];
        
        // 记录每条记录的状态
        logDebug("记录ID {$record['id']} 的状态", $record['status']);
    }
    
    // 计算总页数
    $totalPages = ceil($totalCount / $limit);
    
    // 返回结果
    $response = [
        'error' => false,
        'data' => [
            'total' => $totalCount,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => $totalPages,
            'records' => $records
        ]
    ];
    
    logDebug("返回成功响应");
    echo json_encode($response);
    
} catch (PDOException $e) {
    logDebug("数据库错误", $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '数据库查询失败: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    logDebug("其他错误", $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '处理请求时出错: ' . $e->getMessage()
    ]);
} 