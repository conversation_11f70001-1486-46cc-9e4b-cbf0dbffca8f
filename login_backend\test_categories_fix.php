<?php
/**
 * 测试分类API修复效果的脚本
 * 对比修复前后的查询结果
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['user_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $result = [];
    $result['user_id'] = $userId;
    
    // 1. 检查用户所在的圈子
    $stmt = $conn->prepare("
        SELECT circle_id FROM circle_members 
        WHERE user_id = :user_id AND status = 'active'
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $userCircles = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $result['user_circles'] = $userCircles;
    
    if (empty($userCircles)) {
        $result['message'] = '用户不在任何圈子中';
        echo json_encode(['status' => 'success', 'data' => $result], JSON_PRETTY_PRINT);
        exit;
    }
    
    // 2. 测试修复后的"全部"数据源查询
    $sql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                   u.nickname as creator_nickname,
                   CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
            FROM clothing_categories c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE (
                -- 当前用户的系统分类（每个用户都有自己的系统分类副本）
                (c.is_system = 1 AND c.user_id = :user_id) OR
                -- 所有自定义分类：个人的 + 圈子共享的
                (c.is_system = 0 AND ((c.user_id = :user_id AND c.circle_id IS NULL) OR 
                 (c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'))))
            )
            ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC";
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $allCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['fixed_all_query'] = [
        'sql' => $sql,
        'count' => count($allCategories),
        'categories' => $allCategories
    ];
    
    // 3. 分别查询各种类型的分类
    // 用户的系统分类
    $stmt = $conn->prepare("
        SELECT id, user_id, name, code, is_system, sort_order, created_at, circle_id
        FROM clothing_categories
        WHERE is_system = 1 AND user_id = :user_id
        ORDER BY sort_order ASC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $systemCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['system_categories'] = [
        'count' => count($systemCategories),
        'categories' => $systemCategories
    ];
    
    // 用户的个人自定义分类
    $stmt = $conn->prepare("
        SELECT id, user_id, name, code, is_system, sort_order, created_at, circle_id
        FROM clothing_categories
        WHERE is_system = 0 AND user_id = :user_id AND circle_id IS NULL
        ORDER BY sort_order ASC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $personalCustomCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['personal_custom_categories'] = [
        'count' => count($personalCustomCategories),
        'categories' => $personalCustomCategories
    ];
    
    // 圈子中的共享自定义分类
    $circleIds = implode(',', $userCircles);
    $stmt = $conn->prepare("
        SELECT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at, c.circle_id,
               u.nickname as creator_nickname
        FROM clothing_categories c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.is_system = 0 AND c.circle_id IN ($circleIds)
        ORDER BY c.sort_order ASC
    ");
    $stmt->execute();
    $sharedCustomCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['shared_custom_categories'] = [
        'count' => count($sharedCustomCategories),
        'categories' => $sharedCustomCategories
    ];
    
    // 4. 统计信息
    $result['summary'] = [
        'total_categories' => count($allCategories),
        'system_categories' => count($systemCategories),
        'personal_custom_categories' => count($personalCustomCategories),
        'shared_custom_categories' => count($sharedCustomCategories),
        'expected_total' => count($systemCategories) + count($personalCustomCategories) + count($sharedCustomCategories)
    ];
    
    // 5. 检查是否有其他用户在圈子中创建了自定义分类
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM clothing_categories c
        WHERE c.is_system = 0 AND c.circle_id IN ($circleIds) AND c.user_id != :user_id
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $otherUsersCustomCount = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $result['other_users_custom_categories'] = $otherUsersCustomCount['count'];
    
    echo json_encode([
        'status' => 'success',
        'data' => $result
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '查询失败: ' . $e->getMessage()
    ]);
}
?>
