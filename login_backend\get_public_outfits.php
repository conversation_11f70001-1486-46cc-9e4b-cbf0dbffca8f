<?php
// 引入必要的文件
require_once 'db.php';
require_once 'config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 处理GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'error' => true,
        'msg' => '不支持的请求方法'
    ]);
    exit;
}

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();

    // 获取分页参数
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $perPage = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20; // 默认每页20条
    $offset = ($page - 1) * $perPage;
    
    // 构建查询条件，只获取is_public=1的穿搭
    $whereClause = "WHERE o.is_public = 1";
    
    // 记录请求信息，方便调试
    $logFile = fopen('logs/public_outfits_access.log', 'a');
    if ($logFile) {
        $timestamp = date('Y-m-d H:i:s');
        fwrite($logFile, "[$timestamp] 请求公开穿搭列表: page=$page, per_page=$perPage\n");
        fclose($logFile);
    }
    
    // 查询总数据量
    $countStmt = $conn->prepare("
        SELECT COUNT(*) 
        FROM outfits o
        LEFT JOIN outfit_categories c ON o.category_id = c.id
        $whereClause
    ");
    $countStmt->execute();
    $totalCount = $countStmt->fetchColumn();
    $totalPages = ceil($totalCount / $perPage);

    // 查询公开穿搭列表，按创建时间倒序排列
    $stmt = $conn->prepare("
        SELECT o.id, o.name, o.description, o.thumbnail_url, o.outfit_data, o.category_id, 
               c.name as category_name, o.created_at, o.updated_at, o.is_public, o.user_id,
               o.likes_count, u.nickname, u.avatar_url, u.gender
        FROM outfits o
        LEFT JOIN outfit_categories c ON o.category_id = c.id
        LEFT JOIN users u ON o.user_id = u.id
        $whereClause
        ORDER BY o.created_at DESC
        LIMIT :offset, :per_page
    ");
    
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':per_page', $perPage, PDO::PARAM_INT);
    $stmt->execute();
    
    $outfits = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理查询结果
    $result = [];
    foreach ($outfits as $outfit) {
        // 解析JSON数据
        $outfitData = json_decode($outfit['outfit_data'], true);
        
        // 构建返回的穿搭对象
        $resultOutfit = [
            'id' => $outfit['id'],
            'name' => $outfit['name'],
            'description' => $outfit['description'],
            'thumbnail' => $outfit['thumbnail_url'],
            'category_id' => $outfit['category_id'],
            'category_name' => $outfit['category_name'],
            'created_at' => $outfit['created_at'],
            'updated_at' => $outfit['updated_at'],
            'items' => $outfitData['items'] ?? [],
            'is_public' => (int)$outfit['is_public'],
            'user_id' => $outfit['user_id'],
            'likes_count' => (int)$outfit['likes_count'],
            'creator_nickname' => $outfit['nickname'] ?: '匿名用户',
            'creator_avatar' => $outfit['avatar_url'] ?: '',
            'creator_gender' => (int)$outfit['gender']
        ];
        
        $result[] = $resultOutfit;
    }
    
    // 返回结果
    echo json_encode([
        'success' => true,
        'data' => $result,
        'pagination' => [
            'total' => $totalCount,
            'per_page' => $perPage,
            'current_page' => $page,
            'total_pages' => $totalPages
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '获取穿搭列表失败: ' . $e->getMessage()
    ]);
} 