const app = getApp();

Page({
  data: {
    ratingData: null,
    photoUrl: ''
  },

  onLoad: function(options) {
    // 尝试获取页面参数中的评分数据ID
    if (options.id) {
      this.loadRatingData(options.id);
    } else if (app.globalData.tempRatingData) {
      // 如果没有ID但有临时数据，直接使用临时数据
      this.setData({
        ratingData: app.globalData.tempRatingData,
        photoUrl: app.globalData.tempPhotoUrl || ''
      });
      
      // 使用后清除临时数据
      app.globalData.tempRatingData = null;
      app.globalData.tempPhotoUrl = null;
    } else {
      // 没有评分数据，返回上一页
      wx.showToast({
        title: '未找到评分数据',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 从服务器加载评分数据
  loadRatingData: function(id) {
    wx.showLoading({
      title: '加载中...',
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_outfit_rating.php`,
      method: 'GET',
      data: {
        id: id
      },
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        if (!res.data.error && res.data.data) {
          this.setData({
            ratingData: res.data.data,
            photoUrl: res.data.data.photo_url
          });
        } else {
          wx.showToast({
            title: '获取评分数据失败',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 保存评分图片
  saveImage: function() {
    if (!this.data.photoUrl) {
      wx.showToast({
        title: '无法保存图片',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '保存中...',
    });

    // 下载图片
    wx.downloadFile({
      url: this.data.photoUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          // 保存图片到相册
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.showToast({
                title: '保存成功',
                icon: 'success'
              });
            },
            fail: (err) => {
              console.error('保存失败:', err);
              wx.showToast({
                title: '保存失败，请检查权限',
                icon: 'none'
              });
            }
          });
        } else {
          wx.showToast({
            title: '下载图片失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('下载失败:', err);
        wx.showToast({
          title: '下载图片失败',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 重新拍摄
  retake: function() {
    wx.navigateBack();
  },

  // 分享给好友
  onShareAppMessage: function() {
    return {
      title: `我的穿搭获得了${this.data.ratingData?.rating_details?.overall_score || ''}分！来试试你的穿搭吧~`,
      path: '/pages/outfit_rating/index/index',
      imageUrl: this.data.photoUrl || 'https://images.alidog.cn/logo/xxfx.png'
    }
  }
}); 