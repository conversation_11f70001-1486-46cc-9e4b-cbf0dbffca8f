const app = getApp();

Page({
  data: {
    histories: [],
    leftItems: [], // 左列项目
    rightItems: [], // 右列项目
    isLoading: true,
    showModal: false, // 是否显示弹框
    currentHistory: {} // 当前查看的历史记录
  },
  
  onLoad: function () {
    wx.setNavigationBarTitle({
      title: '试穿历史'
    });
    this.loadHistories();
  },
  
  onShow: function () {
    // 每次显示页面时重新加载数据
    this.loadHistories();
  },
  
  // 分配历史记录到两列
  distributeHistories: function() {
    const histories = this.data.histories;
    const leftItems = [];
    const rightItems = [];
    
    // 从左到右、从上到下的顺序分配照片
    histories.forEach((history, index) => {
      // 按顺序填充：左1、右1、左2、右2...
      if (index % 2 === 0) {
        leftItems.push(history);
      } else {
        rightItems.push(history);
      }
    });
    
    this.setData({
      leftItems,
      rightItems
    });
  },
  
  // 加载试穿历史列表
  loadHistories: function () {
    const token = app.globalData.token;
    if (!token) {
      console.error('未登录');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      isLoading: true
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_try_on_history.php`,
      method: 'GET',
      header: {
        'Authorization': token
      },
      success: (res) => {
        console.log('获取试穿历史响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          let histories = res.data.data || [];
          
          // 确保数据结构统一
          histories = histories.map(history => {
            // 转换clothes_ids从字符串到数组
            let clothesIds = history.clothes_ids;
            if (typeof clothesIds === 'string') {
              try {
                clothesIds = JSON.parse(clothesIds);
              } catch (e) {
                clothesIds = [];
              }
            }
            
            return {
              ...history,
              clothes_ids: clothesIds
            };
          });
          
          this.setData({
            histories: histories
          }, () => {
            // 在数据设置完成后，分配到两列
            this.distributeHistories();
          });
        } else {
          wx.showToast({
            title: res.data.msg || '获取试穿历史失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('获取试穿历史请求失败:', err);
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({
          isLoading: false
        });
      }
    });
  },
  
  // 跳转到试穿结果页面
  goToResultPage: function (e) {
    const historyId = e.currentTarget.dataset.id;
    const imageUrl = e.currentTarget.dataset.url;
    const clothesIds = e.currentTarget.dataset.clothes;
    
    // 查找完整历史记录对象
    const history = this.data.histories.find(item => item.id === historyId);
    
    if (history) {
      // 准备试穿结果数据
      const tryOnResult = {
        result_image_url: history.result_image_url,
        created_at: history.created_at,
        clothes_ids: history.clothes_ids,
        // 这里可以添加更多需要传递的数据
      };
      
      // 将数据存储到全局变量，供结果页面使用
      app.globalData.tryOnResult = tryOnResult;
      
      // 跳转到试穿结果页面
      wx.navigateTo({
        url: '/pages/try_on/result/index'
      });
    } else {
      wx.showToast({
        title: '未找到对应的试穿记录',
        icon: 'none'
      });
    }
  },
  
  // 显示历史记录详情弹框
  showHistoryModal: function (e) {
    const historyId = e.currentTarget.dataset.id;
    const imageUrl = e.currentTarget.dataset.url;
    const clothesIds = e.currentTarget.dataset.clothes;
    
    // 查找完整历史记录对象
    const history = this.data.histories.find(item => item.id === historyId);
    
    if (history) {
      this.setData({
        currentHistory: history,
        showModal: true
      });
    }
  },
  
  // 隐藏弹框
  hideHistoryModal: function () {
    this.setData({
      showModal: false
    });
  },
  
  // 阻止冒泡
  stopPropagation: function (e) {
    // 阻止事件冒泡
  },
  
  // 图片加载完成
  onImageLoad: function (e) {
    console.log('图片加载完成');
  }
}); 