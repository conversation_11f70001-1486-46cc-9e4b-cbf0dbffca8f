<?php
// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => '不支持的请求方法']);
    exit;
}

// 验证管理员身份
$authHeader = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
if (empty($authHeader) || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '未提供授权Token']);
    exit;
}

$token = $matches[1];
if (empty($token)) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '无效的Token']);
    exit;
}

// 引入配置文件
require_once '../config.php';
require_once 'auth.php';

// 验证Token
try {
    $auth = new Auth();
    $payload = $auth->verifyAdminToken($token);
    if (!$payload) {
        throw new Exception('管理员身份验证失败');
    }
} catch (Exception $e) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
    exit;
}

// 获取POST数据
$jsonData = file_get_contents('php://input');
$postData = json_decode($jsonData, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => '无效的JSON数据']);
    exit;
}

// 验证所需配置部分是否存在
if (!isset($postData['database']) || !isset($postData['wechat']) || 
    !isset($postData['admin']) || !isset($postData['aliyun']) || 
    !isset($postData['storage_paths'])) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => '配置数据不完整']);
    exit;
}

// 备份现有配置文件
$backupPath = '../config_backup_' . date('YmdHis') . '.php';
if (!copy('../config.php', $backupPath)) {
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => '无法备份现有配置文件']);
    exit;
}

// 对比处理敏感数据
// 如果提交的值是掩码形式（包含*号），则使用原始配置值
function getActualValue($newValue, $originalValue) {
    // 检查值是否包含掩码（*号）
    if (strpos($newValue, '*') !== false) {
        return $originalValue;
    }
    return $newValue;
}

// 从POST数据和现有配置中获取实际值
$database = [
    'DB_HOST' => $postData['database']['DB_HOST'],
    'DB_NAME' => $postData['database']['DB_NAME'],
    'DB_USER' => $postData['database']['DB_USER'],
    'DB_PASS' => getActualValue($postData['database']['DB_PASS'], DB_PASS)
];

$wechat = [
    'WX_APPID' => $postData['wechat']['WX_APPID'],
    'WX_SECRET' => getActualValue($postData['wechat']['WX_SECRET'], WX_SECRET),
    'API_DOMAIN' => $postData['wechat']['API_DOMAIN'],
    'WX_TOKEN' => getActualValue($postData['wechat']['WX_TOKEN'], WX_TOKEN),
    'WX_ENCODING_AES_KEY' => getActualValue($postData['wechat']['WX_ENCODING_AES_KEY'], WX_ENCODING_AES_KEY)
];

$admin = [
    'ADMIN_SECRET_KEY' => getActualValue($postData['admin']['ADMIN_SECRET_KEY'], ADMIN_SECRET_KEY)
];

$aliyun = [
    'ALIYUN_ACCESS_KEY_ID' => getActualValue($postData['aliyun']['ALIYUN_ACCESS_KEY_ID'], ALIYUN_ACCESS_KEY_ID),
    'ALIYUN_ACCESS_KEY_SECRET' => getActualValue($postData['aliyun']['ALIYUN_ACCESS_KEY_SECRET'], ALIYUN_ACCESS_KEY_SECRET),
    'ALIYUN_OUTFIT_API_KEY' => getActualValue($postData['aliyun']['ALIYUN_OUTFIT_API_KEY'], ALIYUN_OUTFIT_API_KEY),
    'ALIYUN_OSS_ENDPOINT' => $postData['aliyun']['ALIYUN_OSS_ENDPOINT'],
    'ALIYUN_OSS_BUCKET' => $postData['aliyun']['ALIYUN_OSS_BUCKET'],
    'ALIYUN_OSS_BUCKET_DOMAIN' => $postData['aliyun']['ALIYUN_OSS_BUCKET_DOMAIN']
];

$storage_paths = [
    'OSS_PATH_CLOTHES' => $postData['storage_paths']['OSS_PATH_CLOTHES'],
    'OSS_PATH_PHOTOS' => $postData['storage_paths']['OSS_PATH_PHOTOS'],
    'OSS_PATH_TRY_ON' => $postData['storage_paths']['OSS_PATH_TRY_ON']
];

// 生成新的配置文件内容
$configContent = <<<PHP
<?php
/**
 * 次元衣柜配置文件
 * 由系统设置更新于 {$payload['realName']} - {$payload['username']} - {date('Y-m-d H:i:s')}
 */

// 数据库配置
define('DB_HOST', '{$database['DB_HOST']}');
define('DB_NAME', '{$database['DB_NAME']}');
define('DB_USER', '{$database['DB_USER']}');
define('DB_PASS', '{$database['DB_PASS']}');

// 微信小程序配置
define('WX_APPID', '{$wechat['WX_APPID']}');
define('WX_SECRET', '{$wechat['WX_SECRET']}');
define('API_DOMAIN', '{$wechat['API_DOMAIN']}');

// 管理员配置
define('ADMIN_SECRET_KEY', '{$admin['ADMIN_SECRET_KEY']}');

// 微信客服消息验证配置
define('WX_TOKEN', '{$wechat['WX_TOKEN']}');
define('WX_ENCODING_AES_KEY', '{$wechat['WX_ENCODING_AES_KEY']}');

// 阿里云API配置
define('ALIYUN_ACCESS_KEY_ID', '{$aliyun['ALIYUN_ACCESS_KEY_ID']}');
define('ALIYUN_ACCESS_KEY_SECRET', '{$aliyun['ALIYUN_ACCESS_KEY_SECRET']}');
define('ALIYUN_OUTFIT_API_KEY', '{$aliyun['ALIYUN_OUTFIT_API_KEY']}');

// 阿里云OSS配置
define('ALIYUN_OSS_ENDPOINT', '{$aliyun['ALIYUN_OSS_ENDPOINT']}');
define('ALIYUN_OSS_BUCKET', '{$aliyun['ALIYUN_OSS_BUCKET']}');
define('ALIYUN_OSS_BUCKET_DOMAIN', '{$aliyun['ALIYUN_OSS_BUCKET_DOMAIN']}');

// OSS存储路径前缀
define('OSS_PATH_CLOTHES', '{$storage_paths['OSS_PATH_CLOTHES']}');
define('OSS_PATH_PHOTOS', '{$storage_paths['OSS_PATH_PHOTOS']}');
define('OSS_PATH_TRY_ON', '{$storage_paths['OSS_PATH_TRY_ON']}');
PHP;

// 尝试写入新配置文件
try {
    if (!file_put_contents('../config.php', $configContent)) {
        throw new Exception('无法写入配置文件');
    }
    
    // 返回成功消息
    echo json_encode([
        'status' => 'success',
        'message' => '系统设置已成功更新',
        'backup' => basename($backupPath)
    ]);
} catch (Exception $e) {
    // 恢复原配置
    copy($backupPath, '../config.php');
    
    http_response_code(500);
    echo json_encode([
        'status' => 'error', 
        'message' => '更新配置失败：' . $e->getMessage()
    ]);
} 