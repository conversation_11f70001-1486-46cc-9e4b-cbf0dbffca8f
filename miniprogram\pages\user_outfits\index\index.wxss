/* 复用穿搭列表的样式 */
@import "/pages/outfits/index/index.wxss";
@import "/pages/outfit_square/index/index.wxss";

/* 用户信息头部 */
.page-header {
  padding: 30rpx 30rpx 20rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.user-profile {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  border: 1rpx solid #f0f0f0;
  background-color: #f8f8f8;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.page-subtitle {
  font-size: 24rpx;
  color: #999;
} 