-- 为outfits表添加排序字段
-- 执行时间：2025-01-08

-- 添加sort_order字段
ALTER TABLE `outfits` ADD COLUMN `sort_order` INT(11) DEFAULT 0 COMMENT '排序顺序，数值越小越靠前' AFTER `calendar_date`;

-- 为现有数据设置默认排序值（按创建时间排序）
UPDATE `outfits` SET `sort_order` = `id` WHERE `sort_order` = 0;

-- 添加索引以提高排序查询性能
ALTER TABLE `outfits` ADD INDEX `idx_sort_order` (`sort_order`);
ALTER TABLE `outfits` ADD INDEX `idx_user_sort` (`user_id`, `sort_order`);
ALTER TABLE `outfits` ADD INDEX `idx_category_sort` (`category_id`, `sort_order`);
