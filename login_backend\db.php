<?php
require_once 'config.php';

class Database {
    private $conn;
    
    public function __construct() {
        try {
            $this->conn = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
                DB_USER,
                DB_PASS,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
        } catch(PDOException $e) {
            die("Database connection failed: " . $e->getMessage());
        }
    }
    
    public function getConnection() {
        return $this->conn;
    }
    
    // Find user by openid
    public function findUserByOpenid($openid) {
        $stmt = $this->conn->prepare("SELECT * FROM users WHERE openid = :openid");
        $stmt->bindParam(':openid', $openid);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    // Create a new user
    public function createUser($openid, $sessionKey, $unionid = null) {
        $stmt = $this->conn->prepare("INSERT INTO users (openid, session_key, unionid, created_at) VALUES (:openid, :session_key, :unionid, NOW())");
        $stmt->bindParam(':openid', $openid);
        $stmt->bindParam(':session_key', $sessionKey);
        $stmt->bindParam(':unionid', $unionid);
        $stmt->execute();
        
        return $this->conn->lastInsertId();
    }
    
    // Update user's session key
    public function updateSessionKey($userId, $sessionKey) {
        $stmt = $this->conn->prepare("UPDATE users SET session_key = :session_key, updated_at = NOW() WHERE id = :id");
        $stmt->bindParam(':session_key', $sessionKey);
        $stmt->bindParam(':id', $userId);
        $stmt->execute();
        
        return $stmt->rowCount();
    }
    
    // Update user profile
    public function updateUserProfile($userId, $nickName, $avatarUrl, $gender = null) {
        $stmt = $this->conn->prepare("UPDATE users SET nickname = :nickname, avatar_url = :avatar_url, gender = :gender, updated_at = NOW() WHERE id = :id");
        $stmt->bindParam(':nickname', $nickName);
        $stmt->bindParam(':avatar_url', $avatarUrl);
        $stmt->bindParam(':gender', $gender);
        $stmt->bindParam(':id', $userId);
        $stmt->execute();
        
        return $stmt->rowCount();
    }
} 