/* 复用穿搭列表的样式 */
@import "/pages/outfits/index/index.wxss";

/* 页面标题 */
.page-title {
  font-size: 36rpx;
  font-weight: 600;
  padding: 30rpx 30rpx 10rpx;
  color: #333;
  background-color: #fff;
}

.page-subtitle {
  font-size: 24rpx;
  color: #999;
  padding: 0 30rpx 20rpx;
  background-color: #fff;
  margin-bottom: 10rpx;
}

/* 搜索框 */
.search-container {
  padding: 20rpx 30rpx;
  background-color: #fff;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 14rpx 24rpx;
  position: relative;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  opacity: 0.5;
}

.search-input {
  flex: 1;
  height: 36rpx;
  font-size: 28rpx;
  color: #333;
  padding: 0;
}

.search-placeholder {
  color: #999;
  font-size: 28rpx;
}

.search-clear {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 32rpx;
  font-weight: normal;
  border-radius: 50%;
}

.search-clear:active {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 穿搭名称容器 */
.outfit-name-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

/* 创建者容器 */
.outfit-creator-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8rpx;
}

/* 创建者信息 */
.creator-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.creator-avatar {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.creator-name {
  font-size: 24rpx;
  color: #666;
  max-width: 200rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  width: 90%;
  margin-bottom: 20rpx;
}

.empty-action {
  padding: 14rpx 30rpx;
  background-color: #f6f6f6;
  border-radius: 30rpx;
  color: #666;
  font-size: 26rpx;
  margin-top: 10rpx;
}

.empty-action:active {
  background-color: #eee;
}

/* 点赞信息 */
.like-info {
  display: flex;
  align-items: center;
  height: 28rpx;
}

.like-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 4rpx;
  display: block;
  flex-shrink: 0;
}

.like-count {
  font-size: 24rpx;
  color: #666;
  line-height: 1;
  display: flex;
  align-items: center;
} 