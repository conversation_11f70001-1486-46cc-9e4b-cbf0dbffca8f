<?php
/**
 * 调试圈子统计数据
 * 检查circle_member_stats表的数据状态
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "=== 圈子统计数据调试 ===\n";
    echo "当前用户ID: $userId\n\n";
    
    // 1. 检查用户所在的圈子
    echo "1. 用户所在的圈子:\n";
    $stmt = $conn->prepare("
        SELECT cm.circle_id, cm.status, cm.role, c.name as circle_name
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id
        ORDER BY cm.joined_at DESC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $userCircles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($userCircles)) {
        echo "用户不在任何圈子中\n";
        exit;
    }
    
    foreach ($userCircles as $circle) {
        echo "- 圈子ID: {$circle['circle_id']}, 名称: {$circle['circle_name']}, 状态: {$circle['status']}, 角色: {$circle['role']}\n";
    }
    echo "\n";
    
    // 获取活跃圈子
    $activeCircles = array_filter($userCircles, function($c) { return $c['status'] === 'active'; });
    
    if (empty($activeCircles)) {
        echo "用户没有活跃的圈子成员身份\n";
        exit;
    }
    
    foreach ($activeCircles as $circle) {
        $circleId = $circle['circle_id'];
        $circleName = $circle['circle_name'];
        
        echo "=== 圈子: $circleName (ID: $circleId) ===\n\n";
        
        // 2. 检查圈子中的所有成员
        echo "2. 圈子成员列表:\n";
        $stmt = $conn->prepare("
            SELECT cm.user_id, cm.status, cm.role, u.nickname, cm.joined_at
            FROM circle_members cm
            LEFT JOIN users u ON cm.user_id = u.id
            WHERE cm.circle_id = :circle_id
            ORDER BY cm.role DESC, cm.joined_at ASC
        ");
        $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
        $stmt->execute();
        $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($members as $member) {
            $isCurrent = $member['user_id'] == $userId ? ' (当前用户)' : '';
            echo "- 用户ID: {$member['user_id']}, 昵称: {$member['nickname']}, 角色: {$member['role']}, 状态: {$member['status']}{$isCurrent}\n";
        }
        echo "\n";
        
        // 3. 检查circle_member_stats表中的数据
        echo "3. 统计数据表 (circle_member_stats):\n";
        $stmt = $conn->prepare("
            SELECT cms.user_id, cms.wardrobe_count, cms.clothes_count, cms.outfit_count, 
                   cms.clothing_category_count, cms.last_contribution_at, cms.updated_at,
                   u.nickname
            FROM circle_member_stats cms
            LEFT JOIN users u ON cms.user_id = u.id
            WHERE cms.circle_id = :circle_id
            ORDER BY cms.user_id
        ");
        $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
        $stmt->execute();
        $statsRecords = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($statsRecords)) {
            echo "⚠️ 统计数据表中没有该圈子的记录\n";
        } else {
            foreach ($statsRecords as $stats) {
                echo "- 用户: {$stats['nickname']} (ID: {$stats['user_id']})\n";
                echo "  衣橱: {$stats['wardrobe_count']}, 衣物: {$stats['clothes_count']}, 穿搭: {$stats['outfit_count']}, 分类: {$stats['clothing_category_count']}\n";
                echo "  最后贡献: {$stats['last_contribution_at']}, 更新时间: {$stats['updated_at']}\n";
            }
        }
        echo "\n";
        
        // 4. 计算实际的数据统计
        echo "4. 实际数据统计:\n";
        foreach ($members as $member) {
            if ($member['status'] !== 'active') continue;
            
            $memberId = $member['user_id'];
            $memberName = $member['nickname'];
            
            // 统计实际数据
            $actualStats = calculateActualStats($conn, $circleId, $memberId);
            
            echo "- 用户: $memberName (ID: $memberId)\n";
            echo "  实际衣橱: {$actualStats['wardrobe_count']}, 实际衣物: {$actualStats['clothes_count']}, 实际穿搭: {$actualStats['outfit_count']}, 实际分类: {$actualStats['clothing_category_count']}\n";
        }
        echo "\n";
        
        // 5. 对比分析
        echo "5. 数据对比分析:\n";
        $statsMap = [];
        foreach ($statsRecords as $stats) {
            $statsMap[$stats['user_id']] = $stats;
        }
        
        foreach ($members as $member) {
            if ($member['status'] !== 'active') continue;
            
            $memberId = $member['user_id'];
            $memberName = $member['nickname'];
            
            $actualStats = calculateActualStats($conn, $circleId, $memberId);
            $recordedStats = isset($statsMap[$memberId]) ? $statsMap[$memberId] : null;
            
            echo "- 用户: $memberName (ID: $memberId)\n";
            
            if (!$recordedStats) {
                echo "  ❌ 统计表中没有记录\n";
            } else {
                $wardrobeDiff = $actualStats['wardrobe_count'] - $recordedStats['wardrobe_count'];
                $clothesDiff = $actualStats['clothes_count'] - $recordedStats['clothes_count'];
                $outfitDiff = $actualStats['outfit_count'] - $recordedStats['outfit_count'];
                $categoryDiff = $actualStats['clothing_category_count'] - $recordedStats['clothing_category_count'];
                
                if ($wardrobeDiff == 0 && $clothesDiff == 0 && $outfitDiff == 0 && $categoryDiff == 0) {
                    echo "  ✅ 统计数据一致\n";
                } else {
                    echo "  ⚠️ 统计数据不一致:\n";
                    if ($wardrobeDiff != 0) echo "    衣橱差异: $wardrobeDiff\n";
                    if ($clothesDiff != 0) echo "    衣物差异: $clothesDiff\n";
                    if ($outfitDiff != 0) echo "    穿搭差异: $outfitDiff\n";
                    if ($categoryDiff != 0) echo "    分类差异: $categoryDiff\n";
                }
            }
        }
        echo "\n";
    }
    
    echo "=== 调试完成 ===\n";
    echo "\n💡 如果发现统计数据不一致，可以运行以下API来修复:\n";
    echo "GET /refresh_circle_stats.php\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

/**
 * 计算实际的统计数据
 */
function calculateActualStats($conn, $circleId, $userId) {
    $stats = [
        'wardrobe_count' => 0,
        'clothes_count' => 0,
        'outfit_count' => 0,
        'clothing_category_count' => 0
    ];
    
    // 统计衣橱数量
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM wardrobes WHERE circle_id = :circle_id AND user_id = :user_id");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['wardrobe_count'] = intval($result['count']);
    
    // 统计衣物数量
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM clothes WHERE circle_id = :circle_id AND user_id = :user_id");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['clothes_count'] = intval($result['count']);
    
    // 统计穿搭数量
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM outfits WHERE circle_id = :circle_id AND user_id = :user_id");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['outfit_count'] = intval($result['count']);
    
    // 统计分类数量
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM clothing_categories WHERE circle_id = :circle_id AND user_id = :user_id AND is_system = 0");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['clothing_category_count'] = intval($result['count']);
    
    return $stats;
}
?>
