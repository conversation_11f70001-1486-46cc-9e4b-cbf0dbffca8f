<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

require_once 'config.php';
require_once 'auth.php';

// 验证用户Token (可选，如果想要对接口做权限控制)
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION']) && !empty($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
    
    $auth = new Auth();
    $payload = $auth->verifyToken($token);
    
    if (!$payload) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => '无效的授权']);
        exit();
    }
}

try {
    // 获取淘宝物料分类列表
    $categories = getTaobaoMaterialCategories();
    
    // 返回分类数据
    echo json_encode([
        'error' => false,
        'data' => $categories
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '获取淘宝物料分类失败: ' . $e->getMessage()
    ]);
} 