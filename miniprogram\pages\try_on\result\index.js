const app = getApp();

Page({
  data: {
    resultImage: '', // 试衣结果图片
    loading: true,   // 加载状态
    clothes: [],     // 试穿的衣物列表
    activeClothesId: null, // 当前选中的衣物ID
    error: null,      // 错误信息
    isAliyunOss: false, // 是否为阿里云OSS链接
    showDescription: false, // 是否显示描述
    showAddTime: false,     // 是否显示添加时间
    parsedDescription: {},
    showPreview: false
  },
  
  onLoad: function(options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '试穿效果'
    });
    
    // 设置初始状态为加载中
    this.setData({
      loading: true,
      resultImage: '',
      error: null
    });
    
    // 从全局数据获取试衣结果
    const tryOnResult = app.globalData.tryOnResult;
    console.log('试衣结果数据:', tryOnResult);
    
    if (!tryOnResult) {
      // 如果没有结果数据，显示加载中状态
      this.setData({
        loading: true,
        error: null
      });
      
      // 等待1秒后再次检查，可能API请求还在进行中
      setTimeout(() => {
        this.checkTryOnResult();
      }, 1000);
      
      return;
    }
    
    // 如果已有结果数据，直接处理
    this.processTryOnResult(tryOnResult);
  },
  
  // 检查试衣结果
  checkTryOnResult: function() {
    const tryOnResult = app.globalData.tryOnResult;
    
    if (!tryOnResult) {
      // 如果3次检查后仍然没有结果，显示错误
      if (this.checkCount >= 3) {
        this.setData({
          loading: false,
          error: '试衣结果加载失败，请重试'
        });
        return;
      }
      
      // 增加检查次数
      this.checkCount = (this.checkCount || 0) + 1;
      
      // 继续等待并再次检查
      setTimeout(() => {
        this.checkTryOnResult();
      }, 1500);
      
      return;
    }
    
    // 找到结果数据，进行处理
    this.processTryOnResult(tryOnResult);
  },
  
  // 处理试衣结果数据
  processTryOnResult: function(tryOnResult) {
    if (tryOnResult.result_image_url) {
      // 检查是否有警告消息需要显示
      if (tryOnResult.warning) {
        wx.showModal({
          title: '提示',
          content: '图片加载可能存在问题，建议稍后再尝试保存图片',
          showCancel: false
        });
      }
      
      // 检查图片URL是否为阿里云OSS链接
      const isAliyunOssUrl = tryOnResult.result_image_url.indexOf('aliyuncs.com') !== -1;
      
      // 如果是阿里云OSS链接，提示用户并尝试预加载图片
      if (isAliyunOssUrl) {
        wx.showLoading({
          title: '正在加载OSS图片...',
        });
        
        // 尝试预加载图片
        this.preloadImage(tryOnResult.result_image_url).then(success => {
          wx.hideLoading();
          if (!success) {
            wx.showToast({
              title: '图片可能无法正常显示',
              icon: 'none',
              duration: 2000
            });
          }
        });
      }
      
      // 检查是否有衣物
      let clothes = tryOnResult.clothes || [];
      // 如果衣物数据在data字段中
      if (!clothes.length && tryOnResult.data && Array.isArray(tryOnResult.data) && tryOnResult.data.length) {
        clothes = tryOnResult.data;
        console.log('从data字段获取衣物数据:', clothes);
      }
      
      const hasClothes = clothes && clothes.length > 0;
      
      this.setData({
        resultImage: tryOnResult.result_image_url,
        clothes: clothes,
        loading: false,
        isAliyunOss: isAliyunOssUrl
      });
      
      // 如果有衣物，默认选中第一件并检查是否显示描述和添加时间
      if (hasClothes) {
        const firstItem = clothes[0];
        this.setData({
          activeClothesId: firstItem.id
        });
        
        // 更新描述和添加时间的显示状态
        this.updateDetailDisplayStatus(firstItem);
      }
    } else {
      this.setData({
        loading: false,
        error: '未找到试衣结果数据'
      });
    }
  },
  
  // 更新详情显示状态
  updateDetailDisplayStatus: function(clothesItem) {
    if (!clothesItem) return;
    
    // 检查是否有描述内容
    let hasDescription = false;
    let descriptionObj = null;
    
    // 尝试解析description字段，可能是JSON字符串
    if (clothesItem.description && clothesItem.description.trim() !== '') {
      try {
        descriptionObj = JSON.parse(clothesItem.description);
        // 如果成功解析为对象，检查是否有内容
        hasDescription = (descriptionObj && (descriptionObj.color || descriptionObj.brand || descriptionObj.price || descriptionObj.material));
        console.log('解析description成功:', descriptionObj);
      } catch (e) {
        // 不是JSON格式，当作普通文本对待
        hasDescription = true;
        descriptionObj = { text: clothesItem.description };
        console.log('description不是JSON格式:', clothesItem.description);
      }
    }
    
    // 检查是否有添加时间 (检查created_at字段，而不是add_time)
    const hasAddTime = clothesItem.created_at && clothesItem.created_at.trim() !== '';
    
    console.log('衣物详情状态:', {
      id: clothesItem.id,
      hasDescription: hasDescription,
      hasAddTime: hasAddTime,
      description: clothesItem.description,
      created_at: clothesItem.created_at
    });
    
    this.setData({
      showDescription: hasDescription,
      showAddTime: hasAddTime,
      parsedDescription: descriptionObj
    });
  },
  
  // 切换选中的衣物
  switchClothesDetail: function(e) {
    const clothesId = e.currentTarget.dataset.id;
    
    // 找到对应的衣物项
    const selectedClothes = this.data.clothes.find(item => item.id === clothesId);
    
    this.setData({
      activeClothesId: clothesId
    });
    
    // 更新详情显示状态
    if (selectedClothes) {
      this.updateDetailDisplayStatus(selectedClothes);
    }
  },
  
  // 保存图片到相册
  saveImage: function() {
    if (!this.data.resultImage) {
      wx.showToast({
        title: '无法保存，图片未加载',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '处理中...',
    });
    
    // 使用官方的授权API
    wx.authorize({
      scope: 'scope.writePhotosAlbum',
      success: () => {
        // 用户已授权，继续下载并添加水印流程
        this.downloadAndAddWatermark();
      },
      fail: (err) => {
        wx.hideLoading();
        
        // 如果是用户拒绝授权
        if (err.errMsg.indexOf('auth deny') >= 0 || err.errMsg.indexOf('authorize:fail') >= 0) {
          // 引导用户打开设置页面
          wx.showModal({
            title: '需要授权',
            content: '保存图片需要您授权访问相册权限',
            confirmText: '去授权',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      // 用户在设置页面授权了，可以再次尝试保存
                      wx.showToast({
                        title: '授权成功，请再次点击保存',
                        icon: 'none',
                        duration: 2000
                      });
                    } else {
                      wx.showToast({
                        title: '您拒绝了授权，无法保存图片',
                        icon: 'none',
                        duration: 2000
                      });
                    }
                  }
                });
              }
            }
          });
        } else {
          wx.showToast({
            title: '授权失败，请重试',
            icon: 'none'
          });
        }
      }
    });
  },
  
  // 下载图片并添加水印
  downloadAndAddWatermark: function() {
    // 下载原始图片
    wx.downloadFile({
      url: this.data.resultImage,
      success: (res) => {
        // 检查服务器返回的状态码
        if (res.statusCode !== 200) {
          wx.hideLoading();
          wx.showToast({
            title: '下载图片失败，请重试',
            icon: 'none'
          });
          return;
        }
        
        // 图片下载成功，开始添加水印
        const tempFilePath = res.tempFilePath;
        this.addWatermarkToImage(tempFilePath);
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '下载图片失败，请重试',
          icon: 'none'
        });
        console.error('下载图片失败:', err);
      }
    });
  },
  
  // 添加水印到图片
  addWatermarkToImage: function(imagePath) {
    const that = this;
    
    // 获取Canvas节点
    wx.createSelectorQuery()
      .select('#watermarkCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        
        // 获取图片信息，以便设置Canvas大小
        wx.getImageInfo({
          src: imagePath,
          success: (imgInfo) => {
            // 设置Canvas的宽高与图片一致
            const canvasWidth = imgInfo.width;
            const canvasHeight = imgInfo.height;
            canvas.width = canvasWidth;
            canvas.height = canvasHeight;
            
            // 创建图片对象
            const image = canvas.createImage();
            image.onload = function() {
              // 清空Canvas
              ctx.clearRect(0, 0, canvasWidth, canvasHeight);
              
              // 绘制原始图片
              ctx.drawImage(image, 0, 0, canvasWidth, canvasHeight);
              
              // 添加水印
              // 设置水印样式
              ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
              ctx.font = `${Math.max(14, canvasWidth * 0.03)}px sans-serif`;
              ctx.textAlign = 'right';
              ctx.textBaseline = 'bottom';
              
              // 添加图标和文字
              const watermarkText = '✨ 次元衣帽间';
              const padding = canvasWidth * 0.05;
              ctx.fillText(watermarkText, canvasWidth - padding + 5, canvasHeight - padding + 5);
              
              // 导出带水印的图片
              wx.canvasToTempFilePath({
                canvas: canvas,
                success: (result) => {
                  // 保存带水印的图片到相册
                  wx.saveImageToPhotosAlbum({
                    filePath: result.tempFilePath,
                    success: () => {
                      wx.hideLoading();
                      wx.showToast({
                        title: '已保存到相册',
                        icon: 'success'
                      });
                    },
                    fail: (err) => {
                      wx.hideLoading();
                      console.error('保存图片失败:', err);
                      wx.showToast({
                        title: '保存图片失败，请重试',
                        icon: 'none'
                      });
                    }
                  });
                },
                fail: (err) => {
                  wx.hideLoading();
                  console.error('Canvas转图片失败:', err);
                  wx.showToast({
                    title: '处理图片失败，请重试',
                    icon: 'none'
                  });
                }
              });
            };
            
            // 加载图片
            image.src = imagePath;
          },
          fail: (err) => {
            wx.hideLoading();
            console.error('获取图片信息失败:', err);
            wx.showToast({
              title: '处理图片失败，请重试',
              icon: 'none'
            });
          }
        });
      });
  },
  
  // 预加载图片方法
  preloadImage: function(imageUrl) {
    return new Promise((resolve) => {
      // 使用downloadFile尝试预加载图片
      wx.downloadFile({
        url: imageUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(true);
          } else {
            console.error('预加载图片失败:', res.statusCode);
            resolve(false);
          }
        },
        fail: (err) => {
          console.error('预加载图片失败:', err);
          resolve(false);
        }
      });
      
      // 设置超时防止无限等待
      setTimeout(() => {
        resolve(false);
      }, 5000);
    });
  },
  
  // 预览大图
  previewImage: function() {
    wx.previewImage({
      urls: [this.data.resultImage],
      current: this.data.resultImage
    });
  },
  
  // 分享功能
  onShareAppMessage: function() {
    return {
      title: '我在次元衣帽间试穿了这件衣服!',
      path: '/pages/index/index',
      imageUrl: this.data.resultImage
    };
  },
  
  // 重新选择衣物
  retryClothing: function() {
    // 返回到选择模特页面，跳过进度页面
    wx.navigateBack({
      delta: 2  // 返回两页，跳过进度页面，直接到选择模特页面
    });
  },
  
  // 获取衣物分类名称
  getCategoryName: function(category) {
    const categoryMap = {
      'tops': '上衣',
      'pants': '裤子',
      'skirts': '裙子',
      'coats': '外套'
    };
    
    return categoryMap[category] || '其他';
  },
  
  // 格式化日期
  formatDate: function(dateStr) {
    if (!dateStr) return '';
    
    const date = new Date(dateStr);
    return `${date.getFullYear()}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}`;
  },
  
  // 格式化描述，去除特殊字符
  formatDescription: function(description) {
    if (!description) return '';
    
    // 去除HTML标签
    let formattedDesc = description.replace(/<[^>]*>/g, '');
    
    // 替换特殊字符
    formattedDesc = formattedDesc.replace(/&nbsp;/g, ' ');
    formattedDesc = formattedDesc.replace(/&lt;/g, '<');
    formattedDesc = formattedDesc.replace(/&gt;/g, '>');
    formattedDesc = formattedDesc.replace(/&amp;/g, '&');
    formattedDesc = formattedDesc.replace(/&quot;/g, '"');
    
    // 去除多余空格和换行
    formattedDesc = formattedDesc.replace(/\s+/g, ' ').trim();
    
    return formattedDesc;
  },
  
  // 删除试穿历史图片
  deleteImage: function() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张试穿效果图吗？删除后无法恢复。',
      confirmText: '删除',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          // 确认删除，调用删除API
          this.performDelete();
        }
      }
    });
  },

  // 执行删除操作
  performDelete: function() {
    // 显示加载中
    wx.showLoading({
      title: '删除中...',
    });
    
    // 从全局数据获取试衣结果
    const tryOnResult = app.globalData.tryOnResult;
    console.log('删除操作 - 试穿结果数据:', tryOnResult);
    
    // 如果没有任务ID或图片URL，则无法删除
    if (!tryOnResult || (!tryOnResult.task_id && !this.data.resultImage)) {
      wx.hideLoading();
      wx.showToast({
        title: '删除失败，缺少必要信息',
        icon: 'none'
      });
      console.error('删除失败 - 缺少必要信息:', { 
        hasResult: !!tryOnResult, 
        taskId: tryOnResult?.task_id, 
        imageUrl: this.data.resultImage 
      });
      return;
    }
    
    // 构建请求数据
    const requestData = {};
    if (tryOnResult.task_id) {
      requestData.task_id = tryOnResult.task_id;
    }
    if (this.data.resultImage) {
      requestData.image_url = this.data.resultImage;
    }
    
    console.log('删除请求参数:', {
      url: `${app.globalData.apiBaseUrl}/delete_try_on.php`,
      token: app.globalData.token,
      data: requestData
    });
    
    // 调用删除API
    wx.request({
      url: `${app.globalData.apiBaseUrl}/delete_try_on.php`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': app.globalData.token
      },
      data: requestData,
      success: (res) => {
        wx.hideLoading();
        console.log('删除API响应:', res);
        
        if (res.statusCode === 200 && !res.data.error) {
          // 删除成功
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
          
          // 短暂延迟后返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1000);
        } else {
          // 显示错误信息
          const errorMsg = res.data.msg || '删除失败，请重试';
          wx.showToast({
            title: errorMsg,
            icon: 'none'
          });
          console.error('删除失败 - 服务器返回错误:', res.data);
        }
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        console.error('删除试穿历史失败 - 网络错误:', err);
      }
    });
  },
  
  // 关闭预览
  closePreview: function() {
    this.setData({
      showPreview: false
    });
  }
}); 