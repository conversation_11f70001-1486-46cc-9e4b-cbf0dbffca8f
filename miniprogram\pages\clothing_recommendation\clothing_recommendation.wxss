/* 容器样式 */
.container {
  padding: 0;
  width: 100%;
  min-height: 100vh;
  background-color: #f6f7fb;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 未登录状态样式 */
.empty-state {
  width: 100%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30px;
  box-sizing: border-box;
}

.empty-icon {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
}

.empty-image {
  width: 100%;
  height: 100%;
}

.empty-text {
  font-size: 16px;
  color: #333;
  text-align: center;
  margin-bottom: 10px;
}

.text-sm {
  font-size: 14px;
  color: #999;
}

.login-btn {
  margin-top: 30px;
  background-color: #1aad19;
  color: #fff;
  border-radius: 30px;
  font-size: 16px;
  padding: 8px 30px;
}

/* 头部样式 */
.header {
  width: 100%;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  margin-bottom: 10px;
}

.header-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.header-desc {
  font-size: 14px;
  color: #999;
}

/* 衣物选择器样式 */
.clothes-selector {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  padding-bottom: 80px; /* 为底部按钮留出空间 */
}

/* 筛选条件样式 */
.filter-container {
  width: 100%;
  background-color: #fff;
  padding: 10px 0;
  margin-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.filter-scroll {
  width: 100%;
  white-space: nowrap;
  margin-bottom: 10px; /* 添加底部间距，分隔两个滚动区域 */
}

.filter-scroll:last-child {
  margin-bottom: 0; /* 最后一个滚动区域不需要底部间距 */
}

.filter-tabs {
  padding: 0 15px;
  display: inline-block;
}

.filter-tab {
  display: inline-block;
  padding: 6px 15px;
  margin-right: 10px;
  font-size: 14px;
  color: #666;
  border-radius: 16px;
  background-color: #f5f5f5;
  transition: all 0.3s;
}

.filter-tab.active {
  color: #fff;
  background-color: #000;
}

/* 衣物列表样式 */
.clothes-list {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  padding: 0 10px;
  box-sizing: border-box;
}

.clothes-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  width: 100%;
}

/* 四列网格布局 */
.clothes-grid.layout-mode-4 {
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

.clothes-item {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  aspect-ratio: 3/4;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  box-sizing: border-box;
}

/* 四列布局下的衣物项 */
.layout-mode-4 .clothes-item {
  aspect-ratio: 1/1.5;
}

.clothes-img {
  width: 90%;
  height: 90%;
  object-fit: contain;
  border-radius: 10px;
}

/* 四列布局下的图片尺寸 */
.layout-mode-4 .clothes-img {
  width: 85%;
  height: 75%;
}

/* 四列布局下选择图标的尺寸调整 */
.layout-mode-4 .select-indicator {
  width: 20px;
  height: 20px;
  top: 5px;
  right: 5px;
  font-size: 10px;
}

.clothes-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
  max-width: 100%;
}

/* 四列布局下衣物名称的样式调整 */
.layout-mode-4 .clothes-name {
  padding: 4px;
  font-size: 10px;
}

.select-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  background-color: #000;
  color: #fff;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.clothes-item.selected {
  border: 2px solid #000;
}

/* 空状态样式 */
.empty-clothes {
  width: 100%;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.empty-icon {
  font-size: 40px;
  margin-bottom: 10px;
}

/* 加载动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 刷新按钮动画 */
.refresh-btn.refreshing {
  position: relative;
  overflow: hidden;
}

.refresh-btn.refreshing::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: refreshing-animation 1.5s infinite;
}

@keyframes refreshing-animation {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 轻量级加载遮罩 */
.loading-overlay-light {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-overlay-light .loading-icon {
  width: 60px;
  height: 60px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

.loading-overlay-light .loading-text {
  font-size: 18px;
  color: #333;
  font-weight: 500;
}

/* 底部按钮样式 */
.bottom-button {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 46px;
  background-color: #cccccc;
  color: #fff;
  border-radius: 23px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.bottom-button.active {
  background-color: #000;
}

/* 基础衣物卡片样式 */
.base-clothing-card {
  background-color: #fff;
  border-radius: 12px;
  margin: 15px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.base-clothing-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
}

.base-clothing-content {
  display: flex;
  align-items: center;
}

.base-clothing-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  background-color: #f5f5f5;
  margin-right: 15px;
}

.base-clothing-info {
  flex: 1;
}

.base-clothing-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.base-clothing-category {
  font-size: 13px;
  color: #666;
  margin-bottom: 5px;
}

.base-clothing-desc, .base-clothing-tags {
  font-size: 12px;
  color: #999;
  margin-bottom: 3px;
}

/* 穿搭推荐卡片样式 */
.outfit-card {
  background-color: #fff;
  border-radius: 12px;
  margin: 15px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.outfit-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
}

.outfit-item {
  display: flex;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.outfit-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.outfit-item-image-container {
  width: 70px;
  height: 70px;
  border-radius: 8px;
  background-color: #f5f5f5;
  margin-right: 15px;
  overflow: hidden;
  flex-shrink: 0;
}

.outfit-item-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.outfit-item-info {
  flex: 1;
}

.outfit-item-category {
  font-size: 12px;
  color: #999;
  margin-bottom: 3px;
}

.outfit-item-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.outfit-item-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.outfit-item-reason {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

/* 穿搭总结样式 */
.outfit-summary {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.summary-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.summary-content {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  padding: 15px;
  width: 100%;
  box-sizing: border-box;
}

.action-btn {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  background-color: #fff;
  border: 1px solid #ddd;
  margin: 0 5px;
}

.refresh-btn {
  color: #333;
}

.save-btn {
  background-color: #000;
  color: #fff;
  border: none;
}

.refreshing {
  opacity: 0.7;
}

/* 返回按钮样式 */
.back-button {
  width: 100%;
  padding: 12px 0;
  text-align: center;
  font-size: 14px;
  color: #666;
  margin-top: 10px;
}

/* 穿搭推荐提示样式 */
.recommendation-tips {
  padding: 15px;
  font-size: 12px;
  color: #999;
  line-height: 1.6;
}

.tip-item {
  margin-bottom: 5px;
}

/* 形象分析卡片样式 */
.analysis-card {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.section-content {
  padding: 5px 0;
}

.analysis-item {
  margin-bottom: 15px;
}

.analysis-item:last-child {
  margin-bottom: 0;
}

.item-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
  font-weight: 500;
}

.item-value {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.item-list {
  margin-left: 10px;
}

.list-item {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 5px;
  position: relative;
  padding-left: 15px;
}

.list-item:before {
  content: "•";
  position: absolute;
  left: 0;
  color: #000;
}

.empty-analysis {
  padding: 15px 0;
  text-align: center;
  color: #999;
  font-size: 14px;
}

/* 分享提示弹窗样式 */
.share-tip-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.share-tip-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.share-tip-content {
  position: relative;
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 50rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

.share-tip-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.share-tip-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.share-tip-message {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
  line-height: 1.6;
}

.share-tip-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.share-tip-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0 15rpx;
  padding: 0;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.share-btn {
  background-color: #000;
  color: #fff;
}

/* 剩余次数提示样式 */
.quota-tip {
  text-align: center;
  font-size: 26rpx;
  color: #999;
  margin-top: 20rpx;
  margin-bottom: 10rpx;
}

.quota-number {
  color: #f04142;
  font-weight: 600;
  margin: 0 4rpx;
}