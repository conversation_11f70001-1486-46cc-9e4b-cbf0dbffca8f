# 服务器安全防护配置教程

## 目录
- [前言](#前言)
- [一、Fail2ban安装与配置](#一fail2ban安装与配置)
- [二、防火墙(firewalld)配置](#二防火墙firewalld配置)
- [三、验证配置是否生效](#三验证配置是否生效)
- [四、安全维护与监控](#四安全维护与监控)

## 前言

服务器面临各种网络攻击威胁，特别是暴力破解和CC攻击。本教程将帮助配置两层防护：
- Fail2ban：自动检测并封禁尝试暴力破解的IP
- 防火墙：控制端口访问权限和连接限制

## 一、Fail2ban安装与配置

### 1.1 安装Fail2ban

```bash
# 安装Fail2ban
yum install fail2ban -y
```

### 1.2 创建SSH防护配置

```bash
# 创建配置文件
cp -f /etc/fail2ban/jail.conf /etc/fail2ban/jail.local

# 创建基础配置
cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
# 封禁时间（1小时）
bantime = 3600
# 检测时间范围（10分钟）
findtime = 600
# 允许失败次数
maxretry = 5
# 忽略的IP地址
ignoreip = 127.0.0.1/8 ::1

# SSH防护设置
[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/secure
maxretry = 3
EOF
```

### 1.3 添加MySQL防护

```bash
# 在配置文件末尾添加MySQL防护设置
cat >> /etc/fail2ban/jail.local << EOF

# MySQL防护设置
[mysqld-auth]
enabled = true
filter = mysqld-auth
port = 3306
logpath = /www/server/data/mysql-error.log
maxretry = 3
EOF

# 创建MySQL过滤规则文件
cat > /etc/fail2ban/filter.d/mysqld-auth.conf << EOF
[Definition]
# MySQL登录失败检测规则
failregex = ^.*Access denied for user '.*'@'<HOST>'.*$
ignoreregex =
EOF
```

> 提示：MySQL日志路径可能因安装方式不同而异，宝塔面板通常是`/www/server/data/mysql-error.log`或`/www/server/data/mysql-slow.log`

### 1.4 启动Fail2ban服务

```bash
# 设置开机自启动
systemctl enable fail2ban

# 启动服务
systemctl start fail2ban

# 检查服务状态
systemctl status fail2ban
```

## 二、防火墙(firewalld)配置

### 2.1 启用防火墙

```bash
# 检查防火墙状态
systemctl status firewalld

# 启动防火墙并设置开机自启
systemctl start firewalld
systemctl enable firewalld
```

### 2.2 配置基本访问规则

```bash
# 允许SSH服务
firewall-cmd --permanent --zone=public --add-service=ssh

# 允许Web服务
firewall-cmd --permanent --zone=public --add-service=http
firewall-cmd --permanent --zone=public --add-service=https
```

### 2.3 保护MySQL端口

```bash
# 查看自己的IP地址
curl ipinfo.io/ip

# 只允许特定IP访问MySQL（替换为自己的IP）
firewall-cmd --permanent --zone=public --add-rich-rule='rule family="ipv4" source address="您的IP地址/32" port protocol="tcp" port="3306" accept'

# 拒绝其他IP访问MySQL
firewall-cmd --permanent --zone=public --add-rich-rule='rule family="ipv4" port port="3306" protocol="tcp" reject'
```

### 2.4 添加防CC攻击规则

```bash
# 限制单IP对80端口的并发连接数
firewall-cmd --permanent --direct --add-rule ipv4 filter INPUT_direct 0 -p tcp --syn --dport 80 -m connlimit --connlimit-above 20 --connlimit-mask 32 -j REJECT --reject-with tcp-reset

# 限制单IP对443端口的并发连接数
firewall-cmd --permanent --direct --add-rule ipv4 filter INPUT_direct 1 -p tcp --syn --dport 443 -m connlimit --connlimit-above 20 --connlimit-mask 32 -j REJECT --reject-with tcp-reset
```

### 2.5 应用防火墙配置

```bash
# 重新加载防火墙配置
firewall-cmd --reload
```

## 三、验证配置是否生效

### 3.1 检查Fail2ban状态

```bash
# 查看所有防护
fail2ban-client status

# 查看SSH防护详情
fail2ban-client status sshd

# 查看MySQL防护详情
fail2ban-client status mysqld-auth
```

### 3.2 检查防火墙规则

```bash
# 查看所有防火墙规则
firewall-cmd --list-all

# 查看直接规则（连接限制规则）
firewall-cmd --direct --get-all-rules
```

## 四、安全维护与监控

### 4.1 常见命令

```bash
# 查看被Fail2ban封禁的IP
fail2ban-client status sshd
fail2ban-client status mysqld-auth

# 手动解封IP
fail2ban-client set sshd unbanip IP地址
fail2ban-client set mysqld-auth unbanip IP地址

# 检查防火墙日志
journalctl -f -u firewalld
```

### 4.2 注意事项

1. **保持日志监控**：定期查看`/var/log/secure`和MySQL日志文件，观察是否有异常登录尝试
2. **定期更新密码**：定期更改SSH和MySQL的密码，使用强密码策略
3. **更新规则**：根据服务器情况，调整Fail2ban封禁时间和失败次数阈值
4. **日志轮转**：确保日志文件不会无限增长，可能导致存储空间不足

### 4.3 故障排查

如果无法SSH登录系统，可能是被Fail2ban误封，可以：
1. 通过控制台登录（阿里云控制台远程连接）
2. 临时停止Fail2ban：`systemctl stop fail2ban`
3. 查看封禁列表并解除自己的IP

---

配置完成后，您的服务器已具备基本的安全防护能力，可以有效抵御暴力破解和CC攻击。根据实际需求，可以进一步优化规则和参数。
