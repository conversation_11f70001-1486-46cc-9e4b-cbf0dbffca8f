-- Add wardrobe_id column to clothes table
ALTER TABLE `clothes` ADD COLUMN `wardrobe_id` int(11) DEFAULT NULL AFTER `user_id`;

-- Add foreign key constraint
ALTER TABLE `clothes` ADD CONSTRAINT `clothes_wardrobe_id_fk` 
  FOREIGN KEY (`wardrobe_id`) REFERENCES `wardrobes` (`id`) 
  ON DELETE SET NULL;

-- Create index on wardrobe_id for faster lookups
CREATE INDEX idx_wardrobe_id ON clothes(wardrobe_id); 