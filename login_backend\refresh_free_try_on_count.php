<?php
/**
 * 刷新免费试衣次数脚本
 * 
 * 此脚本用于定时任务（计划任务/cron job），每日自动刷新所有用户的免费试衣次数
 * 建议设置为每日凌晨执行
 * 
 * 用法：
 * 1. 命令行: php refresh_free_try_on_count.php
 * 2. cron任务示例: 0 0 * * * /usr/bin/php /path/to/refresh_free_try_on_count.php >> /var/log/cron_try_on_refresh.log 2>&1
 */

// 设置执行时间，避免超时
set_time_limit(600); // 10分钟超时

require_once 'config.php';
require_once 'db.php';

// 定义日志函数
function log_message($message) {
    // 输出到控制台
    echo date('Y-m-d H:i:s') . " - $message\n";
    
    // 同时写入日志文件
    $log_dir = __DIR__ . '/../logs/';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $log_file = $log_dir . 'try_on_refresh_' . date('Y-m-d') . '.log';
    file_put_contents(
        $log_file,
        date('Y-m-d H:i:s') . " - $message\n",
        FILE_APPEND | LOCK_EX
    );
}

log_message("开始刷新免费试衣次数...");

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 获取默认的免费试衣次数
    $defaultFreeCount = defined('DEFAULT_FREE_TRY_ON_COUNT') ? DEFAULT_FREE_TRY_ON_COUNT : 1;
    
    log_message("使用默认免费试衣次数: $defaultFreeCount");
    
    // 更新所有用户的免费试衣次数
    $stmt = $conn->prepare("UPDATE users SET free_try_on_count = :free_count");
    $stmt->execute(['free_count' => $defaultFreeCount]);
    
    $affectedRows = $stmt->rowCount();
    log_message("成功更新了 $affectedRows 个用户的免费试衣次数");
    
    // 记录操作日志
    try {
        $logStmt = $conn->prepare("
            INSERT INTO system_log (
                log_type, 
                action, 
                details, 
                created_at
            ) VALUES (
                'system',
                'refresh_free_try_on_count',
                :details,
                NOW()
            )
        ");
        
        $logStmt->execute([
            'details' => json_encode([
                'affected_users' => $affectedRows,
                'free_count_value' => $defaultFreeCount
            ])
        ]);
        
        log_message("操作已记录到系统日志");
    } catch (PDOException $e) {
        // 系统日志表可能不存在，忽略错误
        log_message("无法记录到系统日志: {$e->getMessage()}");
    }
    
    log_message("免费试衣次数刷新完成");
    
} catch (PDOException $e) {
    log_message("错误: {$e->getMessage()}");
    exit(1);
}

exit(0);