<?php
// 引入必要的文件
require_once 'auth.php';
require_once 'db.php';
require_once 'config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 处理GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'error' => true,
        'msg' => '不支持的请求方法'
    ]);
    exit;
}

// 验证参数
if (!isset($_GET['outfit_id'])) {
    echo json_encode([
        'error' => true,
        'msg' => '缺少穿搭ID参数'
    ]);
    exit;
}

$outfitId = intval($_GET['outfit_id']);

// 验证token获取用户ID
$auth = new Auth();

// 获取token
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 如果有Bearer前缀，去掉它
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
}

if (!$token) {
    echo json_encode([
        'success' => true,
        'is_liked' => false
    ]);
    exit;
}

// 使用Auth类验证token
$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'success' => true,
        'is_liked' => false
    ]);
    exit;
}

$userId = $payload['sub']; // 从payload中获取用户ID

// 检查是否为体验账号
$isMockUser = (strpos($token, 'mock_') === 0) || 
              (isset($payload['openid']) && $payload['openid'] === 'mock_openid');

$isLiked = false;

try {
    // 体验账号从会话中获取点赞状态
    if ($isMockUser) {
        // 启动会话
        $sessionId = session_id();
        if (empty($sessionId)) {
            session_start();
            $sessionId = session_id();
        }
        
        // 检查会话中是否有该点赞记录
        if (isset($_SESSION['mock_likes']["outfit_{$outfitId}"])) {
            $isLiked = $_SESSION['mock_likes']["outfit_{$outfitId}"] === true;
        }
    } else {
        // 正常用户从数据库查询
        $db = new Database();
        $conn = $db->getConnection();
        
        // 检查用户是否已点赞
        $stmt = $conn->prepare("SELECT COUNT(*) FROM outfit_likes WHERE user_id = :user_id AND outfit_id = :outfit_id");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindParam(':outfit_id', $outfitId, PDO::PARAM_INT);
        $stmt->execute();
        
        $isLiked = $stmt->fetchColumn() > 0;
    }
    
    // 返回结果
    echo json_encode([
        'success' => true,
        'is_liked' => $isLiked
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '检查点赞状态失败: ' . $e->getMessage()
    ]);
} 