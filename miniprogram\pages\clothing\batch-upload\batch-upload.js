const app = getApp();

Page({
  data: {
    selectedImages: [], // 选中的图片列表
    uploadStatus: 'waiting', // waiting, uploading, analyzing, complete
    uploadResults: {
      total: 0,
      success: 0,
      failed: 0
    },
    clothingItems: [], // 识别后的衣物数据列表
    categories: [ // 预设的类别选项
      { name: '上衣', value: 'tops' },
      { name: '裤子', value: 'pants' },
      { name: '裙子', value: 'skirts' },
      { name: '外套', value: 'coats' },
      { name: '鞋子', value: 'shoes' },
      { name: '包包', value: 'bags' },
      { name: '配饰', value: 'accessories' }
    ],
    tags: [ // 预设的标签选项
      { name: '春季', value: 'spring' },
      { name: '夏季', value: 'summer' },
      { name: '秋季', value: 'autumn' },
      { name: '冬季', value: 'winter' },
      { name: '休闲', value: 'casual' },
      { name: '通勤', value: 'work' },
      { name: '派对', value: 'party' },
      { name: '运动', value: 'sport' }
    ],
    defaultWardrobeId: null, // 默认衣橱ID
    wardrobeList: [], // 衣橱列表
    selectedWardrobeName: '默认衣橱', // 选中的衣橱名称
    currentEditIndex: -1 // 当前编辑的衣物索引
  },

  onLoad: function() {
    // 检查是否登录
    if (!app.globalData.token) {
      wx.redirectTo({
        url: '/pages/login/login'
      });
      return;
    }
    
    // 获取衣橱列表
    this.getWardrobeList();
    
    // 选择图片
    this.chooseImages();
  },
  
  // 获取衣橱列表
  getWardrobeList: function() {
    wx.showLoading({
      title: '加载衣橱...',
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_wardrobes.php`,
      method: 'GET',
      header: {
        'content-type': 'application/json',
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.statusCode === 200 && res.data.success) {
          console.log('获取衣橱列表成功:', res.data);
          const wardrobeList = res.data.data || [];
          
          // 查找默认衣橱
          let defaultWardrobe = wardrobeList.find(w => w.is_default == 1);
          let defaultWardrobeId = defaultWardrobe ? defaultWardrobe.id : null;
          
          this.setData({
            wardrobeList: wardrobeList,
            defaultWardrobeId: defaultWardrobeId
          });
          
          // 设置选中的衣橱名称
          this.updateSelectedWardrobeName();
        } else {
          console.error('获取衣橱列表失败:', res);
          wx.showToast({
            title: '获取衣橱列表失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 选择图片
  chooseImages: function() {
    wx.chooseMedia({
      count: 9, // 最多可以选择9张图片
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      camera: 'back',
      success: (res) => {
        console.log('选择图片成功:', res);
        
        const tempFiles = res.tempFiles;
        
        // 如果没有选择图片，返回上一页
        if (tempFiles.length === 0) {
          wx.navigateBack();
          return;
        }
        
        // 准备选中的图片列表
        const selectedImages = tempFiles.map(file => {
          return {
            path: file.tempFilePath,
            size: file.size
          };
        });
        
        this.setData({
          selectedImages: selectedImages,
          uploadStatus: 'waiting',
          'uploadResults.total': selectedImages.length
        });
        
        // 开始处理上传
        this.processImages();
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        
        // 如果用户取消了选择，或出现错误，返回上一页
        wx.navigateBack();
      }
    });
  },
  
  // 处理图片上传和分析
  processImages: function() {
    const images = this.data.selectedImages;
    
    if (images.length === 0) {
      wx.showToast({
        title: '请先选择图片',
        icon: 'none'
      });
      return;
    }
    
    // 更新状态为上传中
    this.setData({
      uploadStatus: 'uploading'
    });
    
    // 准备衣物数据数组
    const clothingItems = [];
    
    // 显示加载提示
    wx.showLoading({
      title: '正在处理图片...',
      mask: true
    });
    
    // 使用Promise.all处理多张图片
    const uploadPromises = images.map((image, index) => {
      return new Promise((resolve) => {
        // 先上传图片
        this.uploadImage(image.path, (imageUrl, error) => {
          if (error) {
            console.error(`图片${index+1}上传失败:`, error);
            resolve({
              success: false,
              error: error
            });
            return;
          }
          
          // 图片上传成功，进行AI分析
          this.analyzeImage(image.path, (result, error) => {
            if (error) {
              console.error(`图片${index+1}分析失败:`, error);
              
              // AI分析失败，提供默认模拟数据继续流程
              console.log(`使用默认模拟数据替代AI分析结果`);
              result = {
                "衣物类别": index % 2 === 0 ? "上衣" : "裤子",
                "衣物标签": ["春季", "休闲", "基础款", "日常", "简约", "百搭", "舒适"],
                "衣物信息": {
                  "衣物名称": `默认衣物${index+1}`,
                  "颜色": index % 3 === 0 ? "蓝色" : (index % 3 === 1 ? "黑色" : "白色")
                }
              };
              
              // 创建衣物项
              const category = this.mapCategoryToValue(result.衣物类别 || '');
              const tags = result.衣物标签 || [];
              const name = result.衣物信息?.衣物名称 || `未命名衣物${index+1}`;
              const color = result.衣物信息?.颜色 || '';
              
              // 转换标签为selectedTags对象格式
              const selectedTags = {};
              const existingTags = this.data.tags.map(t => t.value);
              const customTags = [];
              
              // 处理标签
              tags.forEach(tag => {
                const tagValue = this.mapTagToValue(tag);
                
                // 如果是预设标签，直接选中
                if (existingTags.includes(tagValue)) {
                  selectedTags[tagValue] = true;
                } else {
                  // 否则创建自定义标签
                  const customTagId = 'custom_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5);
                  selectedTags[customTagId] = true;
                  customTags.push({
                    name: tag,
                    value: customTagId
                  });
                }
              });
              
              clothingItems.push({
                imageUrl: imageUrl,
                name: name,
                category: category,
                tags: tags,
                customTags: customTags,
                selectedTags: selectedTags,
                wardrobeId: this.data.defaultWardrobeId,
                wardrobeName: this.data.selectedWardrobeName,
                color: color,
                brand: '',
                price: '',
                description: ''
              });
              
              resolve({
                success: true,
                analyzed: true,
                imageUrl: imageUrl
              });
            } else {
              console.log(`图片${index+1}分析成功:`, result);
              
              // 解析AI返回的结果
              const category = this.mapCategoryToValue(result.衣物类别 || '');
              const tags = result.衣物标签 || [];
              const name = result.衣物信息?.衣物名称 || `未命名衣物${index+1}`;
              const color = result.衣物信息?.颜色 || '';
              
              // 转换标签为selectedTags对象格式
              const selectedTags = {};
              const existingTags = this.data.tags.map(t => t.value);
              const customTags = [];
              
              // 处理标签
              tags.forEach(tag => {
                const tagValue = this.mapTagToValue(tag);
                
                // 如果是预设标签，直接选中
                if (existingTags.includes(tagValue)) {
                  selectedTags[tagValue] = true;
                } else {
                  // 否则创建自定义标签
                  const customTagId = 'custom_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5);
                  selectedTags[customTagId] = true;
                  customTags.push({
                    name: tag,
                    value: customTagId
                  });
                }
              });
              
              // 创建衣物项
              clothingItems.push({
                imageUrl: imageUrl,
                name: name,
                category: category,
                tags: tags,
                customTags: customTags,
                selectedTags: selectedTags,
                wardrobeId: this.data.defaultWardrobeId,
                wardrobeName: this.data.selectedWardrobeName,
                color: color,
                brand: '',
                price: '',
                description: ''
              });
              
              resolve({
                success: true,
                analyzed: true,
                imageUrl: imageUrl
              });
            }
          });
        });
      });
    });
    
    // 处理所有上传和分析结果
    Promise.all(uploadPromises)
      .then(results => {
        // 隐藏加载提示
        wx.hideLoading();
        
        // 统计成功和失败数量
        const success = results.filter(r => r.success).length;
        const failed = results.length - success;
        
        // 更新界面
        this.setData({
          clothingItems: clothingItems,
          uploadStatus: 'complete',
          uploadResults: {
            total: results.length,
            success: success,
            failed: failed
          }
        });
        
        console.log('所有图片处理完成:', this.data.clothingItems);
      })
      .catch(error => {
        wx.hideLoading();
        console.error('处理图片出错:', error);
        
        wx.showToast({
          title: '处理图片出错',
          icon: 'none'
        });
      });
  },
  
  // 上传图片到服务器
  uploadImage: function(filePath, callback) {
    // 生成符合JWT格式的token
    const token = app.globalData.token;
    
    wx.uploadFile({
      url: `${app.globalData.apiBaseUrl}/upload_image.php`,
      filePath: filePath,
      name: 'image',
      header: {
        'Authorization': token
      },
      formData: {
        segment_image: 'false'
      },
      success: (res) => {
        console.log('上传图片响应:', res);
        try {
          const data = JSON.parse(res.data);
          
          if (data.error === false) {
            // 上传成功，获取图片URL
            const imageUrl = data.data.image_url;
            console.log('上传成功，图片URL:', imageUrl);
            
            callback(imageUrl);
          } else {
            // 上传失败
            callback(null, data.msg || '图片上传失败');
          }
        } catch (e) {
          console.error('解析响应失败:', e, '原始响应:', res.data);
          callback(null, '解析响应失败');
        }
      },
      fail: (err) => {
        console.error('上传请求失败:', err);
        callback(null, '网络错误，请稍后重试');
      }
    });
  },
  
  // 使用Gemini API分析图片
  analyzeImage: function(imagePath, callback) {
    console.log('开始分析图片:', imagePath);
    // 读取图片文件
    wx.getFileSystemManager().readFile({
      filePath: imagePath,
      success: (res) => {
        // 将图片数据转为base64
        const base64Image = wx.arrayBufferToBase64(res.data);
        console.log('图片转base64成功，长度:', base64Image.length);
        
        // 准备提示词 - 为简化处理，我们使用默认提示词
        const prompt = "识别图片中的衣物，并严格按照以下JSON格式返回结果，不要有任何额外的文本：\n" +
                       "{\n" +
                       '  "衣物类别": "上衣、裤子、裙子、外套、鞋子、包包、配饰中的一个",\n' +
                       '  "衣物标签": ["春季/夏季/秋季/冬季", "休闲/通勤/派对/运动", "标签1", "标签2", "标签3", "标签4", "标签5"],\n' +
                       '  "衣物信息": {\n' +
                       '    "衣物名称": "名称",\n' +
                       '    "颜色": "颜色"\n' +
                       '  }\n' +
                       "}";
        
        // 设置请求参数和headers
        const requestUrl = app.globalData.apiBaseUrl + '/analyze_clothing.php';
        console.log('发送分析请求到:', requestUrl);

        // 打印请求数据（截断base64以免日志过长）
        const base64Preview = base64Image.substring(0, 50) + '...' + base64Image.substring(base64Image.length - 50);
        console.log('请求数据:', {
          image_base64_length: base64Image.length,
          image_base64_preview: base64Preview,
          prompt: prompt
        });
        
        wx.request({
          url: requestUrl,
          method: 'POST',
          data: {
            image_base64: base64Image,
            prompt: prompt
          },
          header: {
            'content-type': 'application/json',
            'Authorization': app.globalData.token
          },
          success: (res) => {
            console.log('分析响应状态码:', res.statusCode);
            console.log('分析响应完整数据:', JSON.stringify(res.data));
            
            // 检查响应是否为字符串（需要解析）
            let responseData = res.data;
            if (typeof responseData === 'string') {
              try {
                // 尝试作为JSON解析
                console.log('尝试解析响应字符串...');
                
                // 检查是否包含多个JSON对象（解决连接问题）
                if (responseData.indexOf('}{') !== -1) {
                  console.log('检测到多个JSON对象连接情况，尝试提取最后一个...');
                  const lastJsonStartPos = responseData.lastIndexOf('{');
                  responseData = responseData.substring(lastJsonStartPos);
                }
                
                responseData = JSON.parse(responseData);
                console.log('解析后的数据:', responseData);
              } catch (e) {
                console.error('解析响应数据失败:', e);
                callback(null, '解析响应数据失败');
                return;
              }
            }
            
            // 增强判断条件，服务器端返回的success字段应该是布尔值true而非字符串
            if (res.statusCode === 200 && responseData.success === true) {
              // 分析成功
              console.log('分析成功，返回数据:', JSON.stringify(responseData.data));
              callback(responseData.data);
            } else {
              // 分析失败
              console.error('分析失败, 错误信息:', responseData.msg);
              if (responseData.raw_text) {
                console.log('AI返回的原始文本:', responseData.raw_text);
              }
              callback(null, responseData.msg || '分析失败');
            }
          },
          fail: (err) => {
            console.error('分析请求失败:', err);
            callback(null, '网络错误，请稍后重试');
          }
        });
      },
      fail: (err) => {
        console.error('读取图片文件失败:', err);
        callback(null, '读取图片文件失败');
      }
    });
  },
  
  // 映射类别名称到类别值
  mapCategoryToValue: function(categoryName) {
    const categoryMap = {
      '上衣': 'tops',
      '裤子': 'pants',
      '裙子': 'skirts',
      '外套': 'coats',
      '鞋子': 'shoes',
      '包包': 'bags',
      '配饰': 'accessories'
    };
    
    return categoryMap[categoryName] || '';
  },
  
  // 映射标签名称到标签值
  mapTagToValue: function(tagName) {
    const tagMap = {
      '春季': 'spring',
      '夏季': 'summer',
      '秋季': 'autumn',
      '冬季': 'winter',
      '休闲': 'casual',
      '通勤': 'work',
      '派对': 'party',
      '运动': 'sport'
    };
    
    return tagMap[tagName] || tagName.toLowerCase().replace(/\s/g, '_');
  },
  
  // 编辑衣物项
  editItem: function(e) {
    const index = e.currentTarget.dataset.index;
    
    this.setData({
      currentEditIndex: index
    });
  },
  
  // 更新衣物名称
  updateItemName: function(e) {
    const index = this.data.currentEditIndex;
    const name = e.detail.value;
    
    if (index >= 0) {
      const key = `clothingItems[${index}].name`;
      this.setData({
        [key]: name
      });
    }
  },
  
  // 更新衣物类别
  updateItemCategory: function(e) {
    const index = this.data.currentEditIndex;
    const category = e.currentTarget.dataset.value;
    
    if (index >= 0) {
      const key = `clothingItems[${index}].category`;
      this.setData({
        [key]: category
      });
    }
  },
  
  // 切换标签选择状态
  toggleTag: function(e) {
    const index = this.data.currentEditIndex;
    const value = e.currentTarget.dataset.value;
    
    if (index >= 0) {
      // 获取当前选中的标签
      const currentTags = this.data.clothingItems[index].selectedTags || {};
      const newTags = { ...currentTags };
      
      if (newTags[value]) {
        delete newTags[value];
      } else {
        newTags[value] = true;
      }
      
      const key = `clothingItems[${index}].selectedTags`;
      this.setData({
        [key]: newTags
      });
    }
  },
  
  // 更新颜色
  updateItemColor: function(e) {
    const index = this.data.currentEditIndex;
    const color = e.detail.value;
    
    if (index >= 0) {
      const key = `clothingItems[${index}].color`;
      this.setData({
        [key]: color
      });
    }
  },
  
  // 更新品牌
  updateItemBrand: function(e) {
    const index = this.data.currentEditIndex;
    const brand = e.detail.value;
    
    if (index >= 0) {
      const key = `clothingItems[${index}].brand`;
      this.setData({
        [key]: brand
      });
    }
  },
  
  // 更新价格
  updateItemPrice: function(e) {
    const index = this.data.currentEditIndex;
    const price = e.detail.value;
    
    if (index >= 0) {
      const key = `clothingItems[${index}].price`;
      this.setData({
        [key]: price
      });
    }
  },
  
  // 完成编辑
  finishEdit: function() {
    this.setData({
      currentEditIndex: -1
    });
  },
  
  // 保存所有衣物
  saveAllItems: function() {
    const items = this.data.clothingItems;
    
    if (items.length === 0) {
      wx.showToast({
        title: '没有衣物可保存',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '保存中...',
      mask: true
    });
    
    // 记录保存结果
    let successCount = 0;
    let failCount = 0;
    
    // 使用Promise.all处理多个保存请求
    const savePromises = items.map((item, index) => {
      return new Promise((resolve) => {
        // 获取当前选中的标签列表
        const selectedTagKeys = Object.keys(item.selectedTags || {});
        const allTags = [...this.data.tags, ...(item.customTags || [])];
        const selectedTagNames = allTags
          .filter(tag => selectedTagKeys.includes(tag.value))
          .map(tag => tag.name);
        
        // 构建请求数据
        const requestData = {
          name: item.name || '未命名衣物',
          category: item.category || 'tops',
          image_url: item.imageUrl,
          tags: selectedTagNames.join(','),
          description: JSON.stringify({
            color: item.color || '',
            brand: item.brand || '',
            price: item.price || ''
          }),
          wardrobe_id: item.wardrobeId || this.data.defaultWardrobeId
        };
        
        // 发送添加衣物请求
        wx.request({
          url: `${app.globalData.apiBaseUrl}/add_clothing.php`,
          method: 'POST',
          header: {
            'Authorization': app.globalData.token,
            'Content-Type': 'application/json'
          },
          data: requestData,
          success: (res) => {
            if (res.statusCode === 200 && !res.data.error) {
              successCount++;
              console.log(`衣物${index+1}保存成功:`, res.data);
              resolve(true);
            } else {
              failCount++;
              console.error(`衣物${index+1}保存失败:`, res.data.msg || '未知错误');
              resolve(false);
            }
          },
          fail: (err) => {
            failCount++;
            console.error(`衣物${index+1}保存请求失败:`, err);
            resolve(false);
          }
        });
      });
    });
    
    // 处理所有保存结果
    Promise.all(savePromises)
      .then(() => {
        wx.hideLoading();
        
        // 标记需要刷新衣物列表
        getApp().globalData.needRefreshClothes = true;
        
        // 显示结果提示
        wx.showModal({
          title: '保存结果',
          content: `共${items.length}件衣物，成功保存${successCount}件，失败${failCount}件`,
          showCancel: false,
          success: () => {
            // 返回上一页
            wx.navigateBack();
          }
        });
      })
      .catch(error => {
        wx.hideLoading();
        console.error('保存衣物出错:', error);
        
        wx.showToast({
          title: '保存出错，请重试',
          icon: 'none'
        });
      });
  },
  
  // 重选图片
  reChooseImages: function() {
    this.chooseImages();
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },
  
  // 更新选中的衣橱名称
  updateSelectedWardrobeName: function() {
    const { defaultWardrobeId, wardrobeList } = this.data;
    
    if (defaultWardrobeId && wardrobeList.length > 0) {
      const selectedWardrobe = wardrobeList.find(w => w.id == defaultWardrobeId);
      if (selectedWardrobe) {
        this.setData({
          selectedWardrobeName: selectedWardrobe.name
        });
      }
    }
  },
  
  // 选择衣橱（针对当前编辑的衣物）
  bindWardrobeChange: function(e) {
    const index = e.detail.value;
    const wardrobe = this.data.wardrobeList[index];
    
    if (wardrobe && this.data.currentEditIndex >= 0) {
      this.updateItemWardrobe(this.data.currentEditIndex, wardrobe.id, wardrobe.name);
      console.log('为衣物选择衣橱:', wardrobe.name, '(ID:', wardrobe.id, ')');
    }
  },
  
  // 更新衣物的衣橱ID和名称
  updateItemWardrobe: function(itemIndex, wardrobeId, wardrobeName) {
    if (itemIndex >= 0 && itemIndex < this.data.clothingItems.length) {
      const wardrobeIdKey = `clothingItems[${itemIndex}].wardrobeId`;
      const wardrobeNameKey = `clothingItems[${itemIndex}].wardrobeName`;
      
      this.setData({
        [wardrobeIdKey]: wardrobeId,
        [wardrobeNameKey]: wardrobeName
      });
    }
  }
}); 