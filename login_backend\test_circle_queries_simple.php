<?php
/**
 * 简单测试圈子查询
 * 直接测试SQL查询是否正确
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "=== 简单圈子查询测试 ===\n";
    echo "用户ID: $userId\n\n";
    
    // 1. 直接查询用户的圈子成员身份
    echo "1. 用户圈子成员身份:\n";
    $stmt = $conn->prepare("SELECT circle_id, status FROM circle_members WHERE user_id = :user_id");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $memberships = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($memberships as $membership) {
        echo "- 圈子ID: {$membership['circle_id']}, 状态: {$membership['status']}\n";
    }
    echo "\n";
    
    // 2. 查询圈子中的所有衣物（不限制用户）
    $activeCircles = array_column(array_filter($memberships, function($m) { return $m['status'] === 'active'; }), 'circle_id');
    
    if (empty($activeCircles)) {
        echo "用户没有活跃的圈子成员身份\n";
        exit;
    }
    
    $circleIds = implode(',', $activeCircles);
    echo "2. 圈子中的所有衣物:\n";
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.user_id, c.circle_id, u.nickname
        FROM clothes c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.circle_id IN ($circleIds)
        ORDER BY c.user_id, c.id
        LIMIT 20
    ");
    $stmt->execute();
    $allCircleClothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($allCircleClothes)) {
        echo "圈子中没有衣物数据\n";
    } else {
        foreach ($allCircleClothes as $item) {
            $isOwner = $item['user_id'] == $userId ? ' (自己)' : '';
            echo "- ID: {$item['id']}, 名称: {$item['name']}, 用户: {$item['nickname']} (ID: {$item['user_id']}){$isOwner}, 圈子: {$item['circle_id']}\n";
        }
    }
    echo "\n";
    
    // 3. 测试API的具体SQL查询
    echo "3. 测试API SQL查询:\n";
    
    // 共享数据查询
    echo "3.1 共享数据查询:\n";
    $sql = "SELECT c.id, c.name, c.user_id, c.circle_id, u.nickname as creator_nickname
            FROM clothes c 
            LEFT JOIN users u ON c.user_id = u.id 
            WHERE c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
            ORDER BY c.created_at DESC
            LIMIT 10";
    
    echo "SQL: $sql\n";
    echo "参数: user_id = $userId\n";
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $sharedResult = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "结果数量: " . count($sharedResult) . "\n";
    foreach ($sharedResult as $item) {
        echo "- {$item['name']} (ID: {$item['id']}) - 创建者: {$item['creator_nickname']} (用户{$item['user_id']})\n";
    }
    echo "\n";
    
    // 全部数据查询
    echo "3.2 全部数据查询:\n";
    $sql = "SELECT c.id, c.name, c.user_id, c.circle_id, u.nickname as creator_nickname,
                   CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
            FROM clothes c 
            LEFT JOIN users u ON c.user_id = u.id 
            WHERE ((c.user_id = :user_id AND c.circle_id IS NULL) OR
                   (c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')))
            ORDER BY c.created_at DESC
            LIMIT 10";
    
    echo "SQL: $sql\n";
    echo "参数: user_id = $userId\n";
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $allResult = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "结果数量: " . count($allResult) . "\n";
    foreach ($allResult as $item) {
        echo "- {$item['name']} (ID: {$item['id']}) - 创建者: {$item['creator_nickname']} (用户{$item['user_id']}) - 数据源: {$item['data_source']}\n";
    }
    echo "\n";
    
    // 4. 分析结果
    echo "4. 分析:\n";
    $circleClothesCount = count($allCircleClothes);
    $sharedQueryCount = count($sharedResult);
    $allQueryCount = count($allResult);
    
    echo "- 圈子中总衣物数: $circleClothesCount\n";
    echo "- 共享查询结果数: $sharedQueryCount\n";
    echo "- 全部查询结果数: $allQueryCount\n";
    
    if ($circleClothesCount > 0 && $sharedQueryCount == 0) {
        echo "⚠️ 问题：圈子中有衣物，但共享查询返回0结果\n";
        echo "可能原因：\n";
        echo "1. 用户成员状态不是'active'\n";
        echo "2. 衣物的circle_id设置有问题\n";
        echo "3. SQL查询逻辑有错误\n";
    } elseif ($sharedQueryCount > 0) {
        echo "✅ 共享查询正常工作\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
