<?php
/**
 * 管理员获取穿搭日历数据API
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

// 检查是否有Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '需要授权头']);
    exit;
}

// 验证管理员token
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或过期的令牌']);
    exit;
}

// 获取参数
$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : null;
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01'); // 默认当月第一天
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-t'); // 默认当月最后一天
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 30;
$offset = ($page - 1) * $limit;

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

try {
    // 构建查询条件
    $whereClause = "";
    $params = [];
    
    if (!empty($userId)) {
        $whereClause .= " AND oc.user_id = :user_id";
        $params[':user_id'] = $userId;
    }
    
    // 查询总记录数
    $countSql = "
        SELECT COUNT(*) as total 
        FROM outfit_calendar oc
        WHERE oc.calendar_date BETWEEN :start_date AND :end_date
        $whereClause
    ";
    
    $countStmt = $conn->prepare($countSql);
    $countStmt->bindParam(':start_date', $startDate);
    $countStmt->bindParam(':end_date', $endDate);
    
    foreach ($params as $key => $value) {
        $countStmt->bindValue($key, $value);
    }
    
    $countStmt->execute();
    $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 查询穿搭日历数据
    $sql = "
        SELECT oc.id as calendar_id, oc.calendar_date, oc.user_id, oc.created_at as calendar_created_at,
               o.id as outfit_id, o.name as outfit_name, o.description, o.thumbnail_url, 
               o.category_id, c.name as category_name,
               u.nickname, u.avatar_url
        FROM outfit_calendar oc
        JOIN outfits o ON oc.outfit_id = o.id
        LEFT JOIN outfit_categories c ON o.category_id = c.id
        JOIN users u ON oc.user_id = u.id
        WHERE oc.calendar_date BETWEEN :start_date AND :end_date
        $whereClause
        ORDER BY oc.calendar_date DESC, oc.created_at DESC
        LIMIT :limit OFFSET :offset
    ";
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':start_date', $startDate);
    $stmt->bindParam(':end_date', $endDate);
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    
    $stmt->execute();
    $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 计算总页数
    $totalPages = ceil($totalCount / $limit);
    
    // 处理结果，确保数据格式一致
    foreach ($records as &$record) {
        // 处理可能的NULL值
        $record['nickname'] = $record['nickname'] ?? '未知用户';
        $record['category_name'] = $record['category_name'] ?? '未分类';
    }
    
    // 获取用户列表（用于筛选）
    $usersSql = "
        SELECT DISTINCT u.id, u.nickname 
        FROM outfit_calendar oc
        JOIN users u ON oc.user_id = u.id
        ORDER BY u.nickname
    ";
    $usersStmt = $conn->prepare($usersSql);
    $usersStmt->execute();
    $users = $usersStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 返回结果
    echo json_encode([
        'error' => false,
        'data' => [
            'total' => $totalCount,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => $totalPages,
            'records' => $records,
            'users' => $users,
            'start_date' => $startDate,
            'end_date' => $endDate
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '获取穿搭日历数据失败: ' . $e->getMessage()
    ]);
} 