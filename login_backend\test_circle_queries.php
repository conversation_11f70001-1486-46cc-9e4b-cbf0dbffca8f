<?php
/**
 * 测试圈子数据查询的API
 * 用于验证圈子数据查询是否正常工作
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['user_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $result = [];
    
    // 1. 检查用户所在的圈子
    $stmt = $conn->prepare("
        SELECT cm.circle_id, c.name as circle_name, cm.status, cm.role
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $circles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['user_circles'] = $circles;
    
    if (!empty($circles)) {
        $circleId = $circles[0]['circle_id'];
        
        // 2. 测试衣物查询
        $stmt = $conn->prepare("
            SELECT c.id, c.name, c.category, c.user_id, c.wardrobe_id, c.circle_id,
                   u.nickname as creator_nickname,
                   CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
            FROM clothes c 
            LEFT JOIN users u ON c.user_id = u.id 
            WHERE c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
            LIMIT 5
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $sharedClothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result['shared_clothes'] = $sharedClothes;
        
        // 3. 测试穿搭查询
        $stmt = $conn->prepare("
            SELECT o.id, o.name, o.user_id, o.circle_id,
                   u.nickname as creator_nickname,
                   CASE WHEN o.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
            FROM outfits o
            LEFT JOIN users u ON o.user_id = u.id
            WHERE o.circle_id IS NOT NULL AND o.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
            LIMIT 5
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $sharedOutfits = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result['shared_outfits'] = $sharedOutfits;
        
        // 4. 测试衣橱查询
        $stmt = $conn->prepare("
            SELECT w.id, w.name, w.user_id, w.circle_id,
                   u.nickname as creator_nickname,
                   CASE WHEN w.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
            FROM wardrobes w
            LEFT JOIN users u ON w.user_id = u.id
            WHERE w.circle_id IS NOT NULL AND w.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
            LIMIT 5
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $sharedWardrobes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result['shared_wardrobes'] = $sharedWardrobes;
        
        // 5. 测试分类查询
        $stmt = $conn->prepare("
            SELECT c.id, c.name, c.code, c.user_id, c.circle_id,
                   u.nickname as creator_nickname,
                   CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
            FROM clothing_categories c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
            LIMIT 5
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $sharedCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result['shared_categories'] = $sharedCategories;
        
        // 6. 检查圈子中的数据总数
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM clothes WHERE circle_id = :circle_id");
        $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
        $stmt->execute();
        $clothesCount = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM outfits WHERE circle_id = :circle_id");
        $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
        $stmt->execute();
        $outfitsCount = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM wardrobes WHERE circle_id = :circle_id");
        $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
        $stmt->execute();
        $wardrobesCount = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $result['circle_data_counts'] = [
            'clothes' => $clothesCount['count'],
            'outfits' => $outfitsCount['count'],
            'wardrobes' => $wardrobesCount['count']
        ];
    }
    
    echo json_encode([
        'status' => 'success',
        'data' => $result
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '查询失败: ' . $e->getMessage()
    ]);
}
?>
