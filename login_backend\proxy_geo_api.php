<?php
/**
 * 地理编码API代理服务
 * 
 * 用于查询经纬度对应的城市信息或城市名对应的位置信息
 */

require_once 'config.php';
require_once 'city_utils.php';

// 设置返回内容类型
header('Content-Type: application/json');

// 获取路径和查询参数
$requestUri = $_SERVER['REQUEST_URI'];
$queryParams = [];
parse_str($_SERVER['QUERY_STRING'] ?? '', $queryParams);

// 获取请求参数
$key = isset($queryParams['key']) ? $queryParams['key'] : WEATHER_API_KEY;
$location = isset($queryParams['location']) ? $queryParams['location'] : '杭州';
$lang = isset($queryParams['lang']) ? $queryParams['lang'] : 'zh';

// 检查关键参数
if (empty($location)) {
    header('HTTP/1.1 400 Bad Request');
    echo json_encode(['code' => '400', 'msg' => '缺少location参数']);
    exit;
}

// 根据请求路径判断API接口
if (strpos($requestUri, 'proxy_geo_api.php') !== false) {
    // 地理位置API请求
    $apiUrl = "https://geoapi.qweather.com/v2/city/lookup";
    
    // 构建查询参数
    $params = [
        'location' => $location,
        'key' => $key,
        'lang' => $lang
    ];
    
    $requestUrl = $apiUrl . '?' . http_build_query($params);
    error_log("地理API - 请求: $requestUrl");
    
    // 创建cURL请求
    $ch = curl_init($requestUrl);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => true
    ]);
    
    // 执行请求
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    // 检查响应
    if ($httpCode !== 200 || $error) {
        error_log("地理API - 请求失败: " . ($error ?: "HTTP状态码: $httpCode"));
        
        // 如果API请求失败，使用模拟数据
        $mockData = getMockGeoData($location, 30.22, 120.21);
        echo json_encode($mockData, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 记录响应
    $result = json_decode($response, true);
    if (!isset($result['code']) || $result['code'] != '200') {
        error_log("地理API - 响应错误: " . json_encode($result));
        
        // 模拟数据
        $mockData = getMockGeoData($location, 30.22, 120.21);
        echo json_encode($mockData, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 记录成功响应
    error_log("地理API - 成功获取位置信息: " . json_encode($result['location'][0] ?? []));
    
    // 返回原始响应
    echo $response;
    exit;
}

// 如果不是上述API，返回错误
header('HTTP/1.1 404 Not Found');
echo json_encode(['code' => '404', 'msg' => '未知API请求']);
exit;

/**
 * 调用和风天气地理编码API
 * 
 * @param float $lat 纬度
 * @param float $lon 经度
 * @return array API响应数据
 */
function callGeocodingAPI($lat, $lon) {
    // 验证参数有效性
    if (!is_numeric($lat) || !is_numeric($lon)) {
        error_log("地理编码API - 经纬度格式无效: $lat, $lon");
        return getMockGeoData("位置($lat,$lon)", $lat, $lon);
    }
    
    // 首先尝试使用城市ID映射查找
    $cityId = getCityIdByLocation($lat, $lon);
    $cityInfo = getCityInfoById($cityId);
    
    if ($cityInfo) {
        error_log("地理编码API - 使用本地映射找到城市ID: $cityId, 城市名: {$cityInfo['name']}");
        return [
            'success' => true,
            'message' => '本地查询成功',
            'data' => [
                [
                    'id' => $cityId,
                    'name' => $cityInfo['name'],
                    'adm1' => $cityInfo['adm1'],
                    'adm2' => $cityInfo['adm2'] ?? $cityInfo['name'],
                    'lat' => $cityInfo['lat'] ?? $lat,
                    'lon' => $cityInfo['lon'] ?? $lon
                ]
            ]
        ];
    }
    
    // 构建API URL
    $apiKey = WEATHER_API_KEY;
    $location = "$lat,$lon";
    $apiUrl = "https://geoapi.qweather.com/v2/city/lookup?location=" . urlencode($location) . "&key=$apiKey";
    
    error_log("地理编码API - 调用外部API: $apiUrl");
    
    // 发送API请求
    $ch = curl_init($apiUrl);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => true
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    // 检查响应
    if ($httpCode !== 200 || $error) {
        error_log("地理编码API - 请求失败: " . ($error ?: "HTTP状态码: $httpCode"));
        return getMockGeoData("位置($lat,$lon)", $lat, $lon);
    }
    
    // 解析响应
    $result = json_decode($response, true);
    if (!$result || !isset($result['code'])) {
        error_log("地理编码API - 响应格式无效");
        return getMockGeoData("位置($lat,$lon)", $lat, $lon);
    }
    
    // 检查API返回状态
    if ($result['code'] != '200' || empty($result['location'])) {
        error_log("地理编码API - API返回错误: " . $result['code']);
        return getMockGeoData("位置($lat,$lon)", $lat, $lon);
    }
    
    // 记录城市ID用于验证
    $apiCityId = $result['location'][0]['id'];
    error_log("地理编码API - 查询成功，城市ID: " . $apiCityId);
    
    // 返回简化的数据
    return [
        'success' => true,
        'message' => '查询成功',
        'data' => array_map(function($loc) {
            return [
                'id' => $loc['id'],
                'name' => $loc['name'],
                'adm1' => $loc['adm1'],
                'adm2' => $loc['adm2'],
                'lat' => $loc['lat'],
                'lon' => $loc['lon']
            ];
        }, $result['location'])
    ];
}

/**
 * 调用和风天气地理编码API，根据城市名称查询
 * 
 * @param string $cityName 城市名称
 * @return array API响应数据
 */
function callGeocodingByName($cityName) {
    // 首先尝试使用城市ID映射查找
    $cityId = getCityIdByName($cityName);
    $cityInfo = getCityInfoById($cityId);
    
    if ($cityInfo) {
        error_log("地理编码API - 使用本地映射找到城市名称: $cityName => ID: $cityId, 城市名: {$cityInfo['name']}");
        return [
            'success' => true,
            'message' => '本地查询成功',
            'data' => [
                [
                    'id' => $cityId,
                    'name' => $cityInfo['name'],
                    'adm1' => $cityInfo['adm1'] ?? '',
                    'adm2' => $cityInfo['adm2'] ?? $cityInfo['name'],
                    'lat' => $cityInfo['lat'] ?? '',
                    'lon' => $cityInfo['lon'] ?? ''
                ]
            ]
        ];
    }
    
    // 构建API URL
    $apiKey = WEATHER_API_KEY;
    $location = urlencode($cityName);
    $apiUrl = "https://geoapi.qweather.com/v2/city/lookup?location=$location&key=$apiKey";
    
    error_log("地理编码API - 调用外部API查询城市名: $cityName, URL: $apiUrl");
    
    // 发送API请求
    $ch = curl_init($apiUrl);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => true
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    // 检查响应
    if ($httpCode !== 200 || $error) {
        error_log("地理编码API - 请求失败: " . ($error ?: "HTTP状态码: $httpCode"));
        // 尝试返回一个合理的默认值，使用北京而不是杭州
        return getNearestCity($cityName);
    }
    
    // 解析响应
    $result = json_decode($response, true);
    if (!$result || !isset($result['code'])) {
        error_log("地理编码API - 响应格式无效");
        return getNearestCity($cityName);
    }
    
    // 检查API返回状态
    if ($result['code'] != '200' || empty($result['location'])) {
        error_log("地理编码API - API返回错误: " . $result['code']);
        return getNearestCity($cityName);
    }
    
    // 记录城市ID用于验证
    $apiCityId = $result['location'][0]['id'];
    error_log("地理编码API - 查询成功，城市名: $cityName, 城市ID: " . $apiCityId);
    
    // 返回简化的数据
    return [
        'success' => true,
        'message' => '查询成功',
        'data' => array_map(function($loc) {
            return [
                'id' => $loc['id'],
                'name' => $loc['name'],
                'adm1' => $loc['adm1'],
                'adm2' => $loc['adm2'],
                'lat' => $loc['lat'],
                'lon' => $loc['lon']
            ];
        }, $result['location'])
    ];
}

/**
 * 根据字符串相似度查找最接近的城市
 * 
 * @param string $cityName 城市名称
 * @return array 最接近的城市信息
 */
function getNearestCity($cityName) {
    // 常用城市及其ID
    $commonCities = [
        '北京' => ['id' => '101010100', 'province' => '北京市', 'lat' => '39.9050', 'lon' => '116.4053'],
        '上海' => ['id' => '101020100', 'province' => '上海市', 'lat' => '31.2317', 'lon' => '121.4726'],
        '广州' => ['id' => '101280101', 'province' => '广东省', 'lat' => '23.1291', 'lon' => '113.2644'],
        '深圳' => ['id' => '101280601', 'province' => '广东省', 'lat' => '22.5431', 'lon' => '114.0579'],
        '杭州' => ['id' => '101210101', 'province' => '浙江省', 'lat' => '30.2236', 'lon' => '120.1469'],
        '南京' => ['id' => '101190101', 'province' => '江苏省', 'lat' => '32.0584', 'lon' => '118.7965'],
        '天津' => ['id' => '101030100', 'province' => '天津市', 'lat' => '39.0851', 'lon' => '117.1990'],
        '武汉' => ['id' => '101200101', 'province' => '湖北省', 'lat' => '30.5928', 'lon' => '114.3055'],
        '西安' => ['id' => '101110101', 'province' => '陕西省', 'lat' => '34.3416', 'lon' => '108.9398'],
        '成都' => ['id' => '101270101', 'province' => '四川省', 'lat' => '30.6570', 'lon' => '104.0665'],
        '重庆' => ['id' => '101040100', 'province' => '重庆市', 'lat' => '29.5628', 'lon' => '106.5528']
    ];
    
    // 默认使用北京而不是杭州
    $bestMatch = ['name' => '北京', 'info' => $commonCities['北京']];
    $highestSimilarity = 0;
    
    // 对每个城市名计算与输入字符串的相似度
    foreach ($commonCities as $name => $info) {
        // 如果完全匹配，直接返回
        if ($cityName === $name) {
            error_log("地理编码API - 找到完全匹配城市: $name");
            return [
                'success' => true,
                'message' => '找到匹配城市',
                'data' => [
                    [
                        'id' => $info['id'],
                        'name' => $name . '市',
                        'adm1' => $info['province'],
                        'adm2' => $name . '市',
                        'lat' => $info['lat'],
                        'lon' => $info['lon']
                    ]
                ]
            ];
        }
        
        // 计算相似度
        $similarity = similar_text($cityName, $name) / max(strlen($cityName), strlen($name));
        
        if ($similarity > $highestSimilarity) {
            $highestSimilarity = $similarity;
            $bestMatch = ['name' => $name, 'info' => $info];
        }
    }
    
    error_log("地理编码API - 找到最接近的城市: {$bestMatch['name']}，相似度: " . round($highestSimilarity * 100, 2) . "%");
    
    return [
        'success' => true,
        'message' => '找到最接近的城市',
        'data' => [
            [
                'id' => $bestMatch['info']['id'],
                'name' => $bestMatch['name'] . '市',
                'adm1' => $bestMatch['info']['province'],
                'adm2' => $bestMatch['name'] . '市',
                'lat' => $bestMatch['info']['lat'],
                'lon' => $bestMatch['info']['lon']
            ]
        ]
    ];
}

/**
 * 获取模拟的地理数据
 * 
 * @param string $location 位置描述
 * @param float $lat 纬度
 * @param float $lon 经度
 * @return array 模拟的地理数据
 */
function getMockGeoData($location, $lat, $lon) {
    // 常用城市映射
    $commonCities = [
        '北京' => ['id' => '101010100', 'province' => '北京市', 'lat' => '39.9050', 'lon' => '116.4053'],
        '上海' => ['id' => '101020100', 'province' => '上海市', 'lat' => '31.2317', 'lon' => '121.4726'],
        '广州' => ['id' => '101280101', 'province' => '广东省', 'lat' => '23.1291', 'lon' => '113.2644'],
        '深圳' => ['id' => '101280601', 'province' => '广东省', 'lat' => '22.5431', 'lon' => '114.0579'],
        '杭州' => ['id' => '101210101', 'province' => '浙江省', 'lat' => '30.2236', 'lon' => '120.1469'],
        '南京' => ['id' => '101190101', 'province' => '江苏省', 'lat' => '32.0584', 'lon' => '118.7965'],
        '天津' => ['id' => '101030100', 'province' => '天津市', 'lat' => '39.0851', 'lon' => '117.1990'],
        '武汉' => ['id' => '101200101', 'province' => '湖北省', 'lat' => '30.5928', 'lon' => '114.3055'],
        '西安' => ['id' => '101110101', 'province' => '陕西省', 'lat' => '34.3416', 'lon' => '108.9398'],
        '成都' => ['id' => '101270101', 'province' => '四川省', 'lat' => '30.6570', 'lon' => '104.0665'],
        '重庆' => ['id' => '101040100', 'province' => '重庆市', 'lat' => '29.5628', 'lon' => '106.5528']
    ];
    
    // 默认使用北京而不是杭州
    $cityName = '北京市';
    $cityId = '101010100';
    $province = '北京市';
    $cityLat = '39.9050';
    $cityLon = '116.4053';
    
    // 如果位置是城市名，使用对应的ID
    foreach ($commonCities as $name => $info) {
        if (strpos($location, $name) !== false) {
            $cityName = $name . '市';
            $cityId = $info['id'];
            $province = $info['province'];
            $cityLat = $info['lat'];
            $cityLon = $info['lon'];
            error_log("地理编码API - 使用城市名匹配: $location => $cityName (ID: $cityId)");
            break;
        }
    }
    
    // 如果不是城市名，使用经纬度查询
    if ($cityId === '101010100' && is_numeric($lat) && is_numeric($lon)) {
        $foundCityId = getCityIdByLocation($lat, $lon);
        $cityInfo = getCityInfoById($foundCityId);
        
        if ($cityInfo) {
            $cityId = $foundCityId;
            $cityName = $cityInfo['name'];
            $province = $cityInfo['adm1'] ?? '';
            $cityLat = $cityInfo['lat'] ?? $lat;
            $cityLon = $cityInfo['lon'] ?? $lon;
            error_log("地理编码API - 根据经纬度找到城市: $cityName (ID: $cityId)");
        }
    }
    
    error_log("地理编码API - 返回模拟数据: 城市=$cityName, ID=$cityId, 经纬度=$cityLat,$cityLon");
    
    return [
        'success' => true,
        'message' => '模拟数据',
        'data' => [
            [
                'id' => $cityId,
                'name' => $cityName,
                'adm1' => $province,
                'adm2' => $cityName,
                'lat' => $cityLat,
                'lon' => $cityLon
            ]
        ]
    ];
}
?>