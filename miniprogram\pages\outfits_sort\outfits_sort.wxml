<view class="container">
  <!-- 页面头部说明 -->
  <view class="header-info">
    <text class="info-text">点击上下箭头调整穿搭排序，调整后点击保存按钮更新排序</text>
  </view>

  <!-- 穿搭列表 -->
  <view class="outfits-list">
    <view wx:for="{{outfitsList}}" wx:key="id" class="outfit-item" data-index="{{index}}" data-id="{{item.id}}">

      <!-- 穿搭头部信息 -->
      <view class="outfit-header">
        <view class="outfit-name">{{item.name}}</view>
        <view class="outfit-meta">
          <text class="outfit-category">{{item.category_name || '未分类'}}</text>
          <text class="outfit-date">{{item.created_at}}</text>
        </view>
      </view>

      <!-- 穿搭内容区域 -->
      <view class="outfit-content">
        <!-- 衣物容器 -->
        <view class="items-container">
          <scroll-view wx:if="{{item.items && item.items.length > 0}}"
                       class="items-scroll"
                       scroll-x="true"
                       show-scrollbar="false">
            <view class="items-list">
              <view
                wx:for="{{item.items}}"
                wx:for-item="clothingItem"
                wx:key="clothing_id"
                class="clothing-item">
                <image
                  src="{{clothingItem.clothing_data.image_url}}"
                  mode="aspectFit"
                  class="clothing-image">
                </image>
              </view>
            </view>
          </scroll-view>
          <view wx:else class="no-items">
            <text class="no-items-text">无衣物</text>
          </view>
        </view>

        <!-- 排序控制按钮 -->
        <view class="sort-controls">
          <view class="sort-btn" bindtap="moveUp" data-index="{{index}}">
            <text class="sort-icon">↑</text>
          </view>
          <view class="sort-btn" bindtap="moveDown" data-index="{{index}}">
            <text class="sort-icon">↓</text>
          </view>
        </view>
      </view>

    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{outfitsList.length === 0 && !loading}}">
    <text class="empty-text">暂无穿搭数据</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 底部保存按钮 -->
<view class="save-btn-container">
  <button class="save-btn" bindtap="saveSortOrder" disabled="{{saving}}">
    {{saving ? '保存中...' : '保存'}}
  </button>
</view>
