<view class="container">
  <!-- 页面头部说明 -->
  <view class="header-info">
    <text class="info-text">点击上下箭头调整穿搭排序，调整后点击保存按钮更新排序</text>
  </view>

  <!-- 穿搭列表 -->
  <view class="outfits-list">
    <view wx:for="{{outfitsList}}" wx:key="id" class="outfit-item" data-index="{{index}}" data-id="{{item.id}}">
      <view class="outfit-content">
        <!-- 穿搭缩略图 -->
        <image class="outfit-image" src="{{item.thumbnail}}" mode="aspectFill" lazy-load="true"></image>
        
        <!-- 穿搭信息 -->
        <view class="outfit-info">
          <text class="outfit-name">{{item.name}}</text>
          <text class="outfit-category">{{item.category_name || '未分类'}}</text>
          <text class="outfit-date">{{item.created_at}}</text>
        </view>
        
        <!-- 排序按钮 -->
        <view class="sort-controls">
          <view class="sort-btn" bindtap="moveUp" data-index="{{index}}">
            <text class="sort-icon">↑</text>
          </view>
          <view class="sort-btn" bindtap="moveDown" data-index="{{index}}">
            <text class="sort-icon">↓</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{outfitsList.length === 0 && !loading}}">
    <text class="empty-text">暂无穿搭数据</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 底部保存按钮 -->
<view class="save-btn-container">
  <button class="save-btn" bindtap="saveSortOrder" disabled="{{saving}}">
    {{saving ? '保存中...' : '保存'}}
  </button>
</view>
