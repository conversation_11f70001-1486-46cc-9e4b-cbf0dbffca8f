<view class="container">
  <!-- 页面头部说明 -->
  <view class="header-info">
    <text class="info-text">点击上下箭头调整穿搭排序，调整后点击保存按钮更新排序</text>
  </view>

  <!-- 穿搭列表 -->
  <view class="outfits-list">
    <view class="outfits-grid">
      <view wx:for="{{outfitsList}}" wx:key="id" class="outfit-item" data-index="{{index}}" data-id="{{item.id}}">

        <!-- 穿搭预览图 -->
        <view class="outfit-preview">
          <block wx:if="{{item.thumbnail}}">
            <image src="{{item.thumbnail}}" mode="aspectFill" class="outfit-thumbnail"></image>
          </block>
          <block wx:else>
            <!-- 穿搭视图，显示所有衣物 -->
            <view class="outfit-placeholder" wx:if="{{item.items && item.items.length > 0}}">
              <view class="outfit-view-mini">
                <view
                  wx:for="{{item.items}}"
                  wx:for-item="clothingItem"
                  wx:key="clothing_id"
                  class="outfit-item-mini"
                  style="left: {{clothingItem.previewPosition.x}}px; top: {{clothingItem.previewPosition.y}}px; width: {{clothingItem.size.width * clothingItem.previewPosition.scale}}px; height: {{clothingItem.size.height * clothingItem.previewPosition.scale}}px; transform: rotate({{clothingItem.rotation}}deg); z-index: {{clothingItem.z_index}};">
                  <image src="{{clothingItem.clothing_data.image_url}}" mode="aspectFit" class="item-image-mini"></image>
                </view>
              </view>
            </view>
            <view class="outfit-no-items" wx:else>
              <text class="no-items-text">无衣物</text>
            </view>
          </block>

          <!-- 排序控制按钮 - 覆盖在预览图上 -->
          <view class="sort-controls-overlay">
            <view class="sort-btn" bindtap="moveUp" data-index="{{index}}">
              <text class="sort-icon">↑</text>
            </view>
            <view class="sort-btn" bindtap="moveDown" data-index="{{index}}">
              <text class="sort-icon">↓</text>
            </view>
          </view>
        </view>

        <!-- 穿搭信息 -->
        <view class="outfit-info">
          <view class="outfit-name">{{item.name}}</view>
          <view class="outfit-bottom-row">
            <view class="outfit-category">{{item.category_name || '未分类'}}</view>
            <view class="outfit-date">{{item.created_at}}</view>
          </view>
        </view>

      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{outfitsList.length === 0 && !loading}}">
    <text class="empty-text">暂无穿搭数据</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 底部保存按钮 -->
<view class="save-btn-container">
  <button class="save-btn" bindtap="saveSortOrder" disabled="{{saving}}">
    {{saving ? '保存中...' : '保存'}}
  </button>
</view>
