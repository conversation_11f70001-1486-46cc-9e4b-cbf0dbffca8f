<view class="container">
  <view class="header">
    <view class="back-button" bindtap="goBack">
      <image class="back-icon" src="/images/back.png"></image>
    </view>
    <view class="title-container">
      <view class="title">更新衣物标签</view>
      <view class="subtitle">智能分析衣物图片，生成详细标签</view>
    </view>
  </view>
  
  <!-- 更新进度 -->
  <view class="update-progress" wx:if="{{loading}}">
    <view class="progress-info">
      <text class="progress-text">正在分析衣物图片，请稍候...</text>
    </view>

    <view class="tips">
      <view class="tip-item">系统将分析没有标签的衣物图片，并生成穿搭相关标签</view>
      <view class="tip-item">分析过程可能需要一些时间，请耐心等待</view>
    </view>
  </view>
  
  <!-- 更新结果 -->
  <view class="update-result" wx:elif="{{showResult}}">
    <view class="result-header">
      <view class="result-icon">✅</view>
      <view class="result-title">更新完成</view>
    </view>
    
    <view class="result-stats">
      <view class="stat-item">
        <text class="stat-label">检测衣物</text>
        <text class="stat-value">{{result.total_processed}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">更新成功</text>
        <text class="stat-value success">{{result.updated_count}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">更新失败</text>
        <text class="stat-value failed">{{result.skipped_count}}</text>
      </view>
      <!-- 清理无效标签统计 -->
      <view class="stat-item" wx:if="{{result.tag_cleanup && result.tag_cleanup.cleaned_items_count > 0}}">
        <text class="stat-label">清除无效标签</text>
        <text class="stat-value cleanup">{{result.tag_cleanup.cleaned_items_count}}</text>
      </view>
    </view>
    
    <!-- 更新详情 -->
    <view class="result-details" wx:if="{{result.updated_count > 0}}">
      <view class="details-header" bindtap="toggleDetailsList">
        <text class="details-title">更新详情</text>
        <view class="toggle-icon {{showDetailsList ? 'active' : ''}}">
          <text class="toggle-text">{{showDetailsList ? '收起' : '展开'}}</text>
          <image class="arrow-icon" src="/images/arrow-down.png" style="transform: rotate({{showDetailsList ? 180 : 0}}deg);"></image>
        </view>
      </view>
      
      <view class="details-list" wx:if="{{showDetailsList}}">
        <view class="detail-item" 
              wx:for="{{result.updated_items}}" 
              wx:key="id"
              bindtap="viewClothingDetail"
              data-id="{{item.id}}">
          <view class="item-name">{{item.name || '未命名衣物'}}</view>
          <view class="item-tags">
            <text class="item-tag" wx:for="{{item.added_tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 无需更新的提示 -->
    <view class="no-update-needed" wx:if="{{result.total_processed === 0}}">
      <view class="check-icon">🎉</view>
      <text class="check-text">您的衣物标签已经很完善了！</text>
      <text class="check-tip">所有衣物都已经有标签，无需更新</text>
    </view>
    
    <!-- 返回按钮 -->
    <button class="return-button" bindtap="goToTagList">
      <text class="button-text">返回标签列表</text>
    </button>
  </view>
  
  <!-- 初始界面 -->
  <view class="initial-screen" wx:else>
    <image class="intro-image" src="/images/tags-intro.png"></image>
    <view class="intro-text">
      <view class="intro-title">智能衣物标签</view>
      <view class="intro-desc">系统将通过AI分析您的衣物图片，自动生成详细的标签信息，助力智能穿搭推荐。</view>
    </view>
    
    <view class="features">
      <view class="feature-item">
        <view class="feature-icon">👕</view>
        <view class="feature-text">自动识别衣物类型、颜色、风格</view>
      </view>
      <view class="feature-item">
        <view class="feature-icon">🏷️</view>
        <view class="feature-text">生成适合季节、场合的标签</view>
      </view>
      <view class="feature-item">
        <view class="feature-icon">✨</view>
        <view class="feature-text">提供穿搭建议和搭配推荐</view>
      </view>
    </view>
    
    <button class="update-button" bindtap="updateClothingTags">
      <text class="button-text">开始更新衣物标签</text>
    </button>
  </view>
</view> 