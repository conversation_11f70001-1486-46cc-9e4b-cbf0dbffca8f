<?php
/**
 * 获取邀请码详情API
 */
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

// 获取Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '需要授权']);
    exit;
}

// 验证token
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或过期的令牌']);
    exit;
}

// 获取管理员ID
$adminId = $payload['admin_id'];

// 检查管理员是否存在
$db = new Database();
$conn = $db->getConnection();
$adminStmt = $conn->prepare("SELECT id, username FROM admin_users WHERE id = :id");
$adminStmt->bindParam(':id', $adminId);
$adminStmt->execute();
$admin = $adminStmt->fetch(PDO::FETCH_ASSOC);

if (!$admin) {
    http_response_code(403);
    echo json_encode(['error' => true, 'msg' => '权限不足']);
    exit;
}

// 获取请求参数
$id = isset($_GET['id']) && is_numeric($_GET['id']) ? (int)$_GET['id'] : 0;

// 验证参数
if ($id <= 0) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '无效的ID']);
    exit;
}

try {
    // 查询邀请码详情
    $sql = "
        SELECT 
            ic.*, 
            creator.username AS creator_name,
            user.nickname AS user_name
        FROM invitation_codes ic
        LEFT JOIN admin_users AS creator ON ic.created_by = creator.id
        LEFT JOIN users AS user ON ic.used_by = user.id
        WHERE ic.id = :id
    ";
    
    $stmt = $conn->prepare($sql);
    $stmt->bindValue(':id', $id, PDO::PARAM_INT);
    $stmt->execute();
    $code = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$code) {
        http_response_code(404);
        echo json_encode(['error' => true, 'msg' => '未找到邀请码']);
        exit;
    }
    
    // 返回结果
    echo json_encode([
        'error' => false,
        'msg' => '获取成功',
        'data' => $code
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '获取邀请码详情失败: ' . $e->getMessage()]);
} 