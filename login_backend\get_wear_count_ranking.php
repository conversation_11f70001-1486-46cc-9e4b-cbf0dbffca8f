<?php
/**
 * Get Wear Count Ranking API
 * 
 * Gets clothing items ranked by wear count (highest to lowest)
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * GET Parameters:
 * - page: Page number (default: 1)
 * - per_page: Items per page (default: 20)
 * 
 * Response:
 * {
 *   "error": false,
 *   "data": [
 *     {
 *       "id": "123",
 *       "name": "衣物名称",
 *       "image_url": "图片URL",
 *       "wear_count": 10,
 *       "category": "分类",
 *       "rank": 1
 *     }
 *   ],
 *   "pagination": {
 *     "total": 50,
 *     "per_page": 20,
 *     "current_page": 1,
 *     "total_pages": 3
 *   }
 * }
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// Set response content type
header('Content-Type: application/json');

// Handle CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if Authorization header exists
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'message' => 'Authorization header is required'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// 处理Bearer前缀
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// Verify token
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    echo json_encode([
        'error' => true,
        'message' => 'Invalid or expired token'
    ]);
    exit;
}

// Get user ID from token data
$user_id = $tokenData['sub'];

// Only allow GET method
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode(['error' => true, 'message' => 'Only GET method allowed']);
    exit;
}

// Get pagination parameters
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
$offset = ($page - 1) * $per_page;

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM clothes WHERE user_id = :user_id";
    $countStmt = $conn->prepare($countSql);
    $countStmt->execute(['user_id' => $user_id]);
    $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Get ranked clothing items
    $sql = "SELECT id, name, image_url, category, description 
            FROM clothes 
            WHERE user_id = :user_id 
            ORDER BY 
                CASE 
                    WHEN description IS NOT NULL AND description != '' THEN
                        CAST(JSON_UNQUOTE(JSON_EXTRACT(description, '$.wearCount')) AS UNSIGNED)
                    ELSE 0
                END DESC,
                created_at DESC
            LIMIT :offset, :per_page";
    
    $stmt = $conn->prepare($sql);
    $stmt->bindValue(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':per_page', $per_page, PDO::PARAM_INT);
    $stmt->execute();
    
    $clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process results and add ranking
    $rankedClothes = [];
    $rank = $offset + 1;
    
    foreach ($clothes as $item) {
        // Parse wear count from description JSON
        $wearCount = 0;
        if (!empty($item['description'])) {
            try {
                $description = json_decode($item['description'], true);
                $wearCount = isset($description['wearCount']) ? intval($description['wearCount']) : 0;
            } catch (Exception $e) {
                // If JSON parsing fails, wear count remains 0
            }
        }
        
        $rankedClothes[] = [
            'id' => $item['id'],
            'name' => $item['name'],
            'image_url' => $item['image_url'],
            'category' => $item['category'],
            'wear_count' => $wearCount,
            'rank' => $rank
        ];
        
        $rank++;
    }
    
    // Calculate pagination info
    $totalPages = ceil($totalCount / $per_page);
    
    echo json_encode([
        'error' => false,
        'data' => $rankedClothes,
        'pagination' => [
            'total' => intval($totalCount),
            'per_page' => $per_page,
            'current_page' => $page,
            'total_pages' => $totalPages
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
