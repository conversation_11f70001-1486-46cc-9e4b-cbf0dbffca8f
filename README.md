# 次元衣柜 (Virtual Wardrobe)

![次元衣柜](https://cyymj.oss-cn-shanghai.aliyuncs.com/logo.jpg)

次元衣柜是一款基于微信小程序的虚拟试衣应用，用户可以上传自己的照片并试穿各种衣物，实现线上虚拟换装体验。

## 最近更新

### 版本1.4.0（2023-08-20）
- ⚡️ **进度显示优化**: 优化了试穿过程的进度显示逻辑，将进度显示与API响应完全解耦
- 🌟 **渐进式加载**: 实现了渐进式进度显示，确保用户获得完整的状态变化体验
- 🚀 **状态序列控制**: 增加了状态序列控制机制，确保排队中→提交数据→合成中→即将完成的顺序展示
- ⏱️ **最小阶段持续**: 设置了各阶段的最小持续时间，确保用户能清晰感知每个处理阶段
- 🔄 **API状态跟踪**: 新增API状态跟踪变量，改进API请求与UI响应的协调机制

### 版本1.3.0（2023-05-11）
- 🐛 **修复错误**: 修复了点击试衣历史详情时的SQL错误（删除了错误引用的`c.gender`字段）
- ✨ **新增仪表盘**: 管理后台增加了数据分析仪表盘，支持统计用户、衣物、照片和试衣数据
- 📊 **图表可视化**: 实现各类数据的趋势分析和分布展示
- 📈 **API使用统计**: 新增试衣接口和抠图接口的使用情况统计和剩余配额显示

## 目录

- [项目概述](#项目概述)
- [系统架构](#系统架构)
- [核心功能](#核心功能)
- [技术栈](#技术栈)
- [安装部署](#安装部署)
- [API文档](#api文档)
- [项目迁移教程](#项目迁移教程)
- [目录结构](#目录结构)
- [未来规划](#未来规划)
- [贡献指南](#贡献指南)
- [许可证](#许可证)

## 项目概述

次元衣柜是一款创新的虚拟试衣小程序，通过整合人工智能技术，让用户能够在数字世界中尝试不同服装搭配。用户可以上传自己的照片，添加服装到个人衣柜，并通过阿里云的OutfitAnyone API实现高质量的虚拟试衣效果。

### 项目特点

- **虚拟试衣**：用户可以在不实际穿着的情况下，预览服装在自己身上的效果
- **个人衣柜管理**：支持添加、编辑、删除和分类管理个人服装
- **照片管理**：支持上传和管理个人照片，用于虚拟试衣
- **试衣历史**：记录并管理历史试衣效果，方便用户回顾和分享
- **社交分享**：支持分享试衣效果到微信好友和朋友圈

## 系统架构

次元衣柜采用前后端分离的架构设计：

### 前端（微信小程序）

- 基于微信小程序原生框架开发
- 提供用户界面和交互体验
- 负责数据展示和用户输入处理

### 后端（PHP RESTful API）

- 基于PHP开发的RESTful API服务
- 提供数据持久化和业务逻辑处理
- 集成第三方服务（阿里云OSS、OutfitAnyone API等）

### 数据存储

- MySQL数据库：存储用户信息、衣物信息、照片信息和试衣历史记录
- 阿里云OSS：存储所有图片资源，包括用户上传的照片、衣物图片和试衣结果

### 系统组件交互
```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  微信小程序     +----->+   PHP后端API   +----->+   MySQL数据库   |
|                |      |                |      |                |
+----------------+      +----------------+      +----------------+
                                |
                                v
                        +----------------+      +----------------+
                        |                |      |                |
                        |   阿里云OSS     |      |   AI试衣API    |
                        |                |      |                |
                        +----------------+      +----------------+
```

## 服饰抠图与OSS存储流程

系统采用先进的服饰抠图技术结合阿里云OSS存储服务，确保服装图片的高质量展示与高效存储。

### 服饰抠图流程

当用户上传服装图片时，系统会自动执行以下处理流程：

1. **上传原始图片**：用户通过小程序上传服装图片到服务器临时目录。
2. **调用阿里云服饰分割API**：系统使用阿里云的`SegmentCloth` API对图片进行处理。
   - 对于本地文件，使用`SegmentClothAdvance`方法处理
   - 对于远程URL，使用标准`SegmentCloth`方法处理
3. **保存分割结果**：API返回的抠图结果会被下载并保存到服务器的分割图片目录。
4. **返回处理后的图片URL**：系统返回分割后的图片URL供前端使用。

### OSS存储流程

系统采用以下策略确保所有图片都存储在阿里云OSS上：

1. **图片上传检测**：检查上传的图片URL是否已经是OSS URL。
2. **自动迁移到OSS**：
   - 对于新上传的图片，直接存储到OSS对应目录。
   - 对于已有的非OSS图片URL，系统会自动下载并上传到OSS。
3. **标准化命名**：
   - 衣物图片：`clothes/cloth_{userId}_{timestamp}_{random}.jpg`
   - 用户照片：`photos/photo_{userId}_{timestamp}_{random}.jpg`
   - 试衣结果：`try_on/try_on_{userId}_{timestamp}_{random}.jpg`
4. **URL替换**：数据库中的图片URL会被更新为OSS URL，确保一致性。

### 技术实现

系统采用以下技术实现服饰抠图与OSS存储：

- **OSS辅助类**：封装了`OssHelper`类处理所有OSS操作，包括：
  - 上传本地文件到OSS
  - 上传内容到OSS
  - 从远程URL下载到OSS
  - 删除OSS文件
  - 判断URL是否为OSS链接

- **阿里云图像分割SDK**：
  - 引入`alibabacloud/imageseg-20191230`进行服装图片分割
  - 支持本地文件和远程URL两种处理模式
  - 封装错误处理和重试机制

- **数据迁移支持**：
  - 提供`migrate_to_oss.php`脚本，支持批量将历史图片迁移到OSS
  - 自动检测并处理照片、衣物和试衣结果图片

### 技术流程图

```
+------------------+     +---------------------+     +----------------------+
|                  |     |                     |     |                      |
| 用户上传服装图片  +---->+ 阿里云服饰分割API   +---->+ 分割结果保存到服务器 |
|                  |     |                     |     |                      |
+------------------+     +---------------------+     +----------------------+
                                                                |
                                                                v
+------------------+     +---------------------+     +----------------------+
|                  |     |                     |     |                      |
| 数据库记录更新    +<----+ OSS URL替换原URL    |<----+ 图片上传到阿里云OSS  |
|                  |     |                     |     |                      |
+------------------+     +---------------------+     +----------------------+
```

### 服饰抠图技术优势

次元衣柜项目在服饰抠图和图像处理方面采用了多项技术创新，显著提升了虚拟试衣体验：

1. **精确抠图技术**：
   - 利用阿里云`SegmentCloth` API实现像素级精确分割
   - 能够处理复杂纹理和透明度的服饰图片
   - 保留服装细节，包括褶皱、纽扣和图案

2. **弹性处理机制**：
   - 实现了dual-path处理模式，支持本地文件和远程URL
   - 内置错误处理和降级策略，确保上传流程不中断
   - 日志记录与诊断工具，便于问题追踪

3. **性能优化**：
   - 图片处理与OSS上传并行化，减少等待时间
   - 临时文件管理与自动清理，减少存储占用
   - 异常情况下返回原始图片URL，确保功能连续性

4. **安全考量**：
   - 所有API调用使用临时会话令牌，降低密钥泄露风险
   - 图片处理中的异常参数检测，防止注入攻击
   - 文件类型验证，仅允许处理安全图片格式

这些技术优势使次元衣柜能够提供高质量的服饰图像，为后续的AI试衣功能奠定了坚实基础。用户可以体验到接近真实的试衣效果，无需担心图像质量或处理延迟问题。

## 核心功能

### 1. 用户管理

- 微信小程序授权登录
- 用户信息管理

### 2. 衣物管理

- 添加衣物（支持名称、分类、标签和描述）
- 编辑和删除衣物
- 按类别浏览衣物

### 3. 照片管理

#### 获取照片列表
- **URL**: `/get_photos.php`
- **方法**: GET
- **头部**: Authorization: <token>
- **参数**:
  - `id` (可选): 特定照片ID
- **响应**: 
  ```json
  {
    "error": false,
    "data": [
      {
        "id": 1,
        "image_url": "https://example.com/photo.jpg",
        "type": "full",
        "description": "Beach day",
        "created_at": "2023-03-31 12:00:00"
      }
    ]
  }
  ```

#### 上传照片
- **URL**: `/upload_photo.php`
- **方法**: POST
- **头部**: Authorization: <token>
- **参数**:
  - `image`: 照片文件
  - `type` (可选): 照片类型
  - `description` (可选): 描述
- **响应**: 
  ```json
  {
    "error": false,
    "data": {
      "id": 1,
      "image_url": "https://example.com/photo.jpg",
      "type": "full",
      "description": "Beach day",
      "created_at": "2023-03-31 12:00:00"
    }
  }
  ```

#### 删除照片
- **URL**: `/delete_photo.php`
- **方法**: POST/GET
- **头部**: Authorization: <token>
- **参数**: 
  - `id`: 照片ID
- **响应**: 
  ```json
  {
    "error": false,
    "msg": "照片已成功删除"
  }
  ```

### 服饰抠图接口

#### 上传并抠图处理
- **URL**: `/upload_image.php`
- **方法**: POST
- **头部**: Authorization: <token>
- **参数**:
  - `image`: 服装图片文件 (multipart/form-data)
  - `segment_image`: 是否执行抠图（可选，默认为true）
- **响应**: 
  ```json
  {
    "error": false,
    "data": {
      "image_url": "https://example.com/uploads/segmented/segmented_clothing_1_1677649012_1234.jpg",
      "original_url": "https://example.com/uploads/clothing_1_1677649012_1234.jpg"
    }
  }
  ```

#### 抠图开关功能
系统现已支持用户控制抠图功能的开启和关闭，通过界面上的抠图开关实现：

1. **功能特点**：
   - 默认上传不执行抠图，保留原图
   - 用户可通过开关选择是否对图片进行抠图处理
   - 支持随时切换抠图状态，无需重新上传
   - 保存时根据开关状态自动选择正确图片URL

2. **使用方法**：
   - 上传图片后，图片预览区下方会显示抠图开关
   - 打开开关后，系统自动执行服饰抠图处理
   - 抠图完成后，界面显示抠图后的图片
   - 关闭开关可恢复显示原图

3. **技术实现**：
   - 前端通过`segment_image`参数控制是否执行抠图
   - 初次上传图片时默认不执行抠图，保存原图URL
   - 用户打开抠图开关时，发送独立请求执行抠图处理
   - 服务端支持标准文件上传和JSON请求两种抠图方式
   - 抠图结果和原图URL均保存在前端状态中，便于切换

4. **优势**：
   - 提升用户体验，让用户可自主选择是否需要抠图
   - 减少不必要的API调用，节约抠图配额
   - 避免对不适合抠图的服装图片进行处理
   - 支持比较抠图前后效果，帮助用户做出选择

5. **JSON抠图请求**
- **URL**: `/upload_image.php`
- **方法**: POST
- **头部**: 
  - Authorization: <token>
  - Content-Type: application/json
- **参数**:
  ```json
  {
    "image_url": "https://example.com/uploads/clothing_1_1677649012_1234.jpg",
    "segment_image": true
  }
  ```
- **响应**: 
  ```json
  {
    "error": false,
    "data": {
      "image_url": "https://example.com/uploads/segmented/segmented_clothing_1_1677649012_1234.jpg",
      "original_url": "https://example.com/uploads/clothing_1_1677649012_1234.jpg"
    }
  }
  ```

#### 处理流程说明
1. 接口自动保存上传的原始图片
2. 根据`segment_image`参数决定是否执行抠图处理
3. 调用阿里云服饰分割API进行背景移除
4. 保存处理后的图片并返回URL
5. 自动将图片同步至OSS存储
6. 在处理失败时返回原始图片URL

#### 错误处理
- 上传失败: `{"error": true, "msg": "No image file uploaded or upload error"}`
- 无效文件类型: `{"error": true, "msg": "Invalid file type. Only JPEG, PNG, GIF, and WEBP are allowed."}`
- 验证失败: `{"error": true, "msg": "Invalid or expired token"}`

#### 支持的图片格式
- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)

### 虚拟试衣

- 选择照片和衣物进行虚拟试衣
- 基于阿里云OutfitAnyone API的AI试衣实现
- 试衣结果预览和保存
- 优化的进度显示流程，提供流畅的阶段式加载体验

### 5. 试衣历史

- 记录试衣历史
- 查看历史试衣效果
- 删除历史记录

### 6. 社交分享

- 分享试衣效果给微信好友
- 分享到朋友圈

### 7. 管理仪表盘

- 系统数据统计和可视化展示
- 用户、衣物、照片、试衣次数等数据分析
- API使用情况追踪和配额管理
- 趋势分析和分布统计图表

## 技术栈

### 前端

- 微信小程序原生框架（WXML, WXSS, JavaScript）
- 微信开发者工具

### 后端

- PHP 7.4+
- MySQL 5.7+
- RESTful API设计
- JWT认证
- 阿里云OSS SDK
- 阿里云OutfitAnyone API

### 依赖库

项目使用了以下第三方依赖库（位于vendor目录）：

- **aliyuncs/oss-sdk-php**: 阿里云对象存储服务(OSS)的官方PHP SDK，用于图片上传、下载和管理
- **alibabacloud/client**: 阿里云开放API的PHP SDK，用于调用阿里云各种服务API
- **alibabacloud/imageseg-20191230**: 阿里云图像分割服务SDK，提供服饰分割等AI能力
- **guzzlehttp/guzzle**: 强大的HTTP客户端，用于API调用和网络请求
- **symfony/http-foundation**: Symfony框架的HTTP基础组件，提供请求和响应对象
- **symfony/deprecation-contracts**: Symfony废弃特性约定
- **symfony/polyfill-mbstring**: Symfony多字节字符串处理兼容库
- **psr/http-message**: PSR-7 HTTP消息接口
- **psr/http-client**: PSR-18 HTTP客户端接口
- **psr/http-factory**: PSR-17 HTTP工厂接口
- **lizhichao/one-sm**: 一个轻量级的状态机实现库
- **ralouphie/getallheaders**: HTTP头获取兼容库
- **adbario/php-dot-notation**: 使用点符号访问和修改PHP数组的库

### 阿里云图像服务

项目使用了以下阿里云图像服务：

- **服饰分割(SegmentCloth)**: 用于精确提取衣物轮廓，去除背景
  - 支持本地文件处理(SegmentClothAdvance)和URL处理(SegmentCloth)
  - 能够处理各类服装，包括上衣、裤子、裙子、外套等
  - 返回透明背景的服装图像，便于后续虚拟试衣
- **虚拟试衣(OutfitAnyone)**: 用于AI生成试衣效果
  - 将分割后的服装图像与用户照片结合
  - 支持多种服装类型和穿着风格
  - 提供渐进式结果呈现，优化用户体验

### Vendor目录结构
```
vendor/
├── composer/             # Composer核心文件
├── aliyuncs/             # 阿里云OSS SDK
│   └── oss-sdk-php/
├── alibabacloud/         # 阿里云服务SDK
│   └── client/
├── guzzlehttp/           # HTTP客户端
│   ├── guzzle/
│   ├── promises/
│   └── psr7/
├── symfony/              # Symfony组件
│   ├── http-foundation/
│   ├── deprecation-contracts/
│   └── polyfill-mbstring/
├── psr/                  # PHP标准接口
│   ├── http-message/
│   ├── http-client/
│   └── http-factory/
├── lizhichao/            # 其他依赖库
│   └── one-sm/
├── ralouphie/            # HTTP头处理库
│   └── getallheaders/
├── adbario/              # 数组处理库
│   └── php-dot-notation/
└── autoload.php          # 自动加载文件
```

## 安装部署

### 前端部署

1. 克隆仓库
```
git clone https://github.com/yourusername/virtual-wardrobe.git
```

2. 使用微信开发者工具打开`miniprogram`目录
3. 在`project.config.json`中配置您的AppID
4. 在开发者工具中编译并预览

### 后端部署

1. 确保服务器环境满足要求：
   - PHP 7.4+
   - MySQL 5.7+
   - Composer

2. 部署`login_backend`目录到Web服务器

3. 安装依赖

一次性安装所有依赖（推荐）：
```bash
# 进入项目根目录
cd /path/to/cyyg.alidog.cn

# 安装所有依赖
composer install
```


或者手动安装各依赖包：
```bash

# 安装所阿里巴巴抠图sdk
composer require alibabacloud/imageseg-20191230

# 进入项目根目录
cd /path/to/cyyg.alidog.cn

# 安装阿里云OSS SDK
composer require aliyuncs/oss-sdk-php:^2.5

# 安装阿里云服务SDK
composer require alibabacloud/client:^1.5

# 安装HTTP客户端
composer require guzzlehttp/guzzle:^7.0

# 安装Symfony组件
composer require symfony/http-foundation
composer require symfony/deprecation-contracts
composer require symfony/polyfill-mbstring

# 安装PSR接口
composer require psr/http-message
composer require psr/http-client
composer require psr/http-factory

# 安装其他依赖
composer require lizhichao/one-sm
composer require ralouphie/getallheaders
composer require adbario/php-dot-notation
```

注意：安装依赖时应确保`composer.json`文件位于项目根目录，而非`login_backend`目录内

4. 配置数据库
   - 创建数据库
   - 导入`setup.sql`、`create_photos_table.sql`和`create_try_on_history_table.sql`

5. 配置`config.php`
   - 更新数据库连接信息
   - 配置微信小程序参数
   - 配置阿里云OSS和API参数

6. 确保目录权限
```
chmod -R 755 login_backend
chmod -R 777 login_backend/uploads
```

7. 配置Web服务器（Apache/Nginx）指向后端目录

### 阿里云OSS配置

1. 创建OSS存储桶（Bucket）
2. 配置Bucket访问权限为"公共读"
3. 配置跨域访问规则
4. 在`config.php`中更新OSS配置信息

### 阿里云服饰分割API配置

1. 创建阿里云账号并开通视觉智能开放平台服务
   - 访问 [阿里云视觉智能开放平台](https://www.aliyun.com/product/visionsandbox)
   - 开通图像分割服务，获取服务访问权限

2. 安装SDK依赖
   ```bash
   # 进入项目根目录
   cd /path/to/cyyg.alidog.cn
   
   # 安装服饰分割SDK
   composer require alibabacloud/imageseg-20191230
   ```

3. 在`config.php`中配置阿里云访问密钥
   ```php
   // 阿里云API配置
   define('ALIYUN_ACCESS_KEY_ID', 'your_access_key_id');  // 替换为实际的阿里云AccessKey ID
   define('ALIYUN_ACCESS_KEY_SECRET', 'your_access_key_secret');  // 替换为实际的阿里云AccessKey Secret
   ```

4. 服饰分割API使用
   - 系统会自动在用户上传服装图片时调用服饰分割API
   - 抠图结果会自动上传到OSS存储桶中
   - 您可以在`login_backend/debug.log`文件中查看API调用日志和状态

5. 测试服饰分割功能
   ```bash
   # 测试图像分割功能
   php login_backend/test_segment_cloth.php
   ```

6. 配额管理
   - 留意阿里云控制台中的API调用配额
   - 在管理后台可以查看API调用计数和剩余配额
   - 推荐设置配额预警，避免超出限制

## API文档

### 认证相关

#### 登录
- **URL**: `/login.php`
- **方法**: POST
- **参数**:
  - `code`: 微信登录凭证
- **响应**: 
  ```json
  {
    "error": false,
    "data": {
      "token": "xxx.yyy.zzz",
      "user_id": 1,
      "is_new_user": false
    }
  }
  ```

#### 验证Token
- **URL**: `/verify_token.php`
- **方法**: GET
- **头部**: Authorization: <token>
- **响应**: 
  ```json
  {
    "error": false,
    "data": {
      "id": 1,
      "nickname": "用户昵称",
      "avatar_url": "头像URL",
      "gender": 1,
      "created_at": "2023-03-31 12:00:00"
    }
  }
  ```

### 衣物管理

#### 获取衣物列表
- **URL**: `/get_clothes.php`
- **方法**: GET
- **头部**: Authorization: <token>
- **参数**:
  - `category` (可选): 过滤特定类别
  - `id` (可选): 特定衣物ID
- **响应**: 
  ```json
  {
    "error": false,
    "data": [
      {
        "id": 1,
        "name": "黑色T恤",
        "category": "tops",
        "image_url": "https://example.com/image.jpg",
        "tags": "summer,casual",
        "description": "{\"color\":\"black\",\"brand\":\"Example\",\"price\":\"99\"}",
        "created_at": "2023-03-31 12:00:00"
      }
    ]
  }
  ```

#### 添加/更新衣物
- **URL**: `/add_clothing.php`
- **方法**: POST
- **头部**: Authorization: <token>
- **参数**:
  - `id` (可选): 更新时的衣物ID
  - `name`: 衣物名称
  - `category`: 类别
  - `image_url`: 图片URL
  - `tags` (可选): 标签
  - `description` (可选): 描述
- **响应**: 
  ```json
  {
    "error": false,
    "data": {
      "id": 1,
      "name": "黑色T恤",
      "category": "tops",
      "image_url": "https://example.com/image.jpg",
      "tags": "summer,casual",
      "description": "{\"color\":\"black\",\"brand\":\"Example\",\"price\":\"99\"}",
      "created_at": "2023-03-31 12:00:00"
    }
  }
  ```

#### 删除衣物
- **URL**: `/delete_clothing.php`
- **方法**: POST
- **头部**: Authorization: <token>
- **参数**:
  - `id`: 衣物ID
- **响应**: 
  ```json
  {
    "error": false,
    "msg": "衣物已成功删除"
  }
  ```

### 照片管理

#### 获取照片列表
- **URL**: `/get_photos.php`
- **方法**: GET
- **头部**: Authorization: <token>
- **参数**:
  - `id` (可选): 特定照片ID
- **响应**: 
  ```json
  {
    "error": false,
    "data": [
      {
        "id": 1,
        "image_url": "https://example.com/photo.jpg",
        "type": "full",
        "description": "Beach day",
        "created_at": "2023-03-31 12:00:00"
      }
    ]
  }
  ```

#### 上传照片
- **URL**: `/upload_photo.php`
- **方法**: POST
- **头部**: Authorization: <token>
- **参数**:
  - `image`: 照片文件
  - `type` (可选): 照片类型
  - `description` (可选): 描述
- **响应**: 
  ```json
  {
    "error": false,
    "data": {
      "id": 1,
      "image_url": "https://example.com/photo.jpg",
      "type": "full",
      "description": "Beach day",
      "created_at": "2023-03-31 12:00:00"
    }
  }
  ```

#### 删除照片
- **URL**: `/delete_photo.php`
- **方法**: POST/GET
- **头部**: Authorization: <token>
- **参数**: 
  - `id`: 照片ID
- **响应**: 
  ```json
  {
    "error": false,
    "msg": "照片已成功删除"
  }
  ```

### 服饰抠图接口

#### 上传并抠图处理
- **URL**: `/upload_image.php`
- **方法**: POST
- **头部**: Authorization: <token>
- **参数**:
  - `image`: 服装图片文件 (multipart/form-data)
  - `segment_image`: 是否执行抠图（可选，默认为true）
- **响应**: 
  ```json
  {
    "error": false,
    "data": {
      "image_url": "https://example.com/uploads/segmented/segmented_clothing_1_1677649012_1234.jpg",
      "original_url": "https://example.com/uploads/clothing_1_1677649012_1234.jpg"
    }
  }
  ```

#### 抠图开关功能
系统现已支持用户控制抠图功能的开启和关闭，通过界面上的抠图开关实现：

1. **功能特点**：
   - 默认上传不执行抠图，保留原图
   - 用户可通过开关选择是否对图片进行抠图处理
   - 支持随时切换抠图状态，无需重新上传
   - 保存时根据开关状态自动选择正确图片URL

2. **使用方法**：
   - 上传图片后，图片预览区下方会显示抠图开关
   - 打开开关后，系统自动执行服饰抠图处理
   - 抠图完成后，界面显示抠图后的图片
   - 关闭开关可恢复显示原图

3. **技术实现**：
   - 前端通过`segment_image`参数控制是否执行抠图
   - 初次上传图片时默认不执行抠图，保存原图URL
   - 用户打开抠图开关时，发送独立请求执行抠图处理
   - 服务端支持标准文件上传和JSON请求两种抠图方式
   - 抠图结果和原图URL均保存在前端状态中，便于切换

4. **优势**：
   - 提升用户体验，让用户可自主选择是否需要抠图
   - 减少不必要的API调用，节约抠图配额
   - 避免对不适合抠图的服装图片进行处理
   - 支持比较抠图前后效果，帮助用户做出选择

5. **JSON抠图请求**
- **URL**: `/upload_image.php`
- **方法**: POST
- **头部**: 
  - Authorization: <token>
  - Content-Type: application/json
- **参数**:
  ```json
  {
    "image_url": "https://example.com/uploads/clothing_1_1677649012_1234.jpg",
    "segment_image": true
  }
  ```
- **响应**: 
  ```json
  {
    "error": false,
    "data": {
      "image_url": "https://example.com/uploads/segmented/segmented_clothing_1_1677649012_1234.jpg",
      "original_url": "https://example.com/uploads/clothing_1_1677649012_1234.jpg"
    }
  }
  ```

#### 处理流程说明
1. 接口自动保存上传的原始图片
2. 根据`segment_image`参数决定是否执行抠图处理
3. 调用阿里云服饰分割API进行背景移除
4. 保存处理后的图片并返回URL
5. 自动将图片同步至OSS存储
6. 在处理失败时返回原始图片URL

#### 错误处理
- 上传失败: `{"error": true, "msg": "No image file uploaded or upload error"}`
- 无效文件类型: `{"error": true, "msg": "Invalid file type. Only JPEG, PNG, GIF, and WEBP are allowed."}`
- 验证失败: `{"error": true, "msg": "Invalid or expired token"}`

#### 支持的图片格式
- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)

### 虚拟试衣

#### 试衣
- **URL**: `/try_on.php`
- **方法**: POST
- **头部**: Authorization: <token>
- **参数**:
  - `photo_id`: 照片ID
  - `clothes_ids`: 衣物ID数组
- **响应**: 
  ```json
  {
    "error": false,
    "data": {
      "result_image_url": "https://example.com/result.jpg",
      "task_id": "xxxxxxx",
      "status": "success",
      "clothes": [...]
    }
  }
  ```

#### 获取试衣历史
- **URL**: `/get_try_on_history.php`
- **方法**: GET
- **头部**: Authorization: <token>
- **参数**: 
  - `page` (可选): 页码
  - `per_page` (可选): 每页数量
- **响应**: 
  ```json
  {
    "error": false,
    "data": [
      {
        "id": 1,
        "result_image_url": "https://example.com/result.jpg",
        "clothes_ids": [39, 44],
        "created_at": "2023-04-01 12:00:00"
      }
    ],
    "pagination": {
      "total": 20,
      "page": 1,
      "per_page": 10,
      "total_pages": 2
    }
  }
  ```

### 管理后台API

#### 获取仪表盘数据
- **URL**: `/get_dashboard_data.php`
- **方法**: GET
- **头部**: Authorization: Bearer <token>
- **响应**: 
  ```json
  {
    "status": "success",
    "message": "获取仪表盘数据成功",
    "data": {
      "users": {
        "total": 100,
        "newUsers": 15,
        "genderDistribution": [
          {"gender": 0, "count": 25},
          {"gender": 1, "count": 35},
          {"gender": 2, "count": 40}
        ]
      },
      "clothing": {
        "total": 350,
        "newClothes": 42,
        "categoryDistribution": [
          {"category": "tops", "count": 120},
          {"category": "pants", "count": 85},
          {"category": "skirts", "count": 45},
          {"category": "coats", "count": 50},
          {"category": "accessories", "count": 30},
          {"category": "shoes", "count": 20}
        ]
      },
      "photos": {
        "total": 280,
        "newPhotos": 30,
        "typeDistribution": [
          {"type": "full", "count": 150},
          {"type": "half", "count": 100},
          {"type": "other", "count": 30}
        ]
      },
      "tryOn": {
        "total": 420,
        "newTryOns": 65,
        "statusDistribution": [
          {"status": "success", "count": 380},
          {"status": "failed", "count": 25},
          {"status": null, "count": 15}
        ]
      },
      "apiUsage": {
        "tryOn": {
          "used": 420,
          "total": 1000,
          "remaining": 580
        },
        "photoEdit": {
          "used": 280,
          "total": 800,
          "remaining": 520
        }
      },
      "dailyStats": [
        {
          "date": "2023-05-04",
          "users": 3,
          "clothes": 8,
          "photos": 5,
          "tryOns": 10
        },
        // ... 6 more days
      ]
    }
  }
  ```

## 项目迁移教程

本章节详细介绍如何将次元衣柜项目从本地文件存储迁移到阿里云OSS对象存储服务，以及其他可能的迁移场景。

### 图片存储迁移至阿里云OSS

#### 1. 迁移前准备

1. **阿里云账号准备**
   - 注册阿里云账号并开通OSS服务
   - 创建AccessKey ID和AccessKey Secret
   - 创建OSS存储桶(Bucket)，记录Bucket名称和地域(Endpoint)

2. **环境准备**
   - 确认PHP 7.4+环境
   - 安装Composer
   - 安装阿里云OSS SDK:
     ```bash
     cd /path/to/cyyg.alidog.cn
     composer require aliyuncs/oss-sdk-php:^2.5
     ```

3. **配置更新**
   - 在`config.php`中添加以下配置:
     ```php
     // 阿里云OSS配置
     define('ALIYUN_OSS_ENDPOINT', 'oss-cn-shanghai.aliyuncs.com');  // OSS服务的Endpoint
     define('ALIYUN_OSS_BUCKET', 'cyymj');  // OSS Bucket名称
     define('ALIYUN_OSS_BUCKET_DOMAIN', 'cyymj.oss-cn-shanghai.aliyuncs.com');  // Bucket域名

     // OSS存储路径前缀
     define('OSS_PATH_CLOTHES', 'clothes/');  // 衣物图片存储路径
     define('OSS_PATH_PHOTOS', 'photos/');    // 用户照片存储路径
     define('OSS_PATH_TRY_ON', 'try_on/');    // 试衣结果图片存储路径
     ```

#### 2. 代码更新

下列文件已进行功能修改以支持OSS存储:

1. **oss_helper.php** (新增)
   - 封装了OSS操作的辅助类
   - 提供上传、下载、删除文件及URL处理等功能

2. **upload_photo.php**
   - 修改为直接上传图片到OSS而非本地服务器
   - 更新路径生成逻辑，符合OSS命名规范

3. **delete_photo.php**
   - 修改为同时删除OSS中的图片
   - 添加OSS对象删除功能

4. **add_clothing.php**
   - 修改为将非OSS图片URL自动下载并上传到OSS
   - 更新图片URL存储逻辑

5. **delete_clothing.php**
   - 修改为同时删除OSS中的图片
   - 添加检查是否为OSS URL的逻辑

6. **try_on.php**
   - 修改为将试衣结果保存到OSS
   - 更新结果图片URL生成逻辑

#### 3. 历史数据迁移

项目提供了专用的迁移脚本`migrate_to_oss.php`来将现有图片数据迁移到OSS:

1. **迁移前备份**
   - 备份数据库:
     ```bash
     mysqldump -u username -p cyyg > cyyg_backup.sql
     ```
   - 备份上传目录:
     ```bash
     cp -r /path/to/login_backend/uploads /path/to/uploads_backup
     ```

2. **运行迁移脚本**
   - Linux环境:
     ```bash
     cd /path/to/cyyg.alidog.cn/login_backend
     php migrate_to_oss.php
     ```
   - Windows环境:
     ```powershell
     cd "path\to\cyyg.alidog.cn\login_backend"
     php migrate_to_oss.php
     ```

3. **迁移过程**
   迁移脚本执行以下操作:
   - 迁移`photos`表中的本地照片到OSS
   - 迁移`clothes`表中的衣物图片到OSS
   - 迁移`try_on_history`表中的试衣结果图片到OSS
   - 更新数据库中的图片URL为OSS URL

4. **迁移验证**
   - 确认数据库中的图片URL已更新为OSS格式
   - 通过应用访问图片，确认可以正常显示
   - 测试新的上传和删除功能是否正常

#### 4. 配置微信小程序

1. **更新域名白名单**
   - 登录微信公众平台
   - 进入"开发"->"开发管理"->"开发设置"->"服务器域名"
   - 添加以下域名到"request合法域名"和"downloadFile合法域名":
     ```
     https://cyymj.oss-cn-shanghai.aliyuncs.com
     ```

2. **重启开发环境**
   - 重启微信开发者工具
   - 清除缓存并重新编译小程序

#### 5. OSS存储配置优化

1. **存储桶访问控制**
   - 设置Bucket访问权限为"公共读"，确保图片可公开访问
   - 步骤:
     1. 登录阿里云控制台，进入OSS服务
     2. 选择Bucket，点击"基础设置"
     3. 在"读写权限"区域选择"公共读"并保存

2. **跨域访问设置**
   - 配置允许跨域访问:
     1. 在OSS控制台选择Bucket，点击"基础设置"
     2. 在"跨域设置"区域添加规则:
        - 来源: `*` (或限定为您的网站域名)
        - 允许Methods: `GET`
        - 允许Headers: `*`
        - 缓存时间: `86400`
        - 返回Vary: Origin: `是`

3. **生命周期规则** (可选)
   - 配置自动清理长期未访问的文件:
     1. 在OSS控制台选择Bucket，点击"基础设置"
     2. 在"生命周期"区域添加规则
     3. 可设置试衣结果图片30天后自动删除等策略

#### 6. 常见问题与解决方案

1. **OSS图片无法访问**
   - 检查Bucket权限设置是否为"公共读"
   - 验证OSS域名是否已添加到微信小程序白名单
   - 检查图片URL格式是否正确

2. **上传失败**
   - 检查AccessKey权限和状态
   - 确认OSS配置参数是否正确
   - 检查PHP是否有足够的内存限制和执行时间

3. **迁移失败**
   - 检查网络连接稳定性
   - 确保源图片可访问
   - 查看PHP错误日志，定位具体错误

4. **微信小程序图片显示失败**
   - 清除小程序缓存并重新编译
   - 确认图片URL协议为HTTPS
   - 检查图片格式是否兼容(支持JPG、PNG、WEBP等)

5. **路径不一致问题**
   - 如遇类似`"Use of undefined constant ALIYUN_OSS_ENDPOINT"`错误，检查配置文件是否正确引入
   - 确保所有引用OSS命名空间的文件都正确引入了autoload.php

#### 7. 回滚方案

如需回滚到本地存储:

1. **恢复代码**
   - 恢复原有的文件上传和处理代码
   - 移除OSS相关代码

2. **数据回滚**
   - 数据库中图片URL已被修改为OSS链接，需要批量替换回本地URL
   - 可编写回滚脚本，从OSS下载图片到本地，并更新数据库URL

3. **完全回滚**
   - 如已备份，可恢复备份的数据库和上传目录
   - 重新部署不含OSS集成的代码版本

### 其他迁移场景

#### 服务器迁移

1. **环境准备**
   - 确保新服务器满足PHP 7.4+和MySQL 5.7+要求
   - 安装所需扩展(如GD库、cURL等)

2. **文件迁移**
   - 复制完整的项目目录到新服务器
   - 保持目录结构和权限一致

3. **数据库迁移**
   - 导出原数据库:
     ```bash
     mysqldump -u username -p cyyg > cyyg_backup.sql
     ```
   - 在新服务器创建数据库并导入:
```