<view class="container">
  <!-- 顶部固定区域 -->
  <view class="fixed-header">
  <!-- 搜索框 -->
  <view class="search-container">
    <view class="search-box">
      <image class="search-icon" src="/images/search.png"></image>
      <input 
        class="search-input" 
        placeholder="请输入穿搭关键词" 
        placeholder-class="search-placeholder"
        bindinput="onSearchInput"
        bindconfirm="onSearchConfirm"
        confirm-type="search"
        value="{{searchKeyword}}"
      />
      <view class="search-clear" bindtap="clearSearch" wx:if="{{searchKeyword}}">×</view>
    </view>
  </view>
  </view>
  

  
  <!-- 穿搭列表 -->
  <view 
    class="outfits-container" 
    wx:if="{{!loading && !isEmpty}}">
    <view class="outfits-grid">
      <view 
        wx:for="{{outfits}}" 
        wx:key="id" 
        class="outfit-item" 
        data-id="{{item.id}}" 
        data-user-id="{{item.user_id}}"
        bindtap="viewOutfit">
        
        <!-- 穿搭预览图 -->
        <view class="outfit-preview">
          <block wx:if="{{item.thumbnail}}">
            <image src="{{item.thumbnail}}" mode="aspectFill" class="outfit-thumbnail"></image>
          </block>
          <block wx:else>
            <!-- 穿搭视图，显示所有衣物 -->
            <view class="outfit-placeholder" wx:if="{{item.items.length > 0}}">
              <!-- 创建一个与详情页类似的outfit-view -->
              <view class="outfit-view-mini">
                <view 
                  wx:for="{{item.items}}" 
                  wx:for-item="clothingItem" 
                  wx:key="clothing_id"
                  class="outfit-item-mini"
                  style="left: {{clothingItem.previewPosition.x}}px; top: {{clothingItem.previewPosition.y}}px; width: {{clothingItem.size.width * clothingItem.previewPosition.scale}}px; height: {{clothingItem.size.height * clothingItem.previewPosition.scale}}px; transform: rotate({{clothingItem.rotation}}deg); z-index: {{clothingItem.z_index}};">
                  <image src="{{clothingItem.clothing_data.image_url}}" mode="aspectFit" class="item-image-mini"></image>
                </view>
              </view>

            </view>
            <view class="outfit-no-items" wx:else>
              <text class="iconfont icon-outfit"></text>
            </view>
          </block>
        </view>
        
        <!-- 穿搭信息 -->
        <view class="outfit-info">
          <view class="outfit-name-container">
            <view class="outfit-name">{{item.name}}</view>
          </view>
          <view class="outfit-creator-container">
            <view class="creator-info">
              <image class="creator-avatar" src="{{item.creator_avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
              <text class="creator-name">{{item.creator_nickname || '匿名用户'}}</text>
            </view>
            <view class="like-info">
              <image src="/images/dianzan.png" class="like-icon"></image>
              <text class="like-count">{{item.likes_count || 0}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view class="loading-more" wx:if="{{loadingMore}}">
      <view class="loading-spinner-small"></view>
      <view class="loading-more-text">加载更多...</view>
    </view>
    <!-- 手动加载更多按钮 -->
    <view class="manual-load-more" wx:if="{{hasMoreData && !loadingMore && outfits.length >= 20}}" bindtap="loadMore">
      <view class="manual-load-text">点击加载更多</view>
    </view>
    <view class="loading-end" wx:if="{{!hasMoreData && outfits.length > 0 && !loadingMore}}">
      已显示全部穿搭
    </view>
    
    <!-- 底部安全区域占位 -->
    <view class="safe-area-bottom"></view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{isEmpty && !loading}}">
    <view class="empty-icon">👔</view>
    <view class="empty-text">{{isSearchMode ? '未找到匹配穿搭' : '暂无公开穿搭'}}</view>
    <view class="empty-desc">{{isSearchMode ? '没有找到与"'+searchKeyword+'"相关的穿搭' : '当前没有用户公开的穿搭可供浏览'}}</view>
    <view class="empty-action" bindtap="clearSearch" wx:if="{{isSearchMode}}">清除搜索</view>
  </view>
</view> 