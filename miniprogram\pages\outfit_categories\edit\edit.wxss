/* pages/outfit_categories/edit/edit.wxss */
.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
  text-align: center;
}

.form-group {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input-container {
  position: relative;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  box-sizing: border-box;
}

.counter {
  position: absolute;
  bottom: 10rpx;
  right: 20rpx;
  font-size: 24rpx;
  color: #999999;
}

/* 按钮组与穿搭页一致 */
.button-group {
  display: flex;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  margin-top: 60rpx;
}

.cancel-button, .submit-button {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin: 0 8px;
  border: none;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #333333;
}

.submit-button {
  background-color: #000000;
  color: #ffffff;
}

/* 禁用状态的按钮 */
.submit-button[disabled] {
  opacity: 0.6;
}

.button-hover {
  opacity: 0.8;
}