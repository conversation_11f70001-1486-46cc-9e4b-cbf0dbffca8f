# 次元衣柜 (Virtual Wardrobe) - 开发者文档 (2025-04-18)

## 1. 项目概述

次元衣柜是一款基于微信小程序的虚拟试衣应用。用户可以上传自己的个人照片，浏览并选择存储在云端的服装图片，通过集成的 AI 试衣接口（阿里云 OutfitAnyone）生成虚拟试衣效果图。项目还支持用户创建和管理自己的衣橱分类，以及**保存和浏览服装搭配组合（穿搭）**。**此外，项目还集成了基于AI的智能穿搭推荐功能，能够根据天气、季节等条件从用户的衣橱（包括自定义分类）中智能推荐合适的穿搭组合**。**同时，项目新增淘宝客功能，支持推荐服饰商品并生成淘口令，实现商品分享和购买**。**最新版本集成了付费的个人形象分析功能，用户可以上传照片，通过AI分析获得专业的体型、肤色、风格等报告**。项目旨在提供便捷、有趣的线上服装搭配体验。

## 2. 系统架构

本项目采用前后端分离的架构：

*   **前端 (Client)**: 微信小程序，负责用户界面展示、用户交互、向后端发起 API 请求。
*   **后端 (Backend)**: 基于 PHP 实现的 RESTful API 服务，负责处理业务逻辑、数据持久化、与第三方服务（数据库、阿里云服务、**Gemini API代理**、**淘宝联盟API**、**微信支付**）交互，**以及管理和提供推荐穿搭数据**，**以及提供基于AI的智能穿搭推荐服务**。
*   **数据库 (Database)**: MySQL 数据库，用于存储用户信息、衣物数据、照片数据、试衣历史、**用户穿搭数据**、**个人形象分析记录和订单**，**以及推荐穿搭及其分类和统计数据**。
*   **云服务 (Cloud Services)**:
    *   阿里云 OSS (Object Storage Service): 用于存储所有图片资源（衣物图、用户照片、试衣结果图），提供高可用性和可扩展性。**项目已配置并启用阿里云 CDN 加速 OSS 访问 (`images.alidog.cn`)**。
    *   阿里云 视觉智能开放平台 API:
        *   `SegmentCloth`: 用于服装图片抠图（背景移除）。**已修复兼容 CDN URL 的问题**。
        *   `SegmentCommonImage`: 通用分割能力，可识别图像中视觉中心的物体轮廓进行抠图，支持多种返回形式。**项目已集成支持，可根据后端配置选择使用。**
        *   `OutfitAnyone`: 用于核心的 AI 虚拟试衣。
*   **第三方 API/服务**:
    *   **Gemini API Proxy**: 一个中转服务，用于安全、便捷地调用 Google Gemini API 进行图片分析（如衣物识别、**个人形象分析**）。项目后端通过调用此服务与 Gemini API 交互。
    *   **淘宝联盟 API**: 用于获取淘宝商品数据和生成淘口令，支持商品推荐和用户购买。
    *   **微信支付 API**: 用于处理个人形象分析功能的付费流程。

**架构交互图:**

```
+--------------------+      +-----------------------+      +-----------------+      +-----------------------+
|  微信小程序 (前端)  |----->|  PHP RESTful API (后端) |----->|  MySQL 数据库   |      |  Gemini API Proxy     |
+--------------------+      +-----------------------+      +-----------------+      +-----------------------+
        |                          |        |                                              ^
        |                          |        v                                              |
        |                          |   +-----------------+  <-- (用于衣物删除前的穿搭关联检查)                   |
        |                          |   | 阿里云 AI API   |                                              |
        |                          |   | (Segment/Outfit) |                                              |
        |                          |   +-----------------+                                              |
        |                          |        |                                                           |
        v                          v        v                                                           |
+-----------------+      +-----------------+      +-----------------+                                 |
|   微信支付 API  |      |  阿里云 OSS     |      | 淘宝联盟 API    | ----------------------------------
|   (处理支付)    |      |  (图片存储)      |      | (商品&淘口令)    |
+-----------------+      +-----------------+      +-----------------+
```

## 3. 技术栈

*   **前端**: 
    *   微信小程序原生框架 (WXML, WXSS, JavaScript)
    *   微信开发者工具
*   **后端**:
    *   PHP 7.4+
    *   Composer (依赖管理)
    *   RESTful API 设计
    *   JSON Web Tokens (JWT) (用于用户认证)
*   **数据库**:
    *   MySQL 5.7+
*   **云服务/第三方库**:
    *   阿里云 OSS SDK for PHP (`aliyuncs/oss-sdk-php`)
    *   阿里云 视觉智能开放平台 SDK (`alibabacloud/imageseg-20191230`, `alibabacloud/client`)
    *   阿里云 淘宝联盟 SDK (`taobao-sdk-PHP`)
    *   Guzzle HTTP Client (`guzzlehttp/guzzle`) (用于调用 AI API)
    *   Firebase PHP-JWT (`firebase/php-jwt`) (推测用于 JWT 处理，基于 `auth.php` 等)
    *   其他 Composer 依赖 (Symfony components, PSR interfaces等)

## 4. 目录结构

```
.
├── admin/                       # Web 管理后台界面 ★
│   ├── css/                     #   - 管理后台 CSS 样式文件目录
│   ├── js/                      #   - 管理后台 JavaScript 逻辑文件目录
│   │   ├── recommended_categories.js #   - 推荐穿搭分类列表页面逻辑
│   │   ├── recommended_category_edit.js #   - 推荐穿搭分类编辑/添加页面逻辑
│   │   ├── recommended_outfit_edit.js #   - 推荐穿搭编辑/添加页面逻辑
│   │   ├── recommended_outfits.js #   - 推荐穿搭列表页面逻辑
│   │   └── auth.js              #   - 管理后台通用认证逻辑
│   ├── images/                  #   - 管理后台使用的静态图片资源目录
│   ├── index.html               #   - 管理后台登录页面 HTML
│   ├── dashboard.html           #   - 管理后台仪表盘页面 HTML
│   ├── user_list.html           #   - 用户列表页面 HTML (管理后台)
│   ├── user_details.html        #   - 用户详情页面 HTML (管理后台)
│   ├── clothing_list.html       #   - 衣物列表页面 HTML (管理后台)
│   ├── clothing_details.html    #   - 衣物详情页面 HTML (管理后台)
│   ├── clothing_edit.html       #   - 衣物编辑页面 HTML (管理后台)
│   ├── photo_list.html          #   - 照片列表页面 HTML (管理后台)
│   ├── photo_details.html       #   - 照片详情页面 HTML (管理后台)
│   ├── photo_edit.html          #   - 照片编辑页面 HTML (管理后台)
│   ├── try_on_list.html         #   - 试衣历史列表页面 HTML (管理后台)
│   ├── try_on_details.html      #   - 试衣历史详情页面 HTML (管理后台)
│   ├── system_settings.html     #   - 系统设置页面 HTML (管理后台)
│   ├── recommended_category_list.html #   - 推荐穿搭分类列表页面 HTML
│   ├── recommended_category_edit.html #   - 推荐穿搭分类编辑/添加页面 HTML
│   ├── recommended_outfit_list.html #   - 推荐穿搭列表页面 HTML
│   ├── recommended_outfit_edit.html #   - 推荐穿搭编辑/添加页面 HTML
│   └── system_settings.html     #   - 系统设置页面 HTML (管理后台)
│
├── login_backend/               # PHP 后端 API 代码 ★
│   ├── uploads/                 #   - (已废弃/历史遗留) 本地图片上传目录
│   ├── vendor/                  #   - Composer 安装的 PHP 依赖库目录
│   ├── .htaccess                #   - Apache Web服务器的配置/重写规则 (用于美化 URL 或目录保护)
│   ├── config.php               #   - ★ 核心配置文件 (数据库连接, 微信 AppID/Secret, 阿里云 Keys/Endpoints/Bucket/CDN, Admin Secret Key 等)
│   ├── db.php                   #   - 数据库连接辅助脚本 (提供获取数据库连接实例的功能)
│   ├── auth.php                 #   - ★ 小程序用户 JWT 认证与Token验证逻辑 (包含 `verifyToken` 等函数)
│   ├── oss_helper.php           #   - ★ 阿里云 OSS 操作封装类 (提供上传、删除、URL判断/转换、生成签名URL等方法)
│   ├── wardrobe_logger.php      #   - ★ 衣橱相关功能的日志记录工具 (输出到 ../logs/ 目录)
│   ├── login.php                #   - 处理小程序用户登录 API (接收 code, 获取 openid/session_key, 返回 token)
│   ├── verify_token.php         #   - 验证小程序用户 Token 有效性 API (供前端检查 token 是否过期)
│   ├── update_profile.php       #   - 更新小程序用户信息 API (接收昵称、头像等)
│   ├── add_clothing.php         #   - 添加/更新衣物信息 API (处理图片上传到 OSS, ★正确处理并关联衣橱ID, 未指定则关联默认衣橱)
│   ├── get_clothes.php          #   - 获取用户衣物列表 API (支持分类和衣橱筛选)
│   ├── delete_clothing.php      #   - 删除指定衣物 API (同时删除 OSS 图片)
│   ├── upload_photo.php         #   - ★ (旧流程?) 上传用户照片到 OSS API
│   ├── get_photos.php           #   - 获取用户照片列表 API
│   ├── delete_photo.php         #   - 删除用户照片 API (同时删除 OSS 图片)
│   ├── upload_image.php         #   - ★ (旧流程?/JSON请求) 上传衣物图片 API (含调用抠图 API[已修复CDN兼容性], 上传原始图和抠图结果到 OSS)
│   ├── get_oss_upload_token.php #   - ★ [新流程] 获取 OSS 直传凭证/签名 URL API
│   ├── oss_upload_callback.php  #   - ★ [新流程] OSS 上传成功后的回调处理 API (触发抠图等)
│   ├── try_on.php               #   - ★ 发起虚拟试衣请求 API (接收照片和衣物 ID, 调用 OutfitAnyone, 返回 task_id)
│   ├── get_try_on_status.php    #   - ★ 查询异步试衣任务状态 API (接收 task_id, 查询阿里云状态)
│   ├── get_try_on_history.php   #   - 获取用户试衣历史记录 API (分页)
│   ├── add_wardrobe.php         #   - ★ 添加新衣橱 API (处理名称、描述、排序)
│   ├── get_wardrobes.php        #   - ★ 获取用户衣橱列表 API (返回排序、是否默认、衣物计数)
│   ├── update_wardrobe.php      #   - ★ 更新衣橱信息 API (处理名称、描述、排序)
│   ├── delete_wardrobe.php      #   - ★ 删除衣橱 API (不能删默认，衣物移至默认)
│   ├── move_clothes.php         #   - ★ 移动衣物到指定衣橱 API
│   ├── get_wardrobe_clothes.php #   - ★ 获取指定衣橱下的衣物列表 API (分页)
│   ├── save_outfit.php          #   - ★ 添加/更新穿搭 API
│   ├── get_outfits.php          #   - ★ 获取用户穿搭列表 API
│   ├── delete_outfit.php        #   - ★ 删除指定穿搭 API
│   ├── get_outfit_recommendation.php #   - ★ 智能穿搭推荐 API (支持自定义分类衣物获取，基于天气/季节/AI分析)
│   ├── create_image_analysis.php #   - ★ 创建新的个人形象分析请求 API
│   ├── get_image_analysis.php   #   - ★ 获取个人形象分析详情 API
│   ├── get_image_analysis_history.php # - ★ 获取个人形象分析历史记录 API
│   ├── update_image_analysis_payment.php # - ★ 更新形象分析记录的支付状态 API
│   ├── request_image_analysis.php #  - ★ 请求（触发）个人形象分析处理 API
│   ├── wx_pay_helper.php        #   - ★ 微信支付辅助类，用于生成支付参数和处理回调
│   ├── migrate_wardrobes.php    #   - 衣橱数据相关的迁移脚本 (可能用于初始化或版本更新)
│   ├── migrate_to_oss.php       #   - 将本地存储图片迁移到 OSS 的一次性工具脚本
│   ├── migrate_to_cdn_complete.php # - ★ 将数据库中 OSS URL 迁移到 CDN URL 的工具脚本 (支持--all)
│   ├── admin_login.php          #   - 管理后台管理员登录 API (验证用户名密码, 返回管理员 token)
│   ├── admin_verify_token.php   #   - 验证管理后台管理员 Token 有效性 API
│   ├── get_admin_users.php      #   - 获取用户列表 API (供管理后台, 含分页/搜索)
│   ├── get_admin_user_details.php # 获取指定用户详情 API (供管理后台)
│   ├── update_admin_user_status.php # 更新用户状态 API (启用/禁用用户, 供管理后台)
│   ├── get_admin_user_items.php #   - 获取特定用户项目(衣物/照片) API (可能用于后台用户详情页)
│   ├── get_admin_clothes.php    #   - (弃用/重复?) 获取衣物列表 (管理后台) - 应统一使用 list 结尾的 API
│   ├── get_admin_clothing_list.php # 获取衣物列表 API (供管理后台, 含分页/搜索)
│   ├── get_admin_clothing_details.php # 获取指定衣物详情 API (供管理后台)
│   ├── update_admin_clothing.php # 更新衣物信息 API (供管理后台)
│   ├── delete_admin_clothing.php # 删除指定衣物 API (供管理后台)
│   ├── get_admin_photos.php     #   - 获取照片列表 API (供管理后台, 含分页/搜索)
│   ├── get_admin_photo_details.php # 获取指定照片详情 API (供管理后台)
│   ├── update_admin_photo.php   #   - 更新照片信息 API (供管理后台)
│   ├── delete_admin_photo.php   #   - 删除指定照片 API (供管理后台)
│   ├── get_admin_try_on_history.php # 获取所有试衣历史 API (供管理后台, 含分页/搜索)
│   ├── get_admin_try_on_details.php # 获取指定试衣记录详情 API (供管理后台)
│   ├── delete_admin_try_on.php  #   - 删除指定试衣记录 API (供管理后台)
│   ├── get_dashboard_data.php   #   - 获取仪表盘统计数据 API (用户数, 衣物数, API使用量等)
│   ├── get_admin_system_settings.php # 获取系统设置 API (供管理后台读取配置)
│   ├── update_admin_system_settings.php # 更新系统设置 API (供管理后台修改配置)
│   ├── composer.json            #   - PHP 项目依赖配置文件 (定义项目所需的库)
│   ├── README_OSS_MIGRATION.md  #   - OSS 迁移相关的说明文档 (可能已过时)
│   ├── *.sql                    #   - (少量 SQL 文件也在此处，应统一移至 数据库/ 目录)
│   ├── create_recommended_outfits_tables.sql #   - ★ 创建推荐穿搭、商品和统计表脚本
│   └── *.sql                    #   - (少量 SQL 文件也在此处，应统一移至 数据库/ 目录)
│
├── miniprogram/                 # 微信小程序前端代码 ★
│   ├── pages/                   # 小程序页面目录 ★
│   │   ├── index/               #   - 首页 (衣橱展示) 页面
│   │   │   ├── index.js         #     - 处理首页衣橱展示逻辑，获取衣物数据，处理分类切换和瀑布流布局。
│   │   │   │                    #     **新增公告获取与展示逻辑。**
│   │   │   │                    #     **新增穿搭列表数据处理和显示居中优化逻辑。**
│   │   │   ├── index.wxml       #     - 定义首页的 WXML 结构，包括分类选项卡、衣物网格布局、添加衣物按钮。
│   │   │   │                    #     **新增公告弹窗 WXML 结构。**
│   │   │   │                    #     **优化穿搭列表预览显示结构，使用计算后的位置。**
│   │   │   ├── index.wxss       #     - 包含首页特定的 WXSS 样式规则。
│   │   │   │                    #     **优化穿搭列表预览相关的样式。**
│   │   │   └── index.json       #     - 配置首页导航栏标题 "次元衣帽间", 启用下拉刷新。
│   │   ├── login/               #   - 登录页面
│   │   │   ├── login.js         #     - 处理用户登录流程，包括隐私协议检查、授权、获取用户信息 (头像/昵称)。
│   │   │   ├── login.wxml       #     - 定义登录页面的 WXML 结构，包含 Logo、授权按钮、隐私协议弹窗、用户信息获取弹窗。
│   │   │   ├── login.wxss       #     - 包含登录页面特定的 WXSS 样式规则。
│   │   │   └── login.json       #     - 配置登录页导航栏标题 "登录", 禁用滚动。
│   │   ├── profile/             #   - 个人中心页面
│   │   │   ├── profile.js       #     - 处理个人中心逻辑，获取用户信息、衣物统计数据，处理页面跳转和退出登录。
│   │   │   ├── profile.wxml     #     - 定义个人中心页面的 WXML 结构，包含用户信息卡片、统计数据、功能入口列表。
│   │   │   ├── profile.wxss     #     - 包含个人中心页面特定的 WXSS 样式规则。
│   │   │   └── profile.json       #     - 配置个人中心页导航栏为自定义模式 (custom)。
│   │   ├── clothing/            #   - 衣物管理相关页面目录
│   │   │   ├── add/             #     - 添加衣物页面
│   │   │   │   ├── add.js       #       - 处理添加/上传衣物逻辑，包括图片选择、抠图开关、表单提交 (可能使用新OSS直传)。
│   │   │   │   ├── add.wxml     #       - 定义添加衣物页面的 WXML 结构，包含图片上传、表单输入项 (名称、分类、标签等)。
│   │   │   │   ├── add.wxss     #       - 包含添加衣物页面特定的 WXSS 样式规则。
│   │   │   │   └── add.json       #       - 配置添加衣物页导航栏标题 "添加衣物"。
│   │   │   ├── detail/          #     - 衣物详情页面
│   │   │   │   ├── detail.js    #       - 处理衣物详情展示逻辑，获取指定 ID 的衣物信息，处理编辑和删除操作。
│   │   │   │   │                    #       **新增删除前检查衣物是否在穿搭中使用逻辑。**
│   │   │   │   ├── detail.wxml  #       - 定义衣物详情页面的 WXML 结构，展示衣物图片、名称、分类、标签等信息。
│   │   │   │   │                    #       **优化删除按钮样式，添加图标。**
│   │   │   │   ├── detail.wxss  #       - 包含衣物详情页面特定的 WXSS 样式规则。
│   │   │   │   │                    #       **优化删除按钮样式。**
│   │   │   │   └── detail.json  #       - 配置衣物详情页导航栏标题 "衣物详情"。
│   │   │   ├── edit/            #     - 编辑衣物页面
│   │   │   │   ├── edit.js      #       - 处理编辑衣物逻辑，加载现有衣物信息，处理表单修改和提交。
│   │   │   │   │                    #       **新增穿搭名称输入处理。**
│   │   │   │   ├── edit.wxml    #       - 定义编辑衣物页面的 WXML 结构，与添加页面类似，但填充了现有数据。
│   │   │   │   │                    #       **新增穿搭名称输入框。**
│   │   │   │   ├── edit.wxss    #       - 包含编辑衣物页面特定的 WXSS 样式规则。
│   │   │   │   │                    #       **新增穿搭名称输入框样式。**
│   │   │   │   └── edit.json       #       - 配置编辑衣物页导航栏标题 "编辑衣物"。
│   │   │   ├── manage/          #     - 管理衣橱列表页面
│   │   │       ├── manage.js    #       - 处理衣橱管理逻辑，获取衣物列表，支持选择和批量删除操作【新增】支持将选中衣物移动到其他衣橱。
│   │   │       ├── manage.wxml  #       - 定义管理衣橱页面的 WXML 结构，展示带有多选框的衣物列表。
│   │   │       ├── manage.wxss  #       - 包含管理衣橱页面特定的 WXSS 样式规则。
│   │   │       └── manage.json       #       - 配置管理衣橱页导航栏标题 "管理衣橱"。
│   │   │   └── batch-upload/    #     - ★ 批量上传衣物页面
│   │   │       ├── batch-upload.js #       - ★ 处理批量图片选择、上传、AI分析、编辑和批量保存逻辑。包括调用后端API进行图片上传和AI分析，处理图片转Base64、响应解析，以及批量衣物数据的管理和保存。
│   │   │       ├── batch-upload.wxml #       - ★ 定义批量上传页面的 WXML 结构。包含上传状态显示区域、可滚动的衣物列表区域（展示衣物预览和编辑区域）、以及底部的批量保存按钮。
│   │   │       ├── batch-upload.wxss #       - ★ 包含批量上传页面特定的 WXSS 样式规则。定义了页面布局、状态区域、衣物列表项、编辑区域以及详情/收起按钮的样式。
│   │   │       └── batch-upload.json #       - ★ 配置批量上传页面的导航栏标题和自定义组件。
│   │   ├── photos/              #   - 用户照片管理相关页面目录
│   │   │   ├── index/           #     - 照片列表页面
│   │   │   │   ├── index.js     #       - 处理照片列表展示逻辑，获取用户上传的照片数据。
│   │   │   │   ├── index.wxml   #       - 定义照片列表页面的 WXML 结构，展示照片缩略图网格。
│   │   │   │   ├── index.wxss     #       - 包含照片列表页面特定的 WXSS 样式规则。
│   │   │   │   └── index.json       #       - 配置照片列表页导航栏标题 "我的照片"。
│   │   │   ├── add/             #     - 添加照片页面
│   │   │   │   ├── add.js       #       - 处理上传照片逻辑，包括图片选择和提交 (可能使用新OSS直传)。
│   │   │   │   ├── add.wxml     #       - 定义添加照片页面的 WXML 结构，包含图片上传控件和描述输入。
│   │   │   │   ├── add.wxss     #       - 包含添加照片页面特定的 WXSS 样式规则。
│   │   │   │   └── add.json       #       - 配置添加照片页导航栏标题 "添加照片"。
│   │   │   └── detail/          #     - 照片详情页面
│   │   │       ├── detail.js    #       - 处理照片详情展示逻辑，获取指定 ID 的照片信息，处理删除操作。
│   │   │       ├── detail.wxml    #       - 定义照片详情页面的 WXML 结构，展示照片大图和描述信息。
│   │   │       ├── detail.wxss    #       - 包含照片详情页面特定的 WXSS 样式规则。
│   │   │       └── detail.json       #       - 配置照片详情页导航栏标题 "照片详情"。
│   │   ├── try_on/              #   - 虚拟试衣相关页面目录
│   │   │   ├── index/           #     - 试衣主入口/Tab页
│   │   │   │   ├── index.js     #       - 作为试衣功能的入口，可能引导用户选择照片和衣物。
│   │   │   │   ├── index.wxml     #       - 定义试衣入口页面的 WXML 结构。
│   │   │   │   ├── index.wxss     #       - 包含试衣入口页面特定的 WXSS 样式规则。
│   │   │   │   └── index.json       #       - 可能无特殊配置或配置 TabBar 标题。
│   │   │   ├── select/          #     - 选择衣物/照片进行试衣页面
│   │   │   │   ├── index.js     #       - 处理选择照片和衣物的逻辑，获取照片和衣物列表供用户选择，【新增】支持按衣橱筛选衣物，发起试衣请求。**已优化每日试衣次数限制处理。**
│   │   │   │   ├── index.wxml     #       - 定义选择页面的 WXML 结构，展示照片和衣物列表供勾选，包含进度弹窗和次数限制提示弹窗。
│   │   │   │   ├── index.wxss     #       - 包含选择页面特定的 WXSS 样式规则。
│   │   │   │   └── index.json       #       - 配置选择页面导航栏标题 "选择试衣"。
│   │   │   ├── result/          #     - 显示试衣结果页面
│   │   │   │   ├── index.js     #       - 处理试衣结果展示逻辑，接收 task_id，轮询获取试衣状态和结果图片。
│   │   │   │   ├── index.wxml     #       - 定义结果页面的 WXML 结构，展示试衣进度、结果图片、分享/保存按钮。
│   │   │   │   ├── index.wxss     #       - 包含结果页面特定的 WXSS 样式规则 (可能包含加载动画样式)。
│   │   │   │   └── index.json       #       - 配置结果页面导航栏标题 "试衣结果"。
│   │   │   └── history/         #     - 试衣历史列表页面
│   │   │       ├── index.js     #       - 处理试衣历史列表逻辑，获取历史记录数据并分页展示。
│   │   │       ├── index.wxml     #       - 定义历史列表页面的 WXML 结构，展示试衣结果缩略图和时间。
│   │   │       ├── index.wxss     #       - 包含历史列表页面特定的 WXSS 样式规则。
│   │   │       └── index.json       #       - 配置历史列表页导航栏标题 "试衣历史"。
│   │   ├── wardrobes/           #   - ★ 衣橱分类管理页面目录
│   │   │   ├── index/           #     - 衣橱列表页面
│   │   │   │   ├── index.js     #       - ★ 处理衣橱列表展示逻辑，分页，处理增删改导航，【注意】删除逻辑与后端不一致。
│   │   │   │   ├── index.wxml     #       - ★ 定义衣橱列表页面的 WXML 结构，展示列表项及操作按钮。
│   │   │   │   ├── index.wxss     #       - ★ 包含衣橱列表页面特定的 WXSS 样式规则。
│   │   │   │   └── index.json       #       - ★ 配置衣橱列表页导航栏标题，启用下拉刷新。
│   │   │   ├── add/             #     - 添加新衣橱页面
│   │   │   │   ├── add.js       #       - ★ 处理添加新衣橱的逻辑 (发送名称/描述/排序)。
│   │   │   │   ├── add.wxml     #       - ★ 定义添加衣橱页面的 WXML 结构 (输入框等)。
│   │   │   │   ├── add.wxss     #       - ★ 包含添加衣橱页面特定的 WXSS 样式规则。
│   │   │   │   └── add.json       #       - ★ 配置添加衣橱页导航栏标题。
│   │   │   ├── edit/            #     - ★ 编辑衣橱页面
│   │   │   │   ├── edit.js      #       - ★ 处理编辑衣橱信息的逻辑 (接收并应发送名称/描述/排序)。**【注意】当前提交更新时仅发送 `id`, `name`, `description`，未发送 `sort_order`，需要修复。**
│   │   │   │   ├── edit.wxml     #       - ★ 定义编辑衣橱页面的 WXML 结构 (输入框等，填充现有数据)。
│   │   │   │   ├── edit.wxss     #       - ★ 包含编辑衣橱页面特定的 WXSS 样式规则。
│   │   │   │   └── edit.json       #       - ★ 配置编辑衣橱页导航栏标题。
│   │   │   └── detail/          #     - ★ 衣橱详情页面
│   │   │       ├── detail.js    #       - ★ 处理衣橱详情展示逻辑，获取衣橱信息及该衣橱下的衣物列表，瀑布流展示，跳转添加衣物。
│   │   │       ├── detail.wxml    #       - ★ 定义衣橱详情页面的 WXML 结构 (展示衣橱信息和衣物列表)。
│   │   │       ├── detail.wxss    #       - ★ 包含衣橱详情页面特定的 WXSS 样式规则。
│   │   │       └── detail.json       #       - ★ 配置衣橱详情页导航栏标题，启用下拉刷新。
│   │   ├── tutorial/            #   - 教程页面
│   │   │   ├── tutorial.js      #     - 处理教程页逻辑，主要是导航返回。
│   │   │   ├── tutorial.wxml    #     - 定义教程页面的 WXML 结构，展示静态的图文教程内容。
│   │   │   ├── tutorial.wxss    #     - 包含教程页面特定的 WXSS 样式规则。
│   │   │   └── tutorial.json       #     - 配置教程页为自定义导航栏模式 (custom)。
│   │   ├── outfits/             #   - ★ 穿搭管理相关页面目录
│   │   │   ├── index/           #     - 穿搭列表页面
│   │   │   │   ├── index.js     #       - ★ 显示穿搭列表，调用app.getOutfits获取数据，支持下拉刷新和上拉加载更多。
│   │   │   │   │                    #       **优化穿搭预览数据处理，实现居中显示。**
│   │   │   │   ├── index.wxml     #       - ★ 渲染穿搭列表，提供操作入口。**优化穿搭预览显示结构，使用计算后的位置。**
│   │   │   │   ├── index.wxss     #       - ★ 穿搭列表样式。**优化穿搭预览相关的样式。**
│   │   │   │   └── index.json       #       - ★ 配置页面标题。
│   │   │   ├── add/             #     - 创建穿搭页面
│   │   │   │   ├── add.js       #       - ★ 处理穿搭创建逻辑。
│   │   │   │   ├── add.wxml     #       - ★ 提供衣物选择、布局画布、表单输入。
│   │   │   │   ├── add.wxss     #       - ★ 创建穿搭页面样式。
│   │   │   │   └── add.json       #       - ★ 配置页面标题。
│   │   ├── edit/            #     - 编辑穿搭页面
│   │   │   │   ├── edit.js      #       - ★ 处理穿搭编辑逻辑，加载现有穿搭数据，选择衣物，布局，保存。**新增穿搭名称输入处理。**
│   │   │   │   ├── edit.wxml     #       - ★ 提供衣物选择、布局画布、表单输入。**新增穿搭名称输入框。**
│   │   │   │   ├── edit.wxss     #       - ★ 编辑穿搭页面样式。**新增穿搭名称输入框样式。**
│   │   │   │   └── edit.json       #       - ★ 配置页面标题。
│   │   │   └── detail/          #     - 穿搭详情展示页面
│   │   │       ├── detail.js    #       - ★ 显示穿搭详情，渲染布局。
│   │   │       ├── detail.wxml    #       - ★ 展示穿搭中的衣物和布局。
│   │   │       ├── detail.wxss    #       - ★ 穿搭详情样式。
│   │   │       └── detail.json       #       - ★ 配置页面标题。
│   ├── components/              # 可复用 UI 组件目录 ★
│   │   ├── icon/                #   - 通用图标组件
│   │   │   ├── icon.js          #     - 定义图标组件的属性 (type, size, color)
│   │   │   ├── icon.wxml        #     - 根据 type 属性渲染不同的 iconfont class
│   │   │   ├── icon.wxss        #     - 定义 iconfont class 对应的 content (\eXXX)
│   │   │   └── icon.json        #     - 声明为组件
│   │   └── icon-photos.*        #   - 照片图标组件 (CSS实现)
│   │       ├── icon-photos.js   #     - 定义照片图标组件的属性 (size, color)
│   │       ├── icon-photos.wxml #     - 定义照片图标的嵌套 view 结构
│   │       ├── icon-photos.wxss #     - 使用 CSS 绘制照片图标的边框和内部图形
│   │       └── icon-photos.json #     - 声明为组件
│   ├── images/                  # 本地静态图片资源目录 (logo, tabbar icons, 占位图等)
│   ├── app.js                   # ★ 小程序全局逻辑脚本 (管理全局状态、登录、API请求封装、**穿搭数据管理**等)
│   ├── app.json                 # ★ 小程序全局配置文件 (页面路径注册、窗口表现、Tab栏(**含穿搭Tab**)、权限等)
│   ├── app.wxss                 # 全局 WXSS 样式表 (定义通用样式)
│   ├── project.config.json      # 微信开发者工具项目配置文件 (AppID, 项目设置, libVersion 等)
│   ├── project.private.config.json # 微信开发者工具私有配置文件 (覆盖 project.config.json 中的设置)
│   ├── sitemap.json             # 小程序页面索引配置文件 (控制哪些页面能被微信搜索到)
│   ├── recommended_outfits/     #   - 推荐穿搭相关页面目录 ★
│   │   ├── index/             #     - 推荐穿搭列表页面
│   │   │   ├── index.js       #       - 处理推荐穿搭列表展示逻辑，获取分类和穿搭数据，支持筛选、分页。
│   │   │   ├── index.wxml     #       - 定义推荐穿搭列表页面的 WXML 结构。
│   │   │   ├── index.wxss     #       - 包含推荐穿搭列表页面特定的 WXSS 样式规则。
│   │   │   └── index.json     #       - 配置推荐穿搭列表页面导航栏标题等。
│   │   └── detail/            #     - 推荐穿搭详情页面
│   │       ├── detail.js      #       - 处理推荐穿搭详情展示逻辑，获取穿搭详情和商品列表，支持图片预览、保存、分享。
│   │       ├── detail.wxml    #       - 定义推荐穿搭详情页面的 WXML 结构。
│   │       ├── detail.wxss    #       - 包含推荐穿搭详情页面特定的 WXSS 样式规则。
│   │       └── detail.json    #       - 配置推荐穿搭详情页面导航栏标题等。
│   │   ├── smart_outfit/           #   - ★ 智能穿搭推荐页面目录
│   │   │   ├── smart_outfit.js     #     - 处理智能穿搭推荐逻辑，获取天气信息、调用推荐API、展示推荐结果
│   │   │   ├── smart_outfit.wxml   #     - 定义智能穿搭推荐页面的 WXML 结构，包含天气显示、推荐结果展示
│   │   │   ├── smart_outfit.wxss   #     - 包含智能穿搭推荐页面特定的 WXSS 样式规则
│   │   │   └── smart_outfit.json   #     - 配置智能穿搭推荐页面导航栏标题等
│
├── 数据库/                      # 数据库 SQL 脚本目录 ★
│   ├── create_users_table.sql     #   - (推测, 基于 setup.sql) 创建 `users` 表脚本
│   ├── create_clothes_table.sql   #   - (推测, 基于 setup.sql) 创建 `clothes` 表脚本
│   ├── create_photos_table.sql    #   - 创建 `photos` 表脚本
│   ├── create_try_on_history_table.sql # 创建 `try_on_history` 表脚本
│   ├── create_admin_table.sql     #   - 创建 `admin_users` 表脚本
│   ├── create_api_usage_table.sql #   - 创建 `api_usage` 表脚本 (用于记录 API 调用)
│   ├── create_outfits_table.sql   #   - ★ 创建 `outfits` 表脚本 (用户穿搭)
│   ├── create_wardrobes_table.sql #   - 创建 `wardrobes` 表脚本 (用户衣橱, 含排序、默认标识)
│   ├── create_image_analysis_tables.sql # - ★ 创建个人形象分析和订单相关表的脚本
│   ├── add_image_to_wardrobes.sql #   - 向 `wardrobes` 表添加 `image_url` 列
│   ├── add_user_status_column.sql #   - 向 `users` 表添加 `status` 列脚本
│   ├── update_clothes_table.sql   #   - 更新 `clothes` 表结构脚本 (添加 `wardrobe_id` 外键及索引)
│   ├── setup.sql                #   - 包含早期 `users` 和 `clothes` 表创建的初始化脚本
│   ├── create_demo_account.sql  #   - 创建演示账户和相关演示数据的 SQL 脚本
│   ├── create_recommended_outfits_tables.sql #   - ★ 创建推荐穿搭、商品和统计表脚本
│   └── create_demo_account.sql  #   - 创建演示账户和相关演示数据的 SQL 脚本
│
├── logs/                        # ★ 后端日志文件目录 (由 wardrobe_logger.php 写入)
├── README.md                    # 项目原始说明文档 (可能部分过时)
├── README20250418.md            # ★ 本开发者文档
└── ...                          # 其他项目根目录文件 (如 .gitignore)
```

## 5. 核心模块与文件详解

### 5.1 后端 (`login_backend/`)

*   **`config.php`**: **极其重要**。定义所有外部依赖的配置，包括数据库连接信息 (Host, DBName, User, Pass), 微信小程序 AppID 和 Secret, 阿里云服务的 AccessKey ID/Secret, OutfitAnyone API Key, OSS Endpoint/Bucket/Bucket域名, **CDN 域名 (`ALIYUN_CDN_DOMAIN`) 及是否启用 (`USE_CDN`)**, 以及各资源在 OSS 上的存储路径前缀。还定义了用于管理员 Token 签名的密钥。
    *   **新增抠图API选择配置**：`SEGMENT_API_TYPE` ('cloth' 使用服饰分割，'common' 使用通用分割) 和 `SEGMENT_COMMON_RETURN_FORM` (通用分割返回格式：'crop', 'mask', 'whiteBK')。
*   **`db.php`**: 封装数据库连接。通常包含一个函数或类来获取 PDO 或 mysqli 数据库连接实例。
*   **`auth.php`**: 处理用户认证。
    *   包含验证 JWT (JSON Web Token) 的逻辑。前端请求需要携带有效的 Token。
    *   通常有一个 `verifyToken` 函数，供需要登录权限的接口调用，验证成功后返回用户 ID 或用户信息。
*   **`oss_helper.php`**: 封装与阿里云 OSS 的交互。
    *   提供方法如 `uploadFile` (上传本地文件), `uploadContent` (上传内存中的内容), `downloadUrlToOss` (从 URL 下载并上传到 OSS), `deleteObject` (删除 OSS 文件), `isOssUrl` (判断是否为 OSS/CDN URL), `getFileUrl` (根据 `USE_CDN` 配置生成 OSS 或 CDN URL), **`convertCdnUrlToOssUrl` (将 CDN URL 转换回 OSS URL，用于内部 API 调用)**。 ★
    *   简化了其他模块与 OSS 的交互逻辑。
*   **`wardrobe_logger.php`**: ★ 为衣橱管理等后端功能提供统一的日志记录接口。可以将日志写入到 `logs/` 目录下，便于问题排查。
*   **图片上传流程 (新 - OSS 直传 + 回调)**:
    *   **`get_oss_upload_token.php`**: ★ 前端请求此接口，获取直接上传到 OSS 所需的签名 URL、策略或 STS 凭证，以及目标文件路径 (`file_key`)。返回的访问 URL (`file_url`) 会根据 `USE_CDN` 配置生成 OSS 或 CDN URL。
    *   **`oss_upload_callback.php`**: ★ 当前端使用获取到的凭证成功将文件上传到 OSS 后，OSS 服务可以配置调用此回调接口。此接口接收上传成功的文件信息 (`file_key`, `file_url` 等)，并可以触发后续处理，例如调用阿里云 `SegmentCloth` API 进行抠图，并将结果（原始图和/或抠图 URL）返回给前端或更新数据库记录。这个流程避免了大文件通过后端服务器中转，提高了效率。
        *   **新增抠图API选择**: 根据 `config.php` 中的 `SEGMENT_API_TYPE` 配置，此回调现在可以选择调用阿里云 `SegmentCloth` (服饰分割) 或 `SegmentCommonImage` (通用分割) API 进行抠图处理。
    *   **图片上传流程 (旧)**:
        *   **`upload_image.php`**: ★ (主要用于 JSON 请求或备用流程) 处理衣物图片上传的接口。接收前端上传的图片文件或图片 URL (JSON 请求)，可选调用 `SegmentCloth` API 抠图。**已修复调用抠图 API 时对 CDN URL 的兼容性问题 (内部会转换回 OSS URL)**。
            *   **新增抠图API选择**: 此接口中的抠图逻辑（通过 `segmentClothingImage` 函数调用）也已更新，根据 `config.php` 中的 `SEGMENT_API_TYPE` 配置选择调用阿里云 `SegmentCloth` 或 `SegmentCommonImage` API。
        *   **`upload_photo.php`**: ★ (可能已部分弃用) 处理用户照片上传。接收照片文件，上传到 OSS，并在 `photos` 表记录。返回的 URL 根据 `USE_CDN` 配置生成。
*   **`add_clothing.php` / `get_clothes.php` / `delete_clothing.php`**: 衣物 CRUD。
    *   `add_clothing`: 接收衣物信息（名称、分类、图片 URL 等），将信息存入 `clothes` 表。★现在能正确处理 `wardrobe_id` 参数：如果提供了有效的 `wardrobe_id`（属于该用户），则关联到指定衣橱；如果未提供或无效，则自动查找并关联到用户的默认衣橱。包含用户权限验证。
    *   `get_clothes`: 从 `clothes` 表查询衣物列表，支持按 `category` 或 `wardrobe_id` 筛选。
    *   `delete_clothing`: 从 `clothes` 表删除记录，并调用 `oss_helper` 删除 OSS 上对应的图片。**新增删除前检查衣物是否被穿搭使用逻辑。**
*   **`try_on.php`**: 核心虚拟试衣逻辑。
    *   **已添加每日试衣次数限制**：在处理试衣请求前，检查用户当天在 `try_on_history` 表中已成功的试衣次数（限制为1次/天，次日8点刷新）。若超过限制，则直接返回错误信息给前端，阻止试衣。
    *   接收用户选择的照片 ID 和衣物 ID(s)。
    *   从数据库查询对应的照片 OSS/CDN URL 和衣物 OSS/CDN URL(s)。**在调用 OutfitAnyone API 前，需要确保传递的是 API 可访问的 URL (可能是 OSS URL)**。
    *   调用阿里云 `OutfitAnyone` API，传入照片和衣物 URL。
    *   将试衣任务信息（用户ID, 照片ID, 衣物ID, 任务ID, 初始状态）存入 `try_on_history` 表。
    *   返回任务 ID 给前端。
*   **`get_try_on_status.php`**: 查询试衣任务状态。
    *   接收任务 ID (`task_id`)。
    *   调用阿里云的查询接口（或通过回调机制，具体实现需看代码）获取异步任务的最新状态。
    *   可能更新 `try_on_history` 表中的任务状态和结果图片 URL。
    *   如果成功，获取结果图片 URL (根据 `USE_CDN` 生成) 并更新数据库。
    *   返回任务状态（如 排队中、处理中、成功、失败）和最终结果图片 URL (如果成功) 给前端。
*   **`get_try_on_history.php`**: 获取用户试衣历史记录 API (分页)。
*   **衣橱管理 API**:
    *   **`add_wardrobe.php`**: ★ 处理添加新衣橱。接收 `name`, `description`, `sort_order`。不处理 `image_url`。
    *   **`get_wardrobes.php`**: ★ 处理获取用户衣橱列表。返回 `id`, `name`, `description`, `sort_order`, `is_default`, 以及衣物计数 `clothes_count`。按默认、排序、名称排序。不返回 `image_url`。
    *   **`update_wardrobe.php`**: ★ 处理更新衣橱。接收 `id`, `name`, `description`, `sort_order`。不处理 `image_url`。
    *   **`delete_wardrobe.php`**: ★ 处理删除衣橱。接收 `id`。**重要逻辑**: 不能删除默认衣橱 (`is_default=1`)。如果删除非默认衣橱，会先将该衣橱内的所有衣物（`clothes` 表记录）的 `wardrobe_id` 更新为用户的默认衣橱 ID，然后再删除该衣橱记录。
    *   **`move_clothes.php`**: ★ 处理将一件或多件衣物移动到指定衣橱。更新 `clothes` 表中对应记录的 `wardrobe_id` 字段。
    *   **`get_wardrobe_clothes.php`**: ★ 处理获取指定衣橱下所有衣物列表。接收 `wardrobe_id`，支持分页。
*   **Admin Endpoints (`*_admin_*.php`)**: 提供管理后台所需的数据接口，用于管理用户、衣物、照片、试衣记录、系统设置等。
*   **`get_dashboard_data.php`**: 为管理后台仪表盘提供聚合统计数据（用户数、衣物数、API 使用量等）。
*   **Migration Scripts**:
    *   **`migrate_to_oss.php`**: ★ 用于将旧的本地存储图片迁移到 OSS 的一次性脚本。
    *   **`migrate_wardrobes.php`**: ★ 推测用于处理衣橱相关的数据库结构或数据迁移（例如，确保每个用户都有默认衣橱）。
    *   **`migrate_to_cdn_complete.php`**: ★ 用于将数据库中已有的 OSS URL 批量更新为 CDN URL 的工具脚本。**增加了 `--all` 参数以支持处理所有用户的数据**。
*   **穿搭管理 API**: ★
    *   **`save_outfit.php`**: 处理添加/更新穿搭 API。接收穿搭数据 (名称, 描述, 缩略图, 布局数据), 在 `outfits` 表中插入或更新记录。
    *   **`get_outfits.php`**: 处理获取用户穿搭列表 API。从 `outfits` 表查询用户的穿搭数据，支持分页。
    *   **`delete_outfit.php`**: 处理删除指定穿搭 API。从 `outfits` 表中删除记录。

*   **推荐穿搭 API (管理后台)**: ★
    *   **`admin_add_recommended_category.php`**: 处理添加推荐穿搭分类 API (管理后台)。
    *   **`admin_update_recommended_category.php`**: 处理更新推荐穿搭分类 API (管理后台)。
    *   **`admin_delete_recommended_category.php`**: 处理删除推荐穿搭分类 API (管理后台)。
    *   **`admin_get_recommended_categories.php`**: 处理获取推荐穿搭分类列表 API (管理后台)。
    *   **`admin_get_recommended_category.php`**: 处理获取单个推荐穿搭分类详情 API (管理后台)。
    *   **`admin_add_recommended_outfit.php`**: 处理添加推荐穿搭 API (管理后台)。
    *   **`admin_update_recommended_outfit.php`**: 处理更新推荐穿搭 API (管理后台)。
    *   **`admin_delete_recommended_outfit.php`**: 处理删除推荐穿搭 API (管理后台)。
    *   **`admin_get_recommended_outfits.php`**: 处理获取推荐穿搭列表 API (管理后台)。
    *   **`admin_get_recommended_outfit_detail.php`**: 处理获取单个推荐穿搭详情 API (管理后台)。
    *   **`admin_update_recommended_outfit_status.php`**: 处理更新推荐穿搭状态 API (管理后台，如上下架)。
    *   **`admin_upload_outfit_image.php`**: 处理管理后台推荐穿搭图片上传 API。

*   **推荐穿搭 API (小程序用户端)**: ★
    *   **`get_recommended_categories.php`**: 处理获取推荐穿搭分类列表 API (小程序用户端)。
    *   **`get_recommended_outfits.php`**: 处理获取推荐穿搭列表 API (小程序用户端)。
    *   **`get_recommended_outfit_detail.php`**: 处理获取单个推荐穿搭详情 API (小程序用户端)。

*   **智能穿搭推荐 API**: ★
    *   **`get_outfit_recommendation.php`**: 处理智能穿搭推荐 API。根据用户地理位置获取天气信息，基于当前天气、季节等条件，从用户衣橱中（包括系统分类和自定义分类）智能推荐合适的穿搭组合。核心功能包括：
        *   **自定义分类支持**: 通过 `getCategoryCodesByType` 函数获取用户自定义分类代码，扩展了原有的硬编码分类限制
        *   **衣物分类映射**: `getUserClothingByCategory` 函数支持从多个分类代码中获取衣物，实现了系统分类到具体衣物的智能映射
        *   **天气数据集成**: 调用天气API获取实时天气和温度信息，为推荐算法提供环境参数
        *   **AI驱动推荐**: 集成Gemini API，基于天气、季节、用户衣物进行智能分析和推荐
        *   **多样化推荐逻辑**: 支持随机推荐、AI推荐等多种推荐策略，确保推荐结果的多样性和准确性

*   **淘宝联盟API相关**:
    *   **`get_taobao_products.php`**: 从淘宝联盟API获取商品列表，支持搜索关键词、分页等参数，返回商品信息（名称、图片、价格、销量等）。
    *   **`get_taobao_link.php`**: 根据商品ID获取淘宝商品链接，可能包含普通链接或优惠券链接。
    *   **`convert_to_tpwd.php`**: 将淘宝商品链接转换为淘口令(淘宝口令)，便于用户在移动端分享和购买。该接口接收商品链接(URL)和商品ID，返回带有优惠信息的淘口令。

### 5.2 前端 (`miniprogram/`)

*   **`app.js`**:
    *   **全局管理**: 管理全局数据如 `userInfo`, `token`, `apiBaseUrl`。
    *   **启动逻辑 (`onLaunch`)**: 检查本地存储 (`wx.getStorageSync`) 中是否存在 `token`，如果存在则加载到 `globalData` 并调用 `checkTokenValidity` 进行验证。
    *   **Token 验证 (`checkTokenValidity`)**: 调用后端 `/verify_token.php` 验证 `token` 有效性，包含重试逻辑 (最多3次)，失败则清除本地 token/userInfo 并可能重定向到登录页。
    *   **登录封装 (`login`)**: 封装 `wx.login()` 获取 `code`，调用后端 `/login.php` 获取 `token`，并将 `token` 存入 `globalData` 和本地存储 (`wx.setStorageSync`)。
    *   **用户信息更新 (`updateUserInfo`)**: 调用后端 `/update_profile.php` 更新用户昵称、头像等信息，并同步更新 `globalData` 和本地存储。
    *   **数据重置 (`clearStorage`)**: 提供清除本地登录相关存储（token, userInfo）和重置全局状态的功能。
*   **`app.json`**:
    *   **页面注册**: 定义所有小程序页面的路径，**包括新增的 `pages/outfits/` 相关页面**。
    *   **全局配置**: 设置窗口表现（导航栏、背景色等）。
    *   **Tab Bar**: 配置底部导航栏，包含 "衣橱" (`pages/index/index`)、**"穿搭" (`pages/outfits/index/index`)**、"试穿" (`pages/try_on/index/index`)、"我的" (`pages/profile/profile`) 四个标签。★
    *   **组件注册**: 注册全局或页面使用的自定义组件 (如 `icon-photos`)。
*   **`pages/`**: 包含各个功能页面的代码。
    *   **`index/index.js`**: 主页（衣橱）逻辑。通过 `/get_clothes.php` 获取用户衣物数据，支持按分类 (`category`) 筛选，实现下拉刷新 (`onPullDownRefresh`)，并将获取到的衣物数据分配到左右两列 (`leftItems`, `rightItems`) 以实现瀑布流布局 (`distributeClothes`)。在 `onShow` 时检查 `app.globalData.needRefreshClothes` 标志来决定是否刷新数据。**新增公告获取与展示逻辑**，包括获取活跃公告API (`/get_announcement.php`)的数据，判断是否需要显示公告弹窗 (`shouldShowAnnouncement`)，以及处理公告已读状态的本地存储 (`wx.getStorageSync`, `wx.setStorageSync`) 和关闭弹窗 (`closeAnnouncement`) 的逻辑。
    *   **`index/index.wxml`**: 定义首页的 WXML 结构，包括衣橱选择器、分类选项卡、衣物网格布局、添加衣物按钮、体验账号提示。**新增公告弹窗 (`announcement-modal`) 的 WXML 结构**，用于展示获取到的活跃公告。
    *   **`login/login.js`**: 处理完整的登录流程。检查 `privacyChecked` 状态，显示授权确认弹窗 (`showAuthModal`)，调用 `app.login()`。成功后显示用户信息获取弹窗 (`showUserInfoModal`)，使用微信开放能力 `<button open-type="chooseAvatar">` 和 `<input type="nickname">` 获取用户头像和昵称，然后调用 `app.updateUserInfo()` 保存。用户也可选择"跳过"信息完善。
    *   **`profile/profile.js`**: "我的"页面逻辑。显示 `userInfo`，通过调用 `/get_clothes.php` 获取全部衣物数据来计算并展示衣物总数、类别数、标签数 (`clothesCount`, `categoryCount`, `tagCount`)。提供导航到"我的照片"、"试穿历史"、"管理衣橱"、"使用教程"等页面的入口。包含"联系客服"、"意见反馈"的按钮（使用 `open-type`）。提供"退出登录" (`logout`) 功能，调用 `app.clearStorage()` 清除登录状态。
    *   **`clothing/`**: 包含衣物管理的子页面 (添加 `add`, 详情 `detail`, 编辑 `edit`, 列表管理 `manage`)。其中 `add` 页面可能需要适配新的 OSS 直传流程。
    *   **`photos/`**: 包含用户照片管理的子页面 (列表 `index`, 添加 `add`, 详情 `detail`)。其中 `add` 页面可能需要适配新的 OSS 直传流程。
    *   **`try_on/`**: 包含虚拟试衣流程的子页面 (主入口 `index`, 选择衣物/照片 `select`, 显示结果 `result`, 查看历史 `history`)。
    *   **`select/`**: 选择衣物/照片进行试衣页面
    *   `index.js`: 处理选择照片和衣物的逻辑，获取照片和衣物列表供用户选择，【新增】支持按衣橱筛选衣物，发起试衣请求。**已优化每日试衣次数限制处理**：当后端返回次数限制错误时，前端会停止所有进度动画和计时器，并显示友好的提示弹窗。
    *   `index.wxml`: 定义选择页面的 WXML 结构，展示照片和衣物列表供勾选，包含进度弹窗和次数限制提示弹窗。
    *   **`wardrobes/`**: ★ 包含用户自定义衣橱（分类）管理的相关页面。
        *   `index/index.js`: ★ 显示衣橱列表，支持分页和下拉刷新。处理编辑和添加导航。**删除逻辑**: 当前检查衣橱是否为空，若不为空则阻止删除。**【注意】这与后端行为不一致（后端会移动衣物），建议修改前端逻辑**。
        *   `add/add.js`: ★ 提供表单创建新衣橱，发送 `name`, `description`, `sort_order` 到后端。不处理 `image_url`。
        *   `edit/edit.js`: ★ 提供表单编辑衣橱，从导航参数接收 `id`, `name`, `description`, `sort_order`。**【注意】当前提交更新时仅发送 `id`, `name`, `description`，未发送 `sort_order`，需要修复**。不处理 `image_url`。
        *   `detail/detail.js`: ★ 显示特定衣橱名称，并调用 `/get_wardrobe_clothes.php` 获取该衣橱下的衣物列表，使用瀑布流展示。提供 "添加衣物" 按钮，跳转到添加衣物页并预设当前衣橱 ID。
    *   **`tutorial/tutorial.js`**: 静态教程页面，主要逻辑是导航返回。
    *   **`outfits/`**: ★ 新增的穿搭管理页面。
        *   `index/index.js`: 显示用户创建的穿搭列表，调用 `app.getOutfits` 获取数据，支持下拉刷新和上拉加载更多。提供创建、查看详情、编辑、删除穿搭的入口。
        *   `add/add.js` (可能包含编辑逻辑): 提供界面让用户选择衣物（可能按衣橱分类），组合成穿搭，设置名称、描述、缩略图，调用 `app.saveOutfit` 保存。编辑时加载现有穿搭数据。
        *   `detail/detail.js`: 显示选定穿搭的详细信息，包括包含的衣物图片、名称、布局等。
    *   **`recommended_outfits/`**: 推荐穿搭相关页面。
        *   `index/index.js`: 处理推荐穿搭列表展示逻辑，获取分类和穿搭数据，支持筛选、分页。**同时管理淘宝商品展示，包括商品列表获取、Tab切换、商品搜索、淘口令生成和复制功能**。
        *   `index.wxml`: 定义推荐穿搭列表页面的 WXML 结构，**包含穿搭和淘宝商品两个Tab**。
        *   `index.wxss`: 包含推荐穿搭列表页面特定的 WXSS 样式规则。
        *   `index.json`: 配置推荐穿搭列表页面导航栏标题等。
*   **`components/`**: 存放可复用的 UI 组件。
    *   **`icon/`**: 通用图标组件，通过传入 `type` 属性显示不同的图标，基于 CSS `::before` 和字体图标实现。
    *   **`icon-photos.*`**: 一个具体的"照片"图标组件，使用 WXML 和 WXSS 绘制图形。
*   **`recommended_outfits/`**: 推荐穿搭相关页面。
    *   `index/index.js`: 处理推荐穿搭列表展示逻辑，获取分类和穿搭数据，支持筛选、分页。**同时管理淘宝商品展示，包括商品列表获取、Tab切换、商品搜索、淘口令生成和复制功能**。
    *   `index.wxml`: 定义推荐穿搭列表页面的 WXML 结构，**包含穿搭和淘宝商品两个Tab**。
    *   `index.wxss`: 包含推荐穿搭列表页面特定的 WXSS 样式规则。
    *   `index.json`: 配置推荐穿搭列表页面导航栏标题等。
    *   `detail/detail.js`: 处理推荐穿搭详情展示逻辑，获取穿搭详情和商品列表，支持图片预览、保存、分享。
    *   `detail.wxml`: 定义推荐穿搭详情页面的 WXML 结构。
    *   `detail.wxss`: 包含推荐穿搭详情页面特定的 WXSS 样式规则。
    *   `detail.json`: 配置推荐穿搭详情页面导航栏标题等。
    *   **`smart_outfit/`**: 智能穿搭推荐页面。
        *   `smart_outfit.js`: 处理智能穿搭推荐逻辑，获取天气信息、调用推荐API、展示推荐结果。
        *   `smart_outfit.wxml`: 定义智能穿搭推荐页面的 WXML 结构，包含天气显示、推荐结果展示。
        *   `smart_outfit.wxss`: 包含智能穿搭推荐页面特定的 WXSS 样式规则。
        *   `smart_outfit.json`: 配置智能穿搭推荐页面导航栏标题等。

### 5.3 数据库 (`数据库/`)

*   **SQL Scripts**: 包含用于创建和修改数据库表结构的 SQL 文件。
    *   **`users`**: 存储用户信息 (openid, nickname, avatar_url, gender, status 等)。
    *   **`clothes`**: 存储衣物信息 (user_id, name, category, image_url (OSS), tags, description, **`wardrobe_id` (FK to wardrobes)** 等)。 ★
    *   **`photos`**: 存储用户上传的照片信息 (user_id, image_url (OSS), type, description 等)。
    *   **`try_on_history`**: 存储虚拟试衣记录 (user_id, photo_id, clothes_ids (可能为 JSON 或关联表), result_image_url (OSS), task_id, status, created_at 等)。
    *   **`admin_users`**: 存储管理后台用户信息。
    *   **`api_usage`**: 可能用于统计 AI API 调用次数。
    *   **`wardrobes`**: ★ (从 `create_wardrobes_table.sql` / `add_image_to_wardrobes.sql` 推断) 用于存储用户创建的衣橱分类信息 (id, user_id, name, description, **`image_url`**, **`sort_order`**, **`is_default`**)。
    *   **`create_demo_account.sql`**: ★ 创建一个 ID 为 1 的 '体验账号' 并为其添加示例衣物和照片，用于测试或演示。
    *   **`update_clothes_table.sql`**: ★ 添加 `wardrobe_id` 到 `clothes` 表并设置外键。
    *   **`add_image_to_wardrobes.sql`**: ★ 添加 `image_url` 到 `wardrobes` 表。
    *   **关系说明**: `clothes` 表通过 `wardrobe_id` 字段关联到 `wardrobes` 表的 `id` 字段，表示衣物所属的衣橱。
    *   **`outfits`**: ★ 用户创建的穿搭组合
        *   `id`: INT AUTO_INCREMENT PRIMARY KEY
        *   `user_id`: INT NOT NULL, Foreign Key -> `users(id)` ON DELETE CASCADE
        *   `name`: VARCHAR(100) NOT NULL
        *   `description`: TEXT NULL
        *   `thumbnail_url`: TEXT NULL COMMENT '缩略图URL'
        *   `outfit_data`: LONGTEXT NOT NULL COMMENT '以JSON格式存储的穿搭布局数据'
        *   `created_at`: DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
        *   `updated_at`: DATETIME NULL ON UPDATE CURRENT_TIMESTAMP
    *   **`image_analysis`**: ★ 个人形象分析记录表
        *   `id`: INT AUTO_INCREMENT PRIMARY KEY
        *   `user_id`: INT NOT NULL, Foreign Key -> `users(id)`
        *   `gender`: TINYINT COMMENT '1-男, 2-女'
        *   `concerns`: TEXT COMMENT '用户关注点'
        *   `photo_urls`: JSON COMMENT '用户上传的照片URL列表'
        *   `status`: ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending'
        *   `payment_status`: ENUM('unpaid', 'paid', 'refunded') DEFAULT 'unpaid'
        *   `analysis_result`: JSON COMMENT 'AI分析结果'
        *   `created_at`: TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        *   `updated_at`: TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    *   **`orders`**: ★ 支付订单表
        *   `id`: INT AUTO_INCREMENT PRIMARY KEY
        *   `user_id`: INT NOT NULL
        *   `related_id`: INT NOT NULL COMMENT '关联的业务ID，如image_analysis的ID'
        *   `related_type`: VARCHAR(50) NOT NULL COMMENT '关联业务类型，如image_analysis'
        *   `order_sn`: VARCHAR(64) UNIQUE NOT NULL COMMENT '商户订单号'
        *   `transaction_id`: VARCHAR(64) COMMENT '微信支付交易号'
        *   `amount`: DECIMAL(10, 2) NOT NULL
        *   `status`: ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending'
        *   `created_at`: TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        *   `paid_at`: DATETIME

*   **`announcements`**: 存储系统公告信息
    *   `id`: INT, Primary Key, Auto Increment
    *   `title`: VARCHAR - 公告标题
    *   `content`: TEXT - 公告内容
    *   `start_time`: DATETIME - 生效开始时间
    *   `end_time`: DATETIME - 生效结束时间
    *   `status`: TINYINT, Default 1 - 状态 (0=禁用, 1=启用)
    *   `created_at`: DATETIME, Default CURRENT_TIMESTAMP
    *   `updated_at`: DATETIME, NULLABLE ON UPDATE CURRENT_TIMESTAMP

*   **`admin_users`**: 存储管理后台用户信息
    *   `id`: INT, Primary Key, Auto Increment
    *   `username`: VARCHAR, Unique - 管理员用户名
    *   `password_hash`: VARCHAR - 加密后的管理员密码
    *   `created_at`: TIMESTAMP, Default CURRENT_TIMESTAMP

*   **`api_usage`**: 记录 AI API 调用情况 (用于统计或计费)
    *   `id`: INT, Primary Key, Auto Increment
    *   `user_id`: INT, Foreign Key -> `users(id)`
    *   `api_type`: VARCHAR - 调用的 API 类型 ('tryOn', 'segment' 等)
    *   `timestamp`: TIMESTAMP, Default CURRENT_TIMESTAMP - 调用时间
    *   `cost`: DECIMAL - 调用成本 (可选)
    *   `status`: VARCHAR - 调用状态 ('success', 'failed')
    *   `request_payload`: TEXT - API 请求内容 (部分或全部)
    *   `response_payload`: TEXT - API 响应内容 (部分或全部)
    *   `task_id`: VARCHAR - 关联的异步任务 ID (如果适用)
    *   `error_message`: TEXT - 调用失败时的错误信息

*   **`wardrobes`**: ★ 用户定义的衣橱 (用于组织衣物)
    *   `id`: INT, Primary Key, Auto Increment
    *   `user_id`: INT, Foreign Key -> `users(id)` ON DELETE CASCADE
    *   `name`: VARCHAR - 衣橱名称
    *   `description`: TEXT - 衣橱描述
    *   `image_url`: VARCHAR(255), NULLABLE - 衣橱封面图片 URL (当前后端 API 未处理)
    *   `sort_order`: INT, Default 0 - 排序值，越小越靠前
    *   `is_default`: TINYINT(1), Default 0 - 是否为默认衣橱 (每个用户应有且仅有一个is_default=1)
    *   `created_at`: TIMESTAMP, Default CURRENT_TIMESTAMP
    *   `updated_at`: TIMESTAMP, NULLABLE ON UPDATE CURRENT_TIMESTAMP

*   **`outfits`**: ★ 用户创建的穿搭组合
    *   `id`: INT AUTO_INCREMENT PRIMARY KEY
    *   `user_id`: INT NOT NULL, Foreign Key -> `users(id)` ON DELETE CASCADE
    *   `name`: VARCHAR(100) NOT NULL
    *   `description`: TEXT NULL
    *   `thumbnail_url`: TEXT NULL COMMENT '缩略图URL'
    *   `outfit_data`: LONGTEXT NOT NULL COMMENT '以JSON格式存储的穿搭布局数据'
    *   `created_at`: DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    *   `updated_at`: DATETIME NULL ON UPDATE CURRENT_TIMESTAMP

*(具体字段类型和约束请以最新的 SQL 文件为准)*

*   **`recommended_categories`**: ★ 推荐穿搭分类表
    *   `category_id`: INT AUTO_INCREMENT PRIMARY KEY
    *   `category_name`: VARCHAR(100) NOT NULL UNIQUE - 分类名称
    *   `created_at`: TIMESTAMP DEFAULT CURRENT_TIMESTAMP

*   **`recommended_outfits`**: ★ 推荐穿搭主表
    *   `outfit_id`: INT AUTO_INCREMENT PRIMARY KEY
    *   `category_id`: INT NOT NULL, FOREIGN KEY -> `recommended_categories(category_id)` - 所属分类ID
    *   `title`: VARCHAR(255) NOT NULL - 穿搭标题
    *   `description`: TEXT - 穿搭描述
    *   `image_url`: VARCHAR(255) NOT NULL - 穿搭封面图片URL
    *   `status`: ENUM('draft', 'published', 'archived') DEFAULT 'draft' - 穿搭状态
    *   `created_at`: TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    *   `updated_at`: TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

*   **`recommended_outfit_items`**: ★ 推荐穿搭包含的衣物（商品）列表
    *   `item_id`: INT AUTO_INCREMENT PRIMARY KEY
    *   `outfit_id`: INT NOT NULL, FOREIGN KEY -> `recommended_outfits(outfit_id)` ON DELETE CASCADE - 所属推荐穿搭ID
    *   `clothing_id`: INT NOT NULL, FOREIGN KEY -> `clothes(id)` - 关联的用户衣物（或商品）ID
    *   `position_x`: DECIMAL(10, 2) - 衣物在穿搭图中的X坐标
    *   `position_y`: DECIMAL(10, 2) - 衣物在穿搭图中的Y坐标
    *   `scale`: DECIMAL(10, 2) DEFAULT 1.0 - 衣物在穿搭图中的缩放比例
    *   `z_index`: INT DEFAULT 0 - 衣物在穿搭图中的层叠顺序
    *   `created_at`: TIMESTAMP DEFAULT CURRENT_TIMESTAMP

## 6. 关键数据流 / 功能流程

### 6.1 用户注册/登录

1.  用户打开小程序，`app.js` 的 `onLaunch` 检查本地是否有有效 Token。
2.  如果无 Token 或 Token 无效，则跳转到登录页 (`pages/login/index`)。
3.  登录页引导用户授权获取基本信息。
4.  用户点击授权按钮，小程序调用 `wx.login()` 获取 `code`。
5.  `login.js` 将 `code` 发送给后端 API `/login.php`。
6.  后端 `/login.php` 使用 `code` 调用微信服务器接口获取 `openid` 和 `session_key`。
7.  后端根据 `openid` 查询用户表，如果用户不存在，则创建新用户记录。
8.  后端生成包含 `openid` 和用户 ID 的 JWT Token，并返回给前端。
9.  前端 `login.js` 接收到 Token，存入 `globalData` 和本地存储 (`wx.setStorageSync`)。
10. 如果用户首次登录或信息不全，引导用户完善头像昵称。
11. 用户通过 `<button open-type="chooseAvatar">` 和 `<input type="nickname">` 获取头像和昵称。
12. 前端将头像 URL 和昵称发送给后端 `/update_profile.php`。
13. 后端更新用户表信息。
14. 登录流程完成，跳转到首页 (`pages/index/index`)。

### 6.2 衣物上传与抠图 (OSS 直传 + 回调)

1.  用户在添加衣物页 (`pages/clothing/add/index`) 选择图片。
2.  前端调用后端 API `/get_oss_upload_token.php` 获取 OSS 上传凭证和目标文件路径。
3.  后端生成带签名的上传 URL/策略或 STS 凭证，返回给前端，包含计算好的 OSS 文件路径和对应的 OSS/CDN URL。
4.  前端使用获取到的凭证直接将图片文件上传到阿里云 OSS。
5.  OSS 在文件上传成功后，触发配置好的回调，调用后端 API `/oss_upload_callback.php`。
6.  后端 `/oss_upload_callback.php` 接收 OSS 传来的文件信息 (如 `file_key`, `file_url`)。
7.  后端根据配置 (`SEGMENT_API_TYPE`) 选择调用阿里云 `SegmentCloth` 或 `SegmentCommonImage` API 对图片进行抠图。
    *   **注意**: 在调用抠图 API 前，如果 `file_url` 是 CDN URL，后端会先将其转换回原始的 OSS URL。
8.  抠图 API 返回抠图结果 (如抠图后图片的 URL)。
9.  后端 `/oss_upload_callback.php` 将原始图片 URL 和抠图后图片 URL (如果成功) 关联到用户，并更新数据库记录（通常是在一个临时表或直接与衣物记录关联）。
10. 用户在前端填写衣物其他信息（名称、分类、标签等）并提交表单。
11. 前端将衣物表单数据（包括图片 URL）发送给后端 `/add_clothing.php`。
12. 后端 `/add_clothing.php` 将衣物信息存入 `clothes` 表，并处理与衣橱的关联（指定衣橱或默认衣橱）。

*   **概述**: `admin/` 目录包含一个独立的 Web 应用程序，作为项目的管理后台界面。
*   **技术栈**: 基于标准的 Web 技术构建：HTML 用于结构，CSS (`css/` 目录) 用于样式，JavaScript (`js/` 目录) 用于处理用户交互、数据获取和表单提交等前端逻辑。
*   **后端交互**: 管理后台不直接操作数据库，而是通过调用 `login_backend/` 目录下专门的 Admin API 端点来获取数据和执行管理操作。这些端点通常以 `_admin_` 开头，例如：
    *   登录: `admin_login.php` (验证管理员身份)
    *   Token 验证: `admin_verify_token.php` (验证后续请求的管理员 Token)
    *   数据获取: `get_admin_users.php`, `get_admin_clothing_list.php`, `get_admin_photo_details.php`, `get_admin_try_on_history.php`, `get_dashboard_data.php`, `get_admin_system_settings.php` 等。
    *   数据更新: `update_admin_user_status.php`, `update_admin_clothing.php`, `update_admin_photo.php`, `update_admin_system_settings.php` 等。
    *   数据删除: `delete_admin_clothing.php`, `delete_admin_photo.php`, `delete_admin_try_on.php` 等。
*   **主要功能模块** (基于目录中的 HTML 文件推断):
    *   **登录入口** (`index.html`): 管理员登录界面。
    *   **仪表盘** (`dashboard.html`): 显示关键业务指标和统计数据。
    *   **用户管理** (`user_list.html`, `user_details.html`): 查看小程序用户列表、用户详细信息，可能包含修改用户状态等功能。
    *   **衣物管理** (`clothing_list.html`, `clothing_details.html`, `clothing_edit.html`): 查看、编辑衣物信息（名称、分类、标签、图片等），可能包含删除功能。
    *   **照片管理** (`photo_list.html`, `photo_details.html`, `photo_edit.html`): 查看、编辑用户上传的照片信息，可能包含删除功能。
    *   **试衣历史管理** (`try_on_list.html`, `try_on_details.html`): 查看用户的虚拟试衣记录列表和详细信息。
    *   **系统设置** (`system_settings.html`): 查看和修改系统级的配置参数，例如 API 密钥、存储路径等（对应 `config.php` 中可能允许后台修改的部分）。
        *   **推荐穿搭分类管理** (`recommended_category_list.html`, `recommended_category_edit.html`): 管理推荐穿搭的分类。
        *   **推荐穿搭管理** (`recommended_outfit_list.html`, `recommended_outfit_edit.html`): 管理推荐穿搭的内容，包括添加、编辑、上下架等。
    *   **核心 JS 文件**:
        *   `auth.js`: 管理后台通用认证逻辑。
        *   `recommended_categories.js`: 推荐穿搭分类列表页面逻辑。
        *   `recommended_category_edit.js`: 推荐穿搭分类编辑/添加页面逻辑。
        *   `recommended_outfit_edit.js`: 推荐穿搭编辑/添加页面逻辑。
        *   `recommended_outfits.js`: 推荐穿搭列表页面逻辑。

1.  用户进入首页 (`pages/index/index`) 或衣橱详情页 (`pages/wardrobes/detail/index`)。
2.  前端页面 (`index.js` 或 `detail.js`) 调用后端 API `/get_clothes.php` (首页) 或 `/get_wardrobe_clothes.php` (衣橱详情)，传入分页参数和可选的分类/衣橱 ID。
3.  后端查询数据库 `clothes` 表，获取符合条件的衣物数据，返回分页结果。
4.  前端接收到数据，处理图片 URL (确保使用 CDN URL)，并更新页面数据 (`setData`)。
5.  前端 (`index.js` / `detail.js`) 的逻辑（如 `distributeClothes`）将衣物分配到瀑布流布局的列中。
6.  用户滚动到底部时，触发 `onReachBottom` 事件或 `scroll-view` 的 `bindscrolltolower` 事件。
7.  前端调用 `/get_clothes.php` 或 `/get_wardrobe_clothes.php` 请求下一页数据（增加分页参数）。
8.  后端返回下一页数据或空数组（如果已加载完）。
9.  前端将新数据追加到现有列表中，并更新加载状态（如隐藏"加载中"提示）。

### 6.1 用户认证流程

1.  用户在列表页点击某个衣物，跳转到衣物详情页 (`pages/clothing/detail/index`)，通过导航参数传递衣物 ID。
2.  前端 `detail.js` 在 `onLoad` 中获取衣物 ID，调用后端 API `/get_clothing_detail.php` (假设有此接口，或者 `/get_clothes.php` 支持按 ID 查询) 获取衣物详细信息。
3.  后端查询 `clothes` 表，返回衣物数据。
4.  前端 `detail.js` 接收数据，更新页面展示。
5.  用户点击删除按钮。
6.  前端 `detail.js` 触发删除操作，**在调用后端删除 API 前，先调用后端 API `/check_clothing_in_outfits.php` (假设新增此接口) 检查该衣物是否在任何穿搭中使用。**
7.  **后端 `/check_clothing_in_outfits.php` 查询 `outfits` 表，检查是否存在包含该衣物 ID 的穿搭。如果存在，返回使用该衣物的穿搭列表。**
8.  **如果衣物在穿搭中使用，前端显示友好提示，列出相关穿搭名称，阻止删除。**
9.  **如果衣物未在穿搭中使用，前端显示确认弹窗。**
10. 用户确认删除后，前端调用后端 API `/delete_clothing.php`，传递衣物 ID。
1.  **小程序 (Login Page)**: 页面加载时 (`onLoad`) 检查本地存储/全局 `globalData` 是否已有有效 `token`。若有，直接跳转主页 (`navigateToMain`)。若无，显示登录按钮和隐私协议。
2.  用户点击 "微信一键登录"。
3.  检查隐私协议是否已勾选 (`privacyChecked`)。若未勾选，弹出隐私协议详情 (`showPrivacyDialog`)，用户需同意后 (`agreePrivacyPolicy`) 方可继续。
4.  显示授权确认弹窗 (`showAuthModal`)，提示将获取用户信息。
5.  用户点击 "允许"。
6.  调用 `app.login()`:
    *   内部调用 `wx.login()` 获取 `code`。
    *   向后端 `/login.php` 发送 `code`。
    *   后端使用 `code` 换取 `openid` 和 `session_key`，查找或创建用户记录，生成 JWT `token`。
    *   后端返回 `{ success: true, token: "...", user_id: ..., is_new_user: ... }`。
    *   `app.js` 将 `token` 保存到 `globalData` 和本地存储 (`wx.setStorageSync`)。
7.  **小程序 (Login Page)**: `app.login()` 回调成功后，显示用户信息完善弹窗 (`showUserInfoModal`)。
8.  **用户信息完善弹窗**:
    *   用户点击头像区域，触发 `<button open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">`，选择头像，`avatarUrl` 存入 `tempUserInfo`。
    *   用户在 `<input type="nickname" bindinput="onInputNickname">` 输入昵称，`nickName` 存入 `tempUserInfo`。
    *   用户点击 "保存"。
9.  调用 `app.updateUserInfo()`:
    *   将 `tempUserInfo` 中的 `nickName` 和 `avatarUrl` 发送到后端 `/update_profile.php`。
    *   后端更新 `users` 表中对应记录。
    *   `app.js` 更新 `globalData.userInfo` 并同步到本地存储。
10. **小程序**: 无论用户是点击"保存"还是"跳过"，都调用 `navigateToMain()` 跳转到主页 Tab (`/pages/index/index`)。
11. **后续请求**: 小程序在需要授权的 API 请求头中添加 `Authorization: Bearer <token>`。
12. **后端 (受保护接口)**: 调用 `auth.php` 内的逻辑（可能通过 `verifyToken` 函数）验证请求头中的 `token` 是否有效，有效则处理请求，无效则返回错误。

### 6.2 图片上传流程 (OSS 直传 + 回调) ★

**取代了旧的服务器中转上传流程，提高了效率和稳定性。**

1.  **小程序 (触发上传)**: 用户在添加衣物或照片页面选择图片，可选抠图。
2.  **小程序 -> 后端 (获取凭证)**: 调用 `/get_oss_upload_token.php` API，可能携带 `file_type` 参数。
3.  **后端 (`get_oss_upload_token.php`)**:
    *   验证用户 Token。
    *   生成唯一的文件名和在 OSS 中的存储路径 (`file_key`，通常在 `clothes/` 或 `photos/` 目录下)。
    *   使用 `oss_helper.php` 生成 OSS 直传所需的签名 URL 或 STS 临时凭证，并设置好上传策略（如文件大小、类型限制）和回调参数。
    *   返回签名 URL/凭证、`file_key`、预计的文件访问 URL (`file_url`)、以及回调服务器地址 (`callback_url`) 给小程序。
4.  **小程序 (执行上传)**:
    *   使用后端返回的签名 URL/凭证，通过 `wx.uploadFile` 或类似方式直接将用户选择的图片文件上传到阿里云 OSS 指定的 `file_key`。
5.  **阿里云 OSS (触发回调)**: 文件上传成功后，OSS 根据上传时设置的回调参数，向后端指定的 `/oss_upload_callback.php` 发送 HTTP 请求，通常包含上传文件的信息 (如 `file_key`, `file_url`, 文件大小等)。
6.  **后端 (`oss_upload_callback.php`)**:
    *   验证回调请求的合法性（例如通过签名验证，确保是 OSS 发起的）。
    *   解析回调数据，获取 `file_key`, `file_url` 等信息。
    *   **(可选) 触发抠图**: 如果请求上传时要求抠图（或者回调参数中包含此信息），则调用阿里云 `SegmentCloth` API 处理 `file_url` 指向的图片。
    *   **(可选) 保存抠图结果**: 将 `SegmentCloth` 返回的抠图结果图片也上传到 OSS (例如到 `clothes_segmented/` 目录)，得到 `segmented_image_url`。
    *   **(可选) 更新数据库**: 如果是添加衣物或照片，可能需要在这里将 `file_url` (原始图) 和 `segmented_image_url` (抠图，如果有) 更新到 `clothes` 或 `photos` 表的相应记录中 (如果记录已提前创建并等待 URL)。
    *   向 OSS 返回成功响应，表示回调处理完成。
7.  **小程序 (获取结果)**: 小程序在发起上传后，可以通过轮询、WebSocket 或等待回调接口的处理结果（如果回调接口设计为能通知前端）来获取最终的文件 URL(s) 和处理状态。

### 6.3 虚拟试衣流程

1.  **小程序**: 用户进入试衣选择页面 (`pages/try_on/select`)。
2.  **小程序 (选择照片)**: 用户从 `/get_photos.php` 获取的照片列表中选择一张个人照片。
3.  **小程序 (选择衣橱 - 新增)**: 用户可以选择一个衣橱进行筛选，或者选择"全部衣橱"。
4.  **小程序 (选择衣物)**: 调用 `/get_clothes.php` (可能带有 `wardrobe_id` 参数) 获取对应衣橱的衣物列表，用户选择一件或多件衣物。
5.  **小程序 -> 后端**: 发送 `photo_id` 和 `clothes_ids` 到 `/try_on.php`。
6.  **后端 (`try_on.php`)**:
    *   查询数据库获取 `photo_id` 对应的照片 OSS URL 和 `clothes_ids` 对应的衣物 OSS URL(s)。
    *   **检查用户试衣次数是否已用完，如果用完则在返回的错误信息中包含 `limit_exceeded`, `show_purchase_button` 和 `show_ad_button` 标记。**
    *   调用阿里云 `OutfitAnyone` API，传入照片和衣物 URL。
    *   记录试衣任务到 `try_on_history` 表 (状态初始为 "pending" 或 "queued")。
    *   返回 `task_id` 给小程序。
7.  **小程序**:
    *   如果次数用完，显示操作菜单，提供"购买次数"、"观看广告获取免费次数"和"返回"选项。
    *   如果用户选择观看广告，调用微信激励视频广告API（`wx.createRewardedVideoAd`）展示广告。
    *   广告成功观看完成后，调用 `/watch_ad_for_try_on.php` 获取一次免费试衣机会。
    *   如果可以试衣，显示"正在生成..."或类似提示，开始轮询或监听状态。
4.  **小程序**: 显示"正在生成..."或类似提示，开始轮询或监听状态。
5.  **小程序 -> 后端 (轮询)**: 定期发送 `task_id` 到 `/get_try_on_status.php`。
6.  **后端 (`get_try_on_status.php`)**:
    *   查询阿里云获取 `task_id` 的最新状态。
    *   如果状态更新（如 "processing", "success", "failed"），更新 `try_on_history` 表。
    *   如果成功，获取结果图片 URL 并更新数据库。
    *   返回当前状态和结果 URL (如果成功) 给小程序。
7.  **小程序**: 根据返回的状态更新 UI (进度条、状态文本)。如果成功，显示试衣结果图片。

### 6.4 OSS 集成概述

*   **核心**: `oss_helper.php` 提供了所有与 OSS 交互的接口，包括生成直传凭证。
*   **上传 (新)**: 主要通过 `/get_oss_upload_token.php` 获取凭证，前端直传，`/oss_upload_callback.php` 处理回调。★
*   **上传 (旧)**: `upload_photo.php`, `upload_image.php` 可能作为备用或已弃用。
*   **读取**: 图片 URL 直接使用 OSS 的公开访问域名 (`ALIYUN_OSS_BUCKET_DOMAIN`)。前端小程序通过这些 URL 直接从 OSS 加载图片。
*   **删除**: `delete_photo.php`, `delete_clothing.php` 在删除数据库记录时，会调用 `oss_helper` 删除 OSS 上对应的文件。
*   **迁移**: `migrate_to_oss.php` 用于将旧的、可能存储在本地服务器上的图片批量迁移到 OSS，并更新数据库中的 URL。

### 6.5 衣物显示流程 (首页)

1.  **小程序 (Index Page)**: 页面加载 (`onLoad`)、显示 (`onShow`) 或用户下拉刷新 (`onPullDownRefresh`) 时触发 `loadClothesData` 函数。
2.  检查用户是否已登录 (检查 `app.globalData.token`)。
3.  若已登录，向后端 `/get_clothes.php` 发起 GET 请求，可能携带 `category` 参数进行分类筛选。
4.  **后端**: 根据用户 token 验证身份，查询 `clothes` 表，返回该用户的衣物列表数据 (JSON 格式)。
5.  **小程序**: 请求成功后，在 `success` 回调中接收衣物列表 `res.data.data`。
6.  对返回的数据进行处理，主要是字段名映射（如 `image_url` -> `imageUrl`）。
7.  调用 `distributeClothes` 函数：遍历处理后的衣物列表，按索引交替将衣物分配到 `leftItems` 和 `rightItems` 两个数组中。
8.  调用 `this.setData({ leftItems, rightItems })` 更新页面数据，WXML 中的 `wx:for` 会根据这两个数组渲染出两列瀑布流效果。
9.  如果是下拉刷新触发，调用 `wx.stopPullDownRefresh()` 停止刷新动画。

### 6.6 衣橱管理流程 (Wardrobe Management Flow) ★

1.  **小程序 (Profile Page / Other Entry)**: 用户通过某个入口（如个人中心页面）导航到衣橱管理功能。
2.  **小程序 (Wardrobe List Page - `pages/wardrobes/index`)**:
    *   页面加载时 (`onLoad` 或 `onShow`) 调用后端 `/get_wardrobes.php` 获取当前用户的所有衣橱列表，支持分页 (`page`, `per_page`)。API 返回衣橱信息，包括 `id`, `name`, `description`, `sort_order`, `is_default`, `clothes_count`。列表按默认、排序、名称排序。不返回 `image_url`。
    *   WXML 使用 `wx:for` 循环渲染衣橱列表，显示名称、描述、衣物数量。
    *   提供"添加新衣橱"的入口按钮，点击后导航到 `add` 页面。
    *   列表中的每个衣橱项提供"编辑"和"删除"操作，以及点击进入详情页的操作。
3.  **添加衣橱**:
    *   导航到 `pages/wardrobes/add` 页面。
    *   WXML 提供 `<input>` 让用户输入衣橱名称、描述、排序 (`sort_order`)。
    *   用户点击"保存"按钮，触发 JS 函数。
    *   JS 函数收集表单数据 (`name`, `description`, `sort_order`)，调用后端 `/add_wardrobe.php` API。
    *   后端验证数据，检查名称是否重复，在 `wardrobes` 表中插入新记录。
    *   API 返回成功后，前端通常 `wx.navigateBack()` 返回列表页，并在列表页的 `onShow` 中刷新数据。
4.  **编辑衣橱**:
    *   用户在列表页点击某个衣橱的"编辑"按钮，导航到 `pages/wardrobes/edit` 页面，并传递 `id`, `name`, `description`, `sort_order` 作为参数。
    *   页面 `onLoad` 时获取参数填充表单。
    *   用户修改信息后点击"保存"。
    *   JS 函数收集表单数据 (`id`, `name`, `description`, `sort_order`)。
    *   调用后端 `/update_wardrobe.php` API，传递衣橱 ID 和修改后的信息。
    *   后端验证数据，更新 `wardrobes` 表中对应记录。
    *   API 返回成功后，返回列表页并刷新。
5.  **删除衣橱**:
    *   用户在列表页点击某个非默认衣橱的"删除"按钮。
    *   **【前端逻辑 - 理想状态】**: 前端应阻止用户删除标记为 `is_default=1` 的衣橱，并显示确认删除弹窗。
    *   **【后端逻辑】**: 用户确认删除后，JS 调用后端 `/delete_wardrobe.php` API，传递要删除的衣橱 ID。后端 **(1)** 检查是否为默认衣橱 (不允许删除)；**(2)** 获取用户的默认衣橱 ID；**(3)** 将待删除衣橱内的所有衣物 (`clothes` 表) 的 `wardrobe_id` 更新为默认衣橱 ID；**(4)** 删除 `wardrobes` 表中指定的衣橱记录。
6.  **查看衣橱详情及衣物**:
    *   用户在列表页点击某个衣橱项，导航到 `pages/wardrobes/detail` 页面，并传递 `wardrobe_id` 和 `name`。
    *   页面 `onLoad` 时获取 `wardrobe_id`。
    *   调用后端 `/get_wardrobe_clothes.php` API，传递衣橱 ID，支持分页获取衣物列表。
    *   前端使用瀑布流等方式渲染该衣橱下的衣物列表。
    *   页面提供 "添加衣物" 按钮，点击后跳转到 `pages/clothing/add` 并预设 `wardrobe_id`。
7.  **移动衣物** (具体实现页面可能不同):
    *   假设在衣物详情页 (`pages/clothing/detail`) 或衣橱管理页 (`pages/clothing/manage`)。
    *   用户选择一件或多件衣物，然后选择一个目标衣橱（可能通过下拉菜单或弹出选择器）。
    *   JS 收集选中的衣物 ID 列表和目标衣橱 ID。
    *   调用后端 `/move_clothes.php` API，传递 `clothes_ids` 和 `target_wardrobe_id`。
    *   后端遍历 `clothes_ids`，更新对应衣物记录的 `wardrobe_id` 字段为 `target_wardrobe_id`。
    *   API 返回成功后，前端根据需要刷新相关视图（如来源衣橱详情、目标衣橱详情、衣物列表等）。

+ ### 6.7 穿搭管理流程 (Outfit Management Flow) ★
+ 
+ 1.  **小程序 (Tab Bar / Other Entry)**: 用户点击底部新增的"穿搭"Tab，进入 `pages/outfits/index` 页面。
+ 2.  **小程序 (Outfit List Page - `pages/outfits/index`)**:
+     *   页面加载时 (`onLoad` 或 `onShow`) 调用 `app.getOutfits()` 获取用户已保存的穿搭列表，支持分页。
+     *   `app.getOutfits()` 内部可能先检查本地缓存，然后调用后端 `/get_outfits.php` API 获取最新数据，并更新缓存。
+     *   WXML 使用 `wx:for` 渲染穿搭列表，通常显示穿搭名称和缩略图 (`thumbnail_url`)。
+     *   提供"创建新穿搭"的入口按钮，点击后导航到 `add` 页面。
+     *   列表中的每个穿搭项提供"查看详情"、"编辑"和"删除"操作。
+ 3.  **创建/编辑穿搭**:
+     *   导航到 `pages/outfits/add` 页面（编辑时可能携带 `outfitId`）。
+     *   提供界面让用户选择衣物（可能需要调用 `/get_clothes.php` 或 `/get_wardrobe_clothes.php`）。
+     *   用户在画布或预览区域调整衣物位置、层叠顺序等，形成搭配。
+     *   提供输入框设置穿搭名称、描述。
+     *   (可选) 自动或手动生成/上传穿搭缩略图 (`thumbnail`)。
+     *   用户点击"保存"按钮。
+     *   JS 函数收集穿搭数据，包括名称、描述、缩略图 URL 以及包含衣物信息和布局的 `items` 数据结构 (通常是 JSON)。
+     *   调用 `app.saveOutfit(outfitData)`。
+     *   `app.saveOutfit()` 内部更新本地缓存，并调用后端 `/save_outfit.php` API。
+     *   后端 API 验证数据，在 `outfits` 表中插入或更新记录。
+     *   API 返回成功后，前端通常 `wx.navigateBack()` 返回列表页，并在列表页的 `onShow` 中刷新数据。
+ 4.  **查看穿搭详情**:
+     *   用户在列表页点击某个穿搭项，导航到 `pages/outfits/detail` 页面，并传递 `outfitId`。
+     *   页面 `onLoad` 时获取 `outfitId`，调用 `app.getOutfits()` 或直接从缓存/API获取该穿搭的详细数据 (`outfit_data`)。
+     *   解析 `outfit_data` JSON，渲染穿搭中的衣物图片及其布局。
+ 5.  **删除穿搭**:
+     *   用户在列表页点击某个穿搭的"删除"按钮。
+     *   显示确认删除弹窗。
+     *   用户确认后，JS 调用 `app.deleteOutfit(outfitId)`。
+     *   `app.deleteOutfit()` 内部先移除本地缓存，然后调用后端 `/delete_outfit.php` API，传递要删除的穿搭 ID。
+     *   后端 API 从 `outfits` 表中删除记录。
+     *   API 返回成功后，前端刷新列表。

+ ### 6.8 智能穿搭推荐流程 (Smart Outfit Recommendation Flow) ★
+ 
+ 1.  **小程序 (Entry Point)**: 用户通过某个入口（如个人中心、首页推荐按钮等）进入智能穿搭推荐页面 (`pages/smart_outfit/smart_outfit`)。
+ 2.  **小程序 (获取用户位置)**: 页面加载时请求用户地理位置授权，获取当前经纬度信息。
+ 3.  **小程序 -> 后端 (智能推荐请求)**: 调用 `/get_outfit_recommendation.php` API，传递用户位置信息（可选）。
+ 4.  **后端 (天气数据获取)**:
+     *   使用用户位置信息调用天气API获取当前天气状况、温度、季节等信息。
+     *   如果无位置信息，使用默认城市或备用天气数据。
+ 5.  **后端 (衣物数据收集)**:
+     *   调用 `getCategoryCodesByType` 函数获取每个基础分类（如上衣、下装、外套等）对应的所有分类代码。
+     *   这包括系统默认分类（如 `tops`, `pants`）和用户自定义分类（如 `custom_1234567890_1_abc123`）。
+     *   通过 `getUserClothingByCategory` 函数从数据库获取每个分类下的用户衣物。
+ 6.  **后端 (智能推荐算法)**:
+     *   根据天气条件、季节、用户衣物库存等因素执行推荐逻辑。
+     *   优先尝试调用AI推荐（`tryGenerateAIOutfit`），使用Gemini API进行智能分析。
+     *   如果AI推荐失败，回退到基于规则的随机推荐算法。
+     *   生成包含上衣、下装、外套、鞋子、配饰等的完整穿搭组合。
+ 7.  **后端 (结果处理)**:
+     *   为每个推荐的衣物生成推荐理由（基于天气、颜色搭配、风格等）。
+     *   构建完整的推荐响应，包括穿搭组合、天气信息、推荐说明等。
+ 8.  **小程序 (结果展示)**: 
+     *   接收推荐结果，在页面上展示天气信息和推荐的穿搭组合。
+     *   用户可以查看详细的推荐理由，保存喜欢的搭配，或重新生成推荐。
+     *   提供与穿搭创建功能的整合，允许用户基于推荐结果创建新的穿搭组合。
+ 
### 6.9 批量上传衣物流程 (Batch Upload Flow) ★

1.  **小程序 (Index Page / Other Entry)**: 用户在主页或其他入口选择批量上传衣物，导航到 `pages/clothing/batch-upload/index` 页面。
2.  **小程序 (Batch Upload Page)**:
    *   用户点击选择图片按钮，调用 `wx.chooseMedia` 选择多张图片。
    *   页面显示上传状态区域，包括待上传数量、处理进度等。
    *   遍历选中的图片列表，依次进行上传和分析处理。
3.  **小程序 -> 后端 (图片上传)**: 对于每张选中的图片，调用后端 API `/upload_image.php` 或通过新的 OSS 直传流程将图片上传到服务器或 OSS。
4.  **后端 (`/upload_image.php` 或 OSS 回调)**: 接收图片数据，保存到 OSS，返回图片的 OSS/CDN URL。
5.  **小程序 -> 后端 (AI 分析)**: 图片上传成功后，前端将图片 URL 和预设的提示词发送给后端 API `/analyze_clothing.php`。
6.  **后端 (`/analyze_clothing.php`)**:
    *   接收图片 URL (或 Base64 数据) 和提示词。
    *   调用配置的 Gemini API Proxy 服务，将图片和提示词发送给 Gemini API 进行分析。
    *   解析 Gemini API 返回的结果，提取衣物类别、标签、名称、颜色等信息。
    *   将分析结果返回给前端。
7.  **小程序 (处理分析结果)**:
    *   前端接收到 AI 分析结果。
    *   如果分析成功，使用返回的数据填充衣物列表项。
    *   如果分析失败（例如返回格式不正确），使用默认的模拟数据填充列表项，并标记为分析失败。
    *   在页面上展示衣物列表，每项包含图片预览和可编辑的衣物信息（名称、类别、标签、颜色等）。
    *   用户可以点击"详情"按钮展开编辑区域，修改AI分析的结果或手动填写信息。
    *   用户可以点击"收起"按钮关闭编辑区域。
8.  **小程序 -> 后端 (批量保存)**: 用户点击"保存全部衣物"按钮。
9.  前端收集所有衣物列表项的数据，包括图片 URL 和编辑后的信息。
10. 将所有衣物数据批量发送给后端 API（可能是一个新的批量保存接口，或者多次调用 `add_clothing.php`）。
11. **后端 (批量保存接口)**: 接收批量衣物数据。
12. 遍历衣物列表，将每件衣物信息保存到 `clothes` 表中，并处理与衣橱的关联（关联到默认衣橱）。
13. 返回批量保存的结果（成功/失败数量）给前端。
14. 小程序显示保存结果，并更新状态区域。

### 6.10 淘宝客商品推荐流程 (Taobao Affiliate Recommendation Flow) ★

1.  **小程序 (Entry Point)**: 用户通过底部导航栏或其他入口进入推荐穿搭页面 (`pages/recommended_outfits/index/index`)，该页面包含"穿搭"和"好物"两个Tab。
2.  **小程序 (切换到好物Tab)**: 用户点击"好物"Tab，触发 `onTabChange` 函数。
3.  **小程序 -> 后端 (商品请求)**: 调用 `/get_taobao_products.php` API，传递分页参数和可选的搜索关键词。
4.  **后端 (淘宝商品获取)**:
    *   通过淘宝联盟API获取商品数据，可能包括热销商品、优惠商品等。
    *   处理返回的商品数据，标准化字段名称和格式。
    *   返回处理后的商品列表和分页信息。
5.  **小程序 (展示商品列表)**:
    *   接收并显示淘宝商品列表，包括商品图片、标题、价格、优惠信息等。
    *   提供商品搜索功能，用户可以输入关键词搜索特定商品。
6.  **小程序 (用户点击商品)**:
    *   用户点击感兴趣的商品，触发 `goToProductDetail` 或 `getProductLink` 函数。
    *   小程序将选中商品的ID发送给后端 `/convert_to_tpwd.php` API。
7.  **后端 (淘口令生成)**:
    *   接收商品ID和优惠链接(如有)。
    *   调用淘宝联盟API转换为淘口令，确保包含商品优惠信息。
    *   如果转换失败，尝试使用备用方法 `getOriginalLink` 获取普通链接。
8.  **小程序 (淘口令复制)**:
    *   接收淘口令或普通链接，调用 `wx.setClipboardData` 将其复制到用户剪贴板。
    *   显示提示信息，引导用户打开淘宝APP粘贴淘口令。
9.  **用户 (在淘宝APP中使用)**:
    *   用户前往淘宝APP，粘贴淘口令。
    *   淘宝APP识别淘口令，跳转到对应商品详情页，用户可以购买商品。

### 6.6 观看广告获取免费试衣次数流程

1.  **小程序**: 用户在试衣过程中，如果次数用完，系统会显示提示弹窗。
2.  **小程序 (显示选项)**: 当试衣次数用完时，系统会首先显示提示消息，然后显示一个操作菜单，包含"购买次数"、"观看广告获取免费次数"和"返回"选项。
3.  **小程序 (初始化广告)**: 在页面加载时，小程序会初始化激励视频广告组件，设置广告单元ID(`adunit-feb43eda47b7263e`)。
4.  **小程序 (观看广告)**: 用户点击"观看广告获取免费次数"选项，触发广告显示。
5.  **小程序 (广告结束处理)**:
    *   监听广告关闭事件，判断用户是否完整观看广告。
    *   如果用户完整观看广告，调用后端 `/watch_ad_for_try_on.php` API，传递 `completed=1` 参数。
    *   如果用户提前关闭广告，调用后端 API 传递 `completed=0` 参数。
6.  **后端 (`watch_ad_for_try_on.php`)**:
    *   验证用户身份和请求参数。
    *   如果用户完整观看广告，增加用户的免费试衣次数（`free_try_on_count`）。
    *   返回更新后的试衣次数信息给小程序。
7.  **小程序**: 根据 API 返回结果，显示获取成功的提示，并重新发起试衣请求。

*   **新增广告解锁试衣次数功能** (2025-05-18):
    *   当用户试衣次数用完时，提供"观看广告获取免费次数"的选项。
    *   在`miniprogram/pages/try_on/start/index.js`中集成微信激励视频广告API（`wx.createRewardedVideoAd`）。
    *   添加广告加载、错误处理和关闭事件监听，确保广告体验流畅。
    *   创建新的后端API端点`/watch_ad_for_try_on.php`，处理广告观看完成后的试衣次数增加逻辑。
    *   优化次数不足提示流程，使用`wx.showActionSheet`提供多选项菜单（购买、看广告、返回）。
    *   改进UI/UX，保证操作流程简洁明了，增强用户体验。

## 7. 数据库 Schema 概要

以下是项目核心数据表的结构摘要：

*   **`users`**: 存储小程序用户信息
    *   `id`: INT, Primary Key, Auto Increment (`id = 1` 被 `create_demo_account.sql` 保留为 '体验账号') ★
    *   `openid`: VARCHAR, Unique - 微信用户唯一标识
    *   `session_key`: VARCHAR - 微信会话密钥 (登录时获取，可能不持久存储或加密存储)
    *   `nickname`: VARCHAR - 用户昵称
    *   `avatar_url`: VARCHAR - 用户头像 URL
    *   `gender`: INT - 性别 (0:未知, 1:男, 2:女)
    *   `status`: INT, Default 1 - 用户状态 (1:正常, 其他值可能表示禁用等)
    *   `created_at`: TIMESTAMP, Default CURRENT_TIMESTAMP
    *   `updated_at`: TIMESTAMP, Default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

*   **`clothes`**: 存储用户衣物信息
    *   `id`: INT, Primary Key, Auto Increment
    *   `user_id`: INT, Foreign Key -> `users(id)`
    *   `wardrobe_id`: INT, NULLABLE, Foreign Key -> `wardrobes(id)` ON DELETE SET NULL - ★ 衣物所属衣橱ID
    *   `name`: VARCHAR - 衣物名称
    *   `category`: VARCHAR - 衣物类别 (e.g., 'tops', 'pants')
    *   `image_url`: VARCHAR(255) - 衣物图片 URL (通常是处理后/抠图后的 OSS URL)
    *   `original_image_url`: VARCHAR(255) - 原始上传的衣物图片 URL (OSS URL)
    *   `tags`: TEXT - 衣物标签 (可能是逗号分隔的字符串或 JSON 字符串)
    *   `description`: TEXT - 衣物描述 (可能是 JSON 字符串存储颜色、品牌等)
    *   `created_at`: TIMESTAMP, Default CURRENT_TIMESTAMP
    *   `updated_at`: TIMESTAMP, Default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

*   **`photos`**: 存储用户上传的用于试穿的照片
    *   `id`: INT, Primary Key, Auto Increment
    *   `user_id`: INT, Foreign Key -> `users(id)`
    *   `image_url`: VARCHAR(255) - 照片的 OSS URL
    *   `type`: VARCHAR - 照片类型 (e.g., 'full', 'half' - 全身/半身)
    *   `description`: VARCHAR - 照片描述
    *   `created_at`: TIMESTAMP, Default CURRENT_TIMESTAMP

*   **`try_on_history`**: 存储虚拟试衣任务记录，**并用于每日试衣次数限制判断**
    *   `id`: INT, Primary Key, Auto Increment
    *   `user_id`: INT, Foreign Key -> `users(id)`
    *   `photo_id`: INT, Foreign Key -> `photos(id)`
    *   `clothes_ids`: JSON - 参与试衣的衣物 ID 列表 (e.g., `[39, 44]`)
    *   `result_image_url`: VARCHAR(255) - 试衣结果图片的 OSS URL
    *   `task_id`: VARCHAR - AI 试衣接口返回的任务 ID
    *   `status`: VARCHAR - 任务状态 (e.g., 'pending', 'processing', 'success', 'failed')
    *   `error_message`: TEXT - 任务失败时的错误信息
    *   `created_at`: TIMESTAMP, Default CURRENT_TIMESTAMP
    *   `updated_at`: TIMESTAMP, Default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

*   **`announcements`**: 存储系统公告信息
    *   `id`: INT, Primary Key, Auto Increment
    *   `title`: VARCHAR - 公告标题
    *   `content`: TEXT - 公告内容
    *   `start_time`: DATETIME - 生效开始时间
    *   `end_time`: DATETIME - 生效结束时间
    *   `status`: TINYINT, Default 1 - 状态 (0=禁用, 1=启用)
    *   `created_at`: DATETIME, Default CURRENT_TIMESTAMP
    *   `updated_at`: DATETIME, NULLABLE ON UPDATE CURRENT_TIMESTAMP

*   **`admin_users`**: 存储管理后台用户信息
    *   `id`: INT, Primary Key, Auto Increment
    *   `username`: VARCHAR, Unique - 管理员用户名
    *   `password_hash`: VARCHAR - 加密后的管理员密码
    *   `created_at`: TIMESTAMP, Default CURRENT_TIMESTAMP

*   **`api_usage`**: 记录 AI API 调用情况 (用于统计或计费)
    *   `id`: INT, Primary Key, Auto Increment
    *   `user_id`: INT, Foreign Key -> `users(id)`
    *   `api_type`: VARCHAR - 调用的 API 类型 ('tryOn', 'segment' 等)
    *   `timestamp`: TIMESTAMP, Default CURRENT_TIMESTAMP - 调用时间
    *   `cost`: DECIMAL - 调用成本 (可选)
    *   `status`: VARCHAR - 调用状态 ('success', 'failed')
    *   `request_payload`: TEXT - API 请求内容 (部分或全部)
    *   `response_payload`: TEXT - API 响应内容 (部分或全部)
    *   `task_id`: VARCHAR - 关联的异步任务 ID (如果适用)
    *   `error_message`: TEXT - 调用失败时的错误信息

*   **`wardrobes`**: ★ 用户定义的衣橱 (用于组织衣物)
    *   `id`: INT, Primary Key, Auto Increment
    *   `user_id`: INT, Foreign Key -> `users(id)` ON DELETE CASCADE
    *   `name`: VARCHAR - 衣橱名称
    *   `description`: TEXT - 衣橱描述
    *   `image_url`: VARCHAR(255), NULLABLE - 衣橱封面图片 URL (当前后端 API 未处理)
    *   `sort_order`: INT, Default 0 - 排序值，越小越靠前
    *   `is_default`: TINYINT(1), Default 0 - 是否为默认衣橱 (每个用户应有且仅有一个is_default=1)
    *   `created_at`: TIMESTAMP, Default CURRENT_TIMESTAMP
    *   `updated_at`: TIMESTAMP, NULLABLE ON UPDATE CURRENT_TIMESTAMP

*   **`outfits`**: ★ 用户创建的穿搭组合
    *   `id`: INT AUTO_INCREMENT PRIMARY KEY
    *   `user_id`: INT NOT NULL, Foreign Key -> `users(id)` ON DELETE CASCADE
    *   `name`: VARCHAR(100) NOT NULL
    *   `description`: TEXT NULL
    *   `thumbnail_url`: TEXT NULL COMMENT '缩略图URL'
    *   `outfit_data`: LONGTEXT NOT NULL COMMENT '以JSON格式存储的穿搭布局数据'
    *   `created_at`: DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    *   `updated_at`: DATETIME NULL ON UPDATE CURRENT_TIMESTAMP

*(具体字段类型和约束请以最新的 SQL 文件为准)*

*   **`recommended_categories`**: ★ 推荐穿搭分类表
    *   `category_id`: INT AUTO_INCREMENT PRIMARY KEY
    *   `category_name`: VARCHAR(100) NOT NULL UNIQUE - 分类名称
    *   `created_at`: TIMESTAMP DEFAULT CURRENT_TIMESTAMP

*   **`recommended_outfits`**: ★ 推荐穿搭主表
    *   `outfit_id`: INT AUTO_INCREMENT PRIMARY KEY
    *   `category_id`: INT NOT NULL, FOREIGN KEY -> `recommended_categories(category_id)` - 所属分类ID
    *   `title`: VARCHAR(255) NOT NULL - 穿搭标题
    *   `description`: TEXT - 穿搭描述
    *   `image_url`: VARCHAR(255) NOT NULL - 穿搭封面图片URL
    *   `status`: ENUM('draft', 'published', 'archived') DEFAULT 'draft' - 穿搭状态
    *   `created_at`: TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    *   `updated_at`: TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

*   **`recommended_outfit_items`**: ★ 推荐穿搭包含的衣物（商品）列表
    *   `item_id`: INT AUTO_INCREMENT PRIMARY KEY
    *   `outfit_id`: INT NOT NULL, FOREIGN KEY -> `recommended_outfits(outfit_id)` ON DELETE CASCADE - 所属推荐穿搭ID
    *   `clothing_id`: INT NOT NULL, FOREIGN KEY -> `clothes(id)` - 关联的用户衣物（或商品）ID
    *   `position_x`: DECIMAL(10, 2) - 衣物在穿搭图中的X坐标
    *   `position_y`: DECIMAL(10, 2) - 衣物在穿搭图中的Y坐标
    *   `scale`: DECIMAL(10, 2) DEFAULT 1.0 - 衣物在穿搭图中的缩放比例
    *   `z_index`: INT DEFAULT 0 - 衣物在穿搭图中的层叠顺序
    *   `created_at`: TIMESTAMP DEFAULT CURRENT_TIMESTAMP

*   **`image_analysis`**: ★ 个人形象分析记录表
    *   `id`: INT AUTO_INCREMENT PRIMARY KEY
    *   `user_id`: INT NOT NULL, Foreign Key -> `users(id)`
    *   `gender`: TINYINT COMMENT '1-男, 2-女'
    *   `concerns`: TEXT COMMENT '用户关注点'
    *   `photo_urls`: JSON COMMENT '用户上传的照片URL列表'
    *   `status`: ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending'
    *   `payment_status`: ENUM('unpaid', 'paid', 'refunded') DEFAULT 'unpaid'
    *   `analysis_result`: JSON COMMENT 'AI分析结果'
    *   `created_at`: TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    *   `updated_at`: TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
*   **`orders`**: ★ 支付订单表
    *   `id`: INT AUTO_INCREMENT PRIMARY KEY
    *   `user_id`: INT NOT NULL
    *   `related_id`: INT NOT NULL COMMENT '关联的业务ID，如image_analysis的ID'
    *   `related_type`: VARCHAR(50) NOT NULL COMMENT '关联业务类型，如image_analysis'
    *   `order_sn`: VARCHAR(64) UNIQUE NOT NULL COMMENT '商户订单号'
    *   `transaction_id`: VARCHAR(64) COMMENT '微信支付交易号'
    *   `amount`: DECIMAL(10, 2) NOT NULL
    *   `status`: ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending'
    *   `created_at`: TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    *   `paid_at`: DATETIME

## 8. API Endpoint 概要

主要 API 端点分组：

*   **认证**: `/login.php`, `/verify_token.php`
*   **衣物管理**: `/add_clothing.php`, `/get_clothes.php`, `/delete_clothing.php`
*   **照片管理**: `/upload_photo.php` (旧?), `/get_photos.php`, `/delete_photo.php`
*   **图片处理 (新流程)**: `/get_oss_upload_token.php`, `/oss_upload_callback.php` ★
*   **图片处理 (旧流程)**: `/upload_image.php` (旧?)
*   **AI 分析**: `/analyze_clothing.php` ★
*   **批量保存**: (待定，可能是一个新的批量保存接口，或多次调用 `add_clothing.php`)
*   **虚拟试衣**: `/try_on.php`, `/get_try_on_status.php`, `/get_try_on_history.php`, `/watch_ad_for_try_on.php` (通过观看广告获取试穿次数)
*   **衣橱管理 (Wardrobe Management)**: `/add_wardrobe.php`, `/get_wardrobes.php`, `/update_wardrobe.php`, `/delete_wardrobe.php`, `/move_clothes.php`, `/get_wardrobe_clothes.php` ★
*   **管理后台**: `/get_admin_*.php`, `/update_admin_*.php`, `/delete_admin_*.php`, `/get_dashboard_data.php`, `/get_admin_system_settings.php`, `/update_admin_system_settings.php`, `/login_backend/get_announcements_list.php` (获取公告列表), `/login_backend/admin_save_announcement.php` (保存/更新公告), `/login_backend/delete_announcement.php` (删除公告)
*   **穿搭管理 (Outfit Management)**: `/save_outfit.php`, `/get_outfits.php`, `/delete_outfit.php` ★
*   **智能穿搭推荐 (Smart Outfit Recommendation)**: `/get_outfit_recommendation.php` (基于天气/季节的AI智能推荐，支持自定义分类) ★
*   **公告管理 (用户端)**: `/get_announcement.php` (获取当前活跃公告)
*   **淘宝客 (Taobao Affiliate)**: `/get_taobao_products.php` (获取商品列表), `/get_taobao_link.php` (获取商品链接), `/convert_to_tpwd.php` (生成淘口令)
*   **个人形象分析**: `/create_image_analysis.php`, `/get_image_analysis.php`, `/get_image_analysis_history.php`, `/update_image_analysis_payment.php`, `/request_image_analysis.php` ★

*(详细参数和响应格式可参考原始 `README.md` 或直接阅读后端代码)*

## 9. 安装、配置与部署

### 9.1 环境要求

*   PHP >= 7.4
*   MySQL >= 5.7
*   Composer
*   Web 服务器 (Nginx 或 Apache)
*   微信开发者工具

### 9.2 后端部署

1.  将 `login_backend/` 目录部署到服务器。
2.  进入 `login_backend/` 目录，运行 `composer install` 安装依赖。
3.  配置 Web 服务器将请求指向 `login_backend/` 目录，并设置 URL 重写规则（参考 `.htaccess` 文件内容，如果使用 Apache）。
4.  创建 MySQL 数据库。
5.  执行 `数据库/` 目录下的 SQL 脚本创建表结构 (注意脚本执行顺序，如先创建 `users`, `wardrobes` 再创建 `clothes`，以及执行 `update_*.sql` 脚本)。
6.  **配置 `config.php`**: 填入正确的数据库连接信息、微信 AppID/Secret、阿里云 AccessKey ID/Secret、OutfitAnyone API Key、OSS Endpoint/Bucket/域名。**这是关键步骤**。
7.  **(可选) 执行 `create_demo_account.sql`**: 在数据库中创建 ID 为 1 的 '体验账号' 并添加示例数据，方便测试和演示。★
8.  **(可选) 执行 `migrate_wardrobes.php` 或其他迁移脚本**: 根据版本需要，可能需要执行 PHP 迁移脚本来处理数据。★
9.  **创建日志目录**: 在项目根目录下创建 `logs/` 目录，并确保 Web 服务器用户 (如 `www-data`) 对该目录有写入权限 (`chmod -R 775 logs && chown -R www-data:www-data logs`)。否则 `wardrobe_logger.php` 将无法写入日志。★
10. 确保 Web 服务器对 `login_backend/` 目录有读取权限。

### 9.3 前端部署

1.  使用微信开发者工具导入 `miniprogram/` 目录。
2.  在 `project.config.json` 中填入你的微信小程序 AppID。
3.  **修改 API 地址**: 在 `app.js` 或封装的 API 请求函数中，将 API 的基地址修改为你的后端部署地址。
4.  在微信公众平台后台，将你的后端 API 域名和阿里云 OSS 域名 (`ALIYUN_OSS_BUCKET_DOMAIN`) 添加到小程序的 `request合法域名` 和 `downloadFile合法域名` 白名单中。
5.  编译并预览小程序。

### 9.4 阿里云服务配置

1.  **OSS**:
    *   创建 Bucket。
    *   设置 Bucket 读写权限为"公共读"。
    *   配置跨域资源共享 (CORS)，允许来自小程序或前端域名的 GET 请求。
2.  **视觉智能开放平台**:
    *   开通"图像分割 (SegmentCloth)"和"虚拟试衣 (OutfitAnyone)"服务。
    *   获取 AccessKey ID 和 Secret。
    *   获取 OutfitAnyone 的 API Key。
    *   确保账户有足够的 API 调用额度。

## 10. 开发注意事项

*   **配置文件安全**: `config.php` 包含敏感信息，切勿将其提交到公共代码仓库。应使用环境变量或服务器上的安全配置文件进行管理。
*   **错误处理**: 后端 API 应提供清晰的错误信息和 HTTP 状态码。前端应妥善处理 API 错误，向用户提供友好的提示。查看 `logs/` 目录下的日志文件有助于后端调试。★
*   **异步任务**: 虚拟试衣是异步的，前端需要实现轮询或使用其他机制（如 WebSocket，如果后端支持）来获取最终结果。轮询逻辑需要健壮，处理超时和错误状态。
*   **OSS 成本**: 注意 OSS 存储和流量费用，以及 AI API 的调用费用。可以考虑设置 OSS 生命周期规则自动清理旧的试衣结果。
*   **依赖管理**: 使用 Composer 管理 PHP 依赖，定期更新。
*   **数据库迁移**: 对数据库结构的任何更改都应创建新的 SQL 脚本，方便版本控制和部署。
*   **前后端一致性**: ★ 之前的版本在衣橱删除/编辑逻辑上存在前后端不一致，现已通过更新后端逻辑（如 `add_clothing.php` 对衣橱ID的处理）和本文档的描述进行了明确和统一，开发时需注意保持一致。
*   **CDN 与 API 调用**: ★ 项目已启用 CDN。在调用需要直接访问图片内容的第三方 API (如阿里云服饰分割) 时，需确保传递的是原始的 OSS URL 而非 CDN URL。相关代码 (`upload_image.php`, `oss_helper.php`) 已进行修改以处理此问题。
*   **新增功能**: ★ 最近新增了"穿搭" (Outfit) 功能，允许用户保存和管理服装搭配组合。

## 11. 近期主要更新 (相对于 2025-04-18 版本)

*   **新增"穿搭"功能**:
    *   允许用户创建、保存、查看、编辑和删除服装搭配组合。
    *   新增数据库表 `outfits`。
    *   新增后端 API `/save_outfit.php`, `/get_outfits.php`, `/delete_outfit.php`。
    *   新增小程序页面 `pages/outfits/*` 和底部 "穿搭" Tab。
    *   更新 `app.js` 添加穿搭相关的全局数据管理和同步逻辑。
*   **CDN 迁移与配置**:
    *   图片访问已切换为使用阿里云 CDN (`images.alidog.cn`)。
    *   `config.php` 中添加了 `ALIYUN_CDN_DOMAIN` 和 `USE_CDN` 配置。
    *   `oss_helper.php` 中 `getFileUrl` 方法会根据 `USE_CDN` 配置生成 CDN 或 OSS URL。
*   **CDN 迁移脚本增强**:
    *   `migrate_to_cdn_complete.php` 增加了 `--all` 参数，可以为所有用户执行数据库 URL 迁移。
*   **抠图功能兼容 CDN**:
    *   修复了使用 CDN URL 调用阿里云服饰分割 API 失败的问题。
    *   `upload_image.php` 中的 `segmentClothingImage` 和 `callStandardSegmentCloth` 函数在调用 API 前会将 CDN URL 转换回 OSS URL。
    *   `oss_helper.php` 中添加了 `convertCdnUrlToOssUrl` 方法。
*   **新增每日试衣次数限制**:
    *   为控制试衣成本，现在限制每位用户每天只能成功试衣一次（次日8点刷新）。
    *   后端 `/try_on.php` 添加了次数检查逻辑，利用 `try_on_history` 表统计用户当天的试衣次数。
    *   前端 `pages/try_on/select/index.js` 在接收到后端返回的次数限制错误时，会停止进度动画并显示友好的提示弹窗。

*   **新增公告管理功能**:
    *   新增数据库表 `announcements` 用于存储公告信息。
    *   新增后端API `/login_backend/admin_save_announcement.php` (保存/更新公告), `/login_backend/delete_announcement.php` (删除公告), `/login_backend/get_announcements_list.php` (获取公告列表) 用于管理后台对公告的CRUD操作。
    *   新增用户端API `/get_announcement.php` 用于获取当前活跃公告。
    *   前端 (`pages/index/index.js` 和 `pages/index/index.wxml`) 实现了首页获取和展示活跃公告弹窗的功能，并记录用户已读状态。

*   **穿搭列表显示优化** (2025-04-22):
    *   在 `index.js` 的 `processOutfitsData` 函数中添加了穿搭边界盒计算和居中逻辑，确保穿搭在列表页居中显示。
    *   通过计算衣物边界，动态调整缩放比例，使整个穿搭都能在预览框内完整显示。
    *   修改 `index.wxml` 中的穿搭渲染代码，使用计算后的预览位置和缩放比例，解决部分衣物显示不全的问题。
    *   优化了 `index.wxss` 中 `outfit-view-mini` 相关样式，确保预览内容始终居中并可见。

*   **穿搭编辑功能增强** (2025-04-22):
    *   在编辑穿搭页面 (`edit.wxml`) 添加了穿搭名称输入框，与添加穿搭页面保持一致。
    *   在 `edit.js` 中添加了 `onNameInput` 函数处理名称输入事件。
    *   确保编辑过程中可以修改穿搭名称，提升用户体验的一致性。

*   **衣物删除安全检查** (2025-04-22):
    *   在 `detail.js` 中添加了 `checkClothingInOutfits` 函数，检查衣物是否在任何穿搭中使用。
    *   修改 `deleteClothing` 函数，在执行删除前先进行穿搭检查。
    *   当衣物被用于穿搭时，阻止删除并显示友好提示，列出使用该衣物的穿搭名称。
    *   防止用户误删衣物导致穿搭中出现空白数据，提高数据完整性。

*   **UI/UX 优化** (2025-04-22):
    *   改进衣物详情页删除按钮样式，从显眼的红色块改为更简洁的灰色带图标设计。
    *   调整编辑按钮与删除按钮的比例，使编辑操作更加突出。
    *   优化交互状态，删除按钮只在点击时才显示红色，减少误操作风险。

*   **穿搭分享功能增强** (2025-04-23):
    *   在穿搭详情页 (`pages/outfits/detail/detail.js`) 实现分享到微信好友和朋友圈的功能。
    *   分享链接中包含穿搭ID、分享者用户ID和临时访问Token，以允许其他用户查看分享的穿搭。
    *   优化了穿搭加载逻辑，优先使用分享Token进行API请求，并增加了多种备选方案（如使用本地数据、显示空穿搭）来应对Token无效或数据获取失败的情况。
    *   当用户通过分享链接访问且无法获取穿搭数据时，点击"我也要穿搭"按钮会引导用户到登录页面。

+ *   **新增批量上传衣物功能** (2025-04-23):
+     *   允许用户一次选择并上传多张衣物图片。
+     *   集成AI分析能力，自动识别衣物的类别、标签、名称和颜色。
+     *   提供批量编辑界面，用户可批量修改或确认AI识别结果。
+     *   支持将多件衣物批量保存到默认衣橱。

*   **智能穿搭推荐自定义分类支持** (2025-04-24):
    *   **扩展分类获取机制**: 修改 `get_outfit_recommendation.php` 中的 `getUserClothingByCategory` 函数，支持从用户自定义分类中获取衣物，不再局限于系统默认分类。
    *   **新增分类映射函数**: 增加 `getCategoryCodesByType` 函数，实现基础分类类型（如 `tops`, `outerwear`）到具体分类代码的智能映射，包括系统分类和用户自定义分类。
    *   **智能分类识别**: 推荐算法现在能够识别并使用用户创建的自定义衣物分类（格式：`custom_[timestamp]_[userid]_[randomstring]`），大大扩展了可推荐的衣物范围。
    *   **增强推荐准确性**: 通过包含更多用户个人衣物，智能穿搭推荐能够生成更贴合用户实际衣橱的个性化搭配建议。
    *   **保持兼容性**: 更新完全向后兼容，对于没有自定义分类的用户，推荐功能仍使用原有的系统分类逻辑。

*   **新增淘宝客功能** (2025-04-28):
    *   在推荐穿搭页面添加"好物"Tab，展示淘宝商品推荐。
    *   集成淘宝联盟API，获取热销商品和优惠商品数据。
    *   实现商品搜索功能，允许用户查找特定商品。
    *   提供一键复制淘口令功能，便于用户在淘宝APP中查看和购买。
    *   支持优惠券信息展示，提高用户购买转化率。
    *   针对不同的API调用场景，提供多种备用方案，确保功能稳定可靠。

*   **拓展淘宝商品推荐分类** (2025-04-30):
    *   新增多个商品推荐分类，如"春季新品"、"夏日防晒"、"男装精选"、"女装热卖"、"儿童服饰"等。
    *   优化分类管理机制，支持后台动态配置和更新商品分类。
    *   为每个分类实现独立的数据获取流程，确保分类内容的专业性和相关性。
    *   增强商品推荐算法，根据用户历史浏览和搜索偏好提供个性化推荐。
    *   改进UI交互，增加分类快速切换栏，提升用户体验。
    *   完善商品优惠信息展示，突出显示限时特惠和大额优惠券商品。

*   **新增个人形象分析功能** (2025-05-20):
    *   集成付费的个人形象分析模块，用户可通过上传照片获取专业的AI形象报告。
    *   新增前后端模块，包括表单提交、照片上传、微信支付、状态轮询和结果展示。
    *   后端集成Gemini API用于生成分析报告，并新增 `image_analysis` 和 `orders` 数据表来管理分析记录和支付订单。
    *   实现了完整的用户流程，从提交信息到支付成功，再到最终查看详细的分析报告。

*   **新增批量上传衣物功能** (2025-04-23):
    *   允许用户一次选择并上传多张衣物图片。
    *   集成AI分析能力，自动识别衣物的类别、标签、名称和颜色。
    *   提供批量编辑界面，用户可批量修改或确认AI识别结果。
    *   支持将多件衣物批量保存到默认衣橱。

*   **虚拟试衣**: `/try_on.php`, `/get_try_on_status.php`, `/get_try_on_history.php`, `/watch_ad_for_try_on.php` (通过观看广告获取试穿次数)
*   **衣橱管理 (Wardrobe Management)**: `/add_wardrobe.php`, `/get_wardrobes.php`, `/update_wardrobe.php`, `/delete_wardrobe.php`, `/move_clothes.php`, `/get_wardrobe_clothes.php` ★
*   **个人形象分析**: `/create_image_analysis.php`, `/get_image_analysis.php`, `/get_image_analysis_history.php`, `/update_image_analysis_payment.php`, `/request_image_analysis.php` ★
*   **管理后台**: `/get_admin_*.php`, `/update_admin_*.php`, `/delete_admin_*.php`, `/get_dashboard_data.php`, `/get_admin_system_settings.php`, `/update_admin_system_settings.php`, `/login_backend/get_announcements_list.php` (获取公告列表), `/login_backend/admin_save_announcement.php` (保存/更新公告), `/login_backend/delete_announcement.php` (删除公告)
*   **穿搭管理 (Outfit Management)**: `/save_outfit.php`, `/get_outfits.php`, `/delete_outfit.php` ★
*   **智能穿搭推荐 (Smart Outfit Recommendation)**: `/get_outfit_recommendation.php` (基于天气/季节的AI智能推荐，支持自定义分类) ★
