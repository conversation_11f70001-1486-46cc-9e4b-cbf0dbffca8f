<?php
// 引入必要的文件
require_once 'auth.php';
require_once 'db.php';
require_once 'config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 验证管理员token
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 如果有Bearer前缀，去掉它
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
}

if (!$token) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供授权Token'
    ]);
    exit;
}

// 验证管理员token
$auth = new Auth();
$adminData = $auth->verifyAdminToken($token);

if (!$adminData) {
    echo json_encode([
        'error' => true,
        'msg' => '未授权，请先登录管理后台'
    ]);
    exit;
}

// 处理GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'error' => true,
        'msg' => '不支持的请求方法'
    ]);
    exit;
}

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查是否请求特定穿搭详情
    if (isset($_GET['id'])) {
        $outfitId = intval($_GET['id']);
        
        // 获取穿搭详情
        $stmt = $conn->prepare("
            SELECT o.*, c.name as category_name, u.nickname as user_nickname, u.avatar_url as user_avatar
            FROM outfits o
            LEFT JOIN outfit_categories c ON o.category_id = c.id
            LEFT JOIN users u ON o.user_id = u.id
            WHERE o.id = :id
        ");
        $stmt->bindParam(':id', $outfitId, PDO::PARAM_INT);
        $stmt->execute();
        
        $outfit = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$outfit) {
            echo json_encode([
                'status' => 'error',
                'message' => '穿搭不存在'
            ]);
            exit;
        }
        
        // 解析outfit_data JSON，计算衣物数量
        if (isset($outfit['outfit_data']) && !empty($outfit['outfit_data'])) {
            $outfitData = json_decode($outfit['outfit_data'], true);
            
            // 计算衣物数量
            $clothesCount = isset($outfitData['items']) ? count($outfitData['items']) : 0;
            
            // 添加衣物数量信息
            $outfit['clothes_count'] = $clothesCount;
            
            // 确保outfit_data可以在响应中正确解析
            $outfit['outfit_data'] = $outfitData;
            
            // 创建图片URL数组，存储所有衣物图片
            $imageUrls = [];
            
            // 提取所有衣物的图片URL
            if (isset($outfitData['items']) && !empty($outfitData['items'])) {
                foreach ($outfitData['items'] as $item) {
                    if (isset($item['clothing_data']) && isset($item['clothing_data']['image_url'])) {
                        $imageUrls[] = $item['clothing_data']['image_url'];
                    }
                }
            }
            
            // 将图片URL数组添加到穿搭数据中
            $outfit['image_urls'] = $imageUrls;
            
            // 当thumbnail_url为空时，提取第一个衣物的图片URL作为缩略图
            if (empty($outfit['thumbnail_url']) && !empty($imageUrls)) {
                $outfit['thumbnail_url'] = $imageUrls[0];
            }
        } else {
            $outfit['clothes_count'] = 0;
            $outfit['image_urls'] = [];
        }
        
        // 返回结果
        echo json_encode([
            'status' => 'success',
            'data' => [
                'outfits' => [$outfit],
                'total' => 1
            ]
        ]);
        exit;
    }
    
    // 处理列表查询
    // 获取分页参数
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $perPage = isset($_GET['limit']) ? intval($_GET['limit']) : 20; // 默认每页20条
    $offset = ($page - 1) * $perPage;
    
    // 构建查询条件
    $whereClause = "";
    $params = [];
    
    // 处理搜索条件
    if (isset($_GET['search']) && !empty($_GET['search'])) {
        $search = $_GET['search'];
        $whereClause .= " WHERE o.name LIKE :search";
        $params['search'] = "%$search%";
    }
    
    // 按用户ID过滤
    if (isset($_GET['user_id']) && !empty($_GET['user_id'])) {
        $userId = intval($_GET['user_id']);
        $whereClause = $whereClause ? "$whereClause AND o.user_id = :user_id" : " WHERE o.user_id = :user_id";
        $params['user_id'] = $userId;
    }
    
    // 按分类ID过滤
    if (isset($_GET['category_id']) && !empty($_GET['category_id'])) {
        $categoryId = intval($_GET['category_id']);
        $whereClause = $whereClause ? "$whereClause AND o.category_id = :category_id" : " WHERE o.category_id = :category_id";
        $params['category_id'] = $categoryId;
    }
    
    // 查询总数据量
    $countSql = "SELECT COUNT(*) FROM outfits o $whereClause";
    $countStmt = $conn->prepare($countSql);
    foreach ($params as $key => $value) {
        $countStmt->bindValue(":$key", $value);
    }
    $countStmt->execute();
    $totalCount = $countStmt->fetchColumn();
    
    // 查询穿搭列表
    $sql = "
        SELECT o.*, c.name as category_name, u.nickname as user_nickname, u.avatar_url as user_avatar
        FROM outfits o
        LEFT JOIN outfit_categories c ON o.category_id = c.id
        LEFT JOIN users u ON o.user_id = u.id
        $whereClause
        ORDER BY o.created_at DESC
        LIMIT :offset, :per_page
    ";
    
    $stmt = $conn->prepare($sql);
    foreach ($params as $key => $value) {
        $stmt->bindValue(":$key", $value);
    }
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':per_page', $perPage, PDO::PARAM_INT);
    $stmt->execute();
    
    $outfits = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取每个穿搭的衣物数量
    foreach ($outfits as &$outfit) {
        // 从outfit_data字段中解析衣物数量
        if (isset($outfit['outfit_data']) && !empty($outfit['outfit_data'])) {
            $outfitData = json_decode($outfit['outfit_data'], true);
            $outfit['clothes_count'] = isset($outfitData['items']) ? count($outfitData['items']) : 0;
            
            // 将解析后的数据重新赋值给outfit_data字段
            $outfit['outfit_data'] = $outfitData;
            
            // 创建图片URL数组，存储所有衣物图片
            $imageUrls = [];
            
            // 提取所有衣物的图片URL
            if (isset($outfitData['items']) && !empty($outfitData['items'])) {
                foreach ($outfitData['items'] as $item) {
                    if (isset($item['clothing_data']) && isset($item['clothing_data']['image_url'])) {
                        $imageUrls[] = $item['clothing_data']['image_url'];
                    }
                }
            }
            
            // 将图片URL数组添加到穿搭数据中
            $outfit['image_urls'] = $imageUrls;
            
            // 当thumbnail_url为空时，提取第一个衣物的图片URL作为缩略图
            if (empty($outfit['thumbnail_url']) && !empty($imageUrls)) {
                $outfit['thumbnail_url'] = $imageUrls[0];
            }
        } else {
            $outfit['clothes_count'] = 0;
            $outfit['image_urls'] = [];
        }
    }
    
    // 返回结果
    echo json_encode([
        'status' => 'success',
        'data' => [
            'outfits' => $outfits,
            'total' => $totalCount,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($totalCount / $perPage)
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => '获取穿搭列表失败: ' . $e->getMessage()
    ]);
} 