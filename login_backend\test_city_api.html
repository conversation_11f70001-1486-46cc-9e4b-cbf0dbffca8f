<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>城市查询与天气API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1, h2 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            padding: 8px 12px;
            margin-right: 10px;
        }
        button {
            background-color: #4CAF50;
            border: none;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            margin-top: 20px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .note {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 10px 15px;
            margin: 15px 0;
        }
        pre {
            background-color: #f1f1f1;
            padding: 10px;
            overflow-x: auto;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>城市查询与天气API测试</h1>
    
    <div class="note">
        <p>此页面用于测试城市坐标查询API和天气API的联动功能。</p>
        <p>您可以通过坐标或城市名称查询城市信息，然后查询对应城市的天气数据。</p>
    </div>

    <h2>方法一：通过坐标查询</h2>
    <div class="form-group">
        <label for="latitude">纬度:</label>
        <input type="text" id="latitude" placeholder="例如：39.9042">
        <label for="longitude">经度:</label>
        <input type="text" id="longitude" placeholder="例如：116.4074">
        <button onclick="queryByCoordinates()">查询城市</button>
        <button onclick="getWeatherByCoordinates()">直接查询天气</button>
    </div>
    
    <h2>方法二：通过城市名称查询</h2>
    <div class="form-group">
        <label for="cityName">城市名称:</label>
        <input type="text" id="cityName" placeholder="例如：北京">
        <button onclick="queryByCity()">查询城市</button>
    </div>

    <h2>方法三：通过城市ID查询</h2>
    <div class="form-group">
        <label for="cityId">城市ID:</label>
        <input type="text" id="cityId" placeholder="例如：101010100">
        <button onclick="getWeatherById()">查询天气</button>
    </div>

    <h2>查询结果</h2>
    <div class="result" id="result">
        <p>点击按钮查询信息</p>
    </div>

    <script>
        // 获取用户当前位置
        function getCurrentLocation() {
            document.getElementById('result').innerHTML = '<p>正在获取位置信息...</p>';
            
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        document.getElementById('latitude').value = position.coords.latitude;
                        document.getElementById('longitude').value = position.coords.longitude;
                        document.getElementById('result').innerHTML = 
                            `<p class="success">获取位置成功!</p>
                            <p>纬度: ${position.coords.latitude}</p>
                            <p>经度: ${position.coords.longitude}</p>`;
                    },
                    function(error) {
                        document.getElementById('result').innerHTML = 
                            `<p class="error">获取位置失败: ${error.message}</p>`;
                    }
                );
            } else {
                document.getElementById('result').innerHTML = 
                    '<p class="error">您的浏览器不支持地理位置功能</p>';
            }
        }

        // 通过坐标查询城市
        function queryByCoordinates() {
            const latitude = document.getElementById('latitude').value.trim();
            const longitude = document.getElementById('longitude').value.trim();
            
            if (!latitude || !longitude) {
                document.getElementById('result').innerHTML = 
                    '<p class="error">请输入有效的经纬度</p>';
                return;
            }
            
            document.getElementById('result').innerHTML = '<p>正在查询城市信息...</p>';
            
            // 构建API URL
            const url = `get_city_by_location.php?latitude=${encodeURIComponent(latitude)}&longitude=${encodeURIComponent(longitude)}`;
            
            // 发送请求
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const city = data.city;
                        document.getElementById('result').innerHTML = 
                            `<p class="success">查询成功!</p>
                            <table>
                                <tr><th>字段</th><th>值</th></tr>
                                <tr><td>城市名称</td><td>${city.name}</td></tr>
                                <tr><td>完整名称</td><td>${city.fullName}</td></tr>
                                <tr><td>城市ID</td><td>${city.id}</td></tr>
                                <tr><td>经度</td><td>${city.lon}</td></tr>
                                <tr><td>纬度</td><td>${city.lat}</td></tr>
                                <tr><td>省份/行政区</td><td>${city.adm1}</td></tr>
                                <tr><td>国家</td><td>${city.country}</td></tr>
                                <tr><td>时区</td><td>${city.timezone}</td></tr>
                            </table>
                            <button onclick="getWeatherById('${city.id}')">获取天气</button>`;
                    } else {
                        document.getElementById('result').innerHTML = 
                            `<p class="error">查询失败: ${data.message}</p>`;
                    }
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = 
                        `<p class="error">请求出错: ${error}</p>`;
                });
        }

        // 通过城市名称查询
        function queryByCity() {
            const cityName = document.getElementById('cityName').value.trim();
            
            if (!cityName) {
                document.getElementById('result').innerHTML = 
                    '<p class="error">请输入城市名称</p>';
                return;
            }
            
            document.getElementById('result').innerHTML = '<p>正在查询城市信息...</p>';
            
            // 构建API URL
            const url = `get_city_by_location.php?city=${encodeURIComponent(cityName)}`;
            
            // 发送请求
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const city = data.city;
                        document.getElementById('result').innerHTML = 
                            `<p class="success">查询成功!</p>
                            <table>
                                <tr><th>字段</th><th>值</th></tr>
                                <tr><td>城市名称</td><td>${city.name}</td></tr>
                                <tr><td>完整名称</td><td>${city.fullName}</td></tr>
                                <tr><td>城市ID</td><td>${city.id}</td></tr>
                                <tr><td>经度</td><td>${city.lon}</td></tr>
                                <tr><td>纬度</td><td>${city.lat}</td></tr>
                                <tr><td>省份/行政区</td><td>${city.adm1}</td></tr>
                                <tr><td>国家</td><td>${city.country}</td></tr>
                                <tr><td>时区</td><td>${city.timezone}</td></tr>
                            </table>
                            <button onclick="getWeatherById('${city.id}')">获取天气</button>`;
                    } else {
                        document.getElementById('result').innerHTML = 
                            `<p class="error">查询失败: ${data.message}</p>`;
                    }
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = 
                        `<p class="error">请求出错: ${error}</p>`;
                });
        }

        // 通过ID获取天气
        function getWeatherById(cityId = null) {
            if (!cityId) {
                cityId = document.getElementById('cityId').value.trim();
            }
            
            if (!cityId) {
                document.getElementById('result').innerHTML = 
                    '<p class="error">请输入城市ID</p>';
                return;
            }
            
            document.getElementById('result').innerHTML = '<p>正在获取天气数据...</p>';
            
            // 构建API URL
            const url = `get_weather.php?cityid=${encodeURIComponent(cityId)}`;
            
            // 发送请求
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const weather = data.data;
                        document.getElementById('result').innerHTML = 
                            `<p class="success">天气查询成功!</p>
                            <table>
                                <tr><th>字段</th><th>值</th></tr>
                                <tr><td>城市</td><td>${weather.city}</td></tr>
                                <tr><td>城市ID</td><td>${weather.cityid}</td></tr>
                                <tr><td>温度</td><td>${weather.temp}°C</td></tr>
                                <tr><td>体感温度</td><td>${weather.feelsLike}°C</td></tr>
                                <tr><td>天气</td><td>${weather.text}</td></tr>
                                <tr><td>风向</td><td>${weather.windDir}</td></tr>
                                <tr><td>风力</td><td>${weather.windScale}级</td></tr>
                                <tr><td>湿度</td><td>${weather.humidity}%</td></tr>
                                <tr><td>更新时间</td><td>${weather.updateTime}</td></tr>
                            </table>
                            <p>天气API数据:</p>
                            <pre>${JSON.stringify(weather, null, 2)}</pre>`;
                    } else {
                        document.getElementById('result').innerHTML = 
                            `<p class="error">天气查询失败: ${data.message || '未知错误'}</p>`;
                    }
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = 
                        `<p class="error">请求出错: ${error}</p>`;
                });
        }

        // 直接通过坐标获取天气
        function getWeatherByCoordinates() {
            const latitude = document.getElementById('latitude').value.trim();
            const longitude = document.getElementById('longitude').value.trim();
            
            if (!latitude || !longitude) {
                document.getElementById('result').innerHTML = 
                    '<p class="error">请输入有效的经纬度</p>';
                return;
            }
            
            document.getElementById('result').innerHTML = '<p>正在获取天气数据...</p>';
            
            // 构建API URL
            const url = `get_weather.php?latitude=${encodeURIComponent(latitude)}&longitude=${encodeURIComponent(longitude)}`;
            
            // 发送请求
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const weather = data.data;
                        document.getElementById('result').innerHTML = 
                            `<p class="success">天气查询成功!</p>
                            <table>
                                <tr><th>字段</th><th>值</th></tr>
                                <tr><td>城市</td><td>${weather.city}</td></tr>
                                <tr><td>城市ID</td><td>${weather.cityid}</td></tr>
                                <tr><td>温度</td><td>${weather.temp}°C</td></tr>
                                <tr><td>体感温度</td><td>${weather.feelsLike}°C</td></tr>
                                <tr><td>天气</td><td>${weather.text}</td></tr>
                                <tr><td>风向</td><td>${weather.windDir}</td></tr>
                                <tr><td>风力</td><td>${weather.windScale}级</td></tr>
                                <tr><td>湿度</td><td>${weather.humidity}%</td></tr>
                                <tr><td>更新时间</td><td>${weather.updateTime}</td></tr>
                            </table>
                            <p>天气API数据:</p>
                            <pre>${JSON.stringify(weather, null, 2)}</pre>`;
                    } else {
                        document.getElementById('result').innerHTML = 
                            `<p class="error">天气查询失败: ${data.message || '未知错误'}</p>`;
                    }
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = 
                        `<p class="error">请求出错: ${error}</p>`;
                });
        }

        // 页面加载时自动获取位置
        window.onload = function() {
            const useLocation = confirm('是否允许获取您的当前位置？');
            if (useLocation) {
                getCurrentLocation();
            }
        };
    </script>
</body>
</html> 