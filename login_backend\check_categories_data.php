<?php
require_once 'config.php';
require_once 'db.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "检查 clothing_categories 表数据：\n\n";
    
    // 查询所有分类
    $stmt = $conn->prepare("SELECT * FROM clothing_categories ORDER BY is_system DESC, sort_order ASC");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "总分类数量: " . count($categories) . "\n\n";
    
    foreach ($categories as $cat) {
        echo "ID: {$cat['id']}\n";
        echo "名称: {$cat['name']}\n";
        echo "代码: {$cat['code']}\n";
        echo "用户ID: " . ($cat['user_id'] ?: '系统') . "\n";
        echo "是否系统分类: " . ($cat['is_system'] ? '是' : '否') . "\n";
        echo "排序: {$cat['sort_order']}\n";
        echo "创建时间: {$cat['created_at']}\n";
        echo "---\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
} 