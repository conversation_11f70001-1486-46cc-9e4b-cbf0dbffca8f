-- 为face_analysis表添加支付相关字段
-- 执行时间：2025-01-01

ALTER TABLE `face_analysis`
ADD COLUMN `payment_status` enum('unpaid','paid','refunded') NOT NULL DEFAULT 'unpaid' COMMENT '支付状态' AFTER `status`,
ADD COLUMN `order_id` varchar(64) DEFAULT NULL COMMENT '订单ID' AFTER `payment_status`,
ADD COLUMN `amount` decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '支付金额' AFTER `order_id`,
ADD COLUMN `usage_status` enum('unused','used','expired') NOT NULL DEFAULT 'unused' COMMENT '使用状态' AFTER `amount`;

-- 添加索引
ALTER TABLE `face_analysis`
ADD KEY `idx_payment_status` (`payment_status`),
ADD KEY `idx_order_id` (`order_id`),
ADD KEY `idx_usage_status` (`usage_status`);
