const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    hasHistory: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.checkHasHistory();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.checkHasHistory();
  },

  /**
   * 检查用户是否有历史分析记录
   */
  checkHasHistory: function () {
    if (!wx.getStorageSync('token')) {
      this.setData({
        hasHistory: false
      });
      return;
    }

    const token = wx.getStorageSync('token');
    
    wx.request({
      url: app.globalData.baseUrl + '/get_image_analysis_history.php',
      method: 'GET',
      header: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      success: (res) => {
        if (res.data && !res.data.error && res.data.data.total > 0) {
          this.setData({
            hasHistory: true
          });
        } else {
          this.setData({
            hasHistory: false
          });
        }
      },
      fail: () => {
        this.setData({
          hasHistory: false
        });
      }
    });
  },

  /**
   * 开始形象分析
   */
  startAnalysis: function () {
    // 获取app实例
    const app = getApp();
    
    // 检查是否登录 - 优化检测逻辑，检查全局isLoggedIn状态和是否为模拟用户
    if (!wx.getStorageSync('token') || app.globalData.useMockUser || !app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录以使用个人形象分析服务',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 使用reLaunch避免导航超时问题
            wx.reLaunch({
              url: '/pages/login/login',
              fail: (err) => {
                console.error('跳转到登录页面失败:', err);
                // 如果reLaunch失败，尝试用switchTab
                wx.switchTab({
                  url: '/pages/profile/profile',
                  fail: (switchErr) => {
                    console.error('跳转到个人页面也失败:', switchErr);
                    // 显示错误提示
                    wx.showToast({
                      title: '跳转失败，请手动前往"我的"页面登录',
                      icon: 'none',
                      duration: 2000
                    });
                  }
                });
              }
            });
          }
        }
      });
      return;
    }

    // 已登录，前往表单页面
    wx.navigateTo({
      url: '/pages/image_analysis/form/form'
    });
  },

  /**
   * 查看历史分析
   */
  goToHistory: function () {
    wx.navigateTo({
      url: '/pages/image_analysis/history/history'
    });
  }
}) 