<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <!-- 穿搭详情 -->
  <block wx:if="{{!loading && outfit}}">
    <!-- 穿搭画布 -->
    <view class="outfit-canvas">
      <!-- 如果有衣物，显示穿搭效果 -->
      <block wx:if="{{outfit.items.length > 0}}">
        <view class="outfit-view" id="outfit-view">
          <view
            wx:for="{{outfit.items}}"
            wx:key="clothing_id"
            class="outfit-item"
            style="left: {{item.position.x}}px; top: {{item.position.y}}px; width: {{item.size.width}}px; height: {{item.size.height}}px; transform: rotate({{item.rotation}}deg); z-index: {{item.z_index}};">
            <image src="{{item.clothing_data.image_url}}" mode="aspectFit" class="item-image"></image>
          </view>

          <!-- 下载按钮 -->
          <view class="download-btn" bindtap="downloadOutfitImage">
            <view class="download-icon"></view>
          </view>
        </view>
      </block>
      
      <!-- 如果没有衣物，显示提示 -->
      <view class="empty-canvas" wx:else>
        <text class="empty-icon">👔</text>
        <text class="empty-text">点击编辑按钮开始添加衣物</text>
      </view>
    </view>
    
    <!-- 穿搭信息 - 简化版 -->
    <view class="outfit-info" wx:if="{{outfit.items.length > 0}}">
      <!-- 衣物列表 -->
      <view class="outfit-items">
        <view class="items-header">
        <view class="items-title">包含的衣物 ({{outfit.items.length}})</view>
          <view class="public-switch-container" wx:if="{{isCreator}}">
            <text class="switch-label">公开</text>
            <switch checked="{{outfit.is_public}}" bindchange="togglePublicStatus" color="#333333" />
          </view>
        </view>
        <scroll-view scroll-x class="items-scroll" enhanced="true" show-scrollbar="false">
          <view class="items-container">
            <view
              wx:for="{{outfit.items}}"
              wx:key="clothing_id"
              class="item-preview"
              bindtap="viewClothingDetail"
              data-clothing-id="{{item.clothing_id}}"
              data-clothing-data="{{item.clothing_data}}"
              hover-class="item-preview-hover">
              <image src="{{item.clothing_data.image_url}}" mode="aspectFit" class="preview-image"></image>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </block>
  
  <!-- 底部操作按钮 -->
  <view class="bottom-actions" wx:if="{{!loading && outfit}}">
    <!-- 创建者显示编辑和删除按钮 -->
    <block wx:if="{{isCreator}}">
      <view class="action-btn edit-btn" bindtap="goToEdit">编辑穿搭</view>
      <view class="action-btn delete-btn" bindtap="deleteOutfit">删除</view>
    </block>
    
    <!-- 从穿搭广场进入显示用户信息和点赞按钮 -->
    <block wx:elif="{{fromSquare}}">
      <view class="creator-info" bindtap="viewUserOutfits">
        <image class="creator-avatar" src="{{outfit.creator_avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        <text class="creator-name">{{outfit.creator_nickname || '匿名用户'}}</text>
        <view class="view-more-icon">›</view>
      </view>
      <view class="action-container">
        <view class="like-btn {{isLiked ? 'liked' : ''}}" bindtap="toggleLike">
          <image src="{{isLiked ? '/images/dianzan_active.png' : '/images/dianzan.png'}}" class="like-btn-icon"></image>
          <text class="like-btn-count">{{outfit.likes_count || 0}}</text>
        </view>
      </view>
    </block>
    
    <!-- 非创建者且非广场显示"我也要穿搭"按钮 -->
    <block wx:else>
      <view class="action-btn want-btn" bindtap="goToLogin">我也要穿搭</view>
    </block>
  </view>

  <!-- 隐藏的canvas用于图片处理 -->
  <canvas
    id="outfitCanvas"
    type="2d"
    class="hidden-canvas"
    style="width: {{canvasDisplayWidth}}px; height: {{canvasDisplayHeight}}px;">
  </canvas>
</view>