<?php
// 获取圈子共享穿搭API
// 模块4：数据共享基础模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';
require_once 'Logger.php';

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

// 获取查询参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$perPage = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
$offset = ($page - 1) * $perPage;
$category = isset($_GET['category']) ? $_GET['category'] : null;
// 处理前端传递的"null"或"undefined"字符串
if ($category === 'null' || $category === 'undefined' || $category === '') {
    $category = null;
}

// 记录API请求
Logger::apiRequest('/get_circle_outfits.php', 'GET', [
    'user_id' => $userId,
    'page' => $page,
    'per_page' => $perPage,
    'category' => $category
], null, 'circle_outfits');

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查找用户所在的圈子
    $findCircleSql = "SELECT cm.circle_id, cm.role, c.name as circle_name
                      FROM circle_members cm 
                      JOIN outfit_circles c ON cm.circle_id = c.id 
                      WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
    $findCircleStmt = $conn->prepare($findCircleSql);
    $findCircleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $findCircleStmt->execute();
    
    $userCircle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$userCircle) {
        echo json_encode([
            'status' => 'error',
            'message' => '您当前未加入任何圈子'
        ]);
        exit;
    }
    
    $circleId = $userCircle['circle_id'];

    // 记录基本信息
    Logger::debug('穿搭API - 圈子和用户基本信息', [
        'user_id' => $userId,
        'circle_id' => $circleId,
        'user_role' => $userCircle['role']
    ], 'circle_outfits');

    // 检查用户穿搭总数和分布
    $totalOutfitsSQL = "SELECT
        COUNT(*) as total,
        SUM(CASE WHEN circle_id IS NULL THEN 1 ELSE 0 END) as personal,
        SUM(CASE WHEN circle_id IS NOT NULL THEN 1 ELSE 0 END) as shared,
        SUM(CASE WHEN circle_id = :circle_id THEN 1 ELSE 0 END) as in_current_circle
        FROM outfits WHERE user_id = :user_id";
    $totalOutfitsStmt = $conn->prepare($totalOutfitsSQL);
    $totalOutfitsStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $totalOutfitsStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $totalOutfitsStmt->execute();
    $outfitDistribution = $totalOutfitsStmt->fetch(PDO::FETCH_ASSOC);

    Logger::debug('用户穿搭分布', [
        'user_id' => $userId,
        'circle_id' => $circleId,
        'distribution' => $outfitDistribution
    ], 'circle_outfits');

    // 构建查询条件
    $whereConditions = [];
    $params = ['circle_id' => $circleId];
    
    // 基础条件：圈子共享数据或圈子成员的个人数据
    $whereConditions[] = "(o.circle_id = :circle_id OR 
                          (o.user_id IN (SELECT user_id FROM circle_members 
                                        WHERE circle_id = :circle_id AND status = 'active') 
                           AND o.circle_id IS NULL))";
    
    // 分类过滤
    if ($category && $category !== 'all') {
        $whereConditions[] = "o.category = :category";
        $params['category'] = $category;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // 获取圈子中的所有穿搭
    $outfitsSql = "SELECT o.id, o.name, o.category, o.category_id, o.thumbnail_url,
                          o.description, o.created_at, o.updated_at, o.user_id, o.circle_id,
                          o.outfit_data, o.is_public, o.likes_count,
                          u.nickname as creator_nickname
                   FROM outfits o
                   JOIN users u ON o.user_id = u.id
                   WHERE $whereClause
                   ORDER BY o.created_at DESC
                   LIMIT :offset, :per_page";
    
    // 记录主查询信息
    Logger::sql('主穿搭查询', $outfitsSql, [
        'params' => $params,
        'offset' => $offset,
        'per_page' => $perPage,
        'where_clause' => $whereClause
    ], 'circle_outfits');

    $outfitsStmt = $conn->prepare($outfitsSql);
    foreach ($params as $key => $value) {
        $outfitsStmt->bindValue(":$key", $value);
    }
    $outfitsStmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $outfitsStmt->bindParam(':per_page', $perPage, PDO::PARAM_INT);
    $outfitsStmt->execute();

    // 记录查询执行结果
    $rowCount = $outfitsStmt->rowCount();
    Logger::debug('穿搭主查询执行结果', [
        'row_count' => $rowCount,
        'expected_more_than_zero' => $rowCount > 0
    ], 'circle_outfits');
    
    $outfits = [];
    while ($outfit = $outfitsStmt->fetch(PDO::FETCH_ASSOC)) {
        // 从outfit_data JSON中解析衣物信息
        $outfitClothes = [];
        $outfitData_json = json_decode($outfit['outfit_data'], true);

        // 记录穿搭数据解析
        Logger::debug('解析穿搭数据', [
            'outfit_id' => $outfit['id'],
            'outfit_name' => $outfit['name'],
            'outfit_data_length' => strlen($outfit['outfit_data']),
            'parsed_successfully' => $outfitData_json !== null
        ], 'circle_outfits');

        if ($outfitData_json && isset($outfitData_json['items']) && is_array($outfitData_json['items'])) {
            // 从items数组中提取衣物信息
            foreach ($outfitData_json['items'] as $item) {
                if (isset($item['clothing_id']) && isset($item['clothing_data'])) {
                    $clothingData = $item['clothing_data'];
                    $outfitClothes[] = [
                        'clothes_id' => $item['clothing_id'],
                        'clothes_name' => $clothingData['name'] ?? '未知衣物',
                        'clothes_image' => $clothingData['image_url'] ?? '',
                        'category' => $clothingData['category'] ?? '',
                        'position' => $item['position'] ?? null,
                        'size' => $item['size'] ?? null,
                        'rotation' => $item['rotation'] ?? 0,
                        'z_index' => $item['z_index'] ?? 0
                    ];
                }
            }

            Logger::debug('解析穿搭衣物成功', [
                'outfit_id' => $outfit['id'],
                'items_count' => count($outfitData_json['items']),
                'clothes_count' => count($outfitClothes)
            ], 'circle_outfits');
        } else {
            Logger::debug('穿搭数据结构异常', [
                'outfit_id' => $outfit['id'],
                'has_outfit_data' => $outfitData_json !== null,
                'outfit_data_keys' => $outfitData_json ? array_keys($outfitData_json) : [],
                'has_items' => isset($outfitData_json['items']),
                'items_type' => isset($outfitData_json['items']) ? gettype($outfitData_json['items']) : 'not_set'
            ], 'circle_outfits');
        }
        
        $outfitData = [
            'id' => $outfit['id'],
            'name' => $outfit['name'],
            'category' => $outfit['category'],
            'category_id' => $outfit['category_id'],
            'thumbnail_url' => $outfit['thumbnail_url'],
            'description' => $outfit['description'],
            'created_at' => $outfit['created_at'],
            'updated_at' => $outfit['updated_at'],
            'user_id' => $outfit['user_id'],
            'circle_id' => $outfit['circle_id'],
            'is_public' => intval($outfit['is_public']),
            'likes_count' => intval($outfit['likes_count']),
            'creator_nickname' => $outfit['creator_nickname'],
            'clothes' => $outfitClothes,
            'clothes_count' => count($outfitClothes),
            'outfit_data' => $outfitData_json,
            'is_shared' => !is_null($outfit['circle_id']),
            'is_own' => $outfit['user_id'] == $userId,
            'data_source' => is_null($outfit['circle_id']) ? 'personal' : 'shared'
        ];
        
        $outfits[] = $outfitData;
    }
    
    // 获取总数量
    $countSql = "SELECT COUNT(*) as total
                 FROM outfits o
                 WHERE $whereClause";
    $countStmt = $conn->prepare($countSql);
    foreach ($params as $key => $value) {
        $countStmt->bindValue(":$key", $value);
    }
    $countStmt->execute();
    
    $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    $totalPages = ceil($totalCount / $perPage);
    
    // 获取分类统计
    $categorySql = "SELECT o.category, COUNT(*) as count
                    FROM outfits o
                    WHERE (o.circle_id = :circle_id OR 
                           (o.user_id IN (SELECT user_id FROM circle_members 
                                         WHERE circle_id = :circle_id AND status = 'active') 
                            AND o.circle_id IS NULL))
                    GROUP BY o.category
                    ORDER BY count DESC";
    $categoryStmt = $conn->prepare($categorySql);
    $categoryStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $categoryStmt->execute();
    
    $categoryStats = [];
    while ($categoryStat = $categoryStmt->fetch(PDO::FETCH_ASSOC)) {
        $categoryStats[] = [
            'category' => $categoryStat['category'],
            'count' => intval($categoryStat['count'])
        ];
    }
    
    $response = [
        'status' => 'success',
        'data' => [
            'circle_info' => [
                'circle_id' => $circleId,
                'circle_name' => $userCircle['circle_name'],
                'user_role' => $userCircle['role']
            ],
            'outfits' => $outfits,
            'pagination' => [
                'total' => intval($totalCount),
                'page' => $page,
                'per_page' => $perPage,
                'total_pages' => $totalPages
            ],
            'category_stats' => $categoryStats,
            'filters' => [
                'category' => $category
            ]
        ]
    ];

    // 记录API响应
    Logger::apiRequest('/get_circle_outfits.php', 'GET', [
        'user_id' => $userId,
        'circle_id' => $circleId
    ], [
        'outfits_count' => count($outfits),
        'category_stats_count' => count($categoryStats),
        'total_count' => $totalCount
    ], 'circle_outfits');

    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '获取圈子穿搭失败：' . $e->getMessage()
    ]);
}
?>
