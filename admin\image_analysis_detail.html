<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>形象分析详情 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* 详情卡片样式 */
        .detail-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
            color: #333;
        }
        
        .info-row {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .info-item {
            margin-right: 30px;
            margin-bottom: 15px;
            min-width: 150px;
        }
        
        .info-label {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #333;
            font-size: 16px;
        }
        
        /* 标签状态样式 */
        .status-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        
        .status-processing {
            background-color: #fcf4e6;
            color: #fa8c16;
        }
        
        .status-completed {
            background-color: #f6ffed;
            color: #52c41a;
        }
        
        .status-failed {
            background-color: #fff2f0;
            color: #f5222d;
        }
        
        .payment-unpaid {
            background-color: #fff7e6;
            color: #fa8c16;
        }
        
        .payment-paid {
            background-color: #f6ffed;
            color: #52c41a;
        }
        
        .payment-refunded {
            background-color: #f9f0ff;
            color: #722ed1;
        }
        
        /* 照片展示区 */
        .photos-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .photo-item {
            position: relative;
            width: 150px;
            height: 200px;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            cursor: pointer;
        }
        
        .photo-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .empty-photos {
            padding: 30px 0;
            text-align: center;
            background-color: #f9f9f9;
            border-radius: 4px;
            color: #999;
            width: 100%;
        }
        
        /* 用户信息区域 */
        .user-info-box {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 15px;
        }
        
        .user-detail {
            flex: 1;
        }
        
        .user-name {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .user-id {
            color: #666;
            font-size: 14px;
        }
        
        /* 按钮样式 */
        .action-btn {
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        
        .back-btn {
            background-color: white;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .back-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        /* 加载状态 */
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 50px 0;
        }
        
        .loading-spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left: 4px solid #1890ff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 分析结果样式 */
        .analysis-result {
            background-color: #f9f9f9;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .result-section {
            margin-bottom: 20px;
        }
        
        .result-title {
            font-weight: 500;
            margin-bottom: 10px;
            color: #1890ff;
        }
        
        .result-content {
            white-space: pre-line;
            color: #333;
            line-height: 1.6;
        }
        
        .json-code {
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-word;
            max-height: 400px;
            overflow-y: auto;
        }
        
        /* 响应式布局 */
        @media (max-width: 768px) {
            .info-row {
                flex-direction: column;
            }
            
            .info-item {
                width: 100%;
                margin-right: 0;
            }
            
            .photo-item {
                width: calc(50% - 10px);
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>形象分析详情</h2>
                <div class="user-info">
                    <span id="userName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="actions">
                <button id="backBtn" class="action-btn back-btn">返回列表</button>
            </div>
            
            <div id="loadingContainer" class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载数据...</p>
            </div>
            
            <div id="detailContainer" style="display: none;">
                <!-- 用户信息 -->
                <div class="detail-container">
                    <div class="section-title">用户信息</div>
                    <div class="user-info-box">
                        <img id="userAvatar" class="user-avatar" src="/images/default-avatar.png" alt="用户头像">
                        <div class="user-detail">
                            <div class="user-name" id="userNickname">加载中...</div>
                            <div class="user-id">ID: <span id="userId">-</span></div>
                        </div>
                    </div>
                </div>
                
                <!-- 分析记录基本信息 -->
                <div class="detail-container">
                    <div class="section-title">基本信息</div>
                    <div class="info-row">
                        <div class="info-item">
                            <div class="info-label">分析ID</div>
                            <div class="info-value" id="analysisId">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">订单ID</div>
                            <div class="info-value" id="orderId">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">状态</div>
                            <div class="info-value" id="status">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">支付状态</div>
                            <div class="info-value" id="paymentStatus">-</div>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <div class="info-label">金额</div>
                            <div class="info-value" id="amount">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">创建时间</div>
                            <div class="info-value" id="createdAt">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">分析完成时间</div>
                            <div class="info-value" id="analysisTime">-</div>
                        </div>
                    </div>
                </div>
                
                <!-- 个人形象数据 -->
                <div class="detail-container">
                    <div class="section-title">个人形象数据</div>
                    <div class="info-row">
                        <div class="info-item">
                            <div class="info-label">性别</div>
                            <div class="info-value" id="gender">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">身高</div>
                            <div class="info-value" id="height">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">体重</div>
                            <div class="info-value" id="weight">-</div>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <div class="info-label">胸围</div>
                            <div class="info-value" id="bust">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">腰围</div>
                            <div class="info-value" id="waist">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">臀围</div>
                            <div class="info-value" id="hips">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">肩宽</div>
                            <div class="info-value" id="shoulderWidth">-</div>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <div class="info-label">肤色</div>
                            <div class="info-value" id="skinTone">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">脸型</div>
                            <div class="info-value" id="faceShape">-</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">体型</div>
                            <div class="info-value" id="bodyShape">-</div>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item" style="width: 100%;">
                            <div class="info-label">用户备注</div>
                            <div class="info-value" id="remarks">-</div>
                        </div>
                    </div>
                </div>
                
                <!-- 照片 -->
                <div class="detail-container">
                    <div class="section-title">用户照片</div>
                    <div class="photos-container" id="photosContainer">
                        <div class="empty-photos">暂无照片</div>
                    </div>
                </div>
                
                <!-- 分析结果 -->
                <div class="detail-container">
                    <div class="section-title">分析结果</div>
                    <div id="analysisResultContainer">
                        <div id="resultPending" style="display: none;">
                            <p>分析尚未完成，暂无结果。</p>
                        </div>
                        <div id="resultCompleted" style="display: none;">
                            <!-- 分析结果将通过JavaScript动态填充 -->
                        </div>
                        <div id="resultRaw" style="display: none;">
                            <div class="section-title">原始JSON数据</div>
                            <pre class="json-code" id="jsonData"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/image_viewer.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为image_analysis
            Sidebar.init('image_analysis');
            
            // 获取管理员信息
            const user = Auth.getCurrentUser();
            if (user) {
                document.getElementById('userName').textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 返回按钮
            document.getElementById('backBtn').addEventListener('click', function() {
                window.location.href = 'image_analysis_list.html';
            });
            
            // 获取URL参数中的ID
            const urlParams = new URLSearchParams(window.location.search);
            const analysisId = urlParams.get('id');
            
            if (!analysisId) {
                alert('缺少分析ID参数');
                window.location.href = 'image_analysis_list.html';
                return;
            }
            
            // 加载形象分析详情
            loadAnalysisDetail(analysisId);
            
            // 加载形象分析详情
            function loadAnalysisDetail(id) {
                // 显示加载中
                document.getElementById('loadingContainer').style.display = 'flex';
                document.getElementById('detailContainer').style.display = 'none';
                
                // 构建请求URL
                const url = new URL('../login_backend/admin_get_image_analysis_detail.php', window.location.origin);
                url.searchParams.append('id', id);
                
                // 发起请求
                fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${Auth.getToken()}`
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常');
                    }
                    return response.json();
                })
                .then(data => {
                    // 隐藏加载中
                    document.getElementById('loadingContainer').style.display = 'none';
                    
                    if (data.error) {
                        alert(data.msg || '加载形象分析详情失败');
                        return;
                    }
                    
                    // 显示详情容器
                    document.getElementById('detailContainer').style.display = 'block';
                    
                    // 渲染数据
                    renderAnalysisDetail(data.data);
                })
                .catch(error => {
                    console.error('获取形象分析详情失败:', error);
                    document.getElementById('loadingContainer').style.display = 'none';
                    alert('获取数据失败，请检查网络连接或刷新页面重试');
                });
            }
            
            // 渲染形象分析详情
            function renderAnalysisDetail(analysis) {
                // 用户信息
                document.getElementById('userId').textContent = analysis.user_id || '-';
                document.getElementById('userNickname').textContent = analysis.nickname || '未知用户';
                if (analysis.avatar_url) {
                    document.getElementById('userAvatar').src = analysis.avatar_url;
                }
                
                // 基本信息
                document.getElementById('analysisId').textContent = analysis.id || '-';
                document.getElementById('orderId').textContent = analysis.order_id || '-';
                
                // 状态
                const statusElem = document.getElementById('status');
                statusElem.innerHTML = '';
                const statusSpan = document.createElement('span');
                statusSpan.className = `status-tag status-${analysis.status}`;
                statusSpan.textContent = getStatusText(analysis.status);
                statusElem.appendChild(statusSpan);
                
                // 支付状态
                const paymentStatusElem = document.getElementById('paymentStatus');
                paymentStatusElem.innerHTML = '';
                const paymentSpan = document.createElement('span');
                paymentSpan.className = `status-tag payment-${analysis.payment_status}`;
                paymentSpan.textContent = getPaymentStatusText(analysis.payment_status);
                paymentStatusElem.appendChild(paymentSpan);
                
                // 金额
                document.getElementById('amount').textContent = `¥${parseFloat(analysis.amount).toFixed(2)}`;
                
                // 时间
                document.getElementById('createdAt').textContent = formatDateTime(analysis.created_at);
                document.getElementById('analysisTime').textContent = analysis.analysis_time ? formatDateTime(analysis.analysis_time) : '-';
                
                // 个人形象数据
                document.getElementById('gender').textContent = analysis.gender === 1 ? '男' : (analysis.gender === 2 ? '女' : '未知');
                document.getElementById('height').textContent = analysis.height ? `${analysis.height}cm` : '-';
                document.getElementById('weight').textContent = analysis.weight ? `${analysis.weight}kg` : '-';
                document.getElementById('bust').textContent = analysis.bust ? `${analysis.bust}cm` : '-';
                document.getElementById('waist').textContent = analysis.waist ? `${analysis.waist}cm` : '-';
                document.getElementById('hips').textContent = analysis.hips ? `${analysis.hips}cm` : '-';
                document.getElementById('shoulderWidth').textContent = analysis.shoulder_width ? `${analysis.shoulder_width}cm` : '-';
                document.getElementById('skinTone').textContent = analysis.skin_tone || '-';
                document.getElementById('faceShape').textContent = analysis.face_shape || '-';
                document.getElementById('bodyShape').textContent = analysis.body_shape || '-';
                document.getElementById('remarks').textContent = analysis.remarks || '无';
                
                // 照片
                renderPhotos(analysis.photo_urls);
                
                // 分析结果
                renderAnalysisResult(analysis);
            }
            
            // 渲染照片
            function renderPhotos(photoUrls) {
                const photosContainer = document.getElementById('photosContainer');
                photosContainer.innerHTML = '';
                
                if (!photoUrls || photoUrls.length === 0) {
                    const emptyElem = document.createElement('div');
                    emptyElem.className = 'empty-photos';
                    emptyElem.textContent = '暂无照片';
                    photosContainer.appendChild(emptyElem);
                    return;
                }
                
                photoUrls.forEach((url, index) => {
                    const photoItem = document.createElement('div');
                    photoItem.className = 'photo-item';
                    photoItem.innerHTML = `<img src="${url}" alt="用户照片${index + 1}">`;
                    photoItem.addEventListener('click', () => {
                        ImageViewer.showImage(url);
                    });
                    photosContainer.appendChild(photoItem);
                });
            }
            
            // 渲染分析结果
            function renderAnalysisResult(analysis) {
                const resultPending = document.getElementById('resultPending');
                const resultCompleted = document.getElementById('resultCompleted');
                const resultRaw = document.getElementById('resultRaw');
                
                // 根据状态显示不同内容
                if (analysis.status !== 'completed' || !analysis.analysis_result) {
                    resultPending.style.display = 'block';
                    resultCompleted.style.display = 'none';
                    resultRaw.style.display = 'none';
                    return;
                }
                
                // 显示已完成的分析结果
                resultPending.style.display = 'none';
                resultCompleted.style.display = 'block';
                resultRaw.style.display = 'block';
                
                // 格式化显示分析结果
                const result = analysis.analysis_result;
                const resultContainer = document.getElementById('resultCompleted');
                resultContainer.innerHTML = '';
                
                // 尝试以JSON格式美化显示
                try {
                    if (typeof result === 'object') {
                        // 是否有风格分析
                        if (result.styleAnalysis) {
                            addResultSection(resultContainer, '风格分析', result.styleAnalysis);
                        }
                        
                        // 是否有体型分析
                        if (result.bodyAnalysis) {
                            addResultSection(resultContainer, '体型分析', result.bodyAnalysis);
                        }
                        
                        // 是否有颜色分析
                        if (result.colorAnalysis) {
                            addResultSection(resultContainer, '颜色分析', result.colorAnalysis);
                        }
                        
                        // 是否有穿衣建议
                        if (result.clothingRecommendations) {
                            addResultSection(resultContainer, '穿衣建议', result.clothingRecommendations);
                        }
                        
                        // 是否有总结
                        if (result.summary) {
                            addResultSection(resultContainer, '总结', result.summary);
                        }
                        
                        // 如果没有具体的分类字段，则直接显示整个结果
                        if (!result.styleAnalysis && !result.bodyAnalysis && 
                            !result.colorAnalysis && !result.clothingRecommendations && 
                            !result.summary) {
                            
                            // 遍历对象中的字段
                            Object.keys(result).forEach(key => {
                                if (result[key] && typeof result[key] === 'string') {
                                    addResultSection(resultContainer, formatFieldName(key), result[key]);
                                } else if (result[key] && typeof result[key] === 'object') {
                                    addResultSection(resultContainer, formatFieldName(key), JSON.stringify(result[key], null, 2));
                                }
                            });
                        }
                    } else {
                        // 如果不是对象，直接显示
                        addResultSection(resultContainer, '分析结果', result.toString());
                    }
                    
                    // 原始JSON数据
                    document.getElementById('jsonData').textContent = JSON.stringify(result, null, 2);
                } catch (error) {
                    console.error('渲染分析结果失败:', error);
                    resultContainer.innerHTML = '<p>无法解析分析结果数据</p>';
                    document.getElementById('jsonData').textContent = '无法解析原始JSON数据';
                }
            }
            
            // 添加结果部分
            function addResultSection(container, title, content) {
                const section = document.createElement('div');
                section.className = 'result-section';
                
                const titleElem = document.createElement('div');
                titleElem.className = 'result-title';
                titleElem.textContent = title;
                
                const contentElem = document.createElement('div');
                contentElem.className = 'result-content';
                contentElem.textContent = content;
                
                section.appendChild(titleElem);
                section.appendChild(contentElem);
                container.appendChild(section);
            }
            
            // 格式化字段名称
            function formatFieldName(field) {
                // 将驼峰命名转换为空格分隔的词组并首字母大写
                return field
                    .replace(/([A-Z])/g, ' $1') // 在大写字母前添加空格
                    .replace(/^./, str => str.toUpperCase()); // 首字母大写
            }
            
            // 获取状态文本
            function getStatusText(status) {
                const statusMap = {
                    'pending': '待分析',
                    'processing': '分析中',
                    'completed': '已完成',
                    'failed': '失败'
                };
                return statusMap[status] || status;
            }
            
            // 获取支付状态文本
            function getPaymentStatusText(status) {
                const statusMap = {
                    'unpaid': '未支付',
                    'paid': '已支付',
                    'refunded': '已退款'
                };
                return statusMap[status] || status;
            }
            
            // 格式化日期时间
            function formatDateTime(dateTimeStr) {
                if (!dateTimeStr) return '-';
                
                try {
                    const date = new Date(dateTimeStr);
                    return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`;
                } catch (e) {
                    return dateTimeStr;
                }
            }
            
            // 数字补零
            function padZero(num) {
                return num < 10 ? '0' + num : num;
            }
        });
    </script>
</body>
</html> 