<view class="container">
  <view class="header">
    <view class="back-button" bindtap="goBack">
      <image class="back-icon" src="/images/back.png"></image>
    </view>
    <view class="title-container">
      <view class="tag-title"># {{tag}}</view>
      <view class="subtitle">包含 {{clothes.length}} 件衣物</view>
    </view>
  </view>
  
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <image class="loading-icon" src="/images/loading.gif"></image>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
  
  <!-- 加载失败 -->
  <view class="error-container" wx:elif="{{loadFailed}}">
    <icon type="warn" size="60" color="#FF4D4F"></icon>
    <text class="error-text">加载失败，请重试</text>
    <button class="retry-button" bindtap="loadClothesWithTag" data-tag="{{tag}}">重新加载</button>
  </view>
  
  <!-- 衣物列表 -->
  <view class="clothes-container" wx:else>
    <block wx:if="{{clothes.length > 0}}">
      <view class="clothes-list">
        <view class="clothing-item" 
              wx:for="{{clothes}}" 
              wx:key="id" 
              bindtap="onClothingTap" 
              data-id="{{item.id}}">
          <image class="clothing-image" src="{{item.image_url}}" mode="aspectFill"></image>
          <view class="clothing-info">
            <view class="clothing-name">{{item.name || '未命名衣物'}}</view>
            <view class="clothing-category">{{item.displayCategory}}</view>
            <view class="clothing-tags">
              <text class="clothing-tag" wx:for="{{item.tags.split(',')}}" wx:key="*this" wx:for-item="tagItem">{{tagItem}}</text>
            </view>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 没有衣物的提示 -->
    <view class="empty-container" wx:else>
      <image class="empty-icon" src="/images/empty-box.png"></image>
      <text class="empty-text">该标签下暂无衣物</text>
    </view>
  </view>
  
  <!-- 底部按钮 -->
  <view class="bottom-button-container">
    <button class="update-button" bindtap="goToUpdateTags">更新衣物标签</button>
  </view>
</view> 