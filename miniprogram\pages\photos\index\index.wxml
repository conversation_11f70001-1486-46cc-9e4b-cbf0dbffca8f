<view class="container">
  <view class="photos-grid" wx:if="{{!isLoading && photos.length > 0}}">
    <!-- 左列 -->
    <view class="column">
      <view class="photo-item {{selectMode && selectedPhotoId === item.id ? 'selected' : ''}}" wx:for="{{leftItems}}" wx:key="id" wx:if="{{!item.isAddButton}}" bindtap="showPhotoModal" data-id="{{item.id}}" data-url="{{item.url}}" data-type="{{item.type}}">
        <image class="photo-image" src="{{item.url}}" mode="widthFix" lazy-load="true"></image>
        <view class="select-mark" wx:if="{{selectMode && selectedPhotoId === item.id}}">✓</view>
      </view>
    </view>
    
    <!-- 右列 -->
    <view class="column">
      <view class="photo-item {{selectMode && selectedPhotoId === item.id ? 'selected' : ''}}" wx:for="{{rightItems}}" wx:key="id" wx:if="{{!item.isAddButton}}" bindtap="showPhotoModal" data-id="{{item.id}}" data-url="{{item.url}}" data-type="{{item.type}}">
        <image class="photo-image" src="{{item.url}}" mode="widthFix" lazy-load="true"></image>
        <view class="select-mark" wx:if="{{selectMode && selectedPhotoId === item.id}}">✓</view>
      </view>
    </view>
  </view>
  
  <!-- 底部试穿按钮 -->
  <view class="try-on-button-container" wx:if="{{selectMode}}">
    <view class="try-on-button add-photo" bindtap="onAddPhoto">
      添加照片
    </view>
    <view class="try-on-button {{selectedPhotoId ? 'active' : 'disabled'}}" bindtap="startTryOn">
      开始试穿
    </view>
  </view>
  
  <!-- 添加照片悬浮按钮 - 仅在非选择模式下显示 -->
  <view class="add-photo-button" bindtap="onAddPhoto" wx:if="{{!selectMode}}">
    <text>添加照片</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!isLoading && photos.length === 0}}">
    <text>还没有照片，点击底部按钮添加</text>
  </view>
  
  <!-- 加载中 -->
  <view class="loading" wx:if="{{isLoading}}">
    <view class="loading-icon"></view>
  </view>
  
  <!-- 照片详情弹框 -->
  <view class="photo-modal" wx:if="{{showModal}}" bindtap="hidePhotoModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-photo-container">
        <image src="{{currentPhoto.url}}" mode="widthFix" class="modal-photo-image" lazy-load="true" show-menu-by-longpress="true" bindload="onImageLoad"></image>
        <view class="photo-type" wx:if="{{currentPhoto.type === 'full'}}">全身照</view>
        <view class="photo-type half" wx:elif="{{currentPhoto.type === 'half'}}">半身照</view>
      </view>
      <!-- 删除按钮 -->
      <view class="modal-delete-button" bindtap="deleteCurrentPhoto">删除</view>
    </view>
  </view>
</view>
