<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>次元衣帽间 - AI智能虚拟试衣小程序 | 打造您的数字化穿搭助手与时尚管理平台</title>
    <meta name="description" content="次元衣帽间是一款创新的微信小程序，融合AI虚拟试衣技术与个人衣橱管理系统。轻松上传照片，即刻体验拟真试衣，解决线下试穿困扰；支持无限衣橱管理、智能服饰图处理、自定义穿搭创建、云端数据同步。为时尚爱好者、购物达人、忙碌白领提供一站式穿搭解决方案，打造属于您的专属数字时尚平台，让每一次穿搭都成为享受。">
    <meta name="keywords" content="虚拟试衣,AI试衣,次元衣帽间,微信小程序,数字衣橱,穿搭搭配,虚拟试穿,AI穿搭,时尚科技,无限衣橱管理,智能服饰图,自定义穿搭,云端数据同步,一键试穿,零成本试衣,实时预览,潮流穿搭,搭配灵感,个人形象管理,穿搭指南,高清渲染,拟真效果,智能识别,解决选择困难,节省试衣时间,穿搭分享,时尚社区,数字化衣橱,服饰管理系统,AI换装,时尚达人,购物爱好者,形象顾问,衣物整理">
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="images/logo.png" type="image/png">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5B6EF9',
                        secondary: '#F664B8',
                        dark: '#333333',
                        light: '#F9FAFC'
                    }
                }
            }
        }
    </script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom styles -->
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        .hero-gradient {
            background: linear-gradient(135deg, #5B6EF9 0%, #F664B8 100%);
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .qrcode-popup {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            visibility: hidden;
            opacity: 0;
            transition: all 0.3s ease;
            width: 150px;
            background: white;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
            text-align: center;
            margin-bottom: 10px;
            z-index: 10;
        }
        .qrcode-popup::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border-width: 8px;
            border-style: solid;
            border-color: white transparent transparent transparent;
        }
        .social-icon-container {
            position: relative;
        }
        .social-icon-container:hover .qrcode-popup {
            visibility: visible;
            opacity: 1;
        }
        .carousel-container {
            position: relative;
            overflow: hidden;
            padding: 0;
            height: 620px; /* 增加高度以放大轮播图 */
        }
        .carousel-slides {
            display: flex;
            transition: transform 0.5s ease-in-out;
            height: 100%; /* 使幻灯片容器占满高度 */
        }
        .carousel-slide {
            min-width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%; /* 使每个幻灯片占满高度 */
        }
        .carousel-slide img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain; /* 改回contain以显示完整图片 */
            border-radius: 8px;
            transform: scale(1.05); /* 稍微放大图片 */
        }
        .carousel-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 15;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            color: white;
        }
        .carousel-arrow:hover {
            background-color: rgba(91, 110, 249, 0.7); /* #5B6EF9 with opacity */
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
        }
        .carousel-arrow-left {
            left: 15px;
        }
        .carousel-arrow-right {
            right: 15px;
        }
        .carousel-indicators {
            position: absolute;
            bottom: 0px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;
            gap: 8px;
            z-index: 10;
        }
        .carousel-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.5);
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .carousel-indicator.active {
            background-color: white;
        }
        .logo-container {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 5px;
        }
        .step-image-container {
            position: relative;
            cursor: pointer;
        }
        .global-image-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            padding: 30px;
        }
        .global-image-popup.active {
            opacity: 1;
            visibility: visible;
        }
        .global-image-popup img {
            max-width: 90%;
            max-height: 90vh;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        .global-image-close {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: white;
            font-size: 24px;
            transition: background-color 0.2s ease;
        }
        .global-image-close:hover {
            background-color: rgba(255, 255, 255, 0.4);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Header/Navigation -->
    <header class="sticky top-0 bg-white shadow-sm z-50">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <a href="#" class="flex items-center space-x-2">
                <div class="logo-container">
                    <img src="images/logo.png" alt="次元衣帽间" class="h-10 w-auto">
                </div>
                <span class="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">次元衣帽间</span>
            </a>
            <nav class="hidden md:flex space-x-8">
                <a href="#features" class="text-dark hover:text-primary font-medium transition-colors">功能特点</a>
                <a href="#how-it-works" class="text-dark hover:text-primary font-medium transition-colors">使用流程</a>
                <a href="#about" class="text-dark hover:text-primary font-medium transition-colors">关于我们</a>
            </nav>
            <div class="md:hidden">
                <button id="mobile-menu-button" class="text-dark p-2">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-md">
            <div class="container mx-auto px-4 py-2 flex flex-col space-y-3">
                <a href="#features" class="text-dark hover:text-primary font-medium transition-colors py-2">功能特点</a>
                <a href="#how-it-works" class="text-dark hover:text-primary font-medium transition-colors py-2">使用流程</a>
                <a href="#about" class="text-dark hover:text-primary font-medium transition-colors py-2">关于我们</a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-gradient text-white py-20 md:py-28">
        <div class="container mx-auto px-4 flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 mb-10 md:mb-0">
                <h1 class="text-4xl md:text-5xl font-bold leading-tight mb-6">智能虚拟试衣，数字化您的衣橱</h1>
                <p class="text-lg mb-8 opacity-90">次元衣帽间让您在微信里轻松体验AI试衣，管理穿搭，创造独特时尚风格。</p>
                <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                    <a href="#miniprogram" class="bg-white text-primary hover:bg-opacity-90 px-8 py-3 rounded-full font-medium inline-flex items-center justify-center transition-all">
                        <i class="fab fa-weixin mr-2"></i> 立即体验小程序
                    </a>
                    <a href="#features" class="border border-white hover:bg-white hover:text-primary px-8 py-3 rounded-full font-medium inline-flex items-center justify-center transition-all">
                        <i class="fas fa-info-circle mr-2"></i> 了解更多
                    </a>
                </div>
            </div>
            <div class="md:w-3/5 carousel-container">
                <div class="carousel-slides">
                    <div class="carousel-slide">
                        <img src="images/1.png" alt="次元衣帽间应用展示1">
                    </div>
                    <div class="carousel-slide">
                        <img src="images/2.png" alt="次元衣帽间应用展示2">
                    </div>
                    <div class="carousel-slide">
                        <img src="images/3.png" alt="次元衣帽间应用展示3">
                    </div>
                    <div class="carousel-slide">
                        <img src="images/4.png" alt="次元衣帽间应用展示4">
                    </div>
                    <div class="carousel-slide">
                        <img src="images/5.png" alt="次元衣帽间应用展示5">
                    </div>
                    <div class="carousel-slide">
                        <img src="images/6.png" alt="次元衣帽间应用展示6">
                    </div>
                </div>
                <div class="carousel-arrow carousel-arrow-left">
                    <i class="fas fa-chevron-left"></i>
                </div>
                <div class="carousel-arrow carousel-arrow-right">
                    <i class="fas fa-chevron-right"></i>
                </div>
                <div class="carousel-indicators">
                    <div class="carousel-indicator active"></div>
                    <div class="carousel-indicator"></div>
                    <div class="carousel-indicator"></div>
                    <div class="carousel-indicator"></div>
                    <div class="carousel-indicator"></div>
                    <div class="carousel-indicator"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">次元衣帽间，让穿衣更智能</h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">基于先进AI技术，打造您的虚拟试衣体验</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                <!-- Feature 1 -->
                <div class="feature-card bg-light rounded-xl p-6 shadow-md transition-all duration-300">
                    <div class="w-16 h-16 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mb-6">
                        <i class="fas fa-tshirt text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">AI虚拟试衣</h3>
                    <p class="text-gray-600">结合最新图像处理技术，上传个人照片，即可虚拟试穿各种服装，快速预览效果。</p>
                </div>
                
                <!-- Feature 2 -->
                <div class="feature-card bg-light rounded-xl p-6 shadow-md transition-all duration-300">
                    <div class="w-16 h-16 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mb-6">
                        <i class="fas fa-layer-group text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">无限衣橱管理</h3>
                    <p class="text-gray-600">创建个性化衣橱分类，轻松组织管理您的服装，随时浏览衣橱中的每一件心爱服饰。</p>
                </div>
                
                <!-- Feature 3 -->
                <div class="feature-card bg-light rounded-xl p-6 shadow-md transition-all duration-300">
                    <div class="w-16 h-16 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mb-6">
                        <i class="fas fa-palette text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">自定义穿搭</h3>
                    <p class="text-gray-600">自由创建穿搭组合，保存喜爱的搭配，一键生成穿搭效果，让您的时尚创意永不丢失。</p>
                </div>
                
                <!-- Feature 4 -->
                <div class="feature-card bg-light rounded-xl p-6 shadow-md transition-all duration-300">
                    <div class="w-16 h-16 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mb-6">
                        <i class="fas fa-magic text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">智能服饰图</h3>
                    <p class="text-gray-600">集成智能抠图技术，自动处理服装图片，去除背景，让您的衣物展示更加清晰专业。</p>
                </div>
                
                <!-- Feature 5 -->
                <div class="feature-card bg-light rounded-xl p-6 shadow-md transition-all duration-300">
                    <div class="w-16 h-16 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mb-6">
                        <i class="fas fa-history text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">试衣历史记录</h3>
                    <p class="text-gray-600">自动保存所有试衣效果，随时查看历史试穿记录，比较不同服装效果，帮助做出最佳选择。</p>
                </div>
                
                <!-- Feature 6 -->
                <div class="feature-card bg-light rounded-xl p-6 shadow-md transition-all duration-300">
                    <div class="w-16 h-16 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mb-6">
                        <i class="fas fa-cloud text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">云端数据同步</h3>
                    <p class="text-gray-600">所有衣物、照片和穿搭数据安全存储于云端，支持多设备访问，让您的数字衣橱随处可用。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="py-20 bg-light">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">简单四步，体验虚拟试衣</h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">次元衣帽间让虚拟试衣变得轻松简单，按部就班即可获得完美试衣体验</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Step 1 -->
                <div class="text-center bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
                    <div class="w-20 h-20 rounded-full bg-primary text-white text-2xl font-bold flex items-center justify-center mx-auto mb-6">1</div>
                    <h3 class="text-xl font-bold mb-3">上传个人照片</h3>
                    <p class="text-gray-600 mb-6">在小程序中上传您的个人照片，作为虚拟试衣的基础。</p>
                    <div class="step-image-container rounded-lg overflow-hidden mb-4 bg-gray-100" style="height: 360px;" data-image="images/step1.png" data-title="上传个人照片">
                        <img src="images/step1.png" alt="上传个人照片" class="w-full h-full object-contain">
                    </div>
                </div>
                
                <!-- Step 2 -->
                <div class="text-center bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
                    <div class="w-20 h-20 rounded-full bg-primary text-white text-2xl font-bold flex items-center justify-center mx-auto mb-6">2</div>
                    <h3 class="text-xl font-bold mb-3">选择心仪服装</h3>
                    <p class="text-gray-600 mb-6">从衣橱中选择或上传您想要尝试的服装图片。</p>
                    <div class="step-image-container rounded-lg overflow-hidden mb-4 bg-gray-100" style="height: 360px;" data-image="images/step2.png" data-title="选择心仪服装">
                        <img src="images/step2.png" alt="选择心仪服装" class="w-full h-full object-contain">
                    </div>
                </div>
                
                <!-- Step 3 -->
                <div class="text-center bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
                    <div class="w-20 h-20 rounded-full bg-primary text-white text-2xl font-bold flex items-center justify-center mx-auto mb-6">3</div>
                    <h3 class="text-xl font-bold mb-3">等待试衣合成</h3>
                    <p class="text-gray-600 mb-6">AI算法开始处理您的图片，进行智能分析与匹配，这通常只需几秒钟时间。</p>
                    <div class="step-image-container rounded-lg overflow-hidden mb-4 bg-gray-100" style="height: 360px;" data-image="images/step3.png" data-title="等待试衣合成">
                        <img src="images/step3.png" alt="等待试衣合成" class="w-full h-full object-contain">
                    </div>
                </div>
                
                <!-- Step 4 -->
                <div class="text-center bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
                    <div class="w-20 h-20 rounded-full bg-primary text-white text-2xl font-bold flex items-center justify-center mx-auto mb-6">4</div>
                    <h3 class="text-xl font-bold mb-3">AI生成效果</h3>
                    <p class="text-gray-600 mb-6">系统自动处理，利用AI技术生成您穿着该服装的效果图。</p>
                    <div class="step-image-container rounded-lg overflow-hidden mb-4 bg-gray-100" style="height: 360px;" data-image="images/step4.png" data-title="AI生成效果">
                        <img src="images/step4.png" alt="AI生成效果" class="w-full h-full object-contain">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Mini Program QR Code Section -->
    <section id="miniprogram" class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row items-center justify-between">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <h2 class="text-3xl md:text-4xl font-bold mb-6">立即体验次元衣帽间</h2>
                    <p class="text-lg text-gray-600 mb-8">扫描右侧小程序码，即刻开启您的虚拟试衣之旅，打造专属数字衣橱。</p>
                    <ul class="space-y-4">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-primary mt-1 mr-3"></i>
                            <span>完全免费使用，无需下载额外应用</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-primary mt-1 mr-3"></i>
                            <span>高效AI技术支持，试衣效果更真实</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-primary mt-1 mr-3"></i>
                            <span>云端数据存储，安全可靠</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-primary mt-1 mr-3"></i>
                            <span>定期功能更新，持续优化体验</span>
                        </li>
                    </ul>
                </div>
                <div class="md:w-1/3 flex justify-center">
                    <div class="bg-white p-6 rounded-xl shadow-lg text-center">
                        <img src="images/qrcode.png" alt="次元衣帽间小程序码" class="mx-auto mb-4" width="250" height="250">
                        <p class="text-dark font-medium">微信扫码进入小程序</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-light">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">关于次元衣帽间</h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">创新虚拟试衣解决方案，让时尚更智能</p>
            </div>
            
            <div class="max-w-4xl mx-auto">
                <p class="text-lg text-gray-700 mb-6">次元衣帽间是一款基于微信小程序的虚拟试衣应用。用户可以上传自己的个人照片，浏览并选择存储在云端的服装图片，通过集成的AI试衣技术生成虚拟试衣效果图。</p>
                
                <p class="text-lg text-gray-700 mb-6">项目还支持用户创建和管理自己的衣橱分类，以及保存和浏览服装搭配组合（穿搭）。项目旨在提供便捷、有趣的线上服装搭配体验。</p>
                
                <p class="text-lg text-gray-700 mb-10">我们的愿景是通过科技创新，改变人们的购物和穿搭方式，降低试衣成本，提升用户体验，为时尚行业带来更多可能性。</p>
                
                <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6">
                    <a href="#miniprogram" class="bg-primary hover:bg-opacity-90 text-white px-8 py-3 rounded-full font-medium inline-flex items-center justify-center transition-all">
                        <i class="fab fa-weixin mr-2"></i> 立即体验
                    </a>
                    <a href="mailto:<EMAIL>" class="border border-primary text-primary hover:bg-primary hover:text-white px-8 py-3 rounded-full font-medium inline-flex items-center justify-center transition-all">
                        <i class="fas fa-envelope mr-2"></i> 联系我们
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
                <div>
                    <h3 class="text-xl font-bold mb-4">次元衣帽间</h3>
                    <p class="text-gray-400 mb-6">AI虚拟试衣小程序，打造您的数字衣橱</p>
                    <div class="flex space-x-4">
                        <!--<div class="social-icon-container">
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-weixin text-xl"></i>
                            </a>
                            <div class="qrcode-popup">
                                <img src="images/qrcode.png" alt="微信二维码" class="w-full">
                                <p class="text-dark text-xs mt-2">扫码关注公众号</p>
                            </div>
                        </div>
                        <div class="social-icon-container">
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fab fa-weibo text-xl"></i>
                            </a>
                            <div class="qrcode-popup">
                                <img src="images/qrcode.png" alt="微博二维码" class="w-full">
                                <p class="text-dark text-xs mt-2">关注微博账号</p>
                            </div>
                        </div>-->
                        <div class="social-icon-container">
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <img src="images/xhs.png" alt="小红书" class="w-12 h-10 inline-block">
                            </a>
                            <div class="qrcode-popup">
                                <img src="images/xhsqrcode.jpg" alt="小红书二维码" class="w-full">
                                <p class="text-dark text-xs mt-2">关注小红书账号</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-xl font-bold mb-4">快速链接</h3>
                    <ul class="space-y-2">
                        <li><a href="#features" class="text-gray-400 hover:text-white transition-colors">功能特点</a></li>
                        <li><a href="#how-it-works" class="text-gray-400 hover:text-white transition-colors">使用流程</a></li>
                        <li><a href="#miniprogram" class="text-gray-400 hover:text-white transition-colors">立即体验</a></li>
                        <li><a href="#about" class="text-gray-400 hover:text-white transition-colors">关于我们</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-xl font-bold mb-4">联系我们</h3>
                    <ul class="space-y-2">
                        <li class="flex items-start">
                            <i class="fas fa-envelope text-gray-400 mt-1 mr-3"></i>
                            <span class="text-gray-400"><EMAIL></span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-phone text-gray-400 mt-1 mr-3"></i>
                            <span class="text-gray-400">18606539135</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fab fa-weixin text-gray-400 mt-1 mr-3"></i>
                            <span class="text-gray-400">shawii</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-500 mb-4 md:mb-0">&copy; 2025 次元衣帽间 - 版权所有</p>
                <div class="flex space-x-6">
                    <a href="privacy.html" class="text-gray-500 hover:text-white transition-colors">隐私政策</a>
                    <a href="terms.html" class="text-gray-500 hover:text-white transition-colors">服务条款</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Global Image Popup -->
    <div class="global-image-popup">
        <div class="global-image-close">
            <i class="fas fa-times"></i>
        </div>
        <img src="" alt="放大查看">
    </div>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Close mobile menu if open
                const mobileMenu = document.getElementById('mobile-menu');
                if (!mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                }
                
                // Scroll to target
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Carousel functionality
        document.addEventListener('DOMContentLoaded', function() {
            const slides = document.querySelectorAll('.carousel-slide');
            const indicators = document.querySelectorAll('.carousel-indicator');
            const prevButton = document.querySelector('.carousel-arrow-left');
            const nextButton = document.querySelector('.carousel-arrow-right');
            const slidesContainer = document.querySelector('.carousel-slides');
            
            let currentIndex = 0;
            const slideCount = slides.length;
            let slideInterval;

            // Function to move to a specific slide
            function goToSlide(index) {
                // Handle index bounds
                if (index < 0) index = slideCount - 1;
                if (index >= slideCount) index = 0;
                
                currentIndex = index;
                
                // Update slides position
                slidesContainer.style.transform = `translateX(-${currentIndex * 100}%)`;
                
                // Update indicators
                indicators.forEach((indicator, i) => {
                    if (i === currentIndex) {
                        indicator.classList.add('active');
                    } else {
                        indicator.classList.remove('active');
                    }
                });
            }

            // Set up event listeners for arrows
            prevButton.addEventListener('click', () => {
                goToSlide(currentIndex - 1);
                resetInterval();
            });
            
            nextButton.addEventListener('click', () => {
                goToSlide(currentIndex + 1);
                resetInterval();
            });
            
            // Set up event listeners for indicators
            indicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => {
                    goToSlide(index);
                    resetInterval();
                });
            });
            
            // Function to start auto-sliding
            function startInterval() {
                slideInterval = setInterval(() => {
                    goToSlide(currentIndex + 1);
                }, 3500); // 3.5 seconds
            }
            
            // Function to reset interval after manual navigation
            function resetInterval() {
                clearInterval(slideInterval);
                startInterval();
            }
            
            // Initialize carousel
            startInterval();
            
            // Global image popup functionality
            const imageContainers = document.querySelectorAll('.step-image-container');
            const globalPopup = document.querySelector('.global-image-popup');
            const globalPopupImg = globalPopup.querySelector('img');
            const globalPopupClose = document.querySelector('.global-image-close');
            
            // Open popup on image container click
            imageContainers.forEach(container => {
                container.addEventListener('click', function() {
                    const imgSrc = this.getAttribute('data-image');
                    const imgTitle = this.getAttribute('data-title');
                    globalPopupImg.src = imgSrc;
                    globalPopupImg.alt = imgTitle;
                    globalPopup.classList.add('active');
                    document.body.style.overflow = 'hidden'; // Prevent scrolling
                });
            });
            
            // Close popup on close button click
            globalPopupClose.addEventListener('click', function() {
                globalPopup.classList.remove('active');
                document.body.style.overflow = ''; // Restore scrolling
            });
            
            // Close popup on click outside the image
            globalPopup.addEventListener('click', function(e) {
                if (e.target === this) {
                    globalPopup.classList.remove('active');
                    document.body.style.overflow = ''; // Restore scrolling
                }
            });
            
            // Close popup on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && globalPopup.classList.contains('active')) {
                    globalPopup.classList.remove('active');
                    document.body.style.overflow = ''; // Restore scrolling
                }
            });
        });
    </script>
</body>
</html> 