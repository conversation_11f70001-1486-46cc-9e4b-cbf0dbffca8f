<?php
header("Content-Type: application/json");
require_once './db.php';
require_once './auth.php';
require_once './config.php';

// 初始化响应数组
$response = [
    'code' => 0,
    'message' => 'success',
    'data' => []
];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['code'] = 405;
    $response['message'] = 'Method Not Allowed';
    echo json_encode($response);
    exit;
}

// 获取认证头
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';

// 验证Bearer令牌格式
if (empty($authHeader) || strpos($authHeader, 'Bearer ') !== 0) {
    $response['code'] = 401;
    $response['message'] = '无效的认证头';
    echo json_encode($response);
    exit;
}

// 提取令牌
$token = substr($authHeader, 7); // 去掉'Bearer '前缀

// 获取POST参数
$postData = json_decode(file_get_contents("php://input"), true) ?: [];
$keyword = isset($postData['keyword']) ? trim($postData['keyword']) : '';
$page = isset($postData['page']) ? intval($postData['page']) : 1;
$limit = isset($postData['limit']) ? intval($postData['limit']) : 20;
$status = isset($postData['status']) ? trim($postData['status']) : 'all'; // 新增状态筛选参数

// 记录日志，帮助调试
error_log("admin_get_merchants.php - 请求参数: " . json_encode($postData));
error_log("admin_get_merchants.php - 认证头: " . $authHeader);

// 验证管理员令牌
$auth = new Auth();
$adminInfo = $auth->verifyAdminToken($token);

if ($adminInfo === false) {
    $response['code'] = 401;
    $response['message'] = '无效或已过期的管理员令牌';
    echo json_encode($response);
    exit;
}

// 记录日志，帮助调试
error_log("admin_get_merchants.php - 管理员信息: " . json_encode($adminInfo));

$db = new Database();
$conn = $db->getConnection();

try {
    $offset = ($page - 1) * $limit;
    
    // 构建WHERE条件
    $whereConditions = [];
    $params = [];
    
    // 如果有关键词，添加搜索条件
    if (!empty($keyword)) {
        $whereConditions[] = "nickname LIKE ?";
        $params[] = "%{$keyword}%";
    }
    
    // 如果指定了状态筛选，添加状态条件
    if ($status !== 'all') {
        $whereConditions[] = "merchant_status = ?";
        $params[] = $status;
    }
    
    // 构建WHERE子句
    $whereClause = "";
    if (!empty($whereConditions)) {
        $whereClause = " WHERE " . implode(" AND ", $whereConditions);
    }
    
    // 构建SQL查询
    $sql = "SELECT id, nickname, avatar_url, merchant_status, share_try_on_credits, paid_try_on_count 
            FROM users" . $whereClause;
    
    // 添加排序和分页
    $sql .= " ORDER BY id DESC LIMIT ? OFFSET ?";
    $queryParams = array_merge($params, [$limit, $offset]);
    
    $stmt = $conn->prepare($sql);
    
    // 使用位置参数绑定
    if (!empty($queryParams)) {
        for ($i = 0; $i < count($queryParams); $i++) {
            $paramType = is_int($queryParams[$i]) ? PDO::PARAM_INT : PDO::PARAM_STR;
            $stmt->bindValue($i + 1, $queryParams[$i], $paramType);
        }
    }
    
    $stmt->execute();
    $merchants = [];
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        // 获取该用户的衣物数量
        $clothesCountSql = "SELECT COUNT(*) as count FROM clothes WHERE user_id = ?";
        $clothesCountStmt = $conn->prepare($clothesCountSql);
        $clothesCountStmt->bindValue(1, $row['id'], PDO::PARAM_INT);
        $clothesCountStmt->execute();
        $clothesCount = $clothesCountStmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
        
        $merchants[] = [
            'id' => $row['id'],
            'nickname' => $row['nickname'] ?: '未设置昵称',
            'avatar_url' => $row['avatar_url'],
            'merchant_status' => $row['merchant_status'] ?: 'no',
            'share_try_on_credits' => (int)$row['share_try_on_credits'],
            'paid_try_on_count' => (int)$row['paid_try_on_count'],
            'clothes_count' => (int)$clothesCount // 添加衣物数量字段
        ];
    }
    
    // 获取总数量 - 确保使用相同的WHERE条件
    $countSql = "SELECT COUNT(*) as total FROM users" . $whereClause;
    
    $countStmt = $conn->prepare($countSql);
    
    // 使用与WHERE条件相同的参数绑定（不包括分页参数）
    if (!empty($params)) {
        for ($i = 0; $i < count($params); $i++) {
            $paramType = is_int($params[$i]) ? PDO::PARAM_INT : PDO::PARAM_STR;
            $countStmt->bindValue($i + 1, $params[$i], $paramType);
        }
    }
    
    $countStmt->execute();
    $totalRow = $countStmt->fetch(PDO::FETCH_ASSOC);
    $total = $totalRow['total'];
    
    $response['data'] = [
        'list' => $merchants,
        'total' => $total,
        'page' => $page,
        'limit' => $limit
    ];
} catch (Exception $e) {
    $response['code'] = 500;
    $response['message'] = '处理请求时发生错误: ' . $e->getMessage();
    error_log("admin_get_merchants.php - 错误: " . $e->getMessage());
    error_log("admin_get_merchants.php - 错误详情: " . $e->getTraceAsString());
} finally {
    // PDO connections are closed automatically when the variable is unset
    $conn = null;
}

echo json_encode($response); 