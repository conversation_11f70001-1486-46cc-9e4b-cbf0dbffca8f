/**
 * 穿搭列表页面的JavaScript逻辑
 * 用于加载穿搭列表数据并处理用户交互
 */

// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalPages = 0;
let searchKeyword = '';

// DOM元素
const outfitTableBody = document.getElementById('outfitTableBody');
const searchInput = document.getElementById('searchInput');
const searchBtn = document.getElementById('searchBtn');
const prevBtn = document.getElementById('prevBtn');
const nextBtn = document.getElementById('nextBtn');
const pageInfo = document.getElementById('pageInfo');
const outfitModal = document.getElementById('outfitModal');
const outfitModalBody = document.getElementById('outfitModalBody');
const outfitError = document.getElementById('outfitError');
const outfitLoading = document.getElementById('outfitLoading');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    init();
    
    // 绑定搜索按钮点击事件
    searchBtn.addEventListener('click', function() {
        searchKeyword = searchInput.value.trim();
        currentPage = 1;
        loadOutfits();
    });
    
    // 绑定搜索框回车事件
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchKeyword = searchInput.value.trim();
            currentPage = 1;
            loadOutfits();
        }
    });
    
    // 绑定分页按钮事件
    prevBtn.addEventListener('click', function() {
        if (currentPage > 1) {
            currentPage--;
            loadOutfits();
        }
    });
    
    nextBtn.addEventListener('click', function() {
        if (currentPage < totalPages) {
            currentPage++;
            loadOutfits();
        }
    });
    
    // 初始化模态框
    initModal();
});

/**
 * 初始化函数
 */
function init() {
    // 加载穿搭列表
    loadOutfits();
}

/**
 * 初始化模态框
 */
function initModal() {
    if (!outfitModal) return;
    
    // 获取关闭按钮
    const closeButtons = outfitModal.querySelectorAll('.modal-close');
    
    // 绑定关闭按钮事件
    closeButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            outfitModal.style.display = 'none';
        });
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', (e) => {
        if (e.target === outfitModal) {
            outfitModal.style.display = 'none';
        }
    });
}

/**
 * 加载穿搭列表数据
 */
function loadOutfits() {
    // 显示加载中
    outfitTableBody.innerHTML = '<tr><td colspan="8" class="no-data">加载中...</td></tr>';
    
    // 构建API URL
    let apiUrl = '../login_backend/admin_get_outfits.php?page=' + currentPage + '&limit=' + pageSize;
    
    // 添加搜索参数
    if (searchKeyword) {
        if (isNumeric(searchKeyword)) {
            apiUrl += '&user_id=' + encodeURIComponent(searchKeyword);
        } else {
            apiUrl += '&search=' + encodeURIComponent(searchKeyword);
        }
    }
    
    // 发起请求
    fetch(apiUrl, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + localStorage.getItem('admin_token')
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络请求失败');
        }
        return response.json();
    })
    .then(data => {
        // 处理响应数据
        if (data.status === 'success') {
            // 渲染表格
            renderOutfitTable(data.data.outfits);
            
            // 更新分页信息
            totalPages = Math.ceil(data.data.total / pageSize);
            updatePagination();
        } else {
            showError(data.message || '加载穿搭列表失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('加载穿搭列表失败: ' + error.message);
    });
}

/**
 * 渲染穿搭表格
 * @param {Array} outfits 穿搭数据数组
 */
function renderOutfitTable(outfits) {
    if (!outfits || outfits.length === 0) {
        outfitTableBody.innerHTML = '<tr><td colspan="8" class="no-data">暂无数据</td></tr>';
        return;
    }
    
    // 清空表格
    outfitTableBody.innerHTML = '';
    
    // 渲染每行数据
    outfits.forEach(outfit => {
        const row = document.createElement('tr');
        
        // 创建图片预览
        let imagePreview = '';
        
        // 如果有图片，创建显示
        if (outfit.image_urls && outfit.image_urls.length > 0) {
            // 创建缩略图HTML
            let thumbnailsHtml = '';
            
            // 使用本地默认图片路径
            const defaultImagePath = 'img/default-outfit.png';
            
            // 添加每张图片的缩略图
            outfit.image_urls.forEach((imageUrl, index) => {
                thumbnailsHtml += `<img src="${imageUrl}" alt="${outfit.name || '穿搭'}" 
                                      class="clothing-thumbnail outfit-img viewable-image" 
                                      data-origin="${imageUrl}"
                                      onerror="this.onerror=null;this.src='${defaultImagePath}';" 
                                      title="${outfit.name || '穿搭'} #${index+1}" 
                                      style="cursor: pointer;">`;
            });
            
            // 使用flex容器包装所有缩略图
            imagePreview = `<div class="clothing-thumbnails-container">${thumbnailsHtml}</div>`;
        } else {
            // 没有图片时显示默认图片
            imagePreview = `<img src="img/default-outfit.png" alt="${outfit.name}" class="thumbnail outfit-img">`;
        }
        
        // 设置单元格内容
        row.innerHTML = `
            <td>${outfit.id}</td>
            <td>${imagePreview}</td>
            <td>${escapeHtml(outfit.name || '未命名穿搭')}</td>
            <td>${outfit.user_id}</td>
            <td>${escapeHtml(outfit.category_name || '未分类')}</td>
            <td>${outfit.clothes_count || 0}</td>
            <td>${formatDate(outfit.created_at)}</td>
            <td>
                <button class="action-btn view-btn" data-id="${outfit.id}">查看</button>
            </td>
        `;
        
        // 绑定查看按钮点击事件
        row.querySelector('.view-btn').addEventListener('click', function() {
            const outfitId = this.getAttribute('data-id');
            viewOutfitDetails(outfitId);
        });
        
        // 添加到表格
        outfitTableBody.appendChild(row);
    });
    
    // 绑定图片查看器到所有图片
    setTimeout(() => {
        if (typeof ImageViewer !== 'undefined') {
            // 绑定图片查看器到所有可查看图片
            ImageViewer.bindImages('.viewable-image, .thumbnail');
            console.log('已为穿搭列表图片绑定查看器');
        } else {
            console.warn('ImageViewer未加载，图片查看功能不可用');
        }
    }, 300);
}

/**
 * 更新分页信息
 */
function updatePagination() {
    // 更新页码信息
    pageInfo.textContent = `第 ${currentPage}/${totalPages} 页`;
    
    // 更新按钮状态
    prevBtn.disabled = currentPage <= 1;
    nextBtn.disabled = currentPage >= totalPages;
}

/**
 * 显示错误信息
 * @param {string} message 错误信息
 */
function showError(message) {
    if (outfitError) {
        outfitError.textContent = message;
        outfitError.style.display = 'block';
        
        // 5秒后自动隐藏
        setTimeout(() => {
            outfitError.style.display = 'none';
        }, 5000);
    } else {
        outfitTableBody.innerHTML = `<tr><td colspan="8" class="no-data error">${message}</td></tr>`;
    }
}

/**
 * 显示/隐藏加载指示器
 * @param {boolean} show 是否显示加载指示器
 */
function showLoading(show) {
    if (outfitLoading) {
        outfitLoading.style.display = show ? 'block' : 'none';
    }
}

/**
 * 查看穿搭详情
 * @param {string} outfitId 穿搭ID
 */
function viewOutfitDetails(outfitId) {
    if (!outfitModal || !outfitModalBody) {
        // 如果模态框不存在，则使用旧的跳转方式
        window.location.href = `outfit_details.html?id=${outfitId}`;
        return;
    }
    
    // 显示模态框
    outfitModal.style.display = 'block';
    outfitModalBody.innerHTML = '<div class="loading-indicator">加载中...</div>';
    
    // 加载穿搭详情
    loadOutfitDetail(outfitId);
}

/**
 * 加载穿搭详情
 * @param {string} outfitId 穿搭ID
 */
function loadOutfitDetail(outfitId) {
    // 构建API URL
    const apiUrl = `../login_backend/admin_get_outfits.php?id=${outfitId}`;
    
    // 发起请求
    fetch(apiUrl, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + localStorage.getItem('admin_token')
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络请求失败');
        }
        return response.json();
    })
    .then(data => {
        // 处理响应数据
        if (data.status === 'success' && data.data.outfits && data.data.outfits.length > 0) {
            // 渲染穿搭详情
            renderOutfitDetail(data.data.outfits[0]);
            
            // 加载穿搭中的衣物
            loadOutfitClothes(data.data.outfits[0].id);
        } else {
            outfitModalBody.innerHTML = '<div class="error-message">穿搭不存在或已被删除</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        outfitModalBody.innerHTML = `<div class="error-message">加载穿搭详情失败: ${error.message}</div>`;
    });
}

/**
 * 加载穿搭中的衣物
 * @param {string} outfitId 穿搭ID
 */
function loadOutfitClothes(outfitId) {
    // 构建API URL
    const apiUrl = `../login_backend/admin_get_outfit_clothes.php?outfit_id=${outfitId}`;
    
    // 发起请求
    fetch(apiUrl, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + localStorage.getItem('admin_token')
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络请求失败');
        }
        return response.json();
    })
    .then(data => {
        // 处理响应数据
        if (data.status === 'success') {
            // 渲染衣物列表
            renderOutfitClothes(data.data.clothes || []);
        } else {
            // 显示空衣物列表
            renderOutfitClothes([]);
            console.warn('加载穿搭衣物失败:', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // 显示空衣物列表，但在控制台记录错误
        renderOutfitClothes([]);
        console.error('加载穿搭衣物失败:', error);
    });
}

/**
 * 渲染穿搭详情
 * @param {Object} outfit 穿搭数据对象
 */
function renderOutfitDetail(outfit) {
    // 创建图片部分
    let imagesHTML = '';
    if (outfit.image_urls && outfit.image_urls.length > 0) {
        const mainImage = outfit.image_urls[0];
        
        // 缩略图HTML
        let thumbnailsHTML = '';
        if (outfit.image_urls.length > 1) {
            thumbnailsHTML = `
                <div class="outfit-thumbnails">
                    ${outfit.image_urls.map((url, index) => 
                        `<img src="${url}" alt="预览图${index+1}" class="outfit-thumbnail ${index === 0 ? 'active' : ''}" 
                              onclick="changeMainOutfitImage('${url}', this)">`
                    ).join('')}
                </div>
            `;
        }
        
        imagesHTML = `
            <div class="outfit-images">
                <img src="${mainImage}" alt="${escapeHtml(outfit.name)}" class="outfit-main-image viewable-image" id="outfitMainImage" data-origin="${mainImage}">
                ${thumbnailsHTML}
            </div>
        `;
    } else {
        imagesHTML = `
            <div class="outfit-images">
                <img src="img/default-outfit.png" alt="${escapeHtml(outfit.name)}" class="outfit-main-image">
            </div>
        `;
    }
    
    // 创建信息部分
    const infoHTML = `
        <div class="outfit-info-list">
            <div class="info-row">
                <span class="info-label">穿搭名称：</span>
                <span class="info-value">${escapeHtml(outfit.name || '未命名穿搭')}</span>
            </div>
            <div class="info-row">
                <span class="info-label">穿搭ID：</span>
                <span class="info-value">${outfit.id}</span>
            </div>
            <div class="info-row">
                <span class="info-label">用户ID：</span>
                <span class="info-value">${outfit.user_id}</span>
            </div>
            <div class="info-row">
                <span class="info-label">分类：</span>
                <span class="info-value">${escapeHtml(outfit.category_name || '未分类')}</span>
            </div>
            <div class="info-row">
                <span class="info-label">描述：</span>
                <span class="info-value">${escapeHtml(outfit.description || '无描述')}</span>
            </div>
            <div class="info-row">
                <span class="info-label">创建时间：</span>
                <span class="info-value">${formatDate(outfit.created_at)}</span>
            </div>
            <div class="info-row">
                <span class="info-label">衣物数量：</span>
                <span class="info-value">${outfit.clothes_count || 0}</span>
            </div>
        </div>
    `;
    
    // 创建衣物容器
    const clothesHTML = `
        <h4 style="margin-top:15px;margin-bottom:10px;">穿搭包含的衣物</h4>
        <div id="outfitClothesContainer" class="clothes-list">
            <div class="loading-indicator">加载衣物中...</div>
        </div>
    `;
    
    // 组合所有内容
    const detailHTML = `
        <div class="outfit-item">
            <div class="outfit-header">
                <div class="user-info">
                    <div class="user-name">${escapeHtml(outfit.name || '未命名穿搭')}</div>
                    <div class="timestamp">${formatDate(outfit.created_at)}</div>
                </div>
            </div>
            ${imagesHTML}
            ${infoHTML}
            ${clothesHTML}
        </div>
    `;
    
    // 设置模态框内容
    outfitModalBody.innerHTML = detailHTML;
    
    // 绑定图片查看器
    setTimeout(() => {
        if (typeof ImageViewer !== 'undefined') {
            ImageViewer.bindImages('.viewable-image');
        }
    }, 300);
}

/**
 * 渲染穿搭中的衣物
 * @param {Array} clothes 衣物数据数组
 */
function renderOutfitClothes(clothes) {
    const container = document.getElementById('outfitClothesContainer');
    if (!container) return;
    
    if (!clothes || clothes.length === 0) {
        container.innerHTML = '<div class="no-data">该穿搭暂无衣物</div>';
        return;
    }
    
    let html = '';
    
    // 使用本地默认图片路径，避免远程请求失败
    const defaultImagePath = 'img/default-clothing.png';
    
    // 渲染每个衣物项
    clothes.forEach(cloth => {
        const imageUrl = cloth.image_url || defaultImagePath;
        
        html += `
            <div class="clothes-item">
                <div class="clothes-image-container">
                    <img src="${imageUrl}" alt="${escapeHtml(cloth.name || '衣物')}" 
                         class="clothes-image viewable-image"
                         data-origin="${imageUrl}"
                         onerror="this.onerror=null;this.src='${defaultImagePath}';">
                </div>
                <div class="clothes-details">
                    <div class="clothes-category">${escapeHtml(cloth.type || '未知类型')}</div>
                    <div class="clothes-description">
                        <strong>衣物名称:</strong> ${escapeHtml(cloth.name || '未命名衣物')}<br>
                        <strong>描述:</strong> ${escapeHtml(cloth.description || '无描述')}
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
    
    // 绑定图片查看器
    setTimeout(() => {
        if (typeof ImageViewer !== 'undefined') {
            ImageViewer.bindImages('.viewable-image');
        }
    }, 300);
}

/**
 * 切换主图显示
 * @param {string} imageUrl 图片URL
 * @param {HTMLElement} thumbElement 缩略图元素
 */
window.changeMainOutfitImage = function(imageUrl, thumbElement) {
    // 更新主图
    const mainImage = document.getElementById('outfitMainImage');
    if (mainImage) {
        mainImage.src = imageUrl;
        mainImage.setAttribute('data-origin', imageUrl);
    }
    
    // 更新缩略图激活状态
    const thumbs = document.querySelectorAll('.outfit-thumbnail');
    thumbs.forEach(thumb => thumb.classList.remove('active'));
    if (thumbElement) {
        thumbElement.classList.add('active');
    }
};

/**
 * 格式化日期
 * @param {string} dateString 日期字符串
 * @returns {string} 格式化后的日期
 */
function formatDate(dateString) {
    if (!dateString) return '未知';
    
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * 检查字符串是否为数字
 * @param {string} str 要检查的字符串
 * @returns {boolean} 是否为数字
 */
function isNumeric(str) {
    return /^\d+$/.test(str);
}

/**
 * HTML转义
 * @param {string} str 要转义的字符串
 * @returns {string} 转义后的字符串
 */
function escapeHtml(str) {
    if (!str) return '';
    return str
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
} 