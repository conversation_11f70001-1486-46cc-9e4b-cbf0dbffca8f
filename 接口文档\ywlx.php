<?php
// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method Not Allowed']);
    exit;
}

// 记录请求
$request_time = date('Y-m-d H:i:s');
$log_file = 'ywlx_log.txt';
file_put_contents($log_file, "[$request_time] 收到请求\n", FILE_APPEND);

// 记录请求头
$headers = getallheaders();
$headers_json = json_encode($headers);
file_put_contents($log_file, "[$request_time] 请求头: $headers_json\n", FILE_APPEND);

// 记录原始请求体
$raw_post_data = file_get_contents('php://input');
file_put_contents($log_file, "[$request_time] 原始请求体: $raw_post_data\n", FILE_APPEND);

// 设置Gemini API密钥和模型
$apiKey = 'AIzaSyCkt3alFLjYSjG1nkYeM3UuXHktr3NMPKQ';
$model = 'gemini-1.5-flash';

// 构建Gemini API URL
$url = "https://generativelanguage.googleapis.com/v1beta/models/{$model}:generateContent?key={$apiKey}";

// 处理图片上传
$image_data = null;
$mime_type = null;

// 检查是否有图片上传
if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
    // 从上传的文件获取图片数据
    $image_path = $_FILES['image']['tmp_name'];
    $image_data = file_get_contents($image_path);
    $mime_type = $_FILES['image']['type'];
    file_put_contents($log_file, "[$request_time] 接收到文件上传\n", FILE_APPEND);
} elseif (isset($_POST['image_base64']) || isset($_POST['image_url'])) {
    if (isset($_POST['image_base64'])) {
        // 从POST数据中获取base64编码的图片
        $base64_data = $_POST['image_base64'];
        // 从base64字符串中提取MIME类型和实际数据
        if (preg_match('/^data:(image\/[^;]+);base64,(.+)$/', $base64_data, $matches)) {
            $mime_type = $matches[1];
            $image_data = base64_decode($matches[2]);
        } else {
            $image_data = base64_decode($base64_data);
            $mime_type = 'image/jpeg'; // 默认MIME类型
        }
        file_put_contents($log_file, "[$request_time] 接收到base64图片\n", FILE_APPEND);
    } else {
        // 从URL获取图片
        $image_url = $_POST['image_url'];
        $image_data = file_get_contents($image_url);
        
        // 尝试从URL或内容中确定MIME类型
        $mime_type = 'image/jpeg'; // 默认值
        // 如果URL包含文件扩展名，可以尝试推断MIME类型
        if (preg_match('/\.(jpe?g|png|gif|webp)$/i', $image_url, $matches)) {
            $ext = strtolower($matches[1]);
            $mime_map = [
                'jpg' => 'image/jpeg',
                'jpeg' => 'image/jpeg',
                'png' => 'image/png',
                'gif' => 'image/gif',
                'webp' => 'image/webp'
            ];
            $mime_type = isset($mime_map[$ext]) ? $mime_map[$ext] : 'image/jpeg';
        }
        file_put_contents($log_file, "[$request_time] 从URL获取图片: $image_url\n", FILE_APPEND);
    }
} elseif ($raw_post_data) {
    // 尝试从原始请求体获取JSON数据
    $json_data = json_decode($raw_post_data, true);
    file_put_contents($log_file, "[$request_time] JSON解析结果: " . json_encode($json_data) . "\n", FILE_APPEND);
    
    if ($json_data && isset($json_data['image_url'])) {
        $image_url = $json_data['image_url'];
        file_put_contents($log_file, "[$request_time] 尝试从URL获取图片: $image_url\n", FILE_APPEND);
        
        try {
            $image_data = @file_get_contents($image_url);
            if ($image_data === false) {
                file_put_contents($log_file, "[$request_time] 从URL获取图片失败\n", FILE_APPEND);
                // 尝试使用cURL获取图片
                $ch = curl_init($image_url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                $image_data = curl_exec($ch);
                $curl_error = curl_error($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($image_data === false) {
                    file_put_contents($log_file, "[$request_time] cURL获取图片失败: $curl_error, HTTP状态码: $http_code\n", FILE_APPEND);
                } else {
                    file_put_contents($log_file, "[$request_time] cURL获取图片成功, HTTP状态码: $http_code, 数据大小: " . strlen($image_data) . " 字节\n", FILE_APPEND);
                }
            } else {
                file_put_contents($log_file, "[$request_time] 成功从URL获取图片, 数据大小: " . strlen($image_data) . " 字节\n", FILE_APPEND);
            }
        } catch (Exception $e) {
            file_put_contents($log_file, "[$request_time] 获取图片异常: " . $e->getMessage() . "\n", FILE_APPEND);
            $image_data = null;
        }
        
        // 从URL推断MIME类型
        $mime_type = 'image/jpeg'; // 默认值
        if (preg_match('/\.(jpe?g|png|gif|webp)$/i', $image_url, $matches)) {
            $ext = strtolower($matches[1]);
            $mime_map = [
                'jpg' => 'image/jpeg',
                'jpeg' => 'image/jpeg',
                'png' => 'image/png',
                'gif' => 'image/gif',
                'webp' => 'image/webp'
            ];
            $mime_type = isset($mime_map[$ext]) ? $mime_map[$ext] : 'image/jpeg';
        }
    } elseif ($json_data && isset($json_data['image_base64'])) {
        $base64_data = $json_data['image_base64'];
        if (preg_match('/^data:(image\/[^;]+);base64,(.+)$/', $base64_data, $matches)) {
            $mime_type = $matches[1];
            $image_data = base64_decode($matches[2]);
        } else {
            $image_data = base64_decode($base64_data);
            $mime_type = 'image/jpeg';
        }
        file_put_contents($log_file, "[$request_time] 从JSON请求体获取base64图片\n", FILE_APPEND);
    } else {
        file_put_contents($log_file, "[$request_time] 请求体格式无效或缺少必要字段\n", FILE_APPEND);
    }
}

// 如果没有图片数据，返回错误
if (!$image_data) {
    http_response_code(400);
    $error_response = ['error' => 'No image data provided', 'success' => false];
    echo json_encode($error_response);
    file_put_contents($log_file, "[$request_time] 错误: 没有提供图片数据\n", FILE_APPEND);
    exit;
}

// 将图片转换为base64
$base64_image = base64_encode($image_data);

// 专门针对服装分类的提示词 - 修改为返回英文类型
$prompt = "Please analyze this clothing image and classify it into one of these three categories: 'Upper' (for upper body garments like tops, T-shirts, shirts), 'Lower' (for lower body garments like pants, shorts, skirts), or 'Dress' (for full-body garments like dresses, jumpsuits).";

// 要求输出格式为简洁的JSON
$enhanced_prompt = $prompt . " Please return ONLY a simple JSON object in this exact format: {\"clothing_type\": \"CATEGORY\"}, where CATEGORY must be exactly one of these three options: 'Upper', 'Lower', or 'Dress'. Do not add any additional text, explanation, or analysis outside of this JSON.";

file_put_contents($log_file, "[$request_time] 提示词: $enhanced_prompt\n", FILE_APPEND);

// 构建多模态请求
$geminiRequestData = [
    'contents' => [
        [
            'parts' => [
                [
                    'text' => $enhanced_prompt
                ],
                [
                    'inline_data' => [
                        'mime_type' => $mime_type,
                        'data' => $base64_image
                    ]
                ]
            ]
        ]
    ],
    // 定义生成配置 - 使用较低的temperature以获得更确定的结果
    'generationConfig' => [
        'temperature' => 0.1,
        'topP' => 0.9,
        'topK' => 40,
        'maxOutputTokens' => 100  // 限制输出长度，因为我们只需要简短的分类结果
    ]
];

// 初始化cURL会话
$ch = curl_init($url);

// 设置cURL选项
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($geminiRequestData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

// 执行cURL请求
$start_time = microtime(true);
$response = curl_exec($ch);
$end_time = microtime(true);
$duration = round(($end_time - $start_time) * 1000); // 毫秒

$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);

// 关闭cURL会话
curl_close($ch);

file_put_contents($log_file, "[$request_time] Gemini API请求耗时: {$duration}ms, HTTP状态码: $httpCode\n", FILE_APPEND);

// 检查cURL错误
if ($curlError) {
    http_response_code(500);
    $error_response = ['error' => 'cURL Error: ' . $curlError, 'success' => false];
    echo json_encode($error_response);
    file_put_contents($log_file, "[$request_time] cURL错误: $curlError\n", FILE_APPEND);
    exit;
}

// 检查HTTP状态码
if ($httpCode !== 200) {
    http_response_code($httpCode);
    $error_response = ['error' => 'HTTP Error: ' . $httpCode, 'response' => json_decode($response, true), 'success' => false];
    echo json_encode($error_response);
    file_put_contents($log_file, "[$request_time] HTTP错误: $httpCode, 响应: $response\n", FILE_APPEND);
    exit;
}

// 解析Gemini API响应
$gemini_response = json_decode($response, true);
file_put_contents($log_file, "[$request_time] Gemini API响应: " . json_encode($gemini_response) . "\n", FILE_APPEND);

// 提取文本内容
$content = '';
if (isset($gemini_response['candidates'][0]['content']['parts'][0]['text'])) {
    $content = $gemini_response['candidates'][0]['content']['parts'][0]['text'];
    file_put_contents($log_file, "[$request_time] Gemini原始输出: $content\n", FILE_APPEND);
}

// 尝试从文本中提取JSON
$clothing_type = '';
if (preg_match('/\{.*"clothing_type"\s*:\s*"([^"]+)".*\}/s', $content, $matches)) {
    $clothing_type = $matches[1];
} else {
    // 如果无法解析JSON，尝试直接从文本中识别关键词
    if (stripos($content, 'Upper') !== false) {
        $clothing_type = 'Upper';
    } elseif (stripos($content, 'Lower') !== false) {
        $clothing_type = 'Lower';
    } elseif (stripos($content, 'Dress') !== false) {
        $clothing_type = 'Dress';
    } else {
        // 默认分类
        $clothing_type = 'Upper'; // 默认值
    }
}

// 确保分类结果是三个预定义类型之一
if (!in_array($clothing_type, ['Upper', 'Lower', 'Dress'])) {
    // 如果返回的不是预期值，设置为默认值
    $clothing_type = 'Upper';
    file_put_contents($log_file, "[$request_time] 警告: 非预期的分类结果，设置为默认值'Upper'\n", FILE_APPEND);
}

// 构建最终响应
$final_response = [
    'clothing_type' => $clothing_type,
    'success' => true
];

// 返回JSON响应
echo json_encode($final_response);

file_put_contents($log_file, "[$request_time] 最终分类结果: $clothing_type\n\n", FILE_APPEND);
?> 