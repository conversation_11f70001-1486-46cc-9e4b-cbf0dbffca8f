const app = getApp();

Page({
  data: {
    packages: [],
    selectedPackage: null,
    loading: false,
    loadingText: '正在加载...',
    orderId: '',
    orderCheckInterval: null
  },

  onLoad: function(options) {
    this.fetchPackages();
  },

  onUnload: function() {
    // 清除订单状态检查定时器
    if (this.data.orderCheckInterval) {
      clearInterval(this.data.orderCheckInterval);
    }
  },

  // 获取套餐列表
  fetchPackages: function() {
    this.setData({
      loading: true,
      loadingText: '正在加载套餐信息...'
    });

    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_try_on_packages.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        if (res.statusCode === 200 && !res.data.error) {
          this.setData({
            packages: res.data.data,
            loading: false
          });
        } else {
          wx.showToast({
            title: '获取套餐失败: ' + (res.data.msg || '未知错误'),
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: (err) => {
        console.error('获取套餐失败', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  // 选择套餐
  selectPackage: function(e) {
    const packageId = e.currentTarget.dataset.id;
    this.setData({
      selectedPackage: packageId
    });
  },

  // 创建订单
  createOrder: function() {
    if (!this.data.selectedPackage) {
      wx.showToast({
        title: '请先选择套餐',
        icon: 'none'
      });
      return;
    }

    this.setData({
      loading: true,
      loadingText: '正在创建订单...'
    });

    // 调用创建订单API
    wx.request({
      url: `${app.globalData.apiBaseUrl}/create_pay_order.php`,
      method: 'POST',
      header: {
        'Authorization': app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: {
        package_id: this.data.selectedPackage
      },
      success: (res) => {
        // 打印完整响应数据以便调试
        console.log('创建订单API响应:', res);
        
        if (res.statusCode === 200 && !res.data.error) {
          // 安全地获取响应数据
          const responseData = res.data.data || {};
          console.log('订单数据:', responseData);
          
          // 检查是否有订单ID
          let orderId = '';
          if (responseData.order_id) {
            orderId = responseData.order_id;
          } else if (typeof responseData === 'object') {
            // 尝试在对象的第一层查找order_id
            orderId = responseData.order_id || '';
          }
          
          if (!orderId) {
            console.error('无法获取订单ID，响应数据结构不符合预期');
            wx.showToast({
              title: '创建订单失败，响应数据异常',
              icon: 'none'
            });
            this.setData({ loading: false });
            return;
          }
          
          // 设置订单ID
          this.setData({ orderId: orderId });
          console.log('成功设置订单ID:', orderId);
          
          // 发起支付
          this.initiatePayment(responseData);
        } else {
          wx.showToast({
            title: '创建订单失败: ' + (res.data.msg || '未知错误'),
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: (err) => {
        console.error('创建订单失败', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  // 发起支付
  initiatePayment: function(orderData) {
    this.setData({
      loadingText: '正在发起支付...'
    });

    console.log('准备发起支付，订单数据:', orderData);
    
    // 验证支付参数
    if (!orderData || !orderData.pay_params) {
      console.error('支付参数无效:', orderData);
      wx.showToast({
        title: '支付参数错误，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
      return;
    }
    
    const payParams = orderData.pay_params;
    console.log('支付参数:', payParams);
    
    // 验证必要的支付参数
    if (!payParams.timeStamp || !payParams.nonceStr || 
        !payParams.package || !payParams.signType || !payParams.paySign) {
      console.error('支付参数不完整:', payParams);
      wx.showToast({
        title: '支付参数不完整，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
      return;
    }

    // 调用微信支付
    wx.requestPayment({
      timeStamp: payParams.timeStamp,
      nonceStr: payParams.nonceStr,
      package: payParams.package,
      signType: payParams.signType,
      paySign: payParams.paySign,
      success: () => {
        // 支付成功，开始轮询订单状态
        this.setData({
          loadingText: '支付处理中，请稍候...'
        });
        this.startCheckingOrderStatus();
      },
      fail: (err) => {
        console.log('支付失败', err);
        if (err.errMsg.indexOf('cancel') > -1) {
          wx.showToast({
            title: '支付已取消',
            icon: 'none'
          });
        } else {
          wx.showToast({
            title: '支付失败，请重试',
            icon: 'none'
          });
        }
        this.setData({ loading: false });
      }
    });
  },

  // 开始检查订单状态
  startCheckingOrderStatus: function() {
    // 设置3秒轮询一次
    const interval = setInterval(() => {
      this.checkOrderStatus();
    }, 3000);

    this.setData({
      orderCheckInterval: interval
    });

    // 设置30秒超时
    setTimeout(() => {
      if (this.data.orderCheckInterval) {
        clearInterval(this.data.orderCheckInterval);
        this.setData({
          orderCheckInterval: null,
          loading: false
        });
        
        wx.showModal({
          title: '订单状态未知',
          content: '我们无法确认您的支付状态，请在"我的-试衣次数"中查看是否已增加，或稍后再试。',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
      }
    }, 30000);
  },

  // 检查订单状态
  checkOrderStatus: function() {
    if (!this.data.orderId) {
      console.error('订单ID不存在，无法检查订单状态');
      return;
    }
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/check_pay_order.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      data: {
        order_id: this.data.orderId
      },
      success: (res) => {
        console.log('检查订单状态响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          // 安全地获取订单数据
          const orderData = res.data.data || {};
          console.log('订单状态数据:', orderData);
          
          if (orderData.status === 'success') {
            // 订单支付成功
            clearInterval(this.data.orderCheckInterval);
            this.setData({
              orderCheckInterval: null,
              loading: false
            });
            
            // 获取购买的次数（默认为1）
            const count = orderData.count || 1;
            
            wx.showModal({
              title: '购买成功',
              content: `恭喜您已成功购买${count}次试衣机会`,
              showCancel: false,
              success: () => {
                wx.navigateBack();
              }
            });
          } else {
            console.log('订单尚未支付完成，状态:', orderData.status);
          }
        } else {
          console.error('检查订单状态失败:', res.data.msg || '未知错误');
        }
      },
      fail: (err) => {
        console.error('检查订单状态请求失败:', err);
      }
    });
  }
}); 