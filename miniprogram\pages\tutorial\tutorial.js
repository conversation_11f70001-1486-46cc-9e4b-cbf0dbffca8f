// pages/tutorial/tutorial.js
Page({
  data: {
    statusBarHeight: 20, // 默认状态栏高度
    currentSection: 0,   // 当前查看的部分
    sections: [
      '添加衣物', 
      '管理标签', 
      '衣橱分类',
      '创建穿搭',
      '分享穿搭',
      '穿搭分类管理',
      '虚拟试衣',
      '商家入驻'
    ]
  },

  onLoad: function () {
    this.getSystemInfo();
    
    // 确保导航菜单初始化正确
    wx.nextTick(() => {
      // 延迟执行，确保DOM已渲染
      setTimeout(() => {
        const query = wx.createSelectorQuery();
        query.select('.nav-scroll').boundingClientRect();
        query.selectAll('.nav-item').boundingClientRect();
        query.exec((res) => {
          if (res && res[0] && res[1] && res[1].length) {
            console.log('导航菜单初始化，共有项目:', res[1].length);
          }
        });
      }, 300);
    });
  },

  // 获取系统信息
  getSystemInfo: function () {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight
      });
      console.log('状态栏高度:', systemInfo.statusBarHeight);
    } catch (e) {
      console.error('获取系统信息失败:', e);
    }
  },

  // 返回上一页
  navigateBack: function () {
    wx.navigateBack({
      delta: 1,
      fail: function() {
        // 如果返回失败，则跳转到个人中心页面
        wx.switchTab({
          url: '/pages/profile/profile'
        });
      }
    });
  },
  
  // 跳转到指定部分 - 修改后的优化版本
  scrollToSection: function(e) {
    const sectionIndex = e.currentTarget.dataset.index;
    console.log('正在切换到部分:', sectionIndex, this.data.sections[sectionIndex]);
    
    // 先立即更新当前选中状态，确保UI响应
    this.setData({
      currentSection: sectionIndex
    });
    
    // 使用id选择器直接定位到对应section
    const query = wx.createSelectorQuery();
    const headerHeight = this.data.statusBarHeight + 44 + 40; // 状态栏 + 标题栏 + 导航栏高度
    
    query.select(`#section-${sectionIndex}`).boundingClientRect();
    query.selectViewport().scrollOffset();
    query.exec(res => {
      console.log('查询结果:', res);
      
      if(res[0]) {
        const section = res[0];
        const scrollTop = section.top + res[1].scrollTop - headerHeight;
        
        console.log('计算滚动位置:', {
          section: section,
          viewportScrollTop: res[1].scrollTop,
          headerHeight: headerHeight,
          finalScrollTop: scrollTop
        });
        
        wx.pageScrollTo({
          scrollTop: scrollTop,
          duration: 300
        });
      } else {
        console.error(`未找到id为section-${sectionIndex}的元素`);
      }
    });
  },
  
  // 修复后的页面滚动处理函数
  onPageScroll: function(e) {
    const scrollTop = e.scrollTop;
    const headerHeight = this.data.statusBarHeight + 44 + 40;
    
    // 使用selectAll选择所有section
    const query = wx.createSelectorQuery();
    query.selectAll('.section').boundingClientRect();
    query.selectViewport().scrollOffset();
    query.exec(res => {
      // 检查是否有结果返回
      if (!res || !res[0] || !res[0].length) return;
      
      const sections = res[0];
      
      // 找到当前可见的section
      let currentVisible = 0;
      
      // 从上到下检查每个section
      for (let i = 0; i < sections.length; i++) {
        // 计算section的顶部相对于视口顶部的位置
        const sectionTop = sections[i].top;
        
        // 如果section顶部在视口上方不远处，或在视口内，则认为它是当前可见section
        // 添加一个偏移量(50)使导航更自然
        if (sectionTop <= 50 + headerHeight) {
          currentVisible = i;
        } else {
          // 一旦找到第一个不可见的section，就跳出循环
          break;
        }
      }
      
      // 如果当前可见section与状态中的不同，则更新状态
      if (this.data.currentSection !== currentVisible) {
        console.log('滚动切换到部分:', currentVisible, this.data.sections[currentVisible]);
        this.setData({
          currentSection: currentVisible
        });
      }
    });
  },
  
  // 分享教程
  onShareAppMessage: function() {
    return {
      title: '次元衣帽间使用教程 - 轻松管理衣橱和创建穿搭',
      path: '/pages/tutorial/tutorial',
      imageUrl: '/images/tutorial_share.png' // 这里需要一张合适的分享图片
    };
  },
  
  // 分享到朋友圈
  onShareTimeline: function() {
    return {
      title: '次元衣帽间使用教程 - 轻松管理衣橱和创建穿搭',
      query: '',
      imageUrl: '/images/tutorial_share.png' // 这里需要一张合适的分享图片
    };
  }
}) 