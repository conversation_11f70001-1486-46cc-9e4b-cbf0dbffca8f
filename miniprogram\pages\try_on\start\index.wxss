/* 试穿进度弹框 */
.progress-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.progress-content {
  width: 85%;
  max-width: 650rpx;
  background-color: #fff;
  border-radius: 16px;
  padding: 25px 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.progress-title {
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

.progress-status-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.progress-status {
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

.progress-timer {
  font-size: 14px;
  color: #666;
}

.progress-bar-container {
  margin-bottom: 25px;
  display: flex;
  align-items: center;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 10px;
}

.progress-fill {
  height: 100%;
  background-color: #000;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-percent {
  font-size: 14px;
  color: #333;
  width: 40px;
  text-align: right;
}

.progress-stages {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stage {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.4;
  transition: opacity 0.3s ease;
}

.stage.active {
  opacity: 1;
}

.stage-icon {
  font-size: 20px;
  margin-bottom: 5px;
}

.stage-text {
  font-size: 12px;
  color: #666;
}

.progress-tip {
  text-align: center;
  font-size: 13px;
  color: #999;
  margin-top: 10px;
} 