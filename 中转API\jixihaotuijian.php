<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 日志函数
function logDebug($message, $data = null) {
    $log_file = __DIR__ . '/preference_recommendation_api_debug.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] {$message}";
    
    if ($data !== null) {
        $log_message .= ': ' . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    
    file_put_contents($log_file, $log_message . PHP_EOL, FILE_APPEND);
}

logDebug("接收到新的个人喜好推荐API请求", ['method' => $_SERVER['REQUEST_METHOD']]);

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    logDebug("OPTIONS预检请求，直接返回");
    exit;
}

// 检查请求数据
$json = file_get_contents('php://input');
if (empty($json)) {
    logDebug("错误: 缺少请求数据");
    echo json_encode(['error' => '缺少请求数据']);
    exit;
}

$data = json_decode($json, true);
if (!$data) {
    logDebug("错误: 无效的JSON数据", ['raw_input' => substr($json, 0, 1000)]);
    echo json_encode(['error' => '无效的JSON数据']);
    exit;
}

// 验证必要参数
if (!isset($data['user_clothes']) || !isset($data['preference'])) {
    logDebug("错误: 缺少必要参数", ['keys' => array_keys($data)]);
    echo json_encode(['error' => '缺少必要参数']);
    exit;
}

// 检查是否为"换一批"请求
$refresh = isset($data['refresh']) ? (int)$data['refresh'] : 0;
// 获取上次推荐的衣物ID列表（如果有）
$previous_outfit = isset($data['previous_outfit']) ? $data['previous_outfit'] : [];
// 获取随机因子
$random_factor = isset($data['random_factor']) ? $data['random_factor'] : time() . '_' . mt_rand(1000, 9999);
// 获取个人喜好
$preference = $data['preference'];

logDebug("请求参数", [
    'preference' => $preference,
    'refresh' => $refresh, 
    'has_previous_outfit' => !empty($previous_outfit),
    'random_factor' => $random_factor
]);

if ($refresh && !empty($previous_outfit)) {
    logDebug("上次推荐的衣物", $previous_outfit);
}

// 提取用户衣物信息
$user_clothes = $data['user_clothes'];
logDebug("接收到的用户衣物数量", ['count' => count($user_clothes)]);

// 配置Gemini API密钥
// 注意：实际部署时，应使用环境变量或安全配置存储API密钥
$apiKey = 'AIzaSyD1-g64EwoKNcvs0LeAn9hbyHJRuKj0Slg'; // 实际API密钥
$model = 'gemini-1.5-flash'; // 使用的模型名称

// 创建衣物ID映射表，按分类整理衣物
$clothes_by_category = [];
$available_clothes_by_category = [];

// 添加用户衣物到映射表
foreach ($user_clothes as $clothing) {
    if (!isset($clothes_by_category[$clothing['category']])) {
        $clothes_by_category[$clothing['category']] = [];
        $available_clothes_by_category[$clothing['category']] = [];
    }
    $clothes_by_category[$clothing['category']][] = $clothing;
    $available_clothes_by_category[$clothing['category']][$clothing['id']] = [
        'id' => $clothing['id'],
        'name' => $clothing['name'],
        'image_url' => isset($clothing['image_url']) ? $clothing['image_url'] : null
    ];
}

logDebug("按分类整理的衣物数量", array_map('count', $clothes_by_category));
logDebug("可用衣物ID映射表", array_keys($available_clothes_by_category));

// 构建提示词前，确定有效的衣物分类
$valid_categories = [];
foreach ($clothes_by_category as $category => $clothes) {
    if (count($clothes) > 0) {
        $valid_categories[$category] = count($clothes);
    }
}

logDebug("有效的衣物分类", $valid_categories);

// 构建提示词
$prompt = "你是一位专业的穿搭顾问，我想请你根据我的个人喜好和衣橱内容创建一套完整的穿搭搭配。\n\n";

// 添加个人喜好
$prompt .= "我的个人喜好：" . $preference . "\n\n";

// 如果是换一批请求，添加特殊指令
if ($refresh) {
    $prompt .= "【重要：这是一个'换一批'请求，请为同一个喜好生成一套全新的、与之前不同的穿搭组合。请确保选择不同的衣物和搭配风格。】\n\n";
    // 添加时间戳作为随机因子
    $prompt .= "当前时间戳: " . time() . "（请基于此生成不同的搭配）\n\n";
    $prompt .= "【非常重要】请务必选择与之前不同的衣物ID，这是用户明确要求的。每个类别都必须选择不同的衣物，不要重复之前的选择。\n\n";
    
    // 如果有上次的推荐结果，添加排除列表
    if (!empty($previous_outfit)) {
        $prompt .= "【排除列表】请不要选择以下衣物ID，这些是上次已经推荐过的：\n";
        foreach ($previous_outfit as $category => $item) {
            if (isset($item['id']) && $category != 'outfit_summary' && $category != 'is_refresh') {
                $prompt .= "- " . $category . ": ID " . $item['id'] . " (" . $item['name'] . ")\n";
            }
        }
        $prompt .= "\n请确保为每个类别选择与上面列表中不同的衣物ID。这是必须遵守的规则。\n\n";
    }
}

$prompt .= "我的衣橱中有以下分类的衣物，请仅从这些衣物中选择搭配：\n";

// 添加衣物信息，包含ID，只包含有效分类
foreach ($valid_categories as $category => $count) {
    $prompt .= "\n" . translateCategory($category) . "（共" . $count . "件）：\n";
    
    // 列出该分类的所有衣物，包含ID
    foreach ($clothes_by_category[$category] as $cloth) {
        $prompt .= "- ID:" . $cloth['id'] . " " . $cloth['name'];
        
        if (isset($cloth['description'])) {
            if (is_string($cloth['description'])) {
                $description = json_decode($cloth['description'], true);
                if ($description && isset($description['color'])) {
                    $prompt .= "（颜色：" . $description['color'] . "）";
                } else if ($description && isset($description['颜色'])) {
                    $prompt .= "（颜色：" . $description['颜色'] . "）";
                }
            } else if (isset($cloth['description']['颜色'])) {
                $prompt .= "（颜色：" . $cloth['description']['颜色'] . "）";
            } else if (isset($cloth['description']['color'])) {
                $prompt .= "（颜色：" . $cloth['description']['color'] . "）";
            }
        }
        
        if (!empty($cloth['tags'])) {
            $prompt .= "（标签：" . $cloth['tags'] . "）";
        }
        
        $prompt .= "\n";
    }
}

$prompt .= "\n请根据我的个人喜好（" . $preference . "），从我衣橱中搭配一套完整的穿搭。请确保搭配合适且时尚，并考虑颜色的协调性、季节适宜性和场合适宜性。";
$prompt .= "\n\n重要提示：";
$prompt .= "\n1. 请只推荐我实际拥有的衣物分类。";
$prompt .= "\n2. 请考虑季节因素，例如：夏季不需要推荐外套，冬季可能不需要凉鞋等。";
$prompt .= "\n3. 如果某个分类的衣物不适合当前搭配（比如季节不合适、风格不搭配等），请完全省略该分类，不要在JSON中包含该分类。";
$prompt .= "\n4. 不要返回空的或不需要的类别，即使是带有解释的也不要返回。例如，不要返回\"外套: {id: null, name: null, reason: '夏天不需要外套'}\"，而是完全省略外套类别。";
$prompt .= "\n5. 每次请生成一套全新的、创意的搭配组合，避免重复之前可能生成过的搭配。";
$prompt .= "\n6. 请尽量选择不同的衣物ID，增加穿搭的多样性。";
if ($refresh) {
    $prompt .= "\n7. 【重要】这是'换一批'请求，必须选择与之前不同的衣物ID。";
    $prompt .= "\n8. 【强制要求】每个类别都必须选择不同的衣物ID，不允许重复之前的推荐。";
}

$prompt .= "\n\n请以JSON格式返回结果，格式如下：";
$prompt .= "\n{";

// 动态生成JSON格式示例，只包含有效分类
$category_mapping = [
    'tops' => 'top',
    'pants' => 'bottom',
    'skirts' => 'bottom',
    'coats' => 'outerwear',
    'shoes' => 'shoes',
    'accessories' => 'accessories',
    'bags' => 'bag'
];

$included_categories = [];

// 添加有效分类
foreach ($valid_categories as $category => $count) {
    if (isset($category_mapping[$category])) {
        $outfit_category = $category_mapping[$category];
        if (!in_array($outfit_category, $included_categories)) {
            $prompt .= "\n  \"" . $outfit_category . "\": {\"id\": \"具体衣物ID\", \"name\": \"名称\", \"reason\": \"推荐理由\"},";
            $included_categories[] = $outfit_category;
        }
    }
}

$prompt .= "\n  \"outfit_summary\": \"整体穿搭风格描述和建议\"";
$prompt .= "\n}";

// 添加示例说明
$prompt .= "\n\n示例：";
$prompt .= "\n如果是夏季搭配，可能不需要外套，那么正确的返回应该是：";
$prompt .= "\n{";
$prompt .= "\n  \"top\": {\"id\": \"123\", \"name\": \"白色T恤\", \"reason\": \"清爽舒适，适合夏季\"},";
$prompt .= "\n  \"bottom\": {\"id\": \"456\", \"name\": \"牛仔短裤\", \"reason\": \"与T恤搭配清新自然\"},";
$prompt .= "\n  \"shoes\": {\"id\": \"789\", \"name\": \"帆布鞋\", \"reason\": \"休闲百搭\"},";
$prompt .= "\n  \"outfit_summary\": \"清新夏日休闲风格\"";
$prompt .= "\n}";
$prompt .= "\n注意上面的例子中完全省略了外套(outerwear)、配饰(accessories)和包包(bag)类别，因为它们不适合这个夏季搭配。";

$prompt .= "\n\n请只从我提供的衣物列表中进行选择，不要编造不存在的衣物或分类。";
$prompt .= "\n请确保返回的每个衣物都包含正确的ID，这是非常重要的。ID必须是我提供的衣物列表中的实际ID。";
$prompt .= "\n不要在返回结果中包含image_url字段，这会在后端处理。";

logDebug("构建的提示词", ['prompt_length' => strlen($prompt), 'prompt_excerpt' => substr($prompt, 0, 500)]);

// 调用Gemini API
$geminiUrl = "https://generativelanguage.googleapis.com/v1beta/models/" . $model . ":generateContent?key=" . $apiKey;

// 根据是否为"换一批"请求调整参数
$temperature = $refresh ? (0.9 + (mt_rand(0, 20) / 100)) : 0.7; // 换一批时增加随机性并添加小的随机波动
$topP = $refresh ? (0.95 + (mt_rand(0, 5) / 100)) : 0.8;
$topK = $refresh ? (40 + mt_rand(0, 20)) : 40;

// 使用随机因子来确保每次请求都不同
$seed = mt_rand(1, 2000000);
if (!empty($random_factor)) {
    // 从随机因子中提取数字部分作为种子的一部分
    preg_match('/(\d+)_(\d+)/', $random_factor, $matches);
    if (!empty($matches)) {
        $seed = $seed + intval($matches[1]) % 10000 + intval($matches[2]);
    } else {
        $seed = $seed + time() % 10000;
    }
}
logDebug("生成的随机种子", ['seed' => $seed]);

$request = [
    "contents" => [
        [
            "parts" => [
                [
                    "text" => $prompt
                ]
            ]
        ]
    ],
    "generationConfig" => [
        "temperature" => $temperature,
        "topP" => $topP,
        "topK" => $topK,
        "maxOutputTokens" => 1024,
        "stopSequences" => [],
        "candidateCount" => 1,
        // 添加随机种子，确保每次生成不同的结果
        "seed" => $seed
    ]
];

logDebug("准备调用Gemini API", [
    'url' => $geminiUrl, 
    'temperature' => $temperature, 
    'topP' => $topP, 
    'topK' => $topK,
    'refresh' => $refresh
]);

// 发送请求到Gemini API
$ch = curl_init($geminiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

$response = curl_exec($ch);
if (curl_errno($ch)) {
    $error = curl_error($ch);
    logDebug("调用Gemini API失败", ['curl_error' => $error]);
    echo json_encode(['error' => '调用Gemini API失败: ' . $error]);
    exit;
}
curl_close($ch);
logDebug("Gemini API调用完成", ['response_length' => strlen($response)]);

// 解析Gemini API响应
$result = json_decode($response, true);

// 验证响应是否有效
if (!$result || !isset($result['candidates'][0]['content']['parts'][0]['text'])) {
    logDebug("Gemini API响应无效", ['result' => $result]);
    echo json_encode(['error' => 'Gemini API响应无效']);
    exit;
}

// 提取Gemini生成的JSON内容
$generatedText = $result['candidates'][0]['content']['parts'][0]['text'];
logDebug("Gemini生成的文本", ['text_length' => strlen($generatedText), 'text_excerpt' => substr($generatedText, 0, 500)]);

// 从文本中提取JSON部分
preg_match('/\{.*\}/s', $generatedText, $matches);
if (empty($matches)) {
    logDebug("无法从Gemini响应中解析JSON", ['generated_text' => $generatedText]);
    echo json_encode(['error' => '无法从Gemini响应中解析JSON']);
    exit;
}

$outfitJson = $matches[0];
$outfit = json_decode($outfitJson, true);

if (!$outfit) {
    logDebug("解析推荐结果JSON失败", ['outfit_json' => $outfitJson]);
    echo json_encode(['error' => '解析推荐结果JSON失败']);
    exit;
}

logDebug("解析后的推荐结果结构", array_keys($outfit));

// 验证和修复衣物ID，添加图片URL
$categories_map = [
    'top' => 'tops',
    'bottom' => ['pants', 'skirts'],
    'outerwear' => 'coats',
    'shoes' => 'shoes',
    'accessories' => 'accessories',
    'bag' => 'bags'
];

// 记录排除的ID
$excluded_ids = [];
$all_excluded_ids = []; // 全局排除列表，记录所有已推荐过的衣物ID

if ($refresh && !empty($previous_outfit)) {
    foreach ($previous_outfit as $category => $item) {
        if (isset($item['id']) && $category != 'outfit_summary' && $category != 'is_refresh') {
            $excluded_ids[$category] = $item['id'];
            $all_excluded_ids[] = $item['id']; // 添加到全局排除列表
        }
    }
    logDebug("排除的ID列表", ['by_category' => $excluded_ids, 'all' => $all_excluded_ids]);
}

// 记录已处理的类别和是否有衣物被替换
$processed_categories = [];
$has_replaced_clothing = false;

foreach ($categories_map as $outfit_category => $db_categories) {
    if (isset($outfit[$outfit_category])) {
        logDebug("处理类别", ['category' => $outfit_category]);
        $processed_categories[] = $outfit_category;
        
        // 检查ID是否存在
        if (!isset($outfit[$outfit_category]['id'])) {
            logDebug("缺少ID", ['category' => $outfit_category]);
            continue;
        }
        
        $item_id = $outfit[$outfit_category]['id'];
        
        // 检查是否与上次推荐的ID相同
        if ($refresh && (
            (isset($excluded_ids[$outfit_category]) && $excluded_ids[$outfit_category] == $item_id) || 
            in_array($item_id, $all_excluded_ids)
        )) {
            logDebug("检测到重复推荐的ID", ['category' => $outfit_category, 'id' => $item_id]);
            // 强制选择不同的衣物
            $force_different = true;
        } else {
            $force_different = false;
        }
        
        // 如果是换一批请求，总是强制选择不同的衣物
        if ($refresh) {
            $force_different = true;
            logDebug("换一批请求，强制选择不同的衣物", ['category' => $outfit_category]);
        }
        
        $found = false;
        $db_category = '';
        
        // 如果db_categories是数组，检查多个可能的分类
        if (is_array($db_categories)) {
            foreach ($db_categories as $possible_category) {
                if (isset($available_clothes_by_category[$possible_category]) && 
                    isset($available_clothes_by_category[$possible_category][$item_id])) {
                    $found = true;
                    $db_category = $possible_category;
                    break;
                }
            }
        } 
        // 否则检查单个分类
        else {
            $db_category = $db_categories;
            if (isset($available_clothes_by_category[$db_category]) && 
                isset($available_clothes_by_category[$db_category][$item_id])) {
                $found = true;
            }
        }
        
        if ($found && !$force_different) {
            // 添加衣物的完整信息
            $outfit[$outfit_category]['category'] = $db_category;
            $outfit[$outfit_category]['image_url'] = $available_clothes_by_category[$db_category][$item_id]['image_url'];
            logDebug("找到匹配的衣物", [
                'category' => $outfit_category, 
                'id' => $item_id, 
                'db_category' => $db_category,
                'image_url' => $available_clothes_by_category[$db_category][$item_id]['image_url']
            ]);
        } else {
            // 如果找不到ID或需要强制选择不同的衣物，从该分类中选择一个随机衣物
            $reason = $force_different ? "需要选择不同的衣物" : "未找到匹配的衣物ID";
            logDebug($reason, ['category' => $outfit_category, 'id' => $item_id]);
            
            if (is_array($db_categories)) {
                foreach ($db_categories as $possible_category) {
                    if (isset($available_clothes_by_category[$possible_category]) && 
                        !empty($available_clothes_by_category[$possible_category])) {
                        
                        // 获取可用的衣物ID列表
                        $available_ids = array_keys($available_clothes_by_category[$possible_category]);
                        
                        // 如果是换一批请求，排除上次推荐的ID
                        if ($refresh && isset($excluded_ids[$outfit_category])) {
                            $available_ids = array_filter($available_ids, function($id) use ($excluded_ids, $outfit_category) {
                                return $id != $excluded_ids[$outfit_category];
                            });
                            
                            // 如果过滤后没有可用ID，则使用所有ID
                            if (empty($available_ids)) {
                                $available_ids = array_keys($available_clothes_by_category[$possible_category]);
                                logDebug("没有可用的不同衣物，使用所有ID", ['category' => $outfit_category]);
                            }
                        }
                        
                        // 随机选择一个ID
                        if (!empty($available_ids)) {
                            $random_index = array_rand($available_ids);
                            $random_id = $available_ids[$random_index];
                            
                            // 保存原始推荐理由，以便稍后生成新的理由
                            $original_reason = isset($outfit[$outfit_category]['reason']) ? $outfit[$outfit_category]['reason'] : '';
                            
                            $outfit[$outfit_category]['id'] = $random_id;
                            $outfit[$outfit_category]['name'] = $available_clothes_by_category[$possible_category][$random_id]['name'];
                            $outfit[$outfit_category]['category'] = $possible_category;
                            $outfit[$outfit_category]['image_url'] = $available_clothes_by_category[$possible_category][$random_id]['image_url'];
                            
                            // 标记有衣物被替换
                            $has_replaced_clothing = true;
                            
                            $found = true;
                            $db_category = $possible_category;
                            logDebug("随机选择了替代衣物", [
                                'category' => $outfit_category, 
                                'id' => $random_id, 
                                'db_category' => $db_category,
                                'is_different' => $force_different,
                                'original_reason' => $original_reason
                            ]);
                            break;
                        }
                    }
                }
            } else if (isset($available_clothes_by_category[$db_category]) && 
                      !empty($available_clothes_by_category[$db_category])) {
                
                // 获取可用的衣物ID列表
                $available_ids = array_keys($available_clothes_by_category[$db_category]);
                
                // 如果是换一批请求，排除上次推荐的ID
                if ($refresh && isset($excluded_ids[$outfit_category])) {
                    $available_ids = array_filter($available_ids, function($id) use ($excluded_ids, $outfit_category) {
                        return $id != $excluded_ids[$outfit_category];
                    });
                    
                    // 如果过滤后没有可用ID，则使用所有ID
                    if (empty($available_ids)) {
                        $available_ids = array_keys($available_clothes_by_category[$db_category]);
                        logDebug("没有可用的不同衣物，使用所有ID", ['category' => $outfit_category]);
                    }
                }
                
                // 随机选择一个ID
                if (!empty($available_ids)) {
                    $random_index = array_rand($available_ids);
                    $random_id = $available_ids[$random_index];
                    
                    // 保存原始推荐理由，以便稍后生成新的理由
                    $original_reason = isset($outfit[$outfit_category]['reason']) ? $outfit[$outfit_category]['reason'] : '';
                    
                    $outfit[$outfit_category]['id'] = $random_id;
                    $outfit[$outfit_category]['name'] = $available_clothes_by_category[$db_category][$random_id]['name'];
                    $outfit[$outfit_category]['category'] = $db_category;
                    $outfit[$outfit_category]['image_url'] = $available_clothes_by_category[$db_category][$random_id]['image_url'];
                    
                    // 标记有衣物被替换
                    $has_replaced_clothing = true;
                    
                    $found = true;
                    logDebug("随机选择了替代衣物", [
                        'category' => $outfit_category, 
                        'id' => $random_id, 
                        'db_category' => $db_category,
                        'is_different' => $force_different,
                        'original_reason' => $original_reason
                    ]);
                }
            }
            
            if (!$found) {
                // 如果仍然找不到，移除该类别
                unset($outfit[$outfit_category]);
                logDebug("移除了无法匹配的类别", ['category' => $outfit_category]);
            }
        }
    }
}

// 如果有衣物被替换，为所有替换的衣物生成新的推荐理由，并更新穿搭总结
if ($has_replaced_clothing) {
    logDebug("检测到衣物替换，为替换的衣物生成新的推荐理由并更新穿搭总结");
    
    // 为每个替换的衣物生成新的推荐理由
    foreach ($outfit as $category => $item) {
        // 跳过非衣物类别
        if ($category === 'outfit_summary' || $category === 'is_refresh' || $category === 'weather_data') {
            continue;
        }
        
        // 生成新的推荐理由
        $new_reason = generateReasonWithGemini($item['name'], $category, $preference);
        if ($new_reason) {
            $outfit[$category]['reason'] = $new_reason;
            logDebug("为替换的衣物生成了新的推荐理由", [
                'category' => $category,
                'name' => $item['name'],
                'new_reason' => $new_reason
            ]);
        }
    }
    
    // 更新穿搭总结
    $new_summary = updateOutfitSummary($outfit, $preference);
    if ($new_summary) {
        $outfit['outfit_summary'] = $new_summary;
        logDebug("更新了穿搭总结", ['new_summary' => $new_summary]);
    }
}

// 如果是"换一批"请求，添加标记
if ($refresh) {
    $outfit['is_refresh'] = true;
}

logDebug("最终返回的推荐结果", ['structure' => array_keys($outfit)]);

// 返回最终结果
echo json_encode($outfit);
logDebug("API请求处理完成");

/**
 * 使用Gemini API为单个衣物生成推荐理由
 * @param string $clothing_name 衣物名称
 * @param string $category 衣物类别
 * @param string $preference 个人喜好
 * @return string 推荐理由，如果生成失败则返回null
 */
function generateReasonWithGemini($clothing_name, $category, $preference) {
    global $apiKey, $model;
    
    // 构建提示词
    $prompt = "你是一位专业的穿搭顾问。请为以下衣物生成一段丰富、专业的推荐理由，解释为什么这件衣物适合用户的个人喜好。\n\n";
    $prompt .= "衣物: " . $clothing_name . "\n";
    $prompt .= "类别: " . translateCategoryToChineseForPrompt($category) . "\n";
    $prompt .= "用户喜好: " . $preference . "\n";
    
    $prompt .= "\n请生成一段详细的推荐理由，包括衣物的特点、材质、款式、颜色如何符合用户的喜好，以及如何与整体穿搭搭配。不要使用通用模板，而是针对这件特定衣物生成独特的理由。理由应该有30-50个汉字。\n";
    $prompt .= "只返回推荐理由文本，不要包含任何其他内容。";
    
    // 调用Gemini API
    $geminiUrl = "https://generativelanguage.googleapis.com/v1beta/models/" . $model . ":generateContent?key=" . $apiKey;
    
    $request = [
        "contents" => [
            [
                "parts" => [
                    [
                        "text" => $prompt
                    ]
                ]
            ]
        ],
        "generationConfig" => [
            "temperature" => 0.7,
            "topP" => 0.8,
            "topK" => 40,
            "maxOutputTokens" => 100,
            "stopSequences" => []
        ]
    ];
    
    logDebug("为单个衣物生成推荐理由", [
        'clothing_name' => $clothing_name,
        'category' => $category,
        'prompt_length' => strlen($prompt)
    ]);
    
    try {
        // 发送请求到Gemini API
        $ch = curl_init($geminiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 设置10秒超时
        
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            logDebug("调用Gemini API失败", ['curl_error' => $error]);
            curl_close($ch);
            return null;
        }
        curl_close($ch);
        
        // 解析Gemini API响应
        $result = json_decode($response, true);
        
        // 验证响应是否有效
        if (!$result || !isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            logDebug("Gemini API响应无效", ['result' => $result]);
            return null;
        }
        
        // 提取Gemini生成的文本
        $generatedText = $result['candidates'][0]['content']['parts'][0]['text'];
        logDebug("Gemini生成的推荐理由", ['text' => $generatedText]);
        
        return $generatedText;
    } catch (Exception $e) {
        logDebug("生成推荐理由时发生异常", ['exception' => $e->getMessage()]);
        return null;
    }
}

/**
 * 使用Gemini API更新穿搭总结
 * @param array $outfit 穿搭数据
 * @param string $preference 个人喜好
 * @return string 更新后的穿搭总结，如果生成失败则返回null
 */
function updateOutfitSummary($outfit, $preference) {
    global $apiKey, $model;
    
    // 构建提示词
    $prompt = "你是一位专业的穿搭顾问。请为以下穿搭组合生成一段整体的穿搭总结，解释这套穿搭如何符合用户的个人喜好。\n\n";
    $prompt .= "用户喜好: " . $preference . "\n";
    
    $prompt .= "\n穿搭组合:\n";
    
    // 添加每件衣物的信息
    foreach ($outfit as $category => $item) {
        // 跳过非衣物类别
        if ($category === 'outfit_summary' || $category === 'is_refresh' || $category === 'weather_data') {
            continue;
        }
        
        $prompt .= translateCategoryToChineseForPrompt($category) . ": " . $item['name'] . "\n";
    }
    
    $prompt .= "\n请生成一段详细的穿搭总结，描述这套穿搭的整体风格、颜色搭配、适合场合，以及如何符合用户的个人喜好。总结应该有80-120个汉字。\n";
    $prompt .= "只返回穿搭总结文本，不要包含任何其他内容。";
    
    // 调用Gemini API
    $geminiUrl = "https://generativelanguage.googleapis.com/v1beta/models/" . $model . ":generateContent?key=" . $apiKey;
    
    $request = [
        "contents" => [
            [
                "parts" => [
                    [
                        "text" => $prompt
                    ]
                ]
            ]
        ],
        "generationConfig" => [
            "temperature" => 0.7,
            "topP" => 0.8,
            "topK" => 40,
            "maxOutputTokens" => 200,
            "stopSequences" => []
        ]
    ];
    
    logDebug("生成穿搭总结", ['prompt_length' => strlen($prompt)]);
    
    try {
        // 发送请求到Gemini API
        $ch = curl_init($geminiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 设置10秒超时
        
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            logDebug("调用Gemini API失败", ['curl_error' => $error]);
            curl_close($ch);
            return null;
        }
        curl_close($ch);
        
        // 解析Gemini API响应
        $result = json_decode($response, true);
        
        // 验证响应是否有效
        if (!$result || !isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            logDebug("Gemini API响应无效", ['result' => $result]);
            return null;
        }
        
        // 提取Gemini生成的文本
        $generatedText = $result['candidates'][0]['content']['parts'][0]['text'];
        logDebug("Gemini生成的穿搭总结", ['text' => $generatedText]);
        
        return $generatedText;
    } catch (Exception $e) {
        logDebug("生成穿搭总结时发生异常", ['exception' => $e->getMessage()]);
        return null;
    }
}

/**
 * 将分类英文名转换为中文名（用于提示词）
 */
function translateCategoryToChineseForPrompt($category) {
    $categoryMap = [
        'top' => '上衣',
        'bottom' => '下装',
        'outerwear' => '外套',
        'shoes' => '鞋子',
        'accessories' => '配饰',
        'bag' => '包包'
    ];
    
    return isset($categoryMap[$category]) ? $categoryMap[$category] : $category;
}

// 辅助函数：将分类英文名转换为中文名
function translateCategory($category) {
    $categoryMap = [
        'tops' => '上衣',
        'pants' => '裤子',
        'skirts' => '裙子',
        'coats' => '外套',
        'shoes' => '鞋子',
        'bags' => '包包',
        'accessories' => '配饰'
    ];
    
    return isset($categoryMap[$category]) ? $categoryMap[$category] : $category;
}
?> 