<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入配置和辅助函数
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// 验证用户Token
$auth = new Auth();
$token = null;

// 从请求头或查询参数中获取token
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 移除可能存在的Bearer前缀
    $token = str_replace('Bearer ', '', $token);
} elseif (isset($_GET['token'])) {
    $token = $_GET['token'];
}

if (!$token) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'No token provided']);
    exit;
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Invalid or expired token']);
    exit;
}

$user_id = $payload['sub'];

try {
    // 获取数据库连接
    $db = new Database();
    $pdo = $db->getConnection();
    
    // 获取分页参数
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $per_page = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 10;
    $offset = ($page - 1) * $per_page;
    
    // 获取用户穿搭分类列表，按is_default降序，sort_order升序，name升序排序
    $stmt = $pdo->prepare("
        SELECT c.id, c.name, c.description, c.sort_order, c.is_default, c.created_at, c.updated_at
        FROM outfit_categories c
        WHERE c.user_id = :user_id
        ORDER BY c.is_default DESC, c.sort_order ASC, c.name ASC
        LIMIT :limit OFFSET :offset
    ");
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->bindParam(':limit', $per_page, PDO::PARAM_INT);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取每个分类的穿搭数量
    foreach ($categories as &$category) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as outfit_count 
            FROM outfits 
            WHERE category_id = :category_id AND user_id = :user_id
        ");
        $stmt->bindParam(':category_id', $category['id'], PDO::PARAM_INT);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $category['outfit_count'] = (int)$result['outfit_count'];
    }
    
    // 获取总分类数量（用于分页）
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM outfit_categories WHERE user_id = :user_id");
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    $total = (int)$stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 计算总页数
    $total_pages = ceil($total / $per_page);
    
    // 返回成功响应
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'data' => $categories,
        'pagination' => [
            'total' => $total,
            'per_page' => $per_page,
            'current_page' => $page,
            'total_pages' => $total_pages
        ]
    ]);
    
} catch (PDOException $e) {
    error_log("Database error in get_outfit_categories.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database error', 'message' => 'An error occurred while getting outfit categories']);
} catch (Exception $e) {
    error_log("General error in get_outfit_categories.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Server error', 'message' => 'An unexpected error occurred']);
} 