<?php
header("Content-Type: application/json");
require_once './db.php';
require_once './auth.php';
require_once './config.php';

// 初始化响应数组
$response = [
    'code' => 0,
    'message' => 'success',
    'data' => null
];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['code'] = 405;
    $response['message'] = 'Method Not Allowed';
    echo json_encode($response);
    exit;
}

// 获取POST参数
$postData = json_decode(file_get_contents("php://input"), true);
$token = isset($postData['token']) ? $postData['token'] : '';
$action = isset($postData['action']) ? $postData['action'] : '';

if (empty($token) || empty($action) || !in_array($action, ['join', 'exit'])) {
    $response['code'] = 400;
    $response['message'] = 'Missing required parameters or invalid action';
    echo json_encode($response);
    exit;
}

// 验证用户token
$auth = new Auth();
$verifyResult = $auth->verifyToken($token);

if ($verifyResult === false) {
    $response['code'] = 401;
    $response['message'] = '无效或已过期的令牌';
    echo json_encode($response);
    exit;
}

$userId = $verifyResult['sub']; // 从验证结果中获取用户ID
$db = new Database();
$conn = $db->getConnection();

try {
    // 准备SQL语句
    if ($action === 'join') {
        $merchantStatus = 'yes';
        $statusMessage = '成功入驻商家';
    } else {
        $merchantStatus = 'no';
        $statusMessage = '成功退出商家入驻';
    }
    
    $stmt = $conn->prepare("UPDATE users SET merchant_status = :merchant_status, updated_at = NOW() WHERE id = :user_id");
    $stmt->bindParam(':merchant_status', $merchantStatus, PDO::PARAM_STR);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    
    if ($stmt->execute()) {
        // 如果退出商家状态，同时关闭共享试穿点数
        if ($action === 'exit') {
            $stmt = $conn->prepare("UPDATE users SET share_try_on_credits = 0 WHERE id = :user_id");
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $stmt->execute();
        }
        
        $response['data'] = [
            'merchant_status' => $merchantStatus
        ];
        $response['message'] = $statusMessage;
    } else {
        $response['code'] = 500;
        $response['message'] = '更新商家状态失败';
    }
} catch (Exception $e) {
    $response['code'] = 500;
    $response['message'] = '处理请求时发生错误: ' . $e->getMessage();
} finally {
    // PDO connections are closed automatically when the variable is unset
    $conn = null;
}

echo json_encode($response); 