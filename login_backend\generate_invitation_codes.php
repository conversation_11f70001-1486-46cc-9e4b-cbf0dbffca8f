<?php
/**
 * 生成邀请码API
 */
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

// 获取Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '需要授权']);
    exit;
}

// 验证token
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或过期的令牌']);
    exit;
}

// 获取管理员ID
$adminId = $payload['admin_id'];

// 检查管理员是否存在
$db = new Database();
$conn = $db->getConnection();
$adminStmt = $conn->prepare("SELECT id, username FROM admin_users WHERE id = :id");
$adminStmt->bindParam(':id', $adminId);
$adminStmt->execute();
$admin = $adminStmt->fetch(PDO::FETCH_ASSOC);

if (!$admin) {
    http_response_code(403);
    echo json_encode(['error' => true, 'msg' => '权限不足']);
    exit;
}

// 获取请求数据
$rawData = file_get_contents('php://input');
$requestData = json_decode($rawData, true);

// 验证参数
if (!isset($requestData['quantity']) || !is_numeric($requestData['quantity'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '无效的数量']);
    exit;
}

$quantity = (int)$requestData['quantity'];
if ($quantity < 1 || $quantity > 100) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '数量必须在1-100之间']);
    exit;
}

$expireDate = isset($requestData['expire_date']) && !empty($requestData['expire_date']) ? $requestData['expire_date'] : null;
$type = isset($requestData['type']) && !empty($requestData['type']) ? $requestData['type'] : 'image_analysis';

try {
    // 开始事务
    $conn->beginTransaction();
    
    // 插入语句
    $sql = "
        INSERT INTO invitation_codes
        (code, status, type, created_by, created_at, expired_at)
        VALUES
        (:code, 'unused', :type, :created_by, NOW(), :expired_at)
    ";
    
    $stmt = $conn->prepare($sql);
    
    // 生成指定数量的邀请码
    $generatedCodes = [];
    $generatedCount = 0;
    
    for ($i = 0; $i < $quantity; $i++) {
        // 生成唯一邀请码
        $code = generateUniqueCode($conn, 8);
        if (!$code) {
            continue; // 跳过生成失败的邀请码
        }
        
        $generatedCodes[] = $code;
        
        // 绑定参数
        $stmt->bindParam(':code', $code);
        $stmt->bindParam(':type', $type);
        $stmt->bindParam(':created_by', $admin['id'], PDO::PARAM_INT);
        $stmt->bindParam(':expired_at', $expireDate);
        
        // 执行插入
        $stmt->execute();
        $generatedCount++;
    }
    
    // 提交事务
    $conn->commit();
    
    // 返回结果
    echo json_encode([
        'error' => false,
        'msg' => '邀请码生成成功',
        'data' => [
            'generated' => $generatedCount,
            'codes' => $generatedCodes
        ]
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '生成邀请码失败: ' . $e->getMessage()]);
}

/**
 * 生成唯一邀请码
 * @param PDO $conn 数据库连接
 * @param int $length 邀请码长度
 * @param int $maxAttempts 最大尝试次数
 * @return string|bool 成功返回邀请码，失败返回false
 */
function generateUniqueCode($conn, $length = 8, $maxAttempts = 10) {
    $attempts = 0;
    
    while ($attempts < $maxAttempts) {
        // 生成随机字符串
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $code = '';
        
        for ($i = 0; $i < $length; $i++) {
            $code .= $chars[random_int(0, strlen($chars) - 1)];
        }
        
        // 检查是否已存在
        $checkSql = "SELECT COUNT(*) AS count FROM invitation_codes WHERE code = :code";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bindParam(':code', $code);
        $checkStmt->execute();
        $result = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        // 如果不存在，返回该邀请码
        if ($result['count'] == 0) {
            return $code;
        }
        
        $attempts++;
    }
    
    return false;
} 