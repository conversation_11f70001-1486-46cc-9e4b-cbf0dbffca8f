<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>照片编辑 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* 编辑页面样式 */
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-hint {
            font-size: 12px;
            color: #888;
            margin-top: 4px;
        }
        
        .required {
            color: #f5222d;
            margin-left: 2px;
        }
        
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }
        
        .col-md-6 {
            flex: 0 0 50%;
            padding: 0 10px;
            box-sizing: border-box;
        }
        
        @media (max-width: 768px) {
            .col-md-6 {
                flex: 0 0 100%;
            }
        }
        
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 4px;
            border: none;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-container {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn-cancel {
            background-color: #f7f7f7;
            color: #666;
        }
        
        .btn-cancel:hover {
            background-color: #e6e6e6;
        }
        
        .btn-submit {
            background-color: #1890ff;
            color: white;
        }
        
        .btn-submit:hover {
            background-color: #40a9ff;
        }
        
        .image-preview {
            width: 300px;
            height: 300px;
            border: 1px dashed #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            position: relative;
            overflow: hidden;
        }
        
        .image-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
        }
        
        .image-preview-text {
            color: #999;
            font-size: 14px;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            color: #1890ff;
            margin-bottom: 15px;
            text-decoration: none;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        .page-title {
            font-size: 20px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>照片编辑</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="card">
                <a href="photo_list.html" class="back-link">
                    &lt; 返回照片列表
                </a>
                
                <h3 class="page-title" id="pageTitle">添加照片</h3>
                
                <form id="photoForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">照片预览</label>
                                <div class="image-preview" id="imagePreview">
                                    <span class="image-preview-text">预览图片</span>
                                </div>
                                <input type="text" class="form-control" id="imageUrl" name="image_url" placeholder="输入图片URL">
                                <div class="form-hint">输入图片URL地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">照片类型 <span class="required">*</span></label>
                                <select class="form-control" id="type" name="type" required>
                                    <option value="full">全身照</option>
                                    <option value="half">半身照</option>
                                    <option value="other">其他</option>
                                </select>
                                <div class="form-hint">选择照片类型</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">所属用户 <span class="required">*</span></label>
                                <select class="form-control" id="userId" name="user_id" required>
                                    <option value="">正在加载用户列表...</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">描述</label>
                                <textarea class="form-control" id="description" name="description" placeholder="描述这张照片"></textarea>
                                <div class="form-hint">添加照片描述，帮助后续识别和管理</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="btn-container">
                        <button type="button" class="btn btn-cancel" onclick="window.location.href='photo_list.html'">取消</button>
                        <button type="submit" class="btn btn-submit">保存照片</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/photo_edit.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为photo
            Sidebar.init('photo');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化照片编辑页面
            if (typeof PhotoEdit !== 'undefined') {
                PhotoEdit.init();
            }
        });
    </script>
</body>
</html> 