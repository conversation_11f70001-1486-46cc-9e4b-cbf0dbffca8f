<?php
/**
 * TOP API: taobao.top.ipout.get request
 * 
 * <AUTHOR> create
 * @since 1.0, 2023.07.25
 */
class TopIpoutGetRequest
{
	
	private $apiParas = array();
	
	public function getApiMethodName()
	{
		return "taobao.top.ipout.get";
	}
	
	public function getApiParas()
	{
		return $this->apiParas;
	}
	
	public function check()
	{
		
	}
	
	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
