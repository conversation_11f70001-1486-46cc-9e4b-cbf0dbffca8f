<view class="container">


  <view class="content">
    <block wx:if="{{analysisRecords.length > 0}}">
      <view class="analysis-list">
        <view class="analysis-item" wx:for="{{analysisRecords}}" wx:key="id">
          <view class="analysis-card" bindtap="viewDetail" data-id="{{item.id}}">
            <view class="analysis-info">
              <view class="analysis-date">{{item.created_at}}</view>
              <view class="analysis-status-container">
                <view class="analysis-status {{item.status}}">
                  <text class="status-text">{{statusText[item.status]}}</text>
                </view>
                <!-- 删除按钮，使用catchtap阻止事件冒泡，避免触发viewDetail -->
                <view class="delete-btn" catchtap="showDeleteConfirm" data-id="{{item.id}}">
                  <view class="delete-icon">×</view>
                </view>
              </view>
            </view>
            <view class="analysis-divider"></view>
            <view class="analysis-action">
              <view class="action-text">查看详情</view>
              <view class="action-icon">〉</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view class="loading-more" wx:if="{{isLoading && hasMore}}">
        <view class="loading-text">加载中...</view>
      </view>
      
      <!-- 手动加载更多按钮 -->
      <view class="load-more-btn-container" wx:if="{{hasMore && !isLoading}}">
        <button class="load-more-btn" bindtap="loadMore">加载更多</button>
      </view>
      
      <!-- 没有更多数据提示 -->
      <view class="no-more" wx:if="{{!hasMore && analysisRecords.length > 0}}">
        <view class="no-more-text">没有更多数据了</view>
      </view>
    </block>
    
    <view class="empty-state" wx:else>
      <view class="empty-icon">📋</view>
      <view class="empty-text">暂无形象分析记录</view>
      <button class="start-btn" bindtap="goToAnalysis">开始分析</button>
    </view>
  </view>
</view> 