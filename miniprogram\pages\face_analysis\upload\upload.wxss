/* pages/face_analysis/upload/upload.wxss */

/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 内容样式 */
.content {
  flex: 1;
  padding: 30rpx;
  padding-top: 20rpx;
}

.upload-section, .style-section {
  margin-bottom: 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.upload-tips {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

/* 照片上传区域 */
.photo-uploader {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.photo-upload-item {
  width: 100%;
}

.photo-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.required-mark {
  color: #ff4d4f;
  margin-right: 4rpx;
}

.photo-upload-area {
  position: relative;
  width: 100%;
  height: 360rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  background-color: #fafafa;
  overflow: hidden;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-size: 28rpx;
}

.upload-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  background-image: url('data:image/svg+xml;base64,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');
  background-size: cover;
}

.photo-preview {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon::before,
.delete-icon::after {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 4rpx;
  background-color: #fff;
}

.delete-icon::before {
  transform: rotate(45deg);
}

.delete-icon::after {
  transform: rotate(-45deg);
}

/* 风格偏好输入区域 */
.section-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.style-input {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

/* 拍摄小技巧区域 */
.tips-section {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tips-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
}

.tip-num {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-right: 10rpx;
}

.tip-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 上传进度条 */
.progress-bar {
  width: 100%;
  height: 10rpx;
  background-color: #f0f0f0;
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  background: linear-gradient(90deg, #1aad19, #39d038);
  transition: width 0.3s ease;
}

/* 底部按钮区域 */
.footer {
  padding: 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #000;
  color: #fff;
  font-size: 32rpx;
  border-radius: 8rpx;
  text-align: center;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

.submit-btn[disabled] {
  background-color: #ddd;
  color: #999;
} 