# 和风天气图标集成指南

## 问题背景

当前应用使用和风天气的图片URL（`https://a.hecdn.net/img/common/icon/202007/[图标代码].png`）加载天气图标，但遇到403错误，原因是和风天气限制了直接图片访问。我们需要改用官方推荐的图标字体方案。

## 解决方案

我们已经更新了后端API，现在同时返回图片URL和字体类名，用于渐进式升级。我们强烈建议使用字体图标方式，因为：

1. 通过CDN加载，不受域名授权限制
2. 支持调整大小和颜色
3. 减少HTTP请求，提高加载速度
4. 官方推荐的接入方式

## 后端API变更

现在的`get_weather.php`返回的数据结构增加了以下字段：

```json
{
  "success": true,
  "data": {
    "temp": "24",
    "text": "多云",
    "icon": "101",
    "icon_url": "https://a.hecdn.net/img/common/icon/202007/101.png", // 旧版图片URL (保留，但可能无法访问)
    "icon_font_class": "qi-101", // 新增：对应的图标字体类名
    "icon_css_url": "https://cdn.jsdelivr.net/npm/qweather-icons@1.6.0/font/qweather-icons.css", // 新增：图标CSS地址
    "icon_use_font": true, // 新增：是否使用字体图标的标志
    "windDir": "东南风",
    "windScale": "1",
    "humidity": "72",
    "updateTime": "2025-05-17T17:21:53+08:00"
  }
}
```

## 微信小程序集成步骤

### 1. 下载图标字体文件

从 [GitHub](https://github.com/qwd/qweather-icons/tree/master/font) 或 [NPM](https://www.npmjs.com/package/qweather-icons) 下载字体文件：

- qweather-icons.css
- qweather-icons.woff2
- qweather-icons.woff
- qweather-icons.ttf

### 2. 添加到项目

将下载的文件添加到小程序项目的静态资源目录中，例如：`/static/fonts/`

### 3. 创建WXSS文件

创建 `qweather-icons.wxss`，替换字体文件路径为本地路径：

```css
@font-face {
  font-family: "qweather-icons";
  src: url("./fonts/qweather-icons.woff2") format("woff2"),
       url("./fonts/qweather-icons.woff") format("woff"),
       url("./fonts/qweather-icons.ttf") format("truetype");
}

.qi {
  font-family: "qweather-icons" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 这里是图标代码映射，从原CSS复制 */
.qi-100:before { content: "\e63f"; } /* 晴 */
.qi-101:before { content: "\e63e"; } /* 多云 */
.qi-102:before { content: "\e639"; } /* 少云 */
/* ... 其他图标代码 ... */
```

### 4. 在app.wxss中引入

```css
@import "./static/qweather-icons.wxss";
```

### 5. 在页面中使用

修改显示天气图标的WXML代码：

#### 旧代码（图片方式）：

```html
<image src="{{weatherData.icon_url}}" class="weather-icon"></image>
```

#### 新代码（字体方式）：

```html
<text class="weather-icon {{weatherData.icon_font_class}}"></text>
```

### 6. 处理兼容性

为确保平滑过渡，可以添加条件判断：

```html
<block wx:if="{{weatherData.icon_use_font}}">
  <text class="weather-icon {{weatherData.icon_font_class}}"></text>
</block>
<block wx:else>
  <image src="{{weatherData.icon_url}}" class="weather-icon"></image>
</block>
```

## H5端集成步骤

对于H5端，直接引用CDN链接即可：

```html
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/qweather-icons@1.6.0/font/qweather-icons.css">
```

然后使用字体图标：

```html
<i class="{{weatherData.icon_font_class}}"></i>
```

## 测试页面

我们提供了一个测试页面展示所有常用图标，并验证API返回的数据：

访问：`/login_backend/weather_icon_test.html`

## 常见问题

1. **问题**: 图标显示为方块或不显示
   **解决**: 确认字体文件路径正确，且已正确引入wxss文件

2. **问题**: 使用CDN方式在小程序中无法显示
   **解决**: 小程序不支持外部字体文件，必须下载并本地引用

3. **问题**: 历史版本兼容问题
   **解决**: 使用条件判断，同时支持图片和字体图标两种方式

## 完整图标列表

完整的图标代码与图标对应关系，请参考：
[和风天气图标代码文档](https://dev.qweather.com/docs/resource/icons/)

## 技术支持

如有问题，请联系技术支持团队。 