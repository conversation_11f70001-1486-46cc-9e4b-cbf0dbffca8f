<view class="container">
  <!-- 页面头部 -->
  <view class="section">
    <view class="text-center">
      <text class="text-xl text-bold">穿搭次数排行榜</text>
    </view>
    <view class="text-center mt-10">
      <text class="text-secondary text-sm">根据穿搭次数从高到低排列</text>
    </view>
  </view>

  <!-- 排行榜列表 -->
  <view class="ranking-list" wx:if="{{!loading && rankingData.length > 0}}">
    <view 
      wx:for="{{rankingData}}" 
      wx:key="id" 
      class="ranking-item"
      data-id="{{item.id}}"
      bindtap="onItemTap">
      
      <!-- 排名 -->
      <view class="rank-number {{item.rank <= 3 ? 'rank-top' : ''}}">
        <text class="rank-text">{{item.rank}}</text>
      </view>
      
      <!-- 衣物图片 -->
      <view class="item-image-container">
        <image 
          src="{{item.image_url}}" 
          mode="aspectFill" 
          class="item-image"
          lazy-load="true">
        </image>
      </view>
      
      <!-- 衣物信息 -->
      <view class="item-info">
        <view class="item-name text-bold">{{item.name}}</view>
        <view class="item-category text-secondary text-sm">{{item.categoryName}}</view>
      </view>
      
      <!-- 穿搭次数 -->
      <view class="wear-count">
        <text class="count-number text-bold">{{item.wear_count}}</text>
        <text class="count-unit text-secondary text-sm">次</text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && rankingData.length === 0}}">
    <view class="text-center">
      <text class="text-muted">暂无穿搭记录</text>
    </view>
    <view class="text-center mt-10">
      <text class="text-secondary text-sm">快去添加衣物并记录穿搭次数吧！</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="text-center">
      <text class="text-secondary">加载中...</text>
    </view>
  </view>

  <!-- 分页加载更多 -->
  <view class="load-more" wx:if="{{hasMore && !loading}}">
    <view class="text-center" bindtap="loadMore">
      <text class="text-secondary">点击加载更多</text>
    </view>
  </view>
</view>
