<?php
/**
 * 测试圈子权限功能
 * 验证衣物和穿搭的权限控制是否正常工作
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    echo "=== 圈子权限功能测试 ===\n";
    echo "当前用户ID: $userId\n\n";
    
    // 测试权限检查API
    echo "1. 测试权限检查API\n";
    
    $testCases = [
        ['data_type' => 'clothes', 'operation' => 'create'],
        ['data_type' => 'outfits', 'operation' => 'create'],
        ['data_type' => 'clothes', 'data_id' => '1', 'operation' => 'edit'],
        ['data_type' => 'outfits', 'data_id' => '1', 'operation' => 'edit'],
        ['data_type' => 'clothes', 'data_id' => '1', 'operation' => 'delete'],
        ['data_type' => 'outfits', 'data_id' => '1', 'operation' => 'delete']
    ];
    
    foreach ($testCases as $testCase) {
        echo "\n测试案例: {$testCase['data_type']} - {$testCase['operation']}";
        if (isset($testCase['data_id'])) {
            echo " (ID: {$testCase['data_id']})";
        }
        echo "\n";
        
        // 构建API URL
        $apiUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/check_circle_permission.php";
        
        $params = http_build_query($testCase);
        $fullUrl = "$apiUrl?$params";
        
        // 发起API请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $fullUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: ' . $_SERVER['HTTP_AUTHORIZATION'],
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "❌ CURL错误: $error\n";
            continue;
        }
        
        if ($httpCode !== 200) {
            echo "❌ HTTP错误: $httpCode\n";
            continue;
        }
        
        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "❌ JSON解析错误: " . json_last_error_msg() . "\n";
            continue;
        }
        
        if ($data['status'] === 'success') {
            $permission = $data['data'];
            $status = $permission['allowed'] ? '✅ 允许' : '❌ 拒绝';
            echo "$status - {$permission['reason']}\n";
            
            if ($permission['is_circle_member']) {
                echo "   角色: {$permission['user_role']}\n";
            }
            if ($permission['is_owner']) {
                echo "   数据创建者: 是\n";
            }
        } else {
            echo "❌ API错误: {$data['message']}\n";
        }
    }
    
    // 测试实际数据的权限
    echo "\n2. 测试实际数据权限\n";
    
    $db = new Database();
    $conn = $db->getConnection();
    
    // 获取用户的衣物
    $stmt = $conn->prepare("
        SELECT id, name, user_id, circle_id, 
               CASE 
                   WHEN circle_id IS NULL THEN 'personal'
                   ELSE 'shared'
               END as data_source
        FROM clothes 
        WHERE user_id = :user_id OR circle_id IN (
            SELECT circle_id FROM circle_members 
            WHERE user_id = :user_id AND status = 'active'
        )
        LIMIT 5
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "用户可见的衣物:\n";
    foreach ($clothes as $item) {
        $isOwner = $item['user_id'] == $userId ? '(自己的)' : '(他人的)';
        echo "- ID: {$item['id']}, 名称: {$item['name']}, 数据源: {$item['data_source']} $isOwner\n";
    }
    
    // 获取用户的穿搭
    $stmt = $conn->prepare("
        SELECT id, name, user_id, circle_id,
               CASE 
                   WHEN circle_id IS NULL THEN 'personal'
                   ELSE 'shared'
               END as data_source
        FROM outfits 
        WHERE user_id = :user_id OR circle_id IN (
            SELECT circle_id FROM circle_members 
            WHERE user_id = :user_id AND status = 'active'
        )
        LIMIT 5
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $outfits = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\n用户可见的穿搭:\n";
    foreach ($outfits as $item) {
        $isOwner = $item['user_id'] == $userId ? '(自己的)' : '(他人的)';
        echo "- ID: {$item['id']}, 名称: {$item['name']}, 数据源: {$item['data_source']} $isOwner\n";
    }
    
    // 测试具体权限
    if (!empty($clothes)) {
        $testClothes = $clothes[0];
        echo "\n测试衣物权限 (ID: {$testClothes['id']}):\n";
        
        foreach (['view', 'edit', 'delete'] as $operation) {
            $permission = testPermission($conn, $userId, 'clothes', $testClothes['id'], $operation);
            $status = $permission['allowed'] ? '✅' : '❌';
            echo "- $operation: $status {$permission['reason']}\n";
        }
    }
    
    if (!empty($outfits)) {
        $testOutfit = $outfits[0];
        echo "\n测试穿搭权限 (ID: {$testOutfit['id']}):\n";
        
        foreach (['view', 'edit', 'delete'] as $operation) {
            $permission = testPermission($conn, $userId, 'outfits', $testOutfit['id'], $operation);
            $status = $permission['allowed'] ? '✅' : '❌';
            echo "- $operation: $status {$permission['reason']}\n";
        }
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

/**
 * 测试权限（简化版本）
 */
function testPermission($conn, $userId, $dataType, $dataId, $operation) {
    // 这里实现简化的权限检查逻辑
    $result = ['allowed' => false, 'reason' => ''];
    
    $table = $dataType === 'clothes' ? 'clothes' : 'outfits';
    
    $stmt = $conn->prepare("SELECT user_id, circle_id FROM $table WHERE id = :id");
    $stmt->bindParam(':id', $dataId, PDO::PARAM_INT);
    $stmt->execute();
    $data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$data) {
        $result['reason'] = '数据不存在';
        return $result;
    }
    
    $isOwner = $data['user_id'] == $userId;
    
    if ($operation === 'view') {
        $result['allowed'] = true;
        $result['reason'] = '查看权限';
    } elseif ($isOwner) {
        $result['allowed'] = true;
        $result['reason'] = '数据创建者';
    } else {
        $result['reason'] = '非数据创建者';
    }
    
    return $result;
}
?>
