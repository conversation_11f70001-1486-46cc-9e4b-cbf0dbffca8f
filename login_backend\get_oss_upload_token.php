<?php
/**
 * 获取OSS上传凭证API
 * 
 * 生成阿里云OSS直传所需的签名URL或STS临时凭证
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * GET Parameters:
 * - file_type: 文件类型，如image/jpeg (可选)
 * - file_name: 自定义文件名 (可选)
 * 
 * Response:
 * {
 *   "error": false,
 *   "data": {
 *     "upload_url": "签名后的URL",
 *     "file_key": "文件在OSS中的路径",
 *     "file_url": "上传后文件的访问URL",
 *     "callback_url": "上传完成后的回调URL"
 *   }
 * }
 */

// 设置响应内容类型
header('Content-Type: application/json');

// 处理CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';
require_once 'oss_helper.php'; // 引入OSS助手

// 错误日志
error_log("===== 开始处理OSS上传凭证请求 =====");
error_log("请求方法: " . $_SERVER['REQUEST_METHOD']);
error_log("请求参数: " . json_encode($_GET));

// 检查是否存在Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    error_log("错误: 缺少Authorization头");
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
error_log("收到的Token: " . substr($token, 0, 20) . '...');

// 验证token
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    error_log("错误: Token验证失败");
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid or expired token'
    ]);
    exit;
}

// 获取用户ID
$userId = $tokenData['sub'];
error_log("用户ID: $userId");

// 创建OSS助手实例
try {
    error_log("尝试创建OSS助手实例");
    error_log("OSS配置: Endpoint=" . ALIYUN_OSS_ENDPOINT . ", Bucket=" . ALIYUN_OSS_BUCKET . ", Domain=" . ALIYUN_OSS_BUCKET_DOMAIN);
    $ossHelper = new OssHelper();
    error_log("OSS助手实例创建成功");
} catch (Exception $e) {
    error_log("OSS助手初始化失败: " . $e->getMessage());
    echo json_encode([
        'error' => true,
        'msg' => 'Failed to initialize OSS helper: ' . $e->getMessage()
    ]);
    exit;
}

// 获取可选参数
$fileType = isset($_GET['file_type']) ? $_GET['file_type'] : 'image/jpeg';
$customFileName = isset($_GET['file_name']) ? $_GET['file_name'] : '';
error_log("文件类型: $fileType, 自定义文件名: $customFileName");

// 生成文件名
$extension = '';
switch ($fileType) {
    case 'image/jpeg':
    case 'image/jpg':
        $extension = 'jpg';
        break;
    case 'image/png':
        $extension = 'png';
        break;
    case 'image/gif':
        $extension = 'gif';
        break;
    case 'image/webp':
        $extension = 'webp';
        break;
    default:
        $extension = 'jpg';
}

// 生成唯一文件名
if (empty($customFileName)) {
    $fileName = 'clothing_' . $userId . '_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
} else {
    // 确保自定义文件名安全且有扩展名
    $fileName = preg_replace('/[^a-zA-Z0-9_\-\.]/', '', $customFileName);
    if (!preg_match('/\.' . $extension . '$/i', $fileName)) {
        $fileName .= '.' . $extension;
    }
}

// 设置OSS路径 - 使用clothes目录存储衣物图片
$ossKey = 'clothes/' . $fileName;

// 获取直传URL和策略
error_log("调用generateUploadUrl方法，参数: ossKey=$ossKey, fileType=$fileType");
$uploadInfo = $ossHelper->generateUploadUrl($ossKey, $fileType);
error_log("generateUploadUrl返回结果: " . json_encode($uploadInfo));

if ($uploadInfo['success']) {
    // 计算上传后的文件URL
    $fileUrl = $ossHelper->getFileUrl($ossKey);
    error_log("生成的文件URL: $fileUrl");
    
    // 设置回调URL - 上传完成后小程序可以调用此URL
    $callbackUrl = '';
    if (isset($_SERVER['HTTP_HOST'])) {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
        $callbackUrl = $protocol . $_SERVER['HTTP_HOST'] . '/login_backend/oss_upload_callback.php';
        error_log("生成的回调URL: $callbackUrl");
    } else {
        error_log("警告: 无法获取HTTP_HOST，回调URL将为空");
    }
    
    $response = [
        'error' => false,
        'data' => [
            'upload_url' => $uploadInfo['url'],
            'file_key' => $ossKey,
            'file_url' => $fileUrl,
            'callback_url' => $callbackUrl,
            'headers' => $uploadInfo['headers'] ?? [],
            'form_fields' => $uploadInfo['form_fields'] ?? []
        ]
    ];
    error_log("准备返回成功响应: " . json_encode($response));
    echo json_encode($response);
} else {
    error_log("错误: 生成上传URL失败: " . ($uploadInfo['error'] ?? '未知错误'));
    echo json_encode([
        'error' => true,
        'msg' => $uploadInfo['error'] ?? 'Failed to generate upload URL'
    ]);
} 