<!--圈子数据共享页面-->
<!--模块4：数据共享基础模块-->

<view class="container">


  <!-- 原始页面头部 -->
  <view class="header" style="display: none;">
    <view class="circle-info">
      <text class="circle-name">{{circleInfo.circle_name}}</text>
      <text class="circle-role">{{circleInfo.user_role === 'creator' ? '创建者' : '成员'}}</text>
    </view>
    <view class="stats-bar">
      <view class="stat-item">
        <text class="stat-number">{{stats.total_wardrobes}}</text>
        <text class="stat-label">衣橱</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.total_clothes}}</text>
        <text class="stat-label">衣物</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.total_outfits}}</text>
        <text class="stat-label">穿搭</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.total_members}}</text>
        <text class="stat-label">成员</text>
      </view>
    </view>
  </view>

  <!-- 数据同步区域 -->
  <view class="sync-section">
    <view class="sync-header">
      <text class="section-title">数据同步</text>
      <text class="sync-desc">将您的个人数据同步到圈子中，与成员共享</text>
    </view>
    <view class="sync-actions">
      <button class="sync-btn" bindtap="showSyncModal" disabled="{{syncing}}">
        {{syncing ? '同步中...' : '同步我的数据'}}
      </button>

    </view>
  </view>

  <!-- 标签页切换 -->
  <view class="tabs">
    <view class="tab-item {{currentTab === 'wardrobes' ? 'active' : ''}}" data-tab="wardrobes" bindtap="switchTab">
      <text>衣橱</text>
    </view>
    <view class="tab-item {{currentTab === 'clothes' ? 'active' : ''}}" data-tab="clothes" bindtap="switchTab">
      <text>衣物</text>
    </view>
    <view class="tab-item {{currentTab === 'outfits' ? 'active' : ''}}" data-tab="outfits" bindtap="switchTab">
      <text>穿搭</text>
    </view>
  </view>

  <!-- 衣橱列表 -->
  <view class="content-area" wx:if="{{currentTab === 'wardrobes'}}">
    <view class="filter-bar" wx:if="{{wardrobes.length > 0}}">
      <text class="filter-label">数据来源：</text>
      <view class="filter-options">
        <text class="filter-option {{dataFilter === 'all' ? 'active' : ''}}" data-filter="all" bindtap="setDataFilter">全部</text>
        <text class="filter-option {{dataFilter === 'shared' ? 'active' : ''}}" data-filter="shared" bindtap="setDataFilter">共享</text>
        <text class="filter-option {{dataFilter === 'personal' ? 'active' : ''}}" data-filter="personal" bindtap="setDataFilter">个人</text>
      </view>
    </view>
    
    <view class="wardrobes-list" wx:if="{{wardrobes.length > 0}}">
      <view class="wardrobe-item" wx:for="{{filteredWardrobes}}" wx:key="id" data-wardrobe="{{item}}" bindtap="viewWardrobeDetail">
        <view class="wardrobe-info">
          <view class="wardrobe-header">
            <text class="wardrobe-name">{{item.name}}</text>
            <view class="wardrobe-badges">
              <text class="badge shared-badge" wx:if="{{item.is_shared}}">共享</text>
              <text class="badge own-badge" wx:if="{{item.is_own}}">我的</text>
              <text class="badge default-badge" wx:if="{{item.is_default}}">默认</text>
            </view>
          </view>
          <text class="wardrobe-desc" wx:if="{{item.description}}">{{item.description}}</text>
          <view class="wardrobe-meta">
            <text class="creator">创建者：{{item.creator_nickname}}</text>
            <text class="clothes-count">{{item.clothes_count}}件衣物</text>
          </view>
        </view>
        <view class="wardrobe-arrow">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
    </view>
    
    <view class="empty-state" wx:if="{{wardrobes.length === 0 && !loading}}">
      <text class="empty-text">暂无衣橱数据</text>
      <text class="empty-desc">同步您的数据或等待其他成员分享</text>
    </view>
  </view>

  <!-- 衣物列表 -->
  <view class="content-area" wx:if="{{currentTab === 'clothes'}}">
    <view class="filter-bar" wx:if="{{clothes.length > 0}}">
      <text class="filter-label">分类：</text>
      <view class="filter-options">
        <text class="filter-option {{categoryFilter === 'all' ? 'active' : ''}}" data-category="all" bindtap="setCategoryFilter">全部</text>
        <text class="filter-option {{categoryFilter === item.category ? 'active' : ''}}"
              wx:for="{{categoryStats}}" wx:key="category"
              data-category="{{item.category}}" bindtap="setCategoryFilter">
          {{item.displayName}}({{item.count}})
        </text>
      </view>
    </view>
    
    <view class="clothes-list" wx:if="{{clothes.length > 0}}">
      <view class="clothes-item" wx:for="{{filteredClothes}}" wx:key="id" data-clothes="{{item}}" bindtap="viewClothesDetail">

        <!-- 衣物头部信息 -->
        <view class="clothes-header">
          <view class="clothes-name">{{item.name}}</view>
          <view class="clothes-meta">
            <text class="clothes-category">{{item.category || '未分类'}}</text>
            <text class="clothes-creator">{{item.creator_nickname}}</text>
            <view class="clothes-badges">
              <text class="badge shared-badge" wx:if="{{item.is_shared}}">共享</text>
              <text class="badge own-badge" wx:if="{{item.is_own}}">我的</text>
            </view>
          </view>
        </view>

        <!-- 衣物内容区域 -->
        <view class="clothes-content">
          <!-- 衣物图片容器 -->
          <view class="clothes-image-container">
            <image class="clothes-image" src="{{item.image_url}}" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </view>
    
    <view class="empty-state" wx:if="{{clothes.length === 0 && !loading}}">
      <text class="empty-text">暂无衣物数据</text>
      <text class="empty-desc">同步您的数据或等待其他成员分享</text>
    </view>
  </view>

  <!-- 穿搭列表 -->
  <view class="content-area" wx:if="{{currentTab === 'outfits'}}">
    <view class="filter-bar" wx:if="{{outfits.length > 0}}">
      <text class="filter-label">分类：</text>
      <view class="filter-options">
        <text class="filter-option {{outfitCategoryFilter === 'all' ? 'active' : ''}}" data-category="all" bindtap="setOutfitCategoryFilter">全部</text>
        <text class="filter-option {{outfitCategoryFilter === item.category ? 'active' : ''}}"
              wx:for="{{outfitCategoryStats}}" wx:key="category"
              data-category="{{item.category}}" bindtap="setOutfitCategoryFilter">
          {{item.displayName}}({{item.count}})
        </text>
      </view>
    </view>
    
    <view class="outfits-list" wx:if="{{outfits.length > 0}}">
      <view class="outfit-item" wx:for="{{filteredOutfits}}" wx:key="id" data-outfit="{{item}}" bindtap="viewOutfitDetail">

        <!-- 穿搭头部信息 -->
        <view class="outfit-header">
          <view class="outfit-name">{{item.name}}</view>
          <view class="outfit-meta">
            <text class="outfit-category">{{item.category || '未分类'}}</text>
            <text class="outfit-creator">{{item.creator_nickname}}</text>
            <view class="outfit-badges">
              <text class="badge shared-badge" wx:if="{{item.is_shared}}">共享</text>
              <text class="badge own-badge" wx:if="{{item.is_own}}">我的</text>
            </view>
          </view>
        </view>

        <!-- 穿搭内容区域 -->
        <view class="outfit-content">
          <!-- 衣物容器 -->
          <view class="items-container">
            <scroll-view wx:if="{{item.items && item.items.length > 0}}"
                         class="items-scroll"
                         scroll-x="true"
                         show-scrollbar="false">
              <view class="items-list">
                <view
                  wx:for="{{item.items}}"
                  wx:for-item="clothingItem"
                  wx:key="clothing_id"
                  class="clothing-item">
                  <image
                    src="{{clothingItem.clothing_data.image_url}}"
                    mode="aspectFit"
                    class="clothing-image">
                  </image>
                </view>
              </view>
            </scroll-view>
            <view wx:else class="no-items">
              <text class="no-items-text">无衣物</text>
            </view>

            <view class="outfit-clothes-count">
              <text>{{item.items ? item.items.length : 0}}件</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="empty-state" wx:if="{{outfits.length === 0 && !loading}}">
      <text class="empty-text">暂无穿搭数据</text>
      <text class="empty-desc">同步您的数据或等待其他成员分享</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 数据同步弹框 -->
<view class="modal-overlay" wx:if="{{showSyncModal}}">
  <view class="modal-background" bindtap="forceHideSyncModal"></view>
  <view class="sync-modal">
    <view class="modal-header">
      <text class="modal-title">同步数据到圈子</text>
    </view>
    <view class="modal-content">
      <text class="modal-desc">选择要同步的数据类型：</text>
      <checkbox-group bindchange="onSyncOptionsChange">
        <view class="sync-options">
          <label class="sync-option">
            <checkbox value="wardrobes" checked="{{syncOptions.wardrobes}}"/>
            <text>衣橱数据</text>
          </label>
          <label class="sync-option">
            <checkbox value="clothes" checked="{{syncOptions.clothes}}"/>
            <text>衣物数据</text>
          </label>
          <label class="sync-option">
            <checkbox value="outfits" checked="{{syncOptions.outfits}}"/>
            <text>穿搭数据</text>
          </label>
        </view>
      </checkbox-group>
      <view class="sync-type-options">
        <text class="option-label">同步方式：</text>
        <radio-group bindchange="onSyncTypeChange">
          <label class="sync-type-option">
            <radio value="incremental" checked="{{syncType === 'incremental'}}"/>
            <text>增量同步（仅最近数据）</text>
          </label>
          <label class="sync-type-option">
            <radio value="initial" checked="{{syncType === 'initial'}}"/>
            <text>完整同步（所有数据）</text>
          </label>
        </radio-group>
      </view>
    </view>
    <view class="modal-actions">
      <button class="cancel-btn" bindtap="forceHideSyncModal">取消</button>
      <button class="confirm-btn" bindtap="startSync" disabled="{{syncing}}">
        {{syncing ? '同步中...' : '开始同步'}}
      </button>
    </view>
  </view>
</view>
