/* pages/wardrobes/detail/detail.wxss */
.container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #ffffff;
  min-height: 100vh;
}

.wardrobe-info {
  width: 100%;
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  border-radius: 10px;
  background-color: #f9f9f9;
}

.wardrobe-name {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #333333;
}

.wardrobe-description {
  font-size: 14px;
  color: #666666;
}

.action-buttons {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.btn-primary, .btn-secondary {
  width: 90%;
  margin-bottom: 15px;
  padding: 12px 0;
  border-radius: 24px;
  font-size: 16px;
  text-align: center;
}

.btn-primary {
  background-color: #000000;
  color: #ffffff;
  font-weight: 500;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333333;
}