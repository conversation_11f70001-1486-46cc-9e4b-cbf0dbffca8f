/**
 * 圈子权限管理工具
 * 用于检查用户对数据的操作权限
 */

const app = getApp();

/**
 * 检查圈子权限
 * @param {string} dataType - 数据类型：clothes, outfits, wardrobes, categories
 * @param {string} dataId - 数据ID
 * @param {string} operation - 操作类型：view, edit, delete, create
 * @returns {Promise} 权限检查结果
 */
function checkCirclePermission(dataType, dataId, operation) {
  return new Promise((resolve, reject) => {
    if (!app.globalData.token) {
      reject(new Error('用户未登录'));
      return;
    }

    const url = `${app.globalData.apiBaseUrl}/check_circle_permission.php`;
    const data = {
      data_type: dataType,
      data_id: dataId,
      operation: operation
    };

    // 使用GET请求，参数放在URL中
    const queryString = Object.keys(data)
      .map(key => `${key}=${encodeURIComponent(data[key])}`)
      .join('&');
    const fullUrl = `${url}?${queryString}`;

    console.log('权限检查请求:', fullUrl);

    wx.request({
      url: fullUrl,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${app.globalData.token}`,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('权限检查响应:', res);
        if (res.statusCode === 200 && res.data.status === 'success') {
          resolve(res.data.data);
        } else {
          console.error('权限检查失败:', res.data);
          reject(new Error(res.data.message || '权限检查失败'));
        }
      },
      fail: (err) => {
        console.error('权限检查请求失败:', err);
        reject(new Error('网络请求失败'));
      }
    });
  });
}

/**
 * 检查是否可以编辑数据
 * @param {string} dataType - 数据类型
 * @param {string} dataId - 数据ID
 * @returns {Promise<boolean>} 是否可以编辑
 */
function canEdit(dataType, dataId) {
  return checkCirclePermission(dataType, dataId, 'edit')
    .then(permission => permission.allowed)
    .catch(() => false);
}

/**
 * 检查是否可以删除数据
 * @param {string} dataType - 数据类型
 * @param {string} dataId - 数据ID
 * @returns {Promise<boolean>} 是否可以删除
 */
function canDelete(dataType, dataId) {
  return checkCirclePermission(dataType, dataId, 'delete')
    .then(permission => permission.allowed)
    .catch(() => false);
}

/**
 * 检查是否可以查看数据
 * @param {string} dataType - 数据类型
 * @param {string} dataId - 数据ID
 * @returns {Promise<boolean>} 是否可以查看
 */
function canView(dataType, dataId) {
  return checkCirclePermission(dataType, dataId, 'view')
    .then(permission => permission.allowed)
    .catch(() => false);
}

/**
 * 检查是否可以创建数据
 * @param {string} dataType - 数据类型
 * @returns {Promise<boolean>} 是否可以创建
 */
function canCreate(dataType) {
  return checkCirclePermission(dataType, '', 'create')
    .then(permission => permission.allowed)
    .catch(() => false);
}

/**
 * 根据数据源和用户ID判断是否可以编辑
 * @param {Object} item - 数据项，包含data_source、user_id等字段
 * @param {number} currentUserId - 当前用户ID
 * @returns {boolean} 是否可以编辑（简单判断）
 */
function canEditByDataSource(item, currentUserId) {
  // 如果是个人数据，只有创建者可以编辑
  if (item.data_source === 'personal') {
    return item.user_id === currentUserId;
  }
  
  // 如果是共享数据，需要进一步权限检查
  if (item.data_source === 'shared') {
    // 这里返回true，具体权限由后端API检查
    return true;
  }
  
  // 默认情况
  return item.user_id === currentUserId;
}

/**
 * 显示权限错误提示
 * @param {string} operation - 操作类型
 * @param {string} reason - 错误原因
 */
function showPermissionError(operation, reason) {
  const operationNames = {
    'view': '查看',
    'edit': '编辑',
    'delete': '删除',
    'create': '创建'
  };
  
  const operationName = operationNames[operation] || operation;
  
  wx.showModal({
    title: '权限不足',
    content: `您没有权限${operationName}此数据。\n原因：${reason}`,
    showCancel: false,
    confirmText: '我知道了'
  });
}

/**
 * 检查权限并执行操作
 * @param {string} dataType - 数据类型
 * @param {string} dataId - 数据ID
 * @param {string} operation - 操作类型
 * @param {Function} callback - 有权限时执行的回调函数
 * @param {Function} errorCallback - 无权限时执行的回调函数（可选）
 */
function checkPermissionAndExecute(dataType, dataId, operation, callback, errorCallback) {
  checkCirclePermission(dataType, dataId, operation)
    .then(permission => {
      if (permission.allowed) {
        callback(permission);
      } else {
        showPermissionError(operation, permission.reason);
        if (errorCallback) {
          errorCallback(permission);
        }
      }
    })
    .catch(error => {
      console.error('权限检查失败:', error);
      wx.showToast({
        title: '权限检查失败',
        icon: 'none'
      });
      if (errorCallback) {
        errorCallback({ allowed: false, reason: error.message });
      }
    });
}

/**
 * 获取用户在圈子中的角色信息
 * @param {Object} permission - 权限检查结果
 * @returns {string} 角色描述
 */
function getRoleDescription(permission) {
  if (permission.is_owner) {
    return '数据创建者';
  }
  
  if (permission.user_role === 'creator') {
    return '圈子创建者';
  }
  
  if (permission.user_role === 'member') {
    return '圈子成员';
  }
  
  return '访客';
}

module.exports = {
  checkCirclePermission,
  canEdit,
  canDelete,
  canView,
  canCreate,
  canEditByDataSource,
  showPermissionError,
  checkPermissionAndExecute,
  getRoleDescription
};
