<?php
// 测试穿搭数据解析API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';
require_once 'Logger.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode(['status' => 'error', 'message' => '缺少授权头']);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode(['status' => 'error', 'message' => '无效或已过期的令牌']);
    exit;
}

$userId = $payload['sub'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 获取一个穿搭数据进行测试
    $outfitId = isset($_GET['outfit_id']) ? intval($_GET['outfit_id']) : null;
    
    if ($outfitId) {
        // 测试指定的穿搭
        $sql = "SELECT id, name, outfit_data FROM outfits WHERE id = :outfit_id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':outfit_id', $outfitId, PDO::PARAM_INT);
        $stmt->execute();
        $outfit = $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        // 获取最新的一个穿搭进行测试
        $sql = "SELECT id, name, outfit_data FROM outfits ORDER BY created_at DESC LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $outfit = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    if (!$outfit) {
        echo json_encode(['status' => 'error', 'message' => '未找到穿搭数据']);
        exit;
    }
    
    // 解析穿搭数据
    $outfitData_json = json_decode($outfit['outfit_data'], true);
    $parseResult = [
        'outfit_id' => $outfit['id'],
        'outfit_name' => $outfit['name'],
        'raw_data_length' => strlen($outfit['outfit_data']),
        'json_valid' => $outfitData_json !== null,
        'parsed_data' => $outfitData_json,
        'clothes' => []
    ];
    
    if ($outfitData_json && isset($outfitData_json['items']) && is_array($outfitData_json['items'])) {
        foreach ($outfitData_json['items'] as $index => $item) {
            $clothingInfo = [
                'index' => $index,
                'has_clothing_id' => isset($item['clothing_id']),
                'has_clothing_data' => isset($item['clothing_data']),
                'clothing_id' => $item['clothing_id'] ?? null,
                'clothing_data' => $item['clothing_data'] ?? null,
                'position' => $item['position'] ?? null,
                'size' => $item['size'] ?? null,
                'rotation' => $item['rotation'] ?? null,
                'z_index' => $item['z_index'] ?? null
            ];
            
            if (isset($item['clothing_data'])) {
                $clothingData = $item['clothing_data'];
                $clothingInfo['parsed_clothes'] = [
                    'clothes_id' => $item['clothing_id'],
                    'clothes_name' => $clothingData['name'] ?? '未知衣物',
                    'clothes_image' => $clothingData['image_url'] ?? '',
                    'category' => $clothingData['category'] ?? ''
                ];
            }
            
            $parseResult['clothes'][] = $clothingInfo;
        }
    }
    
    $parseResult['summary'] = [
        'total_items' => count($outfitData_json['items'] ?? []),
        'valid_clothes' => count(array_filter($parseResult['clothes'], function($item) {
            return $item['has_clothing_id'] && $item['has_clothing_data'];
        }))
    ];
    
    // 记录测试结果
    Logger::debug('穿搭解析测试', $parseResult, 'outfit_parsing_test');
    
    echo json_encode([
        'status' => 'success',
        'data' => $parseResult
    ]);
    
} catch (Exception $e) {
    Logger::error('穿搭解析测试失败', [
        'user_id' => $userId ?? null,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ], 'outfit_parsing_test');
    
    echo json_encode([
        'status' => 'error',
        'message' => '测试失败：' . $e->getMessage()
    ]);
}
?>
