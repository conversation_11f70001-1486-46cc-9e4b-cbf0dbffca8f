<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务条款 - 次元衣帽间</title>
    <meta name="description" content="次元衣帽间服务条款，使用我们的服务前请仔细阅读。">
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="images/logo.png" type="image/png">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5B6EF9',
                        secondary: '#F664B8',
                        dark: '#333333',
                        light: '#F9FAFC'
                    }
                }
            }
        }
    </script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom styles -->
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        .hero-gradient {
            background: linear-gradient(135deg, #5B6EF9 0%, #F664B8 100%);
        }
        .logo-container {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 5px;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Header/Navigation -->
    <header class="sticky top-0 bg-white shadow-sm z-50">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <a href="index.html" class="flex items-center space-x-2">
                <div class="logo-container">
                    <img src="images/logo.png" alt="次元衣帽间" class="h-10 w-auto">
                </div>
                <span class="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">次元衣帽间</span>
            </a>
            <nav class="hidden md:flex space-x-8">
                <a href="index.html#features" class="text-dark hover:text-primary font-medium transition-colors">功能特点</a>
                <a href="index.html#how-it-works" class="text-dark hover:text-primary font-medium transition-colors">使用流程</a>
                <a href="index.html#about" class="text-dark hover:text-primary font-medium transition-colors">关于我们</a>
            </nav>
            <div class="md:hidden">
                <button id="mobile-menu-button" class="text-dark p-2">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-md">
            <div class="container mx-auto px-4 py-2 flex flex-col space-y-3">
                <a href="index.html#features" class="text-dark hover:text-primary font-medium transition-colors py-2">功能特点</a>
                <a href="index.html#how-it-works" class="text-dark hover:text-primary font-medium transition-colors py-2">使用流程</a>
                <a href="index.html#about" class="text-dark hover:text-primary font-medium transition-colors py-2">关于我们</a>
            </div>
        </div>
    </header>

    <!-- Terms of Service Content -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h1 class="text-3xl md:text-4xl font-bold mb-8 text-center">服务条款</h1>
                
                <div class="bg-light p-6 md:p-10 rounded-xl shadow-sm mb-8">
                    <p class="text-gray-700 mb-6">最后更新日期：2025年1月1日</p>
                    
                    <div class="space-y-8">
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">1. 接受条款</h2>
                            <p class="text-gray-700">欢迎使用次元衣帽间（以下简称"我们"或"本应用"）提供的服务。请您在使用我们的服务前仔细阅读本服务条款。通过访问或使用我们的小程序，您确认已阅读、理解并同意受本服务条款的约束。如果您不同意本条款的任何部分，请勿使用我们的服务。</p>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">2. 服务描述</h2>
                            <p class="text-gray-700">次元衣帽间是一款提供AI虚拟试衣和个人衣橱管理功能的微信小程序。我们的服务包括但不限于以下功能：AI虚拟试衣、衣橱管理、穿搭创建和保存、服装图片智能处理等。我们保留随时修改、暂停或终止部分或全部服务的权利，并且在可能的情况下会提前通知用户。</p>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">3. 用户账户</h2>
                            <p class="text-gray-700 mb-4">使用我们的服务可能需要创建用户账户。关于用户账户，您同意：</p>
                            <ul class="list-disc pl-6 space-y-2 text-gray-700">
                                <li>提供准确、完整的信息。</li>
                                <li>保护账户安全，对账户下的所有活动负责。</li>
                                <li>如发现未授权使用您账户的情况，及时通知我们。</li>
                            </ul>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">4. 用户内容</h2>
                            <p class="text-gray-700 mb-4">您可能会在使用我们的服务时上传、发布或提交内容，包括但不限于个人照片、服装图片等（"用户内容"）。关于用户内容，您确认并同意：</p>
                            <ul class="list-disc pl-6 space-y-2 text-gray-700">
                                <li>您拥有或已获得使用和分享该内容的所有必要权利和许可。</li>
                                <li>授予我们非独占的、可转让的、可再许可的、免版税的全球性许可，允许我们使用、复制、修改、展示、执行和分发您的用户内容，以提供和改进我们的服务。</li>
                                <li>您上传的内容不会侵犯任何第三方的权利或违反任何法律法规。</li>
                            </ul>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">5. 禁止行为</h2>
                            <p class="text-gray-700 mb-4">在使用我们的服务时，您同意不会：</p>
                            <ul class="list-disc pl-6 space-y-2 text-gray-700">
                                <li>违反任何适用的法律法规。</li>
                                <li>侵犯他人的知识产权、隐私权或其他权利。</li>
                                <li>上传包含病毒、恶意代码或有害程序的内容。</li>
                                <li>尝试未经授权访问我们的系统或用户账户。</li>
                                <li>利用我们的服务进行非法活动或损害他人权益的行为。</li>
                            </ul>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">6. 知识产权</h2>
                            <p class="text-gray-700">除用户内容外，我们的服务及其所有内容、功能和特性，包括但不限于所有信息、软件、文本、图片、设计等，均为我们或我们的许可方所有，并受中国及国际知识产权法保护。未经我们明确许可，您不得复制、修改、分发、销售或利用我们服务中的任何材料。</p>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">7. 免责声明</h2>
                            <p class="text-gray-700">我们的服务按"现状"和"可用性"提供，不提供任何明示或暗示的保证。我们不保证服务将不中断、无错误或满足您的特定需求。我们不对服务中可能出现的任何错误、病毒、数据丢失或损坏负责。</p>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">8. 责任限制</h2>
                            <p class="text-gray-700">在法律允许的最大范围内，我们不对因使用或无法使用我们的服务而导致的任何直接、间接、附带、特殊、惩罚性或后果性损害负责，无论此类损害是基于保证、合同、侵权行为还是任何其他法律理论。</p>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">9. 条款变更</h2>
                            <p class="text-gray-700">我们可能会不时修改本服务条款。如有重大变更，我们会通过在小程序内公告或其他适当方式通知您。您继续使用我们的服务将视为接受修改后的条款。</p>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">10. 适用法律</h2>
                            <p class="text-gray-700">本服务条款受中华人民共和国法律管辖，并依其解释。任何与我们服务相关的争议应提交至我们所在地有管辖权的法院解决。</p>
                        </div>
                        
                        <div>
                            <h2 class="text-xl font-bold mb-4 text-primary">11. 联系我们</h2>
                            <p class="text-gray-700">如您对本服务条款有任何疑问或建议，请通过以下方式联系我们：</p>
                            <p class="text-gray-700 mt-2">电子邮件：<EMAIL></p>
                            <p class="text-gray-700">电话：18606539135</p>
                            <p class="text-gray-700">微信：shawii</p>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <a href="index.html" class="bg-primary hover:bg-opacity-90 text-white px-8 py-3 rounded-full font-medium inline-flex items-center justify-center transition-all">
                        <i class="fas fa-arrow-left mr-2"></i> 返回首页
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-12">
        <div class="container mx-auto px-4">
            <div class="border-t border-gray-800 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-500 mb-4 md:mb-0">&copy; 2025 次元衣帽间 - 版权所有</p>
                <div class="flex space-x-6">
                    <a href="privacy.html" class="text-gray-500 hover:text-white transition-colors">隐私政策</a>
                    <a href="terms.html" class="text-gray-500 hover:text-white transition-colors">服务条款</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
    </script>
</body>
</html> 