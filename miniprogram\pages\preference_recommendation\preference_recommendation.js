const app = getApp();
// 引入每日配额管理工具
const dailyQuota = require('../../utils/dailyQuota');

// 定义推荐功能名称常量
const FEATURE_NAME = 'preference_outfit_recommendation';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    hasUserInfo: false,
    userInfo: null,
    preference: '', // 个人喜好输入内容
    loading: false, // 加载状态
    outfits: [], // 推荐的穿搭列表
    outfit: null, // 当前选中的穿搭
    refreshing: false, // 刷新状态
    showInput: true, // 是否显示输入框
    historyPreferences: [], // 历史偏好记录
    showHistory: false, // 是否显示历史记录
    // 新增属性，用于控制分享提示弹窗
    showShareTip: false,
    // 剩余可用次数
    availableQuota: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检查是否是从分享链接进入
    if (options.source === 'outfit_share') {
      // 是分享链接进入，直接跳转到首页
      wx.switchTab({
        url: '/pages/index/index'
      });
      return; // 不继续执行后面的代码
    }

    // 清除之前的推荐数据，确保不会复用旧数据
    this.resetOutfitData();
    this.setData({
      showInput: true
    });

    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        hasUserInfo: true
      });
      
      // 加载历史偏好记录
      this.loadHistoryPreferences();
    } else {
      // 监听用户登录
      app.userInfoReadyCallback = res => {
        this.setData({
          userInfo: res,
          hasUserInfo: true
        });
        
        // 加载历史偏好记录
        this.loadHistoryPreferences();
      };
    }
    
    // 更新可用次数状态
    this.updateQuotaStatus();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 清除之前的推荐数据，确保不会复用旧数据
    // 只有在非刷新状态下才重置数据，避免影响刷新操作
    if (!this.data.refreshing && !this.data.loading) {
      this.resetOutfitData();
      this.setData({
        showInput: true
      });
    }
    
    // 更新可用次数状态
    this.updateQuotaStatus();
  },
  
  // 更新配额状态
  updateQuotaStatus: function() {
    const availableQuota = dailyQuota.getAvailableQuota(FEATURE_NAME);
    console.log('更新配额状态, 功能:', FEATURE_NAME, '可用次数:', availableQuota);
    this.setData({
      availableQuota: availableQuota
    });
  },

  /**
   * 加载历史偏好记录
   */
  loadHistoryPreferences: function() {
    try {
      const historyPreferences = wx.getStorageSync('historyPreferences') || [];
      this.setData({
        historyPreferences: historyPreferences
      });
    } catch (e) {
      console.error('获取历史偏好记录失败:', e);
    }
  },

  /**
   * 保存历史偏好记录
   */
  saveHistoryPreference: function(preference) {
    try {
      let historyPreferences = this.data.historyPreferences;
      
      // 如果已存在相同偏好，则移除旧记录
      const index = historyPreferences.indexOf(preference);
      if (index !== -1) {
        historyPreferences.splice(index, 1);
      }
      
      // 添加到历史记录开头
      historyPreferences.unshift(preference);
      
      // 限制历史记录数量为10条
      if (historyPreferences.length > 10) {
        historyPreferences = historyPreferences.slice(0, 10);
      }
      
      // 更新本地存储和数据
      wx.setStorageSync('historyPreferences', historyPreferences);
      this.setData({
        historyPreferences: historyPreferences
      });
    } catch (e) {
      console.error('保存历史偏好记录失败:', e);
    }
  },

  /**
   * 输入框内容变化处理
   */
  onPreferenceInput: function(e) {
    this.setData({
      preference: e.detail.value
    });
  },

  /**
   * 清空输入框
   */
  clearPreference: function() {
    this.setData({
      preference: ''
    });
  },

  /**
   * 显示/隐藏历史记录
   */
  toggleHistory: function() {
    this.setData({
      showHistory: !this.data.showHistory
    });
  },

  /**
   * 选择历史记录
   */
  selectHistory: function(e) {
    const preference = e.currentTarget.dataset.preference;
    this.setData({
      preference: preference,
      showHistory: false
    });
    
    // 直接生成推荐，传入true表示这是初次加载
    this.generateRecommendation(true);
  },

  /**
   * 生成推荐穿搭
   */
  generateRecommendation: function(isInitialLoad = false) {
    console.log('generateRecommendation调用，isInitialLoad=', isInitialLoad);
    
    // 检查输入是否为空
    if (!this.data.preference.trim()) {
      wx.showToast({
        title: '请输入个人喜好',
        icon: 'none'
      });
      return;
    }
    
    // 检查用户是否还有可用次数
    const hasQuota = dailyQuota.hasAvailableQuota(FEATURE_NAME);
    console.log('检查配额结果:', hasQuota, '当前可用次数:', this.data.availableQuota);
    
    if (!hasQuota) {
      console.log("用户今日推荐次数已用完");
      // 显示分享提示
      this.setData({
        showShareTip: true
      });
      return;
    }
    
    // 保存到历史记录
    this.saveHistoryPreference(this.data.preference);
    
    // 清除之前的推荐数据，确保不会显示旧数据
    this.resetOutfitData();
    
    // 设置加载状态
    this.setData({
      loading: true,
      showInput: false // 隐藏输入框，显示结果
    });
    
    const that = this;
    
    // 确保serverUrl存在
    const serverUrl = app.globalData.apiBaseUrl;
    if (!serverUrl) {
      console.error('Server URL is not defined');
      this.setData({ loading: false });
      wx.showToast({
        title: '服务器地址未配置',
        icon: 'none'
      });
      return;
    }
    
    // 确保token存在
    const token = app.globalData.token;
    if (!token) {
      console.error('Authorization token is not defined');
      this.setData({ loading: false });
      wx.showToast({
        title: '未登录或授权已过期',
        icon: 'none'
      });
      return;
    }
    
    const requestData = {
      preference: this.data.preference,
      refresh: 1 // 始终使用refresh=1，强制获取新数据
    };
    
    console.log('准备发送请求:', {
      url: serverUrl + '/get_preference_recommendation.php',
      method: 'POST',
      preference: this.data.preference,
      refresh: requestData.refresh,
      isInitialLoad: isInitialLoad
    });
    
    // 发送请求获取推荐
    wx.request({
      url: serverUrl + '/get_preference_recommendation.php',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: requestData,
      success: function(res) {
        console.log('获取推荐响应:', res.data);
        
        if (res.data.status === 'success') {
          that.setData({
            outfit: res.data.recommendation,
            loading: false
          });
          
          // 消耗一次配额
          console.log('消耗配额前可用次数:', that.data.availableQuota);
          dailyQuota.useQuota(FEATURE_NAME);
          // 更新可用次数状态
          that.updateQuotaStatus();
          console.log('消耗配额后可用次数:', that.data.availableQuota);
        } else {
          that.setData({
            loading: false
          });
          wx.showToast({
            title: res.data.message || '获取推荐失败',
            icon: 'none'
          });
        }
      },
      fail: function(error) {
        console.error('Failed to generate recommendation:', error);
        that.setData({
          loading: false
        });
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 刷新推荐
   */
  refreshRecommendation: function() {
    console.log('refreshRecommendation调用');
    
    if (this.data.refreshing) {
      console.log('正在刷新中，忽略此次调用');
      return;
    }
    
    // 检查用户是否还有可用次数
    const hasQuota = dailyQuota.hasAvailableQuota(FEATURE_NAME);
    console.log('刷新检查配额结果:', hasQuota, '当前可用次数:', this.data.availableQuota);
    
    if (!hasQuota) {
      console.log("用户今日推荐次数已用完");
      // 显示分享提示
      this.setData({
        showShareTip: true
      });
      return;
    }
    
    // 清除之前的推荐数据，确保不会显示旧数据
    this.resetOutfitData();
    
    this.setData({
      refreshing: true
    });
    
    const that = this;
    
    // 确保serverUrl存在
    const serverUrl = app.globalData.apiBaseUrl;
    if (!serverUrl) {
      console.error('Server URL is not defined');
      this.setData({ refreshing: false });
      return;
    }
    
    // 确保token存在
    const token = app.globalData.token;
    if (!token) {
      console.error('Authorization token is not defined');
      this.setData({ refreshing: false });
      wx.showToast({
        title: '未登录或授权已过期',
        icon: 'none'
      });
      return;
    }
    
    const requestData = {
      preference: this.data.preference,
      refresh: 1 // 明确标记为刷新请求
    };
    
    console.log('准备发送刷新请求:', {
      url: serverUrl + '/get_preference_recommendation.php',
      method: 'POST',
      preference: this.data.preference,
      refresh: requestData.refresh
    });
    
    // 发送请求刷新推荐
    wx.request({
      url: serverUrl + '/get_preference_recommendation.php',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: requestData,
      success: function(res) {
        console.log('刷新推荐响应:', res.data);
        
        if (res.data.status === 'success') {
          that.setData({
            outfit: res.data.recommendation,
            refreshing: false
          });
          
          // 消耗一次配额
          console.log('刷新消耗配额前可用次数:', that.data.availableQuota);
          dailyQuota.useQuota(FEATURE_NAME);
          // 更新可用次数状态
          that.updateQuotaStatus();
          console.log('刷新消耗配额后可用次数:', that.data.availableQuota);
        } else {
          that.setData({
            refreshing: false
          });
          wx.showToast({
            title: res.data.message || '刷新推荐失败',
            icon: 'none'
          });
        }
      },
      fail: function(error) {
        console.error('Failed to refresh recommendation:', error);
        that.setData({
          refreshing: false
        });
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 返回输入界面
   */
  backToInput: function() {
    this.setData({
      showInput: true,
      outfit: null
    });
  },

  /**
   * 保存穿搭
   */
  saveOutfit: function() {
    console.log("保存穿搭按钮被点击");
    
    // 检查是否有穿搭数据
    if (!this.data.outfit) {
      wx.showToast({
        title: '暂无穿搭推荐',
        icon: 'none'
      });
      return;
    }
    
    // 检查用户是否已登录
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载中
    wx.showLoading({
      title: '保存中...',
      mask: true
    });
    
    // 获取或创建AI推荐分类
    this.getOrCreateAICategory()
      .then(categoryId => {
        console.log("获取到AI推荐分类ID:", categoryId);
        
        // 构建穿搭对象
        const now = new Date();
        const outfitName = `基于"${this.data.preference}"的推荐穿搭`;
        const outfit = {
          name: outfitName, // 使用喜好作为穿搭名称
          description: this.data.outfit.outfit_summary || "基于个人喜好的AI智能推荐穿搭", // 使用穿搭总结作为描述
          category_id: categoryId, // AI推荐分类
          thumbnail: "", // 由后端生成缩略图
          created_at: now.toISOString(),
          updated_at: now.toISOString(),
          items: this.convertAIOutfitToItems(this.data.outfit), // 转换穿搭项
          forceAdd: true // 强制添加，即使可能为空
        };
        
        console.log("保存的穿搭数据:", outfit);
        
        // 保存穿搭，包括同步到服务器
        app.saveOutfit(outfit, (result) => {
          wx.hideLoading();
          
          if (result.success) {
            const savedOutfit = result.data || outfit;
            console.log('保存穿搭成功:', savedOutfit);
            
            // 显示成功提示，并询问是否前往编辑
            wx.showModal({
              title: '保存成功',
              content: '穿搭已保存至"AI推荐"分类，是否立即编辑?',
              confirmText: '去编辑',
              cancelText: '稍后再说',
              success: (res) => {
                if (res.confirm) {
                  // 跳转到编辑页
                  wx.navigateTo({
                    url: `/pages/outfits/edit/edit?id=${savedOutfit.id}`
                  });
                }
              }
            });
          } else {
            console.error('保存穿搭失败:', result.error);
            wx.showToast({
              title: result.error || '保存失败',
              icon: 'none'
            });
          }
        });
      })
      .catch(error => {
        wx.hideLoading();
        console.error("保存穿搭失败:", error);
        wx.showToast({
          title: '保存失败: ' + error.message,
          icon: 'none'
        });
      });
  },

  // 获取或创建AI推荐分类
  getOrCreateAICategory: function() {
    return new Promise((resolve, reject) => {
      // 获取穿搭分类列表
      wx.request({
        url: `${app.globalData.apiBaseUrl}/get_outfit_categories.php`,
        method: 'GET',
        header: {
          'Authorization': app.globalData.token
        },
        data: {
          page: 1,
          per_page: 100 // 获取足够多的分类
        },
        success: (res) => {
          console.log('获取穿搭分类列表:', res.data);
          
          if (res.statusCode === 200 && res.data.success) {
            const categories = res.data.data || [];
            
            // 查找名为"AI推荐"的分类
            const aiCategory = categories.find(cat => cat.name === 'AI推荐');
            
            if (aiCategory) {
              // 已存在AI推荐分类，直接使用
              console.log('已存在AI推荐分类:', aiCategory);
              resolve(aiCategory.id);
            } else {
              // 不存在则创建新分类
              console.log('需要创建AI推荐分类');
              this.createAICategory().then(resolve).catch(reject);
            }
          } else {
            reject(new Error('获取分类列表失败'));
          }
        },
        fail: (err) => {
          console.error('获取分类列表失败:', err);
          reject(err);
        }
      });
    });
  },

  // 创建AI推荐分类
  createAICategory: function() {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiBaseUrl}/add_outfit_category.php`,
        method: 'POST',
        header: {
          'Authorization': app.globalData.token,
          'Content-Type': 'application/json'
        },
        data: {
          name: 'AI推荐',
          description: 'AI智能推荐的穿搭集合'
        },
        success: (res) => {
          console.log('创建AI推荐分类响应:', res.data);
          
          if (res.statusCode === 200 && res.data.success) {
            resolve(res.data.data.id);
          } else {
            reject(new Error(res.data.message || '创建分类失败'));
          }
        },
        fail: (err) => {
          console.error('创建AI推荐分类失败:', err);
          reject(err);
        }
      });
    });
  },

  // 将AI推荐的穿搭转换为编辑器可用的items数组
  convertAIOutfitToItems: function(aiOutfit) {
    const items = [];
    const canvasWidth = 375; // 假设画布宽度为375px（微信小程序默认设计宽度）
    const canvasHeight = 500; // 假设画布高度为500px
    
    // 定义画布区域
    const leftX = 30;
    const rightX = canvasWidth - 150; // 假设物品宽度约120px
    const topY = 30;
    const bottomY = canvasHeight - 200; // 假设物品高度约170px
    const centerX = canvasWidth / 2 - 75; // 中心X
    const centerY = canvasHeight / 2 - 100; // 中心Y
    
    // 提取所有衣物并保存到一个数组
    let clothingItems = [];
    
    // 按照显示顺序添加衣物
    if (aiOutfit.top && aiOutfit.top.id && aiOutfit.top.name) {
      clothingItems.push(this.createClothingItem(aiOutfit.top, "上衣"));
    }
    
    if (aiOutfit.outerwear && aiOutfit.outerwear.id && aiOutfit.outerwear.name) {
      clothingItems.push(this.createClothingItem(aiOutfit.outerwear, "外套"));
    }
    
    if (aiOutfit.bottom && aiOutfit.bottom.id && aiOutfit.bottom.name) {
      const category = aiOutfit.bottom.category === 'pants' ? '裤子' : '裙子';
      clothingItems.push(this.createClothingItem(aiOutfit.bottom, category));
    }
    
    if (aiOutfit.shoes && aiOutfit.shoes.id && aiOutfit.shoes.name) {
      clothingItems.push(this.createClothingItem(aiOutfit.shoes, "鞋子"));
    }
    
    if (aiOutfit.accessories && aiOutfit.accessories.id && aiOutfit.accessories.name) {
      clothingItems.push(this.createClothingItem(aiOutfit.accessories, "配饰"));
    }
    
    if (aiOutfit.bag && aiOutfit.bag.id && aiOutfit.bag.name) {
      clothingItems.push(this.createClothingItem(aiOutfit.bag, "包包"));
    }
    
    // 根据要求排列衣物
    clothingItems.forEach((item, index) => {
      const outfitItem = {
        clothing_id: item.id,
        clothing_data: {
          name: item.name,
          category: item.category,
          image_url: item.image_url
        },
        position: { x: 0, y: 0 },
        size: { width: 150, height: 200 },
        rotation: 0,
        z_index: index + 1
      };
      
      // 根据索引设置位置
      switch (index) {
        case 0: // 第一件：左上
          outfitItem.position = { x: leftX, y: topY };
          break;
        case 1: // 第二件：右上
          outfitItem.position = { x: rightX, y: topY };
          break;
        case 2: // 第三件：左下
          outfitItem.position = { x: leftX, y: bottomY };
          break;
        case 3: // 第四件：右下
          outfitItem.position = { x: rightX, y: bottomY };
          break;
        case 4: // 第五件：中间
          outfitItem.position = { x: centerX, y: centerY };
          break;
        default: // 其余：围绕中心均匀分布
          const radius = 80; // 分布半径
          const angle = (index - 5) * (2 * Math.PI / (clothingItems.length - 5));
          outfitItem.position = {
            x: centerX + radius * Math.cos(angle),
            y: centerY + radius * Math.sin(angle)
          };
      }
      
      items.push(outfitItem);
    });
    
    return items;
  },

  // 创建衣物项辅助函数
  createClothingItem: function(item, displayCategory) {
    return {
      id: item.id,
      name: item.name || `${displayCategory}`,
      category: item.category || 'unknown',
      image_url: item.image_url
    };
  },

  /**
   * 查看衣物详情
   */
  viewClothingDetail: function(e) {
    const clothingId = e.currentTarget.dataset.id;
    if (!clothingId) return;
    
    wx.navigateTo({
      url: '/pages/clothing/detail/detail?id=' + clothingId
    });
  },

  /**
   * 前往登录
   */
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  /**
   * 获取分类名称
   */
  getCategoryName: function(categoryCode) {
    const categoryMap = {
      'tops': '上衣',
      'pants': '裤子',
      'skirts': '裙子',
      'coats': '外套',
      'shoes': '鞋子',
      'bags': '包包',
      'accessories': '配饰'
    };
    
    return categoryMap[categoryCode] || categoryCode;
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {
    // 分享后增加配额
    setTimeout(() => {
      this.onShareSuccess();
    }, 1000);
    
    if (this.data.outfit) {
      return {
        title: '不知道怎么搭配穿搭，快用次元衣帽间',
        path: '/pages/index/index?source=outfit_share',
        imageUrl: 'https://images.alidog.cn/logo/xxfx.png' // 使用固定的分享图片
      };
    }
    
    return {
      title: '不知道怎么搭配穿搭，快用次元衣帽间',
      path: '/pages/index/index?source=outfit_share',
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png' // 使用固定的分享图片
    };
  },
  
  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function() {
    // 分享后增加配额
    setTimeout(() => {
      this.onShareSuccess();
    }, 1000);
    
    return {
      title: '不知道怎么搭配穿搭，快用次元衣帽间',
      query: 'source=outfit_share',
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png' // 使用固定的分享图片
    };
  },

  // 关闭分享提示弹窗
  closeShareTip: function() {
    this.setData({
      showShareTip: false
    });
  },
  
  // 通过分享获取更多次数
  shareToGetMoreQuota: function() {
    // 分享之前先关闭弹窗
    this.setData({
      showShareTip: false
    });
    
    // 唤起系统分享
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },
  
  // 分享成功回调（需要用户手动告知分享成功）
  onShareSuccess: function() {
    console.log('分享成功回调');
    
    // 增加分享次数
    dailyQuota.addShareQuota(FEATURE_NAME);
    
    // 更新可用次数状态
    this.updateQuotaStatus();
    console.log('分享后可用次数:', this.data.availableQuota);
    
    // 显示成功提示
    wx.showToast({
      title: '获得1次推荐机会',
      icon: 'success',
      duration: 2000
    });
    
    // 自动刷新推荐
    setTimeout(() => {
      console.log('分享后自动刷新推荐, outfit状态:', !!this.data.outfit);
      if (this.data.outfit) {
        this.refreshRecommendation();
      } else {
        this.generateRecommendation(true);
      }
    }, 2000);
  },

  /**
   * 重置穿搭数据
   * 用于确保不会复用旧数据
   */
  resetOutfitData: function() {
    console.log('重置穿搭数据');
    this.setData({
      outfit: null,
      outfits: []
    });
  }
}); 