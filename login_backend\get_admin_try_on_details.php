<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] != 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证管理员身份
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 检查ID参数
if (!isset($_GET['id']) || empty($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少试衣历史ID参数']);
    exit();
}

$historyId = (int)$_GET['id'];

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

// 查询试衣历史详情
$historyQuery = "SELECT 
                    th.id, th.user_id, th.result_image_url, th.clothes_ids, th.photo_id, 
                    th.task_id, th.status, th.created_at, th.updated_at, th.error_message
                FROM try_on_history th
                WHERE th.id = :id";
$historyStmt = $conn->prepare($historyQuery);
$historyStmt->bindValue(':id', $historyId, PDO::PARAM_INT);
$historyStmt->execute();

$history = $historyStmt->fetch(PDO::FETCH_ASSOC);

if (!$history) {
    http_response_code(404);
    echo json_encode(['error' => true, 'msg' => '试衣历史记录不存在']);
    exit();
}

// 提取试衣使用方式
$usageType = "未知次数类型";
if (!empty($history['error_message'])) {
    // 检查error_message是否包含次数类型信息
    $possibleTypes = ["免费次数", "付费次数", "商家免费次数", "商家付费次数", "系统试衣"];
    foreach ($possibleTypes as $type) {
        if (strpos($history['error_message'], $type) === 0) {
            $usageType = $type;
            break;
        }
    }
    
    // 如果error_message中包含"|"，说明还有其他错误信息
    if (strpos($history['error_message'], " | ") !== false) {
        $parts = explode(" | ", $history['error_message'], 2);
        $usageType = $parts[0];
        $history['error_message'] = $parts[1]; // 保留原有的错误信息
    }
}

// 添加使用方式到历史记录中
$history['usage_type'] = $usageType;

// 查询用户信息
$userQuery = "SELECT 
                u.id, u.nickname, u.avatar_url, u.gender, u.created_at, u.status
              FROM users u
              WHERE u.id = :user_id";
$userStmt = $conn->prepare($userQuery);
$userStmt->bindValue(':user_id', $history['user_id'], PDO::PARAM_INT);
$userStmt->execute();

$user = $userStmt->fetch(PDO::FETCH_ASSOC);

// 查询照片信息（如果有）
$photo = null;
if (!empty($history['photo_id'])) {
    $photoQuery = "SELECT 
                    p.id, p.user_id, p.image_url, p.type, p.description, p.created_at
                  FROM photos p
                  WHERE p.id = :photo_id";
    $photoStmt = $conn->prepare($photoQuery);
    $photoStmt->bindValue(':photo_id', $history['photo_id'], PDO::PARAM_INT);
    $photoStmt->execute();
    
    $photo = $photoStmt->fetch(PDO::FETCH_ASSOC);
}

// 解析衣物ID并查询衣物信息
$clothes = [];
if (!empty($history['clothes_ids'])) {
    // 尝试以JSON格式解析
    $clothesIds = json_decode($history['clothes_ids'], true);
    
    // 如果解析失败，则尝试以逗号分隔的方式解析
    if (!is_array($clothesIds)) {
        $clothesIds = explode(',', $history['clothes_ids']);
    }
    
    if (is_array($clothesIds) && count($clothesIds) > 0) {
        $placeholders = implode(',', array_fill(0, count($clothesIds), '?'));
        $clothesQuery = "SELECT 
                            c.id, c.name, c.image_url, c.category, c.description
                        FROM clothes c
                        WHERE c.id IN ($placeholders)";
        $clothesStmt = $conn->prepare($clothesQuery);
        
        foreach ($clothesIds as $index => $clothingId) {
            $clothesStmt->bindValue($index + 1, $clothingId, PDO::PARAM_INT);
        }
        
        $clothesStmt->execute();
        $clothes = $clothesStmt->fetchAll(PDO::FETCH_ASSOC);
    }
}

// 返回数据
echo json_encode([
    'error' => false,
    'data' => [
        'history' => $history,
        'user' => $user,
        'photo' => $photo,
        'clothes' => $clothes
    ]
]); 