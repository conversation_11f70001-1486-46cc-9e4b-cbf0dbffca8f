<?php
// 启用输出缓冲
ob_start();

/**
 * Upload Image API
 * 
 * Uploads an image file and returns the URL
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * POST Parameters:
 * - image: The image file to upload (multipart/form-data)
 * 
 * Response:
 * {
 *   "error": false,
 *   "data": {
 *     "image_url": "https://example.com/uploads/image.jpg"
 *   }
 * }
 */

// 关闭错误显示在输出中，但仍然记录到日志
ini_set('display_errors', 0);
error_reporting(E_ALL);

error_log("===== 开始处理图片上传请求 =====");

// 创建自定义日志文件
$logFile = __DIR__ . '/debug.log';
file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "===== 开始处理图片上传请求 =====\n", FILE_APPEND);

function writeLog($message) {
    global $logFile;
    file_put_contents($logFile, date('[Y-m-d H:i:s] ') . $message . "\n", FILE_APPEND);
    error_log($message);
}

// 添加调试函数，输出关键全局变量状态
function debugGlobals($label) {
    $segment_type = isset($GLOBALS['segment_type']) ? $GLOBALS['segment_type'] : 'undefined';
    $config_type = defined('SEGMENT_API_TYPE') ? SEGMENT_API_TYPE : 'undefined';
    
    writeLog("======= 调试状态 ($label) =======");
    writeLog("GLOBALS['segment_type'] = $segment_type");
    writeLog("SEGMENT_API_TYPE配置 = $config_type");
    writeLog("==============================");
}

// 自动加载SDK
$autoloadPath = __DIR__ . '/../vendor/autoload.php';
if (file_exists($autoloadPath)) {
    writeLog("找到自动加载器: " . $autoloadPath);
    require_once $autoloadPath;
    writeLog("已加载自动加载器");
} else {
    // 使用绝对路径尝试引入
    $altPath = '/www/wwwroot/cyyg.alidog.cn/vendor/autoload.php';
    if (file_exists($altPath)) {
        writeLog("找到替代自动加载器: " . $altPath);
        require_once $altPath;
        writeLog("已加载替代自动加载器");
    } else {
        writeLog("错误: Composer自动加载器未找到，SDK可能无法正常工作");
    }
}

// 开启详细错误报告
// ini_set('display_errors', 1);
// error_reporting(E_ALL);

// 添加判断，避免重复定义类
if (!class_exists('RuntimeOptions')) {
    // 完全按照官方示例定义RuntimeOptions类
    class RuntimeOptions {
        public $autoretry;
        public $ignoreSSL;
        public $maxAttempts;
        public $backoffPolicy;
        public $backoffPeriod;
        public $readTimeout;
        public $connectTimeout;
        public $httpProxy;
        public $httpsProxy;
        public $noProxy;
        public $maxIdleConns;
        public $socks5Proxy;
        public $socks5NetWork;
        public $keepAlive;
        public $retry;
        // 添加OpenApiClient.php需要的属性
        public $key;
        public $cert;
        public $ca;
    
        function __construct($config = [])
        {
            if (!empty($config)) {
                foreach ($config as $k => $v) {
                    $this->{$k} = $v;
                }
            }
        }
    
        function validate() {
            // 不做实际验证，但必须提供此方法
            return true;
        }
    
        function toMap() {
            $res = [];
            foreach (get_object_vars($this) as $k => $v) {
                $res[$k] = $v;
            }
            return $res;
        }
    
        static function fromMap($map = []) {
            $model = new self([]);
            if (!empty($map)) {
                foreach ($map as $k => $v) {
                    $model->{$k} = $v;
                }
            }
            return $model;
        }
    }
    
    // 如果需要，创建别名
    class_alias('RuntimeOptions', 'AlibabaCloud\Tea\Utils\Utils\RuntimeOptions');
    
    writeLog("已定义官方示例RuntimeOptions类并创建别名");
}

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 记录配置信息
writeLog("阿里云配置检查:");
if (defined('ALIYUN_ACCESS_KEY_ID') && !empty(ALIYUN_ACCESS_KEY_ID)) {
    writeLog("ALIYUN_ACCESS_KEY_ID已设置: " . substr(ALIYUN_ACCESS_KEY_ID, 0, 3) . "***");
} else {
    writeLog("错误: ALIYUN_ACCESS_KEY_ID未设置或为空");
}

if (defined('ALIYUN_ACCESS_KEY_SECRET') && !empty(ALIYUN_ACCESS_KEY_SECRET)) {
    writeLog("ALIYUN_ACCESS_KEY_SECRET已设置: " . substr(ALIYUN_ACCESS_KEY_SECRET, 0, 3) . "***");
} else {
    writeLog("错误: ALIYUN_ACCESS_KEY_SECRET未设置或为空");
}

// 检查SDK类是否可用
$sdkAvailable = true;
try {
    if (!class_exists('AlibabaCloud\SDK\Imageseg\*********\Imageseg')) {
        writeLog("错误: 找不到Imageseg类");
        $sdkAvailable = false;
    } else {
        writeLog("Imageseg类可用");
    }
    
    if (!class_exists('Darabonba\OpenApi\Models\Config')) {
        writeLog("错误: 找不到Config类");
        $sdkAvailable = false;
    } else {
        writeLog("Config类可用");
    }
    
    if (!class_exists('AlibabaCloud\SDK\Imageseg\*********\Models\SegmentClothRequest')) {
        writeLog("错误: 找不到SegmentClothRequest类");
        $sdkAvailable = false;
    } else {
        writeLog("SegmentClothRequest类可用");
    }
    
    writeLog("已跳过对RuntimeOptions类的检查，将使用数组替代");
    
} catch (Exception $e) {
    writeLog("检查SDK类时出错: " . $e->getMessage());
    $sdkAvailable = false;
}

// 引入阿里云SDK - 需要在服务器上通过Composer安装
// composer require alibabacloud/imageseg-20191230
use AlibabaCloud\SDK\Imageseg\*********\Imageseg;
use AlibabaCloud\Tea\Utils\Utils;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Imageseg\*********\Models\SegmentClothRequest;
// 添加SegmentClothAdvanceRequest以支持本地文件处理
use AlibabaCloud\SDK\Imageseg\*********\Models\SegmentClothAdvanceRequest;
// 添加SegmentCommonImageRequest以支持通用分割
use AlibabaCloud\SDK\Imageseg\*********\Models\SegmentCommonImageRequest;
use GuzzleHttp\Psr7\Stream;
// 不再使用命名空间类的RuntimeOptions
// use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;

// Set response content type
header('Content-Type: application/json');

// Handle CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if Authorization header exists
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

// 检查是否是POST JSON请求 - 用于抠图处理
$isJsonRequest = false;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && 
    isset($_SERVER['CONTENT_TYPE']) && 
    strpos($_SERVER['CONTENT_TYPE'], 'application/json') !== false) {
    
    $isJsonRequest = true;
    // 获取JSON请求体
    $jsonBody = file_get_contents('php://input');
    writeLog("接收到JSON请求体: " . $jsonBody);
    
    $jsonData = json_decode($jsonBody, true);
    
    if (!$jsonData) {
        writeLog("JSON解析失败，无效的JSON数据: " . $jsonBody);
        echo json_encode([
            'error' => true,
            'msg' => 'Invalid JSON data'
        ]);
        exit;
    }
    
    writeLog("接收到JSON请求: " . print_r($jsonData, true));
}

// Check if the request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => 'Only POST method is allowed'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// Verify token
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid or expired token'
    ]);
    exit;
}

// Get user ID from token data
$userId = $tokenData['sub'];

// 处理JSON请求 - 用于单独的抠图操作
if ($isJsonRequest) {
    writeLog("开始处理JSON抠图请求");
    debugGlobals("JSON请求开始");
    
    if (empty($jsonData['image_url'])) {
        writeLog("错误: 缺少image_url参数");
        echo json_encode([
            'error' => true,
            'msg' => 'Image URL is required'
        ]);
        exit;
    }
    
    // 获取图片URL
    $imageUrl = $jsonData['image_url'];
    $shouldSegment = isset($jsonData['segment_image']) ? (bool)$jsonData['segment_image'] : true;
    
    // 获取请求中指定的分割类型（如果有）
    $segmentType = isset($jsonData['segment_type']) ? $jsonData['segment_type'] : null;
    if ($segmentType && ($segmentType === 'cloth' || $segmentType === 'common')) {
        writeLog("请求指定分割类型: " . $segmentType);
    } else {
        // 使用全局配置的分割类型
        $segmentType = defined('SEGMENT_API_TYPE') ? SEGMENT_API_TYPE : 'common';
        writeLog("使用全局配置的分割类型: " . $segmentType);
    }
    
    writeLog("准备处理图片URL: $imageUrl, 是否抠图: " . ($shouldSegment ? 'true' : 'false') . ", 分割类型: " . $segmentType);
    
    // 验证URL格式
    if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
        writeLog("错误: 无效的URL格式: $imageUrl");
        echo json_encode([
            'error' => true,
            'msg' => 'Invalid URL format'
        ]);
        exit;
    }
    
    // 判断图片位置
    $localFilePath = '';
    $urlParts = parse_url($imageUrl);
    
    writeLog("解析URL结果: " . print_r($urlParts, true));
    
    if (isset($urlParts['path'])) {
        writeLog("URL路径: " . $urlParts['path']);
        
        if (strpos($urlParts['path'], '/login_backend/uploads/') !== false) {
            // 提取相对路径
            $path = $urlParts['path'];
            $relativePath = str_replace('/login_backend/', '', $path);
            $localFilePath = __DIR__ . '/' . $relativePath;
            writeLog("本地文件路径: " . $localFilePath . ", 文件存在: " . (file_exists($localFilePath) ? 'true' : 'false'));
        } else {
            writeLog("URL路径不包含'/login_backend/uploads/'，可能是外部URL");
        }
    } else {
        writeLog("URL没有路径部分");
    }
    
    // 如果不需要抠图，直接返回原图URL
    if (!$shouldSegment) {
        writeLog("参数设置为不抠图，返回原始URL");
        echo json_encode([
            'error' => false,
            'data' => [
                'image_url' => $imageUrl
            ]
        ]);
        exit;
    }
    
    // 测试URL是否可访问
    $headers = @get_headers($imageUrl);
    if (!$headers) {
        writeLog("错误: 无法访问URL: $imageUrl");
        echo json_encode([
            'error' => true,
            'msg' => 'Unable to access image URL'
        ]);
        exit;
    }
    
    writeLog("URL可访问，HTTP状态: " . $headers[0]);
    
    // 创建segment目录
    $segmentDir = 'uploads/segmented/';
    if (!file_exists($segmentDir)) {
        writeLog("创建分割目录: " . $segmentDir);
        mkdir($segmentDir, 0755, true);
    }
    
    // 生成分割后的文件名
    if (!empty($localFilePath)) {
        $segmentedFilename = 'segmented_' . basename($localFilePath);
    } else {
        // 如果不是本地文件，从URL中提取文件名或生成一个唯一的文件名
        $urlFilename = basename(parse_url($imageUrl, PHP_URL_PATH));
        if (empty($urlFilename) || $urlFilename == '/' || strpos($urlFilename, '.') === false) {
            // 无法从URL获取有效文件名，生成一个唯一的
            $segmentedFilename = 'segmented_' . $userId . '_' . time() . '_' . rand(1000, 9999) . '.jpg';
        } else {
            $segmentedFilename = 'segmented_' . $urlFilename;
        }
    }
    
    $segmentedPath = $segmentDir . $segmentedFilename;
    writeLog("分割图片将保存到: " . $segmentedPath);
    
    // 先下载原图
    writeLog("尝试下载原图");
    $imageContent = @file_get_contents($imageUrl);
    if ($imageContent === false) {
        writeLog("错误: 无法下载图片内容: $imageUrl");
        echo json_encode([
            'error' => true,
            'msg' => 'Failed to download image'
        ]);
        exit;
    }
    
    writeLog("图片下载成功，大小: " . strlen($imageContent) . " 字节");
    
    // 检查SDK是否可用
    $sdkAvailable = true;
    if (!class_exists('AlibabaCloud\SDK\Imageseg\*********\Imageseg') || 
        !class_exists('Darabonba\OpenApi\Models\Config')) {
        writeLog("SDK类不可用，降级为简单复制图片");
        $sdkAvailable = false;
    }
    
    // 调用抠图API
    try {
        writeLog("开始调用segmentClothingImage函数");
        
        // 在调用抠图函数前设置全局变量
        $GLOBALS['segment_type'] = $segmentType;
        writeLog("设置全局变量segment_type为: " . $segmentType);
        debugGlobals("JSON请求设置全局变量后");
        
        // 如果SDK不可用，简单复制图片
        if (!$sdkAvailable) {
            writeLog("SDK不可用，直接复制图片");
            $tempSegmentedPath = $segmentDir . 'temp_' . basename($segmentedPath);
            
            // 保存原图
            if (file_put_contents($tempSegmentedPath, $imageContent)) {
                // 构建URL
                $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
                $host = $_SERVER['HTTP_HOST'];
                $baseUrl = $protocol . $host;
                $baseUrl = rtrim($baseUrl, '/') . '/';
                $resultUrl = $baseUrl . 'login_backend/' . $tempSegmentedPath;
                
                writeLog("简单复制成功，URL: $resultUrl");
                
                // 复制文件
                copy($tempSegmentedPath, $segmentedPath);
                
                $segmentedImageUrl = $baseUrl . 'login_backend/' . $segmentedPath;
                writeLog("最终分割图片URL: $segmentedImageUrl");
                
                echo json_encode([
                    'error' => false,
                    'data' => [
                        'image_url' => $segmentedImageUrl,
                        'original_url' => $imageUrl
                    ]
                ]);
                exit;
            } else {
                writeLog("保存图片失败");
                echo json_encode([
                    'error' => true,
                    'msg' => 'Failed to save image'
                ]);
                exit;
            }
        }
        
        $segmentedImageUrl = segmentClothingImage($imageUrl, $segmentedPath);
        
        if ($segmentedImageUrl) {
            writeLog("服饰分割成功，返回分割后的图片URL: " . $segmentedImageUrl);
            $response = [
                'error' => false,
                'data' => [
                    'image_url' => $segmentedImageUrl,
                    'original_url' => $imageUrl
                ]
            ];
            writeLog("返回响应: " . json_encode($response));
            echo json_encode($response);
        } else {
            writeLog("服饰分割失败，返回原始图片URL");
            
            // 降级处理：直接复制图片作为抠图结果
            if (file_put_contents($segmentedPath, $imageContent)) {
                $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
                $host = $_SERVER['HTTP_HOST'];
                $baseUrl = $protocol . $host;
                $baseUrl = rtrim($baseUrl, '/') . '/';
                $fallbackUrl = $baseUrl . 'login_backend/' . $segmentedPath;
                
                writeLog("降级处理成功，使用复制图片作为结果: " . $fallbackUrl);
                
                $response = [
                    'error' => false,
                    'data' => [
                        'image_url' => $fallbackUrl,
                        'original_url' => $imageUrl,
                        'fallback' => true
                    ]
                ];
            } else {
                writeLog("降级处理失败，无法保存图片");
                $response = [
                    'error' => false,
                    'data' => [
                        'image_url' => $imageUrl
                    ]
                ];
            }
            
            writeLog("返回响应: " . json_encode($response));
            echo json_encode($response);
        }
    } catch (\Exception $e) {
        writeLog("服饰分割异常: " . $e->getMessage() . "\n堆栈跟踪: " . $e->getTraceAsString());
        
        // 降级处理：直接复制图片作为抠图结果
        if (file_put_contents($segmentedPath, $imageContent)) {
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
            $host = $_SERVER['HTTP_HOST'];
            $baseUrl = $protocol . $host;
            $baseUrl = rtrim($baseUrl, '/') . '/';
            $fallbackUrl = $baseUrl . 'login_backend/' . $segmentedPath;
            
            writeLog("异常后降级处理成功，使用复制图片作为结果: " . $fallbackUrl);
            
            $response = [
                'error' => false,
                'data' => [
                    'image_url' => $fallbackUrl,
                    'original_url' => $imageUrl,
                    'fallback' => true,
                    'error_msg' => $e->getMessage()
                ]
            ];
        } else {
            writeLog("异常后降级处理失败，无法保存图片");
            $response = [
                'error' => false,
                'data' => [
                    'image_url' => $imageUrl,
                    'error_msg' => $e->getMessage()
                ]
            ];
        }
        
        writeLog("返回响应: " . json_encode($response));
        echo json_encode($response);
    }
    
    writeLog("JSON抠图请求处理完成");
    exit;
}

// Check if file was uploaded
if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode([
        'error' => true,
        'msg' => 'No image file uploaded or upload error'
    ]);
    exit;
}

// 检查是否需要执行抠图操作
$shouldSegment = isset($_POST['segment_image']) ? ($_POST['segment_image'] === 'true') : true;
writeLog("是否执行抠图: " . ($shouldSegment ? 'true' : 'false'));

// 获取请求中指定的分割类型（如果有）
$segmentType = isset($_POST['segment_type']) ? $_POST['segment_type'] : null;
if ($segmentType && ($segmentType === 'cloth' || $segmentType === 'common')) {
    writeLog("表单请求指定分割类型: " . $segmentType);
} else {
    // 使用全局配置的分割类型
    $segmentType = defined('SEGMENT_API_TYPE') ? SEGMENT_API_TYPE : 'common';
    writeLog("表单请求使用全局配置的分割类型: " . $segmentType);
}

// Validate file type
$allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
$fileType = $_FILES['image']['type'];

if (!in_array($fileType, $allowedTypes)) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid file type. Only JPEG, PNG, GIF, and WEBP are allowed.'
    ]);
    exit;
}

// Create upload directory if it doesn't exist
$uploadDir = 'uploads/';
if (!file_exists($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

// Generate a unique filename
$extension = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
$filename = 'clothing_' . $userId . '_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
$targetPath = $uploadDir . $filename;

// Create segment directory if it doesn't exist
$segmentDir = 'uploads/segmented/';
if (!file_exists($segmentDir)) {
    mkdir($segmentDir, 0755, true);
}

// 分割后的文件名
$segmentedFilename = 'segmented_' . $filename;
$segmentedPath = $segmentDir . $segmentedFilename;

// Move the uploaded file
if (move_uploaded_file($_FILES['image']['tmp_name'], $targetPath)) {
    // Get the full URL to the file
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];
    $baseUrl = $protocol . $host;
    $baseUrl = rtrim($baseUrl, '/') . '/';
    $imageUrl = $baseUrl . 'login_backend/' . $targetPath;
    
    writeLog("原始图片已上传: " . $imageUrl);
    
    // 检查是否需要执行抠图
    if (!$shouldSegment) {
        writeLog("根据参数设置，跳过服饰分割步骤");
        
        // 检查是否需要将图片迁移到OSS
        $ossHelper = null;
        try {
            require_once 'oss_helper.php';
            $ossHelper = new OssHelper();
            writeLog("OSS助手类加载成功");
        } catch (\Exception $e) {
            writeLog("OSS助手类加载失败: " . $e->getMessage());
        }
        
        // 如果OSS助手可用，尝试将图片上传到OSS
        if ($ossHelper && !$ossHelper->isOssUrl($imageUrl)) {
            writeLog("图片不是OSS URL，尝试上传到OSS");
            
            // 生成OSS路径
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
            $ossFilename = 'cloth_' . $userId . '_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
            $ossKey = 'clothes/' . $ossFilename;
            
            // 上传文件到OSS
            $uploadResult = $ossHelper->uploadFile($targetPath, $ossKey);
            if ($uploadResult['success']) {
                writeLog("文件已成功上传到OSS: " . $uploadResult['url']);
                
                // 返回成功响应，只包含原图URL（OSS URL）
                echo json_encode([
                    'error' => false,
                    'data' => [
                        'image_url' => $uploadResult['url']
                    ]
                ]);
                exit;
            } else {
                writeLog("上传到OSS失败: " . $uploadResult['error']);
            }
        }
        
        // 如果不使用OSS或上传失败，直接返回原始URL
        echo json_encode([
            'error' => false,
            'data' => [
                'image_url' => $imageUrl
            ]
        ]);
        exit;
    }
    
    // 检查SDK是否可用
    if (!$sdkAvailable) {
        writeLog("SDK不可用，跳过服饰分割步骤");
        echo json_encode([
            'error' => false,
            'data' => [
                'image_url' => $imageUrl
            ]
        ]);
        exit;
    }
    
    writeLog("SDK检测通过，尝试调用服饰分割API");
    
    try {
        // 调用阿里云服饰分割API
        writeLog("开始调用服饰分割函数");
        debugGlobals("标准上传开始");
        
        // 设置全局变量，确保分割类型传递到函数中
        $GLOBALS['segment_type'] = $segmentType;
        writeLog("表单请求设置全局变量segment_type为: " . $segmentType);
        debugGlobals("标准上传设置全局变量后");
        
        $segmentedImageUrl = segmentClothingImage($imageUrl, $segmentedPath);
        
        if ($segmentedImageUrl) {
            writeLog("服饰分割成功，返回分割后的图片URL: " . $segmentedImageUrl);
            // 返回分割后的图片URL和原始URL
            echo json_encode([
                'error' => false,
                'data' => [
                    'image_url' => $imageUrl, // 返回原图URL作为主URL
                    'segmented_image_url' => $segmentedImageUrl // 同时返回分割后的URL
                ]
            ]);
        } else {
            // 如果分割失败，返回原始图片URL
            writeLog("服饰分割失败，返回原始图片URL");
            echo json_encode([
                'error' => false,
                'data' => [
                    'image_url' => $imageUrl
                ]
            ]);
        }
    } catch (\Exception $e) {
        // 记录错误并返回原始图片URL
        writeLog("服饰分割异常: " . $e->getMessage() . "\n堆栈跟踪: " . $e->getTraceAsString());
        echo json_encode([
            'error' => false,
            'data' => [
                'image_url' => $imageUrl
            ]
        ]);
    }
} else {
    // Return error response
    writeLog("文件上传失败");
    echo json_encode([
        'error' => true,
        'msg' => 'Failed to save uploaded file'
    ]);
}

/**
 * 调用阿里云服饰分割API
 * 
 * @param string $imageUrl 原始图片URL
 * @param string $savePath 分割后图片保存路径
 * @return string|false 分割后的图片URL或失败返回false
 */
function segmentClothingImage($imageUrl, $savePath) {
    writeLog("尝试调用图片分割API，图片URL: " . $imageUrl);
    writeLog("分割后图片保存路径: " . $savePath);
    debugGlobals("segmentClothingImage函数内");
    
    // 检查常量定义
    if (!defined('ALIYUN_ACCESS_KEY_ID') || !defined('ALIYUN_ACCESS_KEY_SECRET')) {
        writeLog("错误: 阿里云访问密钥未定义");
        return false;
    }
    
    // 检查URL是否为CDN URL，如果是则转换为OSS URL
    try {
        require_once 'oss_helper.php';
        $ossHelper = new OssHelper();
        
        // 检查是否是CDN URL
        if (defined('ALIYUN_CDN_DOMAIN') && strpos($imageUrl, ALIYUN_CDN_DOMAIN) !== false) {
            writeLog("检测到CDN URL，将转换为OSS URL进行API调用");
            $originalUrl = $imageUrl; // 保存原始URL用于日志
            $imageUrl = $ossHelper->convertCdnUrlToOssUrl($imageUrl);
            writeLog("URL已转换: " . $originalUrl . " -> " . $imageUrl);
        } else {
            writeLog("URL不是CDN URL或CDN未配置，使用原始URL");
        }
    } catch (\Exception $e) {
        writeLog("URL转换过程中出错: " . $e->getMessage() . "，将继续使用原始URL");
    }
    
    // 检查图片URL是否可访问
    $headers = @get_headers($imageUrl);
    if (!$headers || strpos($headers[0], '200') === false) {
        writeLog("错误: 无法访问图片URL: $imageUrl");
        return false;
    }
    
    try {
        writeLog("AK ID: " . ALIYUN_ACCESS_KEY_ID);
        writeLog("AK Secret: " . substr(ALIYUN_ACCESS_KEY_SECRET, 0, 3) . "***"); // 只记录前三位，保护密钥安全
        
        // 创建阿里云客户端
        writeLog("开始创建阿里云客户端");
        $client = createClient(ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET);
        writeLog("阿里云客户端创建成功");
        
        // 获取本地文件路径
        $localFilePath = '';
        $urlParts = parse_url($imageUrl);
        
        writeLog("图片分割解析URL结果: " . print_r($urlParts, true));
        
        if (isset($urlParts['path']) && strpos($urlParts['path'], '/login_backend/uploads/') !== false) {
            // 提取相对路径
            $path = $urlParts['path'];
            $relativePath = str_replace('/login_backend/', '', $path);
            $localFilePath = __DIR__ . '/' . $relativePath;
            writeLog("本地文件路径: " . $localFilePath . ", 文件存在: " . (file_exists($localFilePath) ? 'true' : 'false'));
        } else {
            writeLog("URL不是本地文件，将使用标准方法");
        }
        
        // 检查SDK是否可用，不可用则模拟抠图
        if (!class_exists('AlibabaCloud\SDK\Imageseg\*********\Imageseg') || 
            !class_exists('Darabonba\OpenApi\Models\Config')) {
            writeLog("SDK类不可用，模拟抠图过程");
            
            // 模拟抠图过程，直接下载图片到目标位置
            $imageContent = file_get_contents($imageUrl);
            if ($imageContent === false) {
                writeLog("无法获取图片内容: $imageUrl");
                return false;
            }
            
            if (file_put_contents($savePath, $imageContent)) {
                // 构建URL
                $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
                $host = $_SERVER['HTTP_HOST'];
                $baseUrl = $protocol . $host;
                $baseUrl = rtrim($baseUrl, '/') . '/';
                $resultUrl = $baseUrl . 'login_backend/' . $savePath;
                
                writeLog("模拟抠图成功，保存到: $savePath");
                writeLog("返回URL: $resultUrl");
                return $resultUrl;
            } else {
                writeLog("无法保存模拟抠图结果");
                return false;
            }
        }
        
        // 明确从全局变量获取分割类型参数
        $segmentApiType = isset($GLOBALS['segment_type']) && in_array($GLOBALS['segment_type'], ['cloth', 'common']) 
                         ? $GLOBALS['segment_type'] 
                         : (defined('SEGMENT_API_TYPE') ? SEGMENT_API_TYPE : 'common');
        
        // 增加详细的日志记录
        if (isset($GLOBALS['segment_type'])) {
            writeLog("从全局变量获取分割类型: " . $GLOBALS['segment_type']);
        } else {
            writeLog("全局变量中没有找到分割类型，使用默认配置");
        }
        
        writeLog("最终确定使用分割API类型: " . $segmentApiType);
        debugGlobals("确定分割类型后");
        
        if ($segmentApiType === 'common') {
            // 使用通用分割API
            writeLog("选择使用通用分割API");
            
            if (!empty($localFilePath) && file_exists($localFilePath)) {
                writeLog("本地文件存在，但通用分割API不支持本地文件处理，将使用URL方法");
                return callSegmentCommonImage($client, $imageUrl, $savePath);
            } else {
                writeLog("使用标准通用分割方法处理URL");
                return callSegmentCommonImage($client, $imageUrl, $savePath);
            }
        } else {
            // 使用服饰分割API (默认)
            writeLog("选择使用服饰分割API");
            
            // 检查文件是否存在
            if (!empty($localFilePath) && file_exists($localFilePath)) {
                writeLog("本地文件存在，使用SegmentClothAdvance方法");
                
                // 以二进制模式打开文件
                $file = fopen($localFilePath, 'rb');
                if (!$file) {
                    writeLog("无法打开本地文件");
                    return false;
                }
                
                // 创建文件流
                $stream = new Stream($file);
                
                // 创建Advance请求
                $request = new SegmentClothAdvanceRequest([
                    "imageURLObject" => $stream
                ]);
                
                // 创建RuntimeOptions
                $runtime = new \AlibabaCloud\Tea\Utils\Utils\RuntimeOptions([
                    "ignoreSSL" => true,
                    "autoretry" => false,
                    "maxAttempts" => 3
                ]);
                
                // 调用API
                writeLog("调用SegmentClothAdvance API");
                try {
                    $resp = $client->segmentClothAdvance($request, $runtime);
                    writeLog("API调用成功");
                    
                    // 关闭文件
                    fclose($file);
                } catch (\Throwable $t) {
                    // 关闭文件
                    if (is_resource($file)) {
                        fclose($file);
                    }
                    writeLog("API调用失败: " . $t->getMessage() . "\n" . $t->getTraceAsString());
                    
                    // 退化为标准方法
                    writeLog("尝试使用标准SegmentCloth方法");
                    return callStandardSegmentCloth($client, $imageUrl, $savePath);
                }
            } else {
                writeLog("使用标准SegmentCloth方法处理URL");
                return callStandardSegmentCloth($client, $imageUrl, $savePath);
            }
        }
        
        // 处理响应
        writeLog("API调用响应: " . json_encode($resp));
        
        // 提取结果URL - 修改路径解析
        if (isset($resp->body->data->elements) && is_array($resp->body->data->elements) && isset($resp->body->data->elements[0]->imageURL)) {
            $segmentedUrl = $resp->body->data->elements[0]->imageURL;
            writeLog("获取到分割后的图片URL: " . $segmentedUrl);
            
            // 下载分割后的图片
            writeLog("尝试下载分割后的图片");
            $imageContent = file_get_contents($segmentedUrl);
            if ($imageContent !== false) {
                // 保存到本地
                writeLog("尝试保存分割后的图片到: " . $savePath);
                if (file_put_contents($savePath, $imageContent)) {
                    writeLog("分割后的图片保存成功");
                    
                    // 返回分割后图片的URL
                    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
                    $host = $_SERVER['HTTP_HOST'];
                    $baseUrl = $protocol . $host;
                    $baseUrl = rtrim($baseUrl, '/') . '/';
                    $resultUrl = $baseUrl . 'login_backend/' . $savePath;
                    writeLog("返回分割后图片URL: " . $resultUrl);
                    return $resultUrl;
                } else {
                    writeLog("分割后的图片保存失败");
                }
            } else {
                writeLog("无法下载分割后的图片: " . $segmentedUrl);
            }
        } else {
            writeLog("API响应中未找到elements[0]->imageURL字段，响应内容: " . json_encode($resp->body));
        }
        
        return false;
    } catch (\Exception $e) {
        writeLog("图片分割API调用异常: " . $e->getMessage() . "\n堆栈跟踪: " . $e->getTraceAsString());
        
        // 尝试降级处理 - 直接返回原图作为抠图结果
        try {
            writeLog("尝试降级处理 - 复制原图作为抠图结果");
            $imageContent = file_get_contents($imageUrl);
            if ($imageContent && file_put_contents($savePath, $imageContent)) {
                // 构建URL
                $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
                $host = $_SERVER['HTTP_HOST'];
                $baseUrl = $protocol . $host;
                $baseUrl = rtrim($baseUrl, '/') . '/';
                $resultUrl = $baseUrl . 'login_backend/' . $savePath;
                
                writeLog("降级处理成功，返回URL: $resultUrl");
                return $resultUrl;
            }
        } catch (\Exception $downgradeEx) {
            writeLog("降级处理也失败: " . $downgradeEx->getMessage());
        }
        
        return false;
    }
}

/**
 * 使用标准SegmentCloth方法处理图片URL
 * 
 * @param Imageseg $client 阿里云客户端
 * @param string $imageUrl 图片URL
 * @param string $savePath 保存路径
 * @return string|false 分割后的图片URL或失败返回false
 */
function callStandardSegmentCloth($client, $imageUrl, $savePath) {
    try {
        // 再次检查URL是否为CDN URL，如果是则转换为OSS URL
        try {
            require_once 'oss_helper.php';
            $ossHelper = new OssHelper();
            
            // 检查是否是CDN URL
            if (defined('ALIYUN_CDN_DOMAIN') && strpos($imageUrl, ALIYUN_CDN_DOMAIN) !== false) {
                writeLog("在callStandardSegmentCloth中检测到CDN URL，将转换为OSS URL进行API调用");
                $originalUrl = $imageUrl; // 保存原始URL用于日志
                $imageUrl = $ossHelper->convertCdnUrlToOssUrl($imageUrl);
                writeLog("URL已转换: " . $originalUrl . " -> " . $imageUrl);
            }
        } catch (\Exception $e) {
            writeLog("callStandardSegmentCloth中URL转换过程中出错: " . $e->getMessage() . "，将继续使用原始URL");
        }
        
        // 创建请求
        $request = new SegmentClothRequest([
            "imageURL" => $imageUrl
        ]);
        
        // 创建RuntimeOptions
        $runtime = new \AlibabaCloud\Tea\Utils\Utils\RuntimeOptions([
            "ignoreSSL" => true,
            "autoretry" => false,
            "maxAttempts" => 3
        ]);
        
        // 记录调用类型
        writeLog("调用标准SegmentCloth API - 使用分割类型: " . (isset($GLOBALS['segment_type']) ? $GLOBALS['segment_type'] : 'undefined'));
        
        // 调用API
        writeLog("调用标准SegmentCloth API");
        $resp = $client->segmentCloth($request, $runtime);
        writeLog("API调用成功，响应: " . json_encode($resp));
        
        // 提取结果URL - 修改路径解析
        if (isset($resp->body->data->elements) && is_array($resp->body->data->elements) && isset($resp->body->data->elements[0]->imageURL)) {
            $segmentedUrl = $resp->body->data->elements[0]->imageURL;
            writeLog("获取到分割后的图片URL: " . $segmentedUrl);
            
            // 下载分割后的图片
            writeLog("尝试下载分割后的图片");
            $imageContent = file_get_contents($segmentedUrl);
            if ($imageContent !== false) {
                // 保存到本地
                writeLog("尝试保存分割后的图片到: " . $savePath);
                if (file_put_contents($savePath, $imageContent)) {
                    writeLog("分割后的图片保存成功");
                    
                    // 返回分割后图片的URL
                    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
                    $host = $_SERVER['HTTP_HOST'];
                    $baseUrl = $protocol . $host;
                    $baseUrl = rtrim($baseUrl, '/') . '/';
                    $resultUrl = $baseUrl . 'login_backend/' . $savePath;
                    writeLog("返回分割后图片URL: " . $resultUrl);
                    return $resultUrl;
                } else {
                    writeLog("分割后的图片保存失败");
                }
            } else {
                writeLog("无法下载分割后的图片: " . $segmentedUrl);
            }
        } else {
            writeLog("API响应中未找到elements[0]->imageURL字段，响应内容: " . json_encode($resp->body));
        }
        
        return false;
    } catch (\Throwable $t) {
        writeLog("标准API调用失败: " . $t->getMessage() . "\n" . $t->getTraceAsString());
        return false;
    }
}

/**
 * 使用标准SegmentCommonImage方法处理图片URL
 * 
 * @param Imageseg $client 阿里云客户端
 * @param string $imageUrl 图片URL
 * @param string $savePath 保存路径
 * @return string|false 分割后的图片URL或失败返回false
 */
function callSegmentCommonImage($client, $imageUrl, $savePath) {
    try {
        // 再次检查URL是否为CDN URL，如果是则转换为OSS URL
        try {
            require_once 'oss_helper.php';
            $ossHelper = new OssHelper();
            
            // 检查是否是CDN URL
            if (defined('ALIYUN_CDN_DOMAIN') && strpos($imageUrl, ALIYUN_CDN_DOMAIN) !== false) {
                writeLog("在callSegmentCommonImage中检测到CDN URL，将转换为OSS URL进行API调用");
                $originalUrl = $imageUrl; // 保存原始URL用于日志
                $imageUrl = $ossHelper->convertCdnUrlToOssUrl($imageUrl);
                writeLog("URL已转换: " . $originalUrl . " -> " . $imageUrl);
            }
        } catch (\Exception $e) {
            writeLog("callSegmentCommonImage中URL转换过程中出错: " . $e->getMessage() . "，将继续使用原始URL");
        }
        
        // 获取ReturnForm配置
        $returnForm = defined('SEGMENT_COMMON_RETURN_FORM') ? SEGMENT_COMMON_RETURN_FORM : 'crop';
        writeLog("通用分割返回格式: $returnForm");
        
        // 创建请求
        $request = new SegmentCommonImageRequest([
            "imageURL" => $imageUrl,
            "returnForm" => $returnForm
        ]);
        
        // 创建RuntimeOptions
        $runtime = new \AlibabaCloud\Tea\Utils\Utils\RuntimeOptions([
            "ignoreSSL" => true,
            "autoretry" => false,
            "maxAttempts" => 3
        ]);
        
        // 记录调用类型
        writeLog("调用通用分割API SegmentCommonImage - 使用分割类型: " . (isset($GLOBALS['segment_type']) ? $GLOBALS['segment_type'] : 'undefined'));
        
        // 调用API
        writeLog("调用通用分割API SegmentCommonImage");
        $resp = $client->segmentCommonImageWithOptions($request, $runtime);
        writeLog("API调用成功，响应: " . json_encode($resp));
        
        // 提取结果URL
        if (isset($resp->body->data) && isset($resp->body->data->imageURL)) {
            $segmentedUrl = $resp->body->data->imageURL;
            writeLog("获取到分割后的图片URL: " . $segmentedUrl);
            
            // 下载分割后的图片
            writeLog("尝试下载分割后的图片");
            $imageContent = file_get_contents($segmentedUrl);
            if ($imageContent !== false) {
                // 保存到本地
                writeLog("尝试保存分割后的图片到: " . $savePath);
                if (file_put_contents($savePath, $imageContent)) {
                    writeLog("分割后的图片保存成功");
                    
                    // 返回分割后图片的URL
                    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
                    $host = $_SERVER['HTTP_HOST'];
                    $baseUrl = $protocol . $host;
                    $baseUrl = rtrim($baseUrl, '/') . '/';
                    $resultUrl = $baseUrl . 'login_backend/' . $savePath;
                    writeLog("返回分割后图片URL: " . $resultUrl);
                    return $resultUrl;
                } else {
                    writeLog("分割后的图片保存失败");
                }
            } else {
                writeLog("无法下载分割后的图片: " . $segmentedUrl);
            }
        } else {
            writeLog("通用分割API响应中未找到data.imageURL字段，响应内容: " . json_encode($resp->body));
        }
        
        return false;
    } catch (\Throwable $t) {
        writeLog("通用分割API调用失败: " . $t->getMessage() . "\n" . $t->getTraceAsString());
        return false;
    }
}

/**
 * 创建阿里云客户端
 * 
 * @param string $accessKeyId
 * @param string $accessKeySecret
 * @return Imageseg 客户端实例
 */
function createClient($accessKeyId, $accessKeySecret) {
    writeLog("创建阿里云客户端配置");
    
    try {
        // 基本配置信息
        $configArray = [
            "accessKeyId" => $accessKeyId,
            "accessKeySecret" => $accessKeySecret,
            "regionId" => "cn-shanghai", 
            "type" => "access_key",
            "protocol" => "https",
            "connectTimeout" => 10000,
            "readTimeout" => 10000
        ];
        
        writeLog("创建配置: " . json_encode($configArray, JSON_UNESCAPED_UNICODE));
        
        // 初始化配置
        $config = new Config($configArray);
        
        // 访问的域名
        $config->endpoint = "imageseg.cn-shanghai.aliyuncs.com";
        writeLog("配置创建成功，endpoint: " . $config->endpoint);
        
        // 创建客户端实例
        $client = new Imageseg($config);
        writeLog("客户端实例创建成功");
        
        return $client;
    } catch (\Exception $e) {
        writeLog("创建阿里云客户端异常: " . $e->getMessage() . "\n堆栈跟踪: " . $e->getTraceAsString());
        throw $e; // 重新抛出异常，让上层捕获
    }
}

error_log("===== 图片上传处理完成 =====");

// 获取当前缓冲区内容并保存
$output = ob_get_contents();
writeLog("准备输出响应: " . $output);

// 确保之前没有任何输出被发送
if (ob_get_level()) {
    ob_end_clean();
}

// 重新设置响应头，确保是JSON
header('Content-Type: application/json');

// 再次输出保存的响应
if (!empty(trim($output))) {
    echo $output;
    writeLog("已输出响应内容");
} else {
    // 如果输出为空，生成默认响应
    writeLog("警告: 响应内容为空，生成默认响应");
    
    // 安全检查，确保$imageUrl已定义
    $defaultImageUrl = isset($imageUrl) ? $imageUrl : "未知图片URL";
    
    echo json_encode([
        'error' => false,
        'data' => [
            'image_url' => $defaultImageUrl,
            'message' => '处理完成但响应为空，返回默认图片URL'
        ]
    ]);
}
?> 