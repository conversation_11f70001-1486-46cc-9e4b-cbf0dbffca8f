// 圈子数据共享页面
// 模块4：数据共享基础模块

const app = getApp();

Page({
  data: {
    loading: true,
    syncing: false,
    
    // 圈子信息
    circleInfo: {
      circle_id: null,
      circle_name: '',
      user_role: ''
    },
    
    // 统计信息
    stats: {
      total_wardrobes: 0,
      total_clothes: 0,
      total_outfits: 0,
      total_members: 0
    },
    
    // 标签页
    currentTab: 'wardrobes',
    
    // 数据
    wardrobes: [],
    clothes: [],
    outfits: [],

    // 过滤后的数据
    filteredWardrobes: [],
    filteredClothes: [],
    filteredOutfits: [],
    
    // 过滤器
    dataFilter: 'all', // all, shared, personal
    categoryFilter: 'all',
    outfitCategoryFilter: 'all',
    
    // 统计数据
    categoryStats: [],
    outfitCategoryStats: [],
    
    // 同步弹框
    showSyncModal: false,
    syncOptions: {
      wardrobes: true,
      clothes: true,
      outfits: true
    },
    syncType: 'initial' // incremental, initial
  },

  onLoad: function(options) {
    console.log('数据共享页面加载', options);
    this.loadSharedData();

    // 如果有showSync参数，自动显示同步弹框
    if (options.showSync === 'true') {
      setTimeout(() => {
        this.showSyncModal();
      }, 500); // 延迟500ms确保页面加载完成
    }
  },

  onShow: function() {
    // 每次显示页面时刷新数据
    this.loadSharedData();
  },

  // 加载共享数据
  loadSharedData: function() {
    this.setData({ loading: true });
    
    // 根据当前标签页加载对应数据
    switch (this.data.currentTab) {
      case 'wardrobes':
        this.loadWardrobes();
        break;
      case 'clothes':
        this.loadClothes();
        break;
      case 'outfits':
        this.loadOutfits();
        break;
    }
  },

  // 加载衣橱数据
  loadWardrobes: function() {
    wx.request({
      url: app.globalData.apiBaseUrl + '/get_circle_wardrobes.php',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('获取圈子衣橱响应:', res.data);
        
        if (res.data.status === 'success') {
          this.setData({
            circleInfo: res.data.data.circle_info,
            wardrobes: res.data.data.wardrobes,
            stats: res.data.data.stats,
            loading: false
          });
          this.updateFilteredData();

          // 检查是否需要提示用户同步数据
          this.checkSyncNeeded();
        } else {
          wx.showToast({
            title: res.data.message || '加载失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: (err) => {
        console.error('获取圈子衣橱失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  // 加载衣物数据
  loadClothes: function() {
    wx.request({
      url: app.globalData.apiBaseUrl + '/get_circle_clothes.php',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: this.data.categoryFilter === 'all' ? {} : {
        category: this.data.categoryFilter
      },
      success: (res) => {
        console.log('获取圈子衣物响应:', res.data);
        
        if (res.data.status === 'success') {
          // 处理分类统计数据，添加中文显示名称
          const processedCategoryStats = (res.data.data.category_stats || []).map(item => ({
            ...item,
            displayName: this.getCategoryDisplayName(item.category)
          }));

          this.setData({
            circleInfo: res.data.data.circle_info,
            clothes: res.data.data.clothes,
            categoryStats: processedCategoryStats,
            loading: false
          });
          this.updateFilteredData();
        } else {
          wx.showToast({
            title: res.data.message || '加载失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: (err) => {
        console.error('获取圈子衣物失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  // 加载穿搭数据
  loadOutfits: function() {
    wx.request({
      url: app.globalData.apiBaseUrl + '/get_circle_outfits.php',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: this.data.outfitCategoryFilter === 'all' ? {} : {
        category: this.data.outfitCategoryFilter
      },
      success: (res) => {
        console.log('获取圈子穿搭响应:', res.data);
        
        if (res.data.status === 'success') {
          // 处理穿搭数据，转换为与穿搭列表页面兼容的格式
          const processedOutfits = this.processOutfitsData(res.data.data.outfits);

          // 处理穿搭分类统计数据，添加中文显示名称
          const processedOutfitCategoryStats = (res.data.data.category_stats || []).map(item => ({
            ...item,
            displayName: this.getCategoryDisplayName(item.category)
          }));

          this.setData({
            circleInfo: res.data.data.circle_info,
            outfits: processedOutfits,
            outfitCategoryStats: processedOutfitCategoryStats,
            loading: false
          });
          this.updateFilteredData();
        } else {
          wx.showToast({
            title: res.data.message || '加载失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: (err) => {
        console.error('获取圈子穿搭失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  // 切换标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ 
      currentTab: tab,
      loading: true
    });
    this.loadSharedData();
  },

  // 获取分类中文名称
  getCategoryDisplayName: function(categoryCode) {
    // 系统预设分类映射
    const systemCategoryMap = {
      'tops': '上衣',
      'pants': '裤子',
      'skirts': '裙子',
      'coats': '外套',
      'shoes': '鞋子',
      'bags': '包包',
      'accessories': '配饰',
      'dresses': '连衣裙',
      'underwear': '内衣',
      'sportswear': '运动装',
      'sleepwear': '睡衣',
      'swimwear': '泳装'
    };

    // 如果是系统分类，返回中文名称
    if (systemCategoryMap[categoryCode]) {
      return systemCategoryMap[categoryCode];
    }

    // 如果是自定义分类（通常以custom_开头），提取并格式化名称
    if (categoryCode && categoryCode.startsWith('custom_')) {
      // 提取自定义分类名称部分
      const customName = categoryCode.replace('custom_', '').replace(/_\d+$/, '');
      return customName || '自定义分类';
    }

    // 其他情况返回原始代码或默认名称
    return categoryCode || '未分类';
  },

  // 设置分类过滤器
  setCategoryFilter: function(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({ 
      categoryFilter: category,
      loading: true
    });
    this.loadClothes();
  },

  // 设置穿搭分类过滤器
  setOutfitCategoryFilter: function(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({ 
      outfitCategoryFilter: category,
      loading: true
    });
    this.loadOutfits();
  },

  // 显示同步弹框
  showSyncModal: function() {
    this.setData({ showSyncModal: true });
  },

  // 隐藏同步弹框
  hideSyncModal: function(e) {
    // 只有点击overlay背景时才关闭弹框
    if (e && e.target && e.currentTarget && e.target === e.currentTarget) {
      this.setData({ showSyncModal: false });
    }
  },

  // 强制关闭弹框（用于取消按钮）
  forceHideSyncModal: function() {
    this.setData({ showSyncModal: false });
  },

  // 同步选项变化
  onSyncOptionsChange: function(e) {
    const values = e.detail.value;
    const syncOptions = {
      wardrobes: values.includes('wardrobes'),
      clothes: values.includes('clothes'),
      outfits: values.includes('outfits')
    };
    this.setData({ syncOptions });
  },

  // 同步类型变化
  onSyncTypeChange: function(e) {
    this.setData({ syncType: e.detail.value });
  },

  // 测试穿搭解析
  testOutfitParsing: function() {
    // 获取第一个穿搭的ID进行测试
    const outfits = this.data.outfits;
    if (outfits.length === 0) {
      wx.showToast({
        title: '没有穿搭数据可测试',
        icon: 'none'
      });
      return;
    }

    const firstOutfit = outfits[0];

    wx.request({
      url: app.globalData.apiBaseUrl + '/test_outfit_parsing.php',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: {
        outfit_id: firstOutfit.id
      },
      success: (res) => {
        console.log('穿搭解析测试响应:', res.data);

        if (res.data.status === 'success') {
          const data = res.data.data;
          let message = `=== 穿搭解析测试 ===\n\n`;
          message += `穿搭: ${data.outfit_name} (ID: ${data.outfit_id})\n`;
          message += `JSON有效: ${data.json_valid ? '是' : '否'}\n`;
          message += `总项目数: ${data.summary.total_items}\n`;
          message += `有效衣物: ${data.summary.valid_clothes}\n\n`;

          if (data.clothes.length > 0) {
            message += `衣物详情:\n`;
            data.clothes.forEach((item, index) => {
              if (item.parsed_clothes) {
                message += `${index + 1}. ${item.parsed_clothes.clothes_name} (${item.parsed_clothes.category})\n`;
              }
            });
          }

          message += `\n详细信息请查看控制台`;

          wx.showModal({
            title: '解析测试结果',
            content: message,
            showCancel: false
          });
        } else {
          wx.showToast({
            title: res.data.message || '测试失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('穿搭解析测试失败:', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 测试修复后的查询
  testQueries: function() {
    console.log('测试修复后的查询...');

    // 强制重新加载数据，测试修复效果
    this.setData({
      loading: true,
      categoryFilter: 'all',
      outfitCategoryFilter: 'all'
    });

    // 重新加载所有数据
    this.loadSharedData();

    wx.showToast({
      title: '正在测试修复效果',
      icon: 'loading',
      duration: 2000
    });
  },

  // 分析查询问题
  analyzeQueries: function() {
    wx.request({
      url: app.globalData.apiBaseUrl + '/analyze_circle_queries.php',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('查询分析响应:', res.data);

        if (res.data.status === 'success') {
          const data = res.data.data;
          const clothes = data.clothes_analysis;
          const outfits = data.outfits_analysis;

          let message = `=== 查询分析结果 ===\n\n`;
          message += `用户: ${data.user_info.user_id}\n`;
          message += `圈子: ${data.user_info.circle_name} (ID: ${data.user_info.circle_id})\n\n`;

          message += `=== 衣物分析 ===\n`;
          message += `统计查询结果: ${clothes.category_stats_with_join.length}个分类\n`;
          message += `主查询结果: ${clothes.main_query_count}条记录\n`;
          message += `圈子共享衣物: ${clothes.step_tests.circle_shared_clothes}件\n`;
          message += `成员个人衣物: ${clothes.step_tests.members_personal_clothes}件\n`;
          message += `圈子成员: ${clothes.step_tests.circle_members.length}人\n\n`;

          message += `=== 穿搭分析 ===\n`;
          message += `统计查询结果: ${outfits.category_stats.length}个分类\n`;
          message += `主查询结果: ${outfits.main_query_count}条记录\n\n`;

          message += `详细信息请查看控制台日志`;

          wx.showModal({
            title: '查询分析',
            content: message,
            showCancel: false
          });
        } else {
          wx.showToast({
            title: res.data.message || '分析失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('查询分析失败:', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 调试圈子数据
  debugCircleData: function() {
    wx.request({
      url: app.globalData.apiBaseUrl + '/debug_circle_data.php',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('调试数据响应:', res.data);

        if (res.data.status === 'success') {
          const data = res.data.data;
          let message = `用户: ${data.user.nickname}\n`;
          message += `衣物: 总${data.user_data.clothes.total}件 (个人${data.user_data.clothes.personal}件, 共享${data.user_data.clothes.shared}件)\n`;
          message += `穿搭: 总${data.user_data.outfits.total}件 (个人${data.user_data.outfits.personal}件, 共享${data.user_data.outfits.shared}件)\n`;
          message += `衣橱: 总${data.user_data.wardrobes.total}个 (个人${data.user_data.wardrobes.personal}个, 共享${data.user_data.wardrobes.shared}个)\n`;

          if (data.circle_data) {
            message += `\n圈子: ${data.circle_data.circle_info.circle_name}\n`;
            message += `圈子衣物: ${data.circle_data.clothes_count}件\n`;
            message += `圈子穿搭: ${data.circle_data.outfits_count}件\n`;
            message += `圈子成员: ${data.circle_data.members.length}人`;
          } else {
            message += '\n未加入圈子';
          }

          wx.showModal({
            title: '调试信息',
            content: message,
            showCancel: false
          });
        } else {
          wx.showToast({
            title: res.data.message || '调试失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('调试失败:', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 开始同步
  startSync: function() {
    const { syncOptions, syncType } = this.data;
    
    // 检查是否选择了同步选项
    const selectedTypes = Object.keys(syncOptions).filter(key => syncOptions[key]);
    if (selectedTypes.length === 0) {
      wx.showToast({
        title: '请选择要同步的数据类型',
        icon: 'none'
      });
      return;
    }

    this.setData({ syncing: true });

    wx.request({
      url: app.globalData.apiBaseUrl + '/sync_user_data_to_circle.php',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: {
        sync_type: syncType,
        data_types: selectedTypes
      },
      success: (res) => {
        console.log('数据同步响应:', res.data);
        
        if (res.data.status === 'success') {
          wx.showToast({
            title: '数据同步完成',
            icon: 'success'
          });
          
          this.setData({ 
            showSyncModal: false,
            syncing: false
          });
          
          // 刷新当前数据
          this.loadSharedData();
        } else {
          wx.showToast({
            title: res.data.message || '同步失败',
            icon: 'none'
          });
          this.setData({ syncing: false });
        }
      },
      fail: (err) => {
        console.error('数据同步失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ syncing: false });
      }
    });
  },

  // 查看衣橱详情
  viewWardrobeDetail: function(e) {
    const wardrobe = e.currentTarget.dataset.wardrobe;
    wx.navigateTo({
      url: `/pages/wardrobes/detail/detail?id=${wardrobe.id}&from=circle`
    });
  },

  // 查看衣物详情
  viewClothesDetail: function(e) {
    const clothes = e.currentTarget.dataset.clothes;
    wx.navigateTo({
      url: `/pages/clothes/detail/detail?id=${clothes.id}&from=circle`
    });
  },

  // 查看穿搭详情
  viewOutfitDetail: function(e) {
    const outfit = e.currentTarget.dataset.outfit;
    wx.navigateTo({
      url: `/pages/outfits/detail/detail?id=${outfit.id}&from=circle`
    });
  },

  // 设置数据过滤器并更新过滤结果
  setDataFilter: function(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({ dataFilter: filter });
    this.updateFilteredData();
  },

  // 更新过滤后的数据
  updateFilteredData: function() {
    const { wardrobes, clothes, outfits, dataFilter } = this.data;

    let filteredWardrobes = wardrobes;
    let filteredClothes = clothes;
    let filteredOutfits = outfits;

    if (dataFilter === 'shared') {
      filteredWardrobes = wardrobes.filter(w => w.is_shared);
      filteredClothes = clothes.filter(c => c.is_shared);
      filteredOutfits = outfits.filter(o => o.is_shared);
    } else if (dataFilter === 'personal') {
      filteredWardrobes = wardrobes.filter(w => !w.is_shared);
      filteredClothes = clothes.filter(c => !c.is_shared);
      filteredOutfits = outfits.filter(o => !o.is_shared);
    }

    this.setData({
      filteredWardrobes,
      filteredClothes,
      filteredOutfits
    });
  },

  // 检查是否需要提示用户同步数据
  checkSyncNeeded: function() {
    const { wardrobes, clothes, outfits } = this.data;

    // 如果所有数据都为空，可能需要同步
    if (wardrobes.length === 0 && clothes.length === 0 && outfits.length === 0) {
      // 调用调试API检查用户是否有个人数据
      wx.request({
        url: app.globalData.apiBaseUrl + '/debug_circle_data.php',
        method: 'GET',
        header: {
          'Authorization': 'Bearer ' + app.globalData.token,
          'Content-Type': 'application/json'
        },
        success: (res) => {
          if (res.data.status === 'success') {
            const userData = res.data.data.user_data;
            const hasPersonalData = userData.clothes.personal > 0 ||
                                   userData.outfits.personal > 0 ||
                                   userData.wardrobes.personal > 0;

            if (hasPersonalData) {
              // 用户有个人数据但没有共享数据，提示同步
              wx.showModal({
                title: '数据同步提示',
                content: `检测到您有${userData.clothes.personal}件衣物、${userData.outfits.personal}件穿搭、${userData.wardrobes.personal}个衣橱还未同步到圈子。是否现在同步？`,
                confirmText: '立即同步',
                cancelText: '稍后再说',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    this.showSyncModal();
                  }
                }
              });
            }
          }
        },
        fail: (err) => {
          console.error('检查同步需求失败:', err);
        }
      });
    }
  },

  // 处理穿搭数据，转换为与穿搭列表页面兼容的格式
  processOutfitsData: function(outfits) {
    console.log('开始处理穿搭数据:', outfits.length, '个穿搭');

    return outfits.map(outfit => {
      // 基础数据
      const processedOutfit = {
        ...outfit,
        thumbnail: outfit.thumbnail_url, // 兼容穿搭列表的字段名
        items: []
      };

      // 处理outfit_data，转换为items格式
      if (outfit.outfit_data && outfit.outfit_data.items) {
        processedOutfit.items = outfit.outfit_data.items.map((item, index) => {
          // 使用与穿搭列表相同的位置计算逻辑
          const containerSize = 180; // 容器尺寸（px，与outfit-view-mini一致）
          const previewScale = 0.3; // 缩放比例

          // 原始位置和尺寸
          const originalX = item.position?.x || 0;
          const originalY = item.position?.y || 0;
          const originalWidth = item.size?.width || 150;
          const originalHeight = item.size?.height || 200;

          // 计算缩放后的位置，相对于容器中心
          const scaledWidth = originalWidth * previewScale;
          const scaledHeight = originalHeight * previewScale;

          // 将位置转换为相对于容器中心的坐标
          const centerX = containerSize / 2;
          const centerY = containerSize / 2;

          const previewX = (originalX * previewScale) - (scaledWidth / 2) + centerX - containerSize / 2;
          const previewY = (originalY * previewScale) - (scaledHeight / 2) + centerY - containerSize / 2;

          const previewPosition = {
            x: previewX,
            y: previewY,
            scale: previewScale
          };

          return {
            clothing_id: item.clothing_id,
            clothing_data: item.clothing_data || {},
            position: item.position || { x: 0, y: 0 },
            size: item.size || { width: 150, height: 200 },
            rotation: item.rotation || 0,
            z_index: item.z_index || index + 1,
            previewPosition: previewPosition
          };
        });

        // 按z_index排序，确保层级正确
        processedOutfit.items.sort((a, b) => a.z_index - b.z_index);

        console.log(`穿搭 ${outfit.name} 处理完成:`, {
          id: outfit.id,
          itemsCount: processedOutfit.items.length,
          items: processedOutfit.items.map(item => ({
            id: item.clothing_id,
            name: item.clothing_data.name,
            originalPos: item.position,
            previewPos: item.previewPosition,
            size: item.size,
            zIndex: item.z_index
          }))
        });
      }

      return processedOutfit;
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadSharedData();
    // 延迟停止下拉刷新动画
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  }
});
