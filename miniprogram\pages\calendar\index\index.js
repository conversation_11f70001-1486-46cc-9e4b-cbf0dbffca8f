const app = getApp();

Page({
  data: {
    year: new Date().getFullYear(), // 当前年份
    month: new Date().getMonth() + 1, // 当前月份
    selectedDate: '', // 选中的日期，格式为 YYYY-MM-DD
    days: [], // 当月日期数组
    outfitCalendarData: {}, // 穿搭日历数据，key为日期字符串，value为穿搭对象
    hasUserInfo: false, // 用户是否已登录
    loading: true, // 加载状态
    outfits: [], // 用户的所有穿搭
    currentDayOutfit: null, // 当前选中日期的穿搭
    showOutfitPopup: false, // 是否显示穿搭选择弹出层
    categories: [], // 穿搭分类列表
    currentCategoryId: 'all', // 当前选中的分类ID
    filteredOutfits: [], // 按分类筛选后的穿搭
  },

  onLoad: function() {
    // 检查用户登录状态
    if (app.globalData.userInfo) {
      this.setData({
        hasUserInfo: true
      });
    }
    
    // 初始化日历
    this.initCalendar();
    
    // 加载穿搭日历数据
    this.loadCalendarOutfits(() => {
      // 日历数据加载完成后，默认选中今天的日期
      const today = new Date();
      const todayStr = this.formatDate(today.getFullYear(), today.getMonth() + 1, today.getDate());
      
      console.log('默认选中今天:', todayStr);
      
      // 检查今天是否有穿搭
      const todayOutfit = this.data.outfitCalendarData[todayStr] || null;
      if (todayOutfit) {
        // 如果今天有穿搭，加载详情
        this.loadOutfitDetail(todayOutfit.id, todayStr);
      } else {
        // 只设置选中日期，不显示操作菜单
        this.setData({
          selectedDate: todayStr
        });
      }
    });
  },
  
  onShow: function() {
    // 检查登录状态
    if (app.globalData.userInfo && !this.data.hasUserInfo) {
      this.setData({
        hasUserInfo: true
      });
      this.loadCalendarOutfits();
    }
    
    // 检查是否需要刷新穿搭数据
    if (app.globalData.needRefreshOutfits) {
      app.globalData.needRefreshOutfits = false;
      this.loadCalendarOutfits();
    }
    
    // 检查是否有新保存的日历穿搭
    if (app.globalData.calendarOutfitUpdated && app.globalData.calendarOutfitData) {
      console.log('检测到新保存的日历穿搭，立即更新显示');
      
      const { outfitId, date, outfit } = app.globalData.calendarOutfitData;
      
      // 立即更新当前的outfitCalendarData
      if (outfit && date) {
        // 更新日历数据
        const outfitCalendarData = { ...this.data.outfitCalendarData };
        outfitCalendarData[date] = outfit;
        
        // 更新UI
        this.setData({
          outfitCalendarData: outfitCalendarData,
          selectedDate: date,
          currentDayOutfit: outfit,
          loading: false
        });
        
        console.log('已立即更新日历显示:', date, outfit.name);
      }
      
      // 清除标记和数据
      app.globalData.calendarOutfitUpdated = false;
      app.globalData.calendarOutfitData = null;
      
      // 仍然加载完整的日历数据，以确保所有数据都是最新的
      this.loadCalendarOutfits();
      
      // 不需要继续执行后面的代码
      return;
    }
    
    // 检查是否从创建/编辑穿搭页面返回
    // 这里通过检查selectedCalendarDate是否存在来判断
    if (app.globalData.selectedCalendarDate && !app.globalData.isSelectingForCalendar) {
      console.log('从穿搭创建/编辑页面返回，刷新日历数据');
      
      // 记录当前选中的日期
      const currentSelectedDate = app.globalData.selectedCalendarDate;
      
      // 加载日历数据
      this.loadCalendarOutfits(() => {
        // 数据加载完成后，重新选中之前的日期
        this.setData({
          selectedDate: currentSelectedDate
        });
        
        // 获取该日期的穿搭
        const dateOutfit = this.data.outfitCalendarData[currentSelectedDate];
        if (dateOutfit) {
          // 如果有穿搭，加载详情
          this.loadOutfitDetail(dateOutfit.id, currentSelectedDate);
        } else {
          // 清除当前显示的穿搭
          this.setData({
            currentDayOutfit: null
          });
        }
        
        // 清除全局变量
        app.globalData.selectedCalendarDate = null;
      });
    }
    
    // 检查是否从穿搭列表页面选择了穿搭
    if (app.globalData.isSelectingForCalendar && app.globalData.selectedOutfitForCalendar) {
      console.log('从穿搭列表返回，选择了穿搭:', app.globalData.selectedOutfitForCalendar);
      
      // 获取选中的日期和穿搭
      const selectedDate = app.globalData.selectedCalendarDate;
      const selectedOutfit = app.globalData.selectedOutfitForCalendar;
      
      if (selectedDate && selectedOutfit) {
        // 显示加载状态
        this.setData({ loading: true });
        
        // 发送请求，保存穿搭日期
        wx.request({
          url: `${app.globalData.apiBaseUrl}/save_calendar_outfit.php`,
          method: 'POST',
          header: {
            'Authorization': app.globalData.token,
            'Content-Type': 'application/json'
          },
          data: {
            outfit_id: selectedOutfit.id,
            calendar_date: selectedDate,
            action: 'add'
          },
          success: (res) => {
            console.log('保存穿搭日历响应:', res.data);
            
            if (res.statusCode === 200 && !res.data.error) {
              // 更新本地数据
              const outfitCalendarData = { ...this.data.outfitCalendarData };
              outfitCalendarData[selectedDate] = selectedOutfit;
              
              // 更新界面
              this.setData({
                outfitCalendarData,
                loading: false
              });
              
              // 加载完整的穿搭数据显示
              this.loadOutfitDetail(selectedOutfit.id, selectedDate);
              
              wx.showToast({
                title: '穿搭已安排',
                icon: 'success'
              });
            } else {
              console.error('保存穿搭日期失败:', res.data);
              this.setData({ loading: false });
              
              wx.showToast({
                title: '操作失败',
                icon: 'none'
              });
            }
          },
          fail: (err) => {
            console.error('请求失败:', err);
            this.setData({ loading: false });
            
            wx.showToast({
              title: '网络错误',
              icon: 'none'
            });
          }
        });
        
        // 清除全局变量
        app.globalData.selectedOutfitForCalendar = null;
      }
      
      // 重置选择状态
      app.globalData.isSelectingForCalendar = false;
    }
  },
  
  // 初始化日历
  initCalendar: function() {
    const year = this.data.year;
    const month = this.data.month;
    
    // 获取当月天数
    const daysInMonth = new Date(year, month, 0).getDate();
    
    // 获取当月第一天是周几
    const firstDayOfWeek = new Date(year, month - 1, 1).getDay();
    
    let days = [];
    
    // 添加上个月的日期
    const prevMonthDays = new Date(year, month - 1, 0).getDate();
    for (let i = 0; i < firstDayOfWeek; i++) {
      days.push({
        day: prevMonthDays - firstDayOfWeek + i + 1,
        month: month - 1 <= 0 ? 12 : month - 1,
        year: month - 1 <= 0 ? year - 1 : year,
        currentMonth: false,
        date: this.formatDate(month - 1 <= 0 ? year - 1 : year, month - 1 <= 0 ? 12 : month - 1, prevMonthDays - firstDayOfWeek + i + 1)
      });
    }
    
    // 添加当月的日期
    for (let i = 1; i <= daysInMonth; i++) {
      days.push({
        day: i,
        month: month,
        year: year,
        currentMonth: true,
        date: this.formatDate(year, month, i),
        isToday: i === new Date().getDate() && month === new Date().getMonth() + 1 && year === new Date().getFullYear()
      });
    }
    
    // 计算需要补充的下个月天数
    const totalDaysShown = Math.ceil((firstDayOfWeek + daysInMonth) / 7) * 7;
    const nextMonthDays = totalDaysShown - days.length;
    
    // 添加下个月的日期
    for (let i = 1; i <= nextMonthDays; i++) {
      days.push({
        day: i,
        month: month + 1 > 12 ? 1 : month + 1,
        year: month + 1 > 12 ? year + 1 : year,
        currentMonth: false,
        date: this.formatDate(month + 1 > 12 ? year + 1 : year, month + 1 > 12 ? 1 : month + 1, i)
      });
    }
    
    this.setData({
      days: days
    });
  },
  
  // 格式化日期为 YYYY-MM-DD
  formatDate: function(year, month, day) {
    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
  },
  
  // 加载穿搭日历数据
  loadCalendarOutfits: function(callback) {
    if (!app.globalData.token) {
      this.setData({ loading: false });
      if (callback) callback();
      return;
    }
    
    this.setData({ loading: true });
    
    // 加载当前月份的穿搭日历数据
    const year = this.data.year;
    const month = this.data.month;
    const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
    const endDate = `${year}-${month.toString().padStart(2, '0')}-${new Date(year, month, 0).getDate().toString().padStart(2, '0')}`;
    
    console.log('获取日历穿搭数据范围:', startDate, '至', endDate);
    
    // 增加一个时间戳参数，确保每次请求都获取最新数据，避免缓存问题
    const timestamp = Date.now();
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_calendar_outfits.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      data: {
        start_date: startDate,
        end_date: endDate,
        _t: timestamp // 添加时间戳参数避免缓存
      },
      success: (res) => {
        console.log('日历穿搭数据返回:', res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          const calendarData = {};
          const outfits = res.data.data || [];
          
          console.log('获取到的穿搭数量:', outfits.length);
          
          // 将穿搭数据整理成以日期为key的对象
          outfits.forEach(outfit => {
            if (outfit.calendar_date) {
              // 确保每个穿搭对象有items字段
              if (!outfit.items) {
                try {
                  if (outfit.outfit_data) {
                    const outfitData = typeof outfit.outfit_data === 'string' 
                      ? JSON.parse(outfit.outfit_data) 
                      : outfit.outfit_data;
                    outfit.items = outfitData.items || [];
                  } else {
                    outfit.items = [];
                  }
                } catch (e) {
                  console.error('解析穿搭数据失败:', e, outfit);
                  outfit.items = [];
                }
              }
              
              calendarData[outfit.calendar_date] = outfit;
              console.log('添加日期穿搭:', outfit.calendar_date, outfit.name);
            }
          });
          
          this.setData({
            outfitCalendarData: calendarData,
            loading: false
          });
          
          console.log('日历穿搭数据已更新');
        } else {
          console.error('获取穿搭日历数据失败:', res.data);
          this.setData({ loading: false });
        }
      },
      fail: (err) => {
        console.error('请求穿搭日历数据失败:', err);
        this.setData({ loading: false });
      },
      complete: () => {
        // 完成后执行回调
        if (callback) callback();
      }
    });
    
    // 加载用户所有穿搭，用于选择器
    app.getOutfits((outfits) => {
      console.log('已加载所有穿搭数据用于选择器:', outfits.length);
      this.setData({ outfits });
    });
  },
  
  // 切换月份
  changeMonth: function(e) {
    const direction = e.currentTarget.dataset.direction;
    let { year, month } = this.data;
    
    if (direction === 'prev') {
      if (month === 1) {
        year--;
        month = 12;
      } else {
        month--;
      }
    } else {
      if (month === 12) {
        year++;
        month = 1;
      } else {
        month++;
      }
    }
    
    const currentSelectedDate = this.data.selectedDate;
    const currentDayOutfit = this.data.currentDayOutfit;
    
    this.setData({
      year,
      month
    }, () => {
      this.initCalendar();
      this.loadCalendarOutfits();
      
      // 如果之前有选中的日期和穿搭，保留它们
      if (currentSelectedDate && currentDayOutfit) {
        this.setData({
          selectedDate: currentSelectedDate,
          currentDayOutfit: currentDayOutfit
        });
      }
    });
  },
  
  // 点击日期
  tapDay: function(e) {
    const { date } = e.currentTarget.dataset;
    console.log('点击日期:', date);
    
    const outfit = this.data.outfitCalendarData[date] || null;
    console.log('日期关联的穿搭:', outfit);
    
    // 设置选中日期
    this.setData({
      selectedDate: date
    });
    
    // 如果有穿搭，需要获取完整穿搭数据
    if (outfit) {
      console.log('加载日期穿搭详情:', outfit.id);
      this.loadOutfitDetail(outfit.id, date);
    } else {
      // 清除当前显示的穿搭
      this.setData({
        currentDayOutfit: null
      });
    }
  },
  
  // 加载穿搭详情
  loadOutfitDetail: function(outfitId, date) {
    this.setData({ loading: true });
    
    console.log('开始加载穿搭详情', outfitId, date);
    
    // 先从本地缓存查找
    const outfits = app.getOutfits();
    console.log('本地穿搭列表', outfits);
    
    let outfit = outfits.find(item => item.id === outfitId);
    console.log('在本地找到的穿搭:', outfit);
    
    if (outfit && outfit.items && outfit.items.length > 0) {
      // 本地有完整数据
      console.log('使用本地穿搭数据');
      
      // 优化衣物位置显示
      this.optimizeOutfitItemsPosition(outfit);
      
      // 检查服装数据是否完整
      outfit.items.forEach((item, index) => {
        console.log(`衣物 ${index}:`, item.clothing_id, '图片URL:', item.clothing_data?.image_url || '缺失');
        if (!item.clothing_data || !item.clothing_data.image_url) {
          console.warn('衣物数据不完整:', item);
        }
      });
      
      this.setData({
        selectedDate: date,
        currentDayOutfit: outfit,
        loading: false
      });
      return;
    }
    
    // 本地没有完整数据，从服务器获取
    console.log('从服务器获取穿搭数据');
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_outfits.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      data: {
        outfit_id: outfitId
      },
      success: (res) => {
        console.log('服务器返回数据:', res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data && res.data.data.length > 0) {
          outfit = res.data.data[0];
          
          // 确保outfit是对象
          if (typeof outfit !== 'object') {
            console.error('穿搭数据不是对象:', outfit);
            this.setData({
              loading: false
            });
            return;
          }
          
          // 处理穿搭数据
          console.log('处理服务器返回的穿搭数据:', outfit);
          
          // 检查items是否存在
          if (!outfit.items) {
            // 如果没有items字段，尝试从outfit_data解析
            if (outfit.outfit_data) {
              try {
                const outfitData = typeof outfit.outfit_data === 'string' 
                  ? JSON.parse(outfit.outfit_data) 
                  : outfit.outfit_data;
                
                outfit.items = outfitData.items || [];
                console.log('从outfit_data解析出items:', outfit.items);
              } catch (e) {
                console.error('解析穿搭数据失败:', e);
                outfit.items = [];
              }
            } else {
              // 如果没有outfit_data，设置为空数组
              outfit.items = [];
            }
          }
          
          // 优化衣物位置显示
          this.optimizeOutfitItemsPosition(outfit);
          
          // 更新本地数据
          this.setData({
            selectedDate: date,
            currentDayOutfit: outfit,
            loading: false
          });
          
          console.log('已设置currentDayOutfit:', this.data.currentDayOutfit);
        } else {
          console.error('获取穿搭详情失败:', res.data);
          // 使用简单数据显示
          const simpleOutfit = this.data.outfitCalendarData[date] || {
            id: outfitId,
            name: '未知穿搭',
            items: []
          };
          
          this.setData({
            selectedDate: date,
            currentDayOutfit: simpleOutfit,
            loading: false
          });
        }
      },
      fail: (err) => {
        console.error('请求穿搭详情失败:', err);
        const simpleOutfit = this.data.outfitCalendarData[date] || {
          id: outfitId,
          name: '未知穿搭',
          items: []
        };
        
        this.setData({
          selectedDate: date,
          currentDayOutfit: simpleOutfit,
          loading: false
        });
      }
    });
  },
  
  // 新增：优化穿搭衣物位置，确保在日历预览中完整显示
  optimizeOutfitItemsPosition: function(outfit) {
    if (!outfit || !outfit.items || outfit.items.length === 0) return;
    
    console.log('使用简化版垂直居中算法优化穿搭显示');
    
    try {
      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();
      const screenWidth = systemInfo.windowWidth;
      
      // 容器尺寸计算 - 使用屏幕宽度的90%作为容器宽度
      const containerWidth = screenWidth * 0.9;
      // 使用固定高度350px，与CSS中的outfit-canvas高度匹配
      const containerHeight = 350;
      
      // 容器中心点
      const centerX = containerWidth / 2;
      const centerY = containerHeight / 2;
      
      console.log('容器尺寸:', containerWidth, 'x', containerHeight, '中心点:', centerX, centerY);
      
      // 找出所有衣物的总体边界
      let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
      
      // 确保每个衣物有合法的尺寸并计算边界
      outfit.items.forEach(item => {
        if (!item.size) {
          item.size = { width: 100, height: 150 };
        }
        if (!item.position) {
          item.position = { x: 0, y: 0 };
        }
        
        // 计算衣物的边界
        const left = item.position.x;
        const top = item.position.y;
        const right = left + item.size.width;
        const bottom = top + item.size.height;
        
        // 更新整体边界
        minX = Math.min(minX, left);
        minY = Math.min(minY, top);
        maxX = Math.max(maxX, right);
        maxY = Math.max(maxY, bottom);
      });
      
      // 计算边界框的宽度和高度
      const boundingWidth = maxX - minX;
      const boundingHeight = maxY - minY;
      
      // 计算边界框的中心点
      const boundingCenterX = minX + boundingWidth / 2;
      const boundingCenterY = minY + boundingHeight / 2;
      
      // 决定缩放因子 - 确保完整显示所有衣物
      const scaleX = (containerWidth * 0.8) / boundingWidth;
      const scaleY = (containerHeight * 0.8) / boundingHeight;
      
      // 选择较小的缩放因子，确保衣物能完整显示
      const scale = Math.min(scaleX, scaleY, 0.8);
      
      console.log('缩放计算:', 
                 '边界尺寸:', boundingWidth, 'x', boundingHeight, 
                 '目标尺寸:', containerWidth * 0.8, 'x', containerHeight * 0.8,
                 '缩放比例:', scale);
      
      // 为每个衣物计算新的位置
      outfit.items.forEach(item => {
        // 计算相对于边界中心点的偏移
        const offsetX = (item.position.x + item.size.width / 2) - boundingCenterX;
        const offsetY = (item.position.y + item.size.height / 2) - boundingCenterY;
        
        // 计算新的位置和尺寸
        const newWidth = item.size.width * scale;
        const newHeight = item.size.height * scale;
        const newX = centerX + offsetX * scale - newWidth / 2;
        const newY = centerY + offsetY * scale - newHeight / 2;
        
        // 存储新的位置和尺寸
        item.calendarPosition = {
          x: newX,
          y: newY
        };
        
        item.calendarSize = {
          width: newWidth,
          height: newHeight
        };
        
        console.log('衣物项新位置:', 
                   'id:', item.clothing_id, 
                   'pos:', newX, newY, 
                   'size:', newWidth, newHeight);
      });
      
      console.log('垂直居中算法完成');
    } catch (error) {
      console.error('优化穿搭位置出错:', error);
    }
    
    return outfit;
  },
  
  // 新增穿搭按钮点击事件
  addOutfitTap: function() {
    console.log('添加穿搭按钮点击事件触发');
    console.log('当前选中日期:', this.data.selectedDate);
    console.log('当前日期穿搭:', this.data.currentDayOutfit);
    
    // 如果当前选中日期没有穿搭，打开穿搭选择弹出层
    if (this.data.selectedDate && !this.data.currentDayOutfit) {
      // 存储当前选中的日期到全局变量以备后用
      app.globalData.selectedCalendarDate = this.data.selectedDate;
      
      // 加载穿搭数据并显示弹出层
      this.loadOutfitsForPopup();
    } else {
      console.log('条件不满足，无法添加穿搭:', this.data.selectedDate, !this.data.currentDayOutfit);
    }
  },
  
  // 加载穿搭数据用于弹出层
  loadOutfitsForPopup: function() {
    // 显示加载状态
    this.setData({
      loading: true,
      showOutfitPopup: true,
      currentCategoryId: 'all' // 重置为全部分类
    });
    
    // 调整弹出框样式
    this.adjustPopupStyle();
    
    // 加载分类数据
    this.loadOutfitCategories();
    
    // 从全局获取穿搭数据
    app.getOutfits((outfits) => {
      console.log('加载穿搭数据用于选择器:', outfits.length);
      
      // 处理每个穿搭的数据，确保位置信息正确
      if (outfits && outfits.length > 0) {
        outfits.forEach(outfit => {
          // 确保每个衣物有clothing_data属性
          if (outfit.items && outfit.items.length > 0) {
            outfit.items.forEach(item => {
              // 防止clothing_data不存在
              if (!item.clothing_data) {
                item.clothing_data = {
                  image_url: '/images/outfit_placeholder.png'
                };
              }
              
              // 防止image_url不存在
              if (!item.clothing_data.image_url) {
                item.clothing_data.image_url = '/images/outfit_placeholder.png';
              }
            });
            
            // 计算所有衣物的边界框
            let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
            
            // 首先确保每个衣物项有正确的位置信息，并计算边界
            outfit.items.forEach(item => {
              // 防止位置信息缺失导致显示异常
              if (!item.position) {
                item.position = { x: 50, y: 50 };
              }
              if (!item.size) {
                item.size = { width: 100, height: 120 };
              }
              
              // 计算衣物的边界
              const left = item.position.x;
              const top = item.position.y;
              const right = left + item.size.width;
              const bottom = top + item.size.height;
              
              // 更新整体边界
              minX = Math.min(minX, left);
              minY = Math.min(minY, top);
              maxX = Math.max(maxX, right);
              maxY = Math.max(maxY, bottom);
            });
            
            // 计算边界框的宽度和高度
            const boundingWidth = maxX - minX;
            const boundingHeight = maxY - minY;
            
            // 计算边界框的中心点
            const centerX = minX + boundingWidth / 2;
            const centerY = minY + boundingHeight / 2;
            
            // 为每个衣物计算预览列表中的居中坐标
            // 假设预览容器的中心点是 (35, 35)，因为outfit-preview是70px高宽
            const previewCenterX = 35;
            const previewCenterY = 35;
            
            // 计算最合适的缩放比例，确保整个穿搭都在预览框内
            const maxDimension = Math.max(boundingWidth, boundingHeight);
            const maxAllowedSize = 60; // 预留一些边距
            const scale = maxDimension > 0 ? Math.min(maxAllowedSize / maxDimension, 0.5) : 0.5;
            
            // 为每个衣物添加预览坐标，用于在列表页显示
            outfit.items.forEach(item => {
              item.previewPosition = {
                x: previewCenterX + (item.position.x - centerX) * scale,
                y: previewCenterY + (item.position.y - centerY) * scale,
                scale: scale
              };
            });
          }
          
          // 格式化日期显示
          if (outfit.created_at) {
            try {
              // 处理iOS不兼容的日期格式进行显示
              let dateStr = outfit.created_at;
              let dateObj;
              
              // 尝试转换为iOS兼容格式
              if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateStr)) {
                dateStr = dateStr.replace(' ', 'T');
              }
              
              dateObj = new Date(dateStr);
              
              if (!isNaN(dateObj.getTime())) {
                const year = dateObj.getFullYear();
                const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
                const day = dateObj.getDate().toString().padStart(2, '0');
                outfit.formatted_date = `${year}/${month}/${day}`;
              } else {
                outfit.formatted_date = outfit.created_at.split(' ')[0].replace(/-/g, '/');
              }
            } catch (err) {
              console.error('日期格式化错误:', err, outfit.created_at);
              outfit.formatted_date = '日期错误';
            }
          }
        });
      }
      
      // 按创建时间降序排序
      outfits.sort((a, b) => {
        const dateA = new Date(a.created_at);
        const dateB = new Date(b.created_at);
        return dateB - dateA;
      });
      
      // 更新数据
      this.setData({
        outfits: outfits,
        filteredOutfits: outfits, // 初始时显示全部穿搭
        loading: false
      });
    });
  },
  
  // 调整弹出框样式，避免被底部Tab栏遮挡
  adjustPopupStyle: function() {
    const that = this;
    wx.getSystemInfo({
      success: function(res) {
        console.log('系统信息:', res);
        // 获取底部安全区域高度
        const safeAreaBottom = res.safeArea ? (res.screenHeight - res.safeArea.bottom) : 0;
        // 标准TabBar高度(通常为50px)，但我们减少一些以压缩空白区域
        const tabBarHeight = 34; // 减少默认高度
        // 计算需要的底部padding (保留TabBar的高度，但减少过大的空白)
        const bottomPadding = Math.max(tabBarHeight, safeAreaBottom); // 移除额外缓冲
        
        console.log('底部安全区域:', safeAreaBottom, '计算的底部padding:', bottomPadding);
        
        // 设置弹出框的底部padding
        wx.createSelectorQuery()
          .select('.popup-container')
          .fields({ node: true, size: true }, function(res) {
            if (res && res.node) {
              // 动态设置样式
              try {
                const popupContainer = res.node;
                popupContainer.style.paddingBottom = bottomPadding + 'px';
                // 适当调整最大高度，增加可用空间
                const maxHeight = Math.min(res.screenHeight * 0.7, res.screenHeight - bottomPadding - 50);
                popupContainer.style.maxHeight = maxHeight + 'px';
              } catch (e) {
                console.error('设置弹出框样式失败:', e);
              }
            }
          })
          .exec();
      },
      fail: function(err) {
        console.error('获取系统信息失败:', err);
      }
    });
  },
  
  // 关闭穿搭选择弹出层
  closeOutfitPopup: function() {
    this.setData({
      showOutfitPopup: false
    });
  },
  
  // 加载穿搭分类
  loadOutfitCategories: function() {
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_outfit_categories.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log('获取穿搭分类列表:', res.data);
        
        if (res.statusCode === 200 && res.data.success) {
          const categories = res.data.data || [];
          this.setData({ categories });
        }
      },
      fail: (err) => {
        console.error('获取分类列表失败:', err);
      }
    });
  },
  
  // 切换弹出层中的穿搭分类
  switchPopupCategory: function(e) {
    const categoryId = e.currentTarget.dataset.id;
    console.log('切换穿搭分类:', categoryId);
    
    if (categoryId === this.data.currentCategoryId) {
      return; // 避免重复点击
    }
    
    this.setData({ currentCategoryId: categoryId });
    
    // 筛选穿搭
    this.filterOutfitsByCategory(categoryId);
  },
  
  // 根据分类筛选穿搭
  filterOutfitsByCategory: function(categoryId) {
    const { outfits } = this.data;
    let filteredOutfits = outfits;
    
    if (categoryId !== 'all') {
      // 按分类ID筛选
      filteredOutfits = outfits.filter(outfit => {
        return outfit.category_id == categoryId; // 使用==而非===，因为可能一个是字符串一个是数字
      });
    }
    
    console.log('筛选后的穿搭数量:', filteredOutfits.length);
    
    this.setData({ filteredOutfits });
  },
  
  // 选择穿搭
  selectOutfit: function(e) {
    const outfitId = e.currentTarget.dataset.id;
    console.log('选择穿搭:', outfitId);
    
    // 查找选中的穿搭
    const selectedOutfit = this.data.outfits.find(outfit => outfit.id === outfitId);
    
    if (selectedOutfit && this.data.selectedDate) {
      // 显示加载状态
      this.setData({ 
        loading: true,
        showOutfitPopup: false // 关闭弹出层
      });
      
      // 发送请求，保存穿搭日期
      wx.request({
        url: `${app.globalData.apiBaseUrl}/save_calendar_outfit.php`,
        method: 'POST',
        header: {
          'Authorization': app.globalData.token,
          'Content-Type': 'application/json'
        },
        data: {
          outfit_id: selectedOutfit.id,
          calendar_date: this.data.selectedDate,
          action: 'add'
        },
        success: (res) => {
          console.log('保存穿搭日历响应:', res.data);
          
          if (res.statusCode === 200 && !res.data.error) {
            // 更新本地数据
            const outfitCalendarData = { ...this.data.outfitCalendarData };
            outfitCalendarData[this.data.selectedDate] = selectedOutfit;
            
            // 更新界面
            this.setData({
              outfitCalendarData,
              loading: false
            });
            
            // 加载完整的穿搭数据显示
            this.loadOutfitDetail(selectedOutfit.id, this.data.selectedDate);
            
            wx.showToast({
              title: '穿搭已安排',
              icon: 'success'
            });
          } else {
            console.error('保存穿搭日期失败:', res.data);
            this.setData({ loading: false });
            
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          console.error('请求失败:', err);
          this.setData({ loading: false });
          
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          });
        }
      });
    } else {
      wx.showToast({
        title: '选择穿搭失败',
        icon: 'none'
      });
    }
  },
  
  // 创建新穿搭
  createOutfit: function() {
    // 记录当前选中的日期到全局变量，以便新建穿搭后可以关联
    app.globalData.selectedCalendarDate = this.data.selectedDate;
    
    // 跳转到新建穿搭页面
    wx.navigateTo({
      url: '/pages/outfits/add/add?fromCalendar=true'
    });
  },
  
  // 跳转到穿搭详情页
  viewOutfit: function() {
    if (this.data.currentDayOutfit) {
      wx.navigateTo({
        url: `/pages/outfits/detail/detail?id=${this.data.currentDayOutfit.id}`
      });
    }
  },
  
  // 取消日期穿搭
  cancelOutfit: function() {
    const { selectedDate } = this.data;
    
    wx.showModal({
      title: '取消穿搭',
      content: '确定要取消这一天的穿搭安排吗？',
      confirmColor: '#666666',
      success: (res) => {
        if (res.confirm) {
          this.setData({ loading: true });
          
          wx.request({
            url: `${app.globalData.apiBaseUrl}/save_calendar_outfit.php`,
            method: 'POST',
            header: {
              'Authorization': app.globalData.token,
              'Content-Type': 'application/json'
            },
            data: {
              outfit_id: this.data.currentDayOutfit.id,
              calendar_date: selectedDate,
              action: 'remove'
            },
            success: (res) => {
              if (res.statusCode === 200 && !res.data.error) {
                // 更新本地数据
                const outfitCalendarData = { ...this.data.outfitCalendarData };
                delete outfitCalendarData[selectedDate];
                
                this.setData({
                  outfitCalendarData,
                  currentDayOutfit: null,
                  loading: false
                });
                
                wx.showToast({
                  title: '已取消穿搭',
                  icon: 'success'
                });
              } else {
                console.error('取消穿搭失败:', res.data);
                this.setData({ loading: false });
                
                wx.showToast({
                  title: '操作失败',
                  icon: 'none'
                });
              }
            },
            fail: (err) => {
              console.error('请求失败:', err);
              this.setData({ loading: false });
              
              wx.showToast({
                title: '网络错误',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },
  
  // 返回今天
  goToToday: function() {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth() + 1;
    const todayStr = this.formatDate(year, month, today.getDate());
    
    this.setData({
      year,
      month
    }, () => {
      this.initCalendar();
      this.loadCalendarOutfits(() => {
        // 日历数据加载完成后，选中今天的日期
        const todayOutfit = this.data.outfitCalendarData[todayStr] || null;
        if (todayOutfit) {
          // 如果今天有穿搭，加载详情
          this.loadOutfitDetail(todayOutfit.id, todayStr);
        } else {
          // 只设置选中日期
          this.setData({
            selectedDate: todayStr,
            currentDayOutfit: null
          });
        }
      });
    });
  }
}); 