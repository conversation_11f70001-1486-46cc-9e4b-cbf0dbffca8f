-- 为clothes表添加排序字段
-- 执行时间：2025-01-08

-- 添加sort_order字段
ALTER TABLE `clothes` ADD COLUMN `sort_order` INT(11) DEFAULT 0 COMMENT '排序顺序，数值越小越靠前' AFTER `wear_frequency_unit`;

-- 为现有数据设置默认排序值（按创建时间排序）
UPDATE `clothes` SET `sort_order` = `id` WHERE `sort_order` = 0;

-- 添加索引以提高排序查询性能
ALTER TABLE `clothes` ADD INDEX `idx_sort_order` (`sort_order`);
ALTER TABLE `clothes` ADD INDEX `idx_user_sort` (`user_id`, `sort_order`);
ALTER TABLE `clothes` ADD INDEX `idx_wardrobe_sort` (`wardrobe_id`, `sort_order`);
