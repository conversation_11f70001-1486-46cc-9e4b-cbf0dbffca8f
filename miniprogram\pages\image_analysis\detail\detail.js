const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    error: false,
    errorMsg: '',
    analysis: null,
    statusText: {
      'pending': '待分析',
      'processing': '分析中',
      'completed': '已完成',
      'failed': '分析失败'
    },
    isSharedView: false  // 标记是否为分享查看模式
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('形象分析详情页面加载, 参数:', options);

    if (!options.id) {
      this.setData({
        loading: false,
        error: true,
        errorMsg: '未提供分析ID'
      });
      return;
    }

    this.analysisId = options.id;
    this.shareToken = options.token; // 获取分享token参数
    
    // 如果有分享token，标记为分享查看模式
    if (this.shareToken) {
      console.log('检测到分享token, 启用分享查看模式:', this.shareToken.substring(0, 10) + '...');
      this.setData({ isSharedView: true });
    } else {
      console.log('未检测到分享token，使用普通查看模式');
    }
    
    this.loadAnalysisDetail();
  },

  /**
   * 加载分析详情
   */
  loadAnalysisDetail: function () {
    // 检查是否有分享token或登录token
    const token = this.shareToken || wx.getStorageSync('token');
    
    console.log('使用token加载分析详情:', {
      isShareToken: !!this.shareToken,
      tokenPrefix: token ? token.substring(0, 10) + '...' : 'null',
      analysisId: this.analysisId
    });
    
    if (!token) {
      this.setData({
        loading: false,
        error: true,
        errorMsg: '请先登录以查看分析详情'
      });
      return;
    }

    this.setData({
      loading: true,
      error: false
    });

    wx.request({
      url: app.globalData.baseUrl + '/get_image_analysis.php',
      method: 'GET',
      data: {
        id: this.analysisId
      },
      header: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      success: (res) => {
        console.log('分析详情响应:', res.statusCode, res.data ? (res.data.error ? '有错误' : '成功') : '无数据');
        
        this.setData({
          loading: false
        });
        
        if (res.data && !res.data.error) {
          // 处理分析结果
          let analysisData = res.data.data;
          
          // 确保payment_status属性存在，如果后端未返回则设为默认值
          if (typeof analysisData.payment_status === 'undefined') {
            analysisData.payment_status = 'unpaid';
          }
          
          this.setData({
            analysis: analysisData,
            isSharedView: analysisData.is_shared_view || false // 从API响应更新分享视图状态
          });
        } else {
          this.setData({
            error: true,
            errorMsg: res.data ? (res.data.msg || '获取分析详情失败') : '服务器响应异常'
          });
          console.error('获取分析详情失败:', res.data);
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        this.setData({
          loading: false,
          error: true,
          errorMsg: '网络错误，请重试'
        });
      }
    });
  },

  /**
   * 请求开始分析
   */
  requestAnalysis: function () {
    // 检查是否已支付
    if (this.data.analysis.payment_status !== 'paid') {
      wx.showToast({
        title: '请先完成支付',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '请求中...',
    });

    const token = wx.getStorageSync('token');

    wx.request({
      url: app.globalData.baseUrl + '/request_image_analysis.php',
      method: 'POST',
      data: {
        analysisId: this.analysisId
      },
      header: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.data && !res.data.error) {
          // 刷新分析状态
          this.loadAnalysisDetail();
          
          // 如果状态更新为processing，显示提示
          if (res.data.data.status === 'processing') {
            wx.showToast({
              title: '分析已开始，请耐心等待',
              icon: 'none',
              duration: 2000
            });
          }
        } else {
          wx.showToast({
            title: res.data.msg || '请求分析失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 刷新分析状态
   */
  refreshAnalysis: function () {
    this.loadAnalysisDetail();
  },

  /**
   * 预览图片
   */
  previewImage: function (e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: this.data.analysis.photo_urls
    });
  },

  /**
   * 前往形象分析页面
   */
  goToAnalysis: function () {
    wx.redirectTo({
      url: '/pages/image_analysis/index/index'
    });
  },

  /**
   * 前往历史记录页面
   */
  goToHistory: function () {
    wx.redirectTo({
      url: '/pages/image_analysis/history/history'
    });
  },

  /**
   * 前往支付页面
   */
  goToPayment: function () {
    wx.navigateTo({
      url: `/pages/purchase/image_analysis/index?id=${this.analysisId}`
    });
  },

  /**
   * 基于此分析推荐穿搭
   */
  goToRecommendation: function () {
    // 确保分析已完成
    if (this.data.analysis.status !== 'completed') {
      wx.showToast({
        title: '分析尚未完成，无法推荐穿搭',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/clothing_recommendation/clothing_recommendation?analysis_id=${this.analysisId}&type=image_analysis`
    });
  },

  /**
   * 生成用于分享的令牌
   */
  generateShareToken: function() {
    // 始终创建特殊格式的分享token，不再使用用户的实际token
    // 格式: SHARE_[分析ID]_[当前时间戳]_[随机字符串]
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 10);
    const analysisId = this.analysisId || (this.data.analysis ? this.data.analysis.id : '0');
    
    // 创建特殊格式的分享token，确保以SHARE_开头，后端会特殊处理
    const shareToken = `SHARE_${analysisId}_${timestamp}_${randomStr}`;
    console.log('生成分享token:', shareToken);
    return shareToken;
  },
  
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    // 生成分享token
    const shareToken = this.generateShareToken();
    
    return {
      title: '我的个人形象分析报告',
      path: `/pages/image_analysis/detail/detail?id=${this.analysisId}&token=${shareToken}`,
      imageUrl: this.data.analysis.photo_urls && this.data.analysis.photo_urls.length > 0 ? this.data.analysis.photo_urls[0] : ''
    }
  }
});