.container {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #eaeaea;
  position: relative;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.back-btn image {
  width: 40rpx;
  height: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.category-list {
  background-color: #fff;
  margin-top: 20rpx;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.category-info {
  flex: 1;
}

.category-name {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.system-tag {
  display: inline-block;
  font-size: 22rpx;
  color: #333;
  background-color: #f2f2f2;
  border-radius: 10rpx;
  padding: 2rpx 10rpx;
  margin-left: 10rpx;
  vertical-align: middle;
}

.custom-tag {
  display: inline-block;
  font-size: 22rpx;
  color: #333;
  background-color: #e6f7ff;
  border-radius: 10rpx;
  padding: 2rpx 10rpx;
  margin-left: 10rpx;
  vertical-align: middle;
}

.delete-time {
  font-size: 24rpx;
  color: #999;
}

.category-actions {
  display: flex;
}

.restore-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 20rpx;
}

.restore-btn image {
  width: 40rpx;
  height: 40rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 50rpx;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  text-align: center;
}

.load-more {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 28rpx;
} 