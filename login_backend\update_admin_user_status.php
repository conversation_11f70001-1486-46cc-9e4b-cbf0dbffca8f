<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证管理员身份
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取请求体
$data = json_decode(file_get_contents('php://input'), true);

// 验证参数
if (!isset($data['user_id']) || empty($data['user_id']) || !isset($data['status'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少必要参数: user_id或status']);
    exit();
}

$userId = (int)$data['user_id'];
$status = (int)$data['status'];

// 验证状态值
if ($status !== 0 && $status !== 1) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '无效的状态值，必须为0或1']);
    exit();
}

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

// 检查用户是否存在
$checkQuery = "SELECT id FROM users WHERE id = :user_id";
$checkStmt = $conn->prepare($checkQuery);
$checkStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
$checkStmt->execute();

if (!$checkStmt->fetch()) {
    http_response_code(404);
    echo json_encode(['error' => true, 'msg' => '用户不存在']);
    exit();
}

// 更新用户状态
$updateQuery = "UPDATE users SET status = :status, updated_at = NOW() WHERE id = :user_id";
$updateStmt = $conn->prepare($updateQuery);
$updateStmt->bindValue(':status', $status, PDO::PARAM_INT);
$updateStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
$updateStmt->execute();

// 检查更新结果
if ($updateStmt->rowCount() > 0) {
    echo json_encode([
        'error' => false,
        'msg' => $status === 1 ? '用户已启用' : '用户已禁用',
        'data' => [
            'user_id' => $userId,
            'status' => $status
        ]
    ]);
} else {
    echo json_encode([
        'error' => true,
        'msg' => '用户状态更新失败'
    ]);
} 