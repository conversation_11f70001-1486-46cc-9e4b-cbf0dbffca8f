<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>穿搭详情 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        .outfit-image {
            width: 300px;
            height: 300px;
            border-radius: 8px;
            object-fit: contain;
            background-color: #f5f5f5;
            margin-bottom: 20px;
        }
        
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #1890ff;
            text-decoration: none;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        .outfit-info {
            margin-bottom: 30px;
        }
        
        .outfit-info h3 {
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .info-item {
            display: flex;
            margin-bottom: 10px;
            line-height: 24px;
        }
        
        .info-label {
            font-weight: 500;
            width: 100px;
            flex-shrink: 0;
        }
        
        .info-value {
            flex: 1;
        }
        
        .clothes-container {
            margin-top: 20px;
        }
        
        .clothes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        
        .clothes-item {
            background-color: #f9f9f9;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .clothes-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            background-color: #f0f0f0;
            display: block;
        }
        
        .clothes-info {
            padding: 10px;
        }
        
        .clothes-name {
            font-size: 14px;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .clothes-type {
            font-size: 12px;
            color: #666;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #999;
            font-size: 16px;
        }
        
        .error {
            background-color: #fff1f0;
            border: 1px solid #ffccc7;
            border-radius: 4px;
            padding: 15px;
            color: #cf1322;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>穿搭详情</h2>
                <div class="user-info">
                    <span id="userName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="card">
                <a href="outfit_list.html" class="back-link">&lt; 返回穿搭列表</a>
                
                <div id="outfitContainer">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/outfit_details.js"></script>
    <script src="js/image_viewer.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为outfit
            Sidebar.init('outfit');
            
            // 显示用户信息
            const userName = document.getElementById('userName');
            const user = Auth.getCurrentUser();
            if (user) {
                userName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
        });
    </script>
</body>
</html> 