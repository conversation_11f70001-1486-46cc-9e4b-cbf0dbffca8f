<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>衣物管理 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        .search-box {
            display: flex;
            margin-bottom: 20px;
            align-items: center;
        }
        
        .search-input {
            flex: 1;
            height: 36px;
            padding: 0 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            margin-right: 10px;
        }
        
        .search-btn {
            height: 36px;
            padding: 0 15px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .search-btn:hover {
            background-color: #40a9ff;
        }
        
        .category-filter {
            margin-left: 10px;
            height: 36px;
            padding: 0 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        
        .add-btn {
            height: 36px;
            padding: 0 15px;
            background-color: #52c41a;
            color: white;
            border: none;
            border-radius: 4px;
            margin-left: auto;
            cursor: pointer;
        }
        
        .add-btn:hover {
            background-color: #73d13d;
        }
        
        .clothing-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .clothing-table th, 
        .clothing-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .clothing-table th {
            background-color: #fafafa;
            font-weight: 500;
        }
        
        .clothing-table tr:hover {
            background-color: #f5f5f5;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .clothing-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
            border: 1px solid #eee;
        }
        
        .action-btn {
            padding: 5px 10px;
            border-radius: 4px;
            border: none;
            margin-right: 5px;
            cursor: pointer;
            color: white;
            font-size: 12px;
        }
        
        .view-btn {
            background-color: #1890ff;
        }
        
        .view-btn:hover {
            background-color: #40a9ff;
        }
        
        .edit-btn {
            background-color: #faad14;
        }
        
        .edit-btn:hover {
            background-color: #ffc53d;
        }
        
        .delete-btn {
            background-color: #f5222d;
        }
        
        .delete-btn:hover {
            background-color: #ff4d4f;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
        }
        
        .pagination button {
            padding: 5px 12px;
            margin: 0 5px;
            border: 1px solid #d9d9d9;
            background-color: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .pagination button:disabled {
            color: #d9d9d9;
            cursor: not-allowed;
        }
        
        .pagination button:hover:not(:disabled) {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .page-info {
            margin: 0 10px;
        }
        
        .no-data {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        
        .category-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            background-color: #e6f7ff;
            color: #1890ff;
        }
        
        .tags {
            font-size: 12px;
            color: #888;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>衣物管理</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="card">
                <div class="search-box">
                    <input type="text" id="searchInput" class="search-input" placeholder="搜索衣物名称或标签...">
                    <button id="searchBtn" class="search-btn">搜索</button>
                    <select id="categoryFilter" class="category-filter">
                        <option value="">所有分类</option>
                        <option value="tops">上衣</option>
                        <option value="pants">裤子</option>
                        <option value="skirts">裙子</option>
                        <option value="coats">外套</option>
                        <option value="shoes">鞋子</option>
                        <option value="bags">包包</option>
                        <option value="accessories">配饰</option>
                    </select>
                    <select id="userFilter" class="category-filter" style="min-width: 120px;">
                        <option value="">所有用户</option>
                        <!-- 用户列表将通过JavaScript动态添加 -->
                    </select>
                    <button id="addClothingBtn" class="add-btn" onclick="ClothingList.addClothing()">添加衣物</button>
                </div>
                
                <table class="clothing-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>图片</th>
                            <th>名称</th>
                            <th>分类</th>
                            <th>标签</th>
                            <th>用户</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="clothingTableBody">
                        <tr>
                            <td colspan="8" class="no-data">加载中...</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="pagination">
                    <button id="prevBtn" disabled>&lt; 上一页</button>
                    <span id="pageInfo" class="page-info">第 1/1 页</span>
                    <button id="nextBtn" disabled>下一页 &gt;</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/clothing_list.js"></script>
    <script src="js/image_viewer.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为clothing
            Sidebar.init('clothing');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化衣物列表
            ClothingList.init();
        });
    </script>
</body>
</html> 