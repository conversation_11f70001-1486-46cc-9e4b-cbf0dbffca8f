<?php
/**
 * 更新面容分析支付状态API
 * 
 * 用于更新面容分析的支付状态
 * 
 * 请求方法：POST
 * 请求参数：
 * - analysis_id: 分析ID
 * 
 * 返回：
 * {
 *   "error": false,
 *   "msg": "支付状态更新成功"
 * }
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => '只支持POST请求'
    ]);
    exit;
}

// 验证用户身份
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供授权Token'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
$auth = new Auth();
$verifyResult = $auth->verifyToken($token);

if ($verifyResult === false) {
    echo json_encode([
        'error' => true,
        'msg' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $verifyResult['sub']; // 从验证结果中获取用户ID

// 获取POST数据
$postData = json_decode(file_get_contents("php://input"), true);

if (!isset($postData['analysis_id']) || empty($postData['analysis_id'])) {
    echo json_encode([
        'error' => true,
        'msg' => '缺少分析ID'
    ]);
    exit;
}

$analysisId = $postData['analysis_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 验证分析记录是否属于当前用户
    $stmt = $conn->prepare("
        SELECT id, payment_status 
        FROM face_analysis 
        WHERE id = :analysis_id AND user_id = :user_id
    ");
    $stmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    
    $analysis = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$analysis) {
        echo json_encode([
            'error' => true,
            'msg' => '分析记录不存在或无权限访问'
        ]);
        exit;
    }
    
    // 更新支付状态
    $stmt = $conn->prepare("
        UPDATE face_analysis 
        SET payment_status = 'paid', updated_at = NOW() 
        WHERE id = :analysis_id
    ");
    $stmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
    
    if (!$stmt->execute()) {
        echo json_encode([
            'error' => true,
            'msg' => '更新支付状态失败'
        ]);
        exit;
    }
    
    echo json_encode([
        'error' => false,
        'msg' => '支付状态更新成功'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '更新支付状态失败: ' . $e->getMessage()
    ]);
}
?>
