<view class="container">
  <!-- 状态栏占位 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  
  <!-- 顶部标题栏 -->
  <view class="title-bar" style="top: {{statusBarHeight}}px;">
    <view class="title">个人中心</view>
  </view>
  
  <!-- 主要内容区域 -->
  <view class="main-content" style="margin-top: calc({{statusBarHeight}}px + 44px);">
    <!-- 用户信息 - 已登录状态 -->
    <view class="user-card" wx:if="{{hasUserInfo}}">
      <view class="flex items-center">
        <image class="avatar" src="{{userInfo.avatar_url}}" binderror="onAvatarError"></image>
        <view class="user-info">
          <view class="nickname-row">
          <view class="nickname">{{userInfo.nickname || '微信用户'}}</view>
            <view class="merchant-action" bindtap="toggleMerchantStatus">
              <text wx:if="{{isMerchant}}">退出入驻</text>
              <text wx:else>立即入驻</text>
            </view>
          </view>
          <view class="slogan">ID: {{userInfo.id}}</view>
        </view>
      </view>
      
      <!-- 数据统计 -->
      <view class="stats-container">
        <view class="stat-item" bindtap="navigateToWardrobes">
          <view class="stat-value">{{hasUserInfo ? wardrobeCount : '-'}}</view>
          <view class="stat-label">我的衣橱</view>
        </view>
        <view class="stat-item" bindtap="navigateToClothingCategories">
          <view class="stat-value">{{hasUserInfo ? categoryCount : '-'}}</view>
          <view class="stat-label">衣物分类</view>
        </view>
        <view class="stat-item" bindtap="navigateToClothingTags">
          <view class="stat-value">{{hasUserInfo ? tagCount : '-'}}</view>
          <view class="stat-label">衣物标签</view>
        </view>
        <view class="stat-item" bindtap="navigateToOutfitCategories">
          <view class="stat-value">{{hasUserInfo ? outfitCategoryCount : '-'}}</view>
          <view class="stat-label">穿搭分类</view>
        </view>
      </view>

      <!-- 商家入驻状态 -->
      <view class="merchant-section" wx:if="{{isMerchant}}">
        <view class="merchant-status">
          <view class="merchant-info">
            <view class="merchant-title">商家状态</view>
            <view class="merchant-badge">已入驻</view>
          </view>
          
          <!-- 共享试穿点数开关 -->
          <view class="merchant-info">
            <view class="merchant-title">共享试穿点数</view>
            <view class="share-control">
              <text class="share-status">{{isShareCredits ? '已开启' : '未开启'}}</text>
              <switch class="share-switch" checked="{{isShareCredits}}" color="#000000" catchchange="toggleShareCredits"/>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 用户信息 - 未登录状态 -->
    <view class="user-card" wx:else>
      <view class="flex items-center mb-4">
        <view class="default-avatar">
            <image class="empty-image" src="/images/default-avatar.png" mode="aspectFit"></image>
        </view>
        <view>
          <view class="nickname">未登录</view>
          <view class="slogan">登录后可查看用户ID</view>
        </view>
      </view>
      
      <!-- 未登录状态数据统计 -->
      <view class="stats-container">
        <view class="stat-item">
          <view class="stat-value">-</view>
          <view class="stat-label">我的衣橱</view>
        </view>
        <view class="stat-item">
          <view class="stat-value">-</view>
          <view class="stat-label">衣物分类</view>
        </view>
        <view class="stat-item">
          <view class="stat-value">-</view>
          <view class="stat-label">衣物标签</view>
        </view>
      </view>
        <view class="stat-item">
          <view class="stat-value">-</view>
          <view class="stat-label">穿搭分类</view>
        </view>
      <view class="login-btn" bindtap="goToLogin">
        立即登录
      </view>
    </view>

        <!-- 添加个人形象分析Banner 
        <view class="image-analysis-banner" bindtap="goToImageAnalysis">
      <view class="banner-content">
        <view class="banner-title">个人形象分析</view>
        <view class="banner-subtitle">专业形象顾问为您打造完美穿搭方案</view>
        <view class="banner-price">
          ¥9.9 <text class="original-price">原价¥59</text>
        </view>
      </view>
      
      <view class="banner-image-container">
        <image src="/images/image_analysis.png" class="banner-image" mode="aspectFit"></image>
      </view>
    </view>-->
    
    <!-- 功能区一：我的使用 -->
    <view class="feature-container">
      <view class="feature-list">
        <!-- 形象分析模块 -->
        <view class="feature-item feature-item-normal">
          <view class="business-contact" catchtap="goToImageAnalysisHistory">
            <image class="feature-icon" src="/images/analysis.png" mode="aspectFit"></image>
            <view class="feature-text">形象分析</view>
          </view>
        </view>
        
        <!-- 面容分析模块 -->
        <view class="feature-item feature-item-normal">
          <view class="business-contact" catchtap="goToFaceAnalysisHistory">
            <image class="feature-icon" src="/images/face_analysis.png" mode="aspectFit"></image>
            <view class="feature-text">面容分析</view>
          </view>
        </view>
        
        <!-- 新增照片模块 -->
        <view class="feature-item feature-item-normal">
          <view class="business-contact" catchtap="goToMyPhotos">
            <image class="feature-icon" src="/images/photo.png" mode="aspectFit"></image>
            <view class="feature-text">模特照片</view>
          </view>
        </view>
        
        <!-- 试穿历史模块 -->
        <view class="feature-item feature-item-normal">
          <view class="business-contact" catchtap="goToTryOnHistory">
            <image class="feature-icon" src="/images/history.png" mode="aspectFit"></image>
            <view class="feature-text">试穿历史</view>
          </view>
        </view>
        

      </view>
    </view>
    
    <!-- 功能区二：支持与帮助 -->
    <view class="feature-container">
      <view class="feature-list">
        <!-- 联系客服模块 -->
        <view class="feature-item feature-item-button">
          <button class="contact-button" open-type="contact" bindcontact="handleContact">
            <image class="feature-icon" src="/images/service.png" mode="aspectFit"></image>
            <view class="feature-text">联系客服</view>
          </button>
        </view>
        
        <!-- 联系作者模块 -->
        <view class="feature-item feature-item-normal">
          <view class="business-contact" catchtap="copyWechatId">
            <image class="feature-icon" src="/images/author.png" mode="aspectFit"></image>
            <view class="feature-text">商务合作</view>
          </view>
        </view>
        
        <!-- 打赏作者模块 -->
        <view class="feature-item feature-item-normal">
          <view class="business-contact" catchtap="navigateToDonation">
            <image class="feature-icon" src="/images/donate.png" mode="aspectFit"></image>
            <view class="feature-text">打赏作者</view>
          </view>
        </view>
        
        <!-- 意见反馈模块 -->
        <view class="feature-item feature-item-button">
          <button class="feedback-button" open-type="feedback">
            <image class="feature-icon" src="/images/fankui.png" mode="aspectFit"></image>
            <view class="feature-text">意见反馈</view>
          </button>
        </view>
        
        <!-- 教程模块 -->
        <view class="feature-item feature-item-normal">
          <view class="business-contact" catchtap="goToTutorial">
            <image class="feature-icon" src="/images/tutorial.png" mode="aspectFit"></image>
            <view class="feature-text">使用教程</view>
          </view>
        </view>

        <!-- 推荐穿搭模块 (从tabBar移过来) -->
        <view class="feature-item feature-item-normal">
          <view class="business-contact" catchtap="navigateToRecommendedOutfits">
            <image class="feature-icon" src="/images/recommend.png" mode="aspectFit"></image>
            <view class="feature-text">优惠推荐</view>
          </view>
        </view>

      </view>
    </view>
    
    <!-- 退出登录按钮 - 仅登录状态显示 -->
    <view class="logout-container" wx:if="{{hasUserInfo}}">
      <view class="logout-btn" bindtap="logout">
        退出登录
      </view>
    </view>
  </view>
</view>

<!-- 商家入驻提示弹窗 -->
<view class="modal" wx:if="{{showMerchantTips}}">
  <view class="modal-mask" bind:tap="closeMerchantTips"></view>
  <view class="modal-content">
    <view class="modal-header">
      <view class="modal-title">{{isMerchant ? '退出商家入驻' : '商家入驻'}}</view>
      <view class="modal-close" bind:tap="closeMerchantTips">×</view>
    </view>
    <view class="modal-body">
      <view class="modal-tips">
        <text wx:if="{{!isMerchant}}">商家入驻后将公开您衣橱中的衣物图片信息，其他用户可对您的衣物进行查看与试穿。</text>
        <text wx:else>退出商家入驻后，您的衣物将不再对其他用户公开，共享试穿点数功能也将关闭。</text>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn modal-cancel-btn" bind:tap="closeMerchantTips">取消</button>
      <button class="modal-btn modal-confirm-btn" bind:tap="confirmMerchantAction">{{isMerchant ? '确认退出' : '确认入驻'}}</button>
    </view>
  </view>
</view>

<!-- 自定义Toast提示 -->
<view class="custom-toast" wx:if="{{showCustomToast}}">
  <view class="custom-toast-content">
    <text class="custom-toast-text">{{customToastText}}</text>
  </view>
</view> 