<?php
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, PUT');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] !== 'POST' && $_SERVER['REQUEST_METHOD'] !== 'PUT') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

try {
    $auth = new Auth();
    
    // 验证用户token
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? null;
    
    if (!$authHeader || strpos($authHeader, 'Bearer ') !== 0) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => '未授权访问']);
        exit;
    }
    
    $token = substr($authHeader, 7);
    $tokenData = $auth->verifyToken($token);
    
    if (!$tokenData) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => 'Token无效']);
        exit;
    }
    
    $userId = $tokenData['user_id'] ?? $tokenData['sub'];
    
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => true, 'msg' => '无效的请求数据']);
        exit;
    }
    
    $categoryId = (int)($input['id'] ?? 0);
    $name = trim($input['name'] ?? '');
    $sortOrder = (int)($input['sort_order'] ?? 0);
    $isSystem = (bool)($input['is_system'] ?? false); // 添加系统分类标识
    
    // 验证必填字段
    if ($categoryId <= 0) {
        http_response_code(400);
        echo json_encode(['error' => true, 'msg' => '分类ID无效']);
        exit;
    }
    
    if (empty($name)) {
        http_response_code(400);
        echo json_encode(['error' => true, 'msg' => '分类名称不能为空']);
        exit;
    }
    
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查分类是否存在且属于该用户，处理两种情况：系统分类或用户自定义分类
    if ($isSystem) {
        // 系统分类检查 - 确保是用户自己的系统分类副本
        $checkSql = "SELECT id, name, code, is_system FROM clothing_categories WHERE id = :id AND user_id = :user_id AND is_system = 1";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bindParam(':id', $categoryId, PDO::PARAM_INT);
        $checkStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    } else {
        // 用户自定义分类检查
        $checkSql = "SELECT id, name, code, is_system FROM clothing_categories WHERE id = :id AND user_id = :user_id AND is_system = 0";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bindParam(':id', $categoryId, PDO::PARAM_INT);
        $checkStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    }
    
    $checkStmt->execute();
    
    if ($checkStmt->rowCount() === 0) {
        http_response_code(404);
        echo json_encode(['error' => true, 'msg' => '分类不存在或无权限修改']);
        exit;
    }
    
    $category = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    // 非系统分类才检查名称是否冲突
    if (!$isSystem) {
        // 检查新名称是否与其他分类冲突（排除自己）
        $checkNameSql = "SELECT id FROM clothing_categories WHERE name = :name AND id != :id AND (user_id = :user_id OR is_system = 1)";
        $checkNameStmt = $conn->prepare($checkNameSql);
        $checkNameStmt->bindParam(':name', $name, PDO::PARAM_STR);
        $checkNameStmt->bindParam(':id', $categoryId, PDO::PARAM_INT);
        $checkNameStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $checkNameStmt->execute();
        
        if ($checkNameStmt->rowCount() > 0) {
            http_response_code(400);
            echo json_encode(['error' => true, 'msg' => '分类名称已存在']);
            exit;
        }
    }
    
    // 更新分类，根据系统分类标识决定更新哪些字段
    if ($isSystem) {
        // 系统分类只更新排序字段，但确保是用户自己的系统分类副本
        $updateSql = "UPDATE clothing_categories SET sort_order = :sort_order, updated_at = NOW() WHERE id = :id AND user_id = :user_id AND is_system = 1";
        $updateStmt = $conn->prepare($updateSql);
        $updateStmt->bindParam(':sort_order', $sortOrder, PDO::PARAM_INT);
        $updateStmt->bindParam(':id', $categoryId, PDO::PARAM_INT);
        $updateStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    } else {
        // 用户自定义分类更新名称和排序
        $updateSql = "UPDATE clothing_categories SET name = :name, sort_order = :sort_order, updated_at = NOW() WHERE id = :id AND user_id = :user_id";
        $updateStmt = $conn->prepare($updateSql);
        $updateStmt->bindParam(':name', $name, PDO::PARAM_STR);
        $updateStmt->bindParam(':sort_order', $sortOrder, PDO::PARAM_INT);
        $updateStmt->bindParam(':id', $categoryId, PDO::PARAM_INT);
        $updateStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    }
    
    if ($updateStmt->execute()) {
        echo json_encode([
            'error' => false,
            'msg' => '分类更新成功',
            'data' => [
                'id' => $categoryId,
                'user_id' => $userId, // 无论是系统分类还是用户分类，都是用户自己的
                'name' => $isSystem ? $category['name'] : $name, // 系统分类返回原名称
                'code' => $category['code'],
                'is_system' => $isSystem,
                'sort_order' => $sortOrder,
                'editable' => true
            ]
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => true, 'msg' => '更新分类失败']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '服务器错误: ' . $e->getMessage()
    ]);
} 