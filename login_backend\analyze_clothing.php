<?php
/**
 * 衣物分析 API
 * 
 * 使用 Gemini API 分析衣物图片
 * 
 * 请求参数:
 * - image_base64: 图片的base64编码 (必填)
 * - prompt: 提示词 (可选)
 * 
 * 响应:
 * {
 *   "success": true,
 *   "data": {
 *     "衣物类别": "上衣",
 *     "衣物标签": ["春季", "休闲", "标签1", "标签2"],
 *     "衣物信息": {
 *       "衣物名称": "白色T恤",
 *       "颜色": "白色"
 *     }
 *   }
 * }
 */

// 清除之前的输出缓冲
ob_clean();

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 设置返回内容类型
header('Content-Type: application/json');

// 处理CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 对于OPTIONS请求，直接返回
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'msg' => '仅支持POST请求'
    ]);
    exit;
}

// 获取并验证token
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'success' => false,
        'msg' => '缺少授权信息'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
$auth = new Auth();
$tokenData = $auth->verifyToken($token);

if (!$tokenData) {
    echo json_encode([
        'success' => false,
        'msg' => '无效或已过期的授权信息'
    ]);
    exit;
}

// 获取用户ID
$userId = $tokenData['sub'];

// 获取POST数据
$postData = json_decode(file_get_contents('php://input'), true);

// 验证必需的参数
if (!isset($postData['image_base64']) || empty($postData['image_base64'])) {
    echo json_encode([
        'success' => false,
        'msg' => '缺少图片数据'
    ]);
    exit;
}

// 获取图片数据
$imageBase64 = $postData['image_base64'];

// 去除可能存在的Data URI前缀
if (strpos($imageBase64, ',') !== false) {
    $imageBase64 = explode(',', $imageBase64)[1];
}

// 获取提示词
$prompt = isset($postData['prompt']) ? $postData['prompt'] : "识别图片中的衣物，并严格按照以下JSON格式返回结果，不要有任何额外的文本：\n" .
    "{\n" .
    '  "衣物类别": "上衣、裤子、裙子、外套、鞋子、包包、配饰中的一个",\n' .
    '  "衣物标签": [\n' . 
    '    "季节标签(春季/夏季/秋季/冬季中的一个)",\n' . 
    '    "场合标签(休闲/通勤/派对/运动中的一个或多个)",\n' . 
    '    "适合天气标签(必须从以下选择至少一个：晴天/雨天/阴天/多云/闷热/寒冷/微凉)",\n' . 
    '    "风格标签(如简约/复古/优雅等)",\n' . 
    '    "自定义标签(搭配相关的自定义标签)",\n' . 
    '    "自定义标签(穿着场景相关的自定义标签)",\n' . 
    '    "自定义标签(其他特点相关的自定义标签)"\n' . 
    '  ],\n' .
    '  "衣物信息": {\n' .
    '    "衣物名称": "名称",\n' .
    '    "颜色": "颜色"\n' .
    '  }\n' .
    "}";

// 记录API请求
error_log("用户 $userId 请求分析衣物图片");

try {
    // 准备POST数据
    $requestData = [
        'image_base64' => $imageBase64,
        'prompt' => $prompt
    ];
    
    // 记录请求信息（截断base64以保护隐私）
    $base64Preview = substr($imageBase64, 0, 30) . '...' . substr($imageBase64, -30);
    error_log("向Gemini API发送请求，image_base64长度: " . strlen($imageBase64) . ", preview: " . $base64Preview);
    error_log("提示词: " . $prompt);
    
    // 创建并配置cURL请求
    $ch = curl_init(GEMINI_API_PROXY_URL);
    
    // 记录API URL
    error_log("Gemini API代理URL: " . GEMINI_API_PROXY_URL);
    
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => [
            'image_base64' => $imageBase64,
            'prompt' => $prompt
        ],
        CURLOPT_TIMEOUT => 30, // 30秒超时
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => true
    ]);
    
    // 执行请求
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    // 关闭cURL会话
    curl_close($ch);
    
    // 记录响应信息
    error_log("Gemini API响应状态码: " . $httpCode);
    error_log("Gemini API响应内容 (截断): " . substr($response, 0, 200) . (strlen($response) > 200 ? '...' : ''));
    
    // 检查是否发生错误
    if ($error) {
        error_log("分析衣物图片请求失败: $error");
        echo json_encode([
            'success' => false,
            'msg' => '请求Gemini API失败: ' . $error
        ]);
        exit;
    }
    
    // 检查HTTP状态码
    if ($httpCode !== 200) {
        error_log("分析衣物图片请求返回非200状态码: $httpCode, 响应: " . substr($response, 0, 500));
        echo json_encode([
            'success' => false,
            'msg' => "Gemini API返回错误状态码: $httpCode"
        ]);
        exit;
    }
    
    // 解析JSON响应
    $result = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("解析Gemini API响应JSON失败: " . json_last_error_msg() . ", 响应: " . substr($response, 0, 500));
        echo json_encode([
            'success' => false,
            'msg' => '解析Gemini API响应失败'
        ]);
        exit;
    }
    
    // 记录解析后的JSON结构
    error_log("解析后的JSON结构: " . json_encode(array_keys($result)));
    
    // 提取分析结果
    if (isset($result['contents'][0]['parts'][0]['text'])) {
        $textContent = $result['contents'][0]['parts'][0]['text'];
        error_log("提取到的文本内容: " . substr($textContent, 0, 500));
        
        // 尝试从文本中提取JSON
        if (preg_match('/{.*}/s', $textContent, $matches)) {
            $jsonString = $matches[0];
            error_log("匹配到的JSON字符串: " . $jsonString);
            
            $jsonContent = json_decode($jsonString, true);
            
            if ($jsonContent) {
                error_log("成功解析JSON内容: " . json_encode($jsonContent));
                // 成功提取到JSON结果
                echo json_encode([
                    'success' => true,
                    'data' => $jsonContent
                ]);
                exit;
            } else {
                error_log("JSON解析失败: " . json_last_error_msg());
            }
        } else {
            error_log("未能从文本中匹配到JSON格式");
        }
        
        // 如果无法提取JSON，返回原始文本
        error_log("无法从Gemini API响应中提取JSON: " . substr($textContent, 0, 500));
        echo json_encode([
            'success' => false,
            'msg' => '无法解析AI分析结果，返回的不是有效的JSON格式',
            'raw_text' => $textContent
        ]);
        exit;
    } else if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
        // 根据中转api示例.php的响应格式调整
        $textContent = $result['candidates'][0]['content']['parts'][0]['text'];
        error_log("提取到的文本内容(candidates): " . substr($textContent, 0, 500));
        
        // 尝试从文本中提取JSON
        if (preg_match('/{.*}/s', $textContent, $matches)) {
            $jsonString = $matches[0];
            error_log("匹配到的JSON字符串: " . $jsonString);
            
            $jsonContent = json_decode($jsonString, true);
            
            if ($jsonContent) {
                error_log("成功解析JSON内容: " . json_encode($jsonContent));
                // 成功提取到JSON结果
                echo json_encode([
                    'success' => true,
                    'data' => $jsonContent
                ]);
                exit;
            } else {
                error_log("JSON解析失败: " . json_last_error_msg());
            }
        } else {
            error_log("未能从文本中匹配到JSON格式");
        }
        
        // 如果无法提取JSON，返回原始文本
        error_log("无法从Gemini API响应中提取JSON: " . substr($textContent, 0, 500));
        echo json_encode([
            'success' => false,
            'msg' => '无法解析AI分析结果，返回的不是有效的JSON格式',
            'raw_text' => $textContent
        ]);
        exit;
    }
    
    // 没有找到预期的响应内容
    error_log("Gemini API响应中没有找到预期的内容: " . substr($response, 0, 500));
    echo json_encode([
        'success' => false,
        'msg' => '分析结果格式不正确'
    ]);
    
} catch (Exception $e) {
    error_log("分析衣物图片过程中发生异常: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'msg' => '处理请求时发生错误: ' . $e->getMessage()
    ]);
} 