const app = getApp();

Page({
  data: {
    tag: '',
    clothes: [],
    loading: true,
    loadFailed: false,
    // 分类映射，只用作备用
    categoryMap: {
      'tops': '上衣',
      'pants': '裤子',
      'skirts': '裙子',
      'dresses': '连衣裙',
      'coats': '外套',
      'shoes': '鞋子',
      'bags': '包包',
      'accessories': '配饰'
    }
  },

  onLoad: function(options) {
    // 获取标签名称
    const tag = decodeURIComponent(options.tag || '');
    this.setData({ tag });
    
    // 加载该标签下的衣物数据
    this.loadClothesWithTag(tag);
  },

  onShow: function() {
    // 如果设置了需要刷新标签
    if (app.globalData.needRefreshTags) {
      this.loadClothesWithTag(this.data.tag);
      app.globalData.needRefreshTags = false;
    }
  },

  // 加载指定标签的衣物数据
  loadClothesWithTag: function(tag) {
    this.setData({ loading: true, loadFailed: false });
    
    // 过滤标签，保持与index页面的处理一致
    const filteredTag = this.filterTagToChineseOnly(tag);
    console.log('原始标签:', tag, '过滤后标签:', filteredTag);
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothes.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      data: {
        all_clothes: 1 // 获取所有衣物
      },
      success: (res) => {
        if (res.statusCode === 200 && !res.data.error) {
          const allClothes = res.data.data || [];
          
          // 过滤出包含该标签的衣物 (确保衣物存在且有效)
          const filteredClothes = allClothes.filter(item => {
            if (!item || !item.id || !item.tags) return false;
            
            // 使用与index页面相同的标签处理规则
            const tags = item.tags.split(',').map(t => {
              return this.filterTagToChineseOnly(t.trim());
            }).filter(Boolean); // 过滤掉空标签
            
            // 检查过滤后的标签是否包含目标标签
            return tags.includes(filteredTag);
          });
          
          // 处理衣物分类名称
          const processedClothes = filteredClothes.map(item => {
            const newItem = {...item};
            
            // 显示优先级：
            // 1. 如果description中有中文名称，优先使用
            // 2. 否则使用分类中文映射
            let displayCategory = '';
            
            // 尝试从description中解析分类中文名称
            if (newItem.description) {
              try {
                const description = JSON.parse(newItem.description);
                if (description && description.category_name) {
                  displayCategory = description.category_name;
                }
              } catch (e) {
                console.error('解析description失败:', e);
              }
            }
            
            // 如果没有从description中获取到中文名称，则使用映射
            if (!displayCategory && newItem.category) {
              displayCategory = this.data.categoryMap[newItem.category] || newItem.category;
            }
            
            newItem.displayCategory = displayCategory || '其他';
            return newItem;
          });
          
          this.setData({
            clothes: processedClothes,
            loading: false
          });

          // 如果没有找到衣物，提示用户
          if (filteredClothes.length === 0) {
            wx.showToast({
              title: '没有找到包含该标签的衣物',
              icon: 'none',
              duration: 2000
            });
          }
        } else {
          this.setData({ 
            loading: false, 
            loadFailed: true 
          });
          wx.showToast({
            title: '加载失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        this.setData({ 
          loading: false, 
          loadFailed: true 
        });
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },
  
  // 过滤标签，只保留中文字符（与index页面保持一致）
  filterTagToChineseOnly: function(tag) {
    if (!tag) return '';
    // 使用正则表达式匹配中文字符
    const chineseChars = tag.match(/[\u4e00-\u9fa5]+/g);
    if (!chineseChars) return '';
    return chineseChars.join('');
  },

  // 点击衣物项
  onClothingTap: function(e) {
    const { id } = e.currentTarget.dataset;
    // 跳转到衣物详情页
    wx.navigateTo({
      url: `/pages/clothing/detail/detail?id=${id}`
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 跳转到标签更新页面
  goToUpdateTags: function() {
    wx.navigateTo({
      url: '/pages/clothing-tags/update/update'
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadClothesWithTag(this.data.tag);
    wx.stopPullDownRefresh();
  }
}) 