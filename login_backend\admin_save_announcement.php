<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// Handle preflight request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// Check if Authorization header is present
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => 'Missing authorization header']);
    exit();
}

// Get token from Authorization header
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// Verify token is admin token
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => 'Invalid or expired token']);
    exit();
}

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => 'Method not allowed']);
    exit();
}

// Get request body
$requestBody = file_get_contents('php://input');
$data = json_decode($requestBody, true);

// Validate required fields
if (!isset($data['title']) || !isset($data['content']) || !isset($data['start_time']) || !isset($data['end_time']) || !isset($data['status'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => 'Missing required fields']);
    exit();
}

// Extract fields
$id = isset($data['id']) ? intval($data['id']) : null;
$title = $data['title'];
$content = $data['content'];
$startTime = $data['start_time'];
$endTime = $data['end_time'];
$status = intval($data['status']);

// Validate dates
if (!strtotime($startTime) || !strtotime($endTime)) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => 'Invalid date format']);
    exit();
}

// 获取数据库连接
$db = new Database();
$conn = $db->getConnection();

try {
    // 开启事务
    $conn->beginTransaction();
    
    if ($id) {
        // 更新现有公告
        $sql = "UPDATE announcements SET 
                title = :title, 
                content = :content, 
                start_time = :start_time, 
                end_time = :end_time, 
                status = :status,
                updated_at = NOW()
                WHERE id = :id";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindValue(':title', $title);
        $stmt->bindValue(':content', $content);
        $stmt->bindValue(':start_time', $startTime);
        $stmt->bindValue(':end_time', $endTime);
        $stmt->bindValue(':status', $status, PDO::PARAM_INT);
        $stmt->bindValue(':id', $id, PDO::PARAM_INT);
        
        $stmt->execute();
        
        $announcementId = $id;
    } else {
        // 创建新公告
        $sql = "INSERT INTO announcements (title, content, start_time, end_time, status, created_at, updated_at) 
                VALUES (:title, :content, :start_time, :end_time, :status, NOW(), NOW())";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindValue(':title', $title);
        $stmt->bindValue(':content', $content);
        $stmt->bindValue(':start_time', $startTime);
        $stmt->bindValue(':end_time', $endTime);
        $stmt->bindValue(':status', $status, PDO::PARAM_INT);
        
        $stmt->execute();
        
        $announcementId = $conn->lastInsertId();
    }
    
    // 提交事务
    $conn->commit();
    
    echo json_encode([
        'error' => false,
        'data' => [
            'id' => $announcementId,
            'message' => $id ? '公告更新成功' : '公告创建成功'
        ]
    ]);
} catch (Exception $e) {
    // 回滚事务
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '保存公告失败: ' . $e->getMessage()]);
} 