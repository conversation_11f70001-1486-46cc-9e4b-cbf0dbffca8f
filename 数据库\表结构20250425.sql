数据库中的表：
=============

表名: admin_users
-------------
列信息：
  - id (int(11))
  - username (varchar(50))
  - password (varchar(255))
  - real_name (varchar(50))
  - created_at (datetime)
  - updated_at (datetime)
  - last_login (datetime)


表名: api_usage
-------------
列信息：
  - id (int(11))
  - api_name (varchar(50))
  - total_quota (int(11))
  - used_quota (int(11))
  - reset_date (date)
  - last_used (datetime)
  - updated_at (datetime)


表名: clothes
-------------
列信息：
  - id (int(11))
  - user_id (int(11))
  - wardrobe_id (int(11))
  - name (varchar(100))
  - category (varchar(50))
  - image_url (text) [可能包含URL]
    样本值: https://cyymj.oss-cn-shanghai.aliyuncs.com/clothes/segmented_clothing_2_1743657500_7924.jpg
  - tags (varchar(255))
  - description (text)
  - created_at (datetime)
  - updated_at (datetime)
  - image_url 可能在JSON/TEXT中包含URL
    样本值: https://cyymj.oss-cn-shanghai.aliyuncs.com/clothes/segmented_clothing_2_1743657500_7924.jpg...


表名: customer_service_messages
-------------
列信息：
  - id (int(11))
  - user_id (int(11))
  - openid (varchar(100))
  - message_type (varchar(20))
  - content (text)
  - direction (enum('in','out'))
  - is_read (tinyint(1))
  - created_at (datetime)


表名: media_check_results
-------------
列信息：
  - id (int(11))
  - trace_id (varchar(100))
  - user_id (int(11))
  - openid (varchar(100))
  - media_url (text) [可能包含URL]
  - temp_file_path (varchar(255))
  - status (varchar(20))
  - suggest (varchar(20))
  - label (int(11))
  - created_at (datetime)
  - updated_at (datetime)


表名: photos
-------------
列信息：
  - id (int(11))
  - user_id (int(11))
  - image_url (varchar(255)) [可能包含URL]
    样本值: https://cyymj.oss-cn-shanghai.aliyuncs.com/photos/photo_7_1744108412_5834.jpg
  - type (enum('full','half','other'))
  - description (varchar(255))
  - created_at (datetime)
  - updated_at (datetime)


表名: try_on_history
-------------
列信息：
  - id (int(11))
  - user_id (int(11))
  - photo_id (int(11)) [可能包含URL]
  - result_image_url (text) [可能包含URL]
    样本值: https://cyymj.oss-cn-shanghai.aliyuncs.com/try_on/try_on_3_1743756412_cadc6233.jpg
  - clothes_ids (varchar(255))
  - task_id (varchar(100))
  - status (enum('success','failed','processing'))
  - created_at (datetime)
  - updated_at (datetime)
  - result_image_url 可能在JSON/TEXT中包含URL
    样本值: https://cyymj.oss-cn-shanghai.aliyuncs.com/try_on/try_on_3_1743756412_cadc6233.jpg...


表名: user_message_status
-------------
列信息：
  - id (int(11))
  - user_id (int(11))
  - unread_count (int(11))
  - last_update (datetime)
  - created_at (timestamp)


表名: users
-------------
列信息：
  - id (int(11))
  - openid (varchar(100))
  - unionid (varchar(100))
  - session_key (varchar(100))
  - nickname (varchar(100))
  - avatar_url (text) [可能包含URL]
  - gender (tinyint(4))
  - status (tinyint(4))
  - created_at (datetime)
  - updated_at (datetime)


表名: wardrobes
-------------
列信息：
  - id (int(11))
  - user_id (int(11))
  - name (varchar(100))
  - description (varchar(255))
  - image_url (varchar(255)) [可能包含URL]
  - sort_order (int(11))
  - is_default (tinyint(1))
  - created_at (datetime)
  - updated_at (datetime)


分析完成。
