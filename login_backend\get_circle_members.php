<?php
// 获取圈子成员列表和统计信息API
// 模块2：圈子成员管理模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查找用户所在的圈子
    $findCircleSql = "SELECT cm.circle_id, cm.role, c.name as circle_name
                      FROM circle_members cm 
                      JOIN outfit_circles c ON cm.circle_id = c.id 
                      WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
    $findCircleStmt = $conn->prepare($findCircleSql);
    $findCircleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $findCircleStmt->execute();
    
    $userCircle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$userCircle) {
        echo json_encode([
            'status' => 'error',
            'message' => '您当前未加入任何圈子'
        ]);
        exit;
    }
    
    $circleId = $userCircle['circle_id'];
    $userRole = $userCircle['role'];
    
    // 获取圈子成员列表和统计信息
    $membersSql = "SELECT cm.user_id, cm.role, cm.joined_at, 
                          u.nickname, u.avatar_url,
                          COALESCE(cms.wardrobe_count, 0) as wardrobe_count,
                          COALESCE(cms.clothes_count, 0) as clothes_count,
                          COALESCE(cms.outfit_count, 0) as outfit_count,
                          COALESCE(cms.clothing_category_count, 0) as clothing_category_count,
                          COALESCE(cms.outfit_category_count, 0) as outfit_category_count,
                          COALESCE(cms.tag_count, 0) as tag_count,
                          cms.last_contribution_at
                   FROM circle_members cm 
                   JOIN users u ON cm.user_id = u.id 
                   LEFT JOIN circle_member_stats cms ON cm.circle_id = cms.circle_id AND cm.user_id = cms.user_id
                   WHERE cm.circle_id = :circle_id AND cm.status = 'active'
                   ORDER BY cm.role DESC, cm.joined_at ASC";
    $membersStmt = $conn->prepare($membersSql);
    $membersStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $membersStmt->execute();
    
    $members = [];
    while ($member = $membersStmt->fetch(PDO::FETCH_ASSOC)) {
        $memberData = [
            'user_id' => $member['user_id'],
            'nickname' => $member['nickname'] ?? '未知用户',
            'avatar_url' => $member['avatar_url'],
            'role' => $member['role'],
            'joined_at' => $member['joined_at'],
            'is_current_user' => $member['user_id'] == $userId,
            'stats' => [
                'wardrobe_count' => intval($member['wardrobe_count']),
                'clothes_count' => intval($member['clothes_count']),
                'outfit_count' => intval($member['outfit_count']),
                'clothing_category_count' => intval($member['clothing_category_count']),
                'outfit_category_count' => intval($member['outfit_category_count']),
                'tag_count' => intval($member['tag_count']),
                'total_contributions' => intval($member['wardrobe_count']) + 
                                       intval($member['clothes_count']) + 
                                       intval($member['outfit_count']) + 
                                       intval($member['clothing_category_count']) + 
                                       intval($member['outfit_category_count']) + 
                                       intval($member['tag_count']),
                'last_contribution_at' => $member['last_contribution_at']
            ]
        ];
        
        $members[] = $memberData;
    }
    
    // 计算圈子总体统计
    $totalStatsSql = "SELECT 
                        COUNT(DISTINCT cm.user_id) as total_members,
                        SUM(COALESCE(cms.wardrobe_count, 0)) as total_wardrobes,
                        SUM(COALESCE(cms.clothes_count, 0)) as total_clothes,
                        SUM(COALESCE(cms.outfit_count, 0)) as total_outfits,
                        SUM(COALESCE(cms.clothing_category_count, 0)) as total_clothing_categories,
                        SUM(COALESCE(cms.outfit_category_count, 0)) as total_outfit_categories,
                        SUM(COALESCE(cms.tag_count, 0)) as total_tags
                      FROM circle_members cm 
                      LEFT JOIN circle_member_stats cms ON cm.circle_id = cms.circle_id AND cm.user_id = cms.user_id
                      WHERE cm.circle_id = :circle_id AND cm.status = 'active'";
    $totalStatsStmt = $conn->prepare($totalStatsSql);
    $totalStatsStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $totalStatsStmt->execute();
    
    $totalStats = $totalStatsStmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'status' => 'success',
        'data' => [
            'circle_name' => $userCircle['circle_name'],
            'user_role' => $userRole,
            'members' => $members,
            'total_stats' => [
                'member_count' => intval($totalStats['total_members']),
                'wardrobe_count' => intval($totalStats['total_wardrobes']),
                'clothes_count' => intval($totalStats['total_clothes']),
                'outfit_count' => intval($totalStats['total_outfits']),
                'clothing_category_count' => intval($totalStats['total_clothing_categories']),
                'outfit_category_count' => intval($totalStats['total_outfit_categories']),
                'tag_count' => intval($totalStats['total_tags'])
            ]
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '获取成员信息失败：' . $e->getMessage()
    ]);
}
?>
