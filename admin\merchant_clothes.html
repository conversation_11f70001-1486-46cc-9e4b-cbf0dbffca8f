<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户衣物 - 次元衣柜管理后台</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <style>
        .clothes-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-gap: 20px;
            margin-top: 20px;
        }
        
        .clothes-item {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .clothes-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .clothes-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .clothes-info {
            padding: 10px;
        }
        
        .clothes-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .clothes-category {
            font-size: 12px;
            color: #666;
        }
        
        .search-box {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }
        
        .search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        
        .search-btn {
            padding: 8px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .search-btn:hover {
            background-color: #40a9ff;
        }
        
        .merchant-info {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 8px;
        }
        
        .merchant-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
        }
        
        .merchant-details {
            flex: 1;
        }
        
        .merchant-name {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .merchant-stats {
            font-size: 14px;
            color: #666;
        }
        
        .merchant-stat {
            margin-right: 15px;
        }
        
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #1890ff;
            text-decoration: none;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .empty-icon {
            font-size: 48px;
            color: #d9d9d9;
            margin-bottom: 10px;
        }
        
        .empty-text {
            color: #999;
            font-size: 16px;
        }
        
        @media (max-width: 1200px) {
            .clothes-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .clothes-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .merchant-info {
                flex-direction: column;
                text-align: center;
            }
            
            .merchant-avatar {
                margin-right: 0;
                margin-bottom: 10px;
            }
        }
        
        @media (max-width: 480px) {
            .clothes-grid {
                grid-template-columns: 1fr;
            }
        }
        
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>商户衣物</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button class="logout-btn" id="logoutBtn">退出</button>
                </div>
            </div>
            
            <a href="merchant_list.html" class="back-btn">返回商户列表</a>
            
            <div id="merchantInfo" class="merchant-header">
                <!-- 商户信息将通过JavaScript动态添加 -->
            </div>
            
            <div class="card">
                <h3 class="card-title">衣物列表</h3>
                
                <div class="search-container">
                    <input type="text" id="searchKeyword" class="search-input" placeholder="输入衣物名称或分类搜索">
                    <button id="searchBtn" class="search-btn">搜索</button>
                </div>
                
                <div id="clothesError" class="error-container"></div>
                
                <div id="clothesGrid" class="clothes-grid">
                    <!-- 衣物项将通过JavaScript动态添加 -->
                </div>
                
                <div id="emptyState" class="empty-state" style="display: none;">
                    <p>该商户暂无衣物</p>
                </div>
                
                <div id="clothesLoading" class="loading-indicator">加载中...</div>
                
                <div id="paginationContainer" class="pagination"></div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/merchant_clothes.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为merchant
            Sidebar.init('merchant');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化商户衣物页面
            if (typeof MerchantClothes !== 'undefined') {
                MerchantClothes.init();
            }
        });
    </script>
</body>
</html> 