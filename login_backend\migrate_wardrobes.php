<?php
/**
 * 衣橱数据迁移脚本
 * 为每个用户创建默认衣橱并迁移现有衣物
 * 
 * 使用方法：直接通过命令行运行此脚本
 * php migrate_wardrobes.php
 */

require_once 'config.php';
require_once 'db.php';

// 初始化数据库连接
$db = new Database();
$conn = $db->getConnection();

/**
 * 记录日志
 */
function log_message($message) {
    echo date('Y-m-d H:i:s') . " - " . $message . PHP_EOL;
    error_log($message);
}

/**
 * 为所有用户创建默认衣橱
 */
function create_default_wardrobes($conn) {
    log_message("开始为所有用户创建默认衣橱...");
    
    // 获取所有用户
    $stmt = $conn->query("SELECT id FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $totalCount = count($users);
    $successCount = 0;
    
    log_message("共发现 {$totalCount} 个用户");
    
    foreach ($users as $user) {
        $userId = $user['id'];
        
        // 检查用户是否已有默认衣橱
        $checkStmt = $conn->prepare("SELECT id FROM wardrobes WHERE user_id = :user_id AND is_default = 1");
        $checkStmt->bindParam(':user_id', $userId);
        $checkStmt->execute();
        $existingWardrobe = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existingWardrobe) {
            log_message("用户 #{$userId} 已有默认衣橱，跳过");
            $successCount++;
            continue;
        }
        
        // 创建默认衣橱
        try {
            $insertStmt = $conn->prepare("INSERT INTO wardrobes (user_id, name, description, is_default, created_at) VALUES (:user_id, '我的衣橱', '默认衣橱', 1, NOW())");
            $insertStmt->bindParam(':user_id', $userId);
            $insertStmt->execute();
            
            $wardrobeId = $conn->lastInsertId();
            log_message("为用户 #{$userId} 创建默认衣橱，ID: {$wardrobeId}");
            $successCount++;
        } catch (PDOException $e) {
            log_message("为用户 #{$userId} 创建默认衣橱失败: " . $e->getMessage());
        }
    }
    
    log_message("默认衣橱创建完成，总用户数: {$totalCount}，成功: {$successCount}");
    return $successCount;
}

/**
 * 更新现有衣物，关联到默认衣橱
 */
function update_existing_clothes($conn) {
    log_message("开始更新现有衣物，关联到默认衣橱...");
    
    // 获取未关联衣橱的衣物
    $stmt = $conn->query("SELECT c.id, c.user_id FROM clothes c WHERE c.wardrobe_id IS NULL");
    $clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $totalCount = count($clothes);
    $successCount = 0;
    
    log_message("共发现 {$totalCount} 件未关联衣橱的衣物");
    
    foreach ($clothes as $cloth) {
        $clothId = $cloth['id'];
        $userId = $cloth['user_id'];
        
        // 获取用户的默认衣橱
        $wardrobeStmt = $conn->prepare("SELECT id FROM wardrobes WHERE user_id = :user_id AND is_default = 1");
        $wardrobeStmt->bindParam(':user_id', $userId);
        $wardrobeStmt->execute();
        $wardrobe = $wardrobeStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$wardrobe) {
            log_message("找不到用户 #{$userId} 的默认衣橱，跳过衣物 #{$clothId}");
            continue;
        }
        
        $wardrobeId = $wardrobe['id'];
        
        // 更新衣物关联到默认衣橱
        try {
            $updateStmt = $conn->prepare("UPDATE clothes SET wardrobe_id = :wardrobe_id WHERE id = :id");
            $updateStmt->bindParam(':wardrobe_id', $wardrobeId);
            $updateStmt->bindParam(':id', $clothId);
            $updateStmt->execute();
            
            log_message("衣物 #{$clothId} 已关联到衣橱 #{$wardrobeId}");
            $successCount++;
        } catch (PDOException $e) {
            log_message("更新衣物 #{$clothId} 失败: " . $e->getMessage());
        }
    }
    
    log_message("衣物更新完成，总数: {$totalCount}，成功: {$successCount}");
    return $successCount;
}

// 执行迁移
try {
    log_message("开始衣橱数据迁移...");
    
    // 创建默认衣橱
    $defaultWardrobe = create_default_wardrobes($conn);
    
    // 更新现有衣物
    $updatedClothes = update_existing_clothes($conn);
    
    log_message("所有迁移完成！");
    log_message("默认衣橱创建: {$defaultWardrobe}");
    log_message("衣物更新: {$updatedClothes}");
    
} catch (Exception $e) {
    log_message("迁移过程中发生错误: " . $e->getMessage());
}
?> 