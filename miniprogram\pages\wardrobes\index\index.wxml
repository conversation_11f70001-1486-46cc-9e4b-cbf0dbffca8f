<view class="container">
  <!-- 标题栏 -->
  <view class="header">
    <view class="title">我的衣橱</view>
    <view class="header-options">

      <view class="add-btn" bindtap="goToAddWardrobe">
        <image src="/images/add.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 衣橱列表 -->
  <view class="wardrobe-list" wx:if="{{wardrobes.length > 0}}">
    <view class="wardrobe-item" wx:for="{{wardrobes}}" wx:key="id">
      <view class="wardrobe-info" bindtap="viewClothes" data-id="{{item.id}}" data-name="{{item.name}}">
        <view class="wardrobe-name">
          {{item.name}}
        </view>
        <view class="wardrobe-desc" wx:if="{{item.description}}">{{item.description}}</view>
        <view class="wardrobe-count">衣物数量: {{item.clothes_count}}</view>
      </view>
      <view class="wardrobe-actions">
        <view class="edit-btn" catchtap="editWardrobe" data-wardrobe="{{item}}">
          <image src="/images/edit.png" mode="aspectFit"></image>
        </view>
        <view class="delete-btn" catchtap="deleteWardrobe" data-id="{{item.id}}" data-is-default="{{item.is_default == 1}}">
          <image src="/images/delete.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <image class="empty-image" src="/images/empty-wardrobe.png" mode="aspectFit"></image>
    <view class="empty-text">您还没有创建衣橱</view>
    <view class="create-btn" bindtap="goToAddWardrobe">创建衣橱</view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMoreData}}">
    <view bindtap="loadMore" wx:if="{{!isLoading}}">加载更多</view>
    <view wx:else>加载中...</view>
  </view>
</view> 