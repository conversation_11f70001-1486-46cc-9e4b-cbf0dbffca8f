const app = getApp();

Page({
  data: {
    // 进度相关
    showProgress: true, // 是否显示进度条
    progressStatus: 'queued', // 当前进度状态：queued, uploading, processing, finishing, completed, failed
    progressPercent: 0, // 进度百分比
    progressPercentStr: '0%', // 带百分号的进度字符串
    progressMessage: '排队中...', // 进度消息
    countdownTime: 30, // 倒计时（秒）
    pollingTaskId: null, // 当前轮询的任务ID
    pollingTimer: null, // 轮询计时器
    countdownTimer: null, // 倒计时计时器
    
    // 强制状态控制变量
    currentStageStartTime: 0, // 当前阶段开始时间戳
    forceStageMinDuration: {  // 各阶段最小持续时间（毫秒）
      queued: 2000,           // 排队阶段至少2秒
      uploading: 2000,        // 上传阶段至少2秒
      processing: 3000,       // 处理阶段至少3秒
      finishing: 2000         // 完成阶段至少2秒
    },
    actualTaskStatus: '',     // 后端返回的实际任务状态
    actualTaskProgress: 0,    // 后端返回的实际进度
    stagesSequence: ['queued', 'uploading', 'processing', 'finishing', 'completed'], // 状态顺序
    
    // API状态跟踪变量
    apiRequestSent: false,       // API请求是否已发送
    apiResponseReceived: false,  // API响应是否已接收
    apiResponseData: null,       // API响应数据
    apiTaskId: null,             // API返回的任务ID
    waitingForApiResponse: false // 进度条已完成但正在等待API响应
  },

  onLoad: function(options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '试衣进行中'
    });
    
    console.log("进度页面加载，参数:", options);
    
    // 初始化进度显示
    this.setData({
      showProgress: true,
      progressStatus: 'queued',
      progressPercent: 0,
      progressPercentStr: '0%',
      progressMessage: '排队中...',
      currentStageStartTime: Date.now()
    });
    
    // 从全局变量获取试衣请求参数
    const tryOnRequest = app.globalData.currentTryOnRequest;
    if (!tryOnRequest) {
      console.error('未找到试衣请求参数');
      this.handleError('试衣参数错误，请重试');
      return;
    }
    
    // 启动计时器
    this.startCountdown();
    
    // 立即开始进度动画
    this.startProgressAnimation();
    
    // 延迟一点时间再发送API请求，以便用户能看到进度动画
    setTimeout(() => {
      this.sendTryOnRequest(tryOnRequest);
    }, 500);
  },
  
  // 发送试穿请求
  sendTryOnRequest: function(requestData) {
    console.log('发送试穿请求数据:', requestData);
    
    // 标记API请求已发送
    this.setData({
      apiRequestSent: true
    });
    
    // 发起试穿请求
    wx.request({
      url: `${app.globalData.apiBaseUrl}/try_on.php`,
      method: 'POST',
      header: {
        'Authorization': app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: requestData,
      success: (res) => {
        console.log('试穿请求响应:', res.data);
        
        // 标记API响应已接收
        this.setData({
          apiResponseReceived: true,
          apiResponseData: res.data.data
        });
        
        if (res.statusCode === 200 && !res.data.error) {
          // 保存整个试衣结果数据到全局变量
          app.globalData.tryOnResult = res.data.data;
          app.globalData.tryOnTaskId = res.data.data.task_id;
          
          console.log('已保存试衣结果到全局变量:', app.globalData.tryOnResult);
          
          // 检查是否返回了任务ID（异步模式）
          if (res.data.data.status === 'processing' && res.data.data.task_id) {
            const taskId = res.data.data.task_id;
            console.log("收到任务ID，开始轮询状态:", taskId);
            
            // 保存任务ID
            this.setData({
              apiTaskId: taskId
            });
            
            // 开始轮询任务状态
            this.startPollingTaskStatus(taskId);
          } else if (res.data.data.result_image_url) {
            console.log("API直接返回结果图片");
            
            // 检查进度显示是否已完成
            if (this.data.progressStatus === 'completed') {
              // 进度已完成，直接显示结果
              this.handleApiResultReady(res.data.data);
            }
            // 如果进度尚未完成，等待进度完成后会自动检查API结果
          }
        } else {
          // 检查是否是商家试衣次数用完的特殊情况
          if (res.data.merchant_credits_depleted) {
            // 暂停进度动画
            if (this.data.countdownTimer) {
              clearInterval(this.data.countdownTimer);
            }
            
            // 显示确认对话框
            wx.showModal({
              title: '商家试衣次数已用完',
              content: res.data.message || '商家试衣次数已用完，如果继续将扣除您自己的试穿次数',
              cancelText: '取消',
              confirmText: '继续',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  // 用户选择继续，将请求数据添加force_use_user_credits参数后重新发送
                  const newRequestData = {...requestData};
                  newRequestData.force_use_user_credits = true;
                  
                  console.log('用户确认使用自己的次数继续试穿', newRequestData);
                  
                  // 重新启动进度动画和倒计时
                  this.startCountdown();
                  
                  // 重新发送请求
                  this.sendTryOnRequest(newRequestData);
                } else {
                  // 用户取消，返回上一页
                  wx.navigateBack();
                }
              }
            });
          } else if (res.data.limit_exceeded) {
            // 清除所有计时器
            this.clearAllTimers();
            
            // 隐藏进度条并重置所有相关状态
            this.setData({
              showProgress: false,
              apiRequestSent: false,
              apiResponseReceived: false,
              waitingForApiResponse: false,
              progressStatus: '',
              progressPercent: 0,
              progressPercentStr: '0%',
              progressMessage: '',
              countdownTime: 0
            });
            
            // 显示次数限制提示弹窗
            wx.showModal({
              title: '试衣次数已用完',
              content: res.data.msg || '您的试衣次数已用完。',
              showCancel: true,
              cancelText: '返回',
              confirmText: '购买点数',
              success: (result) => {
                if (result.confirm) {
                  // 跳转到购买页面
                  wx.navigateTo({
                    url: '/pages/purchase/try_on_count/index'
                  });
                } else {
                  // 用户点击取消，返回上一页
                  wx.navigateBack();
                }
              }
            });
          } else {
            // 其他错误情况
            this.handleError(res.data.msg || '试衣失败，请重试');
          }
        }
      },
      fail: (err) => {
        console.error("调用试衣API失败:", err);
        this.handleError('网络错误，请重试');
      }
    });
  },
  
  // 开始渐进式进度动画
  startProgressAnimation: function() {
    // 从排队阶段开始，按序显示各阶段
    const animateStages = () => {
      // 如果进度条已被隐藏，立即终止动画
      if (!this.data.showProgress) {
        console.log("检测到进度条已隐藏，终止动画");
        return;
      }
      
      const currentStatus = this.data.progressStatus;
      const currentIndex = this.data.stagesSequence.indexOf(currentStatus);
      const nextStage = this.data.stagesSequence[currentIndex + 1];
      
      // 检查当前阶段是否已完成最小持续时间
      const now = Date.now();
      const elapsed = now - this.data.currentStageStartTime;
      const minDuration = this.data.forceStageMinDuration[currentStatus] || 0;
      
      console.log(`进度动画 - 当前阶段: ${currentStatus}, 已经持续: ${elapsed}ms, 最小持续: ${minDuration}ms`);
      
      if (elapsed < minDuration) {
        // 当前阶段未满足最小持续时间，增加些进度后继续等待
        this.incrementCurrentStageProgress();
        // 再次检查是否应该继续
        if (this.data.showProgress) {
          setTimeout(animateStages, 500);
        }
        return;
      }
      
      // 如果没有下一阶段或已到最终阶段，检查API响应
      if (!nextStage || nextStage === 'completed') {
        // 设为完成状态
        this.setData({
          progressStatus: 'completed',
          progressPercent: 100,
          progressPercentStr: '100%',
          progressMessage: '完成',
          currentStageStartTime: now
        });
        
        // 检查API是否已返回结果
        if (this.data.apiResponseReceived && this.data.apiResponseData) {
          if (this.data.apiResponseData.result_image_url) {
            // API已返回图片结果，处理完成
            this.handleApiResultReady(this.data.apiResponseData);
          } else if (this.data.apiTaskId) {
            // API已返回任务ID，但尚未完成，保持在完成状态等待轮询结果
            this.setData({
              waitingForApiResponse: true,
              progressMessage: '处理中...'
            });
          }
        } else {
          // API尚未返回任何结果，保持在完成状态等待API响应
          this.setData({
            waitingForApiResponse: true,
            progressMessage: '处理中...'
          });
        }
        return;
      }
      
      // 进入下一阶段
      let progressMessage = '';
      let progressPercent = 0;
      
      switch(nextStage) {
        case 'uploading':
          progressMessage = '提交数据...';
          progressPercent = 25;
          break;
        case 'processing':
          progressMessage = '合成中...';
          progressPercent = 50;
          break;
        case 'finishing':
          progressMessage = '即将完成...';
          progressPercent = 80;
          break;
        case 'completed':
          progressMessage = '完成';
          progressPercent = 100;
          break;
      }
      
      // 更新阶段和时间
      this.setData({
        progressStatus: nextStage,
        progressMessage: progressMessage,
        progressPercent: progressPercent,
        progressPercentStr: progressPercent + '%',
        currentStageStartTime: now
      });
      
      // 计划下一次阶段检查
      const nextDelay = this.data.forceStageMinDuration[nextStage];
      setTimeout(animateStages, nextDelay);
    };
    
    // 启动动画
    setTimeout(animateStages, 500);
  },
  
  // 增加当前阶段的进度
  incrementCurrentStageProgress: function() {
    const currentStatus = this.data.progressStatus;
    const currentIndex = this.data.stagesSequence.indexOf(currentStatus);
    let basePercent = 0;
    let maxPercent = 0;
    
    // 根据当前阶段确定进度范围
    switch(currentStatus) {
      case 'queued':
        basePercent = 0;
        maxPercent = 20;
        break;
      case 'uploading':
        basePercent = 25;
        maxPercent = 45;
        break;
      case 'processing':
        basePercent = 50;
        maxPercent = 75;
        break;
      case 'finishing':
        basePercent = 80;
        maxPercent = 95;
        break;
      case 'completed':
        basePercent = 100;
        maxPercent = 100;
        break;
    }
    
    // 计算当前应该显示的进度
    const elapsed = Date.now() - this.data.currentStageStartTime;
    const minDuration = this.data.forceStageMinDuration[currentStatus] || 1000;
    const progress = Math.min(elapsed / minDuration, 1);
    
    // 线性插值计算进度百分比
    const currentPercent = basePercent + (maxPercent - basePercent) * progress;
    
    // 更新进度显示
    this.setData({
      progressPercent: currentPercent,
      progressPercentStr: Math.round(currentPercent) + '%'
    });
  },
  
  // 启动倒计时
  startCountdown: function() {
    // 清除之前的倒计时
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }
    
    // 设置初始倒计时
    this.setData({
      countdownTime: 30 // 默认30秒
    });
    
    // 创建倒计时
    const timer = setInterval(() => {
      const currentTime = this.data.countdownTime - 1;
      
      if (currentTime <= 0) {
        // 倒计时结束，但不清除计时器，让其保持在0
        this.setData({
          countdownTime: 0
        });
      } else {
        // 更新倒计时
        this.setData({
          countdownTime: currentTime
        });
      }
    }, 1000);
    
    // 保存倒计时ID
    this.setData({
      countdownTimer: timer
    });
  },
  
  // 开始轮询任务状态
  startPollingTaskStatus: function(taskId) {
    // 清除之前的轮询计时器
    if (this.data.pollingTimer) {
      clearInterval(this.data.pollingTimer);
    }
    
    // 设置轮询间隔（毫秒）
    const pollingInterval = 2000; // 2秒查询一次
    
    // 创建新的轮询计时器
    const timer = setInterval(() => {
      this.pollTaskStatus(taskId);
    }, pollingInterval);
    
    // 保存计时器ID
    this.setData({
      pollingTimer: timer
    });
    
    // 立即执行一次轮询
    this.pollTaskStatus(taskId);
  },
  
  // 轮询任务状态
  pollTaskStatus: function(taskId) {
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_try_on_status.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      data: {
        task_id: taskId
      },
      success: (res) => {
        console.log("轮询任务状态响应:", res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          const statusData = res.data.data;
          
          // 更新倒计时
          if (statusData.time_remaining) {
            this.setData({
              countdownTime: statusData.time_remaining
            });
          }
          
          // 检查是否完成
          if (statusData.status === 'completed' && statusData.result_image_url) {
            console.log("任务已完成，处理结果");
            
            // 更新全局结果数据
            app.globalData.tryOnResult = statusData;
            
            // 处理完成的结果
            this.handleApiResultReady(statusData);
          }
        } else {
          console.error("轮询任务状态失败:", res.data.msg || "未知错误");
        }
      },
      fail: (err) => {
        console.error("轮询任务状态网络错误:", err);
      }
    });
  },
  
  // API结果准备好时的处理
  handleApiResultReady: function(resultData) {
    console.log("API结果准备好，处理结果:", resultData);
    
    // 清除所有计时器
    this.clearAllTimers();
    
    // 确保显示100%完成状态
    this.setData({
      progressStatus: 'completed',
      progressPercent: 100,
      progressPercentStr: '100%',
      progressMessage: '完成',
      countdownTime: 0,
      waitingForApiResponse: false
    });
    
    // 保存试衣结果到全局变量
    app.globalData.tryOnResult = resultData;
    
    // 延迟一小段时间再跳转，让用户看到100%的进度
    setTimeout(() => {
      // 隐藏进度条
      this.setData({
        showProgress: false
      });
      
      // 使用redirectTo而不是navigateTo跳转到试衣结果页面
      // 这样可以避免用户返回到进度页
      wx.redirectTo({
        url: '/pages/try_on/result/index'
      });
    }, 1000);
  },
  
  // 处理错误
  handleError: function(errorMessage) {
    // 清除所有计时器
    this.clearAllTimers();
    
    // 显示错误提示
    wx.showModal({
      title: '试衣失败',
      content: errorMessage || '处理过程中遇到错误',
      showCancel: true,
      cancelText: '返回',
      confirmText: '购买点数',
      success: (res) => {
        if (res.confirm) {
          // 用户点击"购买点数"按钮，跳转到购买页面
          wx.navigateTo({
            url: '/pages/purchase/try_on_count/index'
          });
        } else {
          // 用户点击"返回"按钮，返回上一页
          wx.navigateBack();
        }
      }
    });
  },
  
  // 清除所有计时器
  clearAllTimers: function() {
    if (this.data.pollingTimer) {
      clearInterval(this.data.pollingTimer);
    }
    
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }
  },
  
  onUnload: function() {
    // 页面卸载时清除所有计时器
    this.clearAllTimers();
  }
}); 