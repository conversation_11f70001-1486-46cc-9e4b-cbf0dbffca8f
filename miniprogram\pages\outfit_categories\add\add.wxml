<!--pages/outfit_categories/add/add.wxml-->
<view class="container">

  
  <view class="form-group">
    <view class="form-item">
      <view class="form-label">分类名称</view>
      <view class="form-input-container">
        <input class="form-input" bindinput="onNameInput" placeholder="请输入分类名称" maxlength="20" value="{{name}}" />
        <text class="counter">{{nameCount}}/20</text>
      </view>
    </view>
    
    <view class="form-item">
      <view class="form-label">分类描述</view>
      <view class="form-input-container">
        <textarea class="form-textarea" bindinput="onDescInput" placeholder="请输入分类描述（选填）" maxlength="100" value="{{description}}"></textarea>
        <text class="counter">{{descCount}}/100</text>
      </view>
    </view>
    
    <view class="form-item">
      <view class="form-label">排序</view>
      <view class="form-input-container">
        <input class="form-input" type="number" bindinput="onSortInput" placeholder="请输入排序数字（数字越小排序越靠前）" value="{{sort}}" />
      </view>
    </view>
  </view>
  
  <view class="button-group">
    <button class="cancel-button" bindtap="onCancel">取消</button>
    <button class="submit-button" bindtap="onSubmit">保存</button>
  </view>
</view>