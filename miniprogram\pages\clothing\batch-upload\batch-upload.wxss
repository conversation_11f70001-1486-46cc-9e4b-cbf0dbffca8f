.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  overflow: hidden;
  padding-top: 10px;
}

/* 状态区域样式 */
.status-area {
  padding: 15px;
  background-color: #fff;
  margin: 0 15px 10px;
  border-radius: 10px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.status-text {
  font-size: 15px;
  color: #333;
  margin-bottom: 8px;
}

.progress-bar {
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  background-color: #333;
  border-radius: 3px;
  transition: width 0.3s;
}

.upload-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.action-btn {
  font-size: 14px;
  color: #333;
  background-color: #f5f5f5;
  padding: 5px 10px;
  border-radius: 4px;
}

/* 衣物列表区域样式 */
.clothes-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 15px;
  width: 100%;
  box-sizing: border-box;
}

.clothing-item {
  background-color: #fff;
  border-radius: 10px;
  margin-bottom: 15px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.clothing-item.editing {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.preview-section {
  display: flex;
  padding: 15px;
  position: relative;
}

.clothing-image {
  width: 80px;
  height: 80px;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin-right: 15px;
  object-fit: contain;
}

.clothing-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.clothing-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
}

.clothing-category {
  font-size: 14px;
  color: #666;
}

/* 详情按钮样式 */
.detail-btn {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #333;
  background-color: #f5f5f5;
  padding: 4px 10px;
  border-radius: 4px;
}

/* 编辑区域样式 */
.edit-section {
  border-top: 1px solid #f0f0f0;
  padding: 15px;
}

.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.edit-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.close-btn {
  font-size: 14px;
  color: #333;
  background-color: #f5f5f5;
  padding: 4px 10px;
  border-radius: 4px;
}

.input-group {
  margin-bottom: 15px;
}

.input-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.input-control {
  width: 100%;
  height: 40px;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 15px;
  color: #333;
}

/* 类别选择样式 */
.category-options {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

.category-option {
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  margin: 0 5px 10px;
}

.category-option.selected {
  background-color: #333;
  color: #fff;
}

/* 标签选择样式 */
.tags-options {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

.tag-option {
  padding: 6px 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 13px;
  color: #333;
  margin: 0 5px 10px;
}

.tag-option.selected {
  background-color: #333;
  color: #fff;
}

.tag-option.custom {
  border: 1px dashed #ddd;
  background-color: #fff;
}

.tag-option.custom.selected {
  border-color: #333;
  background-color: #333;
  color: #fff;
}

/* 底部按钮区域样式 */
.bottom-actions {
  padding: 15px;
  background-color: #fff;
  box-shadow: 0 -1px 3px rgba(0,0,0,0.05);
}

.action-button {
  width: 100%;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  border-radius: 22px;
}

.action-button.primary {
  background-color: #333;
  color: #fff;
} 