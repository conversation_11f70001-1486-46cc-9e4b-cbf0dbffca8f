<?php
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

try {
    $auth = new Auth();
    
    // 验证用户token
    $headers = getallheaders();
    $authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : null;
    
    if (!$authHeader) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => '未授权访问']);
        exit;
    }
    
    $token = $authHeader;
    $tokenData = $auth->verifyToken($token);
    
    if (!$tokenData) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => 'Token无效']);
        exit;
    }
    
    $userId = $tokenData['user_id'] ?? $tokenData['sub'];
    
    // 获取请求数据 - 同时支持表单数据和JSON数据
    $data = $_POST;
    
    // 如果$_POST为空，尝试从JSON输入流获取数据
    if (empty($data)) {
        $inputJSON = file_get_contents('php://input');
        $data = json_decode($inputJSON, true);
        
        // 记录接收到的数据以便调试
        error_log("接收到的JSON数据: " . $inputJSON);
    }
    
    // 记录解析后的数据
    error_log("解析后的数据: " . print_r($data, true));
    
    // 验证必填参数
    if (empty($data['source_wardrobe_id']) || empty($data['target_wardrobe_id']) || empty($data['clothes_ids']) || empty($data['target_category'])) {
        http_response_code(400);
        
        // 输出详细的错误信息，帮助调试
        $missingParams = [];
        if (empty($data['source_wardrobe_id'])) $missingParams[] = 'source_wardrobe_id';
        if (empty($data['target_wardrobe_id'])) $missingParams[] = 'target_wardrobe_id';
        if (empty($data['clothes_ids'])) $missingParams[] = 'clothes_ids';
        if (empty($data['target_category'])) $missingParams[] = 'target_category';
        
        echo json_encode([
            'error' => true, 
            'msg' => '缺少必要参数',
            'missing_params' => $missingParams,
            'received_data' => $data
        ]);
        exit;
    }
    
    $sourceWardrobeId = (int)$data['source_wardrobe_id'];
    $targetWardrobeId = (int)$data['target_wardrobe_id'];
    $clothesIds = is_array($data['clothes_ids']) ? $data['clothes_ids'] : [$data['clothes_ids']];
    $targetCategory = $data['target_category'];
    
    // 检查源衣橱和目标衣橱是否存在且属于该用户
    $db = new Database();
    $conn = $db->getConnection();
    
    // 验证源衣橱和目标衣橱相同（这个API只处理同一个衣橱内的分类更新）
    if ($sourceWardrobeId !== $targetWardrobeId) {
        http_response_code(400);
        echo json_encode(['error' => true, 'msg' => '源衣橱和目标衣橱必须相同']);
        exit;
    }
    
    // 检查衣橱权限
    $checkWardrobeSql = "SELECT id FROM wardrobes WHERE id = :id AND user_id = :user_id";
    $checkWardrobeStmt = $conn->prepare($checkWardrobeSql);
    $checkWardrobeStmt->bindParam(':id', $sourceWardrobeId, PDO::PARAM_INT);
    $checkWardrobeStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkWardrobeStmt->execute();
    
    if ($checkWardrobeStmt->rowCount() === 0) {
        http_response_code(403);
        echo json_encode(['error' => true, 'msg' => '无权操作该衣橱']);
        exit;
    }
    
    // 验证分类是否存在
    $checkCategorySql = "SELECT id, code FROM clothing_categories WHERE code = :code AND (user_id = :user_id OR is_system = 1)";
    $checkCategoryStmt = $conn->prepare($checkCategorySql);
    $checkCategoryStmt->bindParam(':code', $targetCategory, PDO::PARAM_STR);
    $checkCategoryStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkCategoryStmt->execute();
    
    if ($checkCategoryStmt->rowCount() === 0) {
        http_response_code(404);
        echo json_encode(['error' => true, 'msg' => '目标分类不存在']);
        exit;
    }
    
    $categoryInfo = $checkCategoryStmt->fetch(PDO::FETCH_ASSOC);
    $categoryId = $categoryInfo['id'];
    
    // 开始事务
    $conn->beginTransaction();
    
    try {
        // 更新衣物分类
        $placeholders = [];
        for ($i = 0; $i < count($clothesIds); $i++) {
            $placeholders[] = ":id" . $i;
        }
        
        $updateSql = "UPDATE clothes SET 
                        category = :category,
                        category_id = :category_id,
                        updated_at = NOW()
                      WHERE id IN (" . implode(',', $placeholders) . ") 
                        AND wardrobe_id = :wardrobe_id";
        
        $updateStmt = $conn->prepare($updateSql);
        $updateStmt->bindParam(':category', $targetCategory, PDO::PARAM_STR);
        $updateStmt->bindParam(':category_id', $categoryId, PDO::PARAM_INT);
        $updateStmt->bindParam(':wardrobe_id', $sourceWardrobeId, PDO::PARAM_INT);
        
        // 绑定衣物ID数组，使用命名参数
        foreach ($clothesIds as $index => $clothesId) {
            $updateStmt->bindValue(":id" . $index, $clothesId, PDO::PARAM_INT);
        }
        
        $updateStmt->execute();
        
        // 提交事务
        $conn->commit();
        
        // 返回成功响应
        echo json_encode([
            'error' => false,
            'code' => 0,
            'msg' => '衣物分类更新成功',
            'data' => [
                'updated_count' => $updateStmt->rowCount(),
                'target_category' => $targetCategory
            ]
        ]);
        
    } catch (Exception $e) {
        // 回滚事务
        $conn->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    error_log("Error in update_clothes_category.php: " . $e->getMessage());
    echo json_encode([
        'error' => true,
        'msg' => '服务器错误: ' . $e->getMessage()
    ]);
} 