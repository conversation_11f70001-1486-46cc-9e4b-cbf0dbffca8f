// 获取应用实例
const app = getApp();
const tagParser = require('../../../utils/tagParser.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    id: '', // 衣物ID
    name: '', // 衣物名称
    category: '', // 衣物分类
    imageUrl: '', // 图片URL
    tags: [], // 衣物标签
    selectedTags: {}, // 选中的标签集合
    descriptionFields: {
      color: '',
      wearCount: '',
      brand: '',
      price: '',
      notes: ''
    },
    uploadingImage: false, // 是否正在上传图片
    savingData: false, // 是否正在保存数据
    segmentEnabled: false, // 是否启用抠图功能
    originalImageUrl: '', // 原图URL
    segmentedImageUrl: '', // 抠图后的URL
    tempImagePath: '', // 临时图片路径，用于本地显示
    rotationAngle: 0, // 图片旋转角度
    scaleValue: 1.0, // 图片缩放比例
    
    // 新增：模块展开/收起状态
    isSegmentExpanded: false, // 抠图模块展开状态
    isRotationExpanded: false, // 旋转模块展开状态
    isScaleExpanded: false, // 缩放模块展开状态
    
    // 衣橱相关属性
    wardrobeId: null, // 衣物所属衣橱ID
    wardrobeList: [], // 衣橱列表
    defaultWardrobeId: null, // 默认衣橱ID
    selectedWardrobeName: '默认衣橱', // 选中的衣橱名称
    
    // 分类选项
    categories: [],
    
    // 预设标签
    predefinedTags: [
      { id: 'spring', name: '春季' },
      { id: 'summer', name: '夏季' },
      { id: 'autumn', name: '秋季' },
      { id: 'winter', name: '冬季' },
      { id: 'casual', name: '休闲' },
      { id: 'work', name: '通勤' },
      { id: 'party', name: '派对' },
      { id: 'sport', name: '运动' }
    ],
    
    // 自定义标签
    showCustomTagInput: false,
    customTagText: '',
    customTags: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('编辑页面加载，接收到的参数:', options);
    
    if (options && options.id) {
      console.log('找到衣物ID参数:', options.id);
      this.setData({
        id: options.id
      });
      
      // 确保token存在
      const app = getApp();
      if (!app.globalData.token) {
        console.error('未找到token，尝试从本地存储加载');
        const token = wx.getStorageSync('token');
        if (token) {
          app.globalData.token = token;
          console.log('从本地存储加载token成功');
        } else {
          console.error('本地存储中也没有token，可能需要登录');
          wx.showToast({
            title: '请先登录',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }, 1500);
          return;
        }
      }
      
      console.log('开始加载衣物详情...');
      this.loadClothingDetail(options.id);
      
      // 获取分类列表
      this.getClothingCategories();
    } else {
      console.error('未找到有效的衣物ID参数');
      wx.showToast({
        title: '未找到衣物信息',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },
  
  /**
   * 加载衣物详情
   */
  loadClothingDetail: function(id) {
    const app = getApp();
    
    wx.showLoading({
      title: '加载中...',
    });
    
    console.log('正在加载衣物详情，ID:', id);

    // 构建请求URL，添加圈子数据支持
    let url = `${app.globalData.apiBaseUrl}/get_clothes.php?id=${id}`;
    // 添加圈子数据参数，确保能获取到共享的衣物
    url += '&include_circle_data=true&data_source=all';

    console.log('API URL:', url);

    wx.request({
      url: url,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${app.globalData.token}`
      },
      success: (res) => {
        wx.hideLoading();
        console.log('获取衣物详情响应:', res);
        
        if (res.statusCode === 200 && !res.data.error) {
          // 修复数据获取方式，检查data是否是数组还是单个对象
          let clothingData = null;
          
          if (Array.isArray(res.data.data)) {
            // 如果是数组，取第一个匹配的衣物
            clothingData = res.data.data.find(item => item.id == id) || res.data.data[0];
            console.log('从数组中找到衣物数据:', clothingData);
          } else {
            // 如果是单个对象
            clothingData = res.data.data;
            console.log('获取到单个衣物数据:', clothingData);
          }
          
          if (clothingData) {
            console.log('将使用以下数据填充表单:', clothingData);
            
            // 预处理标签
            let tags = [];
            let selectedTags = {};
            
            if (clothingData.tags) {
              try {
                // 使用标签解析工具解析标签
                tags = tagParser.parseTags(clothingData.tags);
                console.log('处理后的标签列表:', tags);
              } catch (e) {
                console.error('解析标签失败:', e);
                // 尝试简单的逗号分隔处理
                tags = clothingData.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
              }
            }
            
            console.log('处理后的标签列表:', tags);
            
            // 设置标签选中状态
            tags.forEach(tag => {
              // 处理标签对象的情况，针对add.js中使用{name, value}结构的标签
              if (typeof tag === 'object' && tag !== null) {
                if (tag.name) {
                  // 检查是否是预设标签
                  const matchedTag = this.data.predefinedTags.find(
                    predefined => predefined.name === tag.name
                  );
                  
                  if (matchedTag) {
                    selectedTags[matchedTag.id] = true;
                    console.log(`标签对象 ${tag.name} 匹配到预设标签`);
                  } else {
                    // 使用tag.value作为ID（如果存在），否则生成新ID
                    const customTagId = tag.value || ('custom_' + Date.now() + '_' + Math.random().toString(36).substring(2, 8));
                    const customTag = {
                      id: customTagId,
                      name: tag.name
                    };
                    
                    // 添加到自定义标签列表并选中
                    this.setData({
                      customTags: [...this.data.customTags, customTag]
                    });
                    
                    selectedTags[customTagId] = true;
                    console.log(`标签对象 ${tag.name} 作为自定义标签添加`);
                  }
                }
                return; // 处理完对象后返回
              }
              
              // 处理字符串标签的情况
              // 查找是否匹配预设标签
              const matchedTag = this.data.predefinedTags.find(
                predefined => predefined.id === tag || predefined.name === tag
              );
              
              if (matchedTag) {
                selectedTags[matchedTag.id] = true;
                console.log(`标签 ${tag} 匹配到预设标签 ${matchedTag.name}`);
              } else {
                // 如果没有匹配到预设标签，创建一个自定义标签
                const customTagId = 'custom_' + Date.now() + '_' + Math.random().toString(36).substring(2, 8);
                const customTag = {
                  id: customTagId,
                  name: tag
                };
                
                // 添加到自定义标签列表并选中
                this.setData({
                  customTags: [...this.data.customTags, customTag]
                });
                
                selectedTags[customTagId] = true;
                console.log(`标签 ${tag} 作为自定义标签添加`);
              }
            });
            
            // 预处理描述字段
            let descriptionFields = { color: '', wearCount: '', brand: '', price: '', notes: '' };

            if (clothingData.description) {
              try {
                const parsedDesc = JSON.parse(clothingData.description);
                descriptionFields = {
                  color: parsedDesc.color || '',
                  wearCount: parsedDesc.wearCount || '',
                  brand: parsedDesc.brand || '',
                  price: parsedDesc.price || '',
                  notes: parsedDesc.notes || ''
                };
                console.log('解析描述字段成功:', descriptionFields);
              } catch (e) {
                console.error('解析描述信息失败:', e);
              }
            }
            
            // 设置数据到页面
            this.setData({
              name: clothingData.name || '',
              category: clothingData.category || '',
              imageUrl: clothingData.image_url || '',
              originalImageUrl: clothingData.image_url || '',
              tempImagePath: clothingData.image_url || '',
              tags: tags,
              selectedTags: selectedTags,
              descriptionFields: descriptionFields,
              wardrobeId: clothingData.wardrobe_id || null // 设置衣物所属衣橱ID
            });
            
            console.log('页面数据已更新, 名称:', clothingData.name, '分类:', clothingData.category);
            console.log('已选标签状态:', selectedTags);
            console.log('自定义标签:', this.data.customTags);
            
            // 立即更新tags数组，确保显示正确
            this.updateTagsList();
            
            // 加载衣橱列表
            this.getWardrobeList();
          } else {
            console.error('衣物数据为空');
            wx.showToast({
              title: '衣物不存在',
              icon: 'error'
            });
            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          }
        } else {
          console.error('获取衣物详情失败:', res.data);
          wx.showToast({
            title: res.data.msg || '加载失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('请求详情接口失败:', err);
        
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },
  
  /**
   * 返回上一页
   */
  navigateBack: function () {
    wx.navigateBack({
      fail: function() {
        // 如果返回失败，可能是直接打开的，则跳转到服装管理页
        wx.reLaunch({
          url: '/pages/clothing/manage/manage'
        });
      }
    });
  },
  
  /**
   * 输入衣物名称
   */
  inputName: function (e) {
    this.setData({
      name: e.detail.value
    });
  },
  
  /**
   * 选择衣物分类
   */
  selectCategory: function (e) {
    this.setData({
      category: e.currentTarget.dataset.category
    });
  },
  
  /**
   * 切换标签选中状态
   */
  toggleTag: function (e) {
    // 获取标签ID
    const tagId = e.currentTarget.dataset.tagId;
    let selectedTags = { ...this.data.selectedTags };
    
    if (selectedTags[tagId]) {
      // 如果已选中，则取消选中
      delete selectedTags[tagId];
      console.log(`取消选中标签: ${tagId}`);
    } else {
      // 如果未选中，则选中
      selectedTags[tagId] = true;
      console.log(`选中标签: ${tagId}`);
    }
    
    this.setData({ selectedTags });
  },
  
  /**
   * 根据selectedTags更新tags数组
   */
  updateTagsList: function() {
    const { selectedTags, predefinedTags, customTags } = this.data;
    const tagIds = Object.keys(selectedTags);
    
    // 创建对象格式的tags数组而不是字符串数组
    const tags = [];
    
    // 处理选中的预设标签
    predefinedTags.forEach(tag => {
      if (selectedTags[tag.id]) {
        tags.push({
          name: tag.name,
          id: tag.id
        });
      }
    });
    
    // 处理选中的自定义标签
    customTags.forEach(tag => {
      if (selectedTags[tag.id]) {
        tags.push({
          name: tag.name,
          id: tag.id
        });
      }
    });
    
    console.log('更新后的tags数组:', tags);
    
    this.setData({ tags });
  },
  
  /**
   * 显示自定义标签输入框
   */
  showCustomTagInput: function() {
    this.setData({
      showCustomTagInput: true,
      customTagText: ''
    });
  },
  
  /**
   * 取消添加自定义标签
   */
  cancelCustomTag: function() {
    this.setData({
      showCustomTagInput: false,
      customTagText: ''
    });
  },
  
  /**
   * 监听自定义标签输入
   */
  onCustomTagInput: function(e) {
    this.setData({
      customTagText: e.detail.value
    });
  },
  
  /**
   * 添加自定义标签
   */
  addCustomTag: function() {
    const { customTagText, customTags, selectedTags } = this.data;
    
    if (!customTagText.trim()) {
      wx.showToast({
        title: '标签不能为空',
        icon: 'none'
      });
      return;
    }
    
    // 生成一个唯一的标签ID
    const tagId = 'custom_' + Date.now();
    
    // 添加到自定义标签列表
    const newCustomTags = [...customTags, {
      id: tagId,
      name: customTagText
    }];
    
    // 自动选中该标签
    const newSelectedTags = { ...selectedTags };
    newSelectedTags[tagId] = true;
    
    this.setData({
      customTags: newCustomTags,
      selectedTags: newSelectedTags,
      showCustomTagInput: false,
      customTagText: ''
    });
    
    // 更新标签列表
    this.updateTagsList();
    
    // 显示提示
    wx.showToast({
      title: '添加成功',
      icon: 'success'
    });
  },
  
  /**
   * 移除标签
   */
  removeTag: function (e) {
    const index = e.currentTarget.dataset.index;
    const tagToRemove = this.data.tags[index];
    
    // 找到匹配的tagId
    let tagIdToRemove = null;
    
    // 在预设标签中查找
    for (const tagId in this.data.selectedTags) {
      const predefinedTag = this.data.predefinedTags.find(tag => tag.id === tagId);
      if (predefinedTag && predefinedTag.name === tagToRemove) {
        tagIdToRemove = tagId;
        break;
      }
      
      // 在自定义标签中查找
      const customTag = this.data.customTags.find(tag => tag.id === tagId);
      if (customTag && customTag.name === tagToRemove) {
        tagIdToRemove = tagId;
        break;
      }
    }
    
    if (tagIdToRemove) {
      let selectedTags = { ...this.data.selectedTags };
      delete selectedTags[tagIdToRemove];
      
      this.setData({ selectedTags });
      this.updateTagsList();
    } else {
      // 直接从tags数组中移除
      let tags = [...this.data.tags];
      tags.splice(index, 1);
      this.setData({ tags });
    }
  },
  
  /**
   * 输入描述字段
   */
  inputDescription: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`descriptionFields.${field}`]: value
    });
  },
  
  /**
   * 选择图片
   */
  chooseImage: function () {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      camera: 'back',
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        
        this.setData({
          tempImagePath: tempFilePath,
          segmentedImageUrl: '', // 重置抠图URL
          rotationAngle: 0, // 重置旋转角度
          scaleValue: 1.0 // 重置缩放比例
        });
        
        // 上传图片
        this.uploadImage(tempFilePath, (imageUrl) => {
          // 设置原图URL
          this.setData({
            imageUrl: imageUrl,
            originalImageUrl: imageUrl
          });
          
          wx.showToast({
            title: '上传成功',
            icon: 'success'
          });
        });
      }
    });
  },
  
  /**
   * 上传图片
   */
  uploadImage: function (filePath, callback) {
    const app = getApp();
    
    wx.showLoading({
      title: '上传中...',
    });
    
    // 获取符合JWT格式的token
    const compatibleToken = this.generateCompatibleToken();
    
    wx.uploadFile({
      url: `${app.globalData.apiBaseUrl}/upload_image.php`,
      filePath: filePath,
      name: 'image',
      header: {
        'Authorization': compatibleToken
      },
      formData: {
        // 添加参数，初始上传时不执行抠图
        segment_image: 'false'
      },
      success: (res) => {
        wx.hideLoading();
        
        console.log('上传响应:', res);
        
        try {
          const result = JSON.parse(res.data);
          
          if (!result.error) {
            // 设置原图URL
            const imageUrl = result.data.image_url;
            console.log('上传成功，图片URL:', imageUrl);
            
            if (typeof callback === 'function') {
              callback(imageUrl);
            }
          } else {
            // 显示详细错误信息
            const errorMsg = result.msg || '图片上传失败';
            console.error('上传失败原因:', errorMsg);
            wx.showToast({
              title: errorMsg,
              icon: 'none',
              duration: 3000
            });
            
            if (typeof callback === 'function') {
              callback(this.data.originalImageUrl); // 失败时返回原图
            }
          }
        } catch (e) {
          wx.hideLoading();
          console.error('解析响应失败:', e, '原始响应:', res.data);
          wx.showToast({
            title: '解析响应失败',
            icon: 'none',
            duration: 3000
          });
          
          if (typeof callback === 'function') {
            callback(this.data.originalImageUrl); // 失败时返回原图
          }
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('上传请求失败:', err);
        
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none',
          duration: 3000
        });
        
        if (typeof callback === 'function') {
          callback(this.data.originalImageUrl); // 失败时返回原图
        }
      }
    });
  },
  
  // 生成符合后端要求的JWT格式token
  generateCompatibleToken: function() {
    const app = getApp();
    const originalToken = app.globalData.token;
    
    // 检查是否已经是JWT格式
    if(originalToken && originalToken.split('.').length === 3) {
      return originalToken;
    }
    
    try {
      // 生成符合JWT格式的token
      const headerObj = {alg: 'HS256', typ: 'JWT'};
      const payloadObj = {
        sub: 1, // 默认用户ID
        openid: 'mock_openid',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7天后过期
      };
      
      // 简单的Base64编码
      const btoa = (str) => {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
        let out = '';
        for (let i = 0; i < str.length; i += 3) {
          const c1 = str.charCodeAt(i);
          const c2 = i + 1 < str.length ? str.charCodeAt(i + 1) : 0;
          const c3 = i + 2 < str.length ? str.charCodeAt(i + 2) : 0;
          
          const e1 = c1 >> 2;
          const e2 = ((c1 & 3) << 4) | (c2 >> 4);
          const e3 = ((c2 & 15) << 2) | (c3 >> 6);
          const e4 = c3 & 63;
          
          out += chars.charAt(e1) + chars.charAt(e2) +
                (i + 1 < str.length ? chars.charAt(e3) : '=') +
                (i + 2 < str.length ? chars.charAt(e4) : '=');
        }
        return out;
      };
      
      const header = btoa(JSON.stringify(headerObj));
      const payload = btoa(JSON.stringify(payloadObj));
      const signature = 'mocksignature'; // 简化的签名
      
      return `${header}.${payload}.${signature}`;
    } catch (e) {
      console.error('生成JWT token失败:', e);
      // 如果生成失败，返回原始token
      return originalToken || 'mock_token_fallback';
    }
  },
  
  // 获取衣橱列表
  getWardrobeList: function() {
    wx.showLoading({
      title: '加载衣橱...',
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_wardrobes.php`,
      method: 'GET',
      header: {
        'content-type': 'application/json',
        'Authorization': `Bearer ${app.globalData.token}`
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.statusCode === 200 && res.data.success) {
          console.log('获取衣橱列表成功:', res.data);
          const wardrobeList = res.data.data || [];
          
          // 查找默认衣橱
          let defaultWardrobe = wardrobeList.find(w => w.is_default == 1);
          let defaultWardrobeId = defaultWardrobe ? defaultWardrobe.id : null;
          
          this.setData({
            wardrobeList: wardrobeList,
            defaultWardrobeId: defaultWardrobeId
          });
          
          // 如果没有在衣物数据中设置衣橱ID，则使用默认衣橱ID
          if (!this.data.wardrobeId && defaultWardrobeId) {
            this.setData({
              wardrobeId: defaultWardrobeId
            });
          }
          
          // 设置选中的衣橱名称
          this.updateSelectedWardrobeName();
        } else {
          console.error('获取衣橱列表失败:', res);
          wx.showToast({
            title: '获取衣橱列表失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 更新选中的衣橱名称
  updateSelectedWardrobeName: function() {
    const { wardrobeId, wardrobeList } = this.data;
    
    if (wardrobeId && wardrobeList.length > 0) {
      const selectedWardrobe = wardrobeList.find(w => w.id == wardrobeId);
      if (selectedWardrobe) {
        this.setData({
          selectedWardrobeName: selectedWardrobe.name
        });
      }
    }
  },
  
  // 选择衣橱
  bindWardrobeChange: function(e) {
    const index = e.detail.value;
    const wardrobe = this.data.wardrobeList[index];
    
    if (wardrobe) {
      this.setData({
        wardrobeId: wardrobe.id,
        selectedWardrobeName: wardrobe.name
      });
      console.log('选择衣橱:', wardrobe.name, '(ID:', wardrobe.id, ')');
    }
  },
  
  // 切换抠图功能开关
  toggleSegment: function() {
    const currentState = this.data.segmentEnabled;
    const newState = !currentState;
    
    console.log(`[抠图] 开关状态从 ${currentState} 切换为 ${newState}`);
    
    // 更新开关状态
    this.setData({
      segmentEnabled: newState
    });
    
    // 如果打开抠图
    if (newState) {
      // 检查是否有图片
      if (!this.data.tempImagePath) {
        console.log('[抠图] 未选择图片，无法执行抠图');
        wx.showToast({
          title: '请先选择图片',
          icon: 'none'
        });
        
        // 重置开关状态
        this.setData({
          segmentEnabled: false
        });
        return;
      }
      
      // 检查是否有原图URL
      console.log(`[抠图] 检查条件: 开关=${newState}, 原图URL=${!!this.data.originalImageUrl}, 抠图URL=${!!this.data.segmentedImageUrl}`);
      
      // 如果已有原图但没有抠图图片，执行抠图
      if (this.data.originalImageUrl && !this.data.segmentedImageUrl) {
        console.log('[抠图] 条件满足，准备执行抠图处理');
        wx.showLoading({
          title: '正在抠图...',
          mask: true
        });
        
        // 调用抠图处理
        this.processSegmentation();
      } else if (this.data.segmentedImageUrl) {
        console.log('[抠图] 已有抠图结果，直接显示');
        this.updateDisplayImage();
      } else {
        console.log('[抠图] 条件不满足，不执行抠图处理');
        if (!this.data.originalImageUrl) {
          console.log('[抠图] 原因: 缺少原图URL');
        }
      }
    }
    
    // 更新显示的图片
    this.updateDisplayImage();
  },
  
  /**
   * 根据抠图开关状态，更新显示的图片
   */
  updateDisplayImage: function() {
    if (this.data.segmentEnabled && this.data.segmentedImageUrl) {
      console.log('[抠图] 抠图已启用且有抠图结果，显示抠图图片:', this.data.segmentedImageUrl);
      this.setData({
        tempImagePath: this.data.segmentedImageUrl
      });
    } else {
      console.log('[抠图] 抠图未启用或无抠图结果，显示原图:', this.data.originalImageUrl);
      this.setData({
        tempImagePath: this.data.originalImageUrl
      });
    }
    
    // 如果切换了图片，旋转角度或缩放重置
    // (仅当抠图后处理，避免用户已设置旋转角度后被重置)
    if (this.data.segmentEnabled && this.data.segmentedImageUrl) {
      console.log('[抠图] 抠图图片已应用，重置旋转和缩放');
      this.setData({
        rotationAngle: 0,
        scaleValue: 1.0
      });
    }
  },
  
  // 处理抠图请求
  processSegmentation: function() {
    // 获取符合JWT格式的token
    const compatibleToken = this.generateCompatibleToken();
    
    console.log('[抠图] 开始发送抠图请求');
    console.log('[抠图] 原图URL:', this.data.originalImageUrl);
    
    if (!this.data.originalImageUrl) {
      console.error('[抠图] 无原图URL，无法执行抠图');
      wx.hideLoading();
      wx.showToast({
        title: '抠图失败：无原图',
        icon: 'none'
      });
      return;
    }
    
    // 发送抠图请求
    wx.request({
      url: `${getApp().globalData.apiBaseUrl}/upload_image.php`,
      method: 'POST',
      header: {
        'Authorization': compatibleToken,
        'Content-Type': 'application/json'
      },
      data: {
        image_url: this.data.originalImageUrl,
        segment_image: true
      },
      success: (res) => {
        wx.hideLoading();
        console.log('[抠图] 请求成功，响应数据:', res);
        
        if (res.statusCode === 200 && !res.data.error) {
          console.log('[抠图] 抠图成功，完整响应数据:', JSON.stringify(res.data));
          
          // 检查响应中是否包含image_url
          if (res.data.data && res.data.data.image_url) {
            console.log('[抠图] 抠图成功，图片URL:', res.data.data.image_url);
            
            // 设置抠图后的图片URL
            this.setData({
              segmentedImageUrl: res.data.data.image_url
            });
            
            // 更新显示的图片
            this.updateDisplayImage();
            
            // 显示提示
            wx.showToast({
              title: '抠图成功',
              icon: 'success'
            });
          } else {
            console.error('[抠图] 响应中缺少image_url字段');
            console.error('[抠图] 完整响应数据:', JSON.stringify(res.data));
            wx.showToast({
              title: '抠图处理异常',
              icon: 'none'
            });
          }
        } else {
          console.error('[抠图] 抠图失败:', res.data.msg || '未知错误');
          console.error('[抠图] 完整响应:', JSON.stringify(res.data));
          
          wx.showToast({
            title: res.data.msg || '抠图失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('[抠图] 请求失败:', err);
        
        // 处理特定的网络错误
        let errorMsg = '网络错误，请稍后重试';
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMsg = '请求超时，请检查网络';
          } else if (err.errMsg.includes('fail')) {
            errorMsg = '网络连接失败';
          }
          console.error('[抠图] 错误详情:', err.errMsg);
        }
        
        wx.showToast({
          title: errorMsg,
          icon: 'none'
        });
      },
      complete: () => {
        console.log('[抠图] 请求完成');
      }
    });
  },
  
  /**
   * 提交表单
   */
  submitForm: function() {
    // 根据当前selectedTags状态获取最新的标签列表
    this.updateTagsList();
    
    const { id, name, category, tags, originalImageUrl, segmentedImageUrl, segmentEnabled, descriptionFields, wardrobeId, selectedTags, predefinedTags, customTags, rotationAngle, scaleValue } = this.data;
    
    // 打印选中的标签状态
    console.log('[保存] 选中的标签状态:', selectedTags);
    console.log('[保存] 当前标签列表:', tags);
    
    // 基本验证
    if (!name.trim()) {
      wx.showToast({
        title: '请输入衣物名称',
        icon: 'none'
      });
      return;
    }
    
    if (!category) {
      wx.showToast({
        title: '请选择衣物分类',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.tempImagePath) {
      wx.showToast({
        title: '请上传衣物图片',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      savingData: true
    });
    
    wx.showLoading({
      title: '保存中...',
    });
    
    // 检查是否需要进行图片处理（有旋转角度或缩放比例不为1）
    const needRotation = rotationAngle !== 0;
    const needScaling = scaleValue !== 1.0;
    const needImageProcessing = (needRotation || needScaling) && 
                              (this.data.segmentedImageUrl || this.data.originalImageUrl);
    
    if (needImageProcessing) {
      // 记录处理原因
      let processReason = [];
      if (needRotation) processReason.push(`旋转${rotationAngle}°`);
      if (needScaling) processReason.push(`缩放${scaleValue}x`);
      
      console.log(`[保存] 检测到图片需要处理: ${processReason.join('和')}`);
      
      // 确定要处理的图片URL
      const imageToProcess = segmentEnabled && segmentedImageUrl 
                           ? segmentedImageUrl 
                           : originalImageUrl;
      
      // 先下载远程图片，然后应用处理
      console.log(`[保存] 下载远程图片并应用处理, 图片URL: ${imageToProcess}`);
      
      wx.downloadFile({
        url: imageToProcess,
        success: (res) => {
          if (res.statusCode === 200) {
            console.log(`[保存] 图片下载成功，临时路径: ${res.tempFilePath}`);
            
            // 应用旋转和缩放处理
            this.processImage(
              res.tempFilePath, 
              rotationAngle, 
              scaleValue,
              (processedPath) => {
                console.log(`[保存] 图片处理完成，新路径: ${processedPath}`);
                
                // 上传处理后的图片
                this.uploadImage(processedPath, (imageUrl) => {
                  console.log(`[保存] 处理后图片上传成功，URL: ${imageUrl}`);
                  // 提交表单数据
                  this.submitFormData(id, name, category, tags, imageUrl, segmentEnabled, descriptionFields, wardrobeId);
                });
              }
            );
          } else {
            console.error(`[保存] 图片下载失败: ${res.statusCode}`);
            
            // 下载失败时使用原来的逻辑，继续保存
            this.continueWithOriginalData();
          }
        },
        fail: (err) => {
          console.error(`[保存] 图片下载错误:`, err);
          
          // 下载失败时使用原来的逻辑，继续保存
          this.continueWithOriginalData();
        }
      });
    } else {
      // 如果不需要处理图片，使用原来的保存逻辑
      this.continueWithOriginalData();
    }
  },
  
  // 继续使用原始数据保存
  continueWithOriginalData: function() {
    const { id, name, category, tags, originalImageUrl, segmentedImageUrl, segmentEnabled, descriptionFields, wardrobeId } = this.data;
    
    // 确定要使用的图片URL - 严格检查抠图功能是否开启
    let finalImageUrl;
    if (segmentEnabled && segmentedImageUrl) {
      console.log('[保存] 抠图已开启，使用抠图后的图片:', segmentedImageUrl);
      finalImageUrl = segmentedImageUrl;
    } else {
      console.log('[保存] 抠图未开启或无抠图结果，使用原图:', originalImageUrl);
      finalImageUrl = originalImageUrl;
    }
    
    // 提交表单数据
    this.submitFormData(id, name, category, tags, finalImageUrl, segmentEnabled, descriptionFields, wardrobeId);
  },
  
  // 提交表单数据到服务器
  submitFormData: function(id, name, category, tags, imageUrl, segmentEnabled, descriptionFields, wardrobeId) {
    const app = getApp();
    
    console.log('[保存] 抠图状态:', segmentEnabled ? '开启' : '关闭');
    
    // 确定衣橱ID，如果未选择，则使用默认衣橱ID
    let finalWardrobeId = wardrobeId;
    if (!finalWardrobeId && this.data.defaultWardrobeId) {
      finalWardrobeId = this.data.defaultWardrobeId;
      console.log('[保存] 未选择衣橱，使用默认衣橱ID:', finalWardrobeId);
    }
    
    // 描述字段转为JSON
    const description = JSON.stringify(descriptionFields);
    
    // 准备请求数据
    const requestData = {
      id: id, // 添加ID字段，让后端知道这是更新操作
      name: name,
      category: category,
      tags: JSON.stringify(tags.map(tag => tag.name)), // 提取tag.name
      image_url: imageUrl,
      segment_enabled: segmentEnabled, // 传递抠图开关状态到后端
      description: description,
      wardrobe_id: finalWardrobeId // 添加衣橱ID
    };
    
    console.log('提交更新数据:', requestData);
    
    // 直接调用API进行更新
    wx.request({
      url: `${app.globalData.apiBaseUrl}/add_clothing.php`,
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Authorization': `Bearer ${app.globalData.token}`
      },
      data: requestData,
      success: (res) => {
        wx.hideLoading();
        this.setData({
          savingData: false
        });
        
        console.log('更新衣物响应:', res);
        
        if (res.statusCode === 200 && !res.data.error) {
          wx.showToast({
            title: '更新成功',
            icon: 'success'
          });
          
          // 通知上一个页面（管理页面）更新已完成
          const eventChannel = this.getOpenerEventChannel();
          if (eventChannel && eventChannel.emit) {
            try {
              eventChannel.emit('editComplete', { success: true });
              console.log('已通知管理页面刷新列表');
            } catch (e) {
              console.error('通知上一页面失败:', e);
            }
          }
          
          // 等待提示显示后返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          console.error('更新失败:', res);
          wx.showToast({
            title: res.data.msg || '更新失败，请重试',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        this.setData({
          savingData: false
        });
        
        console.error('更新请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 设置旋转角度
  onRotationChange: function(e) {
    const angle = parseInt(e.detail.value);
    console.log('[旋转] 设置旋转角度为', angle, '度');
    this.setData({
      rotationAngle: angle
    });
  },
  
  // 向左旋转90度
  rotateLeft: function() {
    const currentAngle = this.data.rotationAngle;
    const newAngle = (currentAngle - 90 + 360) % 360; // 确保角度在0-359之间
    console.log('[旋转] 向左旋转，从', currentAngle, '度到', newAngle, '度');
    this.setData({
      rotationAngle: newAngle
    });
  },
  
  // 向右旋转90度
  rotateRight: function() {
    const currentAngle = this.data.rotationAngle;
    const newAngle = (currentAngle + 90) % 360;
    console.log('[旋转] 向右旋转，从', currentAngle, '度到', newAngle, '度');
    this.setData({
      rotationAngle: newAngle
    });
  },
  
  // 重置旋转角度
  resetRotation: function() {
    console.log('[旋转] 重置旋转角度为0度');
    this.setData({
      rotationAngle: 0
    });
  },
  
  // 设置缩放比例
  onScaleChange: function(e) {
    const scale = parseFloat(e.detail.value);
    console.log('[缩放] 设置缩放比例为', scale, 'x');
    this.setData({
      scaleValue: scale
    });
  },
  
  // 缩小图片(减小0.1)
  scaleDown: function() {
    let currentScale = this.data.scaleValue;
    const newScale = Math.max(0.5, currentScale - 0.1).toFixed(1);
    console.log('[缩放] 缩小图片，从', currentScale, 'x到', newScale, 'x');
    this.setData({
      scaleValue: parseFloat(newScale)
    });
  },
  
  // 放大图片(增加0.1)
  scaleUp: function() {
    let currentScale = this.data.scaleValue;
    const newScale = Math.min(2.0, currentScale + 0.1).toFixed(1);
    console.log('[缩放] 放大图片，从', currentScale, 'x到', newScale, 'x');
    this.setData({
      scaleValue: parseFloat(newScale)
    });
  },
  
  // 重置缩放比例
  resetScale: function() {
    console.log('[缩放] 重置缩放比例为1.0x');
    this.setData({
      scaleValue: 1.0
    });
  },
  
  // 处理图片函数：应用旋转和缩放
  processImage: function(imagePath, angle, scale, callback) {
    console.log(`[图片处理] 开始处理图片，旋转: ${angle}°, 缩放: ${scale}x`);
    
    // 获取图片信息
    wx.getImageInfo({
      src: imagePath,
      success: (res) => {
        console.log('[图片处理] 获取图片信息成功:', res);
        const { width, height, path } = res;
        
        // 使用Canvas 2D API
        const query = wx.createSelectorQuery();
        query.select('#rotateCanvas')
          .fields({ node: true, size: true })
          .exec((canvasRes) => {
            if (!canvasRes || !canvasRes[0] || !canvasRes[0].node) {
              console.error('[图片处理] 获取canvas节点失败');
              if (typeof callback === 'function') {
                callback(imagePath); // 失败时返回原图路径
              }
              return;
            }
            
            const canvas = canvasRes[0].node;
            const ctx = canvas.getContext('2d');
            
            // 计算画布尺寸，考虑旋转和缩放后的尺寸
            const canvasSize = Math.sqrt(width * width + height * height);
            
            // 设置画布尺寸，确保足够大
            canvas.width = canvasSize;
            canvas.height = canvasSize;
            
            // 创建一个新的Image对象
            const img = canvas.createImage();
            
            // 图片加载事件
            img.onload = () => {
              // 清空画布
              ctx.clearRect(0, 0, canvasSize, canvasSize);
              
              // 保存当前状态
              ctx.save();
              
              // 将原点移到画布中心
              ctx.translate(canvasSize / 2, canvasSize / 2);
              
              // 执行旋转，注意角度需要转换为弧度
              ctx.rotate((angle * Math.PI) / 180);
              
              // 应用缩放
              ctx.scale(scale, scale);
              
              // 将图片绘制到画布中心，考虑偏移
              ctx.drawImage(img, -width / 2, -height / 2, width, height);
              
              // 恢复状态
              ctx.restore();
              
              // 导出图片
              wx.canvasToTempFilePath({
                canvas: canvas,
                success: (res) => {
                  console.log('[图片处理] 导出图片成功:', res.tempFilePath);
                  if (typeof callback === 'function') {
                    callback(res.tempFilePath);
                  }
                },
                fail: (err) => {
                  console.error('[图片处理] 导出图片失败:', err);
                  if (typeof callback === 'function') {
                    callback(imagePath); // 失败时返回原图路径
                  }
                }
              });
            };
            
            // 图片加载错误事件
            img.onerror = (err) => {
              console.error('[图片处理] 图片加载失败:', err);
              if (typeof callback === 'function') {
                callback(imagePath); // 失败时返回原图路径
              }
            };
            
            // 设置图片源
            img.src = path;
          });
      },
      fail: (err) => {
        console.error('[图片处理] 获取图片信息失败:', err);
        if (typeof callback === 'function') {
          callback(imagePath); // 失败时返回原图路径
        }
      }
    });
  },
  
  // 获取分类列表
  getClothingCategories: function() {
    const app = getApp();
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothing_categories.php`,
      method: 'GET',
      header: {
        'content-type': 'application/json',
        'Authorization': `Bearer ${app.globalData.token}`
      },
      success: (res) => {
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          console.log('获取分类列表成功:', res.data);
          const categories = res.data.data || [];
          
          // 转换数据格式
          const formattedCategories = categories.map(cat => ({
            name: cat.name,
            value: cat.code,
            id: cat.id,
            is_system: cat.is_system
          }));
          
          this.setData({
            categories: formattedCategories
          });
        } else {
          console.error('获取分类列表失败:', res);
          // 如果获取失败，使用默认分类
          this.setDefaultCategories();
        }
      },
      fail: (err) => {
        console.error('获取分类列表请求失败:', err);
        // 如果请求失败，使用默认分类
        this.setDefaultCategories();
      }
    });
  },

  // 设置默认分类（兼容性处理）
  setDefaultCategories: function() {
    const defaultCategories = [
      { name: '上衣', value: 'tops', is_system: true },
      { name: '裤子', value: 'pants', is_system: true },
      { name: '裙子', value: 'skirts', is_system: true },
      { name: '外套', value: 'coats', is_system: true },
      { name: '鞋子', value: 'shoes', is_system: true },
      { name: '包包', value: 'bags', is_system: true },
      { name: '配饰', value: 'accessories', is_system: true }
    ];
    
    this.setData({
      categories: defaultCategories
    });
  },

  // 跳转到分类管理
  goToClothingCategories: function() {
    wx.navigateTo({
      url: '/pages/clothing-categories/index/index'
    });
  },
  
  // 切换抠图模块展开状态
  toggleSegmentExpand: function() {
    this.setData({
      isSegmentExpanded: !this.data.isSegmentExpanded,
      isRotationExpanded: false,
      isScaleExpanded: false
    });
  },
  
  // 切换旋转模块展开状态
  toggleRotationExpand: function() {
    this.setData({
      isSegmentExpanded: false,
      isRotationExpanded: !this.data.isRotationExpanded,
      isScaleExpanded: false
    });
  },
  
  // 切换缩放模块展开状态
  toggleScaleExpand: function() {
    this.setData({
      isSegmentExpanded: false,
      isRotationExpanded: false,
      isScaleExpanded: !this.data.isScaleExpanded
    });
  }
}); 