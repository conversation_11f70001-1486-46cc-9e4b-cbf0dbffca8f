<view class="container">
  
  <!-- 状态栏 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  
  <!-- 顶部标题栏 -->
  <view class="title-bar" style="top: {{statusBarHeight}}px;">
    <view class="back-icon" bindtap="navigateBack">
      <text class="icon-back"></text>
    </view>
    <view class="title">使用教程</view>
    <view class="menu-icons">
      <text class="icon-dots"></text>
      <text class="icon-refresh"></text>
    </view>
  </view>
  
  <!-- 导航目录 -->
  <view class="nav-menu" style="top: calc({{statusBarHeight}}px + 44px);">
    <scroll-view scroll-x="true" class="nav-scroll">
      <view class="nav-items">
        <view 
          wx:for="{{sections}}" 
          wx:key="index" 
          class="nav-item {{currentSection === index ? 'active' : ''}}" 
          bindtap="scrollToSection" 
          data-index="{{index}}"
        >
          {{item}}
        </view>
      </view>
    </scroll-view>
  </view>
  
  <!-- 教程内容 -->
  <view class="tutorial-container" style="margin-top: calc({{statusBarHeight}}px + 44px + 40px);">
    <!-- 如何添加衣物 -->
    <view class="section" id="section-0">
      <view class="section-header">
        <text class="section-title">如何添加衣物</text>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">1</view>
          <text class="step-title">打开"我的衣橱"页面</text>
        </view>
        <view class="step-content">
          在底部导航栏选择"衣橱"图标，进入您的个人次元衣帽间。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">2</view>
          <text class="step-title">添加新衣物</text>
        </view>
        <view class="step-content">
          在衣柜列表中点击"添加衣物"图标添加新衣物。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">3</view>
          <text class="step-title">拍照或从相册选择</text>
        </view>
        <view class="step-content">
          您可以拍摄衣物照片或从手机相册中选择已有的衣物照片。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">4</view>
          <text class="step-title">填写衣物信息</text>
        </view>
        <view class="step-content">
          为您的衣物添加分类、颜色、季节等标签，以便更好地管理。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">5</view>
          <text class="step-title">调整图片位置和大小</text>
        </view>
        <view class="step-content">
          上传图片后，您可以对图片进行旋转和缩放调整：
          <view class="sub-step">• 旋转：用两根手指在屏幕上做旋转手势，衣物图片会跟随旋转</view>
          <view class="sub-step">• 缩放：捏合或张开两指可以缩小或放大图片</view>
          <view class="sub-step">• 重置：点击"重置"按钮可恢复图片原始状态</view>
          <view class="sub-step">• 确认：调整完成后，点击"确认"按钮保存您的调整</view>
        </view>
      </view>
    </view>
    
    <!-- 管理衣物标签 -->
    <view class="section" id="section-1">
      <view class="section-header">
        <text class="section-title">管理衣物标签</text>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">1</view>
          <text class="step-title">进入衣物详情</text>
        </view>
        <view class="step-content">
          在衣橱页面，点击任意衣物进入详情页面。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">2</view>
          <text class="step-title">添加或编辑标签</text>
        </view>
        <view class="step-content">
          在衣物详情页面，点击"编辑"按钮，可以添加或修改衣物的标签信息。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">3</view>
          <text class="step-title">保存更改</text>
        </view>
        <view class="step-content">
          完成标签编辑后，点击"保存"按钮保存您的更改。
        </view>
      </view>
    </view>
    
    <!-- 衣橱分类管理 -->
    <view class="section" id="section-2">
      <view class="section-header">
        <text class="section-title">衣橱分类管理</text>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">1</view>
          <text class="step-title">使用分类标签</text>
        </view>
        <view class="step-content">
          在衣橱页面顶部，点击不同的分类标签（如上衣、裤子等）可以快速筛选衣物。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">2</view>
          <text class="step-title">进入管理模式</text>
        </view>
        <view class="step-content">
          点击右下角的齿轮图标按钮，进入衣橱管理模式。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">3</view>
          <text class="step-title">批量操作</text>
        </view>
        <view class="step-content">
          在管理模式下，您可以选择多件衣物进行编辑或删除操作。
        </view>
      </view>
    </view>
    
    <!-- 创建穿搭 -->
    <view class="section" id="section-3">
      <view class="section-header">
        <text class="section-title">创建个性穿搭</text>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">1</view>
          <text class="step-title">进入"穿搭"页面</text>
        </view>
        <view class="step-content">
          在底部导航栏选择"穿搭"图标，进入穿搭管理页面。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">2</view>
          <text class="step-title">创建新穿搭</text>
        </view>
        <view class="step-content">
          点击页面下方"创建穿搭"按钮，进入穿搭创建界面。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">3</view>
          <text class="step-title">选择衣物</text>
        </view>
        <view class="step-content">
          从下方衣物列表中选择您想要搭配的衣物，可以筛选不同衣橱中的衣物。选中的衣物会自动添加到画布中。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">4</view>
          <text class="step-title">调整布局</text>
        </view>
        <view class="step-content">
          拖动衣物调整位置，双指可缩放或旋转衣物，长按可调整衣物的层级顺序。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">5</view>
          <text class="step-title">保存穿搭</text>
        </view>
        <view class="step-content">
          为穿搭添加名称和描述，点击"保存"按钮完成创建。创建的穿搭会显示在穿搭列表中。
        </view>
      </view>
    </view>
    
    <!-- 分享穿搭 -->
    <view class="section" id="section-4">
      <view class="section-header">
        <text class="section-title">分享我的穿搭</text>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">1</view>
          <text class="step-title">进入穿搭详情</text>
        </view>
        <view class="step-content">
          在穿搭列表中点击任意穿搭，进入穿搭详情页面。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">2</view>
          <text class="step-title">分享给好友</text>
        </view>
        <view class="step-content">
          点击页面右上角的分享按钮，选择"分享给好友"，即可将您的穿搭分享给微信好友。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">3</view>
          <text class="step-title">分享到朋友圈</text>
        </view>
        <view class="step-content">
          点击页面右上角的分享按钮，选择"分享到朋友圈"，可将您的穿搭分享到微信朋友圈。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">4</view>
          <text class="step-title">查看分享的穿搭</text>
        </view>
        <view class="step-content">
          您的好友通过分享链接打开小程序后，可以查看您分享的穿搭。如果他们喜欢您的穿搭，可以点击"我也要穿搭"按钮创建自己的穿搭。
        </view>
      </view>
    </view>
    
    <!-- 穿搭分类管理 -->
    <view class="section" id="section-5">
      <view class="section-header">
        <text class="section-title">穿搭分类管理</text>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">1</view>
          <text class="step-title">进入穿搭类别页面</text>
        </view>
        <view class="step-content">
          在穿搭列表页面，点击顶部的分类选项可以筛选不同类别的穿搭。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">2</view>
          <text class="step-title">创建穿搭类别</text>
        </view>
        <view class="step-content">
          在穿搭页面中，点击"管理类别"按钮，然后选择"添加类别"，输入类别名称和描述后保存。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">3</view>
          <text class="step-title">编辑穿搭类别</text>
        </view>
        <view class="step-content">
          在类别管理页面，点击想要编辑的类别旁边的"编辑"按钮，修改名称或描述后保存。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">4</view>
          <text class="step-title">分配穿搭到类别</text>
        </view>
        <view class="step-content">
          创建或编辑穿搭时，可以选择将穿搭分配到特定类别。您也可以在穿搭详情页面点击"编辑"按钮来更改穿搭的类别。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">5</view>
          <text class="step-title">删除穿搭类别</text>
        </view>
        <view class="step-content">
          在类别管理页面，点击想要删除的类别旁边的"删除"按钮。注意：删除类别不会删除其中的穿搭，这些穿搭会被移动到"未分类"类别中。
        </view>
      </view>
    </view>
    
    <!-- 虚拟试衣功能 -->
    <view class="section" id="section-6">
      <view class="section-header">
        <text class="section-title">虚拟试衣功能</text>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">1</view>
          <text class="step-title">进入"试穿"页面</text>
        </view>
        <view class="step-content">
          在底部导航栏选择"试穿"图标，进入虚拟试衣功能页面。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">2</view>
          <text class="step-title">上传个人照片</text>
        </view>
        <view class="step-content">
          如果您还没有上传照片，需要先添加一张个人照片。点击"添加照片"按钮，选择或拍摄一张全身照。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">3</view>
          <text class="step-title">选择衣物进行试穿</text>
        </view>
        <view class="step-content">
          选择一张个人照片，然后选择一件或多件想要试穿的衣物。您可以按衣橱筛选衣物，找到想要试穿的服装。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">4</view>
          <text class="step-title">开始试衣</text>
        </view>
        <view class="step-content">
          点击"开始试衣"按钮，系统会生成您穿着所选衣物的效果图。请注意：系统对每位用户每天有试衣次数限制。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">5</view>
          <text class="step-title">查看试衣结果</text>
        </view>
        <view class="step-content">
          试衣完成后，您可以查看、保存或分享试衣结果。您的所有试衣历史记录都可在"试衣历史"页面中查看。
        </view>
      </view>
    </view>
    
    <!-- 商家入驻 -->
    <view class="section" id="section-7">
      <view class="section-header">
        <text class="section-title">商家入驻功能</text>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">1</view>
          <text class="step-title">什么是商家入驻</text>
        </view>
        <view class="step-content">
          商家入驻功能允许您将个人衣橱公开，与其他用户分享您的衣物。入驻后，其他用户可以浏览您的衣橱并试穿您的服装。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">2</view>
          <text class="step-title">如何成为商家</text>
        </view>
        <view class="step-content">
          进入"个人中心"页面，在用户信息区域点击"立即入驻"按钮，在弹出的确认窗口中了解商家入驻说明，点击"确认入驻"完成操作。入驻成功后，入驻按钮将变为"退出入驻"。
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">3</view>
          <text class="step-title">共享试穿点数</text>
        </view>
        <view class="step-content">
          入驻商家后，您可以开启"共享试穿点数"功能。开启后，其他用户试穿您的衣物时不会消耗自己的试穿点数；关闭则会消耗用户自己的点数。
          <view class="sub-step">• 在个人中心页面的商家状态区域找到"共享试穿点数"开关</view>
          <view class="sub-step">• 点击开关可以切换共享试穿点数的开启/关闭状态</view>
          <view class="sub-step">• 绿色表示已开启，灰色表示未开启</view>
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">4</view>
          <text class="step-title">查看商家列表</text>
        </view>
        <view class="step-content">
          用户可以在"试穿"页面查看所有已入驻的商家列表，通过搜索框可以搜索特定商家。
          <view class="sub-step">• 进入底部导航栏的"试穿"页面</view>
          <view class="sub-step">• 浏览商家列表，每个商家卡片显示名称、头像和共享点数状态</view>
          <view class="sub-step">• 点击商家卡片进入该商家的衣橱页面</view>
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">5</view>
          <text class="step-title">浏览商家衣橱</text>
        </view>
        <view class="step-content">
          点击商家卡片后，您可以浏览该商家的衣橱和衣物分类，查看衣物详情并进行试穿。
          <view class="sub-step">• 在商家衣橱页面可以查看该商家的衣橱和分类</view>
          <view class="sub-step">• 点击衣物可以查看衣物详情，包括大图、名称和标签</view>
          <view class="sub-step">• 在衣物详情页底部，点击"试穿"按钮可以选择自己的照片进行试穿</view>
          <view class="sub-step">• 试穿时会提示是否消耗点数，取决于商家是否开启共享试穿点数</view>
        </view>
      </view>
      
      <view class="step">
        <view class="step-header">
          <view class="step-number">6</view>
          <text class="step-title">退出商家入驻</text>
        </view>
        <view class="step-content">
          如果您想退出商家入驻状态，可以在个人中心页面点击"退出入驻"按钮，确认后您的衣橱将不再对其他用户公开，同时共享试穿点数功能也会自动关闭。
        </view>
      </view>
    </view>
  </view>
</view> 