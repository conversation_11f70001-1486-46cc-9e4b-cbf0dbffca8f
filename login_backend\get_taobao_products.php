<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');

// 调试日志 - 记录HTTP请求信息
$requestInfo = [
    'method' => $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN',
    'uri' => $_SERVER['REQUEST_URI'] ?? 'UNKNOWN',
    'query' => $_SERVER['QUERY_STRING'] ?? 'NONE',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'UNKNOWN',
    'referer' => $_SERVER['HTTP_REFERER'] ?? 'NONE',
    'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? 'UNKNOWN'
];
error_log("Taobao API Request Info: " . json_encode($requestInfo));

require_once 'config.php';
require_once 'auth.php';
require_once TAOBAO_SDK_PATH . '/TopSdk.php';

// 手动引入淘宝物料API相关类
require_once TAOBAO_SDK_PATH . '/top/request/TbkDgMaterialOptionalUpgradeRequest.php';
require_once TAOBAO_SDK_PATH . '/top/domain/TbkItemInfo.php';
require_once TAOBAO_SDK_PATH . '/top/domain/MapData.php';

// 手动引入淘宝商品详情API相关类
require_once TAOBAO_SDK_PATH . '/top/request/TbkItemInfoGetRequest.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 验证用户Token (可选，如果想要对接口做权限控制)
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION']) && !empty($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
    
    $auth = new Auth();
    $payload = $auth->verifyToken($token);
    
    if (!$payload) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => '无效的授权']);
        exit();
    }
}

// 初始化淘宝客SDK
$c = new TopClient();
$c->appkey = TAOBAO_APPKEY;
$c->secretKey = TAOBAO_APPSECRET;
$c->gatewayUrl = "https://eco.taobao.com/router/rest"; // 设置正式环境API网关地址

// 获取商品ID参数，如果存在则使用商品详情API
$itemId = isset($_GET['item_id']) && !empty($_GET['item_id']) ? trim($_GET['item_id']) : '';

// 确保所有字段是标准格式，而不是对象
function normalizeValue($value) {
    if (is_object($value)) {
        // 如果是对象，转换为字符串或获取第一个属性
        if (isset($value->{0})) {
            return $value->{0};
        }
        return strval($value);
    } elseif (is_array($value)) {
        // 如果是数组，递归处理每个元素
        return array_map('normalizeValue', $value);
    }
    return $value;
}

// 如果有itemId参数，使用商品详情API
if (!empty($itemId)) {
    error_log("Taobao API - Using Item Info API for item_id: " . $itemId);
    
    try {
        // 创建淘宝商品详情请求
        $req = new TbkItemInfoGetRequest();
        $req->setNumIids($itemId);
        $req->setPlatform(1); // 链接形式：1:PC，2:无线，默认：1
        
        // 设置广告位ID，必须参数
        $req->setAdzoneId(preg_replace('/^.+_(\d+)$/', '$1', TAOBAO_PID));
        
        // 调用API获取商品详情数据
        $resp = $c->execute($req);
        error_log("Taobao Item Info API Response: " . json_encode($resp));
        
        // 检查API响应错误
        if (isset($resp->error_response)) {
            error_log("Taobao Item Info API Error: " . json_encode($resp->error_response));
            echo json_encode([
                'error' => true,
                'msg' => $resp->error_response->sub_msg ?? $resp->error_response->msg ?? '获取商品详情失败',
                'code' => $resp->error_response->code ?? null
            ]);
            exit();
        }
        
        // 处理商品详情API返回结果
        $items = [];
        
        if (isset($resp->results) && isset($resp->results->n_tbk_item)) {
            $itemInfo = $resp->results->n_tbk_item;
            
            // 处理可能的单条和多条结果的情况
            if (!is_array($itemInfo)) {
                $itemInfo = [$itemInfo];
            }
            
            foreach ($itemInfo as $info) {
                // 尝试提取商品信息
                $item = [
                    'id' => normalizeValue($info->num_iid),
                    'title' => normalizeValue($info->title),
                    'image_url' => normalizeValue($info->pict_url),
                    'small_images' => isset($info->small_images->string) ? normalizeValue($info->small_images->string) : [],
                    'original_price' => floatval($info->reserve_price),
                    'zk_final_price' => floatval($info->zk_final_price),
                    'final_price' => floatval($info->zk_final_price), // 商品详情API可能没有最终价格，使用折扣价
                    'coupon_amount' => 0, // 商品详情API可能没有优惠券信息
                    'shop_title' => normalizeValue($info->nick ?? ''),
                    'volume' => normalizeValue($info->volume ?? 0),
                    'item_url' => isset($info->item_url) ? normalizeValue($info->item_url) : '',
                    'seller_id' => normalizeValue($info->seller_id ?? '')
                ];
                
                $items[] = $item;
            }
        }
        
        // 返回商品详情数据
        echo json_encode([
            'error' => false,
            'data' => $items,
            'pagination' => [
                'total' => count($items),
                'current_page' => 1,
                'page_size' => count($items),
                'total_pages' => 1
            ]
        ]);
        exit();
        
    } catch (Exception $e) {
        error_log("Taobao Item Info API Exception: " . $e->getMessage());
        echo json_encode([
            'error' => true,
            'msg' => '调用淘宝商品详情接口异常: ' . $e->getMessage()
        ]);
        exit();
    }
}

// 如果没有itemId参数，使用原有的物料搜索API逻辑
// 获取请求参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$page = max(1, $page); // 确保页码不小于1

$pageSize = isset($_GET['page_size']) ? intval($_GET['page_size']) : TAOBAO_PAGE_SIZE;
$pageSize = min(100, max(1, $pageSize)); // 限制页面大小在1-100之间

$keyword = isset($_GET['keyword']) && !empty($_GET['keyword']) ? trim($_GET['keyword']) : '女装';
$keyword = htmlspecialchars($keyword); // 防XSS

// 新增：支持物料ID参数，如果没有则使用默认值
$materialId = isset($_GET['material_id']) && !empty($_GET['material_id']) ? trim($_GET['material_id']) : TAOBAO_MATERIAL_ID;

// 记录日志，帮助调试
error_log("Taobao API Request - Page: $page, PageSize: $pageSize, Keyword: $keyword, MaterialID: $materialId");

// 创建物料搜索请求 - 使用升级版API
$req = new TbkDgMaterialOptionalUpgradeRequest();
$req->setAdzoneId(preg_replace('/^.+_(\d+)$/', '$1', TAOBAO_PID)); // 从PID中提取广告位ID
$req->setPageSize($pageSize);
$req->setPageNo($page);
$req->setBizSceneId(1); // 1-自购省，2-消费者比价

// 设置物料类型 - 使用参数传递的物料ID或默认值
$req->setMaterialId($materialId);

// 设置搜索关键词
if (!empty($keyword)) {
    $req->setQ($keyword);
}

// 基本筛选参数
$req->setHasCoupon("false"); // 不限制优惠券
$req->setSort("total_sales"); // 按销量排序，默认最受欢迎

// 添加更多API参数
$userAgent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
$referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';

// 添加微信小程序UA和Referer标识
$isWechat = strpos($userAgent, 'MicroMessenger') !== false || 
            strpos($referer, 'servicewechat.com') !== false;
            
if ($isWechat) {
    // 如果是来自微信的请求，设置特定参数
    $req->setDeviceValue("WXAPP"); // 设备值
    $req->setDeviceType("WXAPP"); // 设备类型
    $req->setDeviceEncrypt("MD5"); // 加密类型
}

// 记录API请求参数
error_log("Taobao API Request - AdZoneID: " . preg_replace('/^.+_(\d+)$/', '$1', TAOBAO_PID) . 
         ", MaterialID: " . $materialId . 
         ", UserAgent: $userAgent" . 
         ", IsWechat: " . ($isWechat ? 'Yes' : 'No') . 
         ", Referer: $referer");

try {
    // 调用API获取商品数据
    $resp = $c->execute($req);
    
    // 记录API响应信息
    error_log("Taobao API Response - Has Error: " . (isset($resp->error_response) ? 'Yes' : 'No'));

    // 处理返回结果
    if (isset($resp->error_response)) {
        error_log("Taobao API Error: " . json_encode($resp->error_response));
        
        // 如果是物料ID问题，尝试使用另一个物料ID
        if (isset($resp->error_response->sub_code) && $resp->error_response->sub_code == "isv.material-id-invalid") {
            error_log("Trying alternative material ID");
            
            // 使用另一个通用物料ID
            $req->setMaterialId(27448); // 使用通用女装物料ID
            $resp = $c->execute($req);
            
            if (isset($resp->error_response)) {
                echo json_encode([
                    'error' => true,
                    'msg' => $resp->error_response->sub_msg ?? $resp->error_response->msg ?? '获取商品失败',
                    'code' => $resp->error_response->code ?? null
                ]);
                exit();
            }
        } else {
            echo json_encode([
                'error' => true,
                'msg' => $resp->error_response->sub_msg ?? $resp->error_response->msg ?? '获取商品失败',
                'code' => $resp->error_response->code ?? null
            ]);
            exit();
        }
    }
    
    // 获取到的商品数据
    $items = [];
    $totalResults = 0;
    
    if (isset($resp->result_list->map_data)) {
        $results = $resp->result_list->map_data;
        $totalResults = $resp->total_results;
        
        error_log("Taobao API Success - Total Results: $totalResults, Items Count: " . count($results));
        
        foreach ($results as $item) {
            // 获取基本信息和价格信息
            $basicInfo = $item->item_basic_info;
            $priceInfo = $item->price_promotion_info;
            $publishInfo = $item->publish_info;
            
            // 计算优惠券信息
            $couponAmount = 0;
            $couponInfo = '';
            $couponStartTime = '';
            $couponEndTime = '';
            
            // 处理优惠券和促销信息
            if (isset($priceInfo->final_promotion_path_list->final_promotion_path_map_data)) {
                $promotions = $priceInfo->final_promotion_path_list->final_promotion_path_map_data;
                
                // 转换为数组处理单个对象情况
                if (!is_array($promotions)) {
                    $promotions = [$promotions];
                }
                
                foreach ($promotions as $promo) {
                    if (strpos($promo->promotion_title, '券') !== false) {
                        $couponAmount += floatval($promo->promotion_fee);
                        $couponInfo = $promo->promotion_desc;
                        $couponStartTime = date('Y-m-d H:i:s', intval($promo->promotion_start_time/1000));
                        $couponEndTime = date('Y-m-d H:i:s', intval($promo->promotion_end_time/1000));
                    }
                }
            }
            
            // 获取原价和最终价格
            $originalPrice = floatval($priceInfo->reserve_price);
            $zkFinalPrice = floatval($priceInfo->zk_final_price);
            $finalPrice = floatval($priceInfo->final_promotion_price);
            
            // 计算佣金比例和金额
            $commissionRate = 0;
            $commissionAmount = 0;
            
            if (isset($publishInfo->income_info)) {
                $commissionRate = floatval($publishInfo->income_info->commission_rate) / 100;
                $commissionAmount = floatval($publishInfo->income_info->commission_amount);
            } else if (isset($publishInfo->income_rate)) {
                $commissionRate = floatval($publishInfo->income_rate) / 100;
                $commissionAmount = $finalPrice * $commissionRate;
            }
            
            // 构建商品信息
            $items[] = [
                'id' => normalizeValue($item->item_id),
                'title' => normalizeValue($basicInfo->title),
                'image_url' => normalizeValue($basicInfo->pict_url),
                'small_images' => isset($basicInfo->small_images->string) ? normalizeValue($basicInfo->small_images->string) : [],
                'original_price' => $originalPrice,
                'zk_final_price' => $zkFinalPrice,
                'final_price' => $finalPrice,
                'coupon_amount' => $couponAmount,
                'coupon_info' => normalizeValue($couponInfo),
                'coupon_start_time' => $couponStartTime,
                'coupon_end_time' => $couponEndTime,
                'shop_title' => normalizeValue($basicInfo->shop_title ?? ''),
                'seller_id' => normalizeValue($basicInfo->seller_id ?? ''),
                'volume' => normalizeValue($basicInfo->volume ?? 0),
                'item_url' => isset($publishInfo->click_url) ? 'https:' . $publishInfo->click_url : '',
                'coupon_click_url' => isset($publishInfo->coupon_share_url) ? 'https:' . $publishInfo->coupon_share_url : '',
                'commission_rate' => $commissionRate,
                'commission_amount' => $commissionAmount
            ];
        }
    }
    
    // 构造分页信息
    $pagination = [
        'total' => $totalResults,
        'current_page' => $page,
        'page_size' => $pageSize,
        'total_pages' => ceil($totalResults / $pageSize)
    ];
    
    // 记录返回的数据量
    error_log("Taobao API Response - Returning items count: " . count($items));
    
    // 返回数据
    echo json_encode([
        'error' => false,
        'data' => $items,
        'pagination' => $pagination
    ]);
    
} catch (Exception $e) {
    error_log("Taobao API Exception: " . $e->getMessage());
    echo json_encode([
        'error' => true,
        'msg' => '调用淘宝接口异常: ' . $e->getMessage()
    ]);
} 