/**
 * 推荐穿搭列表页 JavaScript 逻辑
 */
 
// 配置项
const config = {
    apiBaseUrl: '../login_backend',
    perPage: 10
};

// 页面元素
const elements = {
    // 统计数据
    totalOutfits: document.getElementById('totalOutfits'),
    activeOutfits: document.getElementById('activeOutfits'),
    totalViews: document.getElementById('totalViews'),
    totalClicks: document.getElementById('totalClicks'),
    
    // 筛选和搜索
    categoryFilter: document.getElementById('categoryFilter'),
    statusFilter: document.getElementById('statusFilter'),
    searchInput: document.getElementById('searchInput'),
    
    // 表格和加载指示器
    outfitTableBody: document.getElementById('outfitTableBody'),
    loadingIndicator: document.getElementById('loadingIndicator'),
    pagination: document.getElementById('pagination'),
    errorMessage: document.getElementById('errorMessage'),
    
    // 按钮
    addOutfitBtn: document.getElementById('addOutfitBtn'),
    logoutBtn: document.getElementById('logoutBtn'),
    
    // 模态框
    deleteModal: document.getElementById('deleteModal'),
    deleteOutfitName: document.getElementById('deleteOutfitName'),
    cancelDelete: document.getElementById('cancelDelete'),
    confirmDelete: document.getElementById('confirmDelete')
};

// 状态管理
const state = {
    outfits: [],
    categories: [],
    pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 0
    },
    filters: {
        categoryId: '',
        status: '',
        search: ''
    },
    stats: {
        total: 0,
        active: 0,
        views: 0,
        clicks: 0
    },
    currentDeleteId: null
};

// API 请求处理
const api = {
    /**
     * 获取请求头
     * @returns {Object} 包含认证信息的请求头
     */
    getHeaders() {
        const token = Auth.getToken();
        return {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + token
        };
    },
    
    /**
     * 获取推荐穿搭列表
     * @param {number} page 页码
     * @param {Object} filters 过滤条件
     * @returns {Promise} 返回获取结果
     */
    async getOutfits(page = 1, filters = {}) {
        try {
            const params = new URLSearchParams({
                page: page,
                per_page: config.perPage
            });
            
            // 添加筛选条件
            if (filters.categoryId) params.append('category_id', filters.categoryId);
            if (filters.status !== '') params.append('status', filters.status);
            if (filters.search) params.append('search', filters.search);
            
            const response = await fetch(`${config.apiBaseUrl}/admin_get_recommended_outfits.php?${params.toString()}`, {
                method: 'GET',
                headers: this.getHeaders()
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.msg || '获取推荐穿搭列表失败');
            }
            
            return data;
        } catch (error) {
            throw error;
        }
    },
    
    /**
     * 获取穿搭统计数据
     * @returns {Promise} 返回统计数据
     */
    async getStats() {
        try {
            const response = await fetch(`${config.apiBaseUrl}/admin_get_outfit_stats.php?per_page=1`, {
                method: 'GET',
                headers: this.getHeaders()
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.msg || '获取统计数据失败');
            }
            
            return data;
        } catch (error) {
            throw error;
        }
    },
    
    /**
     * 获取穿搭分类列表
     * @returns {Promise} 返回分类列表
     */
    async getCategories() {
        try {
            const response = await fetch(`${config.apiBaseUrl}/admin_get_recommended_categories.php`, {
                method: 'GET',
                headers: this.getHeaders()
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.msg || '获取分类列表失败');
            }
            
            return data;
        } catch (error) {
            throw error;
        }
    },
    
    /**
     * 删除推荐穿搭
     * @param {number} id 穿搭ID
     * @returns {Promise} 返回删除结果
     */
    async deleteOutfit(id) {
        try {
            const response = await fetch(`${config.apiBaseUrl}/admin_delete_recommended_outfit.php`, {
                method: 'POST',
                headers: this.getHeaders(),
                body: JSON.stringify({ id })
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.msg || '删除推荐穿搭失败');
            }
            
            return data;
        } catch (error) {
            throw error;
        }
    },
    
    /**
     * 更新推荐穿搭状态
     * @param {number} id 穿搭ID
     * @param {number} status 状态值 (0或1)
     * @returns {Promise} 返回更新结果
     */
    async updateOutfitStatus(id, status) {
        try {
            const response = await fetch(`${config.apiBaseUrl}/admin_update_recommended_outfit_status.php`, {
                method: 'POST',
                headers: this.getHeaders(),
                body: JSON.stringify({ id, status })
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.msg || '更新推荐穿搭状态失败');
            }
            
            return data;
        } catch (error) {
            throw error;
        }
    }
};

// 页面功能
const handlers = {
    /**
     * 加载推荐穿搭列表
     * @param {number} page 页码
     */
    async loadOutfits(page = 1) {
        try {
            elements.errorMessage.style.display = 'none';
            elements.loadingIndicator.style.display = 'block';
            elements.outfitTableBody.innerHTML = '';
            
            const data = await api.getOutfits(page, state.filters);
            
            state.outfits = data.data;
            state.pagination = data.pagination;
            
            // 渲染表格
            this.renderTable();
            
            // 渲染分页
            this.renderPagination();
            
            elements.loadingIndicator.style.display = 'none';
        } catch (error) {
            console.error('加载推荐穿搭列表失败:', error);
            elements.loadingIndicator.style.display = 'none';
            elements.errorMessage.textContent = error.message;
            elements.errorMessage.style.display = 'block';
        }
    },
    
    /**
     * 加载统计数据
     */
    async loadStats() {
        try {
            const stats = await api.getStats();
            
            // 计算总浏览量和点击量
            let totalViews = 0;
            let totalClicks = 0;
            let totalOutfits = 0;
            let activeOutfits = 0;
            
            if (stats.data && stats.pagination) {
                totalOutfits = stats.pagination.total;
                
                stats.data.forEach(item => {
                    totalViews += parseInt(item.view_count || 0);
                    totalClicks += parseInt(item.copy_link_count || 0);
                    if (parseInt(item.status) === 1) {
                        activeOutfits++;
                    }
                });
            }
            
            state.stats = {
                total: totalOutfits,
                active: activeOutfits,
                views: totalViews,
                clicks: totalClicks
            };
            
            // 更新UI
            elements.totalOutfits.textContent = state.stats.total;
            elements.activeOutfits.textContent = state.stats.active;
            elements.totalViews.textContent = state.stats.views;
            elements.totalClicks.textContent = state.stats.clicks;
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    },
    
    /**
     * 加载分类列表
     */
    async loadCategories() {
        try {
            const data = await api.getCategories();
            
            state.categories = data.data || [];
            
            // 清空当前选项
            while (elements.categoryFilter.options.length > 1) {
                elements.categoryFilter.remove(1);
            }
            
            // 添加分类选项
            state.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                elements.categoryFilter.appendChild(option);
            });
        } catch (error) {
            console.error('加载分类列表失败:', error);
        }
    },
    
    /**
     * 渲染表格
     */
    renderTable() {
        elements.outfitTableBody.innerHTML = '';
        
        if (state.outfits.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `<td colspan="11" style="text-align: center; padding: 20px;">暂无数据</td>`;
            elements.outfitTableBody.appendChild(row);
            return;
        }
        
        state.outfits.forEach(outfit => {
            const row = document.createElement('tr');
            
            // 格式化日期
            const createdDate = new Date(outfit.created_at);
            const formattedDate = `${createdDate.getFullYear()}-${String(createdDate.getMonth() + 1).padStart(2, '0')}-${String(createdDate.getDate()).padStart(2, '0')}`;
            
            // 状态标签
            const statusBadge = outfit.status == 1 
                ? `<span class="status-badge status-active">已启用</span>` 
                : `<span class="status-badge status-inactive">已禁用</span>`;
            
            // 商品数量
            const itemCount = outfit.item_count || 0;
            
            // 浏览和点击量
            const viewCount = outfit.view_count || 0;
            const clickCount = outfit.copy_link_count || 0;
            
            row.innerHTML = `
                <td>${outfit.id}</td>
                <td><img src="${outfit.image_url}" alt="${outfit.name}" class="outfit-image"></td>
                <td>${outfit.name}</td>
                <td>${outfit.category_name || '-'}</td>
                <td>${itemCount}</td>
                <td>${viewCount}</td>
                <td>${clickCount}</td>
                <td>${outfit.sort_order}</td>
                <td>${statusBadge}</td>
                <td>${formattedDate}</td>
                <td>
                    <button class="action-btn edit-btn" data-id="${outfit.id}">编辑</button>
                    <button class="action-btn toggle-btn" data-id="${outfit.id}" data-status="${outfit.status}">
                        ${outfit.status == 1 ? '禁用' : '启用'}
                    </button>
                    <button class="action-btn delete-btn" data-id="${outfit.id}" data-name="${outfit.name}">删除</button>
                </td>
            `;
            
            elements.outfitTableBody.appendChild(row);
        });
        
        // 添加编辑按钮事件
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', event => {
                const id = event.target.dataset.id;
                window.location.href = `recommended_outfit_edit.html?id=${id}`;
            });
        });
        
        // 添加切换状态按钮事件
        document.querySelectorAll('.toggle-btn').forEach(btn => {
            btn.addEventListener('click', event => {
                const id = event.target.dataset.id;
                const currentStatus = parseInt(event.target.dataset.status);
                const newStatus = currentStatus === 1 ? 0 : 1;
                this.toggleOutfitStatus(id, newStatus);
            });
        });
        
        // 添加删除按钮事件
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', event => {
                const id = event.target.dataset.id;
                const name = event.target.dataset.name;
                this.showDeleteConfirm(id, name);
            });
        });
    },
    
    /**
     * 渲染分页
     */
    renderPagination() {
        elements.pagination.innerHTML = '';
        
        const { currentPage, totalPages } = state.pagination;
        
        if (totalPages <= 1) {
            return;
        }
        
        // 添加第一页按钮
        if (currentPage > 2) {
            const firstPageItem = document.createElement('div');
            firstPageItem.className = 'page-item';
            firstPageItem.textContent = '1';
            firstPageItem.addEventListener('click', () => this.loadOutfits(1));
            elements.pagination.appendChild(firstPageItem);
            
            if (currentPage > 3) {
                const ellipsis = document.createElement('div');
                ellipsis.className = 'page-item';
                ellipsis.textContent = '...';
                ellipsis.style.cursor = 'default';
                elements.pagination.appendChild(ellipsis);
            }
        }
        
        // 添加当前页前后各一页的按钮
        for (let i = Math.max(1, currentPage - 1); i <= Math.min(totalPages, currentPage + 1); i++) {
            const pageItem = document.createElement('div');
            pageItem.className = i === currentPage ? 'page-item active' : 'page-item';
            pageItem.textContent = i;
            
            if (i !== currentPage) {
                pageItem.addEventListener('click', () => this.loadOutfits(i));
            }
            
            elements.pagination.appendChild(pageItem);
        }
        
        // 添加最后一页按钮
        if (currentPage < totalPages - 1) {
            if (currentPage < totalPages - 2) {
                const ellipsis = document.createElement('div');
                ellipsis.className = 'page-item';
                ellipsis.textContent = '...';
                ellipsis.style.cursor = 'default';
                elements.pagination.appendChild(ellipsis);
            }
            
            const lastPageItem = document.createElement('div');
            lastPageItem.className = 'page-item';
            lastPageItem.textContent = totalPages;
            lastPageItem.addEventListener('click', () => this.loadOutfits(totalPages));
            elements.pagination.appendChild(lastPageItem);
        }
    },
    
    /**
     * 切换穿搭状态
     * @param {number} id 穿搭ID
     * @param {number} status 新状态
     */
    async toggleOutfitStatus(id, status) {
        try {
            const result = await api.updateOutfitStatus(id, status);
            
            // 更新本地数据
            const index = state.outfits.findIndex(outfit => outfit.id == id);
            if (index !== -1) {
                state.outfits[index].status = status;
                this.renderTable();
            }
            
            // 刷新统计数据
            this.loadStats();
        } catch (error) {
            console.error('更新穿搭状态失败:', error);
            elements.errorMessage.textContent = error.message;
            elements.errorMessage.style.display = 'block';
        }
    },
    
    /**
     * 显示删除确认框
     * @param {number} id 穿搭ID
     * @param {string} name 穿搭名称
     */
    showDeleteConfirm(id, name) {
        state.currentDeleteId = id;
        elements.deleteOutfitName.textContent = name;
        elements.deleteModal.style.display = 'flex';
    },
    
    /**
     * 关闭删除确认框
     */
    hideDeleteConfirm() {
        elements.deleteModal.style.display = 'none';
        state.currentDeleteId = null;
    },
    
    /**
     * 删除穿搭
     */
    async deleteOutfit() {
        if (!state.currentDeleteId) return;
        
        try {
            await api.deleteOutfit(state.currentDeleteId);
            
            // 关闭确认框
            this.hideDeleteConfirm();
            
            // 刷新列表和统计数据
            this.loadOutfits(state.pagination.currentPage);
            this.loadStats();
        } catch (error) {
            console.error('删除穿搭失败:', error);
            this.hideDeleteConfirm();
            elements.errorMessage.textContent = error.message;
            elements.errorMessage.style.display = 'block';
        }
    },
    
    /**
     * 初始化页面事件
     */
    initEvents() {
        // 添加按钮点击事件
        elements.addOutfitBtn.addEventListener('click', () => {
            window.location.href = 'recommended_outfit_edit.html';
        });
        
        // 退出登录按钮
        elements.logoutBtn.addEventListener('click', () => {
            Auth.logout();
            window.location.href = 'index.html';
        });
        
        // 分类筛选变化
        elements.categoryFilter.addEventListener('change', () => {
            state.filters.categoryId = elements.categoryFilter.value;
            this.loadOutfits(1);
        });
        
        // 状态筛选变化
        elements.statusFilter.addEventListener('change', () => {
            state.filters.status = elements.statusFilter.value;
            this.loadOutfits(1);
        });
        
        // 搜索框输入延迟触发
        let searchTimeout;
        elements.searchInput.addEventListener('input', () => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                state.filters.search = elements.searchInput.value.trim();
                this.loadOutfits(1);
            }, 500);
        });
        
        // 删除确认框事件
        elements.cancelDelete.addEventListener('click', () => this.hideDeleteConfirm());
        elements.confirmDelete.addEventListener('click', () => this.deleteOutfit());
        
        // 显示用户名
        document.getElementById('userName').textContent = Auth.getUsername() || '管理员';
    },
    
    /**
     * 初始化页面
     */
    async init() {
        // 检查登录状态
        if (!Auth.isLoggedIn()) {
            window.location.href = 'index.html';
            return;
        }
        
        try {
            // 初始化事件
            this.initEvents();
            
            // 加载分类列表
            await this.loadCategories();
            
            // 加载穿搭列表和统计数据
            await Promise.all([
                this.loadOutfits(1),
                this.loadStats()
            ]);
        } catch (error) {
            console.error('初始化页面失败:', error);
            elements.errorMessage.textContent = '初始化页面失败，请刷新重试';
            elements.errorMessage.style.display = 'block';
        }
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    handlers.init();
}); 