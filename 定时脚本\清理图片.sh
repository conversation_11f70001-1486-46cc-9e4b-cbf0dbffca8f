#!/bin/bash

# 图片清理脚本 - 用于定时清理指定目录下的图片文件
# 作者：AI助手
# 修改日期：$(date +"%Y-%m-%d")

# 默认参数
DAYS_OLD=0  # 默认清理3天前的文件
UPLOADS_DIR="/www/wwwroot/cyyg.alidog.cn/ceshitupian"
LOGS_DIR="/www/wwwroot/cyyg.alidog.cn/login_backend/logs"
LOG_FILE="$LOGS_DIR/image_cleanup_$(date +"%Y%m%d_%H%M%S").log"

# 检查是否提供了自定义天数参数
if [ ! -z "$1" ] && [[ "$1" =~ ^[0-9]+$ ]]; then
    DAYS_OLD=$1
fi

# 确保日志目录存在
mkdir -p "$LOGS_DIR"

# 创建日志文件头部
echo "===== 图片清理日志 - $(date +"%Y-%m-%d %H:%M:%S") =====" > "$LOG_FILE"
echo "清理目标: $UPLOADS_DIR" >> "$LOG_FILE"
if [ "$DAYS_OLD" -eq 0 ]; then
    echo "清理条件: 清理当前目录下所有jpg/png文件(不包括子目录)" >> "$LOG_FILE"
else
    echo "清理条件: $DAYS_OLD 天前的jpg/png文件(不包括子目录)" >> "$LOG_FILE"
fi
echo "----------------------------------------" >> "$LOG_FILE"

# 获取清理前的图片数量和空间占用
FILE_COUNT_BEFORE=$(find "$UPLOADS_DIR" -maxdepth 1 -type f \( -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" \) | wc -l)
SPACE_BEFORE=$(du -sh "$UPLOADS_DIR" | awk '{print $1}')

echo "清理前: $FILE_COUNT_BEFORE 个图片文件, 占用空间: $SPACE_BEFORE" >> "$LOG_FILE"
echo "----------------------------------------" >> "$LOG_FILE"

# 查找并删除指定天数之前的图片文件，同时记录删除的文件
echo "删除的文件:" >> "$LOG_FILE"

if [ "$DAYS_OLD" -eq 0 ]; then
    # 清理目录下所有jpg/png文件，不检查时间
    find "$UPLOADS_DIR" -maxdepth 1 -type f \( -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" \) -print -delete >> "$LOG_FILE" 2>&1
else
    # 清理目录下指定天数前的jpg/png文件
    find "$UPLOADS_DIR" -maxdepth 1 -type f \( -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" \) -mtime +$DAYS_OLD -print -delete >> "$LOG_FILE" 2>&1
fi

# 获取清理后的图片数量和空间占用
FILE_COUNT_AFTER=$(find "$UPLOADS_DIR" -maxdepth 1 -type f \( -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" \) | wc -l)
SPACE_AFTER=$(du -sh "$UPLOADS_DIR" | awk '{print $1}')
FILES_REMOVED=$((FILE_COUNT_BEFORE - FILE_COUNT_AFTER))

echo "----------------------------------------" >> "$LOG_FILE"
echo "清理后: $FILE_COUNT_AFTER 个图片文件, 占用空间: $SPACE_AFTER" >> "$LOG_FILE"
echo "共删除: $FILES_REMOVED 个图片文件" >> "$LOG_FILE"
echo "清理完成时间: $(date +"%Y-%m-%d %H:%M:%S")" >> "$LOG_FILE"

# 输出清理结果到标准输出，方便宝塔面板显示结果
if [ "$DAYS_OLD" -eq 0 ]; then
    echo "清理完成！删除了 $FILES_REMOVED 个图片文件。详情见日志: $LOG_FILE"
else
    echo "清理完成！删除了 $FILES_REMOVED 个超过 $DAYS_OLD 天的图片文件。详情见日志: $LOG_FILE"
fi

exit 0
