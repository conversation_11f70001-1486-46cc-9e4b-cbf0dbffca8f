/* pages/outfit_categories/add/add.wxss */
.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.form-group {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 30rpx;
}

.form-item {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.form-input-container {
  position: relative;
}

.form-input {
  width: 100%;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  border-radius: 8rpx;
  background-color: #f9f9f9;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  font-size: 28rpx;
  color: #333;
  border-radius: 8rpx;
  background-color: #f9f9f9;
  padding: 20rpx;
  box-sizing: border-box;
}

.counter {
  position: absolute;
  bottom: 10rpx;
  right: 20rpx;
  font-size: 24rpx;
  color: #999;
}

/* 按钮组与穿搭页一致 */
.button-group {
  display: flex;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
}

.cancel-button, .submit-button {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin: 0 8px;
  border: none;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #333333;
}

.submit-button {
  background-color: #000000;
  color: #ffffff;
}

/* 禁用状态的按钮 */
.submit-button[disabled] {
  opacity: 0.6;
}