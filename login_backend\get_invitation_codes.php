<?php
/**
 * 获取邀请码列表API
 */
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

// 获取Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '需要授权']);
    exit;
}

// 验证token
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或过期的令牌']);
    exit;
}

// 获取管理员ID
$adminId = $payload['admin_id'];

// 检查管理员是否存在
$db = new Database();
$conn = $db->getConnection();
$adminStmt = $conn->prepare("SELECT id, username FROM admin_users WHERE id = :id");
$adminStmt->bindParam(':id', $adminId);
$adminStmt->execute();
$admin = $adminStmt->fetch(PDO::FETCH_ASSOC);

if (!$admin) {
    http_response_code(403);
    echo json_encode(['error' => true, 'msg' => '权限不足']);
    exit;
}

// 获取请求参数
$page = isset($_GET['page']) && is_numeric($_GET['page']) ? (int)$_GET['page'] : 1;
$pageSize = isset($_GET['page_size']) && is_numeric($_GET['page_size']) ? (int)$_GET['page_size'] : 10;
$status = isset($_GET['status']) ? $_GET['status'] : '';
$code = isset($_GET['code']) ? $_GET['code'] : '';

// 验证参数
if ($page < 1) $page = 1;
if ($pageSize < 1 || $pageSize > 100) $pageSize = 10;

// 计算偏移量
$offset = ($page - 1) * $pageSize;

try {
    // 构建查询条件
    $whereConditions = [];
    $params = [];
    
    if (!empty($status)) {
        $whereConditions[] = "ic.status = :status";
        $params[':status'] = $status;
    }
    
    if (!empty($code)) {
        $whereConditions[] = "ic.code LIKE :code";
        $params[':code'] = "%{$code}%";
    }
    
    // 组合WHERE子句
    $whereClause = '';
    if (!empty($whereConditions)) {
        $whereClause = "WHERE " . implode(' AND ', $whereConditions);
    }
    
    // 获取总记录数
    $countSql = "
        SELECT COUNT(*) AS total 
        FROM invitation_codes ic
        $whereClause
    ";
    
    $countStmt = $conn->prepare($countSql);
    foreach ($params as $key => $value) {
        $countStmt->bindValue($key, $value);
    }
    $countStmt->execute();
    $totalResult = $countStmt->fetch(PDO::FETCH_ASSOC);
    $total = $totalResult ? (int)$totalResult['total'] : 0;
    
    // 查询邀请码列表
    $sql = "
        SELECT 
            ic.*, 
            creator.username AS creator_name,
            user.nickname AS user_name
        FROM invitation_codes ic
        LEFT JOIN admin_users AS creator ON ic.created_by = creator.id
        LEFT JOIN users AS user ON ic.used_by = user.id
        $whereClause
        ORDER BY ic.id DESC
        LIMIT :offset, :limit
    ";
    
    $stmt = $conn->prepare($sql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':limit', $pageSize, PDO::PARAM_INT);
    $stmt->execute();
    $invitationCodes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 返回结果
    echo json_encode([
        'error' => false,
        'msg' => '获取成功',
        'data' => $invitationCodes,
        'page' => $page,
        'page_size' => $pageSize,
        'total' => $total
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '获取邀请码列表失败: ' . $e->getMessage()]);
} 