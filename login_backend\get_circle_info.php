<?php
// 获取圈子信息API
// 模块1：圈子基础管理模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查找用户所在的圈子
    $findCircleSql = "SELECT c.id, c.name, c.description, c.invitation_code, c.creator_id, 
                             c.member_count, c.created_at, cm.role, cm.joined_at
                      FROM circle_members cm 
                      JOIN outfit_circles c ON cm.circle_id = c.id 
                      WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
    $findCircleStmt = $conn->prepare($findCircleSql);
    $findCircleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $findCircleStmt->execute();
    
    $userCircle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);

    if (!$userCircle) {
        // 检查用户是否曾经被踢出过圈子（且未读通知）
        $removedSql = "SELECT c.name as circle_name, cm.removed_at, cm.removed_by,
                              u.nickname as removed_by_nickname
                       FROM circle_members cm
                       JOIN outfit_circles c ON cm.circle_id = c.id
                       LEFT JOIN users u ON cm.removed_by = u.id
                       WHERE cm.user_id = :user_id AND cm.status = 'removed'
                       AND cm.removed_at NOT LIKE '%_read'
                       ORDER BY cm.removed_at DESC LIMIT 1";
        $removedStmt = $conn->prepare($removedSql);
        $removedStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $removedStmt->execute();

        $removedInfo = $removedStmt->fetch(PDO::FETCH_ASSOC);

        // 用户未加入任何圈子
        echo json_encode([
            'status' => 'success',
            'data' => [
                'has_circle' => false,
                'user_role' => null,
                'circle' => null,
                'removed_info' => $removedInfo ? [
                    'was_removed' => true,
                    'circle_name' => $removedInfo['circle_name'],
                    'removed_at' => $removedInfo['removed_at'],
                    'removed_by_nickname' => $removedInfo['removed_by_nickname'] ?? '创建者',
                    'is_self_removed' => $removedInfo['removed_by'] == $userId
                ] : [
                    'was_removed' => false
                ]
            ]
        ]);
        exit;
    }
    
    // 获取圈子成员列表和统计信息
    $membersSql = "SELECT cm.user_id, cm.role, cm.joined_at,
                          u.nickname, u.avatar_url,
                          COALESCE(cms.wardrobe_count, 0) as wardrobe_count,
                          COALESCE(cms.clothes_count, 0) as clothes_count,
                          COALESCE(cms.outfit_count, 0) as outfit_count,
                          COALESCE(cms.clothing_category_count, 0) as clothing_category_count,
                          COALESCE(cms.outfit_category_count, 0) as outfit_category_count,
                          COALESCE(cms.tag_count, 0) as tag_count,
                          cms.last_contribution_at
                   FROM circle_members cm
                   JOIN users u ON cm.user_id = u.id
                   LEFT JOIN circle_member_stats cms ON cm.circle_id = cms.circle_id AND cm.user_id = cms.user_id
                   WHERE cm.circle_id = :circle_id AND cm.status = 'active'
                   ORDER BY cm.role DESC, cm.joined_at ASC";
    $membersStmt = $conn->prepare($membersSql);
    $membersStmt->bindParam(':circle_id', $userCircle['id'], PDO::PARAM_INT);
    $membersStmt->execute();

    $members = [];
    $totalStats = [
        'wardrobe_count' => 0,
        'clothes_count' => 0,
        'outfit_count' => 0,
        'clothing_category_count' => 0,
        'outfit_category_count' => 0,
        'tag_count' => 0
    ];

    while ($member = $membersStmt->fetch(PDO::FETCH_ASSOC)) {
        $memberStats = [
            'wardrobe_count' => intval($member['wardrobe_count']),
            'clothes_count' => intval($member['clothes_count']),
            'outfit_count' => intval($member['outfit_count']),
            'clothing_category_count' => intval($member['clothing_category_count']),
            'outfit_category_count' => intval($member['outfit_category_count']),
            'tag_count' => intval($member['tag_count']),
            'total_contributions' => intval($member['wardrobe_count']) +
                                   intval($member['clothes_count']) +
                                   intval($member['outfit_count']) +
                                   intval($member['clothing_category_count']) +
                                   intval($member['outfit_category_count']) +
                                   intval($member['tag_count']),
            'last_contribution_at' => $member['last_contribution_at']
        ];

        $members[] = [
            'user_id' => $member['user_id'],
            'nickname' => $member['nickname'] ?? '未知用户',
            'avatar_url' => $member['avatar_url'],
            'role' => $member['role'],
            'joined_at' => $member['joined_at'],
            'is_current_user' => $member['user_id'] == $userId,
            'stats' => $memberStats
        ];

        // 累计总统计
        $totalStats['wardrobe_count'] += $memberStats['wardrobe_count'];
        $totalStats['clothes_count'] += $memberStats['clothes_count'];
        $totalStats['outfit_count'] += $memberStats['outfit_count'];
        $totalStats['clothing_category_count'] += $memberStats['clothing_category_count'];
        $totalStats['outfit_category_count'] += $memberStats['outfit_category_count'];
        $totalStats['tag_count'] += $memberStats['tag_count'];
    }

    // 使用实际统计数据
    $stats = $totalStats;
    
    echo json_encode([
        'status' => 'success',
        'data' => [
            'has_circle' => true,
            'user_role' => $userCircle['role'],
            'circle' => [
                'id' => $userCircle['id'],
                'name' => $userCircle['name'],
                'description' => $userCircle['description'],
                'invitation_code' => $userCircle['invitation_code'],
                'creator_id' => $userCircle['creator_id'],
                'member_count' => $userCircle['member_count'],
                'created_at' => $userCircle['created_at'],
                'user_joined_at' => $userCircle['joined_at'],
                'is_creator' => $userCircle['role'] === 'creator',
                'members' => $members,
                'stats' => $stats
            ]
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '获取圈子信息失败：' . $e->getMessage()
    ]);
}
?>
