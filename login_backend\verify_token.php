<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// <PERSON>le preflight request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// Check if Authorization header is present
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => 'Missing authorization header']);
    exit();
}

// Get token from Authorization header
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// Verify token
$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => 'Invalid or expired token']);
    exit();
}

// Get user info
$userId = $payload['sub'];
$db = new Database();
$stmt = $db->getConnection()->prepare("SELECT id, nickname, avatar_url, gender, created_at, merchant_status, share_try_on_credits FROM users WHERE id = :id");
$stmt->bindParam(':id', $userId);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    http_response_code(404);
    echo json_encode(['error' => true, 'msg' => 'User not found']);
    exit();
}

// 确保商家状态和共享试穿点数字段有默认值
if (!isset($user['merchant_status'])) {
    $user['merchant_status'] = 'no';
}
if (!isset($user['share_try_on_credits'])) {
    $user['share_try_on_credits'] = 0;
} else {
    // 确保share_try_on_credits是整数类型
    $user['share_try_on_credits'] = (int)$user['share_try_on_credits'];
}

// Return user info
echo json_encode([
    'error' => false,
    'data' => $user
]); 