 const app = getApp();

Page({
  data: {
    useSystemNavBar: true, // 默认使用系统导航栏
    tempImagePath: '', // 临时图片路径
    selectedCategory: '', // 选中的类别
    selectedTags: {}, // 选中的标签
    showCustomTagInput: false, // 是否显示自定义标签输入框
    customTagText: '', // 自定义标签文本
    customTags: [], // 用户添加的自定义标签
    name: '', // 衣物名称
    color: '', // 衣物颜色
    brand: '', // 衣物品牌
    price: '', // 衣物价格
    notes: '', // 衣物备注
    segmentEnabled: false, // 是否启用抠图功能
    originalImageUrl: '', // 原图URL
    segmentedImageUrl: '', // 抠图后的URL
    wardrobeId: null, // 衣物所属衣橱ID
    wardrobeList: [], // 衣橱列表
    defaultWardrobeId: null, // 默认衣橱ID
    selectedWardrobeName: '默认衣橱', // 选中的衣橱名称
    rotationAngle: 0, // 图片旋转角度
    scaleValue: 1.0, // 图片缩放比例
    
    // 新增：图片移动相关属性
    imageOffsetX: 0, // 图片X轴偏移量
    imageOffsetY: 0, // 图片Y轴偏移量
    isDragging: false, // 是否正在拖拽
    startX: 0, // 触摸开始时的X坐标
    startY: 0, // 触摸开始时的Y坐标
    lastOffsetX: 0, // 上次的X偏移量
    lastOffsetY: 0, // 上次的Y偏移量
    
    // 新增：模块展开/收起状态
    isSegmentExpanded: false, // 抠图模块展开状态
    isRotationExpanded: false, // 旋转模块展开状态
    isScaleExpanded: false, // 缩放模块展开状态
    
    // 动态获取的类别选项
    categories: [],
    
    // 预设的标签选项
    tags: [
      { name: '春季', value: 'spring' },
      { name: '夏季', value: 'summer' },
      { name: '秋季', value: 'autumn' },
      { name: '冬季', value: 'winter' },
      { name: '休闲', value: 'casual' },
      { name: '通勤', value: 'work' },
      { name: '派对', value: 'party' },
      { name: '运动', value: 'sport' }
    ]
  },
  
  onLoad: function (options) {
    // 检查是否登录
    if (!app.globalData.token) {
      wx.redirectTo({
        url: '/pages/login/login'
      });
      return;
    }
    
    // 如果从其他页面带有参数，可以在这里处理
    if (options.category) {
      this.setData({
        selectedCategory: options.category
      });
    }
    
    // 检查是否指定了衣橱ID
    if (options.wardrobe_id) {
      console.log('添加衣物到衣橱ID:', options.wardrobe_id);
      this.setData({
        wardrobeId: options.wardrobe_id
      });
    }
    
    // 获取衣橱列表
    this.getWardrobeList();
    
    // 获取分类列表
    this.getClothingCategories();
  },
  
  // 获取衣橱列表
  getWardrobeList: function() {
    wx.showLoading({
      title: '加载衣橱...',
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_wardrobes.php`,
      method: 'GET',
      header: {
        'content-type': 'application/json',
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.statusCode === 200 && res.data.success) {
          console.log('获取衣橱列表成功:', res.data);
          const wardrobeList = res.data.data || [];
          
          // 查找默认衣橱
          let defaultWardrobe = wardrobeList.find(w => w.is_default == 1);
          let defaultWardrobeId = defaultWardrobe ? defaultWardrobe.id : null;
          
          this.setData({
            wardrobeList: wardrobeList,
            defaultWardrobeId: defaultWardrobeId
          });
          
          // 如果没有在选项中指定衣橱ID，则使用默认衣橱ID
          if (!this.data.wardrobeId && defaultWardrobeId) {
            this.setData({
              wardrobeId: defaultWardrobeId
            });
          }
          
          // 设置选中的衣橱名称
          this.updateSelectedWardrobeName();
        } else {
          console.error('获取衣橱列表失败:', res);
          wx.showToast({
            title: '获取衣橱列表失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 更新选中的衣橱名称
  updateSelectedWardrobeName: function() {
    const { wardrobeId, wardrobeList } = this.data;
    
    if (wardrobeId && wardrobeList.length > 0) {
      const selectedWardrobe = wardrobeList.find(w => w.id == wardrobeId);
      if (selectedWardrobe) {
        this.setData({
          selectedWardrobeName: selectedWardrobe.name
        });
      }
    }
  },
  
  // 选择衣橱
  bindWardrobeChange: function(e) {
    const index = e.detail.value;
    const wardrobe = this.data.wardrobeList[index];
    
    if (wardrobe) {
      this.setData({
        wardrobeId: wardrobe.id,
        selectedWardrobeName: wardrobe.name
      });
      console.log('选择衣橱:', wardrobe.name, '(ID:', wardrobe.id, ')');
    }
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },
  
  // 选择图片
  chooseImage: function() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      camera: 'back',
      success: (res) => {
        console.log('选择图片成功:', res);
        
        // 保存临时图片路径
        const imagePath = res.tempFiles[0].tempFilePath;
        this.setData({
          tempImagePath: imagePath
        });
        
        // 显示正在分析提示
        wx.showLoading({
          title: '正在分析衣物...',
          mask: true
        });
        
        // 分析图片
        this.analyzeImage(imagePath, (result, error) => {
          wx.hideLoading();
          
          if (error) {
            console.error('衣物分析失败:', error);
            wx.showToast({
              title: '分析失败，请手动填写',
              icon: 'none'
            });
            return;
          }
          
          console.log('衣物分析成功:', result);
          
          // 填充分析结果到表单
          this.fillAnalysisResult(result);
        });
      }
    });
  },
  
  // 使用Gemini API分析图片
  analyzeImage: function(imagePath, callback) {
    console.log('开始分析图片:', imagePath);
    // 读取图片文件
    wx.getFileSystemManager().readFile({
      filePath: imagePath,
      success: (res) => {
        // 将图片数据转为base64
        const base64Image = wx.arrayBufferToBase64(res.data);
        console.log('图片转base64成功，长度:', base64Image.length);
        
        // 准备提示词
        const prompt = "识别图片中的衣物，并严格按照以下JSON格式返回结果，不要有任何额外的文本：\n" +
                       "{\n" +
                       '  "衣物类别": "上衣、裤子、裙子、外套、鞋子、包包、配饰中的一个",\n' +
                       '  "衣物标签": ["春季/夏季/秋季/冬季", "休闲/通勤/派对/运动", "标签1", "标签2", "标签3", "标签4", "标签5"],\n' +
                       '  "衣物信息": {\n' +
                       '    "衣物名称": "名称",\n' +
                       '    "颜色": "颜色"\n' +
                       '  }\n' +
                       "}";
        
        // 设置请求参数和headers
        const requestUrl = app.globalData.apiBaseUrl + '/analyze_clothing.php';
        console.log('发送分析请求到:', requestUrl);
        
        wx.request({
          url: requestUrl,
          method: 'POST',
          data: {
            image_base64: base64Image,
            prompt: prompt
          },
          header: {
            'content-type': 'application/json',
            'Authorization': app.globalData.token
          },
          success: (res) => {
            console.log('分析响应状态码:', res.statusCode);
            console.log('分析响应完整数据:', JSON.stringify(res.data));
            
            // 检查响应是否为字符串（需要解析）
            let responseData = res.data;
            if (typeof responseData === 'string') {
              try {
                // 尝试作为JSON解析
                console.log('尝试解析响应字符串...');
                
                // 检查是否包含多个JSON对象（解决连接问题）
                if (responseData.indexOf('}{') !== -1) {
                  console.log('检测到多个JSON对象连接情况，尝试提取最后一个...');
                  const lastJsonStartPos = responseData.lastIndexOf('{');
                  responseData = responseData.substring(lastJsonStartPos);
                }
                
                responseData = JSON.parse(responseData);
                console.log('解析后的数据:', responseData);
              } catch (e) {
                console.error('解析响应数据失败:', e);
                callback(null, '解析响应数据失败');
                return;
              }
            }
            
            // 增强判断条件，服务器端返回的success字段应该是布尔值true而非字符串
            if (res.statusCode === 200 && responseData.success === true) {
              // 分析成功
              console.log('分析成功，返回数据:', JSON.stringify(responseData.data));
              callback(responseData.data);
            } else {
              // 分析失败
              console.error('分析失败, 错误信息:', responseData.msg);
              if (responseData.raw_text) {
                console.log('AI返回的原始文本:', responseData.raw_text);
              }
              callback(null, responseData.msg || '分析失败');
            }
          },
          fail: (err) => {
            console.error('分析请求失败:', err);
            callback(null, '网络错误，请稍后重试');
          }
        });
      },
      fail: (err) => {
        console.error('读取图片文件失败:', err);
        callback(null, '读取图片文件失败');
      }
    });
  },
  
  // 根据分析结果填充表单
  fillAnalysisResult: function(result) {
    // 提取数据
    const category = this.mapCategoryToValue(result.衣物类别 || '');
    const tags = result.衣物标签 || [];
    const name = result.衣物信息?.衣物名称 || '';
    const color = result.衣物信息?.颜色 || '';
    
    // 转换标签为selectedTags对象格式
    const selectedTags = {};
    const existingTags = this.data.tags.map(t => t.value);
    const customTags = [...this.data.customTags]; // 复制现有自定义标签
    
    // 处理标签
    tags.forEach(tag => {
      const tagValue = this.mapTagToValue(tag);
      
      // 如果是预设标签，直接选中
      if (existingTags.includes(tagValue)) {
        selectedTags[tagValue] = true;
      } else {
        // 否则创建自定义标签
        const customTagId = 'custom_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5);
        selectedTags[customTagId] = true;
        customTags.push({
          name: tag,
          value: customTagId
        });
      }
    });
    
    // 更新表单数据
    this.setData({
      name: name,
      color: color,
      selectedCategory: category,
      selectedTags: selectedTags,
      customTags: customTags
    });
    
    // 显示成功提示
    wx.showToast({
      title: '分析完成',
      icon: 'success'
    });
  },
  
  // 映射类别名称到类别值
  mapCategoryToValue: function(categoryName) {
    const categoryMap = {
      '上衣': 'tops',
      '裤子': 'pants',
      '裙子': 'skirts',
      '外套': 'coats',
      '鞋子': 'shoes',
      '包包': 'bags',
      '配饰': 'accessories'
    };
    
    return categoryMap[categoryName] || '';
  },
  
  // 映射标签名称到标签值
  mapTagToValue: function(tagName) {
    const tagMap = {
      '春季': 'spring',
      '夏季': 'summer',
      '秋季': 'autumn',
      '冬季': 'winter',
      '休闲': 'casual',
      '通勤': 'work',
      '派对': 'party',
      '运动': 'sport'
    };
    
    return tagMap[tagName] || tagName.toLowerCase().replace(/\s/g, '_');
  },
  
  // 选择类别
  selectCategory: function(e) {
    const value = e.currentTarget.dataset.value;
    
    this.setData({
      selectedCategory: value
    });
  },
  
  // 切换标签选择状态
  toggleTag: function(e) {
    const value = e.currentTarget.dataset.value;
    const selectedTags = { ...this.data.selectedTags };
    
    if (selectedTags[value]) {
      delete selectedTags[value];
    } else {
      selectedTags[value] = true;
    }
    
    this.setData({
      selectedTags
    });
  },
  
  // 显示自定义标签输入框
  showCustomTagInput: function() {
    this.setData({
      showCustomTagInput: true,
      customTagText: ''
    });
  },
  
  // 取消添加自定义标签
  cancelCustomTag: function() {
    this.setData({
      showCustomTagInput: false,
      customTagText: ''
    });
  },
  
  // 监听自定义标签输入
  onCustomTagInput: function(e) {
    this.setData({
      customTagText: e.detail.value
    });
  },
  
  // 添加自定义标签
  addCustomTag: function() {
    const { customTagText, customTags, selectedTags } = this.data;
    
    if (!customTagText.trim()) {
      wx.showToast({
        title: '标签不能为空',
        icon: 'none'
      });
      return;
    }
    
    // 生成一个唯一的标签ID
    const tagId = 'custom_' + Date.now();
    
    // 添加到自定义标签列表
    const newCustomTags = [...customTags, {
      name: customTagText,
      value: tagId
    }];
    
    // 自动选中该标签
    const newSelectedTags = { ...selectedTags };
    newSelectedTags[tagId] = true;
    
    this.setData({
      customTags: newCustomTags,
      selectedTags: newSelectedTags,
      showCustomTagInput: false,
      customTagText: ''
    });
    
    // 显示提示
    wx.showToast({
      title: '添加成功',
      icon: 'success'
    });
  },
  
  // 名称输入
  onNameInput: function(e) {
    this.setData({
      name: e.detail.value
    });
  },
  
  // 颜色输入
  onColorInput: function(e) {
    this.setData({
      color: e.detail.value
    });
  },
  
  // 品牌输入
  onBrandInput: function(e) {
    this.setData({
      brand: e.detail.value
    });
  },
  
  // 价格输入
  onPriceInput: function(e) {
    this.setData({
      price: e.detail.value
    });
  },
  
  // 处理备注输入
  onNotesInput: function(e) {
    this.setData({
      notes: e.detail.value
    });
  },
  
  // 切换抠图功能开关
  toggleSegment: function() {
    const currentState = this.data.segmentEnabled;
    const newState = !currentState;
    
    console.log(`[抠图] 开关状态从 ${currentState} 切换为 ${newState}`);
    
    // 更新开关状态
    this.setData({
      segmentEnabled: newState
    });
    
    // 如果打开抠图
    if (newState) {
      // 检查是否有图片
      if (!this.data.tempImagePath) {
        console.log('[抠图] 未选择图片，无法执行抠图');
        wx.showToast({
          title: '请先选择图片',
          icon: 'none'
        });
        
        // 重置开关状态
        this.setData({
          segmentEnabled: false
        });
        return;
      }
      
      // 检查是否有原图URL
      console.log(`[抠图] 检查条件: 开关=${newState}, 原图URL=${!!this.data.originalImageUrl}, 抠图URL=${!!this.data.segmentedImageUrl}, 临时图片=${!!this.data.tempImagePath}`);
      
      if (!this.data.originalImageUrl && this.data.tempImagePath) {
        console.log('[抠图] 有临时图片但没有原图URL，先上传图片');
        
        wx.showLoading({
          title: '处理中...',
          mask: true
        });
        
        // 先上传图片，然后再执行抠图
        this.uploadImage((imageUrl) => {
          console.log('[抠图] 图片上传完成，原图URL:', imageUrl);
          
          // 图片上传完成后，检查是否需要执行抠图
          if (this.data.segmentEnabled && !this.data.segmentedImageUrl) {
            console.log('[抠图] 图片已上传，开始抠图处理');
            this.processSegmentation();
          } else {
            wx.hideLoading();
          }
        }, true); // 传入true表示这是为抠图而上传，不执行后续保存操作
        
        return;
      }
      
      // 如果已有原图但没有抠图图片，执行抠图
      if (this.data.originalImageUrl && !this.data.segmentedImageUrl) {
        console.log('[抠图] 条件满足，准备执行抠图处理');
        wx.showLoading({
          title: '正在抠图...',
          mask: true
        });
        
        // 调用抠图处理
        this.processSegmentation();
      } else {
        console.log('[抠图] 条件不满足，不执行抠图处理');
        if (!this.data.originalImageUrl) {
          console.log('[抠图] 原因: 缺少原图URL');
        }
        if (this.data.segmentedImageUrl) {
          console.log('[抠图] 原因: 已有抠图结果');
        }
      }
    }
    
    // 更新显示的图片
    this.updateDisplayImage();
  },
  
  // 更新显示的图片
  updateDisplayImage: function() {
    // 如果抠图功能开启且有抠图后的图片，则显示抠图后的图片
    // 否则显示原图
    console.log(`[抠图] 更新图片显示: 开关=${this.data.segmentEnabled}, 使用${this.data.segmentEnabled ? '抠图后图片' : '原图'}`);
    console.log(`[抠图] 原图URL: ${this.data.originalImageUrl}`);
    console.log(`[抠图] 抠图URL: ${this.data.segmentedImageUrl}`);
    
    let displayImagePath;
    if (this.data.segmentEnabled && this.data.segmentedImageUrl) {
      console.log('[抠图] 抠图已开启且有抠图图片，显示抠图后的图片');
      displayImagePath = this.data.segmentedImageUrl;
    } else {
      console.log('[抠图] 抠图未开启或无抠图图片，显示原图');
      displayImagePath = this.data.originalImageUrl || this.data.tempImagePath;
    }
    
    console.log(`[抠图] 显示图片: ${displayImagePath}`);
    
    if (displayImagePath && displayImagePath !== this.data.tempImagePath) {
      console.log(`[抠图] 图片路径已变更，更新显示`);
      this.setData({
        tempImagePath: displayImagePath
      });
    } else {
      console.log(`[抠图] 图片路径未变更，保持显示`);
    }
  },
  
  // 处理抠图请求
  processSegmentation: function() {
    // 获取符合JWT格式的token
    const compatibleToken = this.generateCompatibleToken();
    
    console.log('[抠图] 开始发送抠图请求');
    console.log('[抠图] 原图URL:', this.data.originalImageUrl);
    
    if (!this.data.originalImageUrl) {
      console.error('[抠图] 无原图URL，无法执行抠图');
      wx.hideLoading();
      wx.showToast({
        title: '抠图失败：无原图',
        icon: 'none'
      });
      return;
    }
    
    // 先调用图片类型识别API，确定应该使用哪种抠图模式
    this.determineSegmentationType((segmentType) => {
      console.log(`[抠图] 图片类型识别结果: ${segmentType}`);
      
      // 发送抠图请求
      wx.request({
        url: `${app.globalData.apiBaseUrl}/upload_image.php`,
        method: 'POST',
        header: {
          'Authorization': compatibleToken,
          'Content-Type': 'application/json'
        },
        data: {
          image_url: this.data.originalImageUrl,
          segment_image: true,
          segment_type: segmentType // 添加识别出的抠图类型
        },
        success: (res) => {
          wx.hideLoading();
          console.log('[抠图] 请求成功，响应数据:', res);
          
          if (res.statusCode === 200 && !res.data.error) {
            console.log('[抠图] 抠图成功，完整响应数据:', JSON.stringify(res.data));
            
            // 检查响应中是否包含image_url
            if (res.data.data && res.data.data.image_url) {
              console.log('[抠图] 抠图成功，图片URL:', res.data.data.image_url);
              
              // 设置抠图后的图片URL
              this.setData({
                segmentedImageUrl: res.data.data.image_url
              });
              
              // 更新显示的图片
              this.updateDisplayImage();
              
              // 显示提示
              wx.showToast({
                title: '抠图成功',
                icon: 'success'
              });
            } else {
              console.error('[抠图] 响应中缺少image_url字段');
              console.error('[抠图] 完整响应数据:', JSON.stringify(res.data));
              wx.showToast({
                title: '抠图处理异常',
                icon: 'none'
              });
            }
          } else {
            console.error('[抠图] 抠图失败:', res.data.msg || '未知错误');
            console.error('[抠图] 完整响应:', JSON.stringify(res.data));
            
            wx.showToast({
              title: res.data.msg || '抠图失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('[抠图] 请求失败:', err);
          
          // 处理特定的网络错误
          let errorMsg = '网络错误，请稍后重试';
          if (err.errMsg) {
            if (err.errMsg.includes('timeout')) {
              errorMsg = '请求超时，请检查网络';
            } else if (err.errMsg.includes('fail')) {
              errorMsg = '网络连接失败';
            }
            console.error('[抠图] 错误详情:', err.errMsg);
          }
          
          wx.showToast({
            title: errorMsg,
            icon: 'none'
          });
        },
        complete: () => {
          console.log('[抠图] 请求完成');
        }
      });
    });
  },
  
  // 确定抠图类型
  determineSegmentationType: function(callback) {
    console.log('[抠图类型识别] 开始识别图片类型');
    
    if (!this.data.originalImageUrl) {
      console.error('[抠图类型识别] 无原图URL，无法识别图片类型');
      // 如果无法获取图片，默认使用common类型
      callback('common');
      return;
    }
    
    // 获取图片base64数据
    const getImageBase64 = (imageUrl, cb) => {
      // 如果是本地路径，读取文件
      if (this.isLocalPath(imageUrl)) {
        console.log('[抠图类型识别] 图片是本地路径，读取文件内容');
        
        // 读取本地文件
        wx.getFileSystemManager().readFile({
          filePath: imageUrl,
          encoding: 'base64',
          success: (res) => {
            cb(res.data);
          },
          fail: (err) => {
            console.error('[抠图类型识别] 读取图片文件失败:', err);
            // 失败时默认使用common类型
            callback('common');
          }
        });
      } else {
        console.log('[抠图类型识别] 图片是远程URL，下载文件');
        
        // 下载远程图片
        wx.downloadFile({
          url: imageUrl,
          success: (res) => {
            if (res.statusCode === 200) {
              // 读取下载的文件
              wx.getFileSystemManager().readFile({
                filePath: res.tempFilePath,
                encoding: 'base64',
                success: (fileRes) => {
                  cb(fileRes.data);
                },
                fail: (err) => {
                  console.error('[抠图类型识别] 读取下载文件失败:', err);
                  callback('common');
                }
              });
            } else {
              console.error('[抠图类型识别] 下载文件失败:', res);
              callback('common');
            }
          },
          fail: (err) => {
            console.error('[抠图类型识别] 下载图片失败:', err);
            callback('common');
          }
        });
      }
    };
    
    // 获取图片base64数据
    getImageBase64(this.data.originalImageUrl || this.data.tempImagePath, (base64) => {
      console.log('[抠图类型识别] 获取到图片base64数据，长度:', base64.length);
      
      // 调用API识别图片类型
      wx.request({
        url: 'https://www.furrywoo.com/gemini/koutushibie.php',
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: {
          image_base64: base64
        },
        success: (res) => {
          console.log('[抠图类型识别] API响应:', res.data);
          
          if (res.statusCode === 200 && res.data.success) {
            const segmentType = res.data.data && res.data.data.segment_type;
            
            // 验证返回的segment_type是否有效
            if (segmentType === 'cloth' || segmentType === 'common') {
              console.log(`[抠图类型识别] 识别成功，类型: ${segmentType}`);
              callback(segmentType);
            } else {
              console.warn('[抠图类型识别] 返回的类型无效:', segmentType);
              callback('common'); // 默认使用通用类型
            }
          } else {
            console.error('[抠图类型识别] API请求失败:', res.data.msg || '未知错误');
            callback('common'); // 默认使用通用类型
          }
        },
        fail: (err) => {
          console.error('[抠图类型识别] 请求失败:', err);
          callback('common'); // 默认使用通用类型
        }
      });
    });
  },
  
  // 生成符合后端要求的JWT格式token
  generateCompatibleToken: function() {
    const originalToken = app.globalData.token;
    
    // 检查是否已经是JWT格式
    if(originalToken && originalToken.split('.').length === 3) {
      return originalToken;
    }
    
    try {
      // 生成符合JWT格式的token
      const headerObj = {alg: 'HS256', typ: 'JWT'};
      const payloadObj = {
        sub: 1, // 默认用户ID
        openid: 'mock_openid',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7天后过期
      };
      
      // 使用自定义btoa函数进行Base64编码
      const btoa = this.btoa;
      const header = btoa(JSON.stringify(headerObj));
      const payload = btoa(JSON.stringify(payloadObj));
      const signature = 'mocksignature'; // 简化的签名
      
      console.log('生成JWT token组件:', {
        headerObj, payloadObj, 
        headerEncoded: header, 
        payloadEncoded: payload
      });
      
      return `${header}.${payload}.${signature}`;
    } catch (e) {
      console.error('生成JWT token失败:', e);
      // 如果生成失败，返回原始token
      return originalToken || 'mock_token_fallback';
    }
  },
  
  // 设置旋转角度
  onRotationChange: function(e) {
    const angle = parseInt(e.detail.value);
    console.log('[旋转] 设置旋转角度为', angle, '度');
    this.setData({
      rotationAngle: angle
    });
  },
  
  // 向左旋转90度
  rotateLeft: function() {
    const currentAngle = this.data.rotationAngle;
    const newAngle = (currentAngle - 90 + 360) % 360; // 确保角度在0-359之间
    console.log('[旋转] 向左旋转，从', currentAngle, '度到', newAngle, '度');
    this.setData({
      rotationAngle: newAngle
    });
  },
  
  // 向右旋转90度
  rotateRight: function() {
    const currentAngle = this.data.rotationAngle;
    const newAngle = (currentAngle + 90) % 360;
    console.log('[旋转] 向右旋转，从', currentAngle, '度到', newAngle, '度');
    this.setData({
      rotationAngle: newAngle
    });
  },
  
  // 重置旋转角度
  resetRotation: function() {
    console.log('[旋转] 重置旋转角度');
    this.setData({
      rotationAngle: 0
    });
    // 同时重置图片位置
    this.resetImagePosition();
  },
  
  // 设置缩放比例
  onScaleChange: function(e) {
    const scale = parseFloat(e.detail.value);
    console.log('[缩放] 设置缩放比例为', scale, 'x');
    this.setData({
      scaleValue: scale
    });
  },
  
  // 缩小图片(减小0.1)
  scaleDown: function() {
    let currentScale = this.data.scaleValue;
    const newScale = Math.max(0.5, currentScale - 0.1).toFixed(1);
    console.log('[缩放] 缩小图片，从', currentScale, 'x到', newScale, 'x');
    this.setData({
      scaleValue: parseFloat(newScale)
    });
  },
  
  // 放大图片(增加0.1)
  scaleUp: function() {
    let currentScale = this.data.scaleValue;
    const newScale = Math.min(2.0, currentScale + 0.1).toFixed(1);
    console.log('[缩放] 放大图片，从', currentScale, 'x到', newScale, 'x');
    this.setData({
      scaleValue: parseFloat(newScale)
    });
  },
  
  // 重置缩放比例
  resetScale: function() {
    console.log('[缩放] 重置缩放比例');
    this.setData({
      scaleValue: 1.0
    });
    // 同时重置图片位置
    this.resetImagePosition();
  },
  
  // 上传图片
  uploadImage: function(callback, isForSegmentation = false) {
    // 检查是否需要处理图片（旋转或缩放）
    const needRotation = this.data.rotationAngle !== 0;
    const needScaling = this.data.scaleValue !== 1.0;
    const needImageProcessing = (needRotation || needScaling) && 
                                this.data.tempImagePath && 
                                this.isLocalPath(this.data.tempImagePath);
    
    if (needImageProcessing) {
      // 记录处理原因
      let processReason = [];
      if (needRotation) processReason.push(`旋转${this.data.rotationAngle}°`);
      if (needScaling) processReason.push(`缩放${this.data.scaleValue}x`);
      
      console.log(`[上传] 图片需要处理: ${processReason.join('和')}`);
      
      // 处理图片（旋转和缩放）
      wx.showLoading({
        title: '处理图片中...',
        mask: true
      });
      
      // 直接处理本地图片，避免循环调用
      this.processImage(
        this.data.tempImagePath, 
        this.data.rotationAngle, 
        this.data.scaleValue,
        (processedPath) => {
          console.log(`[图片处理] 图片处理完成，新路径: ${processedPath}`);
          
          // 更新临时路径为处理后的图片
          this.setData({
            tempImagePath: processedPath
          });
          
          wx.hideLoading();
          
          // 继续正常的上传流程，使用处理后的图片
          this.uploadOriginal(callback, isForSegmentation);
        }
      );
    } else {
      // 不需要处理，直接使用原来的上传逻辑
      this.uploadOriginal(callback, isForSegmentation);
    }
  },
  
  // 原始保存逻辑，从saveClothing中提取
  continueWithOriginalLogic: function() {
    // 检查是否有旋转或缩放操作
    const needRotation = this.data.rotationAngle !== 0;
    const needScaling = this.data.scaleValue !== 1.0;
    const needImageProcessing = needRotation || needScaling;
    
    let imageToUse = '';
    
    // 如果有旋转或缩放，需要处理图片
    if (needImageProcessing) {
      console.log(`[保存] 检测到图片需要处理: ${needRotation ? '旋转' : ''}${needRotation && needScaling ? '和' : ''}${needScaling ? '缩放' : ''}`);
      
      // 严格检查：确定要使用的图片URL
      if (this.data.segmentEnabled && this.data.segmentedImageUrl) {
        console.log('[保存] 抠图已开启，处理抠图后的图片:', this.data.segmentedImageUrl);
        imageToUse = this.data.segmentedImageUrl;
      } else if (this.data.originalImageUrl) {
        console.log('[保存] 使用原图进行处理:', this.data.originalImageUrl);
        imageToUse = this.data.originalImageUrl;
      } else {
        console.log('[保存] 没有远程URL，处理本地图片');
        // 修复：直接处理本地图片并上传，避免循环调用
        this.processImage(
          this.data.tempImagePath, 
          this.data.rotationAngle, 
          this.data.scaleValue,
          (processedPath) => {
            console.log(`[保存] 本地图片处理完成，新路径: ${processedPath}`);
            this.uploadOriginal((imageUrl) => {
              this.submitClothingData(imageUrl);
            }, false, processedPath);
          }
        );
        return;
      }
      
      // 下载远程图片进行处理
      wx.showLoading({
        title: '处理图片中...',
        mask: true
      });
      
      wx.downloadFile({
        url: imageToUse,
        success: (res) => {
          if (res.statusCode === 200) {
            console.log(`[保存] 图片下载成功，临时路径: ${res.tempFilePath}`);
            
            // 应用旋转和缩放处理
            this.processImage(
              res.tempFilePath, 
              this.data.rotationAngle, 
              this.data.scaleValue,
              (processedPath) => {
                console.log(`[保存] 图片处理完成，新路径: ${processedPath}`);
                
                // 上传处理后的图片
                this.uploadOriginal((imageUrl) => {
                  console.log(`[保存] 处理后图片上传成功，URL: ${imageUrl}`);
                  this.submitClothingData(imageUrl);
                }, false, processedPath);
              }
            );
          } else {
            wx.hideLoading();
            console.error(`[保存] 图片下载失败: ${res.statusCode}`);
            // 下载失败时直接使用远程URL
            this.submitClothingData(imageToUse);
          }
        },
        fail: (err) => {
          wx.hideLoading();
          console.error(`[保存] 图片下载错误:`, err);
          // 下载失败时直接使用远程URL
          this.submitClothingData(imageToUse);
        }
      });
      
      return; // 中断执行，等待异步操作完成
    }
    
    // 如果不需要处理图片，使用原来的保存逻辑
    
    // 严格检查：只有在抠图功能开启的情况下才使用抠图后的图片
    if (this.data.segmentEnabled && this.data.segmentedImageUrl) {
      console.log('[保存] 抠图已开启，使用抠图后的图片:', this.data.segmentedImageUrl);
      imageToUse = this.data.segmentedImageUrl;
      // 直接提交表单数据
      this.submitClothingData(imageToUse);
      return;
    }
    
    // 若抠图未开启或没有抠图结果，使用原图URL
    if (this.data.originalImageUrl) {
      console.log('[保存] 抠图未开启或无抠图结果，使用原图:', this.data.originalImageUrl);
      imageToUse = this.data.originalImageUrl;
      // 直接提交表单数据
      this.submitClothingData(imageToUse);
      return;
    }
    
    // 只有在没有任何远程URL的情况下才上传图片
    console.log('[保存] 没有远程URL，开始上传本地图片');
    // 修复：直接调用uploadOriginal而不是uploadImage，避免循环调用
    this.uploadOriginal((imageUrl) => {
      // 图片上传成功后，提交表单数据
      this.submitClothingData(imageUrl);
    });
  },
  
  // 辅助函数：检查是否是本地路径
  isLocalPath: function(path) {
    return path.startsWith('wxfile://') || 
           path.startsWith('http://tmp') || 
           path.startsWith('https://tmp');
  },
  
  // 原始上传逻辑
  uploadOriginal: function(callback, isForSegmentation = false, rotatedPath = '') {
    // 获取符合JWT格式的token
    const compatibleToken = this.generateCompatibleToken();
    console.log('准备上传图片，使用token:', compatibleToken);
    
    // 检查tempImagePath是否是本地文件路径
    const isLocalPath = this.isLocalPath(rotatedPath || this.data.tempImagePath);
    
    if (!isLocalPath) {
      console.log('[上传] tempImagePath不是本地文件路径，可能是已上传的远程URL:', rotatedPath || this.data.tempImagePath);
      console.log('[上传] 跳过上传，使用现有URL');
      
      // 如果已经有远程URL，直接调用回调
      if (typeof callback === 'function') {
        if (this.data.originalImageUrl) {
          callback(this.data.originalImageUrl);
        } else {
          // 使用tempImagePath作为回退方案
          callback(rotatedPath || this.data.tempImagePath);
        }
      }
      return;
    }
    
    console.log('[上传] tempImagePath是本地文件路径，开始上传');
    
    wx.showLoading({
      title: '上传图片中...',
      mask: true
    });
    
    wx.uploadFile({
      url: `${app.globalData.apiBaseUrl}/upload_image.php`,
      filePath: rotatedPath || this.data.tempImagePath,
      name: 'image',
      header: {
        'Authorization': compatibleToken
      },
      formData: {
        // 添加参数，初始上传时不执行抠图
        segment_image: 'false'
      },
      success: (res) => {
        console.log('上传图片响应:', res);
        try {
          const data = JSON.parse(res.data);
          console.log('解析的响应数据:', data);
          
          if (data.error === false) {
            // 上传成功，获取图片URL
            const imageUrl = data.data.image_url;
            console.log('上传成功，图片URL:', imageUrl);
            console.log('[抠图] 完整响应数据:', JSON.stringify(data.data));
            
            // 保存原图URL
            this.setData({
              originalImageUrl: imageUrl
            });
            
            // 如果response中包含segmented_image_url，说明服务端仍执行了抠图
            // 但只有在用户已启用抠图的情况下才保存和使用它
            if (data.data.segmented_image_url) {
              console.log('[抠图] 服务端返回了抠图URL:', data.data.segmented_image_url);
              
              if (this.data.segmentEnabled) {
                console.log('[抠图] 用户已启用抠图，保存抠图URL');
                this.setData({
                  segmentedImageUrl: data.data.segmented_image_url
                });
              } else {
                console.log('[抠图] 用户未启用抠图，不使用抠图URL');
              }
            } else {
              console.log('[抠图] 服务端未返回抠图URL');
            }
            
            // 更新显示图片 - 根据当前抠图开关状态决定显示哪张图片
            this.updateDisplayImage();
            
            if (typeof callback === 'function') {
              callback(imageUrl);
            }
          } else {
            wx.hideLoading();
            // 显示详细错误信息
            const errorMsg = data.msg || '图片上传失败';
            console.error('上传失败原因:', errorMsg);
            wx.showToast({
              title: errorMsg,
              icon: 'none',
              duration: 3000
            });
            
            // 如果是token无效，尝试使用模拟图片URL继续流程
            if (errorMsg.includes('token') || errorMsg.includes('Token')) {
              console.log('检测到token错误，使用模拟图片URL');
              setTimeout(() => {
                const mockImageUrl = "https://images.unsplash.com/photo-1562157873-818bc0726f68?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80";
                
                // 保存模拟图片URL作为原图
                this.setData({
                  originalImageUrl: mockImageUrl
                });
                
                // 更新显示图片
                this.updateDisplayImage();
                
                if (typeof callback === 'function') {
                  callback(mockImageUrl);
                }
              }, 1500);
            }
          }
        } catch (e) {
          wx.hideLoading();
          console.error('解析响应失败:', e, '原始响应:', res.data);
          wx.showToast({
            title: '解析响应失败',
            icon: 'none',
            duration: 3000
          });
          
          // 解析失败时使用模拟图片URL继续流程
          setTimeout(() => {
            const mockImageUrl = "https://images.unsplash.com/photo-1562157873-818bc0726f68?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80";
            
            // 保存模拟图片URL作为原图
            this.setData({
              originalImageUrl: mockImageUrl
            });
            
            // 更新显示图片
            this.updateDisplayImage();
            
            if (typeof callback === 'function') {
              callback(mockImageUrl);
            }
          }, 1500);
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('上传请求失败:', err);
        
        // 检查是否是"file not found"错误，这可能是因为tempImagePath已经是远程URL
        if (err.errMsg && err.errMsg.includes('file not found')) {
          console.log('[上传] 文件未找到，可能是tempImagePath不是本地路径:', rotatedPath || this.data.tempImagePath);
          
          // 如果已有原图URL，直接使用
          if (this.data.originalImageUrl) {
            console.log('[上传] 使用已存在的原图URL:', this.data.originalImageUrl);
            if (typeof callback === 'function') {
              callback(this.data.originalImageUrl);
            }
            return;
          }
        }
        
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none',
          duration: 3000
        });
        
        // 网络错误时使用模拟图片URL继续流程
        setTimeout(() => {
          const mockImageUrl = "https://images.unsplash.com/photo-1562157873-818bc0726f68?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80";
          
          // 保存模拟图片URL作为原图
          this.setData({
            originalImageUrl: mockImageUrl
          });
          
          // 更新显示图片
          this.updateDisplayImage();
          
          if (typeof callback === 'function') {
            callback(mockImageUrl);
          }
        }, 1500);
      }
    });
  },
  
  // 添加旋转图片的函数
  rotateImage: function(imagePath, angle, callback) {
    console.log(`[旋转] 开始处理图片旋转，角度: ${angle}°`);
    
    // 获取图片信息
    wx.getImageInfo({
      src: imagePath,
      success: (res) => {
        console.log('[旋转] 获取图片信息成功:', res);
        const { width, height, path } = res;
        
        // 使用新版Canvas 2D API
        const query = wx.createSelectorQuery();
        query.select('#rotateCanvas')
          .fields({ node: true, size: true })
          .exec((canvasRes) => {
            if (!canvasRes || !canvasRes[0] || !canvasRes[0].node) {
              console.error('[旋转] 获取canvas节点失败');
              if (typeof callback === 'function') {
                callback(imagePath); // 失败时返回原图路径
              }
              return;
            }
            
            const canvas = canvasRes[0].node;
            const ctx = canvas.getContext('2d');
            
            // 计算画布尺寸
            // 旋转后的图像可能需要更大的画布来容纳
            const canvasSize = Math.sqrt(width * width + height * height);
            
            // 设置画布尺寸
            canvas.width = canvasSize;
            canvas.height = canvasSize;
            
            // 创建一个新的Image对象
            const img = canvas.createImage();
            
            // 图片加载事件
            img.onload = () => {
              // 清空画布
              ctx.clearRect(0, 0, canvasSize, canvasSize);
              
              // 保存当前状态
              ctx.save();
              
              // 将原点移到画布中心
              ctx.translate(canvasSize / 2, canvasSize / 2);
              
              // 执行旋转，注意角度需要转换为弧度
              ctx.rotate((angle * Math.PI) / 180);
              
              // 将图片绘制到画布中心，考虑偏移
              ctx.drawImage(img, -width / 2, -height / 2, width, height);
              
              // 恢复状态
              ctx.restore();
              
              // 导出图片
              wx.canvasToTempFilePath({
                canvas: canvas,
                success: (res) => {
                  console.log('[旋转] 导出图片成功:', res.tempFilePath);
                  if (typeof callback === 'function') {
                    callback(res.tempFilePath);
                  }
                },
                fail: (err) => {
                  console.error('[旋转] 导出图片失败:', err);
                  if (typeof callback === 'function') {
                    callback(imagePath); // 失败时返回原图路径
                  }
                }
              });
            };
            
            // 图片加载错误事件
            img.onerror = (err) => {
              console.error('[旋转] 图片加载失败:', err);
              if (typeof callback === 'function') {
                callback(imagePath); // 失败时返回原图路径
              }
            };
            
            // 设置图片源
            img.src = path;
          });
      },
      fail: (err) => {
        console.error('[旋转] 获取图片信息失败:', err);
        if (typeof callback === 'function') {
          callback(imagePath); // 失败时返回原图路径
        }
      }
    });
  },
  
  // 提交衣物数据
  submitClothingData: function(imageUrl) {
    // 获取符合JWT格式的token
    const compatibleToken = this.generateCompatibleToken();
    
    // 获取当前选中的标签列表
    const selectedTagKeys = Object.keys(this.data.selectedTags);
    const allTags = [...this.data.tags, ...this.data.customTags];
    const selectedTagNames = allTags
      .filter(tag => selectedTagKeys.includes(tag.value))
      .map(tag => tag.name);
    
    // 使用传入的imageUrl参数，这已经是经过处理选择的正确URL
    // 不再需要在这里进行URL选择逻辑
    const finalImageUrl = imageUrl;
    console.log('[保存] 最终使用的图片URL:', finalImageUrl);
    console.log('[保存] 抠图状态:', this.data.segmentEnabled ? '开启' : '关闭');
    
    // 确定衣橱ID，如果未选择，则使用默认衣橱ID
    let finalWardrobeId = this.data.wardrobeId;
    if (!finalWardrobeId && this.data.defaultWardrobeId) {
      finalWardrobeId = this.data.defaultWardrobeId;
      console.log('[保存] 未选择衣橱，使用默认衣橱ID:', finalWardrobeId);
    }
    
    // 构建请求数据
    const requestData = {
      name: this.data.name || '未命名衣物',
      category: this.data.selectedCategory,
      image_url: finalImageUrl,
      tags: selectedTagNames.join(','),
      segment_enabled: this.data.segmentEnabled, // 传递抠图开关状态到后端
      description: JSON.stringify({
        color: this.data.color || '',
        brand: this.data.brand || '',
        price: this.data.price || '',
        notes: this.data.notes || '' // 添加备注
      }),
      wardrobe_id: finalWardrobeId
    };
    
    console.log('提交衣物数据:', requestData);
    
    // 发送添加衣物请求
    wx.request({
      url: `${app.globalData.apiBaseUrl}/add_clothing.php`,
      method: 'POST',
      header: {
        'Authorization': compatibleToken,
        'Content-Type': 'application/json'
      },
      data: requestData,
      success: (res) => {
        wx.hideLoading();
        console.log('[保存] 提交成功，响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          // 设置全局刷新标记，返回列表页时刷新数据
          app.globalData.needRefreshClothes = true;
          
          wx.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 2000,
            success: () => {
              // 延迟返回上一页，让用户能看到成功提示
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            }
          });
        } else {
          console.error('[保存] 提交失败:', res.data.msg || '未知错误');
          wx.showToast({
            title: res.data.msg || '保存失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('[保存] 请求失败:', err);
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      }
    });
  },
  
  // Base64编码辅助函数
  btoa: function(str) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
    let out = '';
    for (let i = 0; i < str.length; i += 3) {
      const c1 = str.charCodeAt(i);
      const c2 = i + 1 < str.length ? str.charCodeAt(i + 1) : 0;
      const c3 = i + 2 < str.length ? str.charCodeAt(i + 2) : 0;
      
      const e1 = c1 >> 2;
      const e2 = ((c1 & 3) << 4) | (c2 >> 4);
      const e3 = ((c2 & 15) << 2) | (c3 >> 6);
      const e4 = c3 & 63;
      
      out += chars.charAt(e1) + chars.charAt(e2) +
             (i + 1 < str.length ? chars.charAt(e3) : '=') +
             (i + 2 < str.length ? chars.charAt(e4) : '=');
    }
    return out;
  },
  
  // 处理图片函数：应用旋转和缩放
  processImage: function(imagePath, angle, scale, callback) {
    console.log(`[图片处理] 开始处理图片，旋转: ${angle}°, 缩放: ${scale}x`);
    
    // 获取图片信息
    wx.getImageInfo({
      src: imagePath,
      success: (res) => {
        console.log('[图片处理] 获取图片信息成功:', res);
        const { width, height, path } = res;
        
        // 使用Canvas 2D API
        const query = wx.createSelectorQuery();
        query.select('#rotateCanvas')
          .fields({ node: true, size: true })
          .exec((canvasRes) => {
            if (!canvasRes || !canvasRes[0] || !canvasRes[0].node) {
              console.error('[图片处理] 获取canvas节点失败');
              if (typeof callback === 'function') {
                callback(imagePath); // 失败时返回原图路径
              }
              return;
            }
            
            const canvas = canvasRes[0].node;
            const ctx = canvas.getContext('2d');
            
            // 计算画布尺寸，考虑旋转和缩放后的尺寸
            const canvasSize = Math.sqrt(width * width + height * height);
            
            // 设置画布尺寸，确保足够大
            canvas.width = canvasSize;
            canvas.height = canvasSize;
            
            // 创建一个新的Image对象
            const img = canvas.createImage();
            
            // 图片加载事件
            img.onload = () => {
              // 清空画布
              ctx.clearRect(0, 0, canvasSize, canvasSize);
              
              // 保存当前状态
              ctx.save();
              
              // 将原点移到画布中心
              ctx.translate(canvasSize / 2, canvasSize / 2);
              
              // 执行旋转，注意角度需要转换为弧度
              ctx.rotate((angle * Math.PI) / 180);
              
              // 应用缩放
              ctx.scale(scale, scale);
              
              // 将图片绘制到画布中心
              ctx.drawImage(img, -width / 2, -height / 2, width, height);
              
              // 恢复状态
              ctx.restore();
              
              // 导出图片
              wx.canvasToTempFilePath({
                canvas: canvas,
                success: (res) => {
                  console.log('[图片处理] 导出图片成功:', res.tempFilePath);
                  if (typeof callback === 'function') {
                    callback(res.tempFilePath);
                  }
                },
                fail: (err) => {
                  console.error('[图片处理] 导出图片失败:', err);
                  if (typeof callback === 'function') {
                    callback(imagePath); // 失败时返回原图路径
                  }
                }
              });
            };
            
            // 图片加载错误事件
            img.onerror = (err) => {
              console.error('[图片处理] 图片加载失败:', err);
              if (typeof callback === 'function') {
                callback(imagePath); // 失败时返回原图路径
              }
            };
            
            // 设置图片源
            img.src = path;
          });
      },
      fail: (err) => {
        console.error('[图片处理] 获取图片信息失败:', err);
        if (typeof callback === 'function') {
          callback(imagePath); // 失败时返回原图路径
        }
      }
    });
  },
  
  // 获取分类列表
  getClothingCategories: function() {
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothing_categories.php`,
      method: 'GET',
      header: {
        'content-type': 'application/json',
        'Authorization': `Bearer ${app.globalData.token}` // 统一使用Bearer格式
      },
      success: (res) => {
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          console.log('获取分类列表成功:', res.data);
          const categories = res.data.data || [];
          
          // 转换数据格式
          const formattedCategories = categories.map(cat => ({
            name: cat.name,
            value: cat.code,
            id: cat.id,
            is_system: cat.is_system
          }));
          
          this.setData({
            categories: formattedCategories
          });
        } else {
          console.error('获取分类列表失败:', res);
          // 如果获取失败，使用默认分类
          this.setDefaultCategories();
        }
      },
      fail: (err) => {
        console.error('获取分类列表请求失败:', err);
        // 如果请求失败，使用默认分类
        this.setDefaultCategories();
      }
    });
  },
  
  // 设置默认分类（兼容性处理）
  setDefaultCategories: function() {
    const defaultCategories = [
      { name: '上衣', value: 'tops', is_system: true },
      { name: '裤子', value: 'pants', is_system: true },
      { name: '裙子', value: 'skirts', is_system: true },
      { name: '外套', value: 'coats', is_system: true },
      { name: '鞋子', value: 'shoes', is_system: true },
      { name: '包包', value: 'bags', is_system: true },
      { name: '配饰', value: 'accessories', is_system: true }
    ];
    
    this.setData({
      categories: defaultCategories
    });
  },
  
  // 跳转到分类管理
  goToClothingCategories: function() {
    wx.navigateTo({
      url: '/pages/clothing-categories/index/index'
    });
  },
  
  // 切换抠图模块展开状态
  toggleSegmentExpand: function() {
    this.setData({
      isSegmentExpanded: !this.data.isSegmentExpanded,
      isRotationExpanded: false,
      isScaleExpanded: false
    });
  },
  
  // 切换旋转模块展开状态
  toggleRotationExpand: function() {
    this.setData({
      isSegmentExpanded: false,
      isRotationExpanded: !this.data.isRotationExpanded,
      isScaleExpanded: false
    });
  },
  
  // 切换缩放模块展开状态
  toggleScaleExpand: function() {
    this.setData({
      isSegmentExpanded: false,
      isRotationExpanded: false,
      isScaleExpanded: !this.data.isScaleExpanded
    });
  },
  
  // 图片触摸开始事件
  onImageTouchStart: function(e) {
    // 功能已禁用
  },
  
  // 图片触摸移动事件
  onImageTouchMove: function(e) {
    // 功能已禁用
  },
  
  // 图片触摸结束事件
  onImageTouchEnd: function(e) {
    // 功能已禁用
  },
  
  // 图片点击事件（防止触发上传区域的点击事件）
  onImageTap: function(e) {
    // 功能已禁用，但保留阻止事件冒泡以防止触发chooseImage
    e.stopPropagation();
  },
  
  // 重置图片位置
  resetImagePosition: function() {
    // 保持数据一致性
    this.setData({
      imageOffsetX: 0,
      imageOffsetY: 0,
      lastOffsetX: 0,
      lastOffsetY: 0
    });
  },
  
  // 保存衣物
  saveClothing: function() {
    // 检查必填项
    if (!this.data.tempImagePath) {
      wx.showToast({
        title: '请上传衣物照片',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.selectedCategory) {
      wx.showToast({
        title: '请选择衣物类别',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载中
    wx.showLoading({
      title: '保存中...',
      mask: true
    });
    
    // 检查是否需要进行图片处理（有旋转角度或缩放比例不为1）
    const needRotation = this.data.rotationAngle !== 0;
    const needScaling = this.data.scaleValue !== 1.0;
    const needImageProcessing = (needRotation || needScaling) && 
                               (this.data.segmentedImageUrl || this.data.originalImageUrl);
    
    if (needImageProcessing) {
      // 记录处理原因
      let processReason = [];
      if (needRotation) processReason.push(`旋转${this.data.rotationAngle}°`);
      if (needScaling) processReason.push(`缩放${this.data.scaleValue}x`);
      
      console.log(`[保存] 检测到图片需要处理: ${processReason.join('和')}`);
      
      // 确定要处理的图片URL
      const imageToProcess = this.data.segmentEnabled && this.data.segmentedImageUrl 
                           ? this.data.segmentedImageUrl 
                           : this.data.originalImageUrl;
      
      // 先下载远程图片，然后应用处理
      console.log(`[保存] 下载远程图片并应用处理, 图片URL: ${imageToProcess}`);
      
      wx.downloadFile({
        url: imageToProcess,
        success: (res) => {
          if (res.statusCode === 200) {
            console.log(`[保存] 图片下载成功，临时路径: ${res.tempFilePath}`);
            
            // 应用旋转和缩放处理
            this.processImage(
              res.tempFilePath, 
              this.data.rotationAngle, 
              this.data.scaleValue,
              (processedPath) => {
                console.log(`[保存] 图片处理完成，新路径: ${processedPath}`);
                
                // 上传处理后的图片
                this.uploadOriginal((imageUrl) => {
                  console.log(`[保存] 处理后图片上传成功，URL: ${imageUrl}`);
                  // 提交表单数据
                  this.submitClothingData(imageUrl);
                }, false, processedPath);
              }
            );
          } else {
            console.error(`[保存] 图片下载失败: ${res.statusCode}`);
            
            // 下载失败时使用原来的逻辑，但避免循环调用
            // 修复：直接调用submitClothingData或uploadOriginal，避免通过continueWithOriginalLogic导致循环调用
            if (this.data.originalImageUrl) {
              this.submitClothingData(this.data.originalImageUrl);
            } else {
              this.uploadOriginal((imageUrl) => {
                this.submitClothingData(imageUrl);
              });
            }
          }
        },
        fail: (err) => {
          console.error(`[保存] 图片下载错误:`, err);
          
          // 下载失败时使用原来的逻辑，但避免循环调用
          // 修复：直接调用submitClothingData或uploadOriginal，避免通过continueWithOriginalLogic导致循环调用
          if (this.data.originalImageUrl) {
            this.submitClothingData(this.data.originalImageUrl);
          } else {
            this.uploadOriginal((imageUrl) => {
              this.submitClothingData(imageUrl);
            });
          }
        }
      });
      
      return; // 中断执行，等待异步操作完成
    } else {
      // 如果不需要处理图片，使用原来的保存逻辑
      // 修复：直接处理保存逻辑，避免通过continueWithOriginalLogic导致循环调用
      
      // 严格检查：只有在抠图功能开启的情况下才使用抠图后的图片
      if (this.data.segmentEnabled && this.data.segmentedImageUrl) {
        console.log('[保存] 抠图已开启，使用抠图后的图片:', this.data.segmentedImageUrl);
        this.submitClothingData(this.data.segmentedImageUrl);
        return;
      }
      
      // 若抠图未开启或没有抠图结果，使用原图URL
      if (this.data.originalImageUrl) {
        console.log('[保存] 抠图未开启或无抠图结果，使用原图:', this.data.originalImageUrl);
        this.submitClothingData(this.data.originalImageUrl);
        return;
      }
      
      // 只有在没有任何远程URL的情况下才上传图片
      console.log('[保存] 没有远程URL，开始上传本地图片');
      this.uploadOriginal((imageUrl) => {
        // 图片上传成功后，提交表单数据
        this.submitClothingData(imageUrl);
      });
    }
  }
})