const app = getApp();

Page({
  data: {
    merchants: [], // 商家列表
    photos: [], // 照片列表
    keyword: '', // 搜索关键词
    loading: true, // 是否正在加载
    currentPage: 1, // 当前页码
    totalPages: 1, // 总页数
    pageSize: 10, // 每页显示数量
    totalMerchants: 0, // 商家总数
    showPhotoSection: false, // 是否显示照片部分
    isLoggedIn: false, // 是否已登录
    isMerchant: false, // 是否是商家角色
    selectedPhotoId: null,
    hasPhotos: false, // 是否有照片
    hasMerchants: false, // 是否有商家
    tryOnResult: null,
    showTryOnResult: false,
    freeCount: 0,      // 免费试衣次数
    paidCount: 0,       // 付费试衣次数
    showTryOnGuidePopup: false, // 是否显示试衣须知弹窗
    activeTab: 'merchants', // 当前激活的标签页：merchants-商家，photos-照片
    totalMerchants: 0, // 商家总数
    showPhotoSection: false, // 是否显示照片部分
    isMerchant: false // 是否是商家角色
  },

  onLoad: function(options) {
    this.checkLogin();
    // 检查是否需要显示试衣须知弹窗
    this.checkShouldShowTryOnGuide();
  },
  
  onShow: function() {
    console.log("试衣页面 onShow - 登录状态:", app.globalData.isLoggedIn, "体验账号:", app.globalData.useMockUser);
    
    // 如果是已登录状态或使用体验账号，都加载商家列表
    if (app.globalData.isLoggedIn || app.globalData.useMockUser) {
      this.setData({
        isLoggedIn: app.globalData.isLoggedIn
      });
      console.log("开始加载商家列表");
      this.loadMerchants();
      this.checkMerchantStatus();
    } else {
      console.log("未登录状态，不加载商家列表");
    }
  },
  
  // 检查登录状态
  checkLogin: function() {
    if (app.globalData.isLoggedIn) {
      this.setData({
        isLoggedIn: true
      });
      this.loadMerchants();
      this.checkMerchantStatus();
    } else {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          app.globalData.userInfo = res.userInfo;
          app.globalData.isLoggedIn = true;
          this.setData({
            isLoggedIn: true
          });
          this.loadMerchants();
        }
      });
    }
  },
  
  // 检查是否是商家
  checkMerchantStatus: function() {
    wx.getStorage({
      key: 'userInfo',
      success: (res) => {
        if (res.data && res.data.merchant_status === 'yes') {
          this.setData({
            isMerchant: true
          });
        }
      }
    });
  },
  
  // 加载商家列表
  loadMerchants: function(page = 1) {
    console.log("执行loadMerchants函数 - 页码:", page, "体验账号:", app.globalData.useMockUser);
    
    this.setData({
      loading: true
    });
    
    // 检查是否为体验账号或未登录状态
    if (!app.globalData.isLoggedIn || app.globalData.useMockUser) {
      console.log('使用体验账号或未登录状态，尝试无token加载商家数据');
      
      wx.request({
        url: app.globalData.apiBaseUrl + '/get_merchants.php',
        method: 'POST',
        data: {
          keyword: this.data.keyword,
          page: page,
          limit: this.data.pageSize,
          demo_mode: true  // 添加标识，表示体验模式
        },
        success: (response) => {
          console.log("体验模式获取商家响应:", response.data);
          
          if (response.data.code === 0) {
            // 对商家列表按衣物数量从高到低排序
            const sortedMerchants = response.data.data.list.sort((a, b) => {
              return (b.clothes_count || 0) - (a.clothes_count || 0);
            });
            
            this.setData({
              merchants: sortedMerchants,
              currentPage: page,
              totalPages: Math.ceil(response.data.data.total / this.data.pageSize),
              totalMerchants: response.data.data.total,
              loading: false,
              hasMerchants: sortedMerchants.length > 0
            });
          } else {
            this.setData({
              loading: false,
              merchants: []
            });
          }
        },
        fail: (error) => {
          console.error("体验模式获取商家失败:", error);
          this.setData({
            loading: false,
            merchants: []
          });
        }
      });
      return;
    }
    
    wx.getStorage({
      key: 'token',
      success: (res) => {
        wx.request({
          url: app.globalData.apiBaseUrl + '/get_merchants.php',
          method: 'POST',
          data: {
            token: res.data,
            keyword: this.data.keyword,
            page: page,
            limit: this.data.pageSize
          },
          success: (response) => {
            if (response.data.code === 0) {
              // 对商家列表按衣物数量从高到低排序
              const sortedMerchants = response.data.data.list.sort((a, b) => {
                return (b.clothes_count || 0) - (a.clothes_count || 0);
              });
              
              this.setData({
                merchants: sortedMerchants,
                currentPage: page,
                totalPages: Math.ceil(response.data.data.total / this.data.pageSize),
                totalMerchants: response.data.data.total,
                loading: false,
                hasMerchants: sortedMerchants.length > 0
              });
              
              // 如果是第一页并且没有商家，尝试加载最近的照片
              if (page === 1 && response.data.data.list.length === 0) {
                this.loadRecentPhotos();
              }
            } else {
              wx.showToast({
                title: response.data.message || '加载商家失败',
                icon: 'none'
              });
              this.setData({
                loading: false
              });
            }
          },
          fail: () => {
            wx.showToast({
              title: '网络错误，请稍后再试',
              icon: 'none'
            });
            this.setData({
              loading: false
            });
          }
        });
      },
      fail: () => {
        // 即使在token获取失败的情况下，也尝试用demo模式加载商家
        console.log('Token获取失败，尝试无token加载商家数据');
        
        wx.request({
          url: app.globalData.apiBaseUrl + '/get_merchants.php',
          method: 'POST',
          data: {
            keyword: this.data.keyword,
            page: page,
            limit: this.data.pageSize,
            demo_mode: true  // 添加标识，表示体验模式
          },
          success: (response) => {
            if (response.data.code === 0) {
              // 对商家列表按衣物数量从高到低排序
              const sortedMerchants = response.data.data.list.sort((a, b) => {
                return (b.clothes_count || 0) - (a.clothes_count || 0);
              });
              
              this.setData({
                merchants: sortedMerchants,
                currentPage: page,
                totalPages: Math.ceil(response.data.data.total / this.data.pageSize),
                totalMerchants: response.data.data.total,
                loading: false,
                hasMerchants: sortedMerchants.length > 0
              });
            } else {
              this.setData({
                loading: false,
                merchants: []
              });
            }
          },
          fail: () => {
            this.setData({
              loading: false,
              merchants: []
            });
          }
        });
      }
    });
  },
  
  // 加载最近的照片
  loadRecentPhotos: function() {
    wx.getStorage({
      key: 'token',
      success: (res) => {
        wx.request({
          url: app.globalData.apiBaseUrl + '/get_photos.php',
          method: 'POST',
          data: {
            token: res.data,
            limit: 5
          },
          success: (response) => {
            if (response.data.code === 0 && response.data.data.length > 0) {
              this.setData({
                photos: response.data.data,
                showPhotoSection: true
              });
            }
          }
        });
      }
    });
  },
  
  // 搜索商家
  searchMerchants: function() {
    this.loadMerchants(1);
  },
  
  // 关键词输入
  onKeywordInput: function(e) {
    this.setData({
      keyword: e.detail.value
    });
  },
  
  // 上一页
  prevPage: function() {
    if (this.data.currentPage > 1) {
      this.loadMerchants(this.data.currentPage - 1);
    }
  },
  
  // 下一页
  nextPage: function() {
    if (this.data.currentPage < this.data.totalPages) {
      this.loadMerchants(this.data.currentPage + 1);
    }
  },
  
  // 导航到商家衣橱
  navigateToMerchantWardrobe: function(e) {
    const merchantId = e.currentTarget.dataset.merchantId;
    wx.navigateTo({
      url: `/pages/merchant/wardrobe/index?merchant_id=${merchantId}`
    });
  },
  
  // 选择照片进行试穿
  selectPhoto: function(e) {
    const photoId = e.currentTarget.dataset.photoId;
    const photoUrl = e.currentTarget.dataset.photoUrl;
    
    if (!this.data.isMerchant) {
      // 如果不是商家，导航到正常的试穿选择页
      wx.navigateTo({
        url: `/pages/try_on/select/index?photo_id=${photoId}&photo_url=${encodeURIComponent(photoUrl)}`
      });
    }
  },
  
  // 上传照片
  uploadPhoto: function() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        
        wx.showLoading({
          title: '上传中...',
        });
        
        wx.getStorage({
          key: 'token',
          success: (tokenRes) => {
            wx.uploadFile({
              url: app.globalData.apiBaseUrl + '/upload_photo.php',
              filePath: tempFilePath,
              name: 'photo',
              formData: {
                'token': tokenRes.data,
                'type': 'other'
              },
              success: (uploadRes) => {
                wx.hideLoading();
                const result = JSON.parse(uploadRes.data);
                
                if (result.code === 0) {
                  wx.showToast({
                    title: '上传成功',
                    icon: 'success'
                  });
                  
                  // 重新加载照片
                  this.loadRecentPhotos();
                } else {
                  wx.showToast({
                    title: result.message || '上传失败',
                    icon: 'none'
                  });
                }
              },
              fail: () => {
                wx.hideLoading();
                wx.showToast({
                  title: '网络错误，请稍后再试',
                  icon: 'none'
                });
              }
            });
          },
          fail: () => {
            wx.hideLoading();
            wx.showToast({
              title: '请先登录',
              icon: 'none'
            });
          }
        });
      }
    });
  },
  
  // 检查并自动显示试衣须知
  checkAndShowTryOnGuide: function() {
    // 如果用户未登录，不显示须知
    if (!this.data.isLoggedIn) {
      return;
    }
    
    // 获取上次显示须知的日期（格式：YYYY-MM-DD）
    const lastShowDate = wx.getStorageSync('lastShowTryOnGuideDate');
    const today = this.getTodayDateString();
    
    console.log('检查试衣须知显示状态', '上次显示日期:', lastShowDate, '今天日期:', today);
    
    // 如果今天没有显示过，则显示
    if (lastShowDate !== today) {
      console.log('今日首次进入试衣页面，显示试衣须知');
      this.setData({
        showTryOnGuidePopup: true
      });
    }
  },
  
  // 获取今天的日期字符串（格式：YYYY-MM-DD）
  getTodayDateString: function() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },
  
  // 关闭试衣须知并记录显示日期
  closeTryOnGuide: function() {
    // 记录今天已经显示过须知
    const today = this.getTodayDateString();
    wx.setStorageSync('lastShowTryOnGuideDate', today);
    
    // 关闭弹窗
    this.setData({
      showTryOnGuidePopup: false
    });
  },
  
  // 显示试衣须知
  showTryOnGuide: function() {
    this.setData({
      showTryOnGuidePopup: true
    });
  },
  
  // 加载试衣次数
  loadTryOnCount: function() {
    if (!this.data.isLoggedIn) {
      return;
    }
    
    console.log("开始获取试衣次数");
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_user_try_on_count.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log("获取试衣次数成功:", res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          const data = res.data.data || {};
          
          // 更新试衣次数数据，使用正确的字段名
          this.setData({
            freeCount: data.free_try_on_count !== undefined ? data.free_try_on_count : 
                       (data.try_on_count !== undefined ? data.try_on_count : 0),
            paidCount: data.paid_try_on_count || 0
          });
          
          console.log(`试衣次数: 免费=${this.data.freeCount}, 付费=${this.data.paidCount}`);
        }
      },
      fail: (err) => {
        console.error("获取试衣次数失败:", err);
      }
    });
  },
  
  // 跳转到购买试衣次数页面
  navigateToTryOnCount: function() {
    wx.navigateTo({
      url: '/pages/purchase/try_on_count/index'
    });
  },
  
  // 跳转到登录页面
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  // 加载用户照片
  loadPhotos: function() {
    // 未登录时不加载
    if (!this.data.isLoggedIn) {
      return;
    }
    
    const that = this;
    
    wx.showLoading({
      title: '加载中...',
    });

    // 调用后端API获取照片
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_photos.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        
        console.log("获取照片数据:", res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          // 添加照片类型（根据后端返回的type字段判断）
          const photos = res.data.data.map(photo => {
            let photoType = "个人照";
            if (photo.type === 'full') {
              photoType = "全身照";
            } else if (photo.type === 'half') {
              photoType = "半身照";
            }
            
            return {
              ...photo,
              photoType: photoType
            };
          });
          
          this.setData({
            photos: photos,
            hasPhotos: photos.length > 0,
            loading: false
          });
        } else {
          this.setData({
            photos: [],
            hasPhotos: false,
            loading: false
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        
        console.error("获取照片失败:", err);
        this.setData({
          photos: [],
          hasPhotos: false,
          loading: false
        });
        
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 图片加载完成处理宽高比
  imageLoaded: function(e) {
    const index = e.currentTarget.dataset.index;
    const id = e.currentTarget.dataset.id;
    const { width, height } = e.detail;
    
    // 如果宽度大于高度，标记为横向图片
    if (width > height) {
      const photos = this.data.photos;
      photos[index].isLandscape = true;
      this.setData({ photos });
    }
  },

  // 跳转到添加照片页面
  goToAddPhoto: function() {
    wx.navigateTo({
      url: '/pages/photos/add/add'
    });
  },
  
  // 跳转到选择衣物页面
  goToSelectClothing: function() {
    // 存储选中的照片
    const selectedPhoto = this.data.photos.find(p => p.id === this.data.selectedPhotoId);
    
    if (selectedPhoto) {
      app.globalData.selectedTryOnPhoto = selectedPhoto;
      
      wx.navigateTo({
        url: '/pages/try_on/select/index'
      });
    } else {
      wx.showToast({
        title: '请先选择照片',
        icon: 'none'
      });
    }
  },

  // 执行试穿操作
  performTryOn: function(photo, clothes) {
    console.log("执行试穿操作", photo, clothes);
    
    if (!photo || !clothes || clothes.length === 0) {
      console.error("试穿参数不完整");
      return;
    }
    
    // 清除之前的状态
    this.setData({
      showTryOnResult: false,
      tryOnResult: null
    });
    
    wx.showLoading({
      title: '正在生成...',
      mask: true
    });
    
    // 组装试穿请求数据
    const clothesIds = clothes.map(item => item.id);
    
    // 发起试穿请求
    wx.request({
      url: `${app.globalData.apiBaseUrl}/try_on.php`,
      method: 'POST',
      header: {
        'content-type': 'application/json',
        'Authorization': app.globalData.token
      },
      data: {
        photo_id: photo.id,
        clothes_ids: clothesIds
      },
      success: (res) => {
        wx.hideLoading();
        
        console.log("试穿API响应:", res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          // 试穿成功，显示结果
          this.setData({
            showTryOnResult: true,
            tryOnResult: res.data.data
          });
          
          // 刷新试衣次数
          this.loadTryOnCount();
          
          // 清除缓存的选择
          app.globalData.selectedTryOnPhoto = null;
          app.globalData.selectedClothes = [];
        } else {
          // 试穿失败，显示错误
          let errorMsg = res.data.msg || '试穿失败，请稍后重试';
          
          // 处理次数限制错误
          if (res.data.limit_exceeded) {
            errorMsg = '试衣次数已用完，请购买更多次数';
          }
          
          wx.showModal({
            title: '试穿失败',
            content: errorMsg,
            showCancel: true,
            cancelText: '返回',
            confirmText: '购买次数',
            success: (result) => {
              if (result.confirm) {
                // 跳转到购买页面
                wx.navigateTo({
                  url: '/pages/purchase/try_on_count/index'
                });
              }
            }
          });
          
          // 刷新试衣次数
          this.loadTryOnCount();
        }
      },
      fail: (err) => {
        wx.hideLoading();
        
        console.error("试穿请求失败:", err);
        
        wx.showModal({
          title: '网络错误',
          content: '请检查网络连接后重试',
          showCancel: false
        });
      }
    });
  },
  
  // 重新试穿其他衣物
  tryOtherClothes: function() {
    // 清除结果，重置状态
    this.setData({
      showTryOnResult: false,
      tryOnResult: null,
      selectedPhotoId: null
    });
    
    // 清除缓存的选择
    app.globalData.selectedTryOnPhoto = null;
    app.globalData.selectedClothes = [];
  },

  // 保存试穿结果图片到相册
  saveImage: function() {
    if (!this.data.tryOnResult || !this.data.tryOnResult.result_image_url) {
      return;
    }
    
    wx.showLoading({
      title: '正在保存...',
      mask: true
    });
    
    wx.downloadFile({
      url: this.data.tryOnResult.result_image_url,
      success: (res) => {
        if (res.statusCode === 200) {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
            success: () => {
              wx.hideLoading();
            wx.showToast({
                title: '保存成功',
              icon: 'success'
            });
          },
          fail: (err) => {
              wx.hideLoading();
              console.error("保存图片失败:", err);
            
            if (err.errMsg.indexOf('auth deny') >= 0) {
              wx.showModal({
                  title: '权限提示',
                  content: '需要您授权保存图片到相册',
                  showCancel: true,
                  confirmText: '去授权',
                success: (res) => {
                  if (res.confirm) {
                      wx.openSetting({
                        success: () => {
                          // 用户可能授权也可能未授权
                        }
                      });
                  }
                }
              });
            } else {
              wx.showToast({
                title: '保存失败',
                icon: 'none'
              });
            }
          }
        });
        } else {
          wx.hideLoading();
          wx.showToast({
            title: '下载图片失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error("下载图片失败:", err);
        
        wx.showToast({
          title: '下载图片失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 分享
  onShareAppMessage: function() {
    if (this.data.showTryOnResult && this.data.tryOnResult) {
      // 分享试穿结果
      return {
        title: '我试穿了这套衣服，快来看看效果吧！',
        path: `/pages/try_on/result/share?id=${this.data.tryOnResult.id}`,
        imageUrl: this.data.tryOnResult.result_image_url
      };
    }
    
    // 默认分享
    return {
      title: '时尚衣橱，一键试穿你的衣橱',
      path: '/pages/index/index'
    };
  },

  // 从衣橱选择衣物
  goToSelectFromWardrobe: function() {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 标记当前是从衣橱选择模式
    app.globalData.selectFromWardrobeMode = true;
    
    // 清除之前可能存在的选择
    app.globalData.selectedClothes = [];
    app.globalData.selectedTryOnPhoto = null;
    
    // 跳转到衣物选择页面
    wx.navigateTo({
      url: '/pages/try_on/select_from_wardrobe/index'
    });
  },

  // 检查是否需要显示试衣须知弹窗（每个用户每天仅显示一次）
  checkShouldShowTryOnGuide: function() {
    // 获取上次显示弹窗的日期
    const lastShowDate = wx.getStorageSync('lastShowTryOnGuideDate');
    const today = this.getTodayDateString();
    
    console.log("检查试衣须知弹窗 - 上次显示日期:", lastShowDate, "今天:", today);
    
    // 如果没有记录或者不是今天，则显示弹窗
    if (!lastShowDate || lastShowDate !== today) {
      console.log("需要显示试衣须知弹窗");
    this.setData({
        showTryOnGuidePopup: true
    });
    } else {
      console.log("今天已经显示过试衣须知弹窗，不再显示");
    }
  }
}); 