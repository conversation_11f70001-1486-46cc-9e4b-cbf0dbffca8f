<?php
/**
 * 修复圈子成员状态问题
 * 检查并修复可能的成员状态问题
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

// 获取操作类型
$action = isset($_GET['action']) ? $_GET['action'] : 'check';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "=== 圈子成员状态修复工具 ===\n";
    echo "当前用户ID: $userId\n";
    echo "操作类型: $action\n\n";
    
    // 1. 检查当前用户的圈子成员状态
    echo "1. 检查当前用户的圈子成员状态:\n";
    $stmt = $conn->prepare("
        SELECT cm.circle_id, cm.status, cm.role, cm.joined_at,
               c.name as circle_name, c.created_by
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id
        ORDER BY cm.joined_at DESC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $memberships = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($memberships)) {
        echo "用户不在任何圈子中\n";
        exit;
    }
    
    $needsFix = false;
    foreach ($memberships as $membership) {
        echo "- 圈子ID: {$membership['circle_id']} ({$membership['circle_name']})\n";
        echo "  状态: {$membership['status']}\n";
        echo "  角色: {$membership['role']}\n";
        echo "  加入时间: {$membership['joined_at']}\n";
        
        if ($membership['status'] !== 'active') {
            echo "  ⚠️ 状态异常，应该是 'active'\n";
            $needsFix = true;
        }
        echo "\n";
    }
    
    if ($action === 'fix' && $needsFix) {
        echo "2. 修复成员状态:\n";
        $stmt = $conn->prepare("
            UPDATE circle_members 
            SET status = 'active' 
            WHERE user_id = :user_id AND status != 'active'
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $fixedCount = $stmt->rowCount();
        
        echo "已修复 $fixedCount 条成员状态记录\n\n";
    }
    
    // 3. 检查圈子中的数据
    $activeCircles = [];
    foreach ($memberships as $membership) {
        if ($membership['status'] === 'active' || $action === 'fix') {
            $activeCircles[] = $membership['circle_id'];
        }
    }
    
    if (!empty($activeCircles)) {
        $circleIds = implode(',', $activeCircles);
        
        echo "3. 检查圈子中的数据:\n";
        
        // 检查衣物数据
        $stmt = $conn->prepare("
            SELECT COUNT(*) as total_count,
                   COUNT(CASE WHEN user_id = :user_id THEN 1 END) as user_count,
                   COUNT(CASE WHEN user_id != :user_id THEN 1 END) as others_count
            FROM clothes
            WHERE circle_id IN ($circleIds)
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $clothesStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "衣物数据统计:\n";
        echo "- 总数: {$clothesStats['total_count']}\n";
        echo "- 当前用户: {$clothesStats['user_count']}\n";
        echo "- 其他用户: {$clothesStats['others_count']}\n\n";
        
        // 检查穿搭数据
        $stmt = $conn->prepare("
            SELECT COUNT(*) as total_count,
                   COUNT(CASE WHEN user_id = :user_id THEN 1 END) as user_count,
                   COUNT(CASE WHEN user_id != :user_id THEN 1 END) as others_count
            FROM outfits
            WHERE circle_id IN ($circleIds)
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $outfitsStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "穿搭数据统计:\n";
        echo "- 总数: {$outfitsStats['total_count']}\n";
        echo "- 当前用户: {$outfitsStats['user_count']}\n";
        echo "- 其他用户: {$outfitsStats['others_count']}\n\n";
        
        // 4. 测试API查询
        echo "4. 测试API查询:\n";
        
        // 测试共享数据查询
        $stmt = $conn->prepare("
            SELECT COUNT(*) as count
            FROM clothes c
            WHERE c.circle_id IS NOT NULL AND c.circle_id IN (
                SELECT circle_id FROM circle_members 
                WHERE user_id = :user_id AND status = 'active'
            )
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $sharedClothesCount = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "共享衣物查询结果: {$sharedClothesCount['count']} 件\n";
        
        // 测试全部数据查询
        $stmt = $conn->prepare("
            SELECT COUNT(*) as count
            FROM clothes c
            WHERE (c.user_id = :user_id AND c.circle_id IS NULL) OR
                  (c.circle_id IS NOT NULL AND c.circle_id IN (
                      SELECT circle_id FROM circle_members 
                      WHERE user_id = :user_id AND status = 'active'
                  ))
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $allClothesCount = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "全部衣物查询结果: {$allClothesCount['count']} 件\n\n";
        
        if ($clothesStats['others_count'] > 0 && $sharedClothesCount['count'] == 0) {
            echo "⚠️ 发现问题：圈子中有其他用户的衣物，但API查询返回0\n";
            echo "可能原因：成员状态或SQL查询逻辑问题\n";
        } elseif ($sharedClothesCount['count'] > 0) {
            echo "✅ API查询正常，能够获取到共享数据\n";
        }
    }
    
    echo "\n=== 检查完成 ===\n";
    
    if ($action === 'check' && $needsFix) {
        echo "\n💡 建议：运行 ?action=fix 来修复成员状态问题\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
