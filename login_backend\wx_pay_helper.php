<?php
/**
 * 微信支付辅助类
 * 实现微信支付V3的API调用
 */
require_once 'config.php';

class WxPayHelper {
    private $merchantId; // 商户号
    private $appId; // 小程序appid
    private $serialNo; // 证书序列号
    private $apiKey; // API密钥
    private $privatekeyPath; // 私钥路径
    private $notifyUrl; // 支付结果通知地址
    
    // 套餐列表：ID => [数量, 价格(分), 描述]
    private $packages = [
        1 => [1, 100, "1次试衣"],
        2 => [10, 900, "10次试衣"],
        3 => [50, 4600, "50次试衣"],
        4 => [100, 8900, "100次试衣"],
        99 => [1, 990, "个人形象分析服务"]
    ];
    
    public function __construct() {
        // 初始化配置
        $this->merchantId = defined('WX_MCH_ID') ? WX_MCH_ID : '';
        $this->appId = defined('WX_APPID') ? WX_APPID : '';
        $this->serialNo = defined('WX_PAY_SERIAL_NO') ? WX_PAY_SERIAL_NO : '';
        $this->apiKey = defined('WX_PAY_API_KEY') ? WX_PAY_API_KEY : '';
        $this->privatekeyPath = defined('WX_PAY_PRIVATE_KEY_PATH') ? WX_PAY_PRIVATE_KEY_PATH : '';
        $this->notifyUrl = defined('WX_PAY_NOTIFY_URL') ? WX_PAY_NOTIFY_URL : '';
        
        // 验证必要配置
        if (empty($this->merchantId)) {
            error_log("警告: 商户号未配置");
        }
        
        if (empty($this->appId)) {
            error_log("警告: 小程序AppID未配置");
        }
        
        if (empty($this->serialNo)) {
            error_log("警告: 证书序列号未配置");
        }
        
        if (empty($this->apiKey)) {
            error_log("警告: API密钥未配置");
        }
        
        if (empty($this->privatekeyPath)) {
            error_log("警告: 私钥路径未配置");
        } else if (!file_exists($this->privatekeyPath)) {
            error_log("警告: 私钥文件不存在: " . $this->privatekeyPath);
        }
        
        if (empty($this->notifyUrl)) {
            error_log("警告: 回调通知URL未配置");
        }
    }
    
    /**
     * 获取套餐信息
     * 
     * @param int $packageId 套餐ID
     * @return array|null 套餐信息 [count, amount, description]
     */
    public function getPackageInfo($packageId) {
        return isset($this->packages[$packageId]) ? $this->packages[$packageId] : null;
    }
    
    /**
     * 获取所有套餐信息
     * 
     * @return array 所有套餐信息
     */
    public function getAllPackages() {
        $result = [];
        foreach ($this->packages as $id => $info) {
            $result[] = [
                'id' => $id,
                'count' => $info[0],
                'price' => $info[1] / 100, // 转换为元
                'amount' => $info[1],
                'description' => $info[2]
            ];
        }
        return $result;
    }
    
    /**
     * 生成支付订单
     * 
     * @param int $userId 用户ID
     * @param string $userNickname 用户昵称
     * @param int $packageId 套餐ID
     * @param string $userIp 用户IP
     * @return array 订单信息和支付参数
     */
    public function createOrder($userId, $userNickname, $packageId, $userIp) {
        // 获取套餐信息
        $package = $this->getPackageInfo($packageId);
        if (!$package) {
            return ['error' => true, 'msg' => '无效的套餐ID'];
        }
        
        $count = $package[0]; // 次数
        $amount = $package[1]; // 金额(分)
        $description = $package[2]; // 描述
        
        // 生成商户订单号
        $outTradeNo = $this->generateOrderNo($userId);
        
        // 构建请求数据
        $requestData = [
            'appid' => $this->appId,
            'mchid' => $this->merchantId,
            'description' => $description,
            'out_trade_no' => $outTradeNo,
            'notify_url' => $this->notifyUrl,
            'amount' => [
                'total' => $amount,
                'currency' => 'CNY'
            ],
            'payer' => [
                'openid' => $this->getUserOpenId($userId)
            ]
        ];
        
        // 调用微信支付API
        $result = $this->callWxPayApi('v3/pay/transactions/jsapi', $requestData);
        
        if (isset($result['error']) && $result['error']) {
            return $result;
        }
        
        // 获取预支付ID
        $prepayId = $result['prepay_id'];
        
        // 生成小程序支付参数
        $payParams = $this->generatePayParams($prepayId);
        
        // 返回订单信息和支付参数
        return [
            'error' => false,
            'order_id' => $outTradeNo,
            'amount' => $amount / 100, // 转换为元
            'count' => $count,
            'pay_params' => $payParams
        ];
    }
    
    /**
     * 生成商户订单号
     * 
     * @param int $userId 用户ID
     * @return string 商户订单号
     */
    private function generateOrderNo($userId) {
        // 生成格式：前缀 + 用户ID + 时间戳 + 随机数
        $prefix = 'CYYG';
        $timestamp = time();
        $random = mt_rand(1000, 9999);
        return $prefix . $userId . $timestamp . $random;
    }
    
    /**
     * 生成打赏订单号
     * 
     * @param int $userId 用户ID
     * @return string 打赏订单号
     */
    public function generateDonationOrderNo($userId) {
        // 生成格式：前缀 + 用户ID + 时间戳 + 随机数
        $prefix = 'DZSD';
        $timestamp = time();
        $random = mt_rand(1000, 9999);
        return $prefix . $userId . $timestamp . $random;
    }
    
    /**
     * 获取用户openid
     * 
     * @param int $userId 用户ID
     * @return string 用户openid
     */
    public function getUserOpenId($userId) {
        // 从数据库获取用户openid
        $db = new Database();
        $conn = $db->getConnection();
        
        $stmt = $conn->prepare("SELECT openid FROM users WHERE id = :user_id");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($user && isset($user['openid'])) {
            return $user['openid'];
        }
        
        throw new Exception("未找到用户openid");
    }
    
    /**
     * 调用微信支付API
     * 
     * @param string $path API路径
     * @param array $data 请求数据
     * @param string $method 请求方法
     * @return array 响应结果
     */
    public function callWxPayApi($path, $data, $method = 'POST') {
        // 记录API调用
        error_log("调用微信支付API: $method $path");
        
        $baseUrl = "https://api.mch.weixin.qq.com";
        $url = $baseUrl . "/" . $path;
        $timestamp = time();
        $nonce = $this->generateNonce();
        
        // 对于GET请求，如果path中包含查询参数，需要特殊处理
        $canonicalUrl = parse_url($url, PHP_URL_PATH);
        $query = parse_url($url, PHP_URL_QUERY);
        
        // 仅POST请求才序列化数据为JSON
        $jsonData = $method === 'POST' ? json_encode($data) : '';
        
        // 生成签名，根据请求方法不同构建签名消息
        if ($method === 'GET' && !empty($query)) {
            // GET请求且有查询参数时，需要将查询参数包含在规范URL中
            $canonicalUrl = $canonicalUrl . '?' . $query;
            // GET请求的body为空字符串
            $message = $method . "\n" . 
                       $canonicalUrl . "\n" .
                       $timestamp . "\n" .
                       $nonce . "\n" .
                       '' . "\n"; // GET请求为空字符串
        } else {
            // POST请求或GET请求但没有查询参数
            $message = $method . "\n" . 
                       $canonicalUrl . "\n" .
                       $timestamp . "\n" .
                       $nonce . "\n" .
                       $jsonData . "\n";
        }
        
        error_log("签名前原文: " . str_replace("\n", "\\n", $message));
        
        // 读取私钥
        if (!file_exists($this->privatekeyPath)) {
            error_log("私钥文件不存在: " . $this->privatekeyPath);
            return ['error' => true, 'msg' => '私钥文件不存在: ' . $this->privatekeyPath];
        }
        
        $privateKey = file_get_contents($this->privatekeyPath);
        if (!$privateKey) {
            error_log("无法读取私钥文件: " . $this->privatekeyPath);
            return ['error' => true, 'msg' => '无法读取私钥文件: ' . $this->privatekeyPath];
        }
        
        // 使用私钥签名
        $signature = '';
        $res = openssl_sign($message, $signature, $privateKey, OPENSSL_ALGO_SHA256);
        if (!$res) {
            error_log("签名失败: " . openssl_error_string());
            return ['error' => true, 'msg' => '签名失败: ' . openssl_error_string()];
        }
        
        // Base64编码签名
        $sign = base64_encode($signature);
        
        // 设置请求头
        $headers = [
            'Content-Type: application/json',
            'Accept: application/json',
            'User-Agent: PHP WxPay Client',
            'Authorization: WECHATPAY2-SHA256-RSA2048 ' . 
                'mchid="' . $this->merchantId . '",' .
                'serial_no="' . $this->serialNo . '",' .
                'nonce_str="' . $nonce . '",' .
                'timestamp="' . $timestamp . '",' .
                'signature="' . $sign . '"'
        ];
        
        error_log("请求头: " . json_encode($headers));
        
        // 发送请求
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        // 记录响应结果
        error_log("API响应HTTP状态码: $httpCode");
        error_log("API响应内容: $response");
        
        // 处理响应
        if ($error) {
            error_log("curl请求失败: " . $error);
            return ['error' => true, 'msg' => '请求失败: ' . $error];
        }
        
        $result = json_decode($response, true);
        
        if ($httpCode >= 200 && $httpCode < 300) {
            return $result;
        } else {
            error_log("API请求失败，HTTP状态码: " . $httpCode . "，响应消息: " . (isset($result['message']) ? $result['message'] : '未知错误'));
            return [
                'error' => true,
                'http_code' => $httpCode,
                'msg' => isset($result['message']) ? $result['message'] : '请求失败',
                'raw' => $response
            ];
        }
    }
    
    /**
     * 生成随机字符串
     * 
     * @return string 随机字符串
     */
    private function generateNonce() {
        return md5(uniqid(mt_rand(), true));
    }
    
    /**
     * 生成小程序支付参数
     * 
     * @param string $prepayId 预支付ID
     * @return array 小程序支付参数
     */
    public function generatePayParams($prepayId) {
        $timestamp = (string)time();
        $nonceStr = $this->generateNonce();
        
        // 构建签名数据
        $message = $this->appId . "\n" .
                   $timestamp . "\n" .
                   $nonceStr . "\n" .
                   'prepay_id=' . $prepayId . "\n";
        
        // 读取私钥
        $privateKey = file_get_contents($this->privatekeyPath);
        
        // 使用私钥签名
        $signature = '';
        openssl_sign($message, $signature, $privateKey, OPENSSL_ALGO_SHA256);
        
        // Base64编码签名
        $sign = base64_encode($signature);
        
        // 返回支付参数
        return [
            'timeStamp' => $timestamp,
            'nonceStr' => $nonceStr,
            'package' => 'prepay_id=' . $prepayId,
            'signType' => 'RSA',
            'paySign' => $sign
        ];
    }
    
    /**
     * 验证支付回调
     * 
     * @param array $headers 请求头
     * @param string $body 请求体
     * @return array 验证结果
     */
    public function verifyPayNotify($headers, $body) {
        // 提取请求头中的签名信息
        $signature = isset($headers['Wechatpay-Signature']) ? $headers['Wechatpay-Signature'] : '';
        $timestamp = isset($headers['Wechatpay-Timestamp']) ? $headers['Wechatpay-Timestamp'] : '';
        $nonce = isset($headers['Wechatpay-Nonce']) ? $headers['Wechatpay-Nonce'] : '';
        $serialNo = isset($headers['Wechatpay-Serial']) ? $headers['Wechatpay-Serial'] : '';
        
        if (!$signature || !$timestamp || !$nonce || !$serialNo) {
            return ['error' => true, 'msg' => '回调请求头缺少必要参数'];
        }
        
        // 构建验签名串
        $message = $timestamp . "\n" . $nonce . "\n" . $body . "\n";
        
        // 获取微信支付平台证书
        $certPem = file_get_contents(WX_PAY_CERT_PATH);
        if (!$certPem) {
            return ['error' => true, 'msg' => '无法读取微信支付平台证书'];
        }
        
        // 使用证书验证签名
        $publicKey = openssl_pkey_get_public($certPem);
        if (!$publicKey) {
            return ['error' => true, 'msg' => '无法获取公钥: ' . openssl_error_string()];
        }
        
        // 对签名进行Base64解码
        $decodedSignature = base64_decode($signature);
        
        // 验证签名
        $result = openssl_verify($message, $decodedSignature, $publicKey, OPENSSL_ALGO_SHA256);
        openssl_free_key($publicKey);
        
        if ($result !== 1) {
            return ['error' => true, 'msg' => '签名验证失败: ' . openssl_error_string()];
        }
        
        // 解析请求体
        $data = json_decode($body, true);
        if (!$data) {
            return ['error' => true, 'msg' => '无法解析回调数据'];
        }
        
        // 解密回调数据
        if (isset($data['resource']) && isset($data['resource']['ciphertext'])) {
            $ciphertext = $data['resource']['ciphertext'];
            $associatedData = isset($data['resource']['associated_data']) ? $data['resource']['associated_data'] : '';
            $nonce = isset($data['resource']['nonce']) ? $data['resource']['nonce'] : '';
            
            // 解密
            $decryptedData = $this->decryptNotifyData($ciphertext, $associatedData, $nonce);
            if (isset($decryptedData['error']) && $decryptedData['error']) {
                return $decryptedData;
            }
            
            $data['resource']['plaintext'] = $decryptedData;
        }
        
        return ['error' => false, 'data' => $data];
    }
    
    /**
     * 解密回调数据
     * 
     * @param string $ciphertext 密文
     * @param string $associatedData 附加数据
     * @param string $nonce 随机串
     * @return array 解密结果
     */
    public function decryptNotifyData($ciphertext, $associatedData, $nonce) {
        // 解密回调数据，使用AEAD_AES_256_GCM算法
        $ciphertext = base64_decode($ciphertext);
        
        // 使用API密钥作为解密密钥
        $key = $this->apiKey;
        
        // 使用OpenSSL进行AES-256-GCM解密
        $tag_length = 16; // AEAD_AES_256_GCM算法的TAG长度固定为16字节
        
        // 提取密文和TAG
        $ciphertextLength = strlen($ciphertext) - $tag_length;
        $ciphertextReal = substr($ciphertext, 0, $ciphertextLength);
        $tag = substr($ciphertext, $ciphertextLength);
        
        // 解密
        $plaintext = openssl_decrypt(
            $ciphertextReal,
            'aes-256-gcm',
            $key,
            OPENSSL_RAW_DATA,
            $nonce,
            $tag,
            $associatedData
        );
        
        if ($plaintext === false) {
            return ['error' => true, 'msg' => '解密失败: ' . openssl_error_string()];
        }
        
        // 解析JSON
        $data = json_decode($plaintext, true);
        if (!$data) {
            return ['error' => true, 'msg' => '解析解密后的数据失败'];
        }
        
        return $data;
    }
    
    /**
     * 查询订单状态
     * 
     * @param string $outTradeNo 商户订单号
     * @return array 查询结果
     */
    public function queryOrder($outTradeNo) {
        error_log("开始查询订单状态，商户订单号: $outTradeNo");
        
        if (empty($outTradeNo)) {
            error_log("订单号为空，无法查询");
            return ['error' => true, 'msg' => '订单号为空'];
        }
        
        if (empty($this->merchantId)) {
            error_log("商户号为空，无法查询订单");
            return ['error' => true, 'msg' => '商户号未配置'];
        }
        
        // 构建API路径，注意微信支付V3的路径格式
        $path = "v3/pay/transactions/out-trade-no/$outTradeNo?mchid=$this->merchantId";
        
        error_log("查询订单API路径: $path");
        
        // 调用微信支付API查询订单
        $result = $this->callWxPayApi($path, [], 'GET');
        
        // 记录查询结果
        if (isset($result['error']) && $result['error']) {
            error_log("查询订单失败: " . ($result['msg'] ?? '未知错误'));
        } else {
            error_log("查询订单成功，状态: " . ($result['trade_state'] ?? '未知'));
        }
        
        return $result;
    }
} 