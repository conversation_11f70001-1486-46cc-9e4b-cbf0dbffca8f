<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查Authorization头是否存在
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '无效或已过期的令牌']);
    exit();
}

// 初始化数据库连接
$db = new Database();
$conn = $db->getConnection();

// 获取请求参数
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10; // 默认每种推荐类型获取10条
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$offset = ($page - 1) * $limit;

try {
    // 1. 获取推荐总数统计
    $stats = [
        'weatherBased' => [
            'total' => 0,
            'recent' => 0
        ],
        'clothingBased' => [
            'total' => 0,
            'recent' => 0
        ],
        'preferenceBased' => [
            'total' => 0,
            'recent' => 0
        ],
        'imageAnalysisBased' => [
            'total' => 0,
            'recent' => 0
        ]
    ];

    // 2. 获取各类推荐统计数据
    
    // 2.1 基于天气的推荐统计
    $weatherStmt = $conn->prepare("
        SELECT COUNT(*) as total,
        SUM(CASE WHEN created_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as recent
        FROM weather_based_recommendations
    ");
    $weatherStmt->execute();
    $weatherStats = $weatherStmt->fetch(PDO::FETCH_ASSOC);
    $stats['weatherBased']['total'] = intval($weatherStats['total']);
    $stats['weatherBased']['recent'] = intval($weatherStats['recent']);
    
    // 计算总页数
    $weatherTotalPages = ceil($stats['weatherBased']['total'] / $limit);

    // 2.2 基于衣物的推荐统计
    $clothingStmt = $conn->prepare("
        SELECT COUNT(*) as total,
        SUM(CASE WHEN created_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as recent
        FROM clothing_based_recommendations
    ");
    $clothingStmt->execute();
    $clothingStats = $clothingStmt->fetch(PDO::FETCH_ASSOC);
    $stats['clothingBased']['total'] = intval($clothingStats['total']);
    $stats['clothingBased']['recent'] = intval($clothingStats['recent']);
    
    // 计算总页数
    $clothingTotalPages = ceil($stats['clothingBased']['total'] / $limit);

    // 2.3 基于喜好的推荐统计
    $preferenceStmt = $conn->prepare("
        SELECT COUNT(*) as total,
        SUM(CASE WHEN created_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as recent
        FROM preference_based_recommendations
    ");
    $preferenceStmt->execute();
    $preferenceStats = $preferenceStmt->fetch(PDO::FETCH_ASSOC);
    $stats['preferenceBased']['total'] = intval($preferenceStats['total']);
    $stats['preferenceBased']['recent'] = intval($preferenceStats['recent']);
    
    // 计算总页数
    $preferenceTotalPages = ceil($stats['preferenceBased']['total'] / $limit);

    // 2.4 基于形象分析的推荐统计
    $imageAnalysisStmt = $conn->prepare("
        SELECT COUNT(*) as total,
        SUM(CASE WHEN created_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as recent
        FROM image_analysis_based_recommendations
    ");
    $imageAnalysisStmt->execute();
    $imageAnalysisStats = $imageAnalysisStmt->fetch(PDO::FETCH_ASSOC);
    $stats['imageAnalysisBased']['total'] = intval($imageAnalysisStats['total']);
    $stats['imageAnalysisBased']['recent'] = intval($imageAnalysisStats['recent']);
    
    // 计算总页数
    $imageAnalysisTotalPages = ceil($stats['imageAnalysisBased']['total'] / $limit);

    // 3. 获取各类最新推荐数据

    // 3.1 基于天气的最新推荐
    $latestWeatherStmt = $conn->prepare("
        SELECT w.id, w.user_id, w.weather_key, w.recommendation_data, w.created_at,
               u.nickname, u.avatar_url
        FROM weather_based_recommendations w
        LEFT JOIN users u ON w.user_id = u.id
        ORDER BY w.created_at DESC
        LIMIT :offset, :limit
    ");
    $latestWeatherStmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $latestWeatherStmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $latestWeatherStmt->execute();
    $latestWeatherRecs = $latestWeatherStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理推荐数据，解析JSON
    foreach ($latestWeatherRecs as &$rec) {
        if (isset($rec['recommendation_data'])) {
            $rec['recommendation_data'] = json_decode($rec['recommendation_data'], true);
        }
    }

    // 3.2 基于衣物的最新推荐
    $latestClothingStmt = $conn->prepare("
        SELECT c.id, c.user_id, c.clothing_id, c.recommendation_data, c.created_at,
               u.nickname, u.avatar_url
        FROM clothing_based_recommendations c
        LEFT JOIN users u ON c.user_id = u.id
        ORDER BY c.created_at DESC
        LIMIT :offset, :limit
    ");
    $latestClothingStmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $latestClothingStmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $latestClothingStmt->execute();
    $latestClothingRecs = $latestClothingStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理推荐数据，解析JSON
    foreach ($latestClothingRecs as &$rec) {
        if (isset($rec['recommendation_data'])) {
            $rec['recommendation_data'] = json_decode($rec['recommendation_data'], true);
        }

    }

    // 3.3 基于喜好的最新推荐
    $latestPreferenceStmt = $conn->prepare("
        SELECT p.id, p.user_id, p.preference, p.recommendation_data, p.created_at,
               u.nickname, u.avatar_url
        FROM preference_based_recommendations p
        LEFT JOIN users u ON p.user_id = u.id
        ORDER BY p.created_at DESC
        LIMIT :offset, :limit
    ");
    $latestPreferenceStmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $latestPreferenceStmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $latestPreferenceStmt->execute();
    $latestPreferenceRecs = $latestPreferenceStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理推荐数据，解析JSON
    foreach ($latestPreferenceRecs as &$rec) {
        if (isset($rec['recommendation_data'])) {
            $rec['recommendation_data'] = json_decode($rec['recommendation_data'], true);
        }
    }

    // 3.4 基于形象分析的最新推荐
    $latestImageAnalysisStmt = $conn->prepare("
        SELECT i.id, i.user_id, i.analysis_id, i.recommendation_data, i.created_at,
               u.nickname, u.avatar_url
        FROM image_analysis_based_recommendations i
        LEFT JOIN users u ON i.user_id = u.id
        ORDER BY i.created_at DESC
        LIMIT :offset, :limit
    ");
    $latestImageAnalysisStmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $latestImageAnalysisStmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $latestImageAnalysisStmt->execute();
    $latestImageAnalysisRecs = $latestImageAnalysisStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理推荐数据，解析JSON
    foreach ($latestImageAnalysisRecs as &$rec) {
        if (isset($rec['recommendation_data'])) {
            $rec['recommendation_data'] = json_decode($rec['recommendation_data'], true);
        }
    }

    // 构建响应数据
    $response = [
        'status' => 'success',
        'data' => [
            'stats' => $stats,
            'latestRecommendations' => [
                'weatherBased' => $latestWeatherRecs,
                'clothingBased' => $latestClothingRecs,
                'preferenceBased' => $latestPreferenceRecs,
                'imageAnalysisBased' => $latestImageAnalysisRecs
            ],
            'pagination' => [
                'weatherBased' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $stats['weatherBased']['total'],
                    'totalPages' => $weatherTotalPages,
                    'hasMore' => $page < $weatherTotalPages
                ],
                'clothingBased' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $stats['clothingBased']['total'],
                    'totalPages' => $clothingTotalPages,
                    'hasMore' => $page < $clothingTotalPages
                ],
                'preferenceBased' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $stats['preferenceBased']['total'],
                    'totalPages' => $preferenceTotalPages,
                    'hasMore' => $page < $preferenceTotalPages
                ],
                'imageAnalysisBased' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $stats['imageAnalysisBased']['total'],
                    'totalPages' => $imageAnalysisTotalPages,
                    'hasMore' => $page < $imageAnalysisTotalPages
                ]
            ]
        ]
    ];

    // 设置缓存控制头，允许客户端缓存1小时
    header('Cache-Control: max-age=3600, public');
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    
    echo json_encode($response);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => '获取推荐数据失败: ' . $e->getMessage()]);
}
?> 