const app = getApp();

Page({
  data: {
    clothesList: [],
    loading: true,
    saving: false,
    originalOrder: [] // 保存原始顺序，用于比较是否有变化
  },

  onLoad() {
    this.loadClothes();
  },

  // 加载衣物数据
  loadClothes() {
    this.setData({ loading: true });
    
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothes.php`,
      method: 'GET',
      header: {
        'Authorization': token
      },
      data: {
        all_clothes: 1 // 获取所有衣物
      },
      success: (res) => {
        if (res.data && !res.data.error) {
          // 转换字段名称 - 后端使用下划线命名，前端使用驼峰命名
          const clothes = res.data.data.map(item => {
            return {
              id: item.id,
              name: item.name,
              category: item.category,
              imageUrl: item.image_url,
              tags: item.tags,
              description: item.description,
              createdAt: item.created_at,
              sortOrder: item.sort_order || item.id
            };
          });

          // 按sort_order排序
          const sortedClothes = clothes.sort((a, b) => {
            return a.sortOrder - b.sortOrder;
          });

          this.setData({
            clothesList: sortedClothes,
            originalOrder: sortedClothes.map(item => item.id),
            loading: false
          });
        } else {
          wx.showToast({
            title: res.data?.message || '加载失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  // 向上移动
  moveUp(e) {
    const index = e.currentTarget.dataset.index;
    if (index === 0) return;
    
    const clothesList = [...this.data.clothesList];
    const item = clothesList[index];
    clothesList.splice(index, 1);
    clothesList.splice(index - 1, 0, item);
    
    this.setData({ clothesList });
  },

  // 向下移动
  moveDown(e) {
    const index = e.currentTarget.dataset.index;
    if (index === this.data.clothesList.length - 1) return;
    
    const clothesList = [...this.data.clothesList];
    const item = clothesList[index];
    clothesList.splice(index, 1);
    clothesList.splice(index + 1, 0, item);
    
    this.setData({ clothesList });
  },

  // 开始拖拽
  startDrag(e) {
    // 可以在这里添加拖拽开始的视觉反馈
    console.log('开始拖拽');
  },

  // 结束拖拽
  endDrag(e) {
    // 可以在这里添加拖拽结束的处理逻辑
    console.log('结束拖拽');
  },

  // 保存排序
  saveSortOrder() {
    const currentOrder = this.data.clothesList.map(item => item.id);
    
    // 检查是否有变化
    if (JSON.stringify(currentOrder) === JSON.stringify(this.data.originalOrder)) {
      wx.showToast({
        title: '排序未发生变化',
        icon: 'none'
      });
      return;
    }

    this.setData({ saving: true });
    
    const token = wx.getStorageSync('token');
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/update_clothes_sort_order.php`,
      method: 'POST',
      header: {
        'Authorization': token,
        'Content-Type': 'application/json'
      },
      data: {
        clothes_order: currentOrder
      },
      success: (res) => {
        if (res.data && !res.data.error) {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          });
          
          // 更新原始顺序
          this.setData({
            originalOrder: currentOrder,
            saving: false
          });
          
          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.data?.message || '保存失败',
            icon: 'none'
          });
          this.setData({ saving: false });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({ saving: false });
      }
    });
  }
});
