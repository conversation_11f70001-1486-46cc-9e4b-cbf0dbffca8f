-- MySQL dump 10.13  Distrib 5.6.50, for Linux (x86_64)
--
-- Host: localhost    Database: cyyg
-- ------------------------------------------------------
-- Server version	5.6.50-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `admin_users`
--

DROP TABLE IF EXISTS `admin_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `real_name` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `announcements`
--

DROP TABLE IF EXISTS `announcements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `announcements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `start_time` datetime NOT NULL COMMENT '生效开始时间',
  `end_time` datetime NOT NULL COMMENT '生效结束时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态: 0=禁用, 1=启用',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `api_usage`
--

DROP TABLE IF EXISTS `api_usage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `api_usage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_name` varchar(50) NOT NULL COMMENT '接口名称：try_on, photo_edit等',
  `total_quota` int(11) NOT NULL COMMENT '总配额数量',
  `used_quota` int(11) NOT NULL DEFAULT '0' COMMENT '已使用数量',
  `reset_date` date DEFAULT NULL COMMENT '配额重置日期',
  `last_used` datetime DEFAULT NULL COMMENT '最后使用时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `api_name` (`api_name`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `clothes`
--

DROP TABLE IF EXISTS `clothes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clothes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `wardrobe_id` int(11) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `category` varchar(50) NOT NULL COMMENT 'tops, pants, skirts, coats, shoes, bags, accessories',
  `category_id` int(11) DEFAULT NULL COMMENT '关联的分类ID',
  `image_url` text NOT NULL,
  `tags` varchar(255) DEFAULT NULL COMMENT 'Comma-separated list of tags',
  `description` text COMMENT 'JSON object with additional properties like color, brand, price',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_category` (`category`),
  KEY `idx_wardrobe_id` (`wardrobe_id`),
  KEY `idx_category_id` (`category_id`),
  CONSTRAINT `clothes_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `clothes_wardrobe_id_fk` FOREIGN KEY (`wardrobe_id`) REFERENCES `wardrobes` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=17835 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `clothing_categories`
--

DROP TABLE IF EXISTS `clothing_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clothing_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID，NULL表示系统默认分类',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `code` varchar(50) NOT NULL COMMENT '分类代码，用于兼容现有数据',
  `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为系统默认分类',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_code` (`code`),
  KEY `idx_is_system` (`is_system`),
  CONSTRAINT `clothing_categories_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2315 DEFAULT CHARSET=utf8mb4 COMMENT='衣物分类表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `customer_service_messages`
--

DROP TABLE IF EXISTS `customer_service_messages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customer_service_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `openid` varchar(100) NOT NULL,
  `message_type` varchar(20) NOT NULL COMMENT 'text, image, link, etc.',
  `content` text NOT NULL,
  `direction` enum('in','out') NOT NULL COMMENT 'in: from user, out: from staff',
  `is_read` tinyint(1) DEFAULT '0',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id_direction` (`user_id`,`direction`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_openid` (`openid`),
  CONSTRAINT `customer_service_messages_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `media_check_results`
--

DROP TABLE IF EXISTS `media_check_results`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `media_check_results` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trace_id` varchar(100) NOT NULL COMMENT '微信安全检测唯一标识',
  `user_id` int(11) NOT NULL,
  `openid` varchar(100) NOT NULL,
  `media_url` text NOT NULL COMMENT '检测的媒体文件URL',
  `temp_file_path` varchar(255) NOT NULL COMMENT '临时文件路径',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '检测状态: pending, passed, rejected',
  `suggest` varchar(20) DEFAULT NULL COMMENT '检测建议: pass, risky, review',
  `label` int(11) DEFAULT NULL COMMENT '命中标签值',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `trace_id` (`trace_id`),
  KEY `idx_trace_id` (`trace_id`),
  KEY `idx_media_user_id` (`user_id`),
  KEY `idx_media_status` (`status`),
  CONSTRAINT `media_check_results_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `outfit_categories`
--

DROP TABLE IF EXISTS `outfit_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `outfit_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `sort_order` int(11) DEFAULT '0',
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_outfit_category_name` (`name`),
  CONSTRAINT `outfit_categories_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=364 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `outfit_recommendations`
--

DROP TABLE IF EXISTS `outfit_recommendations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `outfit_recommendations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `recommendation_data` text NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_created` (`user_id`,`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=604 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `outfits`
--

DROP TABLE IF EXISTS `outfits`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `outfits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `category` varchar(50) NOT NULL DEFAULT '其他' COMMENT '穿搭分类：正式、休闲、聚会、工作、度假、运动、居家、其他',
  `thumbnail_url` text COMMENT '缩略图URL',
  `outfit_data` longtext NOT NULL COMMENT '以JSON格式存储的穿搭布局数据',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `is_public` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否公开: 0=不公开, 1=公开',
  PRIMARY KEY (`id`),
  KEY `idx_outfit_user_id` (`user_id`),
  KEY `idx_outfit_created_at` (`created_at`),
  KEY `idx_outfit_category` (`category`),
  KEY `idx_outfit_category_id` (`category_id`),
  KEY `idx_outfit_is_public` (`is_public`),
  CONSTRAINT `fk_outfit_category` FOREIGN KEY (`category_id`) REFERENCES `outfit_categories` (`id`) ON DELETE SET NULL,
  CONSTRAINT `outfits_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1067 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `photos`
--

DROP TABLE IF EXISTS `photos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `photos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `image_url` varchar(255) NOT NULL,
  `type` enum('full','half','other') DEFAULT 'other',
  `description` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `photos_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=564 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `recharge_records`
--

DROP TABLE IF EXISTS `recharge_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `recharge_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `nickname` varchar(255) DEFAULT NULL COMMENT '用户昵称',
  `order_id` varchar(64) NOT NULL COMMENT '订单号',
  `package_id` int(11) NOT NULL COMMENT '套餐ID',
  `amount` decimal(10,2) NOT NULL COMMENT '充值金额',
  `count` int(11) NOT NULL COMMENT '充值次数',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态：pending-待支付，success-支付成功，failed-支付失败',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '微信支付交易号',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `paid_at` datetime DEFAULT NULL COMMENT '支付时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `order_id` (`order_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT='充值记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `recommended_outfit_categories`
--

DROP TABLE IF EXISTS `recommended_outfit_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `recommended_outfit_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(4) DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `recommended_outfit_items`
--

DROP TABLE IF EXISTS `recommended_outfit_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `recommended_outfit_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `outfit_id` int(11) NOT NULL COMMENT '关联的推荐穿搭ID',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `image_url` text NOT NULL COMMENT '商品图片URL',
  `price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `purchase_url` text NOT NULL COMMENT '购买链接(淘宝客链接)',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序，值越小越靠前',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_recommended_outfit_item_outfit` (`outfit_id`),
  KEY `idx_recommended_outfit_item_sort` (`sort_order`),
  CONSTRAINT `fk_recommended_outfit_item_outfit` FOREIGN KEY (`outfit_id`) REFERENCES `recommended_outfits` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `recommended_outfit_stats`
--

DROP TABLE IF EXISTS `recommended_outfit_stats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `recommended_outfit_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `outfit_id` int(11) NOT NULL COMMENT '关联的推荐穿搭ID',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '查看次数',
  `copy_link_count` int(11) NOT NULL DEFAULT '0' COMMENT '复制链接次数',
  `last_viewed_at` datetime DEFAULT NULL COMMENT '最后查看时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `outfit_id` (`outfit_id`),
  CONSTRAINT `fk_recommended_outfit_stats_outfit` FOREIGN KEY (`outfit_id`) REFERENCES `recommended_outfits` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `recommended_outfits`
--

DROP TABLE IF EXISTS `recommended_outfits`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `recommended_outfits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '穿搭名称',
  `image_url` text NOT NULL COMMENT '穿搭图片URL',
  `description` text COMMENT '穿搭描述',
  `recommendation_reason` text COMMENT '穿搭推荐理由',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态: 1=启用, 0=禁用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序，值越小越靠前',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_recommended_outfit_category` (`category_id`),
  KEY `idx_recommended_outfit_status` (`status`),
  KEY `idx_recommended_outfit_sort` (`sort_order`),
  CONSTRAINT `fk_recommended_outfit_category` FOREIGN KEY (`category_id`) REFERENCES `outfit_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `try_on_history`
--

DROP TABLE IF EXISTS `try_on_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `try_on_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `photo_id` int(11) NOT NULL,
  `result_image_url` text NOT NULL,
  `clothes_ids` varchar(255) NOT NULL COMMENT 'JSON array of clothes ids',
  `task_id` varchar(100) DEFAULT NULL,
  `status` enum('success','failed','processing') DEFAULT 'success',
  `error_message` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `photo_id` (`photo_id`),
  KEY `idx_try_on_user_id` (`user_id`),
  KEY `idx_try_on_created_at` (`created_at`),
  CONSTRAINT `try_on_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `try_on_history_ibfk_2` FOREIGN KEY (`photo_id`) REFERENCES `photos` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=588 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_message_status`
--

DROP TABLE IF EXISTS `user_message_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_message_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `unread_count` int(11) NOT NULL DEFAULT '0',
  `last_update` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='跟踪用户客服消息状态';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `openid` varchar(100) NOT NULL,
  `unionid` varchar(100) DEFAULT NULL,
  `session_key` varchar(100) NOT NULL,
  `nickname` varchar(100) DEFAULT NULL,
  `avatar_url` text,
  `gender` tinyint(4) DEFAULT NULL COMMENT '0: unknown, 1: male, 2: female',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1: active, 0: disabled',
  `merchant_status` enum('yes','no') NOT NULL DEFAULT 'no' COMMENT '商家入驻状态: yes=已入驻, no=未入驻',
  `share_try_on_credits` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否共享试穿点数: 1=是, 0=否',
  `free_try_on_count` int(11) NOT NULL DEFAULT '1' COMMENT '免费试衣次数，每日自动刷新为1',
  `paid_try_on_count` int(11) NOT NULL DEFAULT '0' COMMENT '付费试衣次数',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `openid` (`openid`),
  KEY `idx_openid` (`openid`),
  KEY `idx_unionid` (`unionid`),
  KEY `idx_try_on_count` (`paid_try_on_count`),
  KEY `idx_free_try_on_count` (`free_try_on_count`),
  KEY `idx_paid_try_on_count` (`paid_try_on_count`),
  KEY `idx_merchant_status` (`merchant_status`),
  KEY `idx_share_try_on_credits` (`share_try_on_credits`)
) ENGINE=InnoDB AUTO_INCREMENT=1464 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wardrobes`
--

DROP TABLE IF EXISTS `wardrobes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wardrobes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `sort_order` int(11) DEFAULT '0',
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_wardrobe_name` (`name`),
  CONSTRAINT `wardrobes_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=648 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-08 10:07:23
