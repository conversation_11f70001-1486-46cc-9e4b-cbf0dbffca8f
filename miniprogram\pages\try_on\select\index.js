const app = getApp();

Page({
  data: {
    selectedPhoto: null,
    categories: [], // 动态生成衣物分类
    currentCategory: '', // 默认显示的分类会自动设置
    loading: true,
    allClothes: [], // 所有衣物
    currentClothes: [], // 当前类别的衣物
    selectedClothes: {}, // 已选择的衣物 {id: true}
    canTryOn: false, // 是否可以试穿
    selectedCount: 0, // 已选择的衣物数量
    showSkirtTip: false, // 是否显示裙子提示框
    wardrobeList: [], // 衣柜列表
    selectedWardrobeId: null, // 选中的衣柜ID，null表示全部
    originalAllClothes: [], // 所有衣柜的所有衣物
    isPreviewing: false, // 是否正在预览
    activePivot: 0, // 当前预览的衣物索引
    previewAnimation: null, // 预览动画实例
    
    // 进度相关
    showProgress: false, // 是否显示进度条
    progressStatus: '', // 当前进度状态：queued, uploading, processing, finishing, completed, failed
    progressPercent: 0, // 进度百分比
    progressPercentStr: '0%', // 带百分号的进度字符串
    progressMessage: '准备中...', // 进度消息
    countdownTime: 30, // 倒计时（秒）
    pollingTaskId: null, // 当前轮询的任务ID
    pollingTimer: null, // 轮询计时器
    countdownTimer: null, // 倒计时计时器
    
    // 强制状态控制变量
    currentStageStartTime: 0, // 当前阶段开始时间戳
    forceStageMinDuration: {  // 各阶段最小持续时间（毫秒）
      queued: 3000,           // 排队阶段至少3秒
      uploading: 3000,        // 上传阶段至少3秒
      processing: 3000,       // 处理阶段至少3秒
      finishing: 3000         // 完成阶段至少3秒
    },
    actualTaskStatus: '',     // 后端返回的实际任务状态
    actualTaskProgress: 0,    // 后端返回的实际进度
    stagesSequence: ['queued', 'uploading', 'processing', 'finishing', 'completed'], // 状态顺序
    
    // API状态跟踪变量
    apiRequestSent: false,       // API请求是否已发送
    apiResponseReceived: false,  // API响应是否已接收
    apiResponseData: null,       // API响应数据
    apiTaskId: null,             // API返回的任务ID
    waitingForApiResponse: false, // 进度条已完成但正在等待API响应

    // 试衣须知弹窗
    showTryOnGuidePopup: false   // 是否显示试衣须知弹窗
  },
  
  onLoad: function() {
    // 初始化变量，标记是否已显示过裙子提示框
    this.hasShownSkirtTip = false;
    
    // 初始化激励视频广告
    // this.initRewardedVideoAd();
    
    // 获取选中的照片
    const selectedPhoto = app.globalData.selectedTryOnPhoto || null;
    
    console.log("选择衣物页面onLoad, selectedTryOnPhoto:", app.globalData.selectedTryOnPhoto);
    
    if (selectedPhoto) {
      console.log("选中的照片信息:", selectedPhoto);
      
      this.setData({
        selectedPhoto: selectedPhoto
      });
      
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: '选择试穿衣物'
      });
      
      // 先加载分类列表，再加载衣柜列表
      this.loadCategories();
    } else {
      // 如果没有选择照片，返回上一页
      console.error("未找到选中的照片信息，即将返回上一页");
      
      wx.showToast({
        title: '请先选择照片',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },
  
  // 加载所有衣物分类
  loadCategories: function() {
    wx.showLoading({
      title: '加载中...',
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothing_categories.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log("获取衣物分类响应:", res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          // 处理分类数据，转换为界面需要的格式
          const categories = res.data.data.map(item => ({
            id: item.code,  // 使用分类代码作为ID
            name: item.name // 直接使用后端返回的中文名称
          }));
          
          this.setData({
            categories: categories
          });
          
          console.log("从API获取的分类列表:", categories);
        } else {
          console.error("获取衣物分类失败:", res.data.msg || "未知错误");
          // 失败时使用generateCategories备用方法生成分类
          this.generateCategoriesFromClothes();
        }
        
        // 无论成功失败，继续加载衣柜列表
        this.loadWardrobeList();
      },
      fail: (err) => {
        console.error("获取衣物分类网络错误:", err);
        // 网络错误时使用generateCategories备用方法生成分类
        this.generateCategoriesFromClothes();
        // 继续加载衣柜列表
        this.loadWardrobeList();
      }
    });
  },
  
  // 加载衣柜列表
  loadWardrobeList: function() {
    wx.showLoading({
      title: '加载中...',
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_wardrobes.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log("获取衣柜列表响应:", res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          this.setData({
            wardrobeList: res.data.data || []
          });
          
          // 加载衣物数据
          this.loadClothingData();
        } else {
          console.error("获取衣柜列表失败:", res.data.msg || "未知错误");
          
          // 即使获取衣柜列表失败，也继续加载衣物数据
          this.loadClothingData();
          
          wx.showToast({
            title: res.data.msg || '获取衣柜列表失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error("获取衣柜列表网络错误:", err);
        
        // 即使获取衣柜列表失败，也继续加载衣物数据
        this.loadClothingData();
        
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 切换衣柜
  switchWardrobe: function(e) {
    const wardrobeId = e.currentTarget.dataset.id;
    
    // 如果点击的是当前选中的衣柜，不做任何操作
    if (this.data.selectedWardrobeId === wardrobeId) {
      return;
    }
    
    this.setData({
      selectedWardrobeId: wardrobeId
    });
    
    // 根据选中的衣柜筛选衣物
    this.filterClothingByWardrobe();
  },
  
  // 根据选中的衣柜筛选衣物
  filterClothingByWardrobe: function() {
    const { selectedWardrobeId, originalAllClothes } = this.data;
    
    if (selectedWardrobeId === null) {
      // 如果选择的是"全部衣物"，则显示所有衣物
      this.setData({
        allClothes: originalAllClothes
      }, () => {
        // 更新当前类别的衣物
        this.filterByCategory();
      });
    } else {
      // 筛选特定衣柜的衣物
      const filteredClothes = originalAllClothes.filter(item => 
        item.wardrobe_id == selectedWardrobeId
      );
      
      this.setData({
        allClothes: filteredClothes
      }, () => {
        // 更新当前类别的衣物
        this.filterByCategory();
      });
    }
  },
  
  onUnload: function() {
    // 页面卸载时清除所有定时器
    this.clearAllTimers();
  },
  
  // 清除所有定时器
  clearAllTimers: function() {
    if (this.data.pollingTimer) {
      clearInterval(this.data.pollingTimer);
    }
    
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }
    
    if (this.processingUpdateTimer) {
      clearInterval(this.processingUpdateTimer);
    }
  },
  
  // 重命名原来的生成分类函数作为备用方法
  generateCategoriesFromClothes: function() {
    const { allClothes } = this.data;
    
    // 获取所有不重复的分类
    const uniqueCategories = [...new Set(allClothes.map(item => item.category))];
    
    // 分类名称映射
    const categoryNameMap = {
      'tops': '上衣',
      'pants': '裤子',
      'skirts': '裙子',
      'coats': '外套',
      'shoes': '鞋子',
      'bags': '包包',
      'accessories': '配饰',
      'dresses': '连衣裙',
      'suits': '套装',
      'underwear': '内衣',
      'swimwear': '泳装',
      'sportswear': '运动服',
      'outerwear': '外套'
    };
    
    // 生成分类列表
    const categories = uniqueCategories.map(category => {
      return {
        id: category,
        name: categoryNameMap[category] || category // 如果没有映射名称，就使用原始分类名
      };
    });
    
    console.log("根据衣物生成的分类列表:", categories);
    
    // 更新分类列表
    this.setData({
      categories: categories
    });
  },
  
  // 修改loadClothingData函数，移除其中的generateCategories调用
  loadClothingData: function() {
    wx.showLoading({
      title: '加载中...',
    });
    
    console.log("开始获取衣物数据, URL:", `${app.globalData.apiBaseUrl}/get_clothes.php?all_clothes=1`);
    console.log("Authorization Token:", app.globalData.token ? "已设置" : "未设置");
    
    // 调用API获取衣物数据
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothes.php?all_clothes=1`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        
        console.log("获取衣物数据响应:", res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          const clothes = res.data.data;
          
          // 保存所有衣物，不进行前端过滤
          const allClothes = clothes;
          
          console.log("所有衣物:", allClothes);
          
          // 保存所有衣柜的所有衣物
          this.setData({
            originalAllClothes: allClothes,
            allClothes: allClothes,
            loading: false
          });
          
          // 应用衣柜筛选和分类筛选
          this.filterClothingByWardrobe();
          
          // 如果之前未成功获取分类，使用备用方法生成分类
          if (this.data.categories.length === 0) {
            this.generateCategoriesFromClothes();
          }
          
          this.autoSelectCategory();
        } else {
          console.error("获取衣物失败:", res.data.msg || "未知错误");
          
          this.setData({
            loading: false
          });
          
          wx.showToast({
            title: res.data.msg || '获取衣物失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error("获取衣物网络错误:", err);
        
        this.setData({
          loading: false
        });
        
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 切换衣物类别
  switchCategory: function(e) {
    const categoryId = e.currentTarget.dataset.id;
    
    // 更新当前类别
    this.setData({
      currentCategory: categoryId
    });
    
    // 过滤显示当前类别的衣物
    this.filterByCategory();
  },
  
  // 根据分类过滤衣物
  filterByCategory: function() {
    const { currentCategory, allClothes } = this.data;
    
    // 获取当前类别的衣物
    const currentCategoryClothes = allClothes.filter(
      item => item.category === currentCategory
    );
    
    console.log("当前类别的衣物:", currentCategoryClothes);
    
    this.setData({
      currentClothes: currentCategoryClothes
    });
    
    // 如果当前类别没有衣物，显示提示
    if (currentCategoryClothes.length === 0) {
      wx.showToast({
        title: '当前类别暂无衣物',
        icon: 'none'
      });
    }
  },
  
  // 选择衣物
  selectClothing: function(e) {
    const id = e.currentTarget.dataset.id;
    const category = e.currentTarget.dataset.category;
    const selectedItem = this.findClothingById(id);
    
    if (!selectedItem) return;
    
    // 复制当前已选衣物对象
    const newSelectedClothes = {...this.data.selectedClothes};
    
    // 检查是否已经选择了该衣物（再次点击表示取消选择）
    const isAlreadySelected = newSelectedClothes[id];
    
    if (isAlreadySelected) {
      // 取消选择
      delete newSelectedClothes[id];
    } else {
      // 如果选择的是裙子类别的衣物，且还没显示过提示，显示提示框
      if (category === 'skirts' && !this.hasShownSkirtTip) {
        this.setData({
          showSkirtTip: true
        });
      }
      
      // 检查同类型是否已经选择
      const hasSelectedSameCategory = Object.keys(newSelectedClothes).some(key => {
        const item = this.findClothingById(key);
        return item && item.category === category;
      });
      
      // 如果已经选择了同类型的衣物，则替换
      if (hasSelectedSameCategory) {
        Object.keys(newSelectedClothes).forEach(key => {
          const item = this.findClothingById(key);
          if (item && item.category === category) {
            delete newSelectedClothes[key];
          }
        });
      }
      
      // 添加新选择的衣物
      newSelectedClothes[id] = true;
    }
    
    // 更新已选衣物
    this.setData({
      selectedClothes: newSelectedClothes
    });
    
    // 检查是否可以试穿
    this.checkCanTryOn();
  },
  
  // 检查是否可以试穿
  checkCanTryOn: function() {
    const selectedClothes = this.data.selectedClothes;
    const selectedCount = Object.keys(selectedClothes).length;
    
    // 至少选择了一件衣物才能试穿
    const canTryOn = selectedCount > 0;
    
    this.setData({
      canTryOn: canTryOn,
      selectedCount: selectedCount
    });
  },
  
  // 检查是否已选择指定类别的衣物
  hasSelectedCategory: function(category) {
    const selectedClothes = this.data.selectedClothes;
    
    return Object.keys(selectedClothes).some(id => {
      const item = this.findClothingById(id);
      return item && item.category === category;
    });
  },
  
  // 根据ID查找衣物
  findClothingById: function(id) {
    return this.data.originalAllClothes.find(item => item.id == id);
  },
  
  // 试穿衣物
  tryOnClothing: function() {
    if (!this.data.canTryOn) {
      wx.showToast({
        title: '请至少选择一件衣物',
        icon: 'none'
      });
      return;
    }
    
    // 获取已选衣物的ID列表
    const selectedClothesIds = Object.keys(this.data.selectedClothes);
    
    // 获取已选择的照片ID
    const photoId = this.data.selectedPhoto.id;
    
    // 重置API状态
    this.setData({
      apiRequestSent: true,
      apiResponseReceived: false,
      apiResponseData: null,
      apiTaskId: null,
      waitingForApiResponse: false
    });
    
    // 显示进度条，初始化为排队阶段
    this.setData({
      showProgress: true,
      progressStatus: 'queued',
      progressPercent: 0,
      progressPercentStr: '0%',
      progressMessage: '排队中...',
      countdownTime: 30,
      currentStageStartTime: Date.now() // 记录阶段开始时间
    });
    
    // 启动倒计时
    this.startCountdown();
    
    // 立即开始渐进式显示进度，不等待API响应
    this.startProgressAnimation();
    
    // 调用试衣API
    wx.request({
      url: `${app.globalData.apiBaseUrl}/try_on.php`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': app.globalData.token
      },
      data: {
        photo_id: photoId,
        clothes_ids: selectedClothesIds
      },
      success: (res) => {
        console.log("试衣API响应:", res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          // 更新API响应状态
          this.setData({
            apiResponseReceived: true,
            apiResponseData: res.data.data
          });
          
          // 检查是否返回了任务ID（异步模式）
          if (res.data.data.status === 'processing' && res.data.data.task_id) {
            const taskId = res.data.data.task_id;
            console.log("收到任务ID，开始轮询状态:", taskId);
            
            // 保存任务ID并开始轮询状态
            this.setData({
              apiTaskId: taskId
            });
            
            // 开始轮询任务状态
            this.startPollingTaskStatus(taskId);
          } else if (res.data.data.result_image_url) {
            console.log("API直接返回结果图片");
            
            // 检查进度显示是否已完成
            if (this.data.progressStatus === 'completed') {
              // 进度已完成，直接显示结果
              this.handleApiResultReady(res.data.data);
            }
            // 如果进度尚未完成，等待进度完成后会自动检查API结果
          }
        } else {
          // 检查是否为试衣次数限制错误
          if (res.data.limit_exceeded) {
            // 清除所有计时器
            this.clearAllTimers();
            
            // 隐藏进度条并重置所有相关状态
            this.setData({
              showProgress: false,
              apiRequestSent: false,
              apiResponseReceived: false,
              waitingForApiResponse: false,
              progressStatus: '',
              progressPercent: 0,
              progressPercentStr: '0%',
              progressMessage: '',
              countdownTime: 0
            });
            
            // 详细记录API返回的广告限制相关信息，方便调试
            console.log("试衣次数和广告限制详情:", {
              limit_exceeded: res.data.limit_exceeded,
              ad_limit_reached: res.data.ad_limit_reached,
              ad_watch_limit: res.data.ad_watch_limit,
              ad_watch_count: res.data.ad_watch_count,
              show_ad_button: res.data.show_ad_button,
              msg: res.data.msg
            });
            
            // 不再区分广告观看限制，直接显示购买选项
            wx.showModal({
              title: '每日试衣次数已达上限',
              content: '您今日的试衣次数已用完，可以购买更多次数继续使用。',
              showCancel: true,
              cancelText: '取消',
              confirmText: '购买次数',
              success: (result) => {
                if (result.confirm) {
                  // 跳转到购买页面
                  wx.navigateTo({
                    url: '/pages/purchase/try_on_count/index'
                  });
                } else {
                  // 用户点击取消
                  console.log('用户取消购买试衣次数');
                }
              }
            });
          } else {
            // 显示其他错误信息
            this.handleError(res.data.msg || '试衣失败，请重试');
          }
        }
      },
      fail: (err) => {
        console.error("调用试衣API失败:", err);
        this.handleError('网络错误，请重试');
      }
    });
  },
  
  // 开始渐进式进度动画，不依赖API响应
  startProgressAnimation: function() {
    // 从排队阶段开始，按序显示各阶段
    const animateStages = () => {
      // 如果进度条已被隐藏，立即终止动画
      if (!this.data.showProgress) {
        console.log("检测到进度条已隐藏，终止动画");
        return;
      }
      
      const currentStatus = this.data.progressStatus;
      const currentIndex = this.data.stagesSequence.indexOf(currentStatus);
      const nextStage = this.data.stagesSequence[currentIndex + 1];
      
      // 检查当前阶段是否已完成最小持续时间
      const now = Date.now();
      const elapsed = now - this.data.currentStageStartTime;
      const minDuration = this.data.forceStageMinDuration[currentStatus] || 0;
      
      console.log(`进度动画 - 当前阶段: ${currentStatus}, 已经持续: ${elapsed}ms, 最小持续: ${minDuration}ms`);
      
      if (elapsed < minDuration) {
        // 当前阶段未满足最小持续时间，增加些进度后继续等待
        this.incrementCurrentStageProgress();
        // 再次检查是否应该继续
        if (this.data.showProgress) {
          setTimeout(animateStages, 1000);
        }
        return;
      }
      
      // 如果没有下一阶段或已到最终阶段，检查API响应
      if (!nextStage || nextStage === 'completed') {
        // 设为完成状态
        this.setData({
          progressStatus: 'completed',
          progressPercent: 100,
          progressPercentStr: '100%',
          progressMessage: '完成',
          currentStageStartTime: now
        });
        
        // 检查API是否已返回结果
        if (this.data.apiResponseReceived && this.data.apiResponseData) {
          if (this.data.apiResponseData.result_image_url) {
            // API已返回图片结果，处理完成
            this.handleApiResultReady(this.data.apiResponseData);
          } else if (this.data.apiTaskId) {
            // API已返回任务ID，但尚未完成，保持在完成状态等待轮询结果
            this.setData({
              waitingForApiResponse: true,
              progressMessage: '处理中...'
            });
          }
        } else {
          // API尚未返回任何结果，保持在完成状态等待API响应
          this.setData({
            waitingForApiResponse: true,
            progressMessage: '处理中...'
          });
        }
        return;
      }
      
      // 进入下一阶段
      let progressMessage = '';
      let progressPercent = 0;
      
      switch(nextStage) {
        case 'uploading':
          progressMessage = '提交数据...';
          progressPercent = 25;
          break;
        case 'processing':
          progressMessage = '合成中...';
          progressPercent = 50;
          break;
        case 'finishing':
          progressMessage = '即将完成...';
          progressPercent = 80;
          break;
      }
      
      // 更新阶段和时间
      this.setData({
        progressStatus: nextStage,
        progressMessage: progressMessage,
        progressPercent: progressPercent,
        progressPercentStr: progressPercent + '%',
        currentStageStartTime: now
      });
      
      // 如果是处理阶段，启动进度动态更新
      if (nextStage === 'processing') {
        this.startProcessingProgressUpdate();
      }
      
      // 计划下一次阶段检查
      const nextDelay = this.data.forceStageMinDuration[nextStage];
      setTimeout(animateStages, nextDelay);
    };
    
    // 启动动画
    setTimeout(animateStages, this.data.forceStageMinDuration.queued);
  },
  
  // API结果准备好时的处理
  handleApiResultReady: function(resultData) {
    console.log("API结果准备好，处理结果:", resultData);
    
    // 清除所有计时器
    this.clearAllTimers();
    
    // 确保显示100%完成状态
    this.setData({
      progressStatus: 'completed',
      progressPercent: 100,
      progressPercentStr: '100%',
      progressMessage: '完成',
      countdownTime: 0,
      waitingForApiResponse: false
    });
    
    // 保存试衣结果到全局变量
    app.globalData.tryOnResult = resultData;
    
    // 延迟一小段时间再跳转，让用户看到100%的进度
    setTimeout(() => {
      // 隐藏进度条
      this.setData({
        showProgress: false
      });
      
      // 跳转到试衣结果页面
      wx.navigateTo({
        url: '/pages/try_on/result/index'
      });
    }, 800);
  },
  
  // 开始轮询任务状态
  startPollingTaskStatus: function(taskId) {
    // 清除之前的轮询计时器
    if (this.data.pollingTimer) {
      clearInterval(this.data.pollingTimer);
    }
    
    // 设置轮询间隔（毫秒）
    const pollingInterval = 1500; // 1.5秒查询一次
    
    // 创建新的轮询计时器
    const timer = setInterval(() => {
      this.pollTaskStatus(taskId);
    }, pollingInterval);
    
    // 保存计时器ID
    this.setData({
      pollingTimer: timer
    });
    
    // 立即执行一次轮询
    this.pollTaskStatus(taskId);
  },
  
  // 轮询任务状态
  pollTaskStatus: function(taskId) {
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_try_on_status.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      data: {
        task_id: taskId
      },
      success: (res) => {
        console.log("轮询任务状态响应:", res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          const statusData = res.data.data;
          
          // 记录后端返回的实际状态，但不直接更新UI
          this.setData({
            actualTaskStatus: statusData.status,
            actualTaskProgress: statusData.progress,
            countdownTime: statusData.time_remaining
          });
          
          // 如果任务已完成并且有结果图片，处理完成逻辑
          if (statusData.status === 'completed' && statusData.result_image_url) {
            // 如果进度条在等待API结果，立即处理
            if (this.data.waitingForApiResponse || this.data.progressStatus === 'completed') {
              this.handleApiResultReady(statusData);
            }
          } else if (statusData.status === 'failed') {
            // 如果任务失败，显示错误
            this.handleError('试衣任务处理失败，请重试');
          } else if (this.data.waitingForApiResponse) {
            // 更新等待提示，但不影响进度条显示
            this.setData({
              progressMessage: `${statusData.message || '处理中...'}（${statusData.progress || 0}%）`
            });
          }
        } else {
          console.error("轮询任务状态失败:", res.data);
        }
      },
      fail: (err) => {
        console.error("轮询任务状态网络错误:", err);
      }
    });
  },
  
  // 在当前阶段内平滑增加进度
  incrementCurrentStageProgress: function() {
    // 如果进度条已隐藏，不再增加进度
    if (!this.data.showProgress) {
      return;
    }
    
    const currentStatus = this.data.progressStatus;
    const currentPercent = this.data.progressPercent;
    
    // 根据当前阶段确定最大进度百分比
    let maxPercent = 0;
    switch(currentStatus) {
      case 'queued':
        maxPercent = 25;
        break;
      case 'uploading':
        maxPercent = 50;
        break;
      case 'processing':
        maxPercent = 80;
        break;
      case 'finishing':
        maxPercent = 95;
        break;
      case 'completed':
        maxPercent = 100;
        break;
    }
    
    // 如果未达到该阶段最大进度，小幅增加
    if (currentPercent < maxPercent) {
      // 增量逐渐变小，越接近目标值增量越小
      const remainPercent = maxPercent - currentPercent;
      let increment = Math.max(1, Math.floor(remainPercent * 0.1));
      
      // 确保增量不超过最大百分比
      const newPercent = Math.min(maxPercent, currentPercent + increment);
      
      if (newPercent !== currentPercent) {
        this.setData({
          progressPercent: newPercent,
          progressPercentStr: newPercent + '%'
        });
      }
    }
  },
  
  // 启动处理阶段的进度动态更新
  startProcessingProgressUpdate: function() {
    // 清除可能存在的之前的计时器
    if (this.processingUpdateTimer) {
      clearInterval(this.processingUpdateTimer);
    }
    
    // 设置计时器，每0.8秒更新一次进度
    this.processingUpdateTimer = setInterval(() => {
      // 如果进度条已隐藏，清除计时器并退出
      if (!this.data.showProgress) {
        clearInterval(this.processingUpdateTimer);
        return;
      }
      
      if (this.data.progressStatus === 'processing') {
        // 处理阶段进度从50升至75
        const current = this.data.progressPercent;
        if (current < 75) {
          this.setData({
            progressPercent: current + 1
          });
        } else {
          // 达到上限，清除计时器
          clearInterval(this.processingUpdateTimer);
        }
      } else {
        // 已离开处理阶段，清除计时器
        clearInterval(this.processingUpdateTimer);
      }
    }, 800);
  },
  
  // 检查某个阶段是否已完成最小持续时间
  hasCompletedMinDuration: function(stage) {
    const startTime = this.data.currentStageStartTime;
    const minDuration = this.data.forceStageMinDuration[stage] || 0;
    const now = Date.now();
    
    return (now - startTime) >= minDuration;
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },
  
  // 关闭裙子提示框
  closeSkirtTip: function() {
    this.setData({
      showSkirtTip: false
    });
    // 标记已经显示过提示框
    this.hasShownSkirtTip = true;
  },
  
  // 显示试衣须知弹窗
  showTryOnGuide: function() {
    this.setData({
      showTryOnGuidePopup: true
    });
  },

  // 隐藏试衣须知弹窗
  hideTryOnGuide: function() {
    this.setData({
      showTryOnGuidePopup: false
    });
  },
  
  // 处理错误
  handleError: function(errorMsg) {
    // 清除所有计时器
    this.clearAllTimers();
    
    // 隐藏进度条
    this.setData({
      showProgress: false,
      apiRequestSent: false,
      apiResponseReceived: false,
      waitingForApiResponse: false
    });
    
    // 显示错误提示
    wx.showToast({
      title: errorMsg,
      icon: 'none',
      duration: 3000
    });
  },
  
  // 开始倒计时
  startCountdown: function() {
    // 清除之前的倒计时计时器
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }
    
    const timer = setInterval(() => {
      const time = this.data.countdownTime;
      
      if (time <= 0) {
        // 停止计时器
        clearInterval(timer);
      } else {
        this.setData({
          countdownTime: time - 1
        });
      }
    }, 1000);
    
    this.setData({
      countdownTimer: timer
    });
  },
  
  // 获取已选衣物数组
  getSelectedClothesArray: function() {
    const selectedIds = Object.keys(this.data.selectedClothes);
    return this.data.allClothes.filter(item => selectedIds.includes(item.id.toString()));
  },
  
  // 自动选择有衣物的分类
  autoSelectCategory: function() {
    const { allClothes, categories } = this.data;
    
    // 获取有衣物的分类
    const categoriesWithClothes = categories.filter(category => {
      return allClothes.some(item => item.category === category.id);
    });
    
    console.log("有衣物的分类:", categoriesWithClothes);
    
    if (categoriesWithClothes.length > 0) {
      // 自动选择第一个有衣物的分类
      this.setData({
        currentCategory: categoriesWithClothes[0].id
      });
      
      // 过滤显示当前类别的衣物
      this.filterByCategory();
    } else if (categories.length > 0) {
      // 如果没有找到有衣物的分类，但有分类列表，则选择第一个分类
      this.setData({
        currentCategory: categories[0].id
      });
      
      // 过滤显示当前类别的衣物
      this.filterByCategory();
    } else {
      // 如果既没有有衣物的分类，也没有分类列表，则显示提示
      wx.showToast({
        title: '没有可用的衣物分类',
        icon: 'none'
      });
    }
  },

  // 初始化激励视频广告
  initRewardedVideoAd: function() {
    /* 注释掉整个广告初始化方法
    // 全局变量，使其可以在其他方法中访问
    this.rewardedVideoAd = null;
    
    if (wx.createRewardedVideoAd) {
      this.rewardedVideoAd = wx.createRewardedVideoAd({
        adUnitId: 'adunit-feb43eda47b7263e'
      });
      
      // 监听加载事件
      this.rewardedVideoAd.onLoad(() => {
        console.log('激励视频 广告加载成功');
      });
      
      // 监听错误事件
      this.rewardedVideoAd.onError((err) => {
        console.error('激励视频 广告加载失败', err);
      });
      
      // 监听关闭事件
      this.rewardedVideoAd.onClose((res) => {
        console.log('用户关闭了广告');
        
        // 清除所有计时器
        this.clearAllTimers();
        
        // 隐藏进度条并重置所有相关状态
        this.setData({
          showProgress: false,
          apiRequestSent: false,
          apiResponseReceived: false,
          waitingForApiResponse: false,
          progressStatus: '',
          progressPercent: 0,
          progressPercentStr: '0%',
          progressMessage: '',
          countdownTime: 0
        });
        
        // 如果用户完整观看了广告，则不返回上一页，让系统处理奖励流程
        if (res && res.isEnded) {
          console.log('激励视频广告完整观看');
          return;
        }
        
        // 如果用户提前关闭了广告，则返回上一页
        console.log('用户提前关闭了广告，返回上一页');
        setTimeout(() => {
          wx.navigateBack();
        }, 300);
      });
    }
    */
  }
}) 