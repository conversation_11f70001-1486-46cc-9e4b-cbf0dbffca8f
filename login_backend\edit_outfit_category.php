<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入配置和辅助函数
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// 验证用户Token
$auth = new Auth();
$token = null;

// 从请求头或查询参数中获取token
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 移除可能存在的Bearer前缀
    $token = str_replace('Bearer ', '', $token);
} elseif (isset($_GET['token'])) {
    $token = $_GET['token'];
}

if (!$token) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'No token provided']);
    exit;
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Invalid or expired token']);
    exit;
}

$user_id = $payload['sub'];

// 获取POST数据
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// 检查必要参数
if (!isset($data['id']) || empty($data['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Category ID is required']);
    exit;
}

if (!isset($data['name']) || empty(trim($data['name']))) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Category name is required']);
    exit;
}

// 准备数据
$category_id = (int)$data['id'];
$name = trim($data['name']);
$description = isset($data['description']) ? trim($data['description']) : '';
$sort_order = isset($data['sort_order']) ? (int)$data['sort_order'] : 0;

try {
    // 获取数据库连接
    $db = new Database();
    $pdo = $db->getConnection();
    
    // 检查分类是否存在且属于当前用户
    $stmt = $pdo->prepare("SELECT * FROM outfit_categories WHERE id = :id AND user_id = :user_id");
    $stmt->bindParam(':id', $category_id, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    
    $category = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$category) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'Category not found or not authorized']);
        exit;
    }
    
    // 检查是否存在同名分类（排除当前分类）
    $stmt = $pdo->prepare("SELECT id FROM outfit_categories WHERE user_id = :user_id AND name = :name AND id != :id");
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->bindParam(':name', $name, PDO::PARAM_STR);
    $stmt->bindParam(':id', $category_id, PDO::PARAM_INT);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Category with this name already exists']);
        exit;
    }
    
    // 保存分类的是否默认状态
    $is_default = $category['is_default'];
    
    // 更新分类
    $stmt = $pdo->prepare("
        UPDATE outfit_categories 
        SET name = :name, description = :description, sort_order = :sort_order, updated_at = NOW()
        WHERE id = :id AND user_id = :user_id
    ");
    $stmt->bindParam(':name', $name, PDO::PARAM_STR);
    $stmt->bindParam(':description', $description, PDO::PARAM_STR);
    $stmt->bindParam(':sort_order', $sort_order, PDO::PARAM_INT);
    $stmt->bindParam(':id', $category_id, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    
    // 获取分类的穿搭数量
    $stmt = $pdo->prepare("SELECT COUNT(*) as outfit_count FROM outfits WHERE category_id = :category_id AND user_id = :user_id");
    $stmt->bindParam(':category_id', $category_id, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $outfit_count = (int)$result['outfit_count'];
    
    // 记录日志
    error_log("User ID: $user_id updated outfit category ID: $category_id");
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => 'Outfit category updated successfully',
        'data' => [
            'id' => $category_id,
            'name' => $name,
            'description' => $description,
            'sort_order' => $sort_order,
            'is_default' => $is_default,
            'outfit_count' => $outfit_count,
            'updated_at' => date('Y-m-d H:i:s')
        ]
    ]);
    
} catch (PDOException $e) {
    error_log("Database error in edit_outfit_category.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database error', 'message' => 'An error occurred while updating outfit category']);
} catch (Exception $e) {
    error_log("General error in edit_outfit_category.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Server error', 'message' => 'An unexpected error occurred']);
} 