<!--pages/outfits/categories/edit.wxml-->
<view class="container">
  <view class="form-container">
    <view class="form-title">编辑分类</view>
    
    <!-- 分类表单 -->
    <view class="form-group">
      <view class="form-label">分类名称</view>
      <input 
        class="form-input" 
        placeholder="请输入分类名称" 
        maxlength="20" 
        bindinput="onNameInput"
        value="{{name}}"
      />
      <view class="character-count">{{nameLength || 0}}/20</view>
    </view>
    
    <view class="form-group">
      <view class="form-label">分类描述 (可选)</view>
      <textarea 
        class="form-textarea" 
        placeholder="添加描述，如场合、风格等" 
        maxlength="100" 
        bindinput="onDescriptionInput"
        value="{{description}}"
      />
      <view class="character-count">{{descriptionLength || 0}}/100</view>
    </view>
    
    <!-- 设为默认选项 -->
    <view class="form-group">
      <view class="checkbox-container">
        <checkbox checked="{{isDefault}}" bindtap="toggleDefault"></checkbox>
        <text class="checkbox-label">设为默认分类</text>
      </view>
    </view>
    
    <!-- 表单提示 -->
    <view class="form-tips" wx:if="{{isDefault}}">
      <text>设为默认分类后，创建穿搭时将默认选择此分类</text>
    </view>
  </view>
  
  <!-- 底部按钮 -->
  <view class="bottom-buttons">
    <button class="cancel-btn" bindtap="cancel">取消</button>
    <button class="submit-btn" bindtap="saveCategory" disabled="{{submitting}}">保存</button>
  </view>
</view>