const app = getApp();

Page({
  data: {
    photoId: null,
    photo: null,
    isLoading: true
  },
  
  onLoad: function (options) {
    if (options.id) {
      this.setData({
        photoId: options.id
      });
      
      wx.setNavigationBarTitle({
        title: '照片详情'
      });
      
      this.loadPhotoDetail(options.id);
    } else {
      wx.showToast({
        title: '照片ID不能为空',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },
  
  // 加载照片详情
  loadPhotoDetail: function (photoId) {
    const token = app.globalData.token;
    if (!token) {
      console.error('未登录');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      isLoading: true
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_photos.php`,
      method: 'GET',
      data: {
        id: photoId
      },
      header: {
        'Authorization': token
      },
      success: (res) => {
        console.log('获取照片详情响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data && res.data.data.length > 0) {
          this.setData({
            photo: res.data.data[0]
          });
        } else {
          wx.showToast({
            title: res.data.msg || '获取照片详情失败',
            icon: 'none'
          });
          
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      },
      fail: (err) => {
        console.error('获取照片详情请求失败:', err);
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({
          isLoading: false
        });
      }
    });
  },
  
  // 删除照片
  deletePhoto: function () {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张照片吗？删除后不可恢复。',
      confirmText: '删除',
      confirmColor: '#ff3b30',
      success: (res) => {
        if (res.confirm) {
          this.confirmDelete();
        }
      }
    });
  },
  
  // 确认删除照片
  confirmDelete: function () {
    const token = app.globalData.token;
    if (!token) {
      console.error('未登录');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '删除中...',
      mask: true
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/delete_photo.php`,
      method: 'POST',
      data: {
        id: this.data.photoId
      },
      header: {
        'Authorization': token,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('删除照片响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
          
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.data.msg || '删除失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('删除照片请求失败:', err);
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  }
})
