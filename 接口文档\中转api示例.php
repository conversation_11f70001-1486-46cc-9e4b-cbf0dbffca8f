<?php
// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method Not Allowed']);
    exit;
}

// 设置Gemini API密钥和模型
$apiKey = 'AIzaSyCkt3alFLjYSjG1nkYeM3UuXHktr3NMPKQ'; // 替换为您的实际API密钥
$model = 'gemini-1.5-flash';

// 构建Gemini API URL
$url = "https://generativelanguage.googleapis.com/v1beta/models/{$model}:generateContent?key={$apiKey}";

// 初始化请求数据
$geminiRequestData = [];
$prompt = "理解并帮我将图片中的内容按照json的格式反馈给我";

// 处理图片上传
$image_data = null;
$mime_type = null;

// 检查是否有图片上传
if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
    // 从上传的文件获取图片数据
    $image_path = $_FILES['image']['tmp_name'];
    $image_data = file_get_contents($image_path);
    $mime_type = $_FILES['image']['type'];
} elseif (isset($_POST['image_base64'])) {
    // 从POST数据中获取base64编码的图片
    $base64_data = $_POST['image_base64'];
    // 从base64字符串中提取MIME类型和实际数据
    if (preg_match('/^data:(image\/[^;]+);base64,(.+)$/', $base64_data, $matches)) {
        $mime_type = $matches[1];
        $image_data = base64_decode($matches[2]);
    } else {
        $image_data = base64_decode($base64_data);
        $mime_type = 'image/jpeg'; // 默认MIME类型
    }
}

// 如果没有图片数据，返回错误
if (!$image_data) {
    http_response_code(400);
    echo json_encode(['error' => 'No image data provided']);
    exit;
}

// 获取自定义提示词（如果有）
if (isset($_POST['prompt']) && !empty($_POST['prompt'])) {
    $prompt = $_POST['prompt'];
}

// 将图片转换为base64
$base64_image = base64_encode($image_data);

// 增强提示词以获得结构化输出
$enhanced_prompt = $prompt . " 请严格按照以下JSON格式返回结果，不要有任何额外的文本，衣物标签从推荐穿搭的角度生成5个标签：
{
  \"衣物类别\": \"上衣、裤子、裙子、外套、鞋子、包包、配饰中的一个\",
  \"衣物标签\": [\"春季、夏季、秋季、冬季中选一个\", \"休闲、通勤、派对、运动可多选\",\"标签1\", \"标签2\, \"标签3\, \"标签4\ \"标签5\"],
 
  \"衣物信息\": {
    \"衣物名称\": \"名称\",
    \"颜色\": \"颜色\"
  }
}";

// 构建多模态请求
$geminiRequestData = [
    'contents' => [
        [
            'parts' => [
                [
                    'text' => $enhanced_prompt
                ],
                [
                    'inline_data' => [
                        'mime_type' => $mime_type,
                        'data' => $base64_image
                    ]
                ]
            ]
        ]
    ],
    // 定义生成配置
    'generationConfig' => [
        'temperature' => 0.2,
        'topP' => 0.8,
        'topK' => 40
    ]
];

// 初始化cURL会话
$ch = curl_init($url);

// 设置cURL选项
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($geminiRequestData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

// 执行cURL请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);

// 关闭cURL会话
curl_close($ch);

// 检查cURL错误
if ($curlError) {
    http_response_code(500);
    echo json_encode(['error' => 'cURL Error: ' . $curlError]);
    exit;
}

// 检查HTTP状态码
if ($httpCode !== 200) {
    http_response_code($httpCode);
    echo $response; // 直接返回Gemini API的错误响应
    exit;
}

// 返回Gemini API的响应
echo $response;

// 可选：记录请求日志
// file_put_contents('gemini_log.txt', date('Y-m-d H:i:s') . ' - ' . $response . PHP_EOL, FILE_APPEND);
?> 