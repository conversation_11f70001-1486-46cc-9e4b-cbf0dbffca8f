const app = getApp();

Page({
  data: {
    outfitId: null,
    outfit: null,
    loading: true,
    error: false,
    errorMsg: '',
    showImageModal: false,
    currentImageUrl: '',
    allImageUrls: [], // 存储所有图片URL的数组
    currentImageIndex: 0, // 当前显示的图片索引
    currentSwiperIndex: 0 // 当前轮播图索引
  },

  onLoad: function (options) {
    if (options.id) {
      this.setData({
        outfitId: options.id,
        loading: true
      });
      this.fetchOutfitDetail();
    } else {
      this.setData({
        error: true,
        errorMsg: '未找到穿搭ID',
        loading: false
      });
    }
    
    // 调试信息，检查方法是否存在
    console.log('检查方法:', this.showLargeImage ? '存在' : '不存在');
  },

  onShareAppMessage: function () {
    const { outfit } = this.data;
    if (!outfit) return {};
    
    return {
      title: outfit.name || '查看这套穿搭',
      path: `/pages/recommended_outfits/detail/detail?id=${this.data.outfitId}`,
      imageUrl: outfit.image_url
    };
  },
  
  onShareTimeline: function () {
    const { outfit } = this.data;
    if (!outfit) return {};
    
    return {
      title: outfit.name || '查看这套穿搭',
      query: `id=${this.data.outfitId}`,
      imageUrl: outfit.image_url
    };
  },

  fetchOutfitDetail: function () {
    const apiUrl = app.globalData.apiBaseUrl || 'https://cyyg.alidog.cn/login_backend';
    
    // 检查ID是否有效
    if (!this.data.outfitId || this.data.outfitId === 'recommendation') {
      this.setData({
        error: true,
        errorMsg: '无效的穿搭ID',
        loading: false
      });
      return;
    }
    
    console.log('获取穿搭详情，ID:', this.data.outfitId);
    
    wx.request({
      url: `${apiUrl}/get_recommended_outfit_detail.php`,
      method: 'GET',
      data: {
        id: Number(this.data.outfitId)
      },
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        if (res.statusCode === 200 && !res.data.error) {
          this.setData({
            outfit: res.data.data,
            loading: false
          });
          // 设置导航栏标题
          wx.setNavigationBarTitle({
            title: res.data.data.name || '穿搭详情'
          });
        } else {
          this.setData({
            error: true,
            errorMsg: res.data?.msg || '获取穿搭详情失败',
            loading: false
          });
          console.error('获取推荐穿搭详情失败:', res.data);
        }
      },
      fail: (err) => {
        this.setData({
          error: true,
          errorMsg: '网络错误，请重试',
          loading: false
        });
        console.error('获取穿搭详情失败', err);
      }
    });
  },

  reload: function () {
    this.setData({
      loading: true,
      error: false
    });
    this.fetchOutfitDetail();
  },

  // 临时使用wx.previewImage作为替代方案
  previewImage: function (e) {
    console.log('previewImage被调用', e);
    const url = e.currentTarget.dataset.url;
    if (!url) {
      console.log('URL为空');
      return;
    }
    
    console.log('预览图片URL:', url);
    wx.previewImage({
      urls: [url],
      current: url
    });
  },

  // 显示大图弹框
  showLargeImage: function (e) {
    console.log('showLargeImage被调用', e);
    const url = e.currentTarget.dataset.url;
    if (!url) {
      console.log('URL为空');
      return;
    }
    
    // 收集所有图片URL
    let allUrls = [];
    if (this.data.outfit) {
      // 添加主图
      allUrls.push(this.data.outfit.image_url);
      
      // 添加所有衣物图片
      if (this.data.outfit.items && this.data.outfit.items.length > 0) {
        this.data.outfit.items.forEach(item => {
          if (item.image_url) {
            allUrls.push(item.image_url);
          }
        });
      }
    }
    
    // 找到当前点击图片的索引
    const currentIndex = allUrls.findIndex(item => item === url);
    
    console.log('设置当前图片URL:', url, '索引:', currentIndex);
    this.setData({
      currentImageUrl: url,
      showImageModal: true,
      allImageUrls: allUrls,
      currentImageIndex: currentIndex !== -1 ? currentIndex : 0
    });
  },
  
  // 隐藏大图弹框
  hideLargeImage: function () {
    console.log('hideLargeImage被调用');
    this.setData({
      showImageModal: false
    });
  },
  
  // 阻止事件冒泡
  preventDefault: function (e) {
    // 阻止事件冒泡
    return;
  },
  
  // 阻止滑动图片时的事件冒泡
  stopPropagation: function (e) {
    // 阻止事件冒泡，防止点击图片时关闭弹窗
    return;
  },
  
  // 轮播图切换事件
  swiperChange: function(e) {
    const current = e.detail.current;
    this.setData({
      currentImageIndex: current,
      currentImageUrl: this.data.allImageUrls[current]
    });
  },
  
  // 主轮播图切换事件
  handleSwiperChange: function(e) {
    const current = e.detail.current;
    this.setData({
      currentSwiperIndex: current
    });
  },
  
  // 点击指示点切换轮播图
  switchSwiper: function(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    this.setData({
      currentSwiperIndex: index
    });
    
    const swiperComponent = this.selectComponent('.outfit-swiper');
    if (swiperComponent && swiperComponent.setCurrentIndex) {
      swiperComponent.setCurrentIndex(index);
    } else {
      // 如果无法通过组件方法设置，尝试使用CSS选择器获取swiper
      const query = wx.createSelectorQuery().in(this);
      query.select('.outfit-swiper').node(function(res) {
        if (res && res.node) {
          res.node.swipeTo(index);
        }
      }).exec();
    }
  },
  
  // 保存图片到手机
  saveImageToPhone: function () {
    if (!this.data.currentImageUrl) {
      wx.showToast({
        title: '图片地址为空',
        icon: 'none'
      });
      return;
    }
    
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.writePhotosAlbum']) {
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.saveImage();
            },
            fail: () => {
              wx.showModal({
                title: '提示',
                content: '需要您授权保存图片到相册',
                confirmText: '去授权',
                success: (res) => {
                  if (res.confirm) {
                    wx.openSetting();
                  }
                }
              });
            }
          });
        } else {
          this.saveImage();
        }
      }
    });
  },
  
  // 执行保存图片
  saveImage: function () {
    wx.showLoading({
      title: '保存中...',
    });
    
    wx.downloadFile({
      url: this.data.currentImageUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.hideLoading();
              wx.showToast({
                title: '保存成功',
                icon: 'success'
              });
            },
            fail: () => {
              wx.hideLoading();
              wx.showToast({
                title: '保存失败',
                icon: 'none'
              });
            }
          });
        } else {
          wx.hideLoading();
          wx.showToast({
            title: '下载图片失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '下载图片失败',
          icon: 'none'
        });
      }
    });
  },

  copyPurchaseLink: function (e) {
    const link = e.currentTarget.dataset.link;
    const itemId = e.currentTarget.dataset.item;
    
    if (!link) {
      wx.showToast({
        title: '链接为空',
        icon: 'none'
      });
      return;
    }
    
    wx.setClipboardData({
      data: link,
      success: () => {
        wx.showToast({
          title: '已复制，请前往淘宝查看',
          icon: 'none',
          duration: 2000
        });
        
        // 不需要记录复制链接行为
        // this.recordLinkCopy(itemId);
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },
  
  recordLinkCopy: function (itemId) {
    if (!itemId || !this.data.outfitId) return;
    
    const apiUrl = app.globalData.apiBaseUrl || 'https://cyyg.alidog.cn/login_backend';
    
    wx.request({
      url: `${apiUrl}/record_outfit_link_copy.php`,
      method: 'POST',
      header: {
        'Authorization': app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: {
        outfit_id: Number(this.data.outfitId),
        item_id: Number(itemId)
      },
      fail: (err) => {
        console.error('记录链接复制失败', err);
      }
    });
  },

  previewOutfitImage: function() {
    if (this.data.outfit && this.data.outfit.image_url) {
      this.showLargeImage({
        currentTarget: {
          dataset: {
            url: this.data.outfit.image_url
          }
        }
      });
    }
  },

  openProduct: function(e) {
    const itemId = e.currentTarget.dataset.id;
    const products = this.data.outfit.products;
    
    if (!products || !itemId) return;
    
    const product = products.find(item => item.id === itemId);
    
    if (product && product.purchase_url) {
      // 不需要记录点击行为
      // this.recordLinkCopy(itemId);
      
      // 复制链接
      wx.setClipboardData({
        data: product.purchase_url,
        success: () => {
          wx.showToast({
            title: '已复制，请前往淘宝查看',
            icon: 'none',
            duration: 2000
          });
        }
      });
    } else {
      wx.showToast({
        title: '暂无购买链接',
        icon: 'none'
      });
    }
  },

  // 点击商品项导航到商品详情页
  navigateToProductDetail: function(e) {
    const item = e.currentTarget.dataset.item;
    
    if (!item) {
      wx.showToast({
        title: '商品信息不存在',
        icon: 'none'
      });
      return;
    }
    
    try {
      console.log('商品ID:', item.id, '准备获取购买链接');
      
      // 将商品信息保存到缓存中，以备他用
      const productData = {
        id: item.id,
        title: item.name,
        image_url: item.image_url,
        original_price: item.original_price || item.price,
        final_price: item.price,
        shop_title: item.brand || '',
        coupon_amount: 0,
        item_url: item.purchase_url
      };
      
      wx.setStorageSync('currentProduct', productData);
      
      // 直接使用购买链接
      if (item.purchase_url) {
        this.copyUrlToClipboard(item.purchase_url);
      } else {
        // 如果没有purchase_url，尝试通过API获取链接
        this.getTaobaoLinkByAPI(item.id);
      }
    } catch (error) {
      console.error('处理过程发生错误:', error);
      wx.showToast({
        title: '系统错误，请稍后重试',
        icon: 'none'
      });
    }
  },
  
  // 通过API获取淘宝链接
  getTaobaoLinkByAPI: function(itemId) {
    const app = getApp();
    
    wx.showLoading({
      title: '获取淘口令中...',
    });
    
    // 尝试先获取商品详细信息
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_taobao_products.php`,
      method: 'GET',
      data: {
        item_id: itemId,
        id_type: 'item_id' // 指定按商品ID查询
      },
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        console.log('获取淘宝产品数据:', res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data && res.data.data.length > 0) {
          const item = res.data.data[0];
          // 优先使用优惠券链接，其次是普通商品链接
          const validUrl = item.coupon_click_url || item.url || item.item_url;
          
          if (validUrl) {
            // 将获取到的URL转换为淘口令
            this.convertLinkToTpwd(validUrl, itemId);
          } else {
            wx.showToast({
              title: '未找到有效商品链接',
              icon: 'none',
              duration: 2000
            });
          }
        } else {
          // 尝试使用备用API
          this.getBackupLink(itemId);
        }
      },
      fail: () => {
        wx.hideLoading();
        // 尝试使用备用API
        this.getBackupLink(itemId);
      }
    });
  },
  
  // 备用获取链接方法
  getBackupLink: function(itemId) {
    const app = getApp();
    
    wx.showLoading({
      title: '重新获取链接...',
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_taobao_link.php`,
      method: 'GET',
      data: {
        item_id: itemId,
        need_tpwd: 1  // 请求淘口令
      },
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          // 优先使用淘口令，其次使用普通链接
          const link = res.data.data.tpwd || res.data.data.model || res.data.data.promotion_url;
          if (link) {
            this.copyUrlToClipboard(link, link.includes('￥') || link.includes('¥'));
          } else {
            wx.showToast({
              title: '获取商品链接失败', 
              icon: 'none',
              duration: 2000
            });
          }
        } else {
          wx.showToast({
            title: '获取商品链接失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 新增：将链接转换为淘口令
  convertLinkToTpwd: function(url, itemId) {
    const app = getApp();
    
    console.log('开始转换链接为淘口令:', url);
    
    // 直接使用专用的淘口令转换API
    wx.request({
      url: `${app.globalData.apiBaseUrl}/convert_to_tpwd.php`,
      method: 'POST',
      data: {
        url: url,
        item_id: itemId,
        text: '好物推荐'  // 淘口令文案
      },
      header: {
        'Content-Type': 'application/json',
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        console.log('淘口令转换响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          // 获取淘口令
          const tpwd = res.data.data.model;
          
          if (tpwd && (tpwd.includes('￥') || tpwd.includes('¥'))) {
            console.log('成功获取到淘口令');
            this.copyUrlToClipboard(tpwd, true);
          } else {
            console.log('未获取到淘口令，使用普通链接');
            // 备用: 使用原始链接
            this.copyUrlToClipboard(url);
          }
        } else {
          // 如果API调用失败或未返回淘口令，使用备用方法
          console.error('转换淘口令失败:', res.data);
          this.getBackupLink(itemId);
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('转换淘口令请求失败:', err);
        // 请求失败，尝试备用方法
        this.getBackupLink(itemId);
      }
    });
  },
  
  // 复制URL到剪贴板
  copyUrlToClipboard: function(url, isTpwd = false) {
    wx.setClipboardData({
      data: url,
      success: () => {
        let toastMsg = '链接已复制，请打开淘宝APP';
        
        // 如果是淘口令，使用特定的提示
        if (isTpwd || url.includes('￥') || url.includes('¥')) {
          toastMsg = '淘口令已复制，请打开淘宝APP粘贴';
        }
        
        wx.showToast({
          title: toastMsg,
          icon: 'none',
          duration: 2500
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制链接失败',
          icon: 'none'
        });
      }
    });
  },

  // 删除不存在的方法
  convertToTpwd: null,
}); 