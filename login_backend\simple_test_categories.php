<?php
// 简单的测试脚本，模拟前端调用
echo "Content-Type: application/json\n\n";

// 模拟无token的API调用
$_SERVER['REQUEST_METHOD'] = 'GET';

// 模拟没有Authorization头的情况
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    unset($_SERVER['HTTP_AUTHORIZATION']);
}

// 清空之前的输出
ob_start();

try {
    // 包含API文件
    include 'get_clothing_categories.php';
} catch (Exception $e) {
    echo json_encode(['error' => true, 'msg' => '脚本错误: ' . $e->getMessage()]);
}

$output = ob_get_clean();
echo "API响应:\n";
echo $output;
echo "\n";

// 解析JSON响应
$response = json_decode($output, true);
if ($response) {
    echo "\n解析后的响应:\n";
    echo "错误状态: " . ($response['error'] ? '是' : '否') . "\n";
    echo "消息: " . ($response['msg'] ?? '无') . "\n";
    if (isset($response['data'])) {
        echo "分类数量: " . count($response['data']) . "\n";
        foreach ($response['data'] as $cat) {
            echo "- {$cat['name']} ({$cat['code']})\n";
        }
    }
} else {
    echo "无法解析JSON响应\n";
} 