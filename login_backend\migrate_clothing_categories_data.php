<?php
/**
 * 衣物分类数据迁移脚本
 * 功能：将现有衣物的category字段值关联到clothing_categories表
 * 
 * 使用方法：
 * php migrate_clothing_categories_data.php --user_id=0    # 迁移所有用户
 * php migrate_clothing_categories_data.php --user_id=123  # 迁移指定用户
 * php migrate_clothing_categories_data.php --dry_run      # 试运行模式，不实际执行
 * php migrate_clothing_categories_data.php --check        # 检查迁移状态
 */

require_once 'config.php';
require_once 'db.php';

class ClothingCategoryMigrator {
    private $db;
    private $dryRun = false;
    private $targetUserId = null;
    
    // 分类代码映射
    private $categoryMapping = [
        'tops' => 'tops',
        'pants' => 'pants', 
        'skirts' => 'skirts',
        'coats' => 'coats',
        'shoes' => 'shoes',
        'bags' => 'bags',
        'accessories' => 'accessories'
    ];
    
    public function __construct() {
        $dbInstance = new Database();
        $this->db = $dbInstance->getConnection();
    }
    
    public function setDryRun($dryRun) {
        $this->dryRun = $dryRun;
    }
    
    public function setTargetUserId($userId) {
        $this->targetUserId = ($userId == 0) ? null : $userId;
    }
    
    /**
     * 检查迁移状态
     */
    public function checkMigrationStatus() {
        echo "=== 衣物分类迁移状态检查 ===\n\n";
        
        // 1. 检查clothing_categories表
        $sql = "SELECT COUNT(*) as count FROM clothing_categories WHERE is_system = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "系统分类数量: {$result['count']}\n";
        
        // 2. 检查clothes表结构
        $sql = "SHOW COLUMNS FROM clothes LIKE 'category_id'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $hasCategoryId = count($results) > 0;
        echo "clothes表是否有category_id字段: " . ($hasCategoryId ? "是" : "否") . "\n";
        
        if (!$hasCategoryId) {
            echo "⚠️  警告：clothes表缺少category_id字段，请先执行结构迁移脚本\n";
            return false;
        }
        
        // 3. 统计衣物数据
        $whereClause = $this->targetUserId ? "WHERE user_id = {$this->targetUserId}" : "";
        
        $sql = "SELECT 
                    COUNT(*) as total_clothes,
                    COUNT(category_id) as migrated_clothes,
                    COUNT(*) - COUNT(category_id) as pending_clothes
                FROM clothes {$whereClause}";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $userScope = $this->targetUserId ? "用户ID {$this->targetUserId}" : "所有用户";
        echo "\n{$userScope} 的衣物统计:\n";
        echo "- 总衣物数量: {$result['total_clothes']}\n";
        echo "- 已迁移数量: {$result['migrated_clothes']}\n";
        echo "- 待迁移数量: {$result['pending_clothes']}\n";
        
        // 4. 按分类统计
        $sql = "SELECT category, COUNT(*) as count 
                FROM clothes 
                {$whereClause} 
                GROUP BY category 
                ORDER BY count DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "\n分类分布统计:\n";
        foreach ($results as $row) {
            $categoryName = $row['category'] ?: '未分类';
            echo "- {$categoryName}: {$row['count']} 件\n";
        }
        
        return true;
    }
    
    /**
     * 执行迁移
     */
    public function migrate() {
        if (!$this->checkMigrationStatus()) {
            return false;
        }
        
        echo "\n=== 开始迁移 ===\n";
        echo "模式: " . ($this->dryRun ? "试运行（不会实际修改数据）" : "正式迁移") . "\n";
        echo "范围: " . ($this->targetUserId ? "用户ID {$this->targetUserId}" : "所有用户") . "\n\n";
        
        // 获取分类映射
        $categoryIdMap = $this->buildCategoryIdMap();
        if (empty($categoryIdMap)) {
            echo "❌ 错误：无法获取分类ID映射\n";
            return false;
        }
        
        echo "分类ID映射:\n";
        foreach ($categoryIdMap as $code => $id) {
            echo "- {$code} => {$id}\n";
        }
        echo "\n";
        
        // 构建查询条件
        $whereClause = "WHERE category_id IS NULL";
        $params = [];
        if ($this->targetUserId) {
            $whereClause .= " AND user_id = ?";
            $params[] = $this->targetUserId;
        }
        
        // 获取待迁移的衣物
        $sql = "SELECT id, user_id, name, category FROM clothes {$whereClause}";
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $totalCount = 0;
        $successCount = 0;
        $errorCount = 0;
        
        echo "开始处理衣物数据...\n";
        
        foreach ($results as $row) {
            $totalCount++;
            $clothingId = $row['id'];
            $category = $row['category'];
            $clothingName = $row['name'] ?: "ID:{$clothingId}";
            
            // 查找对应的分类ID
            $categoryId = $categoryIdMap[$category] ?? null;
            
            if ($categoryId) {
                if (!$this->dryRun) {
                    $updateSql = "UPDATE clothes SET category_id = ? WHERE id = ?";
                    $updateStmt = $this->db->prepare($updateSql);
                    
                    if ($updateStmt->execute([$categoryId, $clothingId])) {
                        $successCount++;
                        echo "✓ 衣物 {$clothingName} ({$category} => {$categoryId})\n";
                    } else {
                        $errorCount++;
                        echo "❌ 更新失败: {$clothingName}\n";
                    }
                } else {
                    $successCount++;
                    echo "✓ [试运行] 衣物 {$clothingName} ({$category} => {$categoryId})\n";
                }
            } else {
                $errorCount++;
                echo "⚠️  未知分类: {$clothingName} (category: {$category})\n";
            }
            
            // 每处理100条记录显示进度
            if ($totalCount % 100 == 0) {
                echo "已处理: {$totalCount} 条记录\n";
            }
        }
        
        echo "\n=== 迁移完成 ===\n";
        echo "总处理数量: {$totalCount}\n";
        echo "成功数量: {$successCount}\n";
        echo "错误数量: {$errorCount}\n";
        
        if (!$this->dryRun && $successCount > 0) {
            echo "\n验证迁移结果...\n";
            $this->checkMigrationStatus();
        }
        
        return true;
    }
    
    /**
     * 构建分类代码到ID的映射
     */
    private function buildCategoryIdMap() {
        $sql = "SELECT id, code FROM clothing_categories WHERE is_system = 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $map = [];
        foreach ($results as $row) {
            $map[$row['code']] = (int)$row['id'];
        }
        
        return $map;
    }
}

// 解析命令行参数
function parseArgs($argv) {
    $options = [
        'user_id' => 0,
        'dry_run' => false,
        'check' => false,
        'help' => false
    ];
    
    foreach ($argv as $arg) {
        if (strpos($arg, '--user_id=') === 0) {
            $options['user_id'] = (int)substr($arg, 10);
        } elseif ($arg === '--dry_run') {
            $options['dry_run'] = true;
        } elseif ($arg === '--check') {
            $options['check'] = true;
        } elseif ($arg === '--help' || $arg === '-h') {
            $options['help'] = true;
        }
    }
    
    return $options;
}

// 显示帮助信息
function showHelp() {
    echo "衣物分类数据迁移脚本\n\n";
    echo "使用方法:\n";
    echo "  php migrate_clothing_categories_data.php [选项]\n\n";
    echo "选项:\n";
    echo "  --user_id=N     指定用户ID (0=所有用户，其他数字=指定用户)\n";
    echo "  --dry_run       试运行模式，不实际修改数据\n";
    echo "  --check         检查迁移状态\n";
    echo "  --help, -h      显示此帮助信息\n\n";
    echo "示例:\n";
    echo "  php migrate_clothing_categories_data.php --check\n";
    echo "  php migrate_clothing_categories_data.php --user_id=0 --dry_run\n";
    echo "  php migrate_clothing_categories_data.php --user_id=123\n";
}

// 主程序
if (php_sapi_name() !== 'cli') {
    die("此脚本只能在命令行模式下运行\n");
}

$options = parseArgs($argv);

if ($options['help']) {
    showHelp();
    exit(0);
}

$migrator = new ClothingCategoryMigrator();

if ($options['check']) {
    $migrator->setTargetUserId($options['user_id']);
    $migrator->checkMigrationStatus();
} else {
    $migrator->setTargetUserId($options['user_id']);
    $migrator->setDryRun($options['dry_run']);
    $migrator->migrate();
} 