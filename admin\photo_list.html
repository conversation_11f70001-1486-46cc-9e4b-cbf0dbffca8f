<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>照片管理 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* 照片列表样式 */
        .action-bar {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .search-bar {
            display: flex;
            gap: 10px;
        }
        
        .search-input {
            width: 250px;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .filter-select {
            padding: 8px 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background-color: white;
            font-size: 14px;
        }
        
        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            color: white;
            font-size: 14px;
            cursor: pointer;
        }
        
        .search-btn {
            background-color: #1890ff;
        }
        
        .search-btn:hover {
            background-color: #40a9ff;
        }
        
        .add-btn {
            background-color: #52c41a;
        }
        
        .add-btn:hover {
            background-color: #73d13d;
        }
        
        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .data-table th {
            background-color: #fafafa;
            font-weight: 500;
            color: #333;
        }
        
        .data-table tr:hover {
            background-color: #f5f5f5;
        }
        
        .photo-image {
            width: 60px;
            height: 60px;
            border-radius: 4px;
            object-fit: cover;
        }
        
        .type-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            background-color: #e6f7ff;
            color: #1890ff;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 8px;
            object-fit: cover;
        }
        
        .view-btn {
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            margin-right: 5px;
            cursor: pointer;
        }
        
        .view-btn:hover {
            background-color: #40a9ff;
        }
        
        .edit-btn {
            background-color: #faad14;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            margin-right: 5px;
            cursor: pointer;
        }
        
        .edit-btn:hover {
            background-color: #ffc53d;
        }
        
        .delete-btn {
            background-color: #f5222d;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            cursor: pointer;
        }
        
        .delete-btn:hover {
            background-color: #ff4d4f;
        }
        
        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
        }
        
        .pagination-info {
            color: #666;
            font-size: 14px;
        }
        
        .pagination-btns {
            display: flex;
            gap: 10px;
        }
        
        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            background-color: white;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .pagination-btn:disabled {
            color: #d9d9d9;
            cursor: not-allowed;
        }
        
        .pagination-btn:hover:not(:disabled) {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .no-data {
            text-align: center;
            padding: 20px;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>照片管理</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="card">
                <div class="action-bar">
                    <div class="search-bar">
                        <input type="text" id="searchInput" class="search-input" placeholder="搜索照片描述或用户昵称">
                        <select id="typeFilter" class="filter-select">
                            <option value="">所有类型</option>
                            <option value="full">全身照</option>
                            <option value="half">半身照</option>
                            <option value="other">其他</option>
                        </select>
                        <select id="userFilter" class="filter-select" style="min-width: 120px;">
                            <option value="">所有用户</option>
                            <!-- 用户列表将通过JavaScript动态添加 -->
                        </select>
                        <button id="searchBtn" class="action-btn search-btn">搜索</button>
                    </div>
                    <button id="addPhotoBtn" class="action-btn add-btn" onclick="PhotoList.addPhoto()">添加照片</button>
                </div>
                
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>缩略图</th>
                            <th>类型</th>
                            <th>描述</th>
                            <th>所属用户</th>
                            <th>上传时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="photoTableBody">
                        <tr>
                            <td colspan="7" class="no-data">加载中...</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="pagination">
                    <div id="pageInfo" class="pagination-info">第 1/1 页</div>
                    <div class="pagination-btns">
                        <button id="prevBtn" class="pagination-btn" disabled>&lt; 上一页</button>
                        <button id="nextBtn" class="pagination-btn" disabled>下一页 &gt;</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/photo_list.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为photo
            Sidebar.init('photo');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化照片列表
            PhotoList.init();
        });
    </script>
</body>
</html> 