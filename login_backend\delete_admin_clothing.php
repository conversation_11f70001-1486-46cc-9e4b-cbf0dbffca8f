<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证管理员身份
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取POST数据
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

// 检查必要参数
if (!isset($input['id']) || empty($input['id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少必要参数: id']);
    exit();
}

$clothingId = (int)$input['id'];

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

try {
    // 开始事务
    $conn->beginTransaction();

    // 首先检查衣物是否存在
    $checkQuery = "SELECT id, image_url FROM clothes WHERE id = :id";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bindValue(':id', $clothingId, PDO::PARAM_INT);
    $checkStmt->execute();
    
    $clothing = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$clothing) {
        throw new Exception('衣物不存在');
    }
    
    // 删除记录
    $deleteQuery = "DELETE FROM clothes WHERE id = :id";
    $deleteStmt = $conn->prepare($deleteQuery);
    $deleteStmt->bindValue(':id', $clothingId, PDO::PARAM_INT);
    $deleteStmt->execute();
    
    if ($deleteStmt->rowCount() === 0) {
        throw new Exception('删除失败');
    }
    
    // 提交事务
    $conn->commit();
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'msg' => '衣物删除成功'
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    // 返回错误响应
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '操作失败: ' . $e->getMessage()
    ]);
} 