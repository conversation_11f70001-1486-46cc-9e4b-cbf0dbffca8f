<?php
/**
 * 测试权限检查API修复
 * 验证用户ID获取是否正常
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

echo "=== 权限检查API修复测试 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

// 检查授权头
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "❌ 缺少Authorization头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
echo "1. Token检查:\n";
echo "原始Token: $token\n";

if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
    echo "处理后Token: $token\n";
}

// 验证token
require_once 'config.php';
require_once 'auth.php';

try {
    $auth = new Auth();
    $payload = $auth->verifyToken($token);
    
    if ($payload) {
        echo "✅ Token验证成功\n";
        echo "Payload内容: " . json_encode($payload, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        
        // 测试用户ID获取
        echo "\n2. 用户ID获取测试:\n";
        $userId1 = $payload['user_id'] ?? null;
        $userId2 = $payload['sub'] ?? null;
        
        echo "payload['user_id']: " . ($userId1 ?? 'null') . "\n";
        echo "payload['sub']: " . ($userId2 ?? 'null') . "\n";
        
        // 使用修复后的逻辑
        $userId = $payload['user_id'] ?? $payload['sub'] ?? null;
        echo "最终用户ID: " . ($userId ?? 'null') . "\n";
        
        if ($userId) {
            echo "✅ 用户ID获取成功\n";
        } else {
            echo "❌ 用户ID获取失败\n";
        }
        
    } else {
        echo "❌ Token验证失败\n";
        exit;
    }
} catch (Exception $e) {
    echo "❌ Token验证异常: " . $e->getMessage() . "\n";
    exit;
}

// 测试权限检查API
echo "\n3. 权限检查API测试:\n";

// 获取测试数据
require_once 'db.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 获取一个衣物进行测试
    $stmt = $conn->prepare("SELECT id, name, user_id FROM clothes LIMIT 1");
    $stmt->execute();
    $testClothes = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testClothes) {
        echo "测试衣物: {$testClothes['name']} (ID: {$testClothes['id']}, 创建者: {$testClothes['user_id']})\n";
        
        // 测试权限检查API调用
        $apiUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/check_circle_permission.php";
        
        $testCases = [
            ['data_type' => 'clothes', 'data_id' => $testClothes['id'], 'operation' => 'view'],
            ['data_type' => 'clothes', 'data_id' => $testClothes['id'], 'operation' => 'edit'],
            ['data_type' => 'clothes', 'data_id' => $testClothes['id'], 'operation' => 'delete']
        ];
        
        foreach ($testCases as $testCase) {
            echo "\n测试: {$testCase['operation']} 操作\n";
            
            $params = http_build_query($testCase);
            $fullUrl = "$apiUrl?$params";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $fullUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: ' . $_SERVER['HTTP_AUTHORIZATION']
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                echo "❌ CURL错误: $error\n";
            } elseif ($httpCode !== 200) {
                echo "❌ HTTP错误: $httpCode\n";
                echo "响应: $response\n";
            } else {
                // 检查响应是否包含PHP错误
                if (strpos($response, '<b>Notice</b>') !== false || strpos($response, '<b>Warning</b>') !== false) {
                    echo "❌ PHP错误检测到:\n";
                    echo "$response\n";
                } else {
                    $data = json_decode($response, true);
                    if ($data && $data['status'] === 'success') {
                        $permission = $data['data'];
                        $status = $permission['allowed'] ? '✅ 允许' : '❌ 拒绝';
                        echo "$status - {$permission['reason']}\n";
                        
                        if (isset($data['debug'])) {
                            echo "调试信息: user_id = " . ($data['debug']['user_id'] ?? 'null') . "\n";
                        }
                    } else {
                        echo "❌ API错误: " . ($data['message'] ?? '未知错误') . "\n";
                        if (isset($data['debug'])) {
                            echo "调试信息: " . json_encode($data['debug']) . "\n";
                        }
                    }
                }
            }
        }
    } else {
        echo "❌ 没有找到测试衣物\n";
    }
    
} catch (Exception $e) {
    echo "❌ 数据库错误: " . $e->getMessage() . "\n";
}

// 测试创建权限
echo "\n4. 创建权限测试:\n";

$createTestUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/check_circle_permission.php";
$createParams = http_build_query([
    'data_type' => 'clothes',
    'operation' => 'create'
]);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "$createTestUrl?$createParams");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: ' . $_SERVER['HTTP_AUTHORIZATION']
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    if (strpos($response, '<b>Notice</b>') !== false || strpos($response, '<b>Warning</b>') !== false) {
        echo "❌ 创建权限检查包含PHP错误:\n";
        echo "$response\n";
    } else {
        $data = json_decode($response, true);
        if ($data && $data['status'] === 'success') {
            $permission = $data['data'];
            $status = $permission['allowed'] ? '✅ 允许' : '❌ 拒绝';
            echo "创建权限: $status - {$permission['reason']}\n";
        } else {
            echo "❌ 创建权限检查失败: " . ($data['message'] ?? '未知错误') . "\n";
        }
    }
} else {
    echo "❌ 创建权限检查HTTP错误: $httpCode\n";
}

echo "\n=== 测试完成 ===\n";

if (isset($userId) && $userId) {
    echo "✅ 权限检查API修复成功，用户ID获取正常\n";
} else {
    echo "❌ 权限检查API仍有问题，需要进一步调试\n";
}

echo "\n💡 如果测试通过，前端权限功能应该可以正常工作了！\n";
?>
