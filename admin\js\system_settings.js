/**
 * 系统设置管理模块
 */
const SystemSettings = {
    /**
     * 初始化系统设置页面
     */
    init: function() {
        // 获取当前系统设置
        this.loadSystemSettings();
        
        // 绑定选项卡切换事件
        this.bindTabEvents();
        
        // 绑定密码显示/隐藏切换
        this.bindPasswordToggle();
        
        // 绑定表单提交和重置事件
        this.bindFormEvents();
    },
    
    /**
     * 加载系统设置
     */
    loadSystemSettings: function() {
        // 显示加载指示器
        document.getElementById('savingIndicator').textContent = '正在加载系统设置...';
        document.getElementById('savingIndicator').style.display = 'block';
        
        // 清除提示信息
        this.hideAlerts();
        
        fetch('../login_backend/get_admin_system_settings.php', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('获取系统设置失败，请检查网络连接或权限');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                // 填充表单数据
                this.populateFormData(data.data);
                document.getElementById('savingIndicator').style.display = 'none';
            } else {
                throw new Error(data.message || '获取系统设置失败');
            }
        })
        .catch(error => {
            this.showErrorMessage(error.message);
            document.getElementById('savingIndicator').style.display = 'none';
        });
    },
    
    /**
     * 填充表单数据
     * @param {Object} data 系统设置数据
     */
    populateFormData: function(data) {
        // 填充数据库设置
        if (data.database) {
            document.getElementById('DB_HOST').value = data.database.DB_HOST || '';
            document.getElementById('DB_NAME').value = data.database.DB_NAME || '';
            document.getElementById('DB_USER').value = data.database.DB_USER || '';
            document.getElementById('DB_PASS').value = data.database.DB_PASS || '';
        }
        
        // 填充微信配置
        if (data.wechat) {
            document.getElementById('WX_APPID').value = data.wechat.WX_APPID || '';
            document.getElementById('WX_SECRET').value = data.wechat.WX_SECRET || '';
            document.getElementById('API_DOMAIN').value = data.wechat.API_DOMAIN || '';
            document.getElementById('WX_TOKEN').value = data.wechat.WX_TOKEN || '';
            document.getElementById('WX_ENCODING_AES_KEY').value = data.wechat.WX_ENCODING_AES_KEY || '';
        }
        
        // 填充管理员设置
        if (data.admin) {
            document.getElementById('ADMIN_SECRET_KEY').value = data.admin.ADMIN_SECRET_KEY || '';
        }
        
        // 填充阿里云配置
        if (data.aliyun) {
            document.getElementById('ALIYUN_ACCESS_KEY_ID').value = data.aliyun.ALIYUN_ACCESS_KEY_ID || '';
            document.getElementById('ALIYUN_ACCESS_KEY_SECRET').value = data.aliyun.ALIYUN_ACCESS_KEY_SECRET || '';
            document.getElementById('ALIYUN_OUTFIT_API_KEY').value = data.aliyun.ALIYUN_OUTFIT_API_KEY || '';
            document.getElementById('ALIYUN_OSS_ENDPOINT').value = data.aliyun.ALIYUN_OSS_ENDPOINT || '';
            document.getElementById('ALIYUN_OSS_BUCKET').value = data.aliyun.ALIYUN_OSS_BUCKET || '';
            document.getElementById('ALIYUN_OSS_BUCKET_DOMAIN').value = data.aliyun.ALIYUN_OSS_BUCKET_DOMAIN || '';
        }
        
        // 填充存储路径设置
        if (data.storage_paths) {
            document.getElementById('OSS_PATH_CLOTHES').value = data.storage_paths.OSS_PATH_CLOTHES || '';
            document.getElementById('OSS_PATH_PHOTOS').value = data.storage_paths.OSS_PATH_PHOTOS || '';
            document.getElementById('OSS_PATH_TRY_ON').value = data.storage_paths.OSS_PATH_TRY_ON || '';
        }
    },
    
    /**
     * 绑定选项卡切换事件
     */
    bindTabEvents: function() {
        const tabItems = document.querySelectorAll('.tab-item');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabItems.forEach(tab => {
            tab.addEventListener('click', () => {
                // 清除所有活动状态
                tabItems.forEach(item => item.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // 设置当前选项卡为活动状态
                tab.classList.add('active');
                const tabId = `${tab.dataset.tab}-tab`;
                document.getElementById(tabId).classList.add('active');
            });
        });
    },
    
    /**
     * 绑定密码显示/隐藏切换事件
     */
    bindPasswordToggle: function() {
        const toggleButtons = document.querySelectorAll('.toggle-password');
        
        toggleButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetId = button.dataset.target;
                const inputField = document.getElementById(targetId);
                
                if (inputField.type === 'password') {
                    inputField.type = 'text';
                    button.textContent = '🔒';
                } else {
                    inputField.type = 'password';
                    button.textContent = '👁️';
                }
            });
        });
    },
    
    /**
     * 绑定表单提交和重置事件
     */
    bindFormEvents: function() {
        const settingsForm = document.getElementById('settingsForm');
        const resetBtn = document.getElementById('resetBtn');
        
        // 表单提交事件
        settingsForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveSettings();
        });
        
        // 重置按钮事件
        resetBtn.addEventListener('click', () => {
            if (confirm('确定要重置所有更改吗？这将恢复到最后保存的设置。')) {
                this.loadSystemSettings();
            }
        });
    },
    
    /**
     * 保存系统设置
     */
    saveSettings: function() {
        // 显示保存指示器
        document.getElementById('savingIndicator').textContent = '正在保存设置...';
        document.getElementById('savingIndicator').style.display = 'block';
        
        // 清除提示信息
        this.hideAlerts();
        
        // 收集表单数据
        const formData = new FormData(document.getElementById('settingsForm'));
        
        // 转换为JSON格式
        const formDataObject = {};
        for (const [key, value] of formData.entries()) {
            // 处理嵌套字段，如 database[DB_HOST]
            const matches = key.match(/([^\[]+)\[([^\]]+)\]/);
            if (matches) {
                const section = matches[1];
                const field = matches[2];
                
                if (!formDataObject[section]) {
                    formDataObject[section] = {};
                }
                formDataObject[section][field] = value;
            } else {
                formDataObject[key] = value;
            }
        }
        
        // 发送更新请求
        fetch('../login_backend/update_admin_system_settings.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${Auth.getToken()}`
            },
            body: JSON.stringify(formDataObject)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('保存系统设置失败，请检查网络连接或权限');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                this.showSuccessMessage('系统设置已成功保存');
                // 重新加载设置，以确保显示最新的配置
                setTimeout(() => this.loadSystemSettings(), 1500);
            } else {
                throw new Error(data.message || '保存系统设置失败');
            }
        })
        .catch(error => {
            this.showErrorMessage(error.message);
        })
        .finally(() => {
            document.getElementById('savingIndicator').style.display = 'none';
        });
    },
    
    /**
     * 显示成功消息
     * @param {string} message 成功消息
     */
    showSuccessMessage: function(message) {
        const successAlert = document.getElementById('successAlert');
        successAlert.textContent = message;
        successAlert.style.display = 'block';
        
        // 3秒后自动隐藏
        setTimeout(() => {
            successAlert.style.display = 'none';
        }, 3000);
    },
    
    /**
     * 显示错误消息
     * @param {string} message 错误消息
     */
    showErrorMessage: function(message) {
        const errorAlert = document.getElementById('errorAlert');
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.textContent = message;
        errorAlert.style.display = 'block';
        
        // 5秒后自动隐藏
        setTimeout(() => {
            errorAlert.style.display = 'none';
        }, 5000);
    },
    
    /**
     * 隐藏所有提示信息
     */
    hideAlerts: function() {
        document.getElementById('successAlert').style.display = 'none';
        document.getElementById('errorAlert').style.display = 'none';
    }
}; 