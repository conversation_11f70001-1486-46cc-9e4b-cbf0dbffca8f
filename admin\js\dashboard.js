/**
 * 仪表盘管理模块
 */
const Dashboard = {
    // 保存仪表盘数据
    data: null,
    
    // 图表实例
    charts: {},
    
    // 当前日期筛选
    dateFilter: {
        startDate: null,
        endDate: null,
        period: 7 // 默认显示7天
    },
    
    // 缓存设置
    cache: {
        enabled: true,           // 是否启用缓存
        duration: 60 * 60 * 1000, // 缓存时长（毫秒），默认1小时
        keys: {
            dashboardData: 'dashboard_data_cache',
            timestamp: 'dashboard_cache_timestamp'
        }
    },
    
    /**
     * 初始化仪表盘
     */
    init: function() {
        // 添加Chart.js库
        this.loadChartJs().then(() => {
            // 设置Chart.js全局配置
            this.setupChartGlobals();
            
            // 初始化日期选择器
            this.initDateFilter();
            
            // 加载仪表盘数据
            this.loadDashboardData();
        }).catch(error => {
            console.error('加载Chart.js失败:', error);
            // 显示错误提示
            const errorContainer = document.getElementById('dashboardError');
            if (errorContainer) {
                errorContainer.textContent = '图表库加载失败，请刷新页面重试。';
                errorContainer.style.display = 'block';
            }
        });
    },
    
    /**
     * 动态加载Chart.js库
     */
    loadChartJs: function() {
        return new Promise((resolve, reject) => {
            if (window.Chart) {
                resolve();
                return;
            }
            
            // 定义CDN源列表，按优先级排序
            const cdnSources = [
                'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
                'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js',
                'https://unpkg.com/chart.js@3.9.1/dist/chart.min.js',
                './js/vendor/chart.min.js' // 本地文件作为最后的备选
            ];
            
            // 尝试从不同的源加载
            this.loadFromSources(cdnSources, 0, resolve, reject);
        });
    },
    
    /**
     * 递归尝试从不同的源加载Chart.js
     * @param {Array} sources CDN源列表
     * @param {number} index 当前尝试的源的索引
     * @param {Function} resolve 成功回调
     * @param {Function} reject 失败回调
     */
    loadFromSources: function(sources, index, resolve, reject) {
        if (index >= sources.length) {
            reject(new Error('所有Chart.js源加载失败'));
            return;
        }
        
        const script = document.createElement('script');
        script.src = sources[index];
        
        // 设置超时时间为5秒
        const timeout = setTimeout(() => {
            console.warn(`从 ${sources[index]} 加载Chart.js超时，尝试下一个源...`);
            // 超时后尝试下一个源
            this.loadFromSources(sources, index + 1, resolve, reject);
        }, 5000);
        
        script.onload = () => {
            clearTimeout(timeout);
            console.log(`成功从 ${sources[index]} 加载了Chart.js`);
            resolve();
        };
        
        script.onerror = () => {
            clearTimeout(timeout);
            console.warn(`从 ${sources[index]} 加载Chart.js失败，尝试下一个源...`);
            // 加载失败时尝试下一个源
            this.loadFromSources(sources, index + 1, resolve, reject);
        };
        
        document.head.appendChild(script);
    },
    
    /**
     * 设置Chart.js全局配置
     */
    setupChartGlobals: function() {
        if (window.Chart) {
            Chart.defaults.font.family = '"Microsoft YaHei", "Segoe UI", sans-serif';
            Chart.defaults.color = '#666';
            Chart.defaults.responsive = true;
            Chart.defaults.maintainAspectRatio = false;
        }
    },
    
    /**
     * 初始化日期筛选器
     */
    initDateFilter: function() {
        // 获取元素引用
        const periodSelector = document.getElementById('periodSelector');
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        const filterBtn = document.getElementById('filterBtn');
        
        // 设置默认日期范围
        this.setDefaultDateRange();
        
        // 更新日期输入框
        this.updateDateInputs();
        
        // 预设时间段变化事件
        if (periodSelector) {
            periodSelector.addEventListener('change', () => {
                const period = periodSelector.value;
                
                if (period === 'custom') {
                    // 自定义时间段，不自动更新
                    return;
                }
                
                // 根据选择的时间段设置日期范围
                this.dateFilter.period = parseInt(period);
                this.setDefaultDateRange();
                this.updateDateInputs();
                
                // 加载数据
                this.loadDashboardData();
            });
        }
        
        // 日期输入框变化事件
        if (startDateInput && endDateInput) {
            startDateInput.addEventListener('change', () => {
                this.dateFilter.startDate = startDateInput.value;
                periodSelector.value = 'custom';
            });
            
            endDateInput.addEventListener('change', () => {
                this.dateFilter.endDate = endDateInput.value;
                periodSelector.value = 'custom';
            });
        }
        
        // 筛选按钮点击事件
        if (filterBtn) {
            filterBtn.addEventListener('click', () => {
                this.loadDashboardData();
            });
        }
    },
    
    /**
     * 设置默认日期范围
     */
    setDefaultDateRange: function() {
        const today = new Date();
        const endDate = new Date(today);
        const startDate = new Date(today);
        
        // 设置结束日期为今天
        endDate.setHours(23, 59, 59, 999);
        
        // 根据选择的时间段设置开始日期
        startDate.setDate(startDate.getDate() - this.dateFilter.period + 1);
        startDate.setHours(0, 0, 0, 0);
        
        // 格式化日期为YYYY-MM-DD
        this.dateFilter.startDate = this.formatDate(startDate);
        this.dateFilter.endDate = this.formatDate(endDate);
    },
    
    /**
     * 更新日期输入框
     */
    updateDateInputs: function() {
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        
        if (startDateInput && this.dateFilter.startDate) {
            startDateInput.value = this.dateFilter.startDate;
        }
        
        if (endDateInput && this.dateFilter.endDate) {
            endDateInput.value = this.dateFilter.endDate;
        }
    },
    
    /**
     * 格式化日期为YYYY-MM-DD
     * @param {Date} date 日期对象
     * @returns {string} 格式化后的日期字符串
     */
    formatDate: function(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        return `${year}-${month}-${day}`;
    },
    
    /**
     * 格式化日期时间为可读字符串
     * @param {string} dateString ISO日期字符串
     * @returns {string} 格式化后的日期时间字符串
     */
    formatDateTime: function(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    },
    
    /**
     * 加载仪表盘数据
     * @param {boolean} forceRefresh 是否强制刷新（忽略缓存）
     */
    loadDashboardData: function(forceRefresh = false) {
        const loadingElem = document.getElementById('dashboardLoading');
        const errorElem = document.getElementById('dashboardError');
        
        if (loadingElem) {
            loadingElem.style.display = 'block';
        }
        
        if (errorElem) {
            errorElem.style.display = 'none';
        }
        
        // 检查是否可以使用缓存数据
        if (!forceRefresh && this.cache.enabled) {
            const cachedData = this.getCachedData();
            if (cachedData) {
                console.log('使用缓存的仪表盘数据');
                this.data = cachedData;
                
                // 渲染数据
                if (loadingElem) {
                    loadingElem.style.display = 'none';
                }
                
                this.renderDashboard();
                
                // 检查缓存是否接近过期，如果是，在后台刷新数据
                const timestamp = localStorage.getItem(this.cache.keys.timestamp);
                if (timestamp) {
                    const now = Date.now();
                    const age = now - parseInt(timestamp);
                    
                    // 如果缓存已经存在超过30分钟，在后台刷新数据
                    if (age > 30 * 60 * 1000) {
                        console.log('缓存数据已超过30分钟，在后台刷新...');
                        this.loadFreshDataInBackground();
                    }
                }
                
                return;
            }
        }
        
        // 构建URL和参数
        const url = new URL('../login_backend/get_dashboard_data.php', window.location.origin);
        
        // 添加日期参数
        if (this.dateFilter.startDate) {
            url.searchParams.append('start_date', this.dateFilter.startDate);
        }
        
        if (this.dateFilter.endDate) {
            url.searchParams.append('end_date', this.dateFilter.endDate);
        }
        
        // 添加优先级参数，先加载关键数据
        url.searchParams.append('priority', 'high');
        
        // 记录开始时间
        const startTime = performance.now();
        
        console.log('开始加载仪表盘数据...', url.toString());
        
        // 添加超时控制
        const timeout = 10000; // 10秒超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            },
            signal: controller.signal
        })
        .then(response => {
            clearTimeout(timeoutId);
            if (!response.ok) {
                const errorMsg = `HTTP错误: ${response.status} ${response.statusText}`;
                console.error(errorMsg);
                throw new Error(errorMsg);
            }
            return response.json();
        })
        .then(data => {
            const fetchTime = performance.now() - startTime;
            console.log(`仪表盘高优先级数据加载完成，耗时: ${fetchTime.toFixed(2)}ms`);
            
            if (data.status === 'success') {
                // 检查返回的数据结构是否完整
                if (!data.data) {
                    throw new Error('返回的数据结构不完整：缺少data字段');
                }
                
                // 检查必要的数据字段
                const requiredFields = ['users', 'clothing', 'dailyStats'];
                const missingFields = requiredFields.filter(field => !data.data[field]);
                
                if (missingFields.length > 0) {
                    console.warn(`数据不完整，缺少以下字段: ${missingFields.join(', ')}`);
                }
                
                this.data = data.data;
                
                // 缓存数据
                if (this.cache.enabled) {
                    this.setCachedData(this.data);
                }
                
                // 先渲染关键数据
                this.renderOverviewCards();
                
                // 隐藏加载指示器
                if (loadingElem) {
                    loadingElem.style.display = 'none';
                }
                
                // 延迟加载图表数据，分批渲染以提高性能
                setTimeout(() => {
                    this.renderTrendsChart();
                    
                    // 再延迟加载分布图表
                    setTimeout(() => {
                        this.renderDistributionCharts();
                    }, 100);
                }, 50);
                
                // 使用串行加载低优先级数据，避免竞态条件
                setTimeout(() => {
                    this.loadLowPriorityData();
                }, 200);
            } else {
                throw new Error(data.message || '获取仪表盘数据失败：服务器返回错误状态');
            }
        })
        .catch(error => {
            clearTimeout(timeoutId);
            
            // 处理超时错误
            if (error.name === 'AbortError') {
                console.error('获取仪表盘数据超时');
                error = new Error('数据加载超时，请稍后重试');
            }
            
            console.error('获取仪表盘数据失败:', error);
            
            // 尝试使用缓存数据作为后备
            if (this.cache.enabled) {
                const cachedData = this.getCachedData();
                if (cachedData) {
                    console.log('使用缓存的仪表盘数据作为后备');
                    this.data = cachedData;
                    this.renderDashboard();
                    
                    // 显示警告而不是错误
                    if (errorElem) {
                        errorElem.textContent = `无法获取最新数据，显示的是缓存数据。错误: ${error.message || '未知错误'}`;
                        errorElem.style.display = 'block';
                    }
                    
                    if (loadingElem) {
                        loadingElem.style.display = 'none';
                    }
                    return;
                }
            }
            
            // 显示友好的错误信息
            if (errorElem) {
                errorElem.textContent = `加载失败: ${error.message || '未知错误'}`;
                errorElem.style.display = 'block';
            }
            
            // 隐藏加载指示器
            if (loadingElem) {
                loadingElem.style.display = 'none';
            }
            
            // 尝试初始化空数据，以便UI可以正常显示
            this.data = {
                users: { total: 0, newUsers: 0 },
                clothing: { total: 0, newClothes: 0 },
                photos: { total: 0, newPhotos: 0, typeDistribution: [] },
                tryOn: { total: 0, newTryOns: 0, statusDistribution: [] },
                dailyStats: []
            };
            
            // 尝试渲染可用的UI元素
            this.renderOverviewCards();
        });
    },
    
    /**
     * 在后台加载新鲜数据，不影响UI
     */
    loadFreshDataInBackground: function() {
        // 构建URL和参数
        const url = new URL('../login_backend/get_dashboard_data.php', window.location.origin);
        
        // 添加日期参数
        if (this.dateFilter.startDate) {
            url.searchParams.append('start_date', this.dateFilter.startDate);
        }
        
        if (this.dateFilter.endDate) {
            url.searchParams.append('end_date', this.dateFilter.endDate);
        }
        
        // 添加优先级参数，加载所有数据
        url.searchParams.append('priority', 'all');
        
        // 记录开始时间
        const startTime = performance.now();
        
        console.log('在后台加载新鲜数据...');
        
        fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('后台数据刷新失败');
            }
            return response.json();
        })
        .then(data => {
            const fetchTime = performance.now() - startTime;
            console.log(`后台数据刷新完成，耗时: ${fetchTime.toFixed(2)}ms`);
            
            if (data.status === 'success' && data.data) {
                // 更新缓存
                if (this.cache.enabled) {
                    this.setCachedData(data.data);
                    console.log('缓存数据已在后台更新');
                }
            }
        })
        .catch(error => {
            console.error('后台数据刷新失败:', error);
        });
    },
    
    /**
     * 加载低优先级数据
     */
    loadLowPriorityData: function() {
        console.log('开始加载低优先级数据...');
        
        // 构建URL和参数
        const url = new URL('../login_backend/get_dashboard_data.php', window.location.origin);
        
        // 添加日期参数
        if (this.dateFilter.startDate) {
            url.searchParams.append('start_date', this.dateFilter.startDate);
        }
        
        if (this.dateFilter.endDate) {
            url.searchParams.append('end_date', this.dateFilter.endDate);
        }
        
        // 添加优先级参数
        url.searchParams.append('priority', 'low');
        
        // 记录开始时间
        const startTime = performance.now();
        
        // 添加超时控制
        const timeout = 15000; // 15秒超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            },
            signal: controller.signal
        })
        .then(response => {
            clearTimeout(timeoutId);
            if (!response.ok) {
                throw new Error('获取低优先级数据失败');
            }
            return response.json();
        })
        .then(data => {
            const fetchTime = performance.now() - startTime;
            console.log(`低优先级数据加载完成，耗时: ${fetchTime.toFixed(2)}ms`);
            console.log('低优先级数据内容:', data);
            
            if (data.status === 'success' && data.data) {
                // 更新数据
                this.data = {...this.data, ...data.data};
                
                // 更新缓存
                if (this.cache.enabled) {
                    this.setCachedData(this.data);
                }
                
                // 更新可能受影响的图表
                if (data.data.tryOn) {
                    console.log('尝试渲染试衣状态分布图表...');
                    this.renderTryOnStatusDistribution();
                }
                
                if (data.data.photos) {
                    console.log('尝试渲染照片类型分布图表...');
                    this.renderPhotoTypeDistribution();
                }
            } else {
                console.warn('低优先级数据加载成功，但数据格式不正确:', data);
            }
        })
        .catch(error => {
            clearTimeout(timeoutId);
            
            // 处理超时错误
            if (error.name === 'AbortError') {
                console.error('获取低优先级数据超时');
                return; // 低优先级数据超时可以忽略
            }
            
            console.error('获取低优先级数据失败:', error);
        });
    },
    
    /**
     * 获取缓存的仪表盘数据
     * @returns {Object|null} 缓存的数据或null
     */
    getCachedData: function() {
        try {
            // 检查缓存时间戳
            const timestamp = localStorage.getItem(this.cache.keys.timestamp);
            if (!timestamp) return null;
            
            // 检查缓存是否过期
            const now = Date.now();
            if (now - parseInt(timestamp) > this.cache.duration) {
                console.log('缓存已过期');
                return null;
            }
            
            // 获取缓存数据
            const cachedData = localStorage.getItem(this.cache.keys.dashboardData);
            if (!cachedData) return null;
            
            // 解析缓存数据
            const data = JSON.parse(cachedData);
            console.log('成功获取缓存数据，缓存时间:', new Date(parseInt(timestamp)).toLocaleString());
            return data;
        } catch (error) {
            console.error('获取缓存数据失败:', error);
            return null;
        }
    },
    
    /**
     * 设置缓存的仪表盘数据
     * @param {Object} data 要缓存的数据
     */
    setCachedData: function(data) {
        try {
            // 存储数据
            localStorage.setItem(this.cache.keys.dashboardData, JSON.stringify(data));
            // 存储时间戳
            localStorage.setItem(this.cache.keys.timestamp, Date.now().toString());
            console.log('仪表盘数据已缓存');
        } catch (error) {
            console.error('缓存数据失败:', error);
        }
    },
    
    /**
     * 清除缓存的仪表盘数据
     */
    clearCachedData: function() {
        try {
            localStorage.removeItem(this.cache.keys.dashboardData);
            localStorage.removeItem(this.cache.keys.timestamp);
            console.log('仪表盘缓存数据已清除');
        } catch (error) {
            console.error('清除缓存数据失败:', error);
        }
    },
    
    /**
     * 强制刷新仪表盘数据
     */
    forceRefresh: function() {
        this.clearCachedData();
        this.loadDashboardData(true);
    },
    
    /**
     * 渲染仪表盘
     */
    renderDashboard: function() {
        if (!this.data) return;
        
        // 渲染概览卡片
        this.renderOverviewCards();
        
        // 渲染趋势图表
        this.renderTrendsChart();
        
        // 渲染分布图表
        this.renderDistributionCharts();
    },
    
    /**
     * 渲染概览卡片
     */
    renderOverviewCards: function() {
        // 确保数据存在
        if (!this.data) {
            console.error('仪表盘数据为空');
            return;
        }
        
        // 解构数据并提供默认值
        const { 
            users = { total: 0, newUsers: 0 }, 
            clothing = { total: 0, newClothes: 0 }, 
            photos = { total: 0, newPhotos: 0 }, 
            tryOn = { total: 0, newTryOns: 0 }, 
            donations = { total: 0, totalAmount: 0 } 
        } = this.data;
        
        // 用户统计
        if (document.getElementById('totalUsers')) {
            document.getElementById('totalUsers').textContent = users.total || 0;
        }
        if (document.getElementById('newUsers')) {
            document.getElementById('newUsers').textContent = users.newUsers || 0;
        }
        
        // 衣物统计
        if (document.getElementById('totalClothes')) {
            document.getElementById('totalClothes').textContent = clothing.total || 0;
        }
        if (document.getElementById('newClothes')) {
            document.getElementById('newClothes').textContent = clothing.newClothes || 0;
        }
        
        // 照片统计
        if (document.getElementById('totalPhotos')) {
            document.getElementById('totalPhotos').textContent = photos.total || 0;
        }
        if (document.getElementById('newPhotos')) {
            document.getElementById('newPhotos').textContent = photos.newPhotos || 0;
        }
        
        // 试衣统计
        if (document.getElementById('totalTryOns')) {
            document.getElementById('totalTryOns').textContent = tryOn.total || 0;
        }
        if (document.getElementById('newTryOns')) {
            document.getElementById('newTryOns').textContent = tryOn.newTryOns || 0;
        }
        
        // 如果有打赏统计数据，更新打赏相关卡片
        if (donations) {
            if (document.getElementById('totalDonations')) {
                document.getElementById('totalDonations').textContent = donations.total || 0;
            }
            if (document.getElementById('totalDonationAmount')) {
                document.getElementById('totalDonationAmount').textContent = `¥${(donations.totalAmount || 0).toFixed(2)}`;
            }
        }
    },
    
    /**
     * 渲染趋势图表
     */
    renderTrendsChart: function() {
        // 确保数据存在
        if (!this.data || !this.data.dailyStats) {
            console.error('趋势图表数据不完整');
            return;
        }
        
        const { dailyStats } = this.data;
        
        // 确保dailyStats是数组
        if (!Array.isArray(dailyStats) || dailyStats.length === 0) {
            console.error('每日统计数据为空或格式不正确');
            return;
        }
        
        // 获取图表元素
        const ctx = document.getElementById('trendsChart');
        if (!ctx) {
            console.error('找不到趋势图表元素');
            return;
        }
        
        const dates = dailyStats.map(item => item.date || '');
        const newUsersData = dailyStats.map(item => item.newUsers || 0);
        const newClothesData = dailyStats.map(item => item.newClothes || 0);
        const newPhotosData = dailyStats.map(item => item.newPhotos || 0);
        const newTryOnsData = dailyStats.map(item => item.newTryOns || 0);
        const newDonationsData = dailyStats.map(item => item.newDonations || 0);
        
        if (this.charts.trends) {
            this.charts.trends.destroy();
        }
        
        // 生成图表标题，显示日期范围
        let chartTitle = `数据趋势`;
        if (this.dateFilter.startDate && this.dateFilter.endDate) {
            chartTitle = `数据趋势 (${this.dateFilter.startDate} 至 ${this.dateFilter.endDate})`;
        }
        
        try {
            this.charts.trends = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [
                        {
                            label: '新增用户',
                            data: newUsersData,
                            borderColor: 'rgba(54, 162, 235, 1)',
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '新增衣物',
                            data: newClothesData,
                            borderColor: 'rgba(255, 99, 132, 1)',
                            backgroundColor: 'rgba(255, 99, 132, 0.2)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '新增照片',
                            data: newPhotosData,
                            borderColor: 'rgba(75, 192, 192, 1)',
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '试衣次数',
                            data: newTryOnsData,
                            borderColor: 'rgba(255, 159, 64, 1)',
                            backgroundColor: 'rgba(255, 159, 64, 0.2)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '打赏次数',
                            data: newDonationsData,
                            borderColor: 'rgba(153, 102, 255, 1)',
                            backgroundColor: 'rgba(153, 102, 255, 0.2)',
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    plugins: {
                        title: {
                            display: true,
                            text: chartTitle,
                            font: {
                                size: 16
                            }
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
        } catch (error) {
            console.error('渲染趋势图表失败:', error);
        }
    },
    
    /**
     * 渲染分布图表
     */
    renderDistributionCharts: function() {
        console.log('开始渲染分布图表...');
        
        // 照片类型分布
        this.renderPhotoTypeDistribution();
        
        // 试衣状态分布
        this.renderTryOnStatusDistribution();
        
        // 推荐分布图
        this.renderRecommendationTypeChart();
    },
    
    /**
     * 渲染推荐类型分布图
     */
    renderRecommendationTypeChart: function() {
        // 显示加载中状态
        const ctx = document.getElementById('recommendationTypeChart');
        if (!ctx) return;
        
        // 如果已经有图表实例，先销毁
        if (this.charts.recommendationType) {
            this.charts.recommendationType.destroy();
        }
        
        // 创建临时加载中图表
        this.charts.recommendationType = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['加载中...'],
                datasets: [{
                    data: [1],
                    backgroundColor: ['rgba(200, 200, 200, 0.5)'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        
        // 记录开始时间
        const startTime = performance.now();
        
        // 检查是否有缓存数据
        const cacheKey = 'recommendationStats_' + new Date().toDateString();
        const cachedData = sessionStorage.getItem(cacheKey);
        
        if (cachedData) {
            try {
                const data = JSON.parse(cachedData);
                console.log('使用缓存的推荐统计数据');
                this.updateRecommendationChart(data);
                
                // 如果缓存时间超过30分钟，在后台刷新
                const timestamp = sessionStorage.getItem(cacheKey + '_timestamp');
                if (timestamp) {
                    const now = Date.now();
                    const age = now - parseInt(timestamp);
                    if (age > 30 * 60 * 1000) {
                        console.log('推荐统计数据缓存已超过30分钟，在后台刷新...');
                        this.loadRecommendationStatsInBackground();
                    }
                }
                
                return;
            } catch (e) {
                console.error('解析缓存数据失败:', e);
                // 继续获取新数据
            }
        }
        
        // 添加超时处理
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('获取推荐数据超时')), 10000);
        });
        
        // 添加请求中断功能
        const controller = new AbortController();
        const signal = controller.signal;
        
        Promise.race([
            fetch('../login_backend/get_recommendation_stats.php', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${Auth.getToken()}`
                },
                signal: signal
            }),
            timeoutPromise
        ])
        .then(response => {
            if (!response.ok) {
                throw new Error('获取推荐数据失败');
            }
            return response.json();
        })
        .then(data => {
            const fetchTime = performance.now() - startTime;
            console.log(`推荐统计数据加载完成，耗时: ${fetchTime.toFixed(2)}ms`);
            
            // 缓存数据
            try {
                sessionStorage.setItem(cacheKey, JSON.stringify(data));
                sessionStorage.setItem(cacheKey + '_timestamp', Date.now().toString());
            } catch (e) {
                console.error('缓存推荐数据失败:', e);
            }
            
            this.updateRecommendationChart(data);
        })
        .catch(error => {
            console.error('获取推荐分布数据失败:', error);
            
            // 显示错误状态
            if (this.charts.recommendationType) {
                this.charts.recommendationType.destroy();
            }
            
            this.charts.recommendationType = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['加载失败'],
                    datasets: [{
                        data: [1],
                        backgroundColor: ['rgba(255, 99, 132, 0.5)'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'bottom'
                        }
                    }
                }
            });
        });
    },
    
    /**
     * 在后台加载推荐统计数据
     */
    loadRecommendationStatsInBackground: function() {
        fetch('../login_backend/get_recommendation_stats.php', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('获取推荐数据失败');
            }
            return response.json();
        })
        .then(data => {
            console.log('推荐统计数据后台刷新完成');
            
            // 缓存数据
            const cacheKey = 'recommendationStats_' + new Date().toDateString();
            try {
                sessionStorage.setItem(cacheKey, JSON.stringify(data));
                sessionStorage.setItem(cacheKey + '_timestamp', Date.now().toString());
            } catch (e) {
                console.error('缓存推荐数据失败:', e);
            }
        })
        .catch(error => {
            console.error('后台获取推荐数据失败:', error);
        });
    },
    
    /**
     * 更新推荐图表
     * @param {Object} data API返回的数据
     */
    updateRecommendationChart: function(data) {
        if (data.status === 'success' && data.data.stats) {
            const stats = data.data.stats;
            const ctx = document.getElementById('recommendationTypeChart');
            
            if (!ctx) return;
            
            // 准备图表数据
            const labels = {
                'weatherBased': '基于天气',
                'clothingBased': '基于衣物',
                'preferenceBased': '基于喜好',
                'imageAnalysisBased': '基于形象分析'
            };
            
            const chartData = [];
            const chartLabels = [];
            
            // 获取各类型数量
            Object.entries(stats).forEach(([key, value]) => {
                if (value.total > 0) {
                    chartData.push(value.total);
                    chartLabels.push(labels[key] || key);
                }
            });
            
            // 如果已存在图表实例，先销毁
            if (this.charts.recommendationType) {
                this.charts.recommendationType.destroy();
            }
            
            // 创建图表
            this.charts.recommendationType = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: chartLabels,
                    datasets: [{
                        data: chartData,
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 15,
                                font: {
                                    size: 11
                                }
                            }
                        }
                    }
                }
            });
            
            // 更新推荐统计数据
            this.updateRecommendationStats(stats);
        }
    },
    
    /**
     * 更新推荐统计数据
     */
    updateRecommendationStats: function(stats) {
        if (!stats) return;
        
        let totalCount = 0;
        let recentCount = 0;
        
        // 计算总数和最近数量
        Object.values(stats).forEach(stat => {
            totalCount += stat.total;
            recentCount += stat.recent;
        });
        
        // 更新统计卡片
        const totalElem = document.getElementById('totalRecommendations');
        const recentElem = document.getElementById('newRecommendations');
        
        if (totalElem) totalElem.textContent = totalCount;
        if (recentElem) recentElem.textContent = recentCount;
    },
    
    /**
     * 渲染照片类型分布图
     */
    renderPhotoTypeDistribution: function() {
        // 确保数据存在
        if (!this.data || !this.data.photos) {
            console.error('照片数据不完整');
            return;
        }
        
        console.log('照片数据:', this.data.photos);
        
        // 检查typeDistribution是否存在
        if (!this.data.photos.typeDistribution) {
            console.error('照片类型分布数据不存在');
            
            // 尝试创建一个空的图表
            const ctx = document.getElementById('photoTypeChart');
            if (ctx && !this.charts.photoType) {
                this.charts.photoType = new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: ['数据加载中...'],
                        datasets: [{
                            data: [1],
                            backgroundColor: ['rgba(200, 200, 200, 0.5)']
                        }]
                    },
                    options: {
                        plugins: {
                            title: {
                                display: true,
                                text: '照片类型分布',
                                font: { size: 14 }
                            },
                            legend: { position: 'bottom' }
                        }
                    }
                });
            }
            return;
        }
        
        const { typeDistribution } = this.data.photos;
        
        // 确保typeDistribution是数组
        if (!Array.isArray(typeDistribution)) {
            console.error('照片类型分布数据格式不正确');
            return;
        }
        
        console.log('照片类型分布数据:', typeDistribution);
        
        // 获取图表元素
        const ctx = document.getElementById('photoTypeChart');
        if (!ctx) {
            console.error('找不到照片类型图表元素');
            return;
        }
        
        // 定义类型映射
        const typeMap = {
            'full': '全身照',
            'half': '半身照',
            'other': '其他'
        };
        
        const labels = typeDistribution.map(item => typeMap[item.type] || item.type || '未知');
        const data = typeDistribution.map(item => parseInt(item.count) || 0);
        
        if (this.charts.photoType) {
            this.charts.photoType.destroy();
        }
        
        try {
            this.charts.photoType = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            data: data,
                            backgroundColor: [
                                'rgba(75, 192, 192, 0.7)',
                                'rgba(153, 102, 255, 0.7)',
                                'rgba(255, 159, 64, 0.7)'
                            ],
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    plugins: {
                        title: {
                            display: true,
                            text: '照片类型分布',
                            font: {
                                size: 14
                            }
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            console.log('照片类型分布图表渲染成功');
        } catch (error) {
            console.error('渲染照片类型分布图表失败:', error);
        }
    },
    
    /**
     * 渲染试衣状态分布图
     */
    renderTryOnStatusDistribution: function() {
        // 确保数据存在
        if (!this.data || !this.data.tryOn) {
            console.error('试衣数据不完整');
            return;
        }
        
        console.log('试衣数据:', this.data.tryOn);
        
        // 检查statusDistribution是否存在
        if (!this.data.tryOn.statusDistribution) {
            console.error('试衣状态分布数据不存在');
            
            // 尝试创建一个空的图表
            const ctx = document.getElementById('tryOnStatusChart');
            if (ctx && !this.charts.tryOnStatus) {
                this.charts.tryOnStatus = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['数据加载中...'],
                        datasets: [{
                            data: [1],
                            backgroundColor: ['rgba(200, 200, 200, 0.5)']
                        }]
                    },
                    options: {
                        plugins: {
                            title: {
                                display: true,
                                text: '试衣状态分布',
                                font: { size: 14 }
                            },
                            legend: { position: 'bottom' }
                        }
                    }
                });
            }
            return;
        }
        
        const { statusDistribution } = this.data.tryOn;
        
        // 确保statusDistribution是数组
        if (!Array.isArray(statusDistribution)) {
            console.error('试衣状态分布数据格式不正确');
            return;
        }
        
        console.log('试衣状态分布数据:', statusDistribution);
        
        // 获取图表元素
        const ctx = document.getElementById('tryOnStatusChart');
        if (!ctx) {
            console.error('找不到试衣状态图表元素');
            return;
        }
        
        // 定义状态映射
        const statusMap = {
            'success': '成功',
            'failed': '失败',
            null: '进行中'
        };
        
        const labels = statusDistribution.map(item => statusMap[item.status] || item.status || '未知');
        const data = statusDistribution.map(item => parseInt(item.count) || 0);
        
        if (this.charts.tryOnStatus) {
            this.charts.tryOnStatus.destroy();
        }
        
        try {
            this.charts.tryOnStatus = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            data: data,
                            backgroundColor: [
                                'rgba(75, 192, 192, 0.7)',
                                'rgba(255, 99, 132, 0.7)',
                                'rgba(255, 206, 86, 0.7)'
                            ],
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    plugins: {
                        title: {
                            display: true,
                            text: '试衣状态分布',
                            font: {
                                size: 14
                            }
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            console.log('试衣状态分布图表渲染成功');
        } catch (error) {
            console.error('渲染试衣状态分布图表失败:', error);
        }
    }
};

// 文档加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 仪表盘初始化
    Dashboard.init();
}); 