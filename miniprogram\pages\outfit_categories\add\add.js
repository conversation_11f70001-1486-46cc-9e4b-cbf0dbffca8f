// pages/outfit_categories/add/add.js
const app = getApp()

Page({

    /**
     * 页面的初始数据
     */
    data: {
        name: '',
        nameCount: 0,
        description: '',
        descCount: 0,
        sort: '0',
        isSubmitting: false
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        wx.setNavigationBarTitle({
            title: '添加穿搭分类'
        })
    },

    /**
     * 分类名称输入事件
     */
    onNameInput: function(e) {
        this.setData({
            name: e.detail.value,
            nameCount: e.detail.value.length
        })
    },

    /**
     * 分类描述输入事件
     */
    onDescInput: function(e) {
        this.setData({
            description: e.detail.value,
            descCount: e.detail.value.length
        })
    },

    /**
     * 排序输入事件
     */
    onSortInput: function(e) {
        this.setData({
            sort: e.detail.value
        })
    },

    /**
     * 取消按钮事件
     */
    onCancel: function() {
        wx.navigateBack()
    },

    /**
     * 表单验证
     */
    validateForm: function() {
        const { name } = this.data
        
        if (!name || name.trim() === '') {
            wx.showToast({
                title: '请输入分类名称',
                icon: 'none'
            })
            return false
        }
        
        return true
    },

    /**
     * 提交按钮事件
     */
    onSubmit: function() {
        if (!this.validateForm()) {
            return
        }
        
        if (this.data.isSubmitting) {
            return
        }
        
        this.setData({
            isSubmitting: true
        })
        
        wx.showLoading({
            title: '保存中...',
            mask: true
        })
        
        const requestData = {
            name: this.data.name.trim(),
            description: this.data.description.trim(),
            sort_order: parseInt(this.data.sort) || 0
        }
        
        const token = app.globalData.token
        
        if (!token) {
            wx.hideLoading()
            wx.showToast({
                title: '登录状态异常，请重新登录',
                icon: 'none'
            })
            this.setData({
                isSubmitting: false
            })
            return
        }
        
        wx.request({
            url: `${app.globalData.apiBaseUrl}/add_outfit_category.php`,
            method: 'POST',
            header: {
                'content-type': 'application/json',
                'Authorization': token
            },
            data: requestData,
            success: (res) => {
                wx.hideLoading()
                
                if (res.statusCode === 200 && res.data.success) {
                    wx.showToast({
                        title: '添加成功',
                        icon: 'success',
                        duration: 1500
                    })
                    
                    // 设置全局刷新分类标志，确保所有页面都能更新分类列表
                    app.globalData.needRefreshOutfitCategories = true;
                    
                    // 返回上一页并刷新列表
                    setTimeout(() => {
                        // 获取页面栈
                        const pages = getCurrentPages()
                        if (pages.length > 1) {
                            // 上一页面
                            const prevPage = pages[pages.length - 2]
                            // 调用上一页面的刷新方法
                            if (prevPage && typeof prevPage.loadCategories === 'function') {
                                prevPage.loadCategories(true)
                            }
                        }
                        wx.navigateBack()
                    }, 1500)
                } else {
                    wx.showToast({
                        title: res.data.error || res.data.message || '添加失败',
                        icon: 'none'
                    })
                }
            },
            fail: (err) => {
                wx.hideLoading()
                console.error('添加穿搭分类失败:', err)
                wx.showToast({
                    title: '网络异常，请重试',
                    icon: 'none'
                })
            },
            complete: () => {
                this.setData({
                    isSubmitting: false
                })
            }
        })
    }
})