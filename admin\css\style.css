/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

a {
    color: #1890ff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* 登录页样式 */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.login-box {
    background: white;
    width: 380px;
    border-radius: 8px;
    box-shadow: 0 2px 16px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

.logo {
    text-align: center;
    margin-bottom: 25px;
}

.logo img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
}

.logo h1 {
    font-size: 22px;
    margin-top: 12px;
    color: #333;
    font-weight: 600;
}

.form-item {
    margin-bottom: 16px;
}

.form-item label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #666;
}

.form-item input {
    width: 100%;
    height: 40px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 4px 11px;
    font-size: 14px;
    transition: all 0.3s;
}

.form-item input:hover {
    border-color: #40a9ff;
}

.form-item input:focus {
    border-color: #1890ff;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-item button {
    width: 100%;
    height: 40px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
}

.form-item button:hover {
    background-color: #40a9ff;
}

.form-item button:active {
    background-color: #096dd9;
}

.form-item button:disabled {
    background-color: #d9d9d9;
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
}

.error-message {
    color: #f5222d;
    margin-bottom: 16px;
    min-height: 20px;
    font-size: 14px;
}

/* 仪表盘样式 */
.dashboard-container {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 220px;
    background-color: #001529;
    color: white;
    padding-top: 20px;
}

.sidebar-logo {
    padding: 0 20px;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
}

.sidebar-logo img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
}

.sidebar-logo h2 {
    font-size: 18px;
    font-weight: 500;
    color: white;
}

.menu {
    list-style: none;
}

.menu-item {
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s;
}

.menu-item:hover {
    background-color: #1890ff;
}

.menu-item.active {
    background-color: #1890ff;
}

.content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: white;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    margin-bottom: 20px;
}

.header h2 {
    font-size: 18px;
    color: #333;
    font-weight: 500;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-info span {
    margin-right: 15px;
}

.logout-btn {
    background-color: transparent;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 5px 12px;
    cursor: pointer;
    transition: all 0.3s;
}

.logout-btn:hover {
    color: #f5222d;
    border-color: #f5222d;
}

.card {
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    padding: 24px;
    margin-bottom: 20px;
}

.card-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 15px;
}

/* 图片大小控制 */
.outfit-img {
    max-width: 120px !important;
    max-height: 120px !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
}

.outfit-thumbnail {
    max-width: 60px !important;
    max-height: 40px !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
}

.outfit-main-img {
    max-width: 280px !important;
    max-height: 280px !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
}

/* 响应式图片控制 */
@media (max-width: 768px) {
    .outfit-img {
        max-width: 100px !important;
        max-height: 100px !important;
    }
    
    .outfit-main-img {
        max-width: 220px !important;
        max-height: 220px !important;
    }
    
    .outfit-thumbnail {
        max-width: 50px !important;
        max-height: 35px !important;
    }
}

@media (max-width: 576px) {
    .outfit-img {
        max-width: 80px !important;
        max-height: 80px !important;
    }
    
    .outfit-main-img {
        max-width: 180px !important;
        max-height: 180px !important;
    }
    
    .outfit-thumbnail {
        max-width: 40px !important;
        max-height: 30px !important;
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        padding-top: 10px;
    }
    
    .content {
        padding: 10px;
    }
    
    .login-box {
        width: 90%;
        max-width: 380px;
    }
} 

/* 表格样式 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background-color: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background-color: #f5f7fa;
    font-weight: 500;
    color: #666;
}

.data-table tr:last-child td {
    border-bottom: none;
}

.data-table tr:hover {
    background-color: #f9fafc;
}

/* 搜索区域样式 */
.search-container {
    display: flex;
    margin-bottom: 20px;
}

.search-input {
    flex: 1;
    height: 36px;
    padding: 0 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px 0 0 4px;
    outline: none;
    transition: all 0.3s;
}

.search-input:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.search-btn {
    height: 36px;
    padding: 0 15px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: all 0.3s;
}

.search-btn:hover {
    background-color: #40a9ff;
}

/* 状态标签样式 */
.status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-yes {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
}

.status-no {
    background-color: #fff2e8;
    color: #fa541c;
    border: 1px solid #ffbb96;
}

.share-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.share-badge.no-share {
    background-color: #f5f5f5;
    color: #888;
    border: 1px solid #d9d9d9;
}

/* 操作按钮样式 */
.action-btn {
    padding: 4px 10px;
    margin-right: 5px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s;
    background-color: #fff;
    border: 1px solid #d9d9d9;
}

.action-btn:hover {
    color: #1890ff;
    border-color: #1890ff;
}

.disable-btn {
    color: #ff4d4f;
}

.disable-btn:hover {
    color: #ff4d4f;
    border-color: #ff4d4f;
    background-color: #fff1f0;
}

.view-btn {
    color: #1890ff;
}

.view-btn:hover {
    color: #1890ff;
    border-color: #1890ff;
    background-color: #e6f7ff;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.page-btn {
    margin: 0 4px;
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    background-color: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.page-btn:hover {
    color: #1890ff;
    border-color: #1890ff;
}

.page-btn.active {
    background-color: #1890ff;
    color: white;
    border-color: #1890ff;
}

/* 商户头像样式 */
.merchant-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

/* 加载和错误提示样式 */
.loading-indicator {
    text-align: center;
    padding: 20px;
    color: #1890ff;
}

.error-container {
    padding: 15px;
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: 4px;
    color: #ff4d4f;
    margin-bottom: 20px;
    display: none;
}

/* 确认弹窗样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 15% auto;
    padding: 20px;
    border-radius: 6px;
    width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.close {
    position: absolute;
    right: 15px;
    top: 10px;
    font-size: 20px;
    font-weight: bold;
    color: #999;
    cursor: pointer;
}

.modal-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
}

.modal-message {
    margin-bottom: 20px;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
}

.modal-actions button {
    margin-left: 10px;
    padding: 6px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.cancel-btn {
    background-color: #fff;
    border: 1px solid #d9d9d9;
}

.cancel-btn:hover {
    color: #666;
    border-color: #666;
}

.confirm-btn {
    background-color: #ff4d4f;
    color: white;
    border: none;
}

.confirm-btn:hover {
    background-color: #ff7875;
}

/* 商户衣物页面样式 */
.merchant-header {
    display: flex;
    align-items: center;
    padding: 20px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    margin-bottom: 20px;
}

.merchant-header .merchant-avatar {
    width: 60px;
    height: 60px;
    margin-right: 15px;
}

.merchant-details {
    flex: 1;
}

.merchant-name {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.merchant-stats {
    display: flex;
    flex-wrap: wrap;
}

.merchant-stat {
    margin-right: 15px;
    color: #666;
    font-size: 14px;
}

.back-btn {
    display: inline-block;
    margin-bottom: 15px;
    padding: 6px 15px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background-color: #fff;
    color: #333;
    cursor: pointer;
    transition: all 0.3s;
}

.back-btn:hover {
    color: #1890ff;
    border-color: #1890ff;
}

.clothes-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

.clothes-item {
    background-color: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.3s;
}

.clothes-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.clothes-image-container {
    width: 100%;
    height: 200px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    position: relative;
}

.clothes-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
    cursor: pointer;
}

.clothes-image:hover {
    transform: scale(1.05);
}

.clothes-image-container::after {
    content: '👁️';
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.clothes-image-container:hover::after {
    opacity: 1;
}

.clothes-info {
    padding: 12px;
}

.clothes-name {
    font-weight: 500;
    margin-bottom: 4px;
    color: #333;
}

.clothes-category {
    font-size: 12px;
    color: #888;
}

.empty-state {
    text-align: center;
    padding: 40px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    color: #888;
}

/* 响应式调整 */
@media (max-width: 992px) {
    .clothes-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .clothes-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .data-table {
        display: block;
        overflow-x: auto;
    }
    
    .clothes-image-container {
        height: 180px;
    }
}

@media (max-width: 576px) {
    .clothes-grid {
        grid-template-columns: 1fr;
    }
    
    .search-container {
        flex-direction: column;
    }
    
    .search-input {
        border-radius: 4px;
        margin-bottom: 10px;
    }
    
    .search-btn {
        border-radius: 4px;
    }
    
    .clothes-image-container {
        height: 160px;
    }
} 