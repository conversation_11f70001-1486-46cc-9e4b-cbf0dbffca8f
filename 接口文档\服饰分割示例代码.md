服饰分割

本文档为您介绍服饰分割常用语言和常见情况的示例代码。


能力介绍
关于服饰分割的功能介绍以及具体调用参数说明，请参见服饰分割。

SDK包安装
常见语言的SDK依赖包信息，请参见SDK总览。

配置环境变量
配置环境变量ALIBABA_CLOUD_ACCESS_KEY_ID和ALIBABA_CLOUD_ACCESS_KEY_SECRET。

重要
阿里云账号AccessKey拥有所有API的访问权限，建议您使用RAM用户进行API访问或日常运维，具体操作，请参见创建RAM用户。

请不要将AccessKey ID和AccessKey Secret保存到工程代码里，否则可能导致AccessKey泄露，威胁您账号下所有资源的安全。

Linux和macOS系统配置方法

在IntelliJ IDEA中打开终端Terminal。

执行以下命令，配置环境变量。

<access_key_id>需替换为您RAM用户的AccessKey ID，<access_key_secret>替换为您RAM用户的AccessKey Secret。如果后续需要进行更多权限相关的配置，具体操作请参见使用RAM Policy控制访问权限。

 
export ALIBABA_CLOUD_ACCESS_KEY_ID=<access_key_id> 
export ALIBABA_CLOUD_ACCESS_KEY_SECRET=<access_key_secret>


文件在本地或可访问的URL
<?php

//安装依赖包
//composer require alibabacloud/imageseg-20191230

use AlibabaCloud\SDK\Imageseg\V20191230\Imageseg;
use \Exception;
use AlibabaCloud\Tea\Utils\Utils;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Imageseg\V20191230\Models\SegmentClothAdvanceRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use GuzzleHttp\Psr7\Stream;

class SegmentClothAdvance {

    /**
     * 使用AK&SK初始化账号Client
     * @param string $accessKeyId
     * @param string $accessKeySecret
     * @return Imageseg Client
     */
    public static function createClient($accessKeyId, $accessKeySecret){
        //初始化配置对象Darabonba\OpenApi\Models\Config。
        //Config对象存放accessKeyId、accessKeySecret、endpoint等配置
        $config = new Config([
            "accessKeyId" => $accessKeyId,
            "accessKeySecret" => $accessKeySecret
        ]);
        // 访问的域名
        $config->endpoint = "imageseg.cn-shanghai.aliyuncs.com";
        return new Imageseg($config);
    }

    /**
     * @param string[] $args
     * @return void
     */
    public static function main($args){
        // 创建AccessKey ID和AccessKey Secret，请参考https://help.aliyun.com/document_detail/175144.html
        // 如果您使用的是RAM用户的AccessKey，还需要为子账号授予权限AliyunVIAPIFullAccess，请参考https://help.aliyun.com/document_detail/145025.html
        // 从环境变量读取配置的AccessKey ID和AccessKey Secret。运行代码示例前必须先配置环境变量。
        $accessKeyId = getenv('ALIBABA_CLOUD_ACCESS_KEY_ID');
        $accessKeySecret = getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'); 
        $client = self::createClient($accessKeyId, $accessKeySecret);
        // 场景一，使用本地文件
        //$file = fopen('/tmp/SegmentCloth1.jpg', 'rb');
        //$stream = new Stream($file);
        // 场景二，使用任意可访问的url
        $file = fopen('http://viapi-test.oss-cn-shanghai.aliyuncs.com/viapi-3.0domepic/imageseg/SegmentCloth/SegmentCloth1.jpg', 'rb');
        $stream = new Stream($file);
        $segmentClothAdvanceRequest = new SegmentClothAdvanceRequest([
            "imageURLObject" => $stream
        ]);
        $runtime = new RuntimeOptions([]);
        try {
            $resp = $client->segmentClothAdvance($segmentClothAdvanceRequest, $runtime);
            # 获取整体结果
            echo Utils::toJSONString($resp->body);
        } catch (Exception $exception) {
            # 获取整体报错信息
            echo Utils::toJSONString($exception);
            # 获取单个字段
            echo $exception->getCode();
        }
    }
}
$path = __DIR__ . \DIRECTORY_SEPARATOR . '..' . \DIRECTORY_SEPARATOR . 'vendor' . \DIRECTORY_SEPARATOR . 'autoload.php';
if (file_exists($path)) {
    require_once $path;
}
//$argv是预留的数组入参参数，无实际意义，无需进行修改
SegmentClothAdvance::main(array_slice($argv, 1));