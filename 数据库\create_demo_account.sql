-- 创建体验账号SQL脚本
-- 该脚本用于确保ID为1的体验账号存在于数据库中

-- 检查是否存在ID为1的用户，如不存在则创建体验账号
INSERT INTO users (
    id,
    openid, 
    session_key, 
    unionid, 
    nickname, 
    avatar_url, 
    gender, 
    created_at, 
    updated_at
) 
SELECT 1, 'demo_user_openid', 'demo_session_key', NULL, '体验账号', '/images/default-avatar.png', 0, NOW(), NOW()
FROM dual
WHERE NOT EXISTS (SELECT 1 FROM users WHERE id = 1);

-- 确保体验账号信息正确
UPDATE users 
SET nickname = '体验账号', 
    avatar_url = '/images/default-avatar.png',
    updated_at = NOW()
WHERE id = 1;

-- 为体验账号添加示例衣物
INSERT INTO clothes (
    user_id, 
    name, 
    category, 
    image_url, 
    tags, 
    description, 
    created_at, 
    updated_at
)
SELECT 
    1, 
    '黑色T恤', 
    'tops', 
    '/images/demo/black-tshirt.jpg', 
    '基础款,日常,黑色', 
    '{\"color\":\"黑色\",\"brand\":\"示例品牌\",\"price\":\"99\"}', 
    NOW(), 
    NOW()
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM clothes 
    WHERE user_id = 1 
    AND name = '黑色T恤'
);

-- 更多示例衣物
INSERT INTO clothes (
    user_id, 
    name, 
    category, 
    image_url, 
    tags, 
    description, 
    created_at, 
    updated_at
)
SELECT 
    1, 
    '蓝色牛仔裤', 
    'pants', 
    '/images/demo/blue-jeans.jpg', 
    '牛仔,蓝色,休闲', 
    '{\"color\":\"蓝色\",\"brand\":\"示例品牌\",\"price\":\"129\"}', 
    NOW(), 
    NOW()
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM clothes 
    WHERE user_id = 1 
    AND name = '蓝色牛仔裤'
);

INSERT INTO clothes (
    user_id, 
    name, 
    category, 
    image_url, 
    tags, 
    description, 
    created_at, 
    updated_at
)
SELECT 
    1, 
    '白色衬衫', 
    'tops', 
    '/images/demo/white-shirt.jpg', 
    '正式,白色,商务', 
    '{\"color\":\"白色\",\"brand\":\"示例品牌\",\"price\":\"149\"}', 
    NOW(), 
    NOW()
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM clothes 
    WHERE user_id = 1 
    AND name = '白色衬衫'
);

INSERT INTO clothes (
    user_id, 
    name, 
    category, 
    image_url, 
    tags, 
    description, 
    created_at, 
    updated_at
)
SELECT 
    1, 
    '黑色皮鞋', 
    'shoes', 
    '/images/demo/black-shoes.jpg', 
    '正式,黑色,商务', 
    '{\"color\":\"黑色\",\"brand\":\"示例品牌\",\"price\":\"299\"}', 
    NOW(), 
    NOW()
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM clothes 
    WHERE user_id = 1 
    AND name = '黑色皮鞋'
);

INSERT INTO clothes (
    user_id, 
    name, 
    category, 
    image_url, 
    tags, 
    description, 
    created_at, 
    updated_at
)
SELECT 
    1, 
    '红色连衣裙', 
    'skirts', 
    '/images/demo/red-dress.jpg', 
    '正式,红色,派对', 
    '{\"color\":\"红色\",\"brand\":\"示例品牌\",\"price\":\"259\"}', 
    NOW(), 
    NOW()
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM clothes 
    WHERE user_id = 1 
    AND name = '红色连衣裙'
); 