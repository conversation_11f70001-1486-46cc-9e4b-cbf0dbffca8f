const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentCategory: 'all', // 当前选中的分类
    clothingList: [], // 衣物列表（显示用）
    allClothingList: [], // 所有衣物的完整列表（用于过滤）
    selectedClothes: [], // 已选中的衣物ID列表
    hasSelected: false, // 是否有选中的衣物
    loading: true, // 加载状态
    wardrobeId: null, // 衣橱ID
    wardrobeName: '', // 衣橱名称
    wardrobeDescription: '', // 衣橱描述
    showWardrobePicker: false, // 是否显示衣橱选择弹窗
    wardrobeList: [], // 衣橱列表
    loadingWardrobes: false, // 加载衣橱列表状态
    // 分类相关
    tabCategoryList: [], // 标签栏显示的分类数据
    showCategoryPicker: false, // 是否显示分类选择弹窗
    categoryList: [], // 分类列表
    loadingCategories: false, // 加载分类列表状态
    selectedWardrobeInfo: null, // 存储选中的衣橱信息，用于后续移动
    showClothingName: false // 是否显示衣物名称
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('衣橱衣物页面参数:', options);
    
    if (options.id) {
      // 获取衣橱ID和名称
      const id = options.id;
      const name = options.name ? decodeURIComponent(options.name) : '衣橱';
      
      // 设置显示名称状态
      const showClothingName = wx.getStorageSync('showClothingName') === true;
      console.log('衣橱衣物页面显示名称设置:', showClothingName);
      
      this.setData({
        wardrobeId: id,
        wardrobeName: name,
        showClothingName: showClothingName
      });
      
      // 加载分类数据（用于标签栏显示）
      this.loadTabCategories();
      
      // 加载衣物列表
      this.loadClothingList();
    } else {
      wx.showToast({
        title: '缺少衣橱ID参数',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 设置显示名称状态
    const showClothingName = app.globalData.showClothingName === true;
    console.log('衣橱衣物页面更新显示名称设置:', showClothingName);
    
    this.setData({
      showClothingName: showClothingName
    });
    
    // 如果app.globalData中有标记需要刷新衣物列表
    if (app.globalData.needRefreshClothes) {
      // 清除标记并重新加载数据
      app.globalData.needRefreshClothes = false;
      // 重新加载分类数据（可能有新增的自定义分类）
      this.loadTabCategories();
      // 重新加载衣物列表
      this.loadClothingList();
    }
  },

  /**
   * 加载衣物列表
   */
  loadClothingList: function () {
    const { wardrobeId } = this.data;
    
    if (!wardrobeId) {
      wx.showToast({
        title: '衣橱ID无效',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ loading: true });
    
    // 打印调试信息
    console.log('正在获取衣橱内衣物列表:', wardrobeId);
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothes.php`,
      method: 'GET',
      data: {
        wardrobe_id: wardrobeId
      },
      header: {
        'content-type': 'application/json',
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log('获取衣橱衣物列表响应:', res);
        
        if (res.statusCode === 200) {
          const clothingData = res.data.data || [];
          
          // 添加selected属性用于UI状态管理
          const clothingList = clothingData.map(item => ({
            ...item,
            selected: false,
            id: item.id || '',
            image_url: item.image_url || item.imageUrl || '',
            category: item.category || 'other'
          }));
          
          this.setData({
            clothingList: clothingList,
            allClothingList: clothingList,
            loading: false
          });
          
          // 应用分类过滤
          this.filterByCategory();
        } else {
          console.error('获取衣物列表失败:', res);
          wx.showToast({
            title: '获取衣物列表失败: ' + (res.data.msg || '未知错误'),
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: (err) => {
        console.error('网络请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  /**
   * 加载分类数据（用于标签栏显示）
   */
  loadTabCategories: function () {
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothing_categories.php`,
      method: 'GET',
      header: {
        'content-type': 'application/json',
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log('获取标签栏分类列表响应:', res);

        if (res.statusCode === 200 && !res.data.error) {
          // 构建包含"全部"选项的分类列表
          const allCategories = res.data.data || [];
          const tabCategories = [
            { code: 'all', name: '全部', is_system: true },
            ...allCategories
          ];
          
          this.setData({
            tabCategoryList: tabCategories
          });
        } else {
          console.error('获取分类列表失败:', res);
          // 如果加载失败，使用默认的固定分类
          this.setDefaultCategories();
        }
      },
      fail: (err) => {
        console.error('加载分类列表失败:', err);
        // 如果加载失败，使用默认的固定分类
        this.setDefaultCategories();
      }
    });
  },

  /**
   * 设置默认分类（兜底方案）
   */
  setDefaultCategories: function () {
    const defaultCategories = [
      { code: 'all', name: '全部', is_system: true },
      { code: 'tops', name: '上衣', is_system: true },
      { code: 'pants', name: '裤子', is_system: true },
      { code: 'skirts', name: '裙子', is_system: true },
      { code: 'coats', name: '外套', is_system: true },
      { code: 'shoes', name: '鞋子', is_system: true },
      { code: 'bags', name: '包包', is_system: true },
      { code: 'accessories', name: '配饰', is_system: true }
    ];
    
    this.setData({
      tabCategoryList: defaultCategories
    });
  },

  /**
   * 切换分类
   */
  switchCategory: function (e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      currentCategory: category
    });
    
    // 过滤显示当前分类的衣物
    this.filterByCategory();
  },

  /**
   * 根据分类过滤衣物
   */
  filterByCategory: function () {
    const { currentCategory, allClothingList } = this.data;
    
    if (currentCategory === 'all') {
      // 全部分类不需要过滤
      this.setData({
        clothingList: allClothingList
      });
    } else {
      // 根据分类过滤
      const filteredList = allClothingList.filter(item => {
        return item.category === currentCategory;
      });
      
      this.setData({
        clothingList: filteredList
      });
    }
  },

  /**
   * 切换选中状态
   */
  toggleSelect: function (e) {
    const id = e.currentTarget.dataset.id;
    const { clothingList, allClothingList } = this.data;
    
    // 更新当前显示列表中的选中状态
    const updatedList = clothingList.map(item => {
      if (item.id === id) {
        return { ...item, selected: !item.selected };
      }
      return item;
    });
    
    // 同时更新完整列表中对应项的选中状态
    const updatedAllList = allClothingList.map(item => {
      if (item.id === id) {
        return { ...item, selected: !item.selected };
      }
      return item;
    });
    
    // 获取所有选中的衣物ID
    const selectedClothes = updatedList.filter(item => item.selected).map(item => item.id);
    const hasSelected = selectedClothes.length > 0;
    
    this.setData({
      clothingList: updatedList,
      allClothingList: updatedAllList,
      selectedClothes,
      hasSelected
    });
  },

  /**
   * 编辑选中的衣物
   * 目前只支持一次编辑一件衣物
   */
  editSelected: function () {
    const { selectedClothes } = this.data;
    
    if (selectedClothes.length !== 1) {
      wx.showToast({
        title: '一次只能编辑一件衣物',
        icon: 'none'
      });
      return;
    }
    
    const clothingId = selectedClothes[0];
    console.log('准备编辑衣物ID:', clothingId);
    
    wx.navigateTo({
      url: `/pages/clothing/edit/edit?id=${clothingId}`,
      success: () => {
        console.log('成功跳转到编辑页面');
      }
    });
  },

  /**
   * 删除选中的衣物
   */
  deleteSelected: function () {
    const { selectedClothes } = this.data;
    
    if (selectedClothes.length === 0) {
      wx.showToast({
        title: '请至少选择一件衣物',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedClothes.length} 件衣物吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performDelete();
        }
      }
    });
  },

  /**
   * 执行删除操作
   */
  performDelete: function () {
    const { selectedClothes } = this.data;
    const totalCount = selectedClothes.length;
    
    wx.showLoading({
      title: `准备删除 ${totalCount} 件衣物...`,
    });
    
    console.log('准备删除衣物:', selectedClothes);
    
    // 创建Promise数组，每个Promise对应一个删除请求
    const deletePromises = [];
    let completedCount = 0;
    
    selectedClothes.forEach(id => {
      const promise = new Promise((resolve, reject) => {
        wx.request({
          url: `${app.globalData.apiBaseUrl}/delete_clothing.php`,
          method: 'POST',
          header: {
            'content-type': 'application/json',
            'Authorization': app.globalData.token
          },
          data: { id: id },
          success: (res) => {
            console.log(`删除衣物 ${id} 响应:`, res);
            
            completedCount++;
            // 更新加载提示
            if (completedCount < totalCount) {
              wx.showLoading({
                title: `删除中(${completedCount}/${totalCount})...`,
              });
            }
            
            // 处理响应是字符串的情况
            if (typeof res.data === 'string') {
              console.log('收到字符串响应，尝试判断删除是否成功');
              
              // 尝试提取并解析响应中的JSON对象
              try {
                // 检查是否包含成功状态
                if (res.data.includes('"error":false')) {
                  // 不再检查具体消息内容，只要有error:false就认为成功
                  console.log(`衣物 ${id} 删除成功`);
                  resolve({ id, success: true });
                } else {
                  console.log(`衣物 ${id} 删除失败，响应中无成功状态`);
                  reject({ 
                    id, 
                    success: false, 
                    error: '响应中无成功状态' 
                  });
                }
              } catch (err) {
                console.error(`解析响应失败: ${err.message}`);
                reject({ 
                  id, 
                  success: false, 
                  error: '响应格式异常' 
                });
              }
            } 
            // 处理响应是JSON对象的情况
            else if (res.statusCode === 200 && res.data.error === false) {
              console.log(`衣物 ${id} 删除成功`);
              resolve({ id, success: true });
            } else {
              console.log(`衣物 ${id} 删除失败`);
              reject({ 
                id, 
                success: false, 
                error: res.data.msg || '删除失败' 
              });
            }
          },
          fail: (err) => {
            console.error(`删除衣物 ${id} 请求失败:`, err);
            
            completedCount++;
            // 更新加载提示
            if (completedCount < totalCount) {
              wx.showLoading({
                title: `删除中(${completedCount}/${totalCount})...`,
              });
            }
            
            reject({ 
              id, 
              success: false, 
              error: err.errMsg || '网络请求失败' 
            });
          }
        });
      });
      
      deletePromises.push(promise);
    });
    
    // 使用Promise.all等待所有请求完成
    Promise.all(deletePromises.map(p => p.catch(e => e)))
      .then(results => {
        wx.hideLoading();
        
        // 统计成功和失败的数量
        const successResults = results.filter(r => r.success === true);
        const failedResults = results.filter(r => r.success === false);
        
        const successCount = successResults.length;
        const failCount = failedResults.length;
        
        console.log(`删除结果: 成功=${successCount}, 失败=${failCount}`);
        
        if (successCount > 0) {
          let message = `成功删除 ${successCount} 件衣物`;
          if (failCount > 0) {
            message += `，${failCount} 件删除失败`;
          }
          
          wx.showToast({
            title: message,
            icon: 'none',
            duration: 2000
          });
          
          // 只有在有成功删除的情况下才重新加载列表
          this.loadClothingList();
          
          // 清除选中状态
          this.setData({
            selectedClothes: [],
            hasSelected: false
          });
        } else if (failCount > 0) {
          // 全部删除失败的情况
          const firstError = failedResults[0].error;
          wx.showToast({
            title: `删除失败: ${firstError}`,
            icon: 'none',
            duration: 2000
          });
        }
      })
      .catch(error => {
        // 这里捕获的是Promise.all本身的错误，实际上前面的map处理应该已经避免了这种情况
        wx.hideLoading();
        console.error('批量删除操作失败:', error);
        wx.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      });
  },

  /**
   * 跳转到添加衣物页面
   */
  navigateToAdd: function () {
    const { wardrobeId } = this.data;
    
    wx.navigateTo({
      url: `/pages/clothing/add/add?wardrobe_id=${wardrobeId}`,
      success: () => {
        console.log('成功跳转到添加衣物页面，预设衣橱ID:', wardrobeId);
      }
    });
  },

  /**
   * 显示衣柜选择器
   */
  showWardrobePicker() {
    this.setData({
      showWardrobePicker: true
    });
    this.loadWardrobeList();
  },

  /**
   * 隐藏衣柜选择器
   */
  hideWardrobePicker() {
    this.setData({
      showWardrobePicker: false,
      wardrobeList: []
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation(e) {
    // 仅用于阻止事件冒泡
  },

  /**
   * 加载衣柜列表
   */
  loadWardrobeList() {
    this.setData({ loadingWardrobes: true });

    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_wardrobes.php`,
      method: 'GET',
      header: {
        'content-type': 'application/json',
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log('获取衣柜列表响应:', res);

        if (res.statusCode === 200) {
          // 不再过滤当前衣橱，而是标记它
          const wardrobeList = (res.data.data || []).map(wardrobe => {
            if (wardrobe.id === this.data.wardrobeId) {
              return {
                ...wardrobe,
                isCurrentWardrobe: true, // 标记当前衣橱
                name: `${wardrobe.name} (当前衣橱)`
              };
            }
            return wardrobe;
          });
          
          this.setData({
            wardrobeList,
            loadingWardrobes: false
          });
        } else {
          wx.showToast({
            title: res.data.msg || '获取衣柜列表失败',
            icon: 'none'
          });
          this.setData({ loadingWardrobes: false });
        }
      },
      fail: (err) => {
        console.error('加载衣柜列表失败:', err);
        wx.showToast({
          title: err.errMsg || '加载衣柜列表失败',
          icon: 'none'
        });
        this.setData({ loadingWardrobes: false });
      }
    });
  },

  /**
   * 选择衣柜并移动衣物
   */
  selectWardrobe(e) {
    const { id: targetWardrobeId, name: targetWardrobeName } = e.currentTarget.dataset;
    const { selectedClothes, wardrobeId } = this.data;

    if (!selectedClothes.length) {
      wx.showToast({
        title: '请先选择要移动的衣物',
        icon: 'none'
      });
      return;
    }

    // 检查是否是当前衣橱
    const isCurrentWardrobe = targetWardrobeId === wardrobeId;

    // 存储选中的衣橱信息，用于后续移动
    this.setData({
      selectedWardrobeInfo: {
        id: targetWardrobeId,
        name: targetWardrobeName.replace(' (当前衣橱)', ''), // 移除标记文本
        isCurrentWardrobe: isCurrentWardrobe
      }
    });

    // 无论是哪个衣橱，都显示分类选择弹窗
    this.hideWardrobePicker();
    this.showCategoryPicker();
  },

  /**
   * 执行移动操作
   */
  performMove(targetWardrobeId, targetWardrobeName, sourceWardrobeId, selectedClothes, categoryCode = null) {
    // 如果目标衣橱和源衣橱相同，但没有选择分类，则提示用户
    if (targetWardrobeId === sourceWardrobeId && !categoryCode) {
      wx.showToast({
        title: '请选择一个分类',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '正在移动衣物...',
      mask: true
    });

    // 先验证选中的衣物是否都存在
    wx.request({
      url: `${app.globalData.apiBaseUrl}/validate_clothes.php`,
      method: 'POST',
      data: {
        wardrobe_id: sourceWardrobeId,
        clothes_ids: selectedClothes
      },
      header: {
        'content-type': 'application/json',
        'Authorization': app.globalData.token
      },
      success: (validRes) => {
        if (validRes.statusCode === 200 && !validRes.data.error) {
          // 衣物验证通过，执行移动操作
          const moveData = {
            source_wardrobe_id: sourceWardrobeId,
            target_wardrobe_id: targetWardrobeId,
            clothes_ids: selectedClothes
          };

          // 如果指定了分类，添加到请求数据中
          if (categoryCode) {
            moveData.target_category = categoryCode;
          }

          // 如果是同一个衣橱，只更新分类不移动
          const isSameWardrobe = targetWardrobeId === sourceWardrobeId;
          
          // 对于同一衣橱内的操作，必须有分类，前面已经验证过了
          if (isSameWardrobe && !moveData.target_category) {
            wx.hideLoading();
            wx.showToast({
              title: '需要指定目标分类',
              icon: 'none'
            });
            return;
          }
          
          const apiEndpoint = isSameWardrobe 
            ? `${app.globalData.apiBaseUrl}/update_clothes_category.php` 
            : `${app.globalData.apiBaseUrl}/move_clothes.php`;

          console.log('发送请求数据:', moveData);

          wx.request({
            url: apiEndpoint,
            method: 'POST',
            data: moveData,
            header: {
              'content-type': 'application/json',
              'Authorization': app.globalData.token
            },
            success: (res) => {
              console.log(isSameWardrobe ? '更新分类响应:' : '移动衣物响应:', res);

              if (res.statusCode === 200 && !res.data.error) {
                // 根据操作类型生成不同的成功消息
                let successMessage;
                if (isSameWardrobe) {
                  successMessage = `已更新为"${categoryCode}"分类`;
                } else if (categoryCode) {
                  successMessage = `已移动到${targetWardrobeName}并更新分类`;
                } else {
                  successMessage = `已移动到${targetWardrobeName}`;
                }
                
                wx.showToast({
                  title: successMessage,
                  icon: 'success'
                });
                
                // 重置选中状态
                this.setData({
                  selectedClothes: [],
                  hasSelected: false
                });
                
                // 刷新衣物列表
                this.loadClothingList();
              } else {
                wx.showToast({
                  title: res.data.msg || (isSameWardrobe ? '更新分类失败' : '移动衣物失败'),
                  icon: 'none',
                  duration: 2000
                });
              }
            },
            fail: (err) => {
              console.error('移动衣物失败:', err);
              wx.showToast({
                title: err.errMsg || '移动衣物失败',
                icon: 'none',
                duration: 2000
              });
            }
          });
        } else {
          // 衣物验证失败
          wx.showToast({
            title: validRes.data.msg || '部分衣物不存在或已被移除',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        console.error('验证衣物失败:', err);
        wx.showToast({
          title: '验证衣物失败，请重试',
          icon: 'none',
          duration: 2000
        });
      },
      complete: () => {
        wx.hideLoading();
        this.hideCategoryPicker();
      }
    });
  },

  /**
   * 显示分类选择器
   */
  showCategoryPicker() {
    this.setData({
      showCategoryPicker: true
    });
    this.loadCategoriesForWardrobe();
  },

  /**
   * 隐藏分类选择器
   */
  hideCategoryPicker() {
    this.setData({
      showCategoryPicker: false,
      categoryList: [],
      selectedWardrobeInfo: null
    });
  },

  /**
   * 加载分类列表
   */
  loadCategoriesForWardrobe() {
    // 如果已经有标签栏的分类数据，直接复用
    if (this.data.tabCategoryList.length > 0) {
      // 过滤掉"全部"选项，只保留实际的分类
      const categoryList = this.data.tabCategoryList.filter(item => item.code !== 'all');
      this.setData({
        categoryList: categoryList,
        loadingCategories: false
      });
      return;
    }

    // 否则重新加载分类数据
    this.setData({ loadingCategories: true });

    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothing_categories.php`,
      method: 'GET',
      header: {
        'content-type': 'application/json',
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log('获取分类列表响应:', res);

        if (res.statusCode === 200 && !res.data.error) {
          this.setData({
            categoryList: res.data.data || [],
            loadingCategories: false
          });
        } else {
          wx.showToast({
            title: res.data.msg || '获取分类列表失败',
            icon: 'none'
          });
          this.setData({ loadingCategories: false });
        }
      },
      fail: (err) => {
        console.error('加载分类列表失败:', err);
        wx.showToast({
          title: err.errMsg || '加载分类列表失败',
          icon: 'none'
        });
        this.setData({ loadingCategories: false });
      }
    });
  },

  /**
   * 选择分类并执行移动
   */
  selectCategory(e) {
    const { code: categoryCode, name: categoryName } = e.currentTarget.dataset;
    const { selectedWardrobeInfo, selectedClothes, wardrobeId: sourceWardrobeId } = this.data;

    if (!selectedWardrobeInfo) {
      wx.showToast({
        title: '请先选择目标衣橱',
        icon: 'none'
      });
      return;
    }

    // 添加确认对话框
    let confirmMessage;
    if (selectedWardrobeInfo.isCurrentWardrobe) {
      // 当前衣橱内移动
      confirmMessage = `确定将选中的 ${selectedClothes.length} 件衣物更改为"${categoryName}"分类吗？`;
    } else {
      // 跨衣橱移动
      confirmMessage = `确定将选中的 ${selectedClothes.length} 件衣物移动到"${selectedWardrobeInfo.name}"的"${categoryName}"分类吗？`;
    }

    wx.showModal({
      title: selectedWardrobeInfo.isCurrentWardrobe ? '确认更改分类' : '确认移动',
      content: confirmMessage,
      success: (res) => {
        if (res.confirm) {
          this.performMove(
            selectedWardrobeInfo.id, 
            selectedWardrobeInfo.name, 
            sourceWardrobeId, 
            selectedClothes,
            categoryCode
          );
        }
      }
    });
  },
}); 