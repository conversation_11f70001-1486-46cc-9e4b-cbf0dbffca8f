<?php
/**
 * 最终综合测试分类显示
 * 验证所有修复是否生效
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "=== 最终综合测试 ===\n";
    echo "当前用户ID: $userId\n";
    echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";
    
    // 1. 基础数据检查
    echo "1. 基础数据检查:\n";
    
    // 检查用户圈子
    $stmt = $conn->prepare("SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $userCircles = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($userCircles)) {
        echo "❌ 用户不在任何活跃圈子中，无法测试共享数据\n";
        exit;
    }
    
    echo "✅ 用户活跃圈子: " . implode(', ', $userCircles) . "\n";
    $circleId = $userCircles[0];
    
    // 检查圈子中的自定义分类
    $stmt = $conn->prepare("
        SELECT COUNT(*) as total_count,
               COUNT(CASE WHEN user_id = :user_id THEN 1 END) as own_count,
               COUNT(CASE WHEN user_id != :user_id THEN 1 END) as others_count
        FROM clothing_categories
        WHERE is_system = 0 AND circle_id = :circle_id
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->execute();
    $customStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "圈子中自定义分类统计:\n";
    echo "- 总数: {$customStats['total_count']}\n";
    echo "- 自己的: {$customStats['own_count']}\n";
    echo "- 其他用户的: {$customStats['others_count']}\n\n";
    
    if ($customStats['others_count'] == 0) {
        echo "⚠️ 圈子中没有其他用户的自定义分类，建议先运行数据同步修复\n";
        echo "运行: GET /fix_category_sync.php?action=fix\n\n";
    }
    
    // 2. 测试API调用
    echo "2. 测试API调用:\n";
    
    // 模拟API的完整逻辑
    $includeCircleData = true;
    $dataSource = 'shared';
    
    // 执行与API相同的查询
    if (empty($userCircles)) {
        $sql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                       u.nickname as creator_nickname, 'system' as data_source
                FROM clothing_categories c
                LEFT JOIN users u ON c.user_id = u.id
                WHERE c.is_system = 1 AND c.user_id = :user_id
                ORDER BY c.sort_order ASC, c.created_at ASC";
    } else {
        $circleIds = implode(',', $userCircles);
        $sql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                       u.nickname as creator_nickname,
                       CASE
                           WHEN c.is_system = 1 THEN 'system'
                           WHEN c.circle_id IS NULL THEN 'personal'
                           ELSE 'shared'
                       END as data_source
                FROM clothing_categories c
                LEFT JOIN users u ON c.user_id = u.id
                WHERE (
                    -- 当前用户的系统分类（用于显示系统分类下的共享衣物）
                    (c.is_system = 1 AND c.user_id = :user_id) OR
                    -- 其他用户的自定义分类（已同步到圈子的）
                    (c.is_system = 0 AND c.user_id != :user_id AND c.circle_id IN ($circleIds))
                )
                ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC";
    }
    
    echo "执行SQL查询...\n";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "查询结果数量: " . count($result) . "\n";
    
    $systemCount = 0;
    $customCount = 0;
    
    foreach ($result as $cat) {
        if ($cat['is_system']) {
            $systemCount++;
            echo "- [系统] {$cat['name']} (code: {$cat['code']}, data_source: {$cat['data_source']})\n";
        } else {
            $customCount++;
            echo "- [自定义] {$cat['name']} (code: {$cat['code']}, 创建者: {$cat['creator_nickname']}, data_source: {$cat['data_source']})\n";
        }
    }
    
    echo "统计: 系统分类 $systemCount 个，自定义分类 $customCount 个\n\n";
    
    // 3. 验证结果
    echo "3. 验证结果:\n";
    
    $success = true;
    $issues = [];
    
    if ($systemCount == 0) {
        $success = false;
        $issues[] = "系统分类缺失";
    } else {
        echo "✅ 系统分类显示正常 ($systemCount 个)\n";
    }
    
    if ($customStats['others_count'] > 0) {
        if ($customCount == 0) {
            $success = false;
            $issues[] = "其他用户的自定义分类没有显示";
        } elseif ($customCount < $customStats['others_count']) {
            $success = false;
            $issues[] = "部分自定义分类缺失 ($customCount/{$customStats['others_count']})";
        } else {
            echo "✅ 其他用户自定义分类显示正常 ($customCount 个)\n";
        }
    } else {
        echo "⚠️ 圈子中没有其他用户的自定义分类可供测试\n";
    }
    
    // 4. 实际API调用测试
    echo "\n4. 实际API调用测试:\n";
    
    // 构建API URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['REQUEST_URI']);
    $apiUrl = "$protocol://$host$path/get_clothing_categories.php?include_circle_data=true&data_source=shared";
    
    echo "API URL: $apiUrl\n";
    
    // 发起API请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: ' . $_SERVER['HTTP_AUTHORIZATION'],
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ API调用失败: $error\n";
    } elseif ($httpCode !== 200) {
        echo "❌ API返回错误状态码: $httpCode\n";
        echo "响应内容: $response\n";
    } else {
        $apiData = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "❌ API响应JSON解析失败: " . json_last_error_msg() . "\n";
        } else {
            echo "✅ API调用成功\n";
            echo "错误状态: " . ($apiData['error'] ? 'true' : 'false') . "\n";
            
            if (isset($apiData['data'])) {
                $apiCategories = $apiData['data'];
                $apiSystemCount = 0;
                $apiCustomCount = 0;
                
                foreach ($apiCategories as $cat) {
                    if ($cat['is_system']) {
                        $apiSystemCount++;
                    } else {
                        $apiCustomCount++;
                    }
                }
                
                echo "API返回分类数量: " . count($apiCategories) . "\n";
                echo "API统计: 系统分类 $apiSystemCount 个，自定义分类 $apiCustomCount 个\n";
                
                // 对比SQL查询和API结果
                if ($systemCount == $apiSystemCount && $customCount == $apiCustomCount) {
                    echo "✅ API结果与SQL查询一致\n";
                } else {
                    echo "⚠️ API结果与SQL查询不一致\n";
                    echo "SQL: 系统 $systemCount, 自定义 $customCount\n";
                    echo "API: 系统 $apiSystemCount, 自定义 $apiCustomCount\n";
                }
            }
        }
    }
    
    // 5. 最终结论
    echo "\n5. 最终结论:\n";
    
    if ($success && $systemCount > 0) {
        echo "🎉 测试通过！共享数据模式下的分类显示已修复\n";
        echo "- 系统分类正常显示: $systemCount 个\n";
        echo "- 其他用户自定义分类正常显示: $customCount 个\n";
        echo "- 前端现在应该能看到完整的分类列表\n";
    } else {
        echo "❌ 测试未完全通过，存在以下问题:\n";
        foreach ($issues as $issue) {
            echo "- $issue\n";
        }
        
        echo "\n建议修复步骤:\n";
        if (in_array("系统分类缺失", $issues)) {
            echo "1. 检查用户系统分类初始化\n";
        }
        if (strpos(implode(' ', $issues), "自定义分类") !== false) {
            echo "2. 运行数据同步修复: GET /fix_category_sync.php?action=fix\n";
            echo "3. 让其他用户创建一些自定义分类进行测试\n";
        }
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
?>
