<?php
header("Content-Type: application/json");
require_once './db.php';
require_once './auth.php';
require_once './config.php';

// 初始化响应数组
$response = [
    'code' => 0,
    'message' => 'success',
    'data' => []
];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['code'] = 405;
    $response['message'] = 'Method Not Allowed';
    echo json_encode($response);
    exit;
}

// 获取认证头
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';

// 验证Bearer令牌格式
if (empty($authHeader) || strpos($authHeader, 'Bearer ') !== 0) {
    $response['code'] = 401;
    $response['message'] = '无效的认证头';
    echo json_encode($response);
    exit;
}

// 提取令牌
$token = substr($authHeader, 7); // 去掉'Bearer '前缀

// 获取POST参数
$postData = json_decode(file_get_contents("php://input"), true) ?: [];
$merchantId = isset($postData['merchant_id']) ? intval($postData['merchant_id']) : 0;
$keyword = isset($postData['keyword']) ? trim($postData['keyword']) : '';
$page = isset($postData['page']) ? intval($postData['page']) : 1;
$limit = isset($postData['limit']) ? intval($postData['limit']) : 12;

// 记录日志，帮助调试
error_log("admin_get_merchant_clothes.php - 请求参数: " . json_encode($postData));

// 验证参数
if (empty($merchantId)) {
    $response['code'] = 400;
    $response['message'] = '缺少商户ID参数';
    echo json_encode($response);
    exit;
}

// 验证管理员令牌
$auth = new Auth();
$adminInfo = $auth->verifyAdminToken($token);

if ($adminInfo === false) {
    $response['code'] = 401;
    $response['message'] = '无效或已过期的管理员令牌';
    echo json_encode($response);
    exit;
}

// 记录日志，帮助调试
error_log("admin_get_merchant_clothes.php - 管理员信息: " . json_encode($adminInfo));

$db = new Database();
$conn = $db->getConnection();

try {
    // 先检查商户是否存在
    $checkMerchantSql = "SELECT id FROM users WHERE id = ?";
    $checkMerchantStmt = $conn->prepare($checkMerchantSql);
    $checkMerchantStmt->bindValue(1, $merchantId, PDO::PARAM_INT);
    $checkMerchantStmt->execute();
    
    if (!$checkMerchantStmt->fetch()) {
        $response['code'] = 404;
        $response['message'] = '商户不存在';
        echo json_encode($response);
        exit;
    }
    
    $offset = ($page - 1) * $limit;
    
    // 构建SQL查询 - 移除不存在的is_deleted字段
    $sql = "SELECT c.id, c.name, c.description, c.image_url, c.category, c.created_at, c.updated_at
            FROM clothes c
            WHERE c.user_id = ?";
    
    $params = [$merchantId];
    
    // 如果有关键词，添加搜索条件
    if (!empty($keyword)) {
        $sql .= " AND (c.name LIKE ? OR c.description LIKE ? OR c.category LIKE ?)";
        $searchKeyword = "%{$keyword}%";
        $params[] = $searchKeyword;
        $params[] = $searchKeyword;
        $params[] = $searchKeyword;
    }
    
    // 添加排序和分页
    $sql .= " ORDER BY c.id DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $conn->prepare($sql);
    
    // 使用位置参数绑定
    if (!empty($params)) {
        for ($i = 0; $i < count($params); $i++) {
            $paramType = is_int($params[$i]) ? PDO::PARAM_INT : PDO::PARAM_STR;
            $stmt->bindValue($i + 1, $params[$i], $paramType);
        }
    }
    
    $stmt->execute();
    $clothes = [];
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $clothes[] = [
            'id' => $row['id'],
            'name' => $row['name'] ?: '未命名衣物',
            'description' => $row['description'],
            'image_url' => $row['image_url'],
            'category' => $row['category'],
            'created_at' => $row['created_at'],
            'updated_at' => $row['updated_at']
        ];
    }
    
    // 获取总数量
    $countSql = "SELECT COUNT(*) as total FROM clothes WHERE user_id = ?";
    $countParams = [$merchantId];
    
    // 如果有关键词，添加搜索条件
    if (!empty($keyword)) {
        $countSql .= " AND (name LIKE ? OR description LIKE ? OR category LIKE ?)";
        $countParams[] = $searchKeyword;
        $countParams[] = $searchKeyword;
        $countParams[] = $searchKeyword;
    }
    
    $countStmt = $conn->prepare($countSql);
    
    // 使用位置参数绑定
    if (!empty($countParams)) {
        for ($i = 0; $i < count($countParams); $i++) {
            $paramType = is_int($countParams[$i]) ? PDO::PARAM_INT : PDO::PARAM_STR;
            $countStmt->bindValue($i + 1, $countParams[$i], $paramType);
        }
    }
    
    $countStmt->execute();
    $totalRow = $countStmt->fetch(PDO::FETCH_ASSOC);
    $total = $totalRow['total'];
    
    $response['data'] = [
        'list' => $clothes,
        'total' => $total,
        'page' => $page,
        'limit' => $limit
    ];
} catch (Exception $e) {
    $response['code'] = 500;
    $response['message'] = '处理请求时发生错误: ' . $e->getMessage();
    error_log("admin_get_merchant_clothes.php - 错误: " . $e->getMessage());
    error_log("admin_get_merchant_clothes.php - 错误详情: " . $e->getTraceAsString());
} finally {
    // PDO connections are closed automatically when the variable is unset
    $conn = null;
}

echo json_encode($response); 