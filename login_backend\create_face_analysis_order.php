<?php
/**
 * 创建面容分析订单API
 * 
 * 用于创建面容分析订单
 * 
 * 请求方法：POST
 * 请求参数：无
 * 
 * 返回：
 * {
 *   "error": false,
 *   "data": {
 *     "analysis_id": "分析ID",
 *     "amount": 1.00
 *   }
 * }
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';
require_once 'wx_pay_helper.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => '只支持POST请求'
    ]);
    exit;
}

// 验证用户身份
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供授权Token'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
$auth = new Auth();
$verifyResult = $auth->verifyToken($token);

if ($verifyResult === false) {
    echo json_encode([
        'error' => true,
        'msg' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $verifyResult['sub']; // 从验证结果中获取用户ID

try {
    $db = new Database();
    $conn = $db->getConnection();

    // 获取用户信息
    $stmt = $conn->prepare("SELECT nickname FROM users WHERE id = :user_id");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    $userNickname = $user ? $user['nickname'] : '';

    // 创建微信支付订单
    $wxPayHelper = new WxPayHelper();

    // 获取用户IP
    $userIp = $_SERVER['REMOTE_ADDR'];

    // 生成面容分析订单号
    $outTradeNo = 'FACE' . date('YmdHis') . $userId . rand(1000, 9999);

    // 订单描述
    $description = "面容分析服务";

    // 金额换算为分
    $amountInCents = 100; // 1元 = 100分

    // 构建请求数据
    $requestData = [
        'appid' => WX_APPID,
        'mchid' => WX_MCH_ID,
        'description' => $description,
        'out_trade_no' => $outTradeNo,
        'notify_url' => WX_PAY_NOTIFY_URL,
        'amount' => [
            'total' => $amountInCents,
            'currency' => 'CNY'
        ],
        'payer' => [
            'openid' => $wxPayHelper->getUserOpenId($userId)
        ]
    ];

    // 调用微信支付API
    $result = $wxPayHelper->callWxPayApi('v3/pay/transactions/jsapi', $requestData);

    if (isset($result['error']) && $result['error']) {
        echo json_encode([
            'error' => true,
            'msg' => $result['msg']
        ]);
        exit;
    }

    // 生成支付参数
    $payParams = $wxPayHelper->generatePayParams($result['prepay_id']);

    // 创建面容分析记录
    $stmt = $conn->prepare("
        INSERT INTO face_analysis (
            user_id,
            status,
            payment_status,
            order_id,
            amount,
            usage_status,
            created_at
        ) VALUES (
            :user_id,
            'pending',
            'unpaid',
            :order_id,
            1.00,
            'unused',
            NOW()
        )
    ");

    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':order_id', $outTradeNo);

    if (!$stmt->execute()) {
        echo json_encode([
            'error' => true,
            'msg' => '创建分析记录失败'
        ]);
        exit;
    }

    $analysisId = $conn->lastInsertId();

    // 返回分析记录信息和支付参数
    echo json_encode([
        'error' => false,
        'data' => [
            'analysis_id' => $analysisId,
            'order_id' => $outTradeNo,
            'amount' => 1.00,
            'pay_params' => $payParams
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '创建分析记录失败: ' . $e->getMessage()
    ]);
}
?>
