.container {
  padding: 40rpx 30rpx;
  background-color: #f9f9f9;
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 60rpx;
  color: #333;
}

.options {
  width: 100%;
  margin-top: 40rpx;
}

.option-item {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 30rpx;
}

.iconfont {
  font-size: 40rpx;
  color: #333;
}

.icon-camera:before {
  content: "\e64d";
}

.icon-album:before {
  content: "\e64c";
}

.option-text {
  font-size: 32rpx;
  color: #333;
}

.bottom-button {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 30rpx;
  padding: 0 30rpx;
}

.bottom-button button {
  background-color: #fff;
  color: #333;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: 1px solid #e1e1e1;
}
