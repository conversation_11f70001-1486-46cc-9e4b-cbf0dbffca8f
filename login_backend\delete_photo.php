<?php
/**
 * Delete Photo API
 * 
 * Deletes a photo for the user
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * GET/POST Parameters:
 * - id: The photo ID to delete (required)
 * 
 * Response:
 * {
 *   "error": false,
 *   "msg": "Photo deleted successfully"
 * }
 */

require_once 'config.php';
require_once 'verify_token.php';
require_once 'db.php';
require_once '../vendor/autoload.php'; // 引入阿里云OSS SDK
require_once 'oss_helper.php';

// 引入OSS命名空间
use OSS\OssClient;
use OSS\Core\OssException;

// Set response content type
header('Content-Type: application/json');

// Handle CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if Authorization header exists
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// Verify token
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid or expired token'
    ]);
    exit;
}

// Get user ID from token data
$userId = $tokenData['sub'];

// Get photo ID from GET or POST parameters
$photoId = isset($_GET['id']) ? $_GET['id'] : (isset($_POST['id']) ? $_POST['id'] : null);

if (!$photoId) {
    echo json_encode([
        'error' => true,
        'msg' => 'Photo ID is required'
    ]);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();

    // First check if the photo exists and belongs to this user
    $checkSql = "SELECT id, image_url FROM photos WHERE id = :id AND user_id = :user_id";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->execute([
        'id' => $photoId,
        'user_id' => $userId
    ]);
    
    $photo = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$photo) {
        echo json_encode([
            'error' => true,
            'msg' => 'Photo not found or you do not have permission to delete it'
        ]);
        exit;
    }
    
    // Get the image URL from the photo record
    $imageUrl = $photo['image_url'];
    
    // 初始化OSS辅助类
    $ossHelper = new OssHelper();
    
    // 检查是否为OSS URL，如果是则删除OSS对象
    if ($ossHelper->isOssUrl($imageUrl)) {
        $ossKey = $ossHelper->getKeyFromUrl($imageUrl);
        if ($ossKey) {
            $deleteResult = $ossHelper->deleteFile($ossKey);
            if ($deleteResult['success']) {
                error_log("OSS文件已删除: $ossKey");
            } else {
                error_log("OSS文件删除失败: " . $deleteResult['error']);
            }
        }
    } else {
        // 如果是旧格式的URL，尝试删除本地文件
        $urlParts = parse_url($imageUrl);
        if (isset($urlParts['path'])) {
            $relativePath = ltrim($urlParts['path'], '/');
            // If the URL contains login_backend, extract the part after it
            if (strpos($relativePath, 'login_backend/') === 0) {
                $relativePath = substr($relativePath, strlen('login_backend/'));
            }
            $filePath = __DIR__ . '/' . $relativePath;
            
            if (file_exists($filePath)) {
                @unlink($filePath);
                error_log("本地照片文件已删除: $filePath");
            } else {
                error_log("本地照片文件未找到或无法删除: $filePath");
            }
        }
    }
    
    // Delete from database
    $sql = "DELETE FROM photos WHERE id = :id AND user_id = :user_id";
    $stmt = $conn->prepare($sql);
    $result = $stmt->execute([
        'id' => $photoId,
        'user_id' => $userId
    ]);
    
    if ($result) {
        echo json_encode([
            'error' => false,
            'msg' => 'Photo deleted successfully'
        ]);
    } else {
        echo json_encode([
            'error' => true,
            'msg' => 'Failed to delete photo'
        ]);
    }
} catch (PDOException $e) {
    // 详细记录错误信息
    $errorMessage = $e->getMessage();
    $errorCode = $e->getCode();
    error_log("删除照片错误: Code=$errorCode, Message=$errorMessage");

    // Return error response
    echo json_encode([
        'error' => true,
        'msg' => "删除照片失败: $errorCode - " . substr($errorMessage, 0, 100)
    ]);
} catch (Exception $e) {
    // 其他错误
    error_log("删除照片失败: " . $e->getMessage());
    echo json_encode([
        'error' => true,
        'msg' => '删除照片失败: ' . $e->getMessage()
    ]);
}
?>
