<?php
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] !== 'POST' && $_SERVER['REQUEST_METHOD'] !== 'DELETE') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

try {
    $auth = new Auth();
    
    // 验证用户token
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? null;
    
    if (!$authHeader || strpos($authHeader, 'Bearer ') !== 0) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => '未授权访问']);
        exit;
    }
    
    $token = substr($authHeader, 7);
    $tokenData = $auth->verifyToken($token);
    
    if (!$tokenData) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => 'Token无效']);
        exit;
    }
    
    $userId = $tokenData['user_id'] ?? $tokenData['sub'];
    
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => true, 'msg' => '无效的请求数据']);
        exit;
    }
    
    $categoryId = (int)($input['id'] ?? 0);
    
    // 验证必填字段
    if ($categoryId <= 0) {
        http_response_code(400);
        echo json_encode(['error' => true, 'msg' => '分类ID无效']);
        exit;
    }
    
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查分类是否存在且属于该用户
    $checkSql = "SELECT id, name, code, is_system FROM clothing_categories WHERE id = :id AND user_id = :user_id";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bindParam(':id', $categoryId, PDO::PARAM_INT);
    $checkStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkStmt->execute();
    
    if ($checkStmt->rowCount() === 0) {
        http_response_code(404);
        echo json_encode(['error' => true, 'msg' => '分类不存在或无权限删除']);
        exit;
    }
    
    $category = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    // 检查是否有衣物使用了这个分类代码（检查新旧两种方式）
    $checkClothesSql = "SELECT COUNT(*) as count FROM clothes WHERE (category = :code OR category_id = :id) AND user_id = :user_id";
    $checkClothesStmt = $conn->prepare($checkClothesSql);
    $checkClothesStmt->bindParam(':code', $category['code'], PDO::PARAM_STR);
    $checkClothesStmt->bindParam(':id', $categoryId, PDO::PARAM_INT);
    $checkClothesStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkClothesStmt->execute();
    $clothesData = $checkClothesStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($clothesData['count'] > 0) {
        http_response_code(400);
        echo json_encode([
            'error' => true,
            'msg' => '无法删除该分类，还有 ' . $clothesData['count'] . ' 件衣物使用了此分类。请先将这些衣物移至其他分类或删除，然后再尝试删除该分类。'
        ]);
        exit;
    }
    
    // 删除分类
    $deleteSql = "DELETE FROM clothing_categories WHERE id = :id AND user_id = :user_id";
    $deleteStmt = $conn->prepare($deleteSql);
    $deleteStmt->bindParam(':id', $categoryId, PDO::PARAM_INT);
    $deleteStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    
    if ($deleteStmt->execute()) {
        if ($deleteStmt->rowCount() > 0) {
            // 记录所有删除的分类到deleted_system_categories表中
            // 不再限制只记录系统分类
            try {
                // 创建表如果不存在
                $createTableSql = "CREATE TABLE IF NOT EXISTS deleted_system_categories (
                    id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    code VARCHAR(50) NOT NULL,
                    deleted_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_user_code (user_id, code)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
                $conn->exec($createTableSql);
                
                // 插入记录
                $insertSql = "INSERT INTO deleted_system_categories (user_id, code) 
                              VALUES (:user_id, :code)
                              ON DUPLICATE KEY UPDATE deleted_at = CURRENT_TIMESTAMP";
                $insertStmt = $conn->prepare($insertSql);
                $insertStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
                $insertStmt->bindParam(':code', $category['code'], PDO::PARAM_STR);
                $insertStmt->execute();
            } catch (Exception $e) {
                // 记录错误但不影响删除操作的成功响应
                error_log("记录已删除分类时出错: " . $e->getMessage());
            }
            
            echo json_encode([
                'error' => false,
                'msg' => '分类删除成功'
            ]);
        } else {
            http_response_code(404);
            echo json_encode(['error' => true, 'msg' => '分类不存在或已被删除']);
        }
    } else {
        http_response_code(500);
        echo json_encode(['error' => true, 'msg' => '删除分类失败']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '服务器错误: ' . $e->getMessage()
    ]);
} 