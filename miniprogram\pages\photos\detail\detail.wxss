.container {
  background-color: #f9f9f9;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.photo-container {
  flex: 1;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding: 20rpx 0;
}

.photo-image {
  width: 100%;
  height: 80vh;
  object-fit: contain;
}

.photo-type {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background-color: rgba(59, 130, 246, 0.8);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  z-index: 10;
}

.photo-type.half {
  background-color: rgba(59, 130, 246, 0.8);
}

.photo-info {
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
  font-size: 28rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  color: #333;
}

.footer-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  width: 100rpx;
  height: 100rpx;
}

.action-icon {
  background-color: #ff3b30;
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.iconfont {
  font-size: 48rpx;
  color: #fff;
}

.icon-share:before {
  content: "\e62b";
}

.icon-delete:before {
  content: "\e646";
}

.action-text {
  font-size: 24rpx;
  color: #333;
  margin-top: 8rpx;
}

.delete {
  color: #ff3b30;
}

.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f9f9f9;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.delete-icon {
  display: block;
  width: 30rpx;
  height: 36rpx;
  position: relative;
  border: 2px solid #fff;
  border-radius: 2px;
}
