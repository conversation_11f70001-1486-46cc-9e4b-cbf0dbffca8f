/**
 * 商户衣物管理模块
 */
const MerchantClothes = {
    // 商户ID
    merchantId: null,
    
    // 商户信息
    merchantInfo: null,
    
    // 当前页码
    currentPage: 1,
    
    // 每页条数
    pageSize: 12,
    
    // 搜索关键词
    keyword: '',
    
    // 总记录数
    totalRecords: 0,
    
    // 初始化
    init: function() {
        // 获取URL参数中的商户ID
        const urlParams = new URLSearchParams(window.location.search);
        this.merchantId = urlParams.get('id');
        
        if (!this.merchantId) {
            alert('未指定商户ID，请返回商户列表页');
            window.location.href = 'merchant_list.html';
            return;
        }
        
        // 获取DOM元素
        this.searchInput = document.getElementById('searchKeyword');
        this.searchBtn = document.getElementById('searchBtn');
        this.clothesGrid = document.getElementById('clothesGrid');
        this.merchantInfoEl = document.getElementById('merchantInfo');
        this.paginationContainer = document.getElementById('paginationContainer');
        this.loadingIndicator = document.getElementById('clothesLoading');
        this.errorMessage = document.getElementById('clothesError');
        this.emptyState = document.getElementById('emptyState');
        
        // 绑定事件
        this.bindEvents();
        
        // 加载商户信息
        this.loadMerchantInfo();
        
        // 加载衣物列表
        this.loadClothesList();
    },
    
    // 绑定事件
    bindEvents: function() {
        // 搜索按钮点击事件
        this.searchBtn.addEventListener('click', () => {
            this.keyword = this.searchInput.value.trim();
            this.currentPage = 1;
            this.loadClothesList();
        });
        
        // 搜索框回车事件
        this.searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.keyword = this.searchInput.value.trim();
                this.currentPage = 1;
                this.loadClothesList();
            }
        });
    },
    
    // 加载商户信息
    loadMerchantInfo: function() {
        // 构建API URL - 使用管理员专用API路径
        const url = new URL('../login_backend/admin_get_merchant_info.php', window.location.origin);
        
        // 准备请求数据
        const requestData = {
            merchant_id: this.merchantId
        };
        
        // 发送请求
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + Auth.getToken() // 使用Bearer认证方式
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                this.merchantInfo = data.data;
                this.renderMerchantInfo();
            } else {
                alert(`获取商户信息失败: ${data.message || '未知错误'}`);
            }
        })
        .catch(error => {
            alert(`获取商户信息失败: ${error.message}`);
            console.error('Error loading merchant info:', error);
        });
    },
    
    // 渲染商户信息
    renderMerchantInfo: function() {
        if (!this.merchantInfo) return;
        
        // 默认头像
        const avatarUrl = this.merchantInfo.avatar_url || 'https://cyymj.oss-cn-shanghai.aliyuncs.com/images/default-avatar.png';
        
        // 更新页面标题
        document.title = `${this.merchantInfo.nickname || '商户'} 的衣物 - 次元衣柜`;
        
        // 渲染商户信息
        this.merchantInfoEl.innerHTML = `
            <img src="${avatarUrl}" alt="商户头像" class="merchant-avatar">
            <div class="merchant-details">
                <div class="merchant-name">${this.merchantInfo.nickname || '未设置昵称'}</div>
                <div class="merchant-stats">
                    <span class="merchant-stat">ID: ${this.merchantInfo.id}</span>
                    <span class="merchant-stat">衣物数量: ${this.merchantInfo.clothes_count || 0}</span>
                    <span class="merchant-stat">共享试穿点数: ${parseInt(this.merchantInfo.share_try_on_credits) === 1 ? '已开启' : '未开启'}</span>
                    <span class="merchant-stat">付费试穿点数: ${this.merchantInfo.paid_try_on_count || 0}</span>
                </div>
            </div>
        `;
    },
    
    // 加载衣物列表
    loadClothesList: function() {
        // 显示加载指示器
        this.loadingIndicator.style.display = 'block';
        
        // 隐藏错误消息和空状态
        this.errorMessage.style.display = 'none';
        this.emptyState.style.display = 'none';
        
        // 清空衣物网格
        this.clothesGrid.innerHTML = '';
        
        // 获取衣物列表数据
        this.fetchClothesList()
            .then(data => {
                // 隐藏加载指示器
                this.loadingIndicator.style.display = 'none';
                
                if (data.list && data.list.length > 0) {
                    // 渲染衣物列表
                    this.renderClothesList(data.list);
                    
                    // 更新总记录数
                    this.totalRecords = data.total;
                    
                    // 渲染分页
                    this.renderPagination();
                } else {
                    // 显示空状态
                    this.emptyState.style.display = 'block';
                    
                    // 隐藏分页
                    this.paginationContainer.innerHTML = '';
                }
            })
            .catch(error => {
                // 隐藏加载指示器
                this.loadingIndicator.style.display = 'none';
                
                // 显示错误消息
                this.errorMessage.textContent = `加载失败: ${error.message}`;
                this.errorMessage.style.display = 'block';
                
                console.error('Error loading clothes list:', error);
            });
    },
    
    // 获取衣物列表数据
    fetchClothesList: function() {
        return new Promise((resolve, reject) => {
            // 构建API URL - 使用管理员专用API路径
            const url = new URL('../login_backend/admin_get_merchant_clothes.php', window.location.origin);
            
            // 准备请求数据
            const requestData = {
                merchant_id: this.merchantId,
                page: this.currentPage,
                limit: this.pageSize
            };
            
            // 如果有搜索关键词，添加到请求数据
            if (this.keyword) {
                requestData.keyword = this.keyword;
            }
            
            // 发送请求
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + Auth.getToken() // 使用Bearer认证方式
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    resolve(data.data);
                } else {
                    reject(new Error(data.message || '获取衣物列表失败'));
                }
            })
            .catch(error => {
                reject(error);
            });
        });
    },
    
    // 提取图片的实际URL
    getImageDirectUrl: function(imageUrl) {
        // 如果已经是OSS地址，直接返回
        if (imageUrl && imageUrl.includes('alidog.cn/')) {
            return imageUrl;
        }
        
        // 默认图片
        return 'https://cyymj.oss-cn-shanghai.aliyuncs.com/images/default-clothes.png';
    },
    
    // 渲染衣物列表
    renderClothesList: function(clothes) {
        if (!clothes || clothes.length === 0) {
            this.emptyState.style.display = 'block';
            return;
        }
        
        // 遍历衣物数据，创建卡片
        clothes.forEach(item => {
            const clothesItem = document.createElement('div');
            clothesItem.className = 'clothes-item';
            
            // 获取图片的直接URL
            const imageUrl = this.getImageDirectUrl(item.image_url);
            
            clothesItem.innerHTML = `
                <div class="clothes-image-container">
                    <img src="${imageUrl}" alt="${item.name || '衣物'}" class="clothes-image">
                </div>
                <div class="clothes-info">
                    <div class="clothes-name">${item.name || '未命名衣物'}</div>
                    <div class="clothes-category">${item.category || '未分类'}</div>
                </div>
            `;
            
            // 添加点击事件，直接查看原图
            const imgElement = clothesItem.querySelector('.clothes-image');
            imgElement.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                window.open(imageUrl, '_blank');
            });
            
            // 添加到网格
            this.clothesGrid.appendChild(clothesItem);
        });
    },
    
    // 渲染分页
    renderPagination: function() {
        // 清空分页容器
        this.paginationContainer.innerHTML = '';
        
        // 计算总页数
        const totalPages = Math.ceil(this.totalRecords / this.pageSize);
        
        if (totalPages <= 1) {
            return;
        }
        
        // 创建"上一页"按钮
        if (this.currentPage > 1) {
            const prevBtn = document.createElement('button');
            prevBtn.className = 'page-btn';
            prevBtn.textContent = '上一页';
            prevBtn.addEventListener('click', () => {
                this.currentPage--;
                this.loadClothesList();
            });
            this.paginationContainer.appendChild(prevBtn);
        }
        
        // 创建页码按钮
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, startPage + 4);
        
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = i === this.currentPage ? 'page-btn active' : 'page-btn';
            pageBtn.textContent = i;
            pageBtn.addEventListener('click', () => {
                this.currentPage = i;
                this.loadClothesList();
            });
            this.paginationContainer.appendChild(pageBtn);
        }
        
        // 创建"下一页"按钮
        if (this.currentPage < totalPages) {
            const nextBtn = document.createElement('button');
            nextBtn.className = 'page-btn';
            nextBtn.textContent = '下一页';
            nextBtn.addEventListener('click', () => {
                this.currentPage++;
                this.loadClothesList();
            });
            this.paginationContainer.appendChild(nextBtn);
        }
    }
};

// 文档加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 商户衣物管理初始化
    MerchantClothes.init();
}); 