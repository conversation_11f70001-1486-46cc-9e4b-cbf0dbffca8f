<?php
/**
 * 阿里云OSS辅助类
 * 封装OSS的上传、删除、获取URL等基本操作
 */

// 使用绝对路径加载配置文件
require_once __DIR__ . '/config.php';

// 引入OSS命名空间
use OSS\OssClient;
use OSS\Core\OssException;

class OssHelper {
    private $accessKeyId;
    private $accessKeySecret;
    private $endpoint;
    private $bucket;
    private $bucketDomain;
    
    /**
     * 构造函数
     */
    public function __construct() {
        // 检查常量是否已定义
        if (!defined('ALIYUN_ACCESS_KEY_ID') || !defined('ALIYUN_ACCESS_KEY_SECRET') ||
            !defined('ALIYUN_OSS_ENDPOINT') || !defined('ALIYUN_OSS_BUCKET') || 
            !defined('ALIYUN_OSS_BUCKET_DOMAIN')) {
            
            throw new \Exception('OSS配置常量未定义，请检查config.php文件');
        }
        
        $this->accessKeyId = ALIYUN_ACCESS_KEY_ID;
        $this->accessKeySecret = ALIYUN_ACCESS_KEY_SECRET;
        $this->endpoint = ALIYUN_OSS_ENDPOINT;
        $this->bucket = ALIYUN_OSS_BUCKET;
        $this->bucketDomain = ALIYUN_OSS_BUCKET_DOMAIN;
    }
    
    /**
     * 上传文件到OSS
     * 
     * @param string $localFile 本地文件路径
     * @param string $ossKey OSS存储的key(路径+文件名)
     * @return array 包含成功状态和URL或错误信息
     */
    public function uploadFile($localFile, $ossKey) {
        try {
            if (!file_exists($localFile)) {
                return ['success' => false, 'error' => '本地文件不存在: ' . $localFile];
            }
            
            // 确保OSS路径使用正斜杠
            $ossKey = ltrim(str_replace('\\', '/', $ossKey), '/');
            
            // 初始化OSSClient
            $ossClient = new OssClient(
                $this->accessKeyId,
                $this->accessKeySecret,
                $this->endpoint
            );
            
            // 上传文件
            $ossClient->uploadFile($this->bucket, $ossKey, $localFile);
            
            // 生成访问URL
            $url = $this->getFileUrl($ossKey);
            
            return ['success' => true, 'url' => $url, 'key' => $ossKey];
        } catch (OssException $e) {
            error_log('OSS上传文件失败: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * 上传内容到OSS
     * 
     * @param string $content 文件内容
     * @param string $ossKey OSS存储的key(路径+文件名)
     * @return array 包含成功状态和URL或错误信息
     */
    public function uploadContent($content, $ossKey) {
        try {
            // 确保OSS路径使用正斜杠
            $ossKey = ltrim(str_replace('\\', '/', $ossKey), '/');
            
            // 初始化OSSClient
            $ossClient = new OssClient(
                $this->accessKeyId,
                $this->accessKeySecret,
                $this->endpoint
            );
            
            // 上传内容
            $ossClient->putObject($this->bucket, $ossKey, $content);
            
            // 生成访问URL
            $url = $this->getFileUrl($ossKey);
            
            return ['success' => true, 'url' => $url, 'key' => $ossKey];
        } catch (OssException $e) {
            error_log('OSS上传内容失败: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * 删除OSS上的文件
     * 
     * @param string $ossKey OSS存储的key(路径+文件名)
     * @return array 包含成功状态和错误信息
     */
    public function deleteFile($ossKey) {
        try {
            // 确保OSS路径使用正斜杠
            $ossKey = ltrim(str_replace('\\', '/', $ossKey), '/');
            
            // 初始化OSSClient
            $ossClient = new OssClient(
                $this->accessKeyId,
                $this->accessKeySecret,
                $this->endpoint
            );
            
            // 删除文件
            $ossClient->deleteObject($this->bucket, $ossKey);
            
            return ['success' => true];
        } catch (OssException $e) {
            error_log('OSS删除文件失败: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * 获取OSS文件的URL
     * 
     * @param string $ossKey OSS存储的key(路径+文件名)
     * @param bool|null $useCdn 是否使用CDN域名，null表示使用全局配置
     * @return string 完整的URL
     */
    public function getFileUrl($ossKey, $useCdn = null) {
        // 确保OSS路径使用正斜杠
        $ossKey = ltrim(str_replace('\\', '/', $ossKey), '/');
        
        // 如果没有指定是否使用CDN，则使用全局配置
        if ($useCdn === null) {
            $useCdn = defined('USE_CDN') ? USE_CDN : false;
        }
        
        // 根据配置选择域名
        $domain = $useCdn && defined('ALIYUN_CDN_DOMAIN') ? ALIYUN_CDN_DOMAIN : $this->bucketDomain;
        
        // 返回完整URL
        return 'https://' . $domain . '/' . $ossKey;
    }
    
    /**
     * 从URL中提取OSS Key
     * 
     * @param string $url 完整的URL
     * @return string|null OSS Key或null
     */
    public function getKeyFromUrl($url) {
        if (empty($url)) {
            return null;
        }
        
        // 检查URL是否包含Bucket域名或CDN域名
        $domains = [$this->bucketDomain];
        if (defined('ALIYUN_CDN_DOMAIN')) {
            $domains[] = ALIYUN_CDN_DOMAIN;
        }
        
        foreach ($domains as $domain) {
            if (strpos($url, $domain) !== false) {
                $parsed = parse_url($url);
                if (isset($parsed['path'])) {
                    return ltrim($parsed['path'], '/');
                }
            }
        }
        
        return null;
    }
    
    /**
     * 判断URL是否为OSS链接或CDN链接
     * 
     * @param string $url 完整的URL
     * @return boolean 是否为OSS链接或CDN链接
     */
    public function isOssUrl($url) {
        $domains = [$this->bucketDomain];
        if (defined('ALIYUN_CDN_DOMAIN')) {
            $domains[] = ALIYUN_CDN_DOMAIN;
        }
        
        foreach ($domains as $domain) {
            if (strpos($url, $domain) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 从URL下载文件到OSS
     * 
     * @param string $url 源文件URL
     * @param string $ossKey OSS存储的key(路径+文件名)
     * @return array 包含成功状态和URL或错误信息
     */
    public function downloadUrlToOss($url, $ossKey) {
        // 创建临时文件
        $tempFile = tempnam(sys_get_temp_dir(), 'oss_download_');
        
        try {
            // 下载文件到临时目录
            $fileContent = file_get_contents($url);
            if ($fileContent === false) {
                return ['success' => false, 'error' => '下载文件失败: ' . $url];
            }
            
            file_put_contents($tempFile, $fileContent);
            
            // 上传到OSS
            $result = $this->uploadFile($tempFile, $ossKey);
            
            // 清理临时文件
            @unlink($tempFile);
            
            return $result;
        } catch (OssException $e) {
            // 清理临时文件
            @unlink($tempFile);
            
            error_log('从URL下载到OSS失败: ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        } catch (\Exception $e) {
            // 清理临时文件
            @unlink($tempFile);
            
            error_log('从URL下载到OSS失败(一般错误): ' . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * 将CDN URL转换为OSS URL
     * 
     * @param string $cdnUrl CDN域名的URL
     * @return string 对应的OSS URL
     */
    public function convertCdnUrlToOssUrl($cdnUrl) {
        // 检查URL是否为CDN URL
        if (defined('ALIYUN_CDN_DOMAIN') && strpos($cdnUrl, ALIYUN_CDN_DOMAIN) !== false) {
            // 替换CDN域名为OSS域名
            return str_replace('https://' . ALIYUN_CDN_DOMAIN, 'https://' . $this->bucketDomain, $cdnUrl);
        }
        
        // 如果不是CDN URL，直接返回原URL
        return $cdnUrl;
    }
} 