<?php
/**
 * 城市地理位置解析服务
 *
 * 接收经纬度或城市名，返回城市信息
 */

header('Content-Type: application/json;charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理preflight请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 对于城市信息API，我们允许无授权访问
// 但仍然尝试验证token（如果存在的话）
$userInfo = null;
if (isset($_SERVER['HTTP_AUTHORIZATION']) && !empty($_SERVER['HTTP_AUTHORIZATION'])) {
    // 如果提供了授权头，则验证它
    require_once 'auth.php';
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
    
    $auth = new Auth();
    $payload = $auth->verifyToken($token);
    
    if ($payload) {
        // 如果token有效，获取用户信息
        $userInfo = ['user_id' => $payload['sub']];
    }
}

require_once 'city_utils.php';

// 获取查询参数
$location = isset($_GET['location']) ? $_GET['location'] : '';

// 中国大致中心位置，避免默认使用杭州
$defaultLat = 35.86166;   // 中国大致中心纬度
$defaultLon = 104.195397; // 中国大致中心经度

// 记录请求的详细信息
$ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
error_log("城市API - 收到查询请求: '$location'" . ($userInfo ? " 用户ID: {$userInfo['user_id']}" : " (匿名访问)") . " IP: $ip UA: $userAgent");

// 检查是否为经纬度格式（格式：lat,lon 或 lat;lon）
if (preg_match('/^([\d\.\-]+)[,;]([\d\.\-]+)$/', $location, $matches)) {
    $latitude = $matches[1];
    $longitude = $matches[2];
    
    // 验证经纬度格式
    if (!is_numeric($latitude) || !is_numeric($longitude) ||
        $latitude < -90 || $latitude > 90 || $longitude < -180 || $longitude > 180) {
        error_log("城市API - 无效的经纬度格式: $latitude, $longitude");
        
        // 如果经纬度无效，返回错误信息
        $response = [
            'success' => false,
            'message' => '无效的经纬度格式',
            'data' => []
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    error_log("城市API - 解析为经纬度: $latitude, $longitude");

    // 使用位置查询城市ID
    $cityId = getCityIdByLocation($latitude, $longitude);
    $cityInfo = getCityInfoById($cityId);

    if ($cityInfo) {
        error_log("城市API - 根据经纬度找到城市: " . $cityInfo['name'] . " (ID: $cityId)");
        
        // 构建响应
        $response = [
            'success' => true,
            'message' => '解析成功',
            'data' => [
                [
                    'id' => $cityId,
                    'name' => $cityInfo['name'],
                    'adm1' => $cityInfo['adm1'] ?? '',
                    'adm2' => $cityInfo['adm2'] ?? $cityInfo['name'],
                    'lat' => $cityInfo['lat'],
                    'lon' => $cityInfo['lon'],
                    'source' => 'local_db'
                ]
            ]
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
    } else {
        // 调用地理编码API
        require_once 'proxy_geo_api.php';
        $geoResponse = callGeocodingAPI($latitude, $longitude);
        
        // 添加源信息
        if ($geoResponse['success'] && !empty($geoResponse['data'])) {
            foreach ($geoResponse['data'] as &$item) {
                $item['source'] = 'geo_api';
            }
        }
        
        echo json_encode($geoResponse, JSON_UNESCAPED_UNICODE);
    }
} else if (!empty($location)) {
    // 当作城市名称处理
    error_log("城市API - 解析为城市名称: $location");
    
    // 使用城市名查询城市ID
    $cityId = getCityIdByName($location);
    $cityInfo = getCityInfoById($cityId);
    
    if ($cityInfo) {
        error_log("城市API - 根据名称找到城市: " . $cityInfo['name'] . " (ID: $cityId)");
        
        // 构建响应
        $response = [
            'success' => true,
            'message' => '解析成功',
            'data' => [
                [
                    'id' => $cityId,
                    'name' => $cityInfo['name'],
                    'adm1' => $cityInfo['adm1'] ?? '',
                    'adm2' => $cityInfo['adm2'] ?? $cityInfo['name'],
                    'lat' => $cityInfo['lat'],
                    'lon' => $cityInfo['lon'],
                    'source' => 'local_db'
                ]
            ]
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
    } else {
        // 使用模拟数据
        error_log("城市API - 未找到城市名称，使用地理编码API");
        require_once 'proxy_geo_api.php';
        $geoResponse = callGeocodingByName($location);
        
        // 添加源信息
        if ($geoResponse['success'] && !empty($geoResponse['data'])) {
            foreach ($geoResponse['data'] as &$item) {
                $item['source'] = 'geo_api';
            }
        }
        
        echo json_encode($geoResponse, JSON_UNESCAPED_UNICODE);
    }
} else {
    // 没有提供任何位置信息
    error_log("城市API - 未提供位置信息，返回错误");
    
    $response = [
        'success' => false,
        'message' => '未提供位置信息',
        'data' => []
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}
?> 