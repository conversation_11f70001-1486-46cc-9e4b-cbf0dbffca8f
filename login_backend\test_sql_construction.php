<?php
/**
 * 测试SQL构建逻辑的脚本
 * 用于验证衣橱筛选的SQL查询是否正确构建
 */

// 模拟参数
$userId = 3;
$queryUserId = $userId;
$wardrobeId = 17;
$dataSource = 'all';
$includeCircleData = true;
$isMerchantMode = false;

echo "=== SQL构建测试 ===\n\n";

// 模拟SQL构建过程
if ($includeCircleData && !$isMerchantMode) {
    // 新功能：包含圈子数据的查询
    $sql = "SELECT c.id, c.name, c.category, c.image_url, c.tags, c.description, c.created_at, c.wardrobe_id,
                   c.circle_id, u.nickname as creator_nickname,
                   CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
            FROM clothes c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE ";

    // 根据数据源参数构建WHERE条件
    if ($dataSource === 'personal') {
        $sql .= "c.user_id = :user_id AND c.circle_id IS NULL";
        $params = ['user_id' => $queryUserId];
    } elseif ($dataSource === 'shared') {
        // 查询用户所在圈子的共享数据
        $sql .= "c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')";
        $params = ['user_id' => $queryUserId];
    } else { // $dataSource === 'all'
        // 查询个人数据 + 圈子共享数据
        $sql .= "((c.user_id = :user_id AND c.circle_id IS NULL) OR
                 (c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')))";
        $params = ['user_id' => $queryUserId];
    }
} else {
    // 原有逻辑：只查询个人数据（保持向后兼容）
    $sql = "SELECT id, name, category, image_url, tags, description, created_at, wardrobe_id FROM clothes WHERE user_id = :user_id";
    $params = ['user_id' => $queryUserId];
}

echo "基础SQL:\n$sql\n\n";
echo "基础参数:\n" . json_encode($params, JSON_PRETTY_PRINT) . "\n\n";

// Add wardrobe filter if provided
if ($wardrobeId) {
    if ($includeCircleData && !$isMerchantMode) {
        // 对于圈子数据查询，需要明确指定表别名，并确保衣橱权限正确
        if ($dataSource === 'shared') {
            // 只查询圈子共享衣橱中的衣物
            $sql .= " AND c.wardrobe_id = :wardrobe_id AND c.circle_id IS NOT NULL";
        } elseif ($dataSource === 'all') {
            // 查询指定衣橱中的衣物，包括个人和共享的
            // 基础WHERE条件已经包含了用户权限检查，这里只需要添加衣橱筛选
            $sql .= " AND c.wardrobe_id = :wardrobe_id";
        } else {
            // personal模式，只查询个人衣橱
            $sql .= " AND c.wardrobe_id = :wardrobe_id AND c.circle_id IS NULL";
        }
    } else {
        $sql .= " AND wardrobe_id = :wardrobe_id";
    }
    $params['wardrobe_id'] = $wardrobeId;
}

$sql .= " ORDER BY created_at DESC";

echo "最终SQL:\n$sql\n\n";
echo "最终参数:\n" . json_encode($params, JSON_PRETTY_PRINT) . "\n\n";

// 分析SQL查询
echo "=== SQL分析 ===\n";
echo "数据源: $dataSource\n";
echo "衣橱ID: $wardrobeId\n";
echo "用户ID: $userId\n";

if ($dataSource === 'all') {
    echo "\n预期行为:\n";
    echo "1. 查询用户自己的衣物 (c.user_id = $userId AND c.circle_id IS NULL)\n";
    echo "2. 查询用户所在圈子的共享衣物 (c.circle_id IS NOT NULL AND c.circle_id IN (...))\n";
    echo "3. 筛选指定衣橱的衣物 (c.wardrobe_id = $wardrobeId)\n";
    echo "\n这应该只返回衣橱ID为 $wardrobeId 的衣物，包括个人和共享的。\n";
}

echo "\n=== 测试完成 ===\n";
?>
