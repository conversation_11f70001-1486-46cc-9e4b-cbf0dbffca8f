<?php
/**
 * 测试共享数据源排除用户自己数据的修复效果
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "=== 共享数据源排除测试 ===\n";
    echo "当前用户ID: $userId\n\n";
    
    // 1. 检查用户所在的圈子
    echo "1. 用户所在的圈子:\n";
    $stmt = $conn->prepare("
        SELECT cm.circle_id, cm.status, cm.role, c.name as circle_name
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id AND cm.status = 'active'
        ORDER BY cm.joined_at DESC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $userCircles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($userCircles)) {
        echo "用户不在任何活跃圈子中\n";
        exit;
    }
    
    foreach ($userCircles as $circle) {
        echo "- 圈子ID: {$circle['circle_id']}, 名称: {$circle['circle_name']}, 角色: {$circle['role']}\n";
    }
    echo "\n";
    
    $circleId = $userCircles[0]['circle_id'];
    
    // 2. 测试衣物API - shared数据源
    echo "2. 测试衣物API (shared数据源):\n";
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.user_id, c.circle_id, u.nickname as creator_nickname,
               CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
        FROM clothes c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.circle_id IS NOT NULL AND c.user_id != :user_id AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
        ORDER BY c.created_at DESC
        LIMIT 10
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $sharedClothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "查询结果数量: " . count($sharedClothes) . "\n";
    if (!empty($sharedClothes)) {
        foreach ($sharedClothes as $item) {
            $isOwner = $item['user_id'] == $userId ? ' ❌ 包含自己的数据!' : ' ✅ 其他用户数据';
            echo "- {$item['name']} (ID: {$item['id']}) - 创建者: {$item['creator_nickname']} (用户{$item['user_id']}){$isOwner}\n";
        }
    } else {
        echo "没有找到共享衣物数据\n";
    }
    echo "\n";
    
    // 3. 测试穿搭API - shared数据源
    echo "3. 测试穿搭API (shared数据源):\n";
    $stmt = $conn->prepare("
        SELECT o.id, o.name, o.user_id, o.circle_id, u.nickname as creator_nickname,
               CASE WHEN o.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
        FROM outfits o
        LEFT JOIN users u ON o.user_id = u.id
        WHERE o.circle_id IS NOT NULL AND o.user_id != :user_id AND o.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
        ORDER BY o.created_at DESC
        LIMIT 10
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $sharedOutfits = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "查询结果数量: " . count($sharedOutfits) . "\n";
    if (!empty($sharedOutfits)) {
        foreach ($sharedOutfits as $item) {
            $isOwner = $item['user_id'] == $userId ? ' ❌ 包含自己的数据!' : ' ✅ 其他用户数据';
            echo "- {$item['name']} (ID: {$item['id']}) - 创建者: {$item['creator_nickname']} (用户{$item['user_id']}){$isOwner}\n";
        }
    } else {
        echo "没有找到共享穿搭数据\n";
    }
    echo "\n";
    
    // 4. 测试衣橱API - shared数据源
    echo "4. 测试衣橱API (shared数据源):\n";
    $stmt = $conn->prepare("
        SELECT w.id, w.name, w.user_id, w.circle_id, u.nickname as creator_nickname,
               CASE WHEN w.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
        FROM wardrobes w
        LEFT JOIN users u ON w.user_id = u.id
        WHERE w.circle_id IS NOT NULL AND w.user_id != :user_id AND w.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
        ORDER BY w.is_default DESC, w.sort_order ASC, w.name ASC
        LIMIT 10
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $sharedWardrobes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "查询结果数量: " . count($sharedWardrobes) . "\n";
    if (!empty($sharedWardrobes)) {
        foreach ($sharedWardrobes as $item) {
            $isOwner = $item['user_id'] == $userId ? ' ❌ 包含自己的数据!' : ' ✅ 其他用户数据';
            echo "- {$item['name']} (ID: {$item['id']}) - 创建者: {$item['creator_nickname']} (用户{$item['user_id']}){$isOwner}\n";
        }
    } else {
        echo "没有找到共享衣橱数据\n";
    }
    echo "\n";
    
    // 5. 测试分类API - shared数据源
    echo "5. 测试分类API (shared数据源):\n";
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.user_id, c.circle_id, u.nickname as creator_nickname,
               CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
        FROM clothing_categories c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.is_system = 0 AND c.user_id != :user_id AND c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
        ORDER BY c.sort_order ASC, c.created_at ASC
        LIMIT 10
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $sharedCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "查询结果数量: " . count($sharedCategories) . "\n";
    if (!empty($sharedCategories)) {
        foreach ($sharedCategories as $item) {
            $isOwner = $item['user_id'] == $userId ? ' ❌ 包含自己的数据!' : ' ✅ 其他用户数据';
            echo "- {$item['name']} (ID: {$item['id']}) - 创建者: {$item['creator_nickname']} (用户{$item['user_id']}){$isOwner}\n";
        }
    } else {
        echo "没有找到共享分类数据\n";
    }
    echo "\n";
    
    // 6. 对比测试 - all数据源（应该包含自己的数据）
    echo "6. 对比测试 - all数据源衣物查询（应该包含自己的数据）:\n";
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.user_id, c.circle_id, u.nickname as creator_nickname,
               CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
        FROM clothes c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE ((c.user_id = :user_id AND c.circle_id IS NULL) OR
               (c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')))
        ORDER BY c.created_at DESC
        LIMIT 10
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $allClothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "查询结果数量: " . count($allClothes) . "\n";
    $ownCount = 0;
    $othersCount = 0;
    
    if (!empty($allClothes)) {
        foreach ($allClothes as $item) {
            if ($item['user_id'] == $userId) {
                $ownCount++;
                echo "- {$item['name']} (ID: {$item['id']}) - 自己的数据 ({$item['data_source']})\n";
            } else {
                $othersCount++;
                echo "- {$item['name']} (ID: {$item['id']}) - 其他用户: {$item['creator_nickname']} ({$item['data_source']})\n";
            }
        }
    }
    
    echo "统计: 自己的数据 $ownCount 件，其他用户数据 $othersCount 件\n\n";
    
    // 7. 总结
    echo "7. 测试总结:\n";
    echo "✅ shared数据源修复验证:\n";
    echo "- 衣物API: " . (count($sharedClothes) > 0 ? "有数据，已排除自己" : "无数据或已正确排除") . "\n";
    echo "- 穿搭API: " . (count($sharedOutfits) > 0 ? "有数据，已排除自己" : "无数据或已正确排除") . "\n";
    echo "- 衣橱API: " . (count($sharedWardrobes) > 0 ? "有数据，已排除自己" : "无数据或已正确排除") . "\n";
    echo "- 分类API: " . (count($sharedCategories) > 0 ? "有数据，已排除自己" : "无数据或已正确排除") . "\n";
    echo "\n";
    echo "✅ all数据源对比验证:\n";
    echo "- 包含自己的数据: $ownCount 件\n";
    echo "- 包含其他用户数据: $othersCount 件\n";
    echo "\n";
    
    if ($ownCount > 0 && $othersCount > 0) {
        echo "🎉 修复成功！shared数据源只显示其他用户数据，all数据源包含所有数据\n";
    } elseif ($ownCount > 0 && $othersCount == 0) {
        echo "⚠️ 圈子中只有自己的数据，无法完全验证修复效果\n";
    } else {
        echo "⚠️ 需要检查数据同步状态\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
