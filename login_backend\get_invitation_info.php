<?php
// 获取邀请详情API
// 模块3：邀请分享模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证必需参数
if (!isset($_GET['code']) || empty(trim($_GET['code']))) {
    echo json_encode([
        'status' => 'error',
        'message' => '邀请码不能为空'
    ]);
    exit;
}

$invitationCode = trim(strtoupper($_GET['code']));
$inviterId = isset($_GET['inviter']) ? intval($_GET['inviter']) : null;

// 验证用户身份（可选，用于判断用户状态）
$userId = null;
if (isset($_SERVER['HTTP_AUTHORIZATION']) && !empty($_SERVER['HTTP_AUTHORIZATION'])) {
    $auth = new Auth();
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    
    // 如果token是Bearer格式，提取实际token值
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
    
    $payload = $auth->verifyToken($token);
    if ($payload) {
        $userId = $payload['sub'];
    }
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查找圈子信息
    $findCircleSql = "SELECT c.id, c.name, c.description, c.invitation_code, c.creator_id, 
                             c.member_count, c.created_at,
                             u.nickname as creator_nickname, u.avatar_url as creator_avatar
                      FROM outfit_circles c
                      JOIN users u ON c.creator_id = u.id
                      WHERE c.invitation_code = :invitation_code AND c.status = 'active'";
    $findCircleStmt = $conn->prepare($findCircleSql);
    $findCircleStmt->bindParam(':invitation_code', $invitationCode);
    $findCircleStmt->execute();
    
    $circle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$circle) {
        echo json_encode([
            'status' => 'error',
            'message' => '邀请码无效或圈子不存在'
        ]);
        exit;
    }
    
    // 获取邀请者信息（如果提供了邀请者ID）
    $inviterInfo = null;
    if ($inviterId) {
        $getInviterSql = "SELECT nickname, avatar_url FROM users WHERE id = :inviter_id";
        $getInviterStmt = $conn->prepare($getInviterSql);
        $getInviterStmt->bindParam(':inviter_id', $inviterId, PDO::PARAM_INT);
        $getInviterStmt->execute();
        $inviterInfo = $getInviterStmt->fetch(PDO::FETCH_ASSOC);
    }
    
    // 获取圈子统计信息
    $getStatsSql = "SELECT 
                        COUNT(DISTINCT cm.user_id) as member_count,
                        COUNT(DISTINCT w.id) as wardrobe_count,
                        COUNT(DISTINCT cl.id) as clothes_count,
                        COUNT(DISTINCT o.id) as outfit_count
                    FROM circle_members cm
                    LEFT JOIN wardrobes w ON cm.circle_id = w.circle_id
                    LEFT JOIN clothes cl ON cm.circle_id = cl.circle_id  
                    LEFT JOIN outfits o ON cm.circle_id = o.circle_id
                    WHERE cm.circle_id = :circle_id AND cm.status = 'active'";
    $getStatsStmt = $conn->prepare($getStatsSql);
    $getStatsStmt->bindParam(':circle_id', $circle['id'], PDO::PARAM_INT);
    $getStatsStmt->execute();
    $stats = $getStatsStmt->fetch(PDO::FETCH_ASSOC);
    
    // 检查用户状态（如果用户已登录）
    $userStatus = 'unknown';
    $statusMessage = '';
    
    if ($userId) {
        // 检查用户是否已在其他圈子
        $checkExistingCircleSql = "SELECT c.id, c.name 
                                   FROM circle_members cm 
                                   JOIN outfit_circles c ON cm.circle_id = c.id 
                                   WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
        $checkExistingStmt = $conn->prepare($checkExistingCircleSql);
        $checkExistingStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $checkExistingStmt->execute();
        $existingCircle = $checkExistingStmt->fetch(PDO::FETCH_ASSOC);
        
        // 检查用户是否已是该圈子成员
        $checkMemberSql = "SELECT status FROM circle_members 
                           WHERE circle_id = :circle_id AND user_id = :user_id";
        $checkMemberStmt = $conn->prepare($checkMemberSql);
        $checkMemberStmt->bindParam(':circle_id', $circle['id'], PDO::PARAM_INT);
        $checkMemberStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $checkMemberStmt->execute();
        $existingMember = $checkMemberStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existingCircle) {
            if ($existingCircle['id'] == $circle['id']) {
                $userStatus = 'already_member';
                $statusMessage = '您已经是该圈子的成员';
            } else {
                $userStatus = 'in_other_circle';
                $statusMessage = '您已经在圈子"' . $existingCircle['name'] . '"中，请先退出后再加入其他圈子';
            }
        } elseif ($existingMember && $existingMember['status'] === 'active') {
            $userStatus = 'already_member';
            $statusMessage = '您已经是该圈子的成员';
        } elseif ($existingMember && $existingMember['status'] === 'removed') {
            $userStatus = 'can_rejoin';
            $statusMessage = '您之前曾是该圈子的成员，可以重新加入';
        } else {
            $userStatus = 'can_join';
            $statusMessage = '您可以加入这个圈子';
        }
    }
    
    echo json_encode([
        'status' => 'success',
        'data' => [
            'circle' => [
                'id' => $circle['id'],
                'name' => $circle['name'],
                'description' => $circle['description'],
                'invitation_code' => $circle['invitation_code'],
                'member_count' => $circle['member_count'],
                'created_at' => $circle['created_at'],
                'creator' => [
                    'id' => $circle['creator_id'],
                    'nickname' => $circle['creator_nickname'] ?? '未知用户',
                    'avatar_url' => $circle['creator_avatar']
                ],
                'stats' => [
                    'member_count' => $stats['member_count'] ?? 0,
                    'wardrobe_count' => $stats['wardrobe_count'] ?? 0,
                    'clothes_count' => $stats['clothes_count'] ?? 0,
                    'outfit_count' => $stats['outfit_count'] ?? 0
                ]
            ],
            'inviter' => $inviterInfo ? [
                'nickname' => $inviterInfo['nickname'] ?? '未知用户',
                'avatar_url' => $inviterInfo['avatar_url']
            ] : null,
            'user_status' => $userStatus,
            'status_message' => $statusMessage
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '获取邀请信息失败：' . $e->getMessage()
    ]);
}
?>
