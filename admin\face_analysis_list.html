<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面部分析历史 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        .search-box {
            display: flex;
            margin-bottom: 20px;
        }
        
        .search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px 0 0 4px;
            outline: none;
        }
        
        .search-btn {
            padding: 8px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
        }
        
        .search-btn:hover {
            background-color: #40a9ff;
        }

        .filter-section {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        
        .filter-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-label {
            font-weight: 500;
            white-space: nowrap;
        }
        
        .filter-select {
            padding: 6px 10px;
            border-radius: 4px;
            border: 1px solid #d9d9d9;
        }
        
        .analysis-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .analysis-table th,
        .analysis-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .analysis-table th {
            background-color: #fafafa;
            font-weight: 500;
        }
        
        .analysis-table tr:hover {
            background-color: #f5f5f5;
        }
        
        .thumbnail {
            width: 60px;
            height: 60px;
            border-radius: 4px;
            object-fit: cover;
            background-color: #f5f5f5;
            cursor: pointer;
        }
        
        .analysis-summary {
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #666;
            font-size: 0.9em;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 12px;
            display: inline-block;
            text-align: center;
            min-width: 60px;
        }
        
        .status-pending {
            background-color: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        
        .status-processing {
            background-color: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        
        .status-completed {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .status-failed {
            background-color: #fff1f0;
            color: #f5222d;
            border: 1px solid #ffa39e;
        }
        
        .action-btn {
            padding: 4px 10px;
            border-radius: 4px;
            border: 1px solid;
            background-color: transparent;
            cursor: pointer;
            margin-right: 5px;
            font-size: 13px;
        }
        
        .view-btn {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .view-btn:hover {
            background-color: #e6f7ff;
        }
        
        .pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        .pagination button {
            padding: 5px 10px;
            margin: 0 5px;
            border: 1px solid #d9d9d9;
            background-color: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .pagination button:hover:not(:disabled) {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .pagination button:disabled {
            color: #d9d9d9;
            cursor: not-allowed;
        }
        
        .pagination .current-page {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .no-data {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        
        .loading-indicator {
            text-align: center;
            padding: 20px;
            color: #666;
            display: none;
        }
        
        .error-message {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #f5222d;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }
        
        .error-row {
            background-color: #fff2f0;
            color: #f5222d;
            text-align: center;
            padding: 10px;
        }
        
        /* 添加调试模式样式 */
        .debug-info {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            display: none;
        }
        
        /* 详情模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            overflow-y: auto;
        }
        
        .modal-content {
            background-color: #fff;
            margin: 50px auto;
            width: 90%;
            max-width: 900px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            position: relative;
        }
        
        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 500;
            margin: 0;
        }
        
        .modal-close {
            font-size: 22px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
            background: none;
            border: none;
            padding: 0;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #f0f0f0;
            text-align: right;
        }
        
        /* 分析详情样式 */
        .analysis-detail {
            margin-bottom: 20px;
        }
        
        .analysis-info {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .analysis-info {
                grid-template-columns: 1fr;
            }
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #666;
        }
        
        .info-value {
            color: #333;
        }
        
        .analysis-photos {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .photo-container {
            text-align: center;
        }
        
        .photo-container img {
            max-width: 100%;
            max-height: 300px;
            object-fit: contain;
            border-radius: 4px;
            border: 1px solid #eee;
            cursor: pointer;
        }
        
        .photo-label {
            margin-top: 8px;
            color: #666;
        }
        
        .analysis-result {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>面部分析历史</h2>
                <div class="user-info">
                    <span id="userName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div id="analysisError" class="error-message"></div>
            <div id="analysisLoading" class="loading-indicator">正在加载分析数据...</div>
            
            <div class="card">
                <div class="search-box">
                    <input type="text" id="searchInput" class="search-input" placeholder="搜索用户ID..." />
                    <button id="searchBtn" class="search-btn">搜索</button>
                </div>
                
                <div class="filter-section">
                    <div class="filter-item">
                        <span class="filter-label">状态:</span>
                        <select id="statusFilter" class="filter-select">
                            <option value="">全部</option>
                            <option value="pending">待处理</option>
                            <option value="processing">处理中</option>
                            <option value="completed">已完成</option>
                            <option value="failed">失败</option>
                        </select>
                    </div>
                </div>
                
                <table class="analysis-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户</th>
                            <th>正面照片</th>
                            <th>侧面照片</th>
                            <th>偏好风格</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="analysisTable">
                        <tr>
                            <td colspan="8" class="no-data">加载中...</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="pagination" id="analysisPagination">
                    <button id="prevBtn" disabled>&lt; 上一页</button>
                    <span id="pageInfo">第 0/0 页</span>
                    <button id="nextBtn" disabled>下一页 &gt;</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 分析详情模态框 -->
    <div id="analysisModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">面部分析详情</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="analysisModalBody">
                <!-- 分析详情内容将动态插入这里 -->
            </div>
            <div class="modal-footer">
                <button class="action-btn view-btn modal-close">关闭</button>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js?v=20250710"></script>
    <script src="js/sidebar.js?v=20250710"></script>
    <script src="js/image_viewer.js?v=20250710"></script>
    <script src="js/face_analysis_list.js?v=20250711"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，开始初始化面部分析列表页面');
            
            // 检查关键元素是否存在
            const checkElements = [
                'searchInput', 'searchBtn', 'statusFilter', 'analysisTable',
                'prevBtn', 'nextBtn', 'pageInfo', 'analysisLoading', 'analysisError'
            ];
            
            let allElementsExist = true;
            checkElements.forEach(id => {
                const element = document.getElementById(id);
                if (!element) {
                    console.error(`关键元素未找到: #${id}`);
                    allElementsExist = false;
                }
            });
            
            if (!allElementsExist) {
                console.error('部分关键元素未找到，页面可能无法正常工作');
                const errorElement = document.getElementById('analysisError');
                if (errorElement) {
                    errorElement.textContent = '页面初始化失败：部分关键元素未找到';
                    errorElement.style.display = 'block';
                }
            }
            
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为face_analysis
            Sidebar.init('face_analysis');
            
            // 显示用户信息
            const userName = document.getElementById('userName');
            const user = Auth.getCurrentUser();
            if (user) {
                userName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化面部分析模块
            try {
                FaceAnalysis.init();
            } catch (error) {
                console.error('初始化面部分析模块失败:', error);
                const errorElement = document.getElementById('analysisError');
                if (errorElement) {
                    errorElement.textContent = `初始化失败: ${error.message}`;
                    errorElement.style.display = 'block';
                }
            }
        });
    </script>
</body>
</html> 