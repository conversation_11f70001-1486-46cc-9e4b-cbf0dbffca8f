/**
 * 推荐穿搭编辑页 JavaScript 逻辑
 */

// 配置项
const config = {
    apiBaseUrl: '../login_backend',
    defaultImageUrl: 'https://cyymj.oss-cn-shanghai.aliyuncs.com/placeholder.png'
};

// 页面元素
const elements = {
    // 页面标题
    pageTitle: document.getElementById('pageTitle'),
    
    // 表单元素
    outfitName: document.getElementById('outfitName'),
    categoryId: document.getElementById('categoryId'),
    imageUrl: document.getElementById('imageUrl'),
    imagePreview: document.getElementById('imagePreview'),
    description: document.getElementById('description'),
    recommendationReason: document.getElementById('recommendationReason'),
    sortOrder: document.getElementById('sortOrder'),
    status: document.getElementById('status'),
    itemsContainer: document.getElementById('itemsContainer'),
    
    // 按钮
    cancelBtn: document.getElementById('cancelBtn'),
    saveBtn: document.getElementById('saveBtn'),
    addItemBtn: document.getElementById('addItemBtn'),
    uploadImageBtn: document.getElementById('uploadImageBtn'),
    imageUpload: document.getElementById('imageUpload'),
    logoutBtn: document.getElementById('logoutBtn'),
    
    // 模板
    itemTemplate: document.getElementById('itemTemplate'),
    
    // 消息和加载
    errorMessage: document.getElementById('errorMessage'),
    successMessage: document.getElementById('successMessage'),
    loadingOverlay: document.getElementById('loadingOverlay')
};

// 状态管理
const state = {
    isEditing: false,
    outfitId: null,
    categories: [],
    itemIndex: 0,
    outfitData: null
};

// API 请求处理
const api = {
    /**
     * 获取请求头
     * @returns {Object} 包含认证信息的请求头
     */
    getHeaders() {
        return {
            'Content-Type': 'application/json',
            'Authorization': Auth.getToken()
        };
    },
    
    /**
     * 获取穿搭分类列表
     * @returns {Promise} 返回分类列表
     */
    async getCategories() {
        try {
            const response = await fetch(`${config.apiBaseUrl}/admin_get_recommended_categories.php`, {
                method: 'GET',
                headers: this.getHeaders()
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.msg || '获取分类列表失败');
            }
            
            return data;
        } catch (error) {
            throw error;
        }
    },
    
    /**
     * 获取穿搭详情
     * @param {number} id 穿搭ID
     * @returns {Promise} 返回穿搭详情
     */
    async getOutfitDetail(id) {
        try {
            const response = await fetch(`${config.apiBaseUrl}/admin_get_recommended_outfit_detail.php?id=${id}`, {
                method: 'GET',
                headers: this.getHeaders()
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.msg || '获取穿搭详情失败');
            }
            
            console.log('API返回的穿搭详情数据:', data);
            
            return data;
        } catch (error) {
            throw error;
        }
    },
    
    /**
     * 添加新推荐穿搭
     * @param {Object} outfitData 穿搭数据
     * @returns {Promise} 返回添加结果
     */
    async addOutfit(outfitData) {
        try {
            const response = await fetch(`${config.apiBaseUrl}/admin_add_recommended_outfit.php`, {
                method: 'POST',
                headers: this.getHeaders(),
                body: JSON.stringify(outfitData)
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.msg || '添加推荐穿搭失败');
            }
            
            return data;
        } catch (error) {
            throw error;
        }
    },
    
    /**
     * 更新推荐穿搭
     * @param {Object} outfitData 穿搭数据
     * @returns {Promise} 返回更新结果
     */
    async updateOutfit(outfitData) {
        try {
            const response = await fetch(`${config.apiBaseUrl}/admin_update_recommended_outfit.php`, {
                method: 'POST',
                headers: this.getHeaders(),
                body: JSON.stringify(outfitData)
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.msg || '更新推荐穿搭失败');
            }
            
            return data;
        } catch (error) {
            throw error;
        }
    },
    
    /**
     * 上传图片到服务器
     * @param {File} file 图片文件
     * @returns {Promise} 返回上传结果
     */
    async uploadImage(file) {
        try {
            console.log('开始上传图片...');
            const formData = new FormData();
            formData.append('image', file);
            
            console.log('发送上传请求，使用token: ', Auth.getToken().substring(0, 20) + '...');
            const response = await fetch(`${config.apiBaseUrl}/admin_upload_outfit_image.php`, {
                method: 'POST',
                headers: {
                    'Authorization': Auth.getToken()
                },
                body: formData
            });
            
            console.log('上传请求完成，状态码: ', response.status);
            
            // 获取原始响应文本以便调试
            const responseText = await response.text();
            console.log('原始响应: ', responseText);
            
            let data;
            try {
                data = JSON.parse(responseText);
            } catch (e) {
                console.error('解析响应JSON失败: ', e);
                throw new Error('服务器返回了无效的JSON数据');
            }
            
            if (data.error) {
                console.error('上传失败，服务器返回错误: ', data.msg);
                throw new Error(data.msg || '上传图片失败');
            }
            
            console.log('上传成功，返回数据: ', data);
            return data;
        } catch (error) {
            console.error('上传图片过程中出错: ', error);
            throw error;
        }
    }
};

// 页面功能
const handlers = {
    /**
     * 加载分类列表
     */
    async loadCategories() {
        try {
            const data = await api.getCategories();
            
            state.categories = data.data || [];
            
            // 清空当前选项
            while (elements.categoryId.options.length > 1) {
                elements.categoryId.remove(1);
            }
            
            // 添加分类选项
            state.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                elements.categoryId.appendChild(option);
            });
        } catch (error) {
            console.error('加载分类列表失败:', error);
            this.showError('加载分类列表失败：' + error.message);
        }
    },
    
    /**
     * 加载穿搭详情
     * @param {number} id 穿搭ID
     */
    async loadOutfitDetail(id) {
        try {
            elements.loadingOverlay.style.display = 'flex';
            
            const data = await api.getOutfitDetail(id);
            state.outfitData = data.data;
            
            console.log('获取到的穿搭数据:', state.outfitData);
            console.log('穿搭数据的所有键:', Object.keys(state.outfitData));
            
            // 调试：直接检查recommendation_reason字段以及其他可能的变体名称
            console.log('推荐理由字段(recommendation_reason):', state.outfitData.recommendation_reason);
            console.log('推荐理由字段(recommendationReason):', state.outfitData.recommendationReason);
            console.log('推荐理由字段(recommend_reason):', state.outfitData.recommend_reason);
            
            // 填充表单数据
            elements.outfitName.value = state.outfitData.name;
            elements.categoryId.value = state.outfitData.category_id || '';
            elements.imageUrl.value = state.outfitData.image_url;
            elements.description.value = state.outfitData.description || '';
            
            // 调试推荐理由字段
            console.log('推荐理由元素:', elements.recommendationReason);
            if (!elements.recommendationReason) {
                console.error('无法找到推荐理由元素!');
            } else {
                console.log('推荐理由元素类型:', elements.recommendationReason.tagName);
                console.log('推荐理由值设置前:', elements.recommendationReason.value);
                
                // 尝试多种可能的字段名称
                const reasonValue = state.outfitData.recommendation_reason || 
                                  state.outfitData.recommendationReason || 
                                  state.outfitData.recommend_reason || '';
                
                // 使用直接DOM操作确保赋值
                elements.recommendationReason.value = reasonValue;
                
                // 强制触发变更事件
                const event = new Event('input', { bubbles: true });
                elements.recommendationReason.dispatchEvent(event);
                
                console.log('推荐理由值设置后:', elements.recommendationReason.value);
                console.log('使用的推荐理由值:', reasonValue);
            }
            
            elements.sortOrder.value = state.outfitData.sort_order || 0;
            elements.status.value = state.outfitData.status;
            
            // 更新预览图
            this.updateImagePreview(elements.imagePreview, state.outfitData.image_url);
            
            // 添加商品项
            elements.itemsContainer.innerHTML = '';
            state.itemIndex = 0;
            
            if (state.outfitData.items && state.outfitData.items.length > 0) {
                state.outfitData.items.forEach(item => {
                    this.addItemRow(item);
                });
            }
            
            elements.loadingOverlay.style.display = 'none';
        } catch (error) {
            console.error('加载穿搭详情失败:', error);
            elements.loadingOverlay.style.display = 'none';
            this.showError('加载穿搭详情失败：' + error.message);
        }
    },
    
    /**
     * 添加商品行
     * @param {Object} item 商品数据，可选
     */
    addItemRow(item = null) {
        // 复制模板
        const template = elements.itemTemplate.content.cloneNode(true);
        const itemRow = template.querySelector('.item-row');
        const itemIndex = state.itemIndex++;
        
        // 设置序号
        itemRow.querySelector('.item-number').textContent = itemIndex + 1;
        
        // 获取表单元素
        const nameInput = itemRow.querySelector('.item-name');
        const imageUrlInput = itemRow.querySelector('.item-image-url');
        const imagePreview = itemRow.querySelector('.item-image-preview');
        const priceInput = itemRow.querySelector('.item-price');
        const purchaseUrlInput = itemRow.querySelector('.item-purchase-url');
        const sortOrderInput = itemRow.querySelector('.item-sort-order');
        const uploadBtn = itemRow.querySelector('.item-upload-btn');
        const imageUpload = itemRow.querySelector('.item-image-upload');
        
        // 如果有数据，填充数据
        if (item) {
            nameInput.value = item.name || '';
            imageUrlInput.value = item.image_url || '';
            priceInput.value = item.price || '';
            purchaseUrlInput.value = item.purchase_url || '';
            sortOrderInput.value = item.sort_order || 0;
            
            // 更新图片预览
            this.updateImagePreview(imagePreview, item.image_url);
        }
        
        // 绑定图片URL输入事件
        imageUrlInput.addEventListener('input', () => {
            this.updateImagePreview(imagePreview, imageUrlInput.value);
        });
        
        // 绑定上传按钮点击事件
        uploadBtn.addEventListener('click', () => {
            imageUpload.click();
        });
        
        // 绑定图片上传事件
        imageUpload.addEventListener('change', async (event) => {
            const file = event.target.files[0];
            if (!file) return;
            
            try {
                elements.loadingOverlay.style.display = 'flex';
                
                // 上传图片
                const result = await api.uploadImage(file);
                
                // 提取图片URL (处理不同的返回格式)
                const imageUrl = result.data && result.data.image_url ? 
                                 result.data.image_url : 
                                 (result.image_url || '');
                
                console.log('获取到的商品图片URL: ', imageUrl);
                
                // 更新URL和预览
                imageUrlInput.value = imageUrl;
                this.updateImagePreview(imagePreview, imageUrl);
                
                elements.loadingOverlay.style.display = 'none';
            } catch (error) {
                console.error('上传商品图片失败:', error);
                elements.loadingOverlay.style.display = 'none';
                this.showError('上传商品图片失败：' + error.message);
            }
        });
        
        // 绑定删除按钮事件
        const removeBtn = itemRow.querySelector('.remove-item-btn');
        removeBtn.addEventListener('click', () => {
            itemRow.remove();
            // 重新编号所有商品
            this.renumberItems();
        });
        
        // 添加到商品列表
        elements.itemsContainer.appendChild(itemRow);
    },
    
    /**
     * 重新编号所有商品
     */
    renumberItems() {
        const items = elements.itemsContainer.querySelectorAll('.item-row');
        items.forEach((item, index) => {
            item.querySelector('.item-number').textContent = index + 1;
        });
    },
    
    /**
     * 更新图片预览
     * @param {HTMLElement} previewElement 预览元素
     * @param {string} url 图片URL
     */
    updateImagePreview(previewElement, url) {
        previewElement.innerHTML = '';
        previewElement.classList.add('empty');
        
        if (url && url.trim()) {
            const img = document.createElement('img');
            img.src = url;
            img.alt = 'Preview';
            img.addEventListener('load', () => {
                previewElement.classList.remove('empty');
                previewElement.appendChild(img);
            });
            img.addEventListener('error', () => {
                previewElement.classList.add('empty');
                previewElement.innerHTML = '';
            });
        }
    },
    
    /**
     * 收集表单数据
     * @returns {Object} 表单数据
     */
    collectFormData() {
        // 基本信息
        const outfitData = {
            name: elements.outfitName.value.trim(),
            category_id: elements.categoryId.value ? parseInt(elements.categoryId.value) : null,
            image_url: elements.imageUrl.value.trim(),
            description: elements.description.value.trim(),
            recommendation_reason: elements.recommendationReason.value.trim(),
            sort_order: parseInt(elements.sortOrder.value) || 0,
            status: parseInt(elements.status.value),
            items: []
        };
        
        // 如果是编辑模式，添加ID
        if (state.isEditing) {
            outfitData.id = state.outfitId;
        }
        
        // 收集商品数据
        const itemRows = elements.itemsContainer.querySelectorAll('.item-row');
        itemRows.forEach((row, index) => {
            const nameInput = row.querySelector('.item-name');
            const imageUrlInput = row.querySelector('.item-image-url');
            const priceInput = row.querySelector('.item-price');
            const purchaseUrlInput = row.querySelector('.item-purchase-url');
            const sortOrderInput = row.querySelector('.item-sort-order');
            
            const item = {
                name: nameInput.value.trim(),
                image_url: imageUrlInput.value.trim(),
                price: parseFloat(priceInput.value) || 0,
                purchase_url: purchaseUrlInput.value.trim(),
                sort_order: parseInt(sortOrderInput.value) || index
            };
            
            // 如果是编辑模式且有ID，添加ID
            if (state.isEditing && state.outfitData && state.outfitData.items && state.outfitData.items[index]) {
                item.id = state.outfitData.items[index].id;
            }
            
            outfitData.items.push(item);
        });
        
        return outfitData;
    },
    
    /**
     * 验证表单数据
     * @param {Object} data 表单数据
     * @returns {boolean} 是否验证通过
     */
    validateFormData(data) {
        // 验证基本信息
        if (!data.name) {
            this.showError('请输入穿搭名称');
            elements.outfitName.focus();
            return false;
        }
        
        if (!data.image_url) {
            this.showError('请输入或上传穿搭图片');
            elements.imageUrl.focus();
            return false;
        }
        
        // 验证商品列表
        if (data.items.length === 0) {
            this.showError('请至少添加一个商品');
            elements.addItemBtn.focus();
            return false;
        }
        
        for (let i = 0; i < data.items.length; i++) {
            const item = data.items[i];
            
            if (!item.name) {
                this.showError(`第 ${i + 1} 个商品缺少名称`);
                return false;
            }
            
            if (!item.image_url) {
                this.showError(`第 ${i + 1} 个商品缺少图片`);
                return false;
            }
            
            if (item.price <= 0) {
                this.showError(`第 ${i + 1} 个商品价格必须大于0`);
                return false;
            }
            
            if (!item.purchase_url) {
                this.showError(`第 ${i + 1} 个商品缺少购买链接`);
                return false;
            }
        }
        
        return true;
    },
    
    /**
     * 保存推荐穿搭
     */
    async saveOutfit() {
        // 收集并验证表单数据
        const formData = this.collectFormData();
        
        if (!this.validateFormData(formData)) {
            return;
        }
        
        try {
            elements.loadingOverlay.style.display = 'flex';
            
            let result;
            
            if (state.isEditing) {
                // 更新穿搭
                result = await api.updateOutfit(formData);
                this.showSuccess('推荐穿搭更新成功');
            } else {
                // 添加穿搭
                result = await api.addOutfit(formData);
                this.showSuccess('推荐穿搭添加成功');
                
                // 清空表单数据
                elements.outfitName.value = '';
                elements.categoryId.value = '';
                elements.imageUrl.value = '';
                elements.description.value = '';
                elements.recommendationReason.value = '';
                elements.sortOrder.value = '0';
                elements.status.value = '1';
                elements.itemsContainer.innerHTML = '';
                elements.imagePreview.innerHTML = '';
                elements.imagePreview.classList.add('empty');
            }
            
            elements.loadingOverlay.style.display = 'none';
            
            // 3秒后跳转回列表页
            setTimeout(() => {
                window.location.href = 'recommended_outfit_list.html';
            }, 3000);
        } catch (error) {
            console.error('保存穿搭失败:', error);
            elements.loadingOverlay.style.display = 'none';
            this.showError('保存失败：' + error.message);
        }
    },
    
    /**
     * 显示错误信息
     * @param {string} message 错误消息
     */
    showError(message) {
        elements.errorMessage.textContent = message;
        elements.errorMessage.style.display = 'block';
        elements.successMessage.style.display = 'none';
        
        // 自动隐藏
        setTimeout(() => {
            elements.errorMessage.style.display = 'none';
        }, 5000);
    },
    
    /**
     * 显示成功信息
     * @param {string} message 成功消息
     */
    showSuccess(message) {
        elements.successMessage.textContent = message;
        elements.successMessage.style.display = 'block';
        elements.errorMessage.style.display = 'none';
    },
    
    /**
     * 初始化页面事件
     */
    initEvents() {
        // 添加商品按钮
        elements.addItemBtn.addEventListener('click', () => {
            this.addItemRow();
        });
        
        // 取消按钮
        elements.cancelBtn.addEventListener('click', () => {
            window.location.href = 'recommended_outfit_list.html';
        });
        
        // 保存按钮
        elements.saveBtn.addEventListener('click', () => {
            this.saveOutfit();
        });
        
        // 图片URL输入
        elements.imageUrl.addEventListener('input', () => {
            this.updateImagePreview(elements.imagePreview, elements.imageUrl.value);
        });
        
        // 上传图片按钮
        elements.uploadImageBtn.addEventListener('click', () => {
            elements.imageUpload.click();
        });
        
        // 图片上传
        elements.imageUpload.addEventListener('change', async (event) => {
            const file = event.target.files[0];
            if (!file) return;
            
            try {
                elements.loadingOverlay.style.display = 'flex';
                
                // 上传图片
                const result = await api.uploadImage(file);
                
                // 提取图片URL (处理不同的返回格式)
                const imageUrl = result.data && result.data.image_url ? 
                                 result.data.image_url : 
                                 (result.image_url || '');
                
                console.log('获取到的商品图片URL: ', imageUrl);
                
                // 更新URL和预览
                elements.imageUrl.value = imageUrl;
                this.updateImagePreview(elements.imagePreview, imageUrl);
                
                elements.loadingOverlay.style.display = 'none';
            } catch (error) {
                console.error('上传商品图片失败:', error);
                elements.loadingOverlay.style.display = 'none';
                this.showError('上传商品图片失败：' + error.message);
            }
        });
        
        // 退出登录按钮
        elements.logoutBtn.addEventListener('click', () => {
            Auth.logout();
            window.location.href = 'index.html';
        });
        
        // 显示用户名
        document.getElementById('userName').textContent = Auth.getUsername() || '管理员';
    },
    
    /**
     * 初始化页面
     */
    async init() {
        // 检查登录状态
        if (!Auth.isLoggedIn()) {
            window.location.href = 'index.html';
            return;
        }
        
        try {
            // 检查是否是编辑模式
            const urlParams = new URLSearchParams(window.location.search);
            const outfitId = urlParams.get('id');
            
            state.isEditing = !!outfitId;
            state.outfitId = outfitId ? parseInt(outfitId) : null;
            
            // 更新页面标题
            elements.pageTitle.textContent = state.isEditing ? '编辑推荐穿搭' : '新增推荐穿搭';
            
            // 初始化事件
            this.initEvents();
            
            // 加载分类列表
            await this.loadCategories();
            
            // 如果是编辑模式，加载穿搭详情
            if (state.isEditing) {
                await this.loadOutfitDetail(state.outfitId);
            } else {
                // 添加一个空商品行
                this.addItemRow();
            }
        } catch (error) {
            console.error('初始化页面失败:', error);
            this.showError('初始化页面失败，请刷新重试');
        }
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    handlers.init();
}); 