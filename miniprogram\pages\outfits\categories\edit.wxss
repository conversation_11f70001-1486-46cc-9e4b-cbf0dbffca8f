/* pages/outfits/categories/edit.wxss */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f8f8f8;
  min-height: 100vh;
  padding: 20px;
  box-sizing: border-box;
}

.form-container {
  width: 100%;
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.form-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

.form-group {
  margin-bottom: 20px;
  position: relative;
}

.form-label {
  font-size: 15px;
  color: #333;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  height: 44px;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 15px;
  color: #333;
  box-sizing: border-box;
  border: 1px solid #eee;
}

.form-textarea {
  width: 100%;
  height: 100px;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 10px 12px;
  font-size: 15px;
  color: #333;
  box-sizing: border-box;
  border: 1px solid #eee;
}

.character-count {
  position: absolute;
  right: 5px;
  bottom: -22px;
  font-size: 12px;
  color: #999;
}

.checkbox-container {
  display: flex;
  align-items: center;
  padding: 5px 0;
}

.checkbox-label {
  margin-left: 8px;
  font-size: 15px;
  color: #333;
}

.form-tips {
  font-size: 13px;
  color: #999;
  margin-top: 5px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.bottom-buttons {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.cancel-btn, .submit-btn {
  flex: 1;
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 16px;
  border-radius: 8px;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 10px;
}

.submit-btn {
  background-color: #007aff;
  color: #fff;
  margin-left: 10px;
}

.submit-btn[disabled] {
  background-color: #cccccc;
  color: #ffffff;
}