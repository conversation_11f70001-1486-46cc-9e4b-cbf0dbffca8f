<view class="container">
  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 上传区域 -->
    <view class="upload-area" bindtap="chooseImage">
      <block wx:if="{{!tempImagePath}}">
        <text class="icon-camera"></text>
        <view class="upload-text">上传衣物照片</view>
        <view class="upload-desc">系统将自动抠图生成透明背景</view>
      </block>
      <block wx:else>
        <image src="{{tempImagePath}}" mode="aspectFit" class="preview-image" style="transform: rotate({{rotationAngle}}deg) scale({{scaleValue}});"></image>
      </block>
    </view>
    
    <!-- 图片编辑功能模块导航 -->
    <view class="modules-nav" wx:if="{{tempImagePath}}">
      <view class="module-tab {{isSegmentExpanded ? 'active' : ''}}" bindtap="toggleSegmentExpand">
        <text class="module-tab-text">抠图</text>
      </view>
      <view class="module-tab {{isRotationExpanded ? 'active' : ''}}" bindtap="toggleRotationExpand">
        <text class="module-tab-text">旋转</text>
      </view>
      <view class="module-tab {{isScaleExpanded ? 'active' : ''}}" bindtap="toggleScaleExpand">
        <text class="module-tab-text">缩放</text>
      </view>
    </view>
    
    <!-- 模块内容区域 -->
    <view class="modules-content" wx:if="{{tempImagePath}}">
      <!-- 抠图模块内容 -->
      <view class="module-content-item {{isSegmentExpanded ? 'visible' : 'hidden'}}">
        <view class="segment-switch-container">
          <view class="segment-switch">
            <text class="switch-label">抠图开关</text>
            <switch 
              checked="{{segmentEnabled}}" 
              bindchange="toggleSegment" 
              color="#000"
            />
          </view>
          <text class="segment-desc">{{segmentEnabled ? '已开启抠图' : '开启抠图可去除背景，建议抠图后再旋转'}}</text>
        </view>
      </view>
      
      <!-- 旋转模块内容 -->
      <view class="module-content-item {{isRotationExpanded ? 'visible' : 'hidden'}}">
        <view class="rotation-control">
          <view class="rotation-title">
            <text>图片旋转调整</text>
            <text class="rotation-angle">{{rotationAngle}}°</text>
          </view>
          
          <view class="rotation-actions">
            <view class="rotation-btn" bindtap="rotateLeft">
              <text class="rotation-icon">⟲</text>
              <text class="rotation-text">向左转</text>
            </view>
            
            <slider 
              class="rotation-slider" 
              min="0" 
              max="359" 
              value="{{rotationAngle}}" 
              bindchange="onRotationChange" 
              show-value="{{false}}"
              step="1"
              activeColor="#000"
              block-size="20"
            />
            
            <view class="rotation-btn" bindtap="rotateRight">
              <text class="rotation-icon">⟳</text>
              <text class="rotation-text">向右转</text>
            </view>
          </view>
          
          <view class="reset-btn" bindtap="resetRotation">
            <text>重置角度</text>
          </view>
        </view>
      </view>
      
      <!-- 缩放模块内容 -->
      <view class="module-content-item {{isScaleExpanded ? 'visible' : 'hidden'}}">
        <view class="rotation-control">
          <view class="scale-title">
            <text>图片缩放调整</text>
            <text class="scale-value">{{scaleValue}}x</text>
          </view>
          
          <view class="scale-actions">
            <view class="scale-btn" bindtap="scaleDown">
              <text class="scale-icon">－</text>
              <text class="scale-text">缩小</text>
            </view>
            
            <slider 
              class="scale-slider" 
              min="0.5" 
              max="2.0" 
              value="{{scaleValue}}" 
              bindchange="onScaleChange" 
              show-value="{{false}}"
              step="0.1"
              activeColor="#000"
              block-size="20"
            />
            
            <view class="scale-btn" bindtap="scaleUp">
              <text class="scale-icon">＋</text>
              <text class="scale-text">放大</text>
            </view>
          </view>
          
          <view class="reset-btn" bindtap="resetScale">
            <text>重置缩放</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 衣物类别 -->
    <view class="section">
      <view class="section-title">选择衣物类别</view>
      <view class="tag-container">
        <view 
          wx:for="{{categories}}" 
          wx:key="value" 
          class="tag {{category === item.value ? 'selected' : ''}}"
          bindtap="selectCategory"
          data-category="{{item.value}}"
        >
          {{item.name}}
        </view>
        <view class="tag custom-category-btn" bindtap="goToClothingCategories">+ 自定义</view>
      </view>
    </view>
    
    <!-- 衣物标签 -->
    <view class="section">
      <view class="section-title">添加衣物标签</view>
      <view class="tag-container">
        <view 
          wx:for="{{predefinedTags}}" 
          wx:key="id" 
          class="tag {{selectedTags[item.id] ? 'selected' : ''}}"
          bindtap="toggleTag"
          data-tag-id="{{item.id}}"
        >
          {{item.name}}
        </view>
        <view 
          wx:for="{{customTags}}" 
          wx:key="id" 
          class="tag {{selectedTags[item.id] ? 'selected' : ''}}"
          bindtap="toggleTag"
          data-tag-id="{{item.id}}"
        >
          {{item.name}}
        </view>
        <view class="tag" bindtap="showCustomTagInput">+ 自定义</view>
      </view>
    </view>
    
    <!-- 自定义标签输入框 -->
    <view class="custom-tag-input" wx:if="{{showCustomTagInput}}">
      <input 
        class="tag-input" 
        placeholder="请输入自定义标签" 
        value="{{customTagText}}" 
        bindinput="onCustomTagInput"
        bindconfirm="addCustomTag"
        focus="{{showCustomTagInput}}"
      />
      <button class="tag-btn" bindtap="addCustomTag">添加</button>
      <button class="tag-btn cancel" bindtap="cancelCustomTag">取消</button>
    </view>
    
    <!-- 已选标签展示，确保单独显示每个标签 -->
    <view class="section" wx:if="{{Object.keys(selectedTags).length > 0}}">
      <view class="section-title">已选标签</view>
      <view class="tag-container">
        <!-- 显示选中的预设标签 -->
        <block wx:for="{{predefinedTags}}" wx:key="id">
          <view wx:if="{{selectedTags[item.id]}}" class="tag selected" bindtap="toggleTag" data-tag-id="{{item.id}}">
            {{item.name}}
            <text class="tag-delete" catchtap="toggleTag" data-tag-id="{{item.id}}">×</text>
          </view>
        </block>
        <!-- 显示选中的自定义标签 -->
        <block wx:for="{{customTags}}" wx:key="id">
          <view wx:if="{{selectedTags[item.id]}}" class="tag selected" bindtap="toggleTag" data-tag-id="{{item.id}}">
            {{item.name}}
            <text class="tag-delete" catchtap="toggleTag" data-tag-id="{{item.id}}">×</text>
          </view>
        </block>
      </view>
    </view>
    
    <!-- 衣物信息 -->
    <view class="section">
      <view class="section-title">衣物信息（选填）</view>
      
      <!-- 衣橱选择 -->
      <view class="wardrobe-selector">
        <view class="wardrobe-label">所属衣橱:</view>
        <picker bindchange="bindWardrobeChange" range="{{wardrobeList}}" range-key="name">
          <view class="wardrobe-picker">
            <text>{{selectedWardrobeName}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="input-group">
        <view class="input-item">
          <input type="text" placeholder="名称" value="{{name}}" bindinput="inputName" />
        </view>
        <view class="input-item">
          <input type="text" placeholder="颜色" value="{{descriptionFields.color}}" bindinput="inputDescription" data-field="color" />
        </view>
        <view class="input-item">
          <input type="text" placeholder="品牌" value="{{descriptionFields.brand}}" bindinput="inputDescription" data-field="brand" />
        </view>
        <view class="input-item">
          <input type="text" placeholder="价格" value="{{descriptionFields.price}}" bindinput="inputDescription" data-field="price" type="digit" />
        </view>
        <view class="input-item">
          <input type="text" placeholder="备注" value="{{descriptionFields.notes}}" bindinput="inputDescription" data-field="notes" />
        </view>
      </view>
    </view>
  </view>
  
  <!-- 旋转用的隐藏canvas -->
  <canvas type="2d" id="rotateCanvas" style="position: absolute; width: 500px; height: 500px; left: -9999px; top: 0px; z-index: -100;"></canvas>
  
  <!-- 底部保存按钮 -->
  <view class="bottom-bar">
    <button class="save-btn" bindtap="submitForm" disabled="{{uploadingImage || savingData}}">保存修改</button>
  </view>
</view> 