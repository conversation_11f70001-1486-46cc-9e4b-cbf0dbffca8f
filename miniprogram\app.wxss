/**app.wxss**/

page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  background-color: #f7f7f7;
  color: #333;
  font-size: 14px;
  box-sizing: border-box;
}

/* 通用样式 */
.container {
  min-height: 100vh;
  padding: 0 15px;
  box-sizing: border-box;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.btn-primary {
  background-color: #000;
  color: #fff;
  border-radius: 8px;
  height: 42px;
  line-height: 42px;
  text-align: center;
  border: none;
}

.btn-primary::after {
  border: none;
}

.section {
  padding: 15px 0;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

.text-center {
  text-align: center;
}

.text-primary {
  color: #000;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

.text-sm {
  font-size: 12px;
}

.text-lg {
  font-size: 16px;
}

.text-xl {
  font-size: 18px;
}

.text-bold {
  font-weight: 500;
}

.mt-10 {
  margin-top: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mr-5 {
  margin-right: 5px;
}

.ml-5 {
  margin-left: 5px;
}

/* 自定义Toast样式 */
.custom-toast {
  position: fixed;
  bottom: 120rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  pointer-events: none;
}

.custom-toast-content {
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  min-width: 400rpx;
  max-width: 80%;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.custom-toast-text {
  color: #fff;
  font-size: 28rpx;
  text-align: center;
  word-break: break-all;
  white-space: normal;
} 