2025-07-01 11:22:39 收到请求: city=, cityid=, lat=, lon=
2025-07-01 11:22:39 未提供任何位置参数，请求端需要更新以提供位置信息
2025-07-01 11:22:39 使用默认城市ID（配置值）: 101210101
2025-07-01 11:22:39 [REQUEST] 请求天气数据
Data: {"longitude":null,"latitude":null,"location_param":"101210101","api_key":"99b4a9...fb1d","api_host":"kq2k5mg4v2.re.qweatherapi.com"}
2025-07-01 11:22:39 城市信息: {"id":"101210101","name":"Hangzhou","adm1":"Zhejiang","adm2":"浙江省,Hangzhou","lat":30.246,"lon":120.2108}
2025-07-01 11:22:39 使用正式环境API: kq2k5mg4v2.re.qweatherapi.com
2025-07-01 11:22:39 [CONFIG] 使用正式环境API
Data: {"api_host":"kq2k5mg4v2.re.qweatherapi.com","api_key":"99b4a9...fb1d"}
2025-07-01 11:22:39 直接请求和风天气API: https://kq2k5mg4v2.re.qweatherapi.com/v7/weather/now?location=101210101&key=99b4a915afcc4ac2ba654f1a436cfb1d&lang=zh&unit=m&t=1751340159
2025-07-01 11:22:39 [API_REQUEST_DETAILED] 天气API详细请求信息
Data: {"url":"https:\/\/kq2k5mg4v2.re.qweatherapi.com\/v7\/weather\/now?location=101210101&key=99b4a915afcc4ac2ba654f1a436cfb1d&lang=zh&unit=m&t=1751340159","method":"GET","host":"kq2k5mg4v2.re.qweatherapi.com","path":"\/v7\/weather\/now","params":{"location":"101210101","key":"99b4a915afcc4ac2ba654f1a436cfb1d","lang":"zh","unit":"m","t":1751340159},"location_param":"101210101","start_time":"2025-07-01 11:22:39.000000","location_type":"city_id"}
2025-07-01 11:22:39 使用请求头: ["User-Agent: WeatherApp\/1.0","Referer: https:\/\/cyyg.alidog.cn"]
2025-07-01 11:22:39 [API_REQUEST] 发送API请求
Data: {"url":"https:\/\/kq2k5mg4v2.re.qweatherapi.com\/v7\/weather\/now?location=101210101&key=99b4a915afcc4ac2ba654f1a436cfb1d&lang=zh&unit=m&t=1751340159","headers":["User-Agent: WeatherApp\/1.0","Referer: https:\/\/cyyg.alidog.cn"],"use_prod":true}
2025-07-01 11:22:40 [API_RESPONSE] API请求结果
Data: {"http_code":200,"error":"无","response_preview":"{\"code\":\"200\",\"updateTime\":\"2025-07-01T11:18+08:00\",\"fxLink\":\"https:\/\/www.qweather.com\/weather\/hangzhou-101210101.html\",\"now\":{\"obsTime\":\"2025-07-01T11:16+08:00\",\"temp\":\"34\",\"feelsLike\":\"35\",\"icon\":\"1","duration_ms":166.08,"is_success":true}
2025-07-01 11:22:40 [API_RESPONSE_DETAILED] 天气API详细响应信息
Data: {"url":"https:\/\/kq2k5mg4v2.re.qweatherapi.com\/v7\/weather\/now?location=101210101&key=99b4a915afcc4ac2ba654f1a436cfb1d&lang=zh&unit=m&t=1751340159","http_code":200,"error":"无","duration_ms":166.08,"content_type":"application\/json","size_download":315,"response_code":"unknown","is_valid_json":true,"has_weather_data":true,"weather_summary":null}
2025-07-01 11:22:40 API请求结果: HTTP状态码=200, 错误=无
2025-07-01 11:22:40 API响应信息: {"url":"https:\/\/kq2k5mg4v2.re.qweatherapi.com\/v7\/weather\/now?location=101210101&key=99b4a915afcc4ac2ba654f1a436cfb1d&lang=zh&unit=m&t=1751340159","content_type":"application\/json","http_code":200,"header_size":227,"request_size":248,"filetime":-1,"ssl_verify_result":20,"redirect_count":0,"total_time":0.165201,"namelookup_time":0.021314,"connect_time":0.053339,"pretransfer_time":0.124346,"size_upload":0,"size_download":315,"speed_download":1909,"speed_upload":0,"download_content_length":315,"upload_content_length":-1,"starttransfer_time":0.164668,"redirect_time":0,"redirect_url":"","primary_ip":"**************","certinfo":[],"primary_port":443,"local_ip":"*************","local_port":2214}
2025-07-01 11:22:40 API原始响应: {"code":"200","updateTime":"2025-07-01T11:18+08:00","fxLink":"https://www.qweather.com/weather/hangzhou-101210101.html","now":{"obsTime":"2025-07-01T11:16+08:00","temp":"34","feelsLike":"35","icon":"100","text":"晴","wind360":"135","windDir":"东南风","windScale":"3","windSpeed":"17","humidity":"51","precip":"0.0","pressure":"1006","vis":"30","cloud":"10","dew":"24"},"refer":{"sources":["QWeather"],"license":["QWeather Developers License"]}}
2025-07-01 11:22:40 API响应原始数据: {"code":"200","updateTime":"2025-07-01T11:18+08:00","fxLink":"https://www.qweather.com/weather/hangzhou-101210101.html","now":{"obsTime":"2025-07-01T11:16+08:00","temp":"34","feelsLike":"35","icon":"100","text":"晴","wind360":"135","windDir":"东南风","windScale":"3","windSpeed":"17","humidity":"51","precip":"0.0","pressure":"1006","vis":"30","cloud":"10","dew":"24"},"refer":{"sources":["QWeather"],"license":["QWeather Developers License"]}}
2025-07-01 11:22:40 API返回的fxLink: https://www.qweather.com/weather/hangzhou-101210101.html
2025-07-01 11:22:40 从fxLink提取的城市信息: 名称=hangzhou, ID=101210101
2025-07-01 11:22:40 成功获取天气数据: 城市=hangzhou, 温度=34
2025-07-01 11:22:40 缓存功能已禁用，跳过写入
2025-07-01 11:22:40 天气数据已保存到缓存
2025-07-01 11:22:40 天气数据诊断 - 来源: API, 城市: hangzhou, 温度: 34, 状态: 晴
