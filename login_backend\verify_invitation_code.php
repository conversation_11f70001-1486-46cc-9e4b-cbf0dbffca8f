<?php
/**
 * 验证邀请码API
 * 验证邀请码有效性并处理兑换
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 日志功能
function writeLog($message, $data = null) {
    $logDir = __DIR__ . '/logs';
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/invitation_code_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $logData = "[{$timestamp}] {$message}";
    
    if ($data !== null) {
        $logData .= " - " . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    file_put_contents($logFile, $logData . PHP_EOL, FILE_APPEND);
}

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

// 检查是否有Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '需要授权头']);
    exit;
}

// 验证token
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或过期的令牌']);
    exit;
}

// 获取用户ID
$userId = $payload['sub'];
writeLog("用户ID", ['user_id' => $userId]);

// 获取请求数据
$rawData = file_get_contents('php://input');
$requestData = json_decode($rawData, true);
writeLog("请求数据", $requestData);

// 验证必要字段
if (!isset($requestData['code']) || empty($requestData['code'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少邀请码']);
    exit;
}

if (!isset($requestData['analysis_id']) || empty($requestData['analysis_id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少分析ID']);
    exit;
}

$invitationCode = trim($requestData['code']);
$analysisId = intval($requestData['analysis_id']);

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

try {
    // 开始事务
    $conn->beginTransaction();
    
    // 查询邀请码
    $codeStmt = $conn->prepare("
        SELECT * FROM invitation_codes
        WHERE code = :code AND status = 'unused' AND (expired_at IS NULL OR expired_at > NOW()) AND type = 'image_analysis'
    ");
    $codeStmt->bindParam(':code', $invitationCode);
    $codeStmt->execute();
    $codeData = $codeStmt->fetch(PDO::FETCH_ASSOC);
    
    // 检查邀请码是否有效
    if (!$codeData) {
        http_response_code(400);
        echo json_encode(['error' => true, 'msg' => '邀请码无效、已使用或已过期']);
        exit;
    }
    
    // 检查分析记录是否存在且属于当前用户
    $analysisStmt = $conn->prepare("
        SELECT id, payment_status FROM user_image_analysis
        WHERE id = :analysis_id AND user_id = :user_id
    ");
    $analysisStmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
    $analysisStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $analysisStmt->execute();
    $analysis = $analysisStmt->fetch(PDO::FETCH_ASSOC);
    
    // 检查分析记录是否存在
    if (!$analysis) {
        $conn->rollBack();
        http_response_code(404);
        echo json_encode(['error' => true, 'msg' => '未找到形象分析记录或该记录不属于您']);
        exit;
    }
    
    // 检查是否已经是已支付状态
    if ($analysis['payment_status'] === 'paid') {
        $conn->rollBack();
        echo json_encode([
            'error' => false,
            'msg' => '该记录已经是已支付状态',
            'data' => [
                'analysis_id' => $analysisId
            ]
        ]);
        exit;
    }
    
    // 更新邀请码状态
    $updateCodeStmt = $conn->prepare("
        UPDATE invitation_codes
        SET status = 'used', used_by = :user_id, used_at = NOW()
        WHERE id = :code_id
    ");
    $updateCodeStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $updateCodeStmt->bindParam(':code_id', $codeData['id'], PDO::PARAM_INT);
    $updateCodeStmt->execute();
    
    // 更新分析记录支付状态
    $updateAnalysisStmt = $conn->prepare("
        UPDATE user_image_analysis
        SET payment_status = 'paid'
        WHERE id = :analysis_id
    ");
    $updateAnalysisStmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
    $updateAnalysisStmt->execute();
    
    // 提交事务
    $conn->commit();
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'msg' => '邀请码验证成功，已为您解锁形象分析服务',
        'data' => [
            'analysis_id' => $analysisId
        ]
    ]);
    
    writeLog("邀请码验证成功", [
        'code' => $invitationCode,
        'analysis_id' => $analysisId,
        'user_id' => $userId
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '处理邀请码失败: ' . $e->getMessage()]);
    writeLog("异常", ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
} 