/* 页面容器 */
page {
  background-color: #f9f9f9;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 180px; /* 增加底部内边距，确保底部选择区域不会遮挡内容 */
}

/* 类别选项卡 */
.category-tabs {
  display: flex;
  background-color: #fff;
  padding: 12px 15px;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  overflow-x: auto;
  white-space: nowrap;
}

.tab-item {
  padding: 8px 16px;
  margin-right: 8px;
  border-radius: 16px;
  font-size: 13px;
  color: #666;
  background-color: #f5f5f5;
  display: inline-block;
}

.tab-item.active {
  background-color: #000;
  color: #fff;
}

/* 衣物网格 */
.clothes-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-auto-flow: dense; /* 紧凑排列，让空缺自动填充 */
  gap: 15px;
  padding: 15px;
}

.clothes-item {
  aspect-ratio: 1/1; /* 正方形容器，适合上衣、裤子和外套 */
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.08);
  position: relative;
  background-color: transparent; /* 完全透明背景 */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px; /* 内边距确保内容不贴边 */
  border: 1px solid rgba(0,0,0,0.05); /* 添加细边框代替阴影 */
}

/* 裙子专用样式 */
.skirt-item {
  aspect-ratio: 3/5; /* 更高的容器比例，适合展示长裙 */
}

.clothes-img {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain; /* 保持内容比例 */
  background-color: transparent; /* 完全透明背景 */
}

.clothes-item.selected::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.3);
  border: 2px solid #000;
  border-radius: 12px;
}

.select-icon {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  border-radius: 10px;
  background-color: #000;
  color: white;
  display: none;
  align-items: center;
  justify-content: center;
}

.clothes-item.selected .select-icon {
  display: flex;
}

.check-icon {
  font-size: 14px;
  line-height: 1;
}

/* 底部按钮 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  padding: 10px 15px;
  padding-bottom: calc(env(safe-area-inset-bottom) + 10px);
  z-index: 100;
}

.bottom-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.proceed-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  background-color: #ccc;
  color: #666;
}

.proceed-btn.active {
  background-color: #000;
  color: #fff;
}

.nav-back-btn {
  background-color: transparent;
  border: 1px solid #000000;
  color: #000000;
  font-size: 13px;
  padding: 0 15px;
  border-radius: 22px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  margin-right: 12px;
}

/* 裙子搭配提示弹框 */
.skirt-tip-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.skirt-tip-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 16px;
  padding: 30px 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.skirt-tip-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}

.skirt-tip-text {
  width: 100%;
  font-size: 14px;
  line-height: 1.6;
  color: #666;
  margin-bottom: 25px;
}

.skirt-tip-text view {
  margin-bottom: 12px;
}

.highlight {
  color: #000;
  font-weight: 600;
}

.skirt-tip-btn {
  background-color: #000;
  color: #fff;
  padding: 10px 30px;
  border-radius: 20px;
  font-size: 15px;
  font-weight: 500;
}

/* 已选衣物预览 */
.selected-preview {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 10px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.preview-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.selected-items {
  display: flex;
  overflow-x: auto;
  padding-bottom: 5px;
}

.selected-item {
  width: 50px;
  height: 50px;
  margin-right: 10px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  flex-shrink: 0;
}

.selected-item-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-icon {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 18px;
  height: 18px;
  border-radius: 9px;
  background-color: #ff4d5000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.remove-img {
  width: 12px;
  height: 12px;
}

.next-icon {
  margin-right: 8px;
}

/* 空状态提示 */
.empty-clothes-tip {
  width: 100%;
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  grid-column: 1 / -1; /* 占据整行 */
  text-align: center;
}

.empty-icon {
  font-size: 40px;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 16px;
  color: #999;
}

/* 添加衣柜选择器样式 */
.wardrobe-switcher {
  padding: 10rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.wardrobe-scroll {
  width: 100%;
  white-space: nowrap;
}

.wardrobe-option {
  display: inline-block;
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 30rpx;
  background-color: #f5f5f5;
  transition: all 0.3s;
}

.wardrobe-option.active {
  color: #fff;
  background-color: #000;
} 