-- 创建双层试衣次数系统的数据库升级脚本

-- 1. 重命名现有的try_on_count字段为paid_try_on_count
ALTER TABLE users 
CHANGE COLUMN try_on_count paid_try_on_count INT NOT NULL DEFAULT 0 COMMENT '付费试衣次数';

-- 2. 添加免费试衣次数字段
ALTER TABLE users 
ADD COLUMN free_try_on_count INT NOT NULL DEFAULT 1 COMMENT '免费试衣次数，每日自动刷新为1' 
AFTER status;

-- 3. 创建索引以优化查询效率
CREATE INDEX idx_free_try_on_count ON users(free_try_on_count);
CREATE INDEX idx_paid_try_on_count ON users(paid_try_on_count);

-- 注: 如果idx_try_on_count索引已经存在，需要先删除
-- 检查索引是否存在，如果存在则删除
-- DROP INDEX IF EXISTS idx_try_on_count ON users;

-- 4. 初始化所有用户的免费次数为1（如果之前已有该字段则此步骤可能不需要）
UPDATE users SET free_try_on_count = 1; 