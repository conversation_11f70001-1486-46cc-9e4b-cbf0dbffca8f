<?php
/**
 * TOP API: taobao.tbk.item.convert request
 * 
 * <AUTHOR> create
 * @since 1.0, 2022.05.16
 */
class TbkItemConvertRequest
{
    /**
     * 广告位id，区分效果位置
     **/
    private $adzoneId;
    
    /**
     * 需返回的字段列表
     **/
    private $fields;
    
    /**
     * 商品ID串，用','分割，从taobao.tbk.item.get接口获取
     **/
    private $itemIds;
    
    /**
     * 链接形式：1：PC，2：无线，默认：1
     **/
    private $platform;
    
    /**
     * 自定义输入串，英文和数字组成，长度不能大于12个字符，区分不同的推广渠道
     **/
    private $unid;
    
    /**
     * 1表示商品转通用计划链接，0或不填表示转营销计划链接
     **/
    private $dx;
    
    private $apiParas = array();
    
    public function setAdzoneId($adzoneId)
    {
        $this->adzoneId = $adzoneId;
        $this->apiParas["adzone_id"] = $adzoneId;
    }
    
    public function getAdzoneId()
    {
        return $this->adzoneId;
    }
    
    public function setFields($fields)
    {
        $this->fields = $fields;
        $this->apiParas["fields"] = $fields;
    }
    
    public function getFields()
    {
        return $this->fields;
    }
    
    public function setItemIds($itemIds)
    {
        $this->itemIds = $itemIds;
        $this->apiParas["item_ids"] = $itemIds;
    }
    
    public function getItemIds()
    {
        return $this->itemIds;
    }
    
    public function setPlatform($platform)
    {
        $this->platform = $platform;
        $this->apiParas["platform"] = $platform;
    }
    
    public function getPlatform()
    {
        return $this->platform;
    }
    
    public function setUnid($unid)
    {
        $this->unid = $unid;
        $this->apiParas["unid"] = $unid;
    }
    
    public function getUnid()
    {
        return $this->unid;
    }
    
    public function setDx($dx)
    {
        $this->dx = $dx;
        $this->apiParas["dx"] = $dx;
    }
    
    public function getDx()
    {
        return $this->dx;
    }
    
    public function getApiMethodName()
    {
        return "taobao.tbk.item.convert";
    }
    
    public function getApiParas()
    {
        return $this->apiParas;
    }
    
    /**
     * 检查参数合法性
     */
    public function check()
    {
        // 必填参数检查
        RequestCheckUtil::checkNotNull($this->adzoneId, "adzone_id");
        RequestCheckUtil::checkNotNull($this->fields, "fields");
        RequestCheckUtil::checkNotNull($this->itemIds, "item_ids");
        
        // 其他可选参数的条件检查
        if (!is_null($this->unid)) {
            RequestCheckUtil::checkMaxLength($this->unid, 12, "unid");
        }
    }
} 