<?php
/**
 * TOP API: taobao.tbk.item.info.get request
 * 
 * <AUTHOR> create
 * @since 1.0, 2023.10.26
 */
class TbkItemInfoGetRequest
{
    /**
     * 商品ID，支持传入多个，以英文逗号分隔，最多40个
     **/
    private $numIids;
    
    /**
     * 链接形式：1：PC，2：无线，默认：1
     **/
    private $platform;
    
    /**
     * ip地址，影响邮费获取，如果不传或者传入不准确，邮费无法精准提供
     **/
    private $ip;
    
    /**
     * 1-动态ID转链场景，2-消费者比价场景（不填默认为1）
     **/
    private $bizSceneId;
    
    /**
     * 推广位id，mm_xx_xx_xx pid三段式中的第三段
     */
    private $adzoneId;
    
    /**
     * 会员运营ID
     */
    private $relationId;
    
    /**
     * 淘宝客外部用户标识，格式：字母 + 数字，长度大于等于6，小于等于32
     **/
    private $specialId;
    
    /**
     * 1-自购省，2-推广赚（代理模式专属ID，代理模式必填，非代理模式不用填写该字段）
     **/
    private $promotionType;
    
    private $apiParas = array();
    
    public function setNumIids($numIids)
    {
        $this->numIids = $numIids;
        $this->apiParas["num_iids"] = $numIids;
    }
    
    public function getNumIids()
    {
        return $this->numIids;
    }
    
    public function setPlatform($platform)
    {
        $this->platform = $platform;
        $this->apiParas["platform"] = $platform;
    }
    
    public function getPlatform()
    {
        return $this->platform;
    }
    
    public function setIp($ip)
    {
        $this->ip = $ip;
        $this->apiParas["ip"] = $ip;
    }
    
    public function getIp()
    {
        return $this->ip;
    }
    
    public function setBizSceneId($bizSceneId)
    {
        $this->bizSceneId = $bizSceneId;
        $this->apiParas["biz_scene_id"] = $bizSceneId;
    }
    
    public function getBizSceneId()
    {
        return $this->bizSceneId;
    }
    
    public function setAdzoneId($adzoneId)
    {
        $this->adzoneId = $adzoneId;
        $this->apiParas["adzone_id"] = $adzoneId;
    }
    
    public function getAdzoneId()
    {
        return $this->adzoneId;
    }
    
    public function setRelationId($relationId)
    {
        $this->relationId = $relationId;
        $this->apiParas["relation_id"] = $relationId;
    }
    
    public function getRelationId()
    {
        return $this->relationId;
    }
    
    public function setSpecialId($specialId)
    {
        $this->specialId = $specialId;
        $this->apiParas["special_id"] = $specialId;
    }
    
    public function getSpecialId()
    {
        return $this->specialId;
    }
    
    public function setPromotionType($promotionType)
    {
        $this->promotionType = $promotionType;
        $this->apiParas["promotion_type"] = $promotionType;
    }
    
    public function getPromotionType()
    {
        return $this->promotionType;
    }
    
    public function getApiMethodName()
    {
        return "taobao.tbk.item.info.get";
    }
    
    public function getApiParas()
    {
        return $this->apiParas;
    }
    
    public function check()
    {
    }
    
    public function putOtherTextParam($key, $value)
    {
        $this->apiParas[$key] = $value;
        $this->$key = $value;
    }
} 