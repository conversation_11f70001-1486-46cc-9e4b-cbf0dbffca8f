/**
 * 打赏记录管理模块
 */
const DonationList = {
    // 保存当前状态
    state: {
        page: 1,
        perPage: 10,
        totalPages: 1,
        searchParams: {}
    },
    
    /**
     * 初始化
     */
    init: function() {
        // 显示加载指示器
        document.getElementById('donationLoadingIndicator').style.display = 'block';
        
        // 绑定搜索事件
        this.bindSearchEvents();
        
        // 首次加载数据
        this.loadDonations();
    },
    
    /**
     * 绑定搜索事件
     */
    bindSearchEvents: function() {
        const searchBtn = document.getElementById('btnSearch');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.onSearch();
            });
        }
        
        // 回车键搜索
        const searchInputs = document.querySelectorAll('.search-form input');
        searchInputs.forEach(input => {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.onSearch();
                }
            });
        });
    },
    
    /**
     * 搜索事件处理
     */
    onSearch: function() {
        // 重置到第一页
        this.state.page = 1;
        
        // 获取搜索参数
        const userInput = document.getElementById('searchUser').value.trim();
        const startDate = document.getElementById('searchDateStart').value;
        const endDate = document.getElementById('searchDateEnd').value;
        
        // 保存搜索参数
        this.state.searchParams = {};
        
        if (userInput) {
            if (/^\d+$/.test(userInput)) {
                // 如果是纯数字，可能是用户ID
                this.state.searchParams.user_id = parseInt(userInput);
            } else {
                // 否则作为昵称搜索
                this.state.searchParams.nickname = userInput;
            }
        }
        
        if (startDate) {
            this.state.searchParams.start_date = startDate;
        }
        
        if (endDate) {
            this.state.searchParams.end_date = endDate;
        }
        
        // 重新加载数据
        this.loadDonations();
    },
    
    /**
     * 加载打赏记录
     */
    loadDonations: function() {
        // 显示加载指示器
        document.getElementById('donationLoadingIndicator').style.display = 'block';
        // 隐藏错误信息
        document.getElementById('donationErrorMessage').style.display = 'none';
        
        // 构建URL和参数
        const url = new URL('../login_backend/admin_get_donations.php', window.location.origin);
        
        // 添加分页参数
        url.searchParams.append('page', this.state.page);
        url.searchParams.append('per_page', this.state.perPage);
        
        // 添加搜索参数
        for (const key in this.state.searchParams) {
            url.searchParams.append(key, this.state.searchParams[key]);
        }
        
        // 发送请求
        fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': Auth.getToken()
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('获取打赏记录失败，请检查网络连接或权限');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                // 更新状态
                this.state.page = data.data.pagination.current_page;
                this.state.totalPages = data.data.pagination.total_pages;
                
                // 渲染数据
                this.renderDonations(data.data.donations);
                this.renderPagination();
                this.renderSummary(data.data.summary);
            } else {
                throw new Error(data.message || '获取打赏记录失败');
            }
        })
        .catch(error => {
            console.error('获取打赏记录失败:', error);
            document.getElementById('donationErrorMessage').textContent = `加载失败: ${error.message}`;
            document.getElementById('donationErrorMessage').style.display = 'block';
        })
        .finally(() => {
            // 隐藏加载指示器
            document.getElementById('donationLoadingIndicator').style.display = 'none';
        });
    },
    
    /**
     * 渲染打赏记录
     * @param {Array} donations 打赏记录列表
     */
    renderDonations: function(donations) {
        const tableBody = document.getElementById('donationTableBody');
        
        // 清空表格
        tableBody.innerHTML = '';
        
        if (donations && donations.length > 0) {
            // 添加数据行
            donations.forEach(donation => {
                const tr = document.createElement('tr');
                
                // ID
                const tdId = document.createElement('td');
                tdId.textContent = donation.id;
                tr.appendChild(tdId);
                
                // 用户信息
                const tdUser = document.createElement('td');
                const userInfo = document.createElement('div');
                userInfo.style.display = 'flex';
                userInfo.style.alignItems = 'center';
                
                const avatar = document.createElement('img');
                avatar.src = donation.avatar_url || donation.user_avatar || '/images/default-avatar.png';
                avatar.style.width = '32px';
                avatar.style.height = '32px';
                avatar.style.borderRadius = '50%';
                avatar.style.marginRight = '10px';
                avatar.onerror = function() {
                    this.src = '/images/default-avatar.png';
                };
                
                const userDetails = document.createElement('div');
                userDetails.innerHTML = `<div>${donation.nickname || donation.user_nickname || '未知用户'}</div>
                                        <div style="font-size: 12px; color: #999;">ID: ${donation.user_id}</div>`;
                
                userInfo.appendChild(avatar);
                userInfo.appendChild(userDetails);
                tdUser.appendChild(userInfo);
                
                tr.appendChild(tdUser);
                
                // 打赏金额
                const tdAmount = document.createElement('td');
                tdAmount.textContent = `¥${parseFloat(donation.amount).toFixed(2)}`;
                tdAmount.classList.add('amount');
                tr.appendChild(tdAmount);
                
                // 订单号
                const tdOrderId = document.createElement('td');
                tdOrderId.textContent = donation.order_id;
                tr.appendChild(tdOrderId);
                
                // 微信交易号
                const tdTransactionId = document.createElement('td');
                tdTransactionId.textContent = donation.transaction_id || '-';
                tr.appendChild(tdTransactionId);
                
                // 状态
                const tdStatus = document.createElement('td');
                let statusText = '未知';
                let statusClass = '';
                
                if (donation.status === 'success') {
                    statusText = '已支付';
                    statusClass = 'success';
                } else if (donation.status === 'pending') {
                    statusText = '待支付';
                    statusClass = 'pending';
                } else if (donation.status === 'failed') {
                    statusText = '已失败';
                    statusClass = 'failed';
                }
                
                const statusSpan = document.createElement('span');
                statusSpan.textContent = statusText;
                statusSpan.className = statusClass;
                statusSpan.style.padding = '2px 8px';
                statusSpan.style.borderRadius = '10px';
                statusSpan.style.fontSize = '12px';
                
                // 根据状态设置不同的颜色
                if (statusClass === 'success') {
                    statusSpan.style.backgroundColor = 'rgba(82, 196, 26, 0.1)';
                    statusSpan.style.color = '#52c41a';
                } else if (statusClass === 'pending') {
                    statusSpan.style.backgroundColor = 'rgba(24, 144, 255, 0.1)';
                    statusSpan.style.color = '#1890ff';
                } else if (statusClass === 'failed') {
                    statusSpan.style.backgroundColor = 'rgba(245, 34, 45, 0.1)';
                    statusSpan.style.color = '#f5222d';
                }
                
                tdStatus.appendChild(statusSpan);
                tr.appendChild(tdStatus);
                
                // 创建时间
                const tdCreatedAt = document.createElement('td');
                tdCreatedAt.textContent = this.formatDate(donation.created_at);
                tr.appendChild(tdCreatedAt);
                
                // 支付时间
                const tdPaidAt = document.createElement('td');
                tdPaidAt.textContent = donation.paid_at ? this.formatDate(donation.paid_at) : '-';
                tr.appendChild(tdPaidAt);
                
                // 将行添加到表格
                tableBody.appendChild(tr);
            });
        } else {
            // 没有数据，显示空行
            const tr = document.createElement('tr');
            const td = document.createElement('td');
            td.colSpan = 8;
            td.style.textAlign = 'center';
            td.style.padding = '20px';
            td.textContent = '暂无打赏记录';
            tr.appendChild(td);
            tableBody.appendChild(tr);
        }
    },
    
    /**
     * 渲染分页
     */
    renderPagination: function() {
        const pagination = document.getElementById('donationPagination');
        pagination.innerHTML = '';
        
        if (this.state.totalPages <= 1) {
            return;
        }
        
        // 添加"上一页"按钮
        if (this.state.page > 1) {
            const prevBtn = this.createPaginationItem('«', () => {
                this.gotoPage(this.state.page - 1);
            });
            pagination.appendChild(prevBtn);
        }
        
        // 确定显示哪些页码
        let startPage = Math.max(1, this.state.page - 2);
        let endPage = Math.min(this.state.totalPages, startPage + 4);
        
        // 调整，确保显示5个页码
        if (endPage - startPage < 4) {
            startPage = Math.max(1, endPage - 4);
        }
        
        // 添加页码按钮
        for (let i = startPage; i <= endPage; i++) {
            const pageItem = this.createPaginationItem(i, () => {
                this.gotoPage(i);
            });
            
            if (i === this.state.page) {
                pageItem.classList.add('active');
            }
            
            pagination.appendChild(pageItem);
        }
        
        // 添加"下一页"按钮
        if (this.state.page < this.state.totalPages) {
            const nextBtn = this.createPaginationItem('»', () => {
                this.gotoPage(this.state.page + 1);
            });
            pagination.appendChild(nextBtn);
        }
    },
    
    /**
     * 创建分页项
     * @param {string|number} text 按钮文本
     * @param {Function} clickHandler 点击处理函数
     * @returns {HTMLElement} 分页项元素
     */
    createPaginationItem: function(text, clickHandler) {
        const item = document.createElement('div');
        item.classList.add('pagination-item');
        item.textContent = text;
        item.addEventListener('click', clickHandler);
        return item;
    },
    
    /**
     * 跳转到指定页
     * @param {number} page 页码
     */
    gotoPage: function(page) {
        if (page < 1 || page > this.state.totalPages) {
            return;
        }
        
        this.state.page = page;
        this.loadDonations();
    },
    
    /**
     * 渲染统计信息
     * @param {Object} summary 统计数据
     */
    renderSummary: function(summary) {
        document.getElementById('totalDonations').textContent = summary.total_count;
        document.getElementById('totalAmount').textContent = `¥${summary.total_amount.toFixed(2)}`;
        document.getElementById('todayDonations').textContent = summary.today_count;
        document.getElementById('todayAmount').textContent = `¥${summary.today_amount.toFixed(2)}`;
    },
    
    /**
     * 格式化日期
     * @param {string} dateString 日期字符串
     * @returns {string} 格式化后的日期字符串
     */
    formatDate: function(dateString) {
        if (!dateString) return '-';
        
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return dateString;
        
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    }
}; 