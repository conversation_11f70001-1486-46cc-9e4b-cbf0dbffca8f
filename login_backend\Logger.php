<?php
/**
 * 日志记录工具类
 * 用于记录详细的调试信息到文件
 */
class Logger {
    private static $logDir = __DIR__ . '/logs/';
    
    /**
     * 记录日志
     * @param string $level 日志级别 (DEBUG, INFO, WARN, ERROR)
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @param string $filename 日志文件名（不含扩展名）
     */
    public static function log($level, $message, $context = [], $filename = 'app') {
        // 确保日志目录存在
        if (!is_dir(self::$logDir)) {
            mkdir(self::$logDir, 0755, true);
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $logFile = self::$logDir . $filename . '_' . date('Y-m-d') . '.log';
        
        // 格式化日志内容
        $logEntry = sprintf(
            "[%s] [%s] %s",
            $timestamp,
            $level,
            $message
        );
        
        // 如果有上下文数据，添加到日志中
        if (!empty($context)) {
            $logEntry .= "\nContext: " . json_encode($context, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        }
        
        $logEntry .= "\n" . str_repeat('-', 80) . "\n";
        
        // 写入日志文件
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 记录调试信息
     */
    public static function debug($message, $context = [], $filename = 'debug') {
        self::log('DEBUG', $message, $context, $filename);
    }
    
    /**
     * 记录信息
     */
    public static function info($message, $context = [], $filename = 'app') {
        self::log('INFO', $message, $context, $filename);
    }
    
    /**
     * 记录警告
     */
    public static function warn($message, $context = [], $filename = 'app') {
        self::log('WARN', $message, $context, $filename);
    }
    
    /**
     * 记录错误
     */
    public static function error($message, $context = [], $filename = 'error') {
        self::log('ERROR', $message, $context, $filename);
    }
    
    /**
     * 记录SQL查询
     */
    public static function sql($sql, $params = [], $result = null, $filename = 'sql') {
        $context = [
            'sql' => $sql,
            'params' => $params
        ];
        
        if ($result !== null) {
            if (is_array($result)) {
                $context['result_count'] = count($result);
                $context['result_sample'] = array_slice($result, 0, 3); // 只记录前3条结果
            } else {
                $context['result'] = $result;
            }
        }
        
        self::log('SQL', 'Database Query Executed', $context, $filename);
    }
    
    /**
     * 记录API请求
     */
    public static function apiRequest($endpoint, $method, $params = [], $response = null, $filename = 'api') {
        $context = [
            'endpoint' => $endpoint,
            'method' => $method,
            'params' => $params,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown'
        ];
        
        if ($response !== null) {
            $context['response'] = $response;
        }
        
        self::log('API', "API Request: $method $endpoint", $context, $filename);
    }
    
    /**
     * 记录用户操作
     */
    public static function userAction($userId, $action, $details = [], $filename = 'user_action') {
        $context = [
            'user_id' => $userId,
            'action' => $action,
            'details' => $details,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
        ];
        
        self::log('USER_ACTION', "User Action: $action", $context, $filename);
    }
}
?>
