<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>衣物编辑 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* 编辑页面样式 */
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-hint {
            font-size: 12px;
            color: #888;
            margin-top: 4px;
        }
        
        .required {
            color: #f5222d;
            margin-left: 2px;
        }
        
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }
        
        .col-md-6 {
            flex: 0 0 50%;
            padding: 0 10px;
            box-sizing: border-box;
        }
        
        @media (max-width: 768px) {
            .col-md-6 {
                flex: 0 0 100%;
            }
        }
        
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 4px;
            border: none;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-container {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn-cancel {
            background-color: #f7f7f7;
            color: #666;
        }
        
        .btn-cancel:hover {
            background-color: #e6e6e6;
        }
        
        .btn-submit {
            background-color: #1890ff;
            color: white;
        }
        
        .btn-submit:hover {
            background-color: #40a9ff;
        }
        
        .image-preview {
            width: 200px;
            height: 200px;
            border: 1px dashed #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            position: relative;
            overflow: hidden;
        }
        
        .image-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
        }
        
        .image-preview-text {
            color: #999;
            font-size: 14px;
        }
        
        .tag-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        
        .tag-item {
            display: inline-flex;
            align-items: center;
            background-color: #f7f7f7;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 13px;
        }
        
        .tag-remove {
            margin-left: 5px;
            cursor: pointer;
            color: #f5222d;
            font-weight: bold;
        }
        
        .tag-input {
            display: flex;
        }
        
        .tag-input input {
            flex: 1;
            margin-right: 8px;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            color: #1890ff;
            margin-bottom: 15px;
            text-decoration: none;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        .page-title {
            font-size: 20px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>衣物编辑</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="card">
                <a href="clothing_list.html" class="back-link">
                    &lt; 返回衣物列表
                </a>
                
                <h3 class="page-title" id="pageTitle">添加衣物</h3>
                
                <form id="clothingForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">衣物图片</label>
                                <div class="image-preview" id="imagePreview">
                                    <span class="image-preview-text">预览图片</span>
                                </div>
                                <input type="text" class="form-control" id="imageUrl" name="image_url" placeholder="输入图片URL">
                                <div class="form-hint">输入图片URL地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">衣物名称 <span class="required">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required placeholder="输入衣物名称">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">分类 <span class="required">*</span></label>
                                <select class="form-control" id="category" name="category" required>
                                    <option value="">请选择分类</option>
                                    <option value="tops">上衣</option>
                                    <option value="pants">裤子</option>
                                    <option value="skirts">裙子</option>
                                    <option value="coats">外套</option>
                                    <option value="shoes">鞋子</option>
                                    <option value="bags">包包</option>
                                    <option value="accessories">配饰</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">所属用户 <span class="required">*</span></label>
                                <select class="form-control" id="userId" name="user_id" required>
                                    <option value="">正在加载用户列表...</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">标签</label>
                        <div class="tag-input">
                            <input type="text" class="form-control" id="tagInput" placeholder="输入标签后按Enter添加">
                            <button type="button" class="btn btn-submit" id="addTagBtn">添加</button>
                        </div>
                        <div class="form-hint">添加多个标签描述衣物特征，例如：红色、棉质、夏季等</div>
                        <div class="tag-container" id="tagContainer"></div>
                        <input type="hidden" id="tags" name="tags">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">详细属性 (JSON格式)</label>
                        <textarea class="form-control" id="description" name="description" placeholder='{"color":"红色","material":"棉质","season":"夏季"}'></textarea>
                        <div class="form-hint">以JSON格式输入详细属性，键值对形式描述衣物特征</div>
                    </div>
                    
                    <div class="btn-container">
                        <button type="button" class="btn btn-cancel" onclick="window.location.href='clothing_list.html'">取消</button>
                        <button type="submit" class="btn btn-submit">保存衣物</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/clothing_edit.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为clothing
            Sidebar.init('clothing');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化衣物编辑
            ClothingEdit.init();
        });
    </script>
</body>
</html> 