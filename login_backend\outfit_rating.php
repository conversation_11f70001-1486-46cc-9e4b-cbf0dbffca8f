<?php
/**
 * Outfit Rating API
 * 
 * 接收用户上传的穿搭照片，发送到中转API获取评分，并保存到数据库
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * POST Parameters:
 * - image: The image file to upload (multipart/form-data)
 * 
 * Response:
 * {
 *   "error": false,
 *   "data": {
 *     "id": 1,
 *     "image_url": "https://example.com/photo.jpg",
 *     "score": "8.5",
 *     "rating_details": { ... },
 *     "created_at": "2023-03-31 12:00:00"
 *   }
 * }
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';
require_once '../vendor/autoload.php'; // 引入阿里云OSS SDK
require_once 'oss_helper.php';

// 引入OSS命名空间
use OSS\OssClient;
use OSS\Core\OssException;

// 设置日志文件
$log_file = __DIR__ . '/logs/outfit_rating_debug.log';

// 日志函数
function logDebug($message, $data = null) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] {$message}";
    
    if ($data !== null) {
        if (is_array($data) && isset($data['image_base64'])) {
            // 避免记录整个图片base64到日志
            $data['image_base64'] = substr($data['image_base64'], 0, 100) . '... [truncated]';
        }
        $log_message .= ': ' . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    
    file_put_contents($log_file, $log_message . PHP_EOL, FILE_APPEND);
}

// Set response content type
header('Content-Type: application/json');

// Handle CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 记录请求开始
logDebug("收到穿搭打分请求", ['method' => $_SERVER['REQUEST_METHOD']]);

// Check if Authorization header exists
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

// Check if the request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => 'Only POST method is allowed'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// Verify token
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid or expired token'
    ]);
    exit;
}

// Get user ID from token data
$userId = $tokenData['sub'];
logDebug("用户验证通过", ['user_id' => $userId]);

// 获取用户信息
$db = new Database();
$conn = $db->getConnection();
$stmt = $conn->prepare("SELECT id, nickname, avatar_url, gender FROM users WHERE id = :id");
$stmt->bindParam(':id', $userId);
$stmt->execute();
$userInfo = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$userInfo) {
    echo json_encode([
        'error' => true,
        'msg' => 'User not found'
    ]);
    exit;
}

logDebug("获取到用户信息", ['gender' => $userInfo['gender']]);

// Check if file was uploaded
$fileField = isset($_FILES['image']) ? 'image' : (isset($_FILES['photo']) ? 'photo' : null);

if (!$fileField || $_FILES[$fileField]['error'] !== UPLOAD_ERR_OK) {
    echo json_encode([
        'error' => true,
        'msg' => 'No image file uploaded or upload error'
    ]);
    exit;
}

// Validate file type
$allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
$fileType = $_FILES[$fileField]['type'];

if (!in_array($fileType, $allowedTypes)) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid file type. Only JPEG, PNG, GIF, and WEBP are allowed.'
    ]);
    exit;
}

// 生成文件扩展名
$extension = pathinfo($_FILES[$fileField]['name'], PATHINFO_EXTENSION);
if (empty($extension)) {
    // 根据MIME类型确定扩展名
    switch ($fileType) {
        case 'image/jpeg':
            $extension = 'jpg';
            break;
        case 'image/png':
            $extension = 'png';
            break;
        case 'image/gif':
            $extension = 'gif';
            break;
        case 'image/webp':
            $extension = 'webp';
            break;
        default:
            $extension = 'jpg';
    }
}

logDebug("准备处理上传的图片", ['file_type' => $fileType, 'extension' => $extension]);

try {
    // 首先将文件上传到OSS
    $ossFilename = 'outfit_rating_' . $userId . '_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
    $ossKey = OSS_PATH_PHOTOS . $ossFilename;

    // 初始化OSS辅助类
    $ossHelper = new OssHelper();
    
    // 上传文件到OSS
    $uploadResult = $ossHelper->uploadFile($_FILES[$fileField]['tmp_name'], $ossKey);
    
    if (!$uploadResult['success']) {
        echo json_encode([
            'error' => true,
            'msg' => '上传到OSS失败: ' . $uploadResult['error']
        ]);
        exit;
    }
    
    // 获取OSS图片URL
    $imageUrl = $uploadResult['url'];
    logDebug("图片上传到OSS成功", ['image_url' => $imageUrl]);
    
    // 将图片转换为Base64
    $imageData = file_get_contents($_FILES[$fileField]['tmp_name']);
    $base64Image = base64_encode($imageData);
    
    logDebug("图片已转换为Base64", ['base64_length' => strlen($base64Image)]);
    
    // 准备调用中转API
    $apiUrl = "https://cyyg.alidog.cn/中转API/outfit_rating_api.php"; // 使用完整URL，确保可以正确访问
    
    // 构建请求数据
    $requestData = [
        'image_base64' => $base64Image,
        'user_info' => [
            'gender' => $userInfo['gender']
        ]
    ];
    
    // 调用中转API获取评分
    $ch = curl_init($apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 30秒超时
    
    logDebug("正在调用中转API获取评分", ['api_url' => $apiUrl]);
    
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        logDebug("调用中转API失败", ['curl_error' => $error]);
        echo json_encode([
            'error' => true,
            'msg' => '调用评分服务失败: ' . $error
        ]);
        exit;
    }
    
    curl_close($ch);
    
    // 解析评分响应
    $ratingResult = json_decode($response, true);
    
    if (!$ratingResult || isset($ratingResult['error']) && $ratingResult['error'] === true) {
        logDebug("中转API返回错误", ['response' => $ratingResult]);
        echo json_encode([
            'error' => true,
            'msg' => isset($ratingResult['msg']) ? $ratingResult['msg'] : '评分服务返回错误'
        ]);
        exit;
    }
    
    logDebug("获取评分成功", ['rating' => $ratingResult]);
    
    // 数据库操作
    $conn = $db->getConnection();

    // 获取当前时间戳
    $now = date('Y-m-d H:i:s');
    
    // 准备评分数据
    $score = isset($ratingResult['data']['overall_score']) ? $ratingResult['data']['overall_score'] : 0;
    $ratingDetails = json_encode($ratingResult['data'], JSON_UNESCAPED_UNICODE);
    $aiComments = isset($ratingResult['data']['outfit_analysis']) ? $ratingResult['data']['outfit_analysis'] : '';
    $improvementSuggestions = isset($ratingResult['data']['improvement']) ? $ratingResult['data']['improvement'] : '';

    // 插入评分记录
    $sql = "INSERT INTO outfit_ratings (user_id, photo_url, score, rating_details, ai_comments, improvement_suggestions, created_at, updated_at) 
            VALUES (:user_id, :photo_url, :score, :rating_details, :ai_comments, :improvement_suggestions, :created_at, :updated_at)";
    
    $stmt = $conn->prepare($sql);
    
    $params = [
        'user_id' => $userId,
        'photo_url' => $imageUrl,
        'score' => $score,
        'rating_details' => $ratingDetails,
        'ai_comments' => $aiComments,
        'improvement_suggestions' => $improvementSuggestions,
        'created_at' => $now,
        'updated_at' => $now
    ];
    
    logDebug("准备插入评分记录", $params);
    
    $stmt->execute($params);

    // 获取新插入记录的ID
    $ratingId = $conn->lastInsertId();
    logDebug("成功插入评分记录", ['rating_id' => $ratingId]);

    // 返回评分数据
    echo json_encode([
        'error' => false,
        'data' => [
            'id' => $ratingId,
            'image_url' => $imageUrl,
            'score' => $score,
            'rating_details' => $ratingResult['data'],
            'created_at' => $now
        ]
    ]);
    
} catch (PDOException $e) {
    // 详细记录错误信息
    $errorMessage = $e->getMessage();
    $errorCode = $e->getCode();
    logDebug("数据库错误", ['code' => $errorCode, 'message' => $errorMessage]);
    
    // 返回通用错误，但带有更多信息
    echo json_encode([
        'error' => true,
        'msg' => "数据库错误: $errorCode - " . substr($errorMessage, 0, 100)
    ]);
} catch (Exception $e) {
    // 其他错误
    logDebug("处理失败", ['error' => $e->getMessage()]);
    echo json_encode([
        'error' => true,
        'msg' => '处理失败: ' . $e->getMessage()
    ]);
}
?>