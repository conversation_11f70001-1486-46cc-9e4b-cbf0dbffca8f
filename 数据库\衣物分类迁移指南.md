# 衣物分类数据迁移指南

## 概述

本指南用于将现有的硬编码衣物分类系统迁移到新的动态分类管理系统。迁移采用**渐进式、安全优先**的策略，确保线上数据的完整性。

## 迁移策略

### 1. 数据结构设计
- **保留兼容性**：保留原有的 `clothes.category` 字段作为备份
- **添加新字段**：增加 `clothes.category_id` 字段关联到 `clothing_categories` 表
- **双重支持**：API同时支持新旧分类系统查询

### 2. 迁移步骤

#### 步骤1：创建分类表（已完成）
```sql
-- 此步骤已完成，分类表已创建
SELECT COUNT(*) FROM clothing_categories WHERE is_system = 1;
-- 应该返回7个系统默认分类
```

#### 步骤2：修改clothes表结构
```bash
# 执行数据库结构修改
mysql -u [用户名] -p [数据库名] < 数据库/migrate_clothing_categories.sql
```

#### 步骤3：检查迁移状态
```bash
# 进入后端目录
cd login_backend

# 检查当前状态
php migrate_clothing_categories_data.php --check
```

#### 步骤4：试运行迁移（安全测试）
```bash
# 试运行模式 - 不会实际修改数据
php migrate_clothing_categories_data.php --user_id=0 --dry_run
```

#### 步骤5：分批次迁移
```bash
# 先迁移测试用户（如ID=1的体验账号）
php migrate_clothing_categories_data.php --user_id=1

# 再迁移指定用户
php migrate_clothing_categories_data.php --user_id=123

# 最后迁移所有用户
php migrate_clothing_categories_data.php --user_id=0
```

## 运行命令详解

### 检查命令
```bash
# 检查所有用户的迁移状态
php migrate_clothing_categories_data.php --check

# 检查指定用户的迁移状态  
php migrate_clothing_categories_data.php --check --user_id=123
```

### 迁移命令
```bash
# 试运行模式（推荐先执行）
php migrate_clothing_categories_data.php --dry_run --user_id=0

# 迁移所有用户
php migrate_clothing_categories_data.php --user_id=0

# 迁移指定用户
php migrate_clothing_categories_data.php --user_id=123

# 查看帮助
php migrate_clothing_categories_data.php --help
```

### 参数说明
- `--user_id=N`：指定用户ID（0=所有用户，其他数字=指定用户）
- `--dry_run`：试运行模式，不实际修改数据
- `--check`：检查迁移状态
- `--help`：显示帮助信息

## 对现有数据的影响分析

### ✅ 不会有影响的操作

1. **创建 `clothing_categories` 表**
   - 这是新增表，不影响现有数据

2. **添加 `clothes.category_id` 字段**
   - 新增字段，默认值为NULL，不影响现有记录

3. **前端代码修改**
   - API已做向后兼容处理，同时支持新旧分类查询

### ⚠️ 需要注意的影响

1. **数据库表结构变更**
   - 会临时锁定表进行结构修改（通常几秒钟）
   - 建议在低峰期执行

2. **API行为变化**
   - 前端已修改为动态获取分类，如果分类表为空可能影响显示
   - 通过预设系统分类避免此问题

## 回滚方案

如果迁移后发现问题，可以执行以下回滚：

```sql
-- 1. 临时禁用新分类系统（前端回滚到旧代码）
-- 2. 如果需要，可以删除新增字段
ALTER TABLE clothes DROP COLUMN category_id;

-- 3. 如果需要，可以删除分类表
DROP TABLE clothing_categories;
```

## 验证迁移结果

### 1. 数据完整性检查
```sql
-- 检查分类映射是否正确
SELECT 
    c.category as old_category,
    cc.code as new_category_code,
    cc.name as new_category_name,
    COUNT(*) as count
FROM clothes c
LEFT JOIN clothing_categories cc ON c.category_id = cc.id
GROUP BY c.category, cc.code, cc.name;
```

### 2. 功能测试
- 登录小程序，检查衣物分类显示是否正常
- 测试添加、编辑衣物时的分类选择
- 验证分类筛选功能

### 3. 性能测试
- 检查分类查询的响应时间
- 确认数据库索引是否正常工作

## 最佳实践建议

### 1. 执行时机
- 选择用户访问量较低的时间段
- 准备好回滚方案
- 通知相关团队成员

### 2. 分批执行
- 先在测试环境完整执行一遍
- 生产环境先迁移少量用户测试
- 确认无问题后再批量迁移

### 3. 监控要点
- 数据库连接数和查询性能
- 小程序API响应时间
- 用户反馈和错误日志

## 常见问题解决

### Q1: 迁移脚本执行失败
**A**: 检查数据库连接配置，确认用户权限，查看错误日志

### Q2: 前端显示分类异常
**A**: 确认 `clothing_categories` 表有系统默认分类，检查API返回数据

### Q3: 分类筛选不工作
**A**: 检查迁移是否完成，确认 `clothes.category_id` 字段已正确设置

### Q4: 性能下降
**A**: 检查索引是否创建，考虑优化查询SQL

## 支持联系

如遇到问题，请提供：
1. 错误信息截图
2. 执行的命令
3. 相关日志文件
4. 数据库版本和配置信息 