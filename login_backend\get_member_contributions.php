<?php
// 获取成员贡献统计API
// 模块2：圈子成员管理模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

// 获取查询参数
$targetUserId = isset($_GET['user_id']) ? intval($_GET['user_id']) : $userId;

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查找用户所在的圈子
    $findCircleSql = "SELECT cm.circle_id, cm.role, c.name as circle_name
                      FROM circle_members cm 
                      JOIN outfit_circles c ON cm.circle_id = c.id 
                      WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
    $findCircleStmt = $conn->prepare($findCircleSql);
    $findCircleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $findCircleStmt->execute();
    
    $userCircle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$userCircle) {
        echo json_encode([
            'status' => 'error',
            'message' => '您当前未加入任何圈子'
        ]);
        exit;
    }
    
    $circleId = $userCircle['circle_id'];
    
    // 验证目标用户是否在同一圈子中
    if ($targetUserId !== $userId) {
        $checkTargetSql = "SELECT user_id FROM circle_members 
                           WHERE circle_id = :circle_id AND user_id = :target_user_id AND status = 'active'";
        $checkTargetStmt = $conn->prepare($checkTargetSql);
        $checkTargetStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
        $checkTargetStmt->bindParam(':target_user_id', $targetUserId, PDO::PARAM_INT);
        $checkTargetStmt->execute();
        
        if ($checkTargetStmt->rowCount() === 0) {
            echo json_encode([
                'status' => 'error',
                'message' => '目标用户不在您的圈子中'
            ]);
            exit;
        }
    }
    
    // 获取目标用户信息
    $userInfoSql = "SELECT u.nickname, u.avatar_url, cm.role, cm.joined_at
                    FROM users u
                    JOIN circle_members cm ON u.id = cm.user_id
                    WHERE u.id = :target_user_id AND cm.circle_id = :circle_id AND cm.status = 'active'";
    $userInfoStmt = $conn->prepare($userInfoSql);
    $userInfoStmt->bindParam(':target_user_id', $targetUserId, PDO::PARAM_INT);
    $userInfoStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $userInfoStmt->execute();
    
    $userInfo = $userInfoStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$userInfo) {
        echo json_encode([
            'status' => 'error',
            'message' => '用户信息不存在'
        ]);
        exit;
    }
    
    // 获取用户的贡献统计
    $statsSql = "SELECT wardrobe_count, clothes_count, outfit_count, 
                        clothing_category_count, outfit_category_count, tag_count,
                        last_contribution_at, created_at, updated_at
                 FROM circle_member_stats 
                 WHERE circle_id = :circle_id AND user_id = :target_user_id";
    $statsStmt = $conn->prepare($statsSql);
    $statsStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $statsStmt->bindParam(':target_user_id', $targetUserId, PDO::PARAM_INT);
    $statsStmt->execute();
    
    $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);
    
    // 如果没有统计记录，创建默认统计
    if (!$stats) {
        $stats = [
            'wardrobe_count' => 0,
            'clothes_count' => 0,
            'outfit_count' => 0,
            'clothing_category_count' => 0,
            'outfit_category_count' => 0,
            'tag_count' => 0,
            'last_contribution_at' => null,
            'created_at' => null,
            'updated_at' => null
        ];
    }
    
    // 计算总贡献数
    $totalContributions = intval($stats['wardrobe_count']) + 
                         intval($stats['clothes_count']) + 
                         intval($stats['outfit_count']) + 
                         intval($stats['clothing_category_count']) + 
                         intval($stats['outfit_category_count']) + 
                         intval($stats['tag_count']);
    
    // 获取圈子中的排名
    $rankSql = "SELECT COUNT(*) + 1 as rank
                FROM circle_member_stats cms
                JOIN circle_members cm ON cms.circle_id = cm.circle_id AND cms.user_id = cm.user_id
                WHERE cms.circle_id = :circle_id 
                AND cm.status = 'active'
                AND (cms.wardrobe_count + cms.clothes_count + cms.outfit_count + 
                     cms.clothing_category_count + cms.outfit_category_count + cms.tag_count) > :total_contributions";
    $rankStmt = $conn->prepare($rankSql);
    $rankStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $rankStmt->bindParam(':total_contributions', $totalContributions, PDO::PARAM_INT);
    $rankStmt->execute();
    
    $rankResult = $rankStmt->fetch(PDO::FETCH_ASSOC);
    $rank = intval($rankResult['rank']);
    
    echo json_encode([
        'status' => 'success',
        'data' => [
            'user_info' => [
                'user_id' => $targetUserId,
                'nickname' => $userInfo['nickname'] ?? '未知用户',
                'avatar_url' => $userInfo['avatar_url'],
                'role' => $userInfo['role'],
                'joined_at' => $userInfo['joined_at'],
                'is_current_user' => $targetUserId == $userId
            ],
            'contributions' => [
                'wardrobe_count' => intval($stats['wardrobe_count']),
                'clothes_count' => intval($stats['clothes_count']),
                'outfit_count' => intval($stats['outfit_count']),
                'clothing_category_count' => intval($stats['clothing_category_count']),
                'outfit_category_count' => intval($stats['outfit_category_count']),
                'tag_count' => intval($stats['tag_count']),
                'total_contributions' => $totalContributions,
                'rank' => $rank,
                'last_contribution_at' => $stats['last_contribution_at']
            ],
            'circle_info' => [
                'circle_id' => $circleId,
                'circle_name' => $userCircle['circle_name']
            ]
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '获取贡献统计失败：' . $e->getMessage()
    ]);
}
?>
