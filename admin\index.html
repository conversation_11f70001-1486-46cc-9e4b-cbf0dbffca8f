<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>次元衣柜 - 管理后台</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="login-container">
        <div class="login-box">
            <div class="logo">
                <img src="https://cyymj.oss-cn-shanghai.aliyuncs.com/logo.png" alt="次元衣柜">
                <h1>次元衣柜管理后台</h1>
            </div>
            <div class="login-form">
                <div class="form-item">
                    <label for="username">用户名</label>
                    <input type="text" id="username" placeholder="请输入用户名" autocomplete="username">
                </div>
                <div class="form-item">
                    <label for="password">密码</label>
                    <input type="password" id="password" placeholder="请输入密码" autocomplete="current-password">
                </div>
                <div class="error-message" id="errorMessage"></div>
                <div class="form-item">
                    <button id="loginBtn">登录</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 如果已有有效token，直接跳转到仪表盘
            if (Auth.isLoggedIn()) {
                window.location.href = 'dashboard.html';
                return;
            }
            
            const loginBtn = document.getElementById('loginBtn');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');
            const errorMessage = document.getElementById('errorMessage');
            
            loginBtn.addEventListener('click', function() {
                const username = usernameInput.value.trim();
                const password = passwordInput.value.trim();
                
                // 清除之前的错误消息
                errorMessage.textContent = '';
                
                // 简单验证
                if (!username || !password) {
                    errorMessage.textContent = '请输入用户名和密码';
                    return;
                }
                
                // 禁用按钮，显示加载状态
                loginBtn.disabled = true;
                loginBtn.textContent = '登录中...';
                
                // 调用登录方法
                Auth.login(username, password)
                    .then(function(response) {
                        // 登录成功，跳转到仪表盘
                        window.location.href = 'dashboard.html';
                    })
                    .catch(function(error) {
                        // 显示错误信息
                        errorMessage.textContent = error.message || '登录失败，请检查用户名和密码';
                    })
                    .finally(function() {
                        // 恢复按钮状态
                        loginBtn.disabled = false;
                        loginBtn.textContent = '登录';
                    });
            });
            
            // 支持回车键登录
            passwordInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    loginBtn.click();
                }
            });
        });
    </script>
</body>
</html> 