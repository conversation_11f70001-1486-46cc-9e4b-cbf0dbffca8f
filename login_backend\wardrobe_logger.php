<?php
/**
 * 衣橱管理系统日志工具
 * 
 * 提供了一系列函数用于记录系统操作、请求、数据库活动和错误信息
 */

// 定义日志文件路径
define('WARDROBE_LOG_DIR', __DIR__ . '/../logs');
define('WARDROBE_LOG_FILE', WARDROBE_LOG_DIR . '/wardrobe_' . date('Y-m-d') . '.log');

/**
 * 测试日志系统是否正常工作
 * 
 * @return bool 测试是否成功
 */
function wardrobe_log_test() {
    try {
        error_log("Logger test: Starting test");
        
        // 测试目录是否存在，不存在则尝试创建
        if (!file_exists(WARDROBE_LOG_DIR)) {
            $dirCreated = mkdir(WARDROBE_LOG_DIR, 0755, true);
            error_log("Logger test: Directory creation " . ($dirCreated ? "successful" : "failed") . " for " . WARDROBE_LOG_DIR);
            
            if (!$dirCreated) {
                $error = error_get_last();
                error_log("Logger test: mkdir error - " . ($error ? $error['message'] : 'Unknown error'));
                
                // 尝试使用临时目录
                define('WARDROBE_LOG_DIR_FALLBACK', sys_get_temp_dir());
                error_log("Logger test: Trying fallback directory - " . WARDROBE_LOG_DIR_FALLBACK);
            }
        } else {
            error_log("Logger test: Directory exists - " . WARDROBE_LOG_DIR);
            
            // 测试目录是否可写
            if (!is_writable(WARDROBE_LOG_DIR)) {
                error_log("Logger test: Directory not writable - " . WARDROBE_LOG_DIR);
                @chmod(WARDROBE_LOG_DIR, 0755);
                if (!is_writable(WARDROBE_LOG_DIR)) {
                    error_log("Logger test: chmod failed, still not writable");
                }
            } else {
                error_log("Logger test: Directory is writable");
            }
        }
        
        // 使用正确的日志目录（原始或回退的临时目录）
        $logDir = defined('WARDROBE_LOG_DIR_FALLBACK') ? WARDROBE_LOG_DIR_FALLBACK : WARDROBE_LOG_DIR;
        
        // 测试文件写入
        $testFile = $logDir . '/wardrobe_test.log';
        $writeResult = file_put_contents($testFile, "Test entry at " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
        error_log("Logger test: Write test " . ($writeResult !== false ? "successful" : "failed") . " to " . $testFile);
        
        if ($writeResult === false) {
            $error = error_get_last();
            error_log("Logger test: file_put_contents error - " . ($error ? $error['message'] : 'Unknown error'));
            return false;
        }
        
        return true;
    } catch (Exception $e) {
        error_log("Logger test: Exception - " . $e->getMessage());
        return false;
    }
}

// 确保日志目录存在
if (!file_exists(WARDROBE_LOG_DIR)) {
    try {
        $dirCreated = mkdir(WARDROBE_LOG_DIR, 0755, true);
        if (!$dirCreated) {
            $error = error_get_last();
            error_log("Logger init: Failed to create directory " . WARDROBE_LOG_DIR . " - " . 
                     ($error ? $error['message'] : 'Unknown error'));
            
            // 定义回退使用的临时目录
            define('WARDROBE_LOG_DIR_FALLBACK', sys_get_temp_dir());
        }
    } catch (Exception $e) {
        error_log("Logger init: Exception creating directory - " . $e->getMessage());
        // 定义回退使用的临时目录
        define('WARDROBE_LOG_DIR_FALLBACK', sys_get_temp_dir());
    }
}

/**
 * 记录普通信息
 *
 * @param string $message 日志消息
 * @return void
 */
function wardrobe_log_info($message) {
    _wardrobe_log('INFO', $message);
}

/**
 * 记录警告信息
 *
 * @param string $message 警告消息
 * @return void
 */
function wardrobe_log_warning($message) {
    _wardrobe_log('WARNING', $message);
}

/**
 * 记录错误信息
 *
 * @param string $message 错误消息
 * @return void
 */
function wardrobe_log_error($message) {
    _wardrobe_log('ERROR', $message);
}

/**
 * 记录API请求
 *
 * @param string $endpoint 接口名称
 * @return void
 */
function wardrobe_log_request($endpoint) {
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    $method = $_SERVER['REQUEST_METHOD'] ?? 'unknown';
    
    $message = "Endpoint: {$endpoint}, Method: {$method}, IP: {$ip}, UA: {$userAgent}";
    _wardrobe_log('REQUEST', $message);
}

/**
 * 记录数据库操作
 *
 * @param string $operation 操作类型 (select, insert, update, delete)
 * @param string $sql SQL语句
 * @param array $params 参数数组
 * @return void
 */
function wardrobe_log_db($operation, $sql, $params = []) {
    $operation = strtoupper($operation);
    $paramStr = json_encode($params, JSON_UNESCAPED_UNICODE);
    $message = "Operation: {$operation}, SQL: {$sql}, Params: {$paramStr}";
    _wardrobe_log('DATABASE', $message);
}

/**
 * 记录异常信息
 *
 * @param Exception $exception 异常对象
 * @param string $context 上下文信息
 * @return void
 */
function wardrobe_log_exception($exception, $context = '') {
    $message = "Context: {$context}, Message: {$exception->getMessage()}, " .
               "Code: {$exception->getCode()}, File: {$exception->getFile()}, " .
               "Line: {$exception->getLine()}, Trace: " . $exception->getTraceAsString();
    
    _wardrobe_log('EXCEPTION', $message);
    
    // 也记录到系统日志
    error_log("WARDROBE EXCEPTION in {$context}: {$exception->getMessage()} at {$exception->getFile()}:{$exception->getLine()}");
}

/**
 * 记录用户活动
 *
 * @param int $userId 用户ID
 * @param string $action 活动类型
 * @param string $details 活动详情
 * @return void
 */
function wardrobe_log_user_activity($userId, $action, $details = '') {
    $message = "UserID: {$userId}, Action: {$action}, Details: {$details}";
    _wardrobe_log('USER_ACTIVITY', $message);
}

/**
 * 内部日志记录函数
 *
 * @param string $level 日志级别
 * @param string $message 日志消息
 * @return void
 */
function _wardrobe_log($level, $message) {
    try {
        $timestamp = date('Y-m-d H:i:s');
        $formattedMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
        
        // 确定使用哪个日志目录
        $logDir = defined('WARDROBE_LOG_DIR_FALLBACK') ? WARDROBE_LOG_DIR_FALLBACK : WARDROBE_LOG_DIR;
        $logFile = $logDir . '/wardrobe_' . date('Y-m-d') . '.log';
        
        // 确保目录存在
        if (!file_exists($logDir)) {
            $result = @mkdir($logDir, 0755, true);
            if (!$result) {
                $error = error_get_last();
                error_log("Logger: Directory creation failed for {$logDir} - " . ($error ? $error['message'] : 'Unknown error'));
                
                // 尝试使用PHP的临时目录
                $logDir = sys_get_temp_dir();
                $logFile = $logDir . '/wardrobe_' . date('Y-m-d') . '.log';
                error_log("Logger: Using PHP temp dir instead - {$logDir}");
            }
        }
        
        // 尝试写入日志
        $result = @file_put_contents($logFile, $formattedMessage, FILE_APPEND);
        
        // 检查写入是否成功
        if ($result === false) {
            $error = error_get_last();
            error_log("Logger: Write failed to {$logFile} - " . ($error ? $error['message'] : 'Unknown error'));
        }
        
        // 将关键级别日志也写入PHP错误日志
        if ($level === 'ERROR' || $level === 'EXCEPTION' || $level === 'WARNING') {
            error_log("WARDROBE {$level}: {$message}");
        }
    } catch (Exception $e) {
        // 确保异常不会传播，但会记录
        error_log("Logger Exception: " . $e->getMessage());
    }
} 