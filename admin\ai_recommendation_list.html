<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI推荐 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        .search-box {
            display: flex;
            margin-bottom: 20px;
        }
        
        .search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px 0 0 4px;
            outline: none;
        }
        
        .search-btn {
            padding: 8px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
        }
        
        .search-btn:hover {
            background-color: #40a9ff;
        }
        
        .recommendation-tabs {
            display: flex;
            border-bottom: 1px solid #e8e8e8;
            margin-bottom: 20px;
        }
        
        .recommendation-tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            margin-right: 20px;
        }
        
        .recommendation-tab.active {
            border-bottom: 2px solid #1890ff;
            color: #1890ff;
            font-weight: 500;
        }
        
        .recommendation-content {
            display: none;
        }
        
        .recommendation-content.active {
            display: block;
        }
        
        .recommendation-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .recommendation-table th,
        .recommendation-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .recommendation-table th {
            background-color: #fafafa;
            font-weight: 500;
        }
        
        .recommendation-table tr:hover {
            background-color: #f5f5f5;
        }
        
        .thumbnail {
            width: 60px;
            height: 60px;
            border-radius: 4px;
            object-fit: cover;
            background-color: #f5f5f5;
        }
        
        .clothing-thumbnail {
            width: 50px;
            height: 50px;
            border-radius: 4px;
            object-fit: cover;
            background-color: #f5f5f5;
            border: 1px solid #e8e8e8;
        }
        
        .recommendation-reason-short {
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #666;
            font-size: 0.9em;
        }
        
        .recommendation-reason-tooltip {
            position: relative;
            display: inline-block;
        }
        
        .recommendation-reason-tooltip:hover .recommendation-reason-full {
            display: block;
        }
        
        .recommendation-reason-full {
            display: none;
            position: absolute;
            bottom: 100%;
            left: 0;
            background-color: #fff;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 10px;
            width: 300px;
            max-height: 200px;
            overflow-y: auto;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            z-index: 100;
            white-space: normal;
        }
        
        .action-btn {
            padding: 4px 10px;
            border-radius: 4px;
            border: 1px solid;
            background-color: transparent;
            cursor: pointer;
            margin-right: 5px;
            font-size: 13px;
        }
        
        .view-btn {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .view-btn:hover {
            background-color: #e6f7ff;
        }
        
        .pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        .pagination button {
            padding: 5px 10px;
            margin: 0 5px;
            border: 1px solid #d9d9d9;
            background-color: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .pagination button:hover:not(:disabled) {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .pagination button:disabled {
            color: #d9d9d9;
            cursor: not-allowed;
        }
        
        .pagination .current-page {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .no-data {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        
        .loading-indicator {
            text-align: center;
            padding: 20px;
            color: #666;
            display: none;
        }
        
        .error-message {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #f5222d;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }
        
        /* 推荐项目详情样式 */
        .recommendation-item {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 15px;
        }
        
        .recommendation-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .recommendation-header .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
            object-fit: cover;
        }
        
        .recommendation-header .user-info {
            flex: 1;
        }
        
        .recommendation-header .user-info .user-name {
            font-weight: 500;
            color: #333;
        }
        
        .recommendation-header .user-info .timestamp {
            font-size: 0.8rem;
            color: #999;
        }
        
        .recommendation-clothes {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        @media (max-width: 992px) {
            .recommendation-clothes {
                /* 保持纵向布局 */
            }
        }
        
        @media (max-width: 576px) {
            .recommendation-clothes {
                /* 保持纵向布局 */
            }
        }
        
        .clothing-item {
            text-align: center;
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        
        .clothing-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        .clothing-item .clothing-category {
            font-weight: bold;
            margin-top: 8px;
            color: #333;
        }
        
        .clothing-reason {
            margin-top: 8px;
            text-align: left;
            font-size: 0.9em;
            color: #666;
            background: #f9f9f9;
            padding: 8px;
            border-radius: 4px;
            max-height: 100px;
            overflow-y: auto;
        }
        
        .recommendation-reason {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .recommendation-summary {
            font-style: italic;
            color: #666;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
            line-height: 1.5;
            margin-bottom: 10px;
        }
        
        .recommendation-reason h4, .recommendation-summary h4 {
            margin-top: 0;
            margin-bottom: 8px;
            color: #333;
            font-size: 16px;
        }
        
        /* 详情模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            overflow-y: auto;
        }
        
        .modal-content {
            background-color: #fff;
            margin: 50px auto;
            width: 80%;
            max-width: 800px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            position: relative;
        }
        
        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 500;
            margin: 0;
        }
        
        .modal-close {
            font-size: 22px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
            background: none;
            border: none;
            padding: 0;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #f0f0f0;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>AI推荐管理</h2>
                <div class="user-info">
                    <span id="userName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div id="recommendationError" class="error-message"></div>
            <div id="recommendationLoading" class="loading-indicator">正在加载推荐数据...</div>
            
            <div class="card">
                <div class="search-box">
                    <input type="text" id="searchInput" class="search-input" placeholder="搜索用户ID或昵称..." />
                    <button id="searchBtn" class="search-btn">搜索</button>
                </div>
                
                <div class="recommendation-tabs">
                    <div class="recommendation-tab active" data-tab="weatherBased">基于天气</div>
                    <div class="recommendation-tab" data-tab="clothingBased">基于衣物</div>
                    <div class="recommendation-tab" data-tab="preferenceBased">基于喜好</div>
                    <div class="recommendation-tab" data-tab="imageAnalysisBased">基于形象分析</div>
                </div>
                
                <!-- 基于天气的推荐 -->
                <div class="recommendation-content active" id="weatherBased">
                    <table class="recommendation-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户昵称</th>
                                <th>用户ID</th>
                                <th>天气</th>
                                <th>推荐衣物</th>
                                <th>推荐理由</th>
                                <th>推荐时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="weatherBasedTable">
                            <tr>
                                <td colspan="8" class="no-data">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="pagination" id="weatherPagination">
                        <button id="weatherPrevBtn" disabled>&lt; 上一页</button>
                        <span id="weatherPageInfo">第 0/0 页</span>
                        <button id="weatherNextBtn" disabled>下一页 &gt;</button>
                    </div>
                </div>
                
                <!-- 基于衣物的推荐 -->
                <div class="recommendation-content" id="clothingBased">
                    <table class="recommendation-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户昵称</th>
                                <th>用户ID</th>
                                <th>基准衣物</th>
                                <th>推荐衣物</th>
                                <th>推荐理由</th>
                                <th>推荐时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="clothingBasedTable">
                            <tr>
                                <td colspan="8" class="no-data">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="pagination" id="clothingPagination">
                        <button id="clothingPrevBtn" disabled>&lt; 上一页</button>
                        <span id="clothingPageInfo">第 0/0 页</span>
                        <button id="clothingNextBtn" disabled>下一页 &gt;</button>
                    </div>
                </div>
                
                <!-- 基于喜好的推荐 -->
                <div class="recommendation-content" id="preferenceBased">
                    <table class="recommendation-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户昵称</th>
                                <th>用户ID</th>
                                <th>用户喜好</th>
                                <th>推荐衣物</th>
                                <th>推荐理由</th>
                                <th>推荐时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="preferenceBasedTable">
                            <tr>
                                <td colspan="8" class="no-data">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="pagination" id="preferencePagination">
                        <button id="preferencePrevBtn" disabled>&lt; 上一页</button>
                        <span id="preferencePageInfo">第 0/0 页</span>
                        <button id="preferenceNextBtn" disabled>下一页 &gt;</button>
                    </div>
                </div>
                
                <!-- 基于形象分析的推荐 -->
                <div class="recommendation-content" id="imageAnalysisBased">
                    <table class="recommendation-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户昵称</th>
                                <th>用户ID</th>
                                <th>分析ID</th>
                                <th>推荐衣物</th>
                                <th>推荐理由</th>
                                <th>推荐时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="imageAnalysisBasedTable">
                            <tr>
                                <td colspan="8" class="no-data">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="pagination" id="imageAnalysisPagination">
                        <button id="imageAnalysisPrevBtn" disabled>&lt; 上一页</button>
                        <span id="imageAnalysisPageInfo">第 0/0 页</span>
                        <button id="imageAnalysisNextBtn" disabled>下一页 &gt;</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 推荐详情模态框 -->
    <div id="recommendationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">推荐详情</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="recommendationModalBody">
                <!-- 推荐详情内容将动态插入这里 -->
            </div>
            <div class="modal-footer">
                <button class="action-btn view-btn modal-close">关闭</button>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/image_viewer.js"></script>
    <script src="js/ai_recommendation_list.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为ai_recommendation
            Sidebar.init('ai_recommendation');
            
            // 显示用户信息
            const userName = document.getElementById('userName');
            const user = Auth.getCurrentUser();
            if (user) {
                userName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化AI推荐模块
            AIRecommendation.init();
        });
    </script>
</body>
</html> 