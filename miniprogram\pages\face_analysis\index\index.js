// pages/face_analysis/index/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    
  },

  /**
   * 开始分析 - 导航到支付页面
   */
  startAnalysis() {
    // 获取app实例
    const app = getApp();

    // 检查是否登录 - 复用形象分析的检测逻辑
    if (!wx.getStorageSync('token') || app.globalData.useMockUser || !app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录以使用面容分析服务',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 使用reLaunch避免导航超时问题
            wx.reLaunch({
              url: '/pages/login/login',
              fail: (err) => {
                console.error('跳转到登录页面失败:', err);
                // 如果reLaunch失败，尝试用switchTab
                wx.switchTab({
                  url: '/pages/my/my',
                  fail: (switchErr) => {
                    console.error('跳转到个人页面也失败:', switchErr);
                    // 显示错误提示
                    wx.showToast({
                      title: '跳转失败，请手动前往"我的"页面登录',
                      icon: 'none',
                      duration: 2000
                    });
                  }
                });
              }
            });
          }
        }
      });
      return;
    }

    // 已登录，暂时直接创建分析记录（强制走支付流程）
    // TODO: 数据库稳定后可以启用次数检查
    this.createAnalysisRecord();

    // 如果要启用次数检查，取消注释下面这行，注释上面这行
    // this.checkAvailableAnalysis();
  },

  /**
   * 检查是否有可用的分析次数
   */
  checkAvailableAnalysis() {
    wx.showLoading({
      title: '检查中...',
    });

    const token = wx.getStorageSync('token');
    const app = getApp();

    wx.request({
      url: app.globalData.baseUrl + '/check_face_analysis_quota.php',
      method: 'POST',
      data: {},
      header: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      success: (res) => {
        wx.hideLoading();
        console.log('检查分析次数响应:', res.data);

        if (!res.data.error) {
          if (res.data.data.has_quota) {
            // 有可用次数，直接跳转到上传页面
            const analysisId = res.data.data.analysis_id;
            wx.navigateTo({
              url: `/pages/face_analysis/upload/upload?analysisId=${analysisId}`
            });
          } else {
            // 没有可用次数，创建新的分析记录（需要支付）
            this.createAnalysisRecord();
          }
        } else {
          wx.showToast({
            title: res.data.msg || '检查失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('检查分析次数失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 创建分析记录
   */
  createAnalysisRecord() {
    wx.showLoading({
      title: '准备中...',
    });

    const token = wx.getStorageSync('token');
    const app = getApp();

    wx.request({
      url: app.globalData.baseUrl + '/create_face_analysis_order.php',
      method: 'POST',
      data: {},
      header: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      success: (res) => {
        wx.hideLoading();
        console.log('创建分析记录响应:', res.data);

        if (!res.data.error) {
          // 创建成功，跳转到支付页面
          const analysisId = res.data.data.analysis_id;
          const orderId = res.data.data.order_id;
          const payParams = res.data.data.pay_params;

          // 构建跳转URL，包含支付参数
          let url = `/pages/face_analysis/payment/payment?analysisId=${analysisId}&orderId=${orderId}`;
          if (payParams) {
            url += `&payParams=${encodeURIComponent(JSON.stringify(payParams))}`;
          }

          wx.navigateTo({
            url: url
          });
        } else {
          wx.showToast({
            title: res.data.msg || '创建分析记录失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('创建分析记录失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 查看历史分析
   */
  viewHistory() {
    wx.navigateTo({
      url: '/pages/face_analysis/history/history',
    })
  }
}) 