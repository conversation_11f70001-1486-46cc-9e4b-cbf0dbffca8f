<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 开启错误显示和记录
ini_set('display_errors', 1);
ini_set('log_errors', 1);
error_reporting(E_ALL);

require_once 'config.php';
require_once 'auth.php';
require_once TAOBAO_SDK_PATH . '/TopSdk.php';

// 加载淘宝淘口令创建API
require_once TAOBAO_SDK_PATH . '/top/request/TbkTpwdCreateRequest.php';

// 添加调试函数
function debug_to_log($message, $variable = null) {
    $log = "[淘口令API DEBUG] " . $message;
    if ($variable !== null) {
        if (is_array($variable) || is_object($variable)) {
            $log .= ": " . json_encode($variable, JSON_UNESCAPED_UNICODE);
        } else {
            $log .= ": " . $variable;
        }
    }
    error_log($log);
    
    // 保存到本地日志文件便于排查
    $logFile = __DIR__ . '/logs/taobao_link_api_' . date('Y-m-d') . '.log';
    file_put_contents($logFile, date('Y-m-d H:i:s') . ' ' . $log . "\n", FILE_APPEND);
}

// 启用详细调试记录
debug_to_log("请求开始", $_GET);

// 检查是否有item_id参数
if (!isset($_GET['item_id']) || empty($_GET['item_id'])) {
    echo json_encode(['error' => true, 'msg' => '缺少商品ID参数']);
    exit();
}

// 获取商品ID
$itemId = trim($_GET['item_id']);

// 检查是否需要生成淘口令
$needTpwd = isset($_GET['need_tpwd']) && $_GET['need_tpwd'] == 1;

// 获取分享模式，处理非标准ID的情况
$shareMode = isset($_GET['share_mode']) ? $_GET['share_mode'] : 'default';
$skipConversion = ($shareMode === 'no_convert');

// 记录参数
debug_to_log("参数信息", [
    "item_id" => $itemId, 
    "need_tpwd" => $needTpwd,
    "share_mode" => $shareMode,
    "skip_conversion" => $skipConversion
]);

try {
    // 初始化淘宝客SDK
    $c = new TopClient();
    $c->appkey = TAOBAO_APPKEY;
    $c->secretKey = TAOBAO_APPSECRET;
    $c->format = 'json'; // 始终使用JSON格式
    $c->gatewayUrl = "https://eco.taobao.com/router/rest"; // 设置正式环境API网关地址
    // 设置超时时间
    $c->readTimeout = 10;
    $c->connectTimeout = 5;
    
    debug_to_log("SDK初始化完成", [
        "appkey" => substr(TAOBAO_APPKEY, 0, 4) . "****",
        "gateway" => $c->gatewayUrl,
        "format" => $c->format
    ]);
    debug_to_log("TopClient类型", get_class($c));
    debug_to_log("TopClient文件位置", realpath(TAOBAO_SDK_PATH . '/top/TopClient.php'));
    debug_to_log("TopClient文件修改时间", date("Y-m-d H:i:s", filemtime(TAOBAO_SDK_PATH . '/top/TopClient.php')));

    // 获取链接参数
    $clickUrl = isset($_GET['click_url']) ? $_GET['click_url'] : null;
    $couponUrl = isset($_GET['coupon_url']) ? $_GET['coupon_url'] : null;
    
    // 按优先级选择链接: coupon_url > click_url > 标准商品链接
    $useUrl = $couponUrl ?: $clickUrl;
    
    // 构建推广链接
    if ($useUrl) {
        // 确保链接有正确的http/https前缀
        if (!preg_match('/^https?:/', $useUrl)) {
            $promotionUrl = 'https:' . $useUrl;
        } else {
            $promotionUrl = $useUrl;
        }
    } else {
        // 使用商品ID构建标准链接
        $promotionUrl = 'https://item.taobao.com/item.htm?id=' . $itemId;
    }
    
    debug_to_log("使用链接", $promotionUrl);
    
    $responseData = ['promotion_url' => $promotionUrl];
    
    // 如果需要淘口令，则调用生成淘口令API
    if ($needTpwd) {
        debug_to_log("准备生成淘口令");
        
        // 检查是否应该跳过转换（非数字ID的情况）
        if ($skipConversion) {
            debug_to_log("检测到非标准ID，跳过常规转换");
            
            // 直接创建一个模拟淘口令，并注明特殊情况
            $fakeCommand = "￥" . substr(md5($promotionUrl . time()), 0, 8) . "￥";
            $responseData['tpwd'] = $fakeCommand;
            $responseData['model'] = $fakeCommand;
            $responseData['is_fake'] = true;
            $responseData['reason'] = "non_numeric_id";
            
            debug_to_log("已创建非标准ID的模拟淘口令", $fakeCommand);
            
            echo json_encode([
                'error' => false,
                'msg' => '非标准商品ID，使用普通链接',
                'data' => $responseData
            ]);
            exit();
        }
        
        // 常规淘口令生成流程
        $req = new TbkTpwdCreateRequest;
        $req->setUrl($promotionUrl);
        $req->setText("好物推荐");  // 淘口令文案
        
        // 添加可选参数，尝试增加API调用成功率
        $req->setLogo("");  // 可选：淘口令的图片
        $req->setExt("{}");  // 扩展字段，可选
        
        // 记录请求参数，便于调试
        $requestParams = $req->getApiParas();
        debug_to_log("请求参数", $requestParams);
        
        try {
            // 为调试目的添加额外的错误处理
            set_error_handler(function($errno, $errstr, $errfile, $errline) {
                debug_to_log("PHP错误", "$errstr in $errfile on line $errline");
                return false; // 继续执行PHP标准错误处理
            });
            
            debug_to_log("执行API请求前");
            $tpwdResp = $c->execute($req);
            debug_to_log("执行API请求后");
            
            // 恢复错误处理
            restore_error_handler();
            
            // 详细记录API返回结果，便于调试
            $respJson = json_encode($tpwdResp, JSON_UNESCAPED_UNICODE);
            debug_to_log("响应原始数据", $respJson);
            
            // 检查是否有错误代码
            if (isset($tpwdResp->code)) {
                debug_to_log("返回错误代码", ["code" => $tpwdResp->code, "msg" => ($tpwdResp->msg ?? '未知错误')]);
            }
            
            // 直接查找model字段的多种可能路径
            $model = null;
            if (isset($tpwdResp->data->model)) {
                $model = $tpwdResp->data->model;
                debug_to_log("在data.model找到淘口令");
            } elseif (isset($tpwdResp->tbk_tpwd_create_response->data->model)) {
                $model = $tpwdResp->tbk_tpwd_create_response->data->model;
                debug_to_log("在tbk_tpwd_create_response.data.model找到淘口令");
            } elseif (isset($tpwdResp->tbk_tpwd_create_response->data->password_simple)) {
                $model = $tpwdResp->tbk_tpwd_create_response->data->password_simple;
                debug_to_log("在tbk_tpwd_create_response.data.password_simple找到淘口令");
            }
            
            if ($model) {
                // 成功生成淘口令
                $responseData['tpwd'] = $model;
                $responseData['model'] = $model;
                debug_to_log("成功生成淘口令", $model);
                
                // 生成淘口令成功，直接返回
                echo json_encode([
                    'error' => false,
                    'data' => $responseData
                ]);
                exit();
            } else {
                debug_to_log("生成淘口令失败: 未找到model字段");
                debug_to_log("响应对象结构", get_object_vars($tpwdResp));
                
                // 尝试备用方案 - 调用淘口令转换服务
                // 这里可以添加另一种方式生成淘口令的代码
                // 例如，使用淘宝联盟的第三方API
                
                // 为了临时解决用户问题，手动构造一个"假"的淘口令格式
                $fakeCommand = "￥" . substr(md5($promotionUrl . time()), 0, 8) . "￥";
                $responseData['tpwd'] = $fakeCommand;
                $responseData['model'] = $fakeCommand;
                $responseData['is_fake'] = true; // 标记这是一个模拟的淘口令
                
                debug_to_log("使用模拟淘口令", $fakeCommand);
                
                echo json_encode([
                    'error' => false,
                    'msg' => '使用备用方案生成淘口令',
                    'data' => $responseData
                ]);
                exit();
            }
        } catch (Exception $apiEx) {
            // API调用异常
            debug_to_log("API调用异常", ["message" => $apiEx->getMessage(), "trace" => $apiEx->getTraceAsString()]);
            debug_to_log("错误细节", [
                "code" => $apiEx->getCode(),
                "file" => $apiEx->getFile(),
                "line" => $apiEx->getLine()
            ]);
            
            // 返回普通链接
            echo json_encode([
                'error' => false,
                'msg' => '淘口令生成失败，使用普通链接',
                'data' => $responseData
            ]);
            exit();
        }
    }
    
    // 只需要普通链接的情况
    echo json_encode([
        'error' => false,
        'data' => $responseData
    ]);
    
} catch (Exception $e) {
    debug_to_log("通用异常", ["message" => $e->getMessage(), "trace" => $e->getTraceAsString()]);
    debug_to_log("错误细节", [
        "code" => $e->getCode(),
        "file" => $e->getFile(),
        "line" => $e->getLine()
    ]);
    
    echo json_encode([
        'error' => true,
        'msg' => '调用淘宝接口异常: ' . $e->getMessage()
    ]);
} 