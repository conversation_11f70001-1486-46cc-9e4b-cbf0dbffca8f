<?php
/**
 * 测试穿搭编辑页面的数据源功能
 * 验证衣物选择器是否能正确加载不同数据源的衣物
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    echo "=== 穿搭编辑页面数据源功能测试 ===\n";
    echo "当前用户ID: $userId\n\n";
    
    // 测试不同数据源的衣物API调用
    $dataSources = ['personal', 'shared', 'all'];
    
    foreach ($dataSources as $dataSource) {
        echo "=== 测试数据源: $dataSource ===\n";
        
        // 构建API URL
        $apiUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/get_clothes.php";
        
        $params = [];
        if ($dataSource !== 'personal') {
            $params['include_circle_data'] = 'true';
            $params['data_source'] = $dataSource;
        }
        
        if (!empty($params)) {
            $queryString = http_build_query($params);
            $apiUrl .= "?" . $queryString;
        }
        
        echo "API URL: $apiUrl\n";
        
        // 准备请求头
        $headers = [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json'
        ];
        
        // 发起API请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "HTTP状态码: $httpCode\n";
        
        if ($error) {
            echo "CURL错误: $error\n\n";
            continue;
        }
        
        if ($httpCode !== 200) {
            echo "HTTP错误，响应内容:\n$response\n\n";
            continue;
        }
        
        // 解析响应
        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "JSON解析错误: " . json_last_error_msg() . "\n";
            echo "原始响应:\n$response\n\n";
            continue;
        }
        
        echo "API响应解析成功\n";
        echo "错误状态: " . ($data['error'] ? 'true' : 'false') . "\n";
        
        if (isset($data['data'])) {
            $clothes = $data['data'];
            echo "衣物数量: " . count($clothes) . "\n";
            
            $personalCount = 0;
            $sharedCount = 0;
            
            foreach ($clothes as $item) {
                if (isset($item['data_source']) && $item['data_source'] === 'shared') {
                    $sharedCount++;
                } else {
                    $personalCount++;
                }
            }
            
            echo "统计: 个人衣物 $personalCount 件，共享衣物 $sharedCount 件\n";
            
            // 显示前5件衣物的详细信息
            echo "前5件衣物详情:\n";
            for ($i = 0; $i < min(5, count($clothes)); $i++) {
                $item = $clothes[$i];
                $dataSourceLabel = isset($item['data_source']) && $item['data_source'] === 'shared' ? '[共享]' : '[个人]';
                $creator = isset($item['creator_nickname']) ? " (创建者: {$item['creator_nickname']})" : '';
                echo "- $dataSourceLabel {$item['name']} (ID: {$item['id']})$creator\n";
            }
            
        } else {
            echo "响应中没有data字段\n";
        }
        
        echo "\n";
    }
    
    // 测试特定分类的衣物
    echo "=== 测试特定分类衣物 (shared数据源, tops分类) ===\n";
    
    $apiUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/get_clothes.php";
    $params = [
        'include_circle_data' => 'true',
        'data_source' => 'shared',
        'category' => 'tops'
    ];
    
    $queryString = http_build_query($params);
    $apiUrl .= "?" . $queryString;
    
    echo "API URL: $apiUrl\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        if (isset($data['data'])) {
            echo "上装分类衣物数量: " . count($data['data']) . "\n";
        }
    }
    
    echo "\n=== 测试完成 ===\n";
    echo "\n💡 前端使用建议:\n";
    echo "1. 在穿搭编辑页面打开衣物选择器\n";
    echo "2. 点击数据源切换按钮（表情图标）\n";
    echo "3. 选择不同的数据源：个人数据、共享数据、全部数据\n";
    echo "4. 验证衣物列表是否正确更新\n";
    echo "5. 检查共享衣物是否显示绿色'共享'标签和创建者信息\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
?>
