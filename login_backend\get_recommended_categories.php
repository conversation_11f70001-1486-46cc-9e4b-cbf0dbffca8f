<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入配置和辅助函数
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

// 验证用户权限
$headers = getallheaders();
if (!isset($headers['Authorization'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

$token = $headers['Authorization'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}
$auth = new Auth();
$payload = $auth->verifyToken($token);
if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}
$userId = $payload['sub'];

try {
    // 获取数据库连接
    $database = new Database();
    $db = $database->getConnection();
    
    // 获取分页参数
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $per_page = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 20;
    $offset = ($page - 1) * $per_page;
    
    // 只获取状态为启用的分类
    $sql = "SELECT id, name, description, sort_order, status 
            FROM recommended_outfit_categories 
            WHERE status = 1 
            ORDER BY sort_order ASC, name ASC";
    
    // 执行查询
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 返回成功响应
    http_response_code(200);
    echo json_encode([
        'error' => false,
        'data' => $categories
    ]);
    
} catch (PDOException $e) {
    error_log("Database error in get_recommended_categories.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '数据库错误']);
} catch (Exception $e) {
    error_log("General error in get_recommended_categories.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '服务器错误']);
} 