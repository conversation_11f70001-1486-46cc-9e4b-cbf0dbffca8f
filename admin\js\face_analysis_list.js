/**
 * 面部分析历史管理模块
 */
const FaceAnalysis = {
    // 基础数据
    apiBaseUrl: '../login_backend',
    currentPage: 1,
    pageSize: 10,
    totalPages: 0,
    analyses: [],
    
    // 筛选条件
    filters: {
        userId: null,
        status: ''
    },
    
    // DOM元素引用
    elements: {},
    
    /**
     * 初始化模块
     */
    init: function() {
        try {
            console.log('初始化面部分析列表模块');
            
            // 获取DOM元素引用
            this.elements = {
                searchInput: document.getElementById('searchInput'),
                searchBtn: document.getElementById('searchBtn'),
                statusFilter: document.getElementById('statusFilter'),
                analysisTable: document.getElementById('analysisTable'),
                prevBtn: document.getElementById('prevBtn'),
                nextBtn: document.getElementById('nextBtn'),
                pageInfo: document.getElementById('pageInfo'),
                loading: document.getElementById('analysisLoading'),
                error: document.getElementById('analysisError'),
                modal: document.getElementById('analysisModal'),
                modalBody: document.getElementById('analysisModalBody')
            };
            
            // 检查关键元素是否存在
            if (!this.elements.searchInput) console.error('未找到搜索输入框元素 (searchInput)');
            if (!this.elements.searchBtn) console.error('未找到搜索按钮元素 (searchBtn)');
            if (!this.elements.statusFilter) console.error('未找到状态筛选器元素 (statusFilter)');
            if (!this.elements.analysisTable) console.error('未找到分析表格元素 (analysisTable)');
            
            // 绑定事件
            this.bindEvents();
            
            // 加载初始数据
            this.loadAnalyses();
        } catch (error) {
            console.error('初始化面部分析列表模块时发生错误:', error);
            if (this.elements.error) {
                this.elements.error.textContent = `初始化失败: ${error.message}`;
                this.elements.error.style.display = 'block';
            }
        }
    },
    
    /**
     * 绑定事件处理
     */
    bindEvents: function() {
        try {
            console.log('绑定面部分析列表事件');
            
            // 搜索按钮点击事件
            if (this.elements.searchBtn && this.elements.searchInput) {
                this.elements.searchBtn.addEventListener('click', () => {
                    try {
                        const inputValue = this.elements.searchInput.value.trim();
                        console.log('搜索用户ID:', inputValue);
                        
                        // 验证输入是否为有效数字
                        if (inputValue && !/^\d+$/.test(inputValue)) {
                            throw new Error('用户ID必须是数字');
                        }
                        
                        this.filters.userId = inputValue ? parseInt(inputValue) : null;
                        this.currentPage = 1; // 重置页码
                        this.loadAnalyses();
                    } catch (error) {
                        console.error('搜索处理错误:', error);
                        this.showError(`搜索错误: ${error.message}`);
                    }
                });
                
                // 回车键搜索
                this.elements.searchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.elements.searchBtn.click();
                    }
                });
            }
            
            // 状态筛选变化事件
            if (this.elements.statusFilter) {
                this.elements.statusFilter.addEventListener('change', () => {
                    try {
                        const selectedStatus = this.elements.statusFilter.value;
                        console.log('筛选状态:', selectedStatus);
                        
                        this.filters.status = selectedStatus;
                        this.currentPage = 1; // 重置页码
                        this.loadAnalyses();
                    } catch (error) {
                        console.error('状态筛选处理错误:', error);
                        this.showError(`筛选错误: ${error.message}`);
                    }
                });
            }
            
            // 上一页按钮点击事件
            if (this.elements.prevBtn) {
                this.elements.prevBtn.addEventListener('click', () => {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                        this.loadAnalyses();
                    }
                });
            }
            
            // 下一页按钮点击事件
            if (this.elements.nextBtn) {
                this.elements.nextBtn.addEventListener('click', () => {
                    if (this.currentPage < this.totalPages) {
                        this.currentPage++;
                        this.loadAnalyses();
                    }
                });
            }
            
            // 模态框关闭按钮
            const closeButtons = document.querySelectorAll('.modal-close');
            closeButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    if (this.elements.modal) {
                        this.elements.modal.style.display = 'none';
                        document.body.style.overflow = '';
                    }
                });
            });
            
            // 点击模态框外部关闭
            if (this.elements.modal) {
                window.addEventListener('click', (e) => {
                    if (e.target === this.elements.modal) {
                        this.elements.modal.style.display = 'none';
                        document.body.style.overflow = '';
                    }
                });
            }
        } catch (error) {
            console.error('绑定事件时发生错误:', error);
            this.showError(`绑定事件失败: ${error.message}`);
        }
    },
    
    /**
     * 显示错误信息
     */
    showError: function(message) {
        if (this.elements.error) {
            this.elements.error.textContent = message;
            this.elements.error.style.display = 'block';
            
            // 5秒后自动隐藏错误信息
            setTimeout(() => {
                this.elements.error.style.display = 'none';
            }, 5000);
        }
    },
    
    /**
     * 加载面部分析数据
     */
    loadAnalyses: function() {
        try {
            console.log('加载面部分析数据, 页码:', this.currentPage, '筛选条件:', this.filters);
            
            // 显示加载中
            if (this.elements.loading) {
                this.elements.loading.style.display = 'block';
            }
            if (this.elements.error) {
                this.elements.error.style.display = 'none';
            }
            
            // 构建URL参数
            const params = new URLSearchParams({
                page: this.currentPage,
                page_size: this.pageSize
            });
            
            // 添加筛选条件
            if (this.filters.userId) {
                params.append('user_id', this.filters.userId);
                console.log('添加用户ID筛选:', this.filters.userId);
            }
            
            if (this.filters.status) {
                params.append('status', this.filters.status);
                console.log('添加状态筛选:', this.filters.status);
            }
            
            const apiUrl = `${this.apiBaseUrl}/admin_get_face_analyses.php?${params}`;
            console.log('API请求URL:', apiUrl);
            
            // 发起API请求
            fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${Auth.getToken()}`,
                    'Cache-Control': 'no-cache, no-store, must-revalidate'
                }
            })
            .then(response => {
                console.log('API响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP错误 ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('API响应数据:', data);
                if (data.status === 'success') {
                    this.analyses = data.data;
                    this.totalPages = data.pagination.total_pages;
                    this.renderTable();
                    this.updatePagination(data.pagination);
                } else {
                    throw new Error(data.message || '获取数据失败');
                }
            })
            .catch(error => {
                console.error('加载面部分析数据失败:', error);
                if (this.elements.error) {
                    this.elements.error.textContent = `加载失败: ${error.message}`;
                    this.elements.error.style.display = 'block';
                }
                if (this.elements.analysisTable) {
                    this.elements.analysisTable.innerHTML = `<tr><td colspan="8" class="no-data">加载数据失败，请重试</td></tr>`;
                }
            })
            .finally(() => {
                if (this.elements.loading) {
                    this.elements.loading.style.display = 'none';
                }
            });
        } catch (error) {
            console.error('加载数据过程中发生错误:', error);
            this.showError(`加载数据失败: ${error.message}`);
            if (this.elements.loading) {
                this.elements.loading.style.display = 'none';
            }
        }
    },
    
    /**
     * 渲染数据表格
     */
    renderTable: function() {
        try {
            console.log('渲染面部分析数据表格, 数据条数:', this.analyses ? this.analyses.length : 0);
            
            // 如果没有数据
            if (!this.analyses || this.analyses.length === 0) {
                if (this.elements.analysisTable) {
                    this.elements.analysisTable.innerHTML = `<tr><td colspan="8" class="no-data">没有找到符合条件的记录</td></tr>`;
                }
                return;
            }
            
            // 渲染表格内容
            let html = '';
            
            this.analyses.forEach((analysis, index) => {
                try {
                    // 处理图片URL，确保有效
                    let frontPhotoUrl = '#';
                    let sidePhotoUrl = '';
                    
                    // 优先使用CDN URL，然后是display URL，最后是本地URL
                    if (analysis.cdn_front_photo_url) {
                        frontPhotoUrl = analysis.cdn_front_photo_url;
                    } else if (analysis.display_front_photo_url) {
                        frontPhotoUrl = analysis.display_front_photo_url;
                    } else if (analysis.front_photo_url) {
                        frontPhotoUrl = analysis.front_photo_url;
                    }
                    
                    // 优先使用CDN URL，然后是display URL，最后是本地URL
                    if (analysis.cdn_side_photo_url) {
                        sidePhotoUrl = analysis.cdn_side_photo_url;
                    } else if (analysis.display_side_photo_url) {
                        sidePhotoUrl = analysis.display_side_photo_url;
                    } else if (analysis.side_photo_url) {
                        sidePhotoUrl = analysis.side_photo_url;
                    }
                    
                    // 状态样式
                    const statusMap = {
                        'pending': '待处理',
                        'processing': '处理中',
                        'completed': '已完成',
                        'failed': '失败'
                    };
                    
                    const statusClass = `status-badge status-${analysis.status || 'pending'}`;
                    const statusText = statusMap[analysis.status] || analysis.status || '未知';
                    
                    // 生成表格行
                    html += `
                        <tr>
                            <td>${analysis.id || '-'}</td>
                            <td>
                                ${analysis.nickname ? `<span>${analysis.nickname}</span><br>` : ''}
                                <small>ID: ${analysis.user_id || '-'}</small>
                            </td>
                            <td>
                                <img src="${frontPhotoUrl}" alt="正面照片" class="thumbnail" data-origin="${frontPhotoUrl}">
                            </td>
                            <td>
                                ${sidePhotoUrl ? `<img src="${sidePhotoUrl}" alt="侧面照片" class="thumbnail" data-origin="${sidePhotoUrl}">` : '无'}
                            </td>
                            <td>${analysis.preferred_style || '无'}</td>
                            <td><span class="${statusClass}">${statusText}</span></td>
                            <td>${analysis.created_at || '-'}</td>
                            <td>
                                <button class="action-btn view-btn" data-id="${analysis.id}">查看详情</button>
                                <button class="action-btn delete-btn" data-id="${analysis.id}" style="color: #f5222d; border-color: #f5222d;">删除</button>
                            </td>
                        </tr>
                    `;
                } catch (error) {
                    console.error(`渲染第${index+1}行数据时出错:`, error, analysis);
                    html += `
                        <tr>
                            <td colspan="8" class="error-row">数据渲染错误: ${error.message}</td>
                        </tr>
                    `;
                }
            });
            
            if (this.elements.analysisTable) {
                this.elements.analysisTable.innerHTML = html;
                
                // 绑定查看详情按钮点击事件
                const viewButtons = document.querySelectorAll('.view-btn[data-id]');
                viewButtons.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const analysisId = e.target.getAttribute('data-id');
                        this.viewAnalysisDetail(analysisId);
                    });
                });
                
                // 绑定删除按钮点击事件
                const deleteButtons = document.querySelectorAll('.delete-btn[data-id]');
                deleteButtons.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const analysisId = e.target.getAttribute('data-id');
                        this.confirmDeleteAnalysis(analysisId);
                    });
                });
                
                // 绑定图片查看功能
                this.bindImageViewer();
            } else {
                console.error('分析表格元素不存在，无法渲染数据');
            }
        } catch (error) {
            console.error('渲染表格时发生错误:', error);
            this.showError(`渲染表格失败: ${error.message}`);
            if (this.elements.analysisTable) {
                this.elements.analysisTable.innerHTML = `<tr><td colspan="8" class="no-data">渲染数据失败，请刷新页面重试</td></tr>`;
            }
        }
    },
    
    /**
     * 绑定图片查看器
     */
    bindImageViewer: function() {
        // 确保ImageViewer已初始化
        if (typeof ImageViewer !== 'undefined') {
            ImageViewer.bindImages('.thumbnail');
        } else {
            console.error('ImageViewer未找到，无法启用图片预览功能');
        }
    },
    
    /**
     * 更新分页控件
     * @param {Object} pagination - 分页信息
     */
    updatePagination: function(pagination) {
        this.elements.pageInfo.textContent = `第 ${pagination.page}/${pagination.total_pages} 页`;
        this.elements.prevBtn.disabled = pagination.page <= 1;
        this.elements.nextBtn.disabled = pagination.page >= pagination.total_pages;
    },
    
    /**
     * 查看分析详情
     * @param {number} id - 分析记录ID
     */
    viewAnalysisDetail: function(id) {
        // 显示加载中
        this.elements.modalBody.innerHTML = '<div class="loading-indicator" style="display:block;">加载详情数据...</div>';
        this.elements.modal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // 阻止背景滚动
        
        // 发起API请求获取详细信息
        fetch(`${this.apiBaseUrl}/admin_get_face_analysis_detail.php?id=${id}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP错误 ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                this.renderAnalysisDetail(data.data);
            } else {
                throw new Error(data.message || '获取详情失败');
            }
        })
        .catch(error => {
            console.error('加载分析详情失败:', error);
            this.elements.modalBody.innerHTML = `<div class="error-message" style="display:block;">加载详情失败: ${error.message}</div>`;
        });
    },
    
    /**
     * 渲染分析详情
     * @param {Object} analysis - 分析详情数据
     */
    renderAnalysisDetail: function(analysis) {
        // 添加调试代码，输出接收到的详情数据
        console.log('接收到的面容分析详情数据:', analysis);
        
        // 状态映射
        const statusMap = {
            'pending': '待处理',
            'processing': '处理中',
            'completed': '已完成',
            'failed': '失败'
        };
        
        // 处理图片URL，确保有效
        let frontPhotoUrl = '#';
        let sidePhotoUrl = '';
        
        // 优先使用CDN URL，然后是display URL，最后是本地URL
        if (analysis.cdn_front_photo_url) {
            frontPhotoUrl = analysis.cdn_front_photo_url;
            console.log('详情页使用CDN正面照片URL:', frontPhotoUrl);
        } else if (analysis.display_front_photo_url) {
            frontPhotoUrl = analysis.display_front_photo_url;
            console.log('详情页使用display正面照片URL:', frontPhotoUrl);
        } else if (analysis.front_photo_url) {
            frontPhotoUrl = analysis.front_photo_url;
            console.log('详情页使用本地正面照片URL:', frontPhotoUrl);
        }
        
        // 优先使用CDN URL，然后是display URL，最后是本地URL
        if (analysis.cdn_side_photo_url) {
            sidePhotoUrl = analysis.cdn_side_photo_url;
            console.log('详情页使用CDN侧面照片URL:', sidePhotoUrl);
        } else if (analysis.display_side_photo_url) {
            sidePhotoUrl = analysis.display_side_photo_url;
            console.log('详情页使用display侧面照片URL:', sidePhotoUrl);
        } else if (analysis.side_photo_url) {
            sidePhotoUrl = analysis.side_photo_url;
            console.log('详情页使用本地侧面照片URL:', sidePhotoUrl);
        }
        
        // 构建基本信息
        let html = `
            <div class="analysis-detail">
                <h4>基本信息</h4>
                <div class="analysis-info">
                    <div class="info-item">
                        <span class="info-label">分析ID</span>
                        <span class="info-value">${analysis.id}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">用户ID</span>
                        <span class="info-value">${analysis.user_id}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">用户昵称</span>
                        <span class="info-value">${analysis.nickname || '无'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">偏好风格</span>
                        <span class="info-value">${analysis.preferred_style || '无'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">状态</span>
                        <span class="info-value">${statusMap[analysis.status] || analysis.status}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">创建时间</span>
                        <span class="info-value">${analysis.created_at}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">更新时间</span>
                        <span class="info-value">${analysis.updated_at || '无'}</span>
                    </div>
                </div>
                
                <h4>照片</h4>
                <div class="analysis-photos">
                    <div class="photo-container">
                        <img src="${frontPhotoUrl}" alt="正面照片" data-origin="${frontPhotoUrl}">
                        <div class="photo-label">正面照片</div>
                    </div>
                    ${sidePhotoUrl ? `
                    <div class="photo-container">
                        <img src="${sidePhotoUrl}" alt="侧面照片" data-origin="${sidePhotoUrl}">
                        <div class="photo-label">侧面照片</div>
                    </div>` : `
                    <div class="photo-container">
                        <div style="height:200px;display:flex;align-items:center;justify-content:center;background:#f5f5f5;border-radius:4px;">
                            <span style="color:#999;">无侧面照片</span>
                        </div>
                        <div class="photo-label">侧面照片</div>
                    </div>`}
                </div>
        `;
        
        // 添加分析结果（如果有）
        if (analysis.analysis_result && analysis.status === 'completed') {
            let resultContent = '';
            
            // 尝试格式化JSON显示
            if (typeof analysis.analysis_result === 'object') {
                try {
                    resultContent = JSON.stringify(analysis.analysis_result, null, 2);
                } catch (e) {
                    console.error('格式化JSON失败:', e);
                    resultContent = '解析结果格式错误';
                }
            } else if (analysis.analysis_result_json) {
                try {
                    const parsed = JSON.parse(analysis.analysis_result_json);
                    resultContent = JSON.stringify(parsed, null, 2);
                } catch (e) {
                    console.error('解析JSON失败:', e);
                    resultContent = analysis.analysis_result_json || '无法解析的结果数据';
                }
            } else {
                resultContent = '无结构化结果数据';
            }
            
            html += `
                <h4>分析结果</h4>
                ${analysis.summary ? `<div class="analysis-summary">${analysis.summary}</div>` : ''}
                <pre class="analysis-result">${this.escapeHTML(resultContent)}</pre>
            `;
        } else if (analysis.status === 'failed') {
            html += `
                <h4>分析结果</h4>
                <div class="error-message" style="display:block;">分析失败</div>
            `;
        } else if (analysis.status === 'pending' || analysis.status === 'processing') {
            html += `
                <h4>分析结果</h4>
                <div style="text-align:center;padding:20px;color:#999;">分析尚未完成</div>
            `;
        }
        
        html += '</div>'; // 结束 analysis-detail
        
        // 更新模态框内容
        this.elements.modalBody.innerHTML = html;
        
        // 绑定图片查看器
        if (typeof ImageViewer !== 'undefined') {
            ImageViewer.bindImages('.photo-container img');
        }
    },
    
    /**
     * 转义HTML特殊字符
     * @param {string} text - 需要转义的文本
     * @return {string} 转义后的文本
     */
    escapeHTML: function(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },
    
    /**
     * 确认删除分析记录
     * @param {number} id - 分析记录ID
     */
    confirmDeleteAnalysis: function(id) {
        if (confirm(`确定要删除ID为${id}的面容分析记录吗？此操作不可恢复。`)) {
            this.deleteAnalysis(id);
        }
    },
    
    /**
     * 删除分析记录
     * @param {number} id - 分析记录ID
     */
    deleteAnalysis: function(id) {
        console.log('开始删除分析记录:', id);
        
        // 显示加载提示
        if (this.elements.loading) {
            this.elements.loading.style.display = 'block';
        }
        
        // 构建FormData
        const formData = new FormData();
        formData.append('action', 'delete');
        formData.append('analysis_id', id);
        
        // 发送删除请求
        fetch(`${this.apiBaseUrl}/admin_delete_face_analysis.php`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            },
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP错误 ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('删除响应:', data);
            
            if (data.status === 'success') {
                // 显示成功提示
                alert('删除成功');
                
                // 重新加载数据
                this.loadAnalyses();
            } else {
                throw new Error(data.message || '删除失败');
            }
        })
        .catch(error => {
            console.error('删除分析记录失败:', error);
            alert(`删除失败: ${error.message}`);
        })
        .finally(() => {
            // 隐藏加载提示
            if (this.elements.loading) {
                this.elements.loading.style.display = 'none';
            }
        });
    }
}; 