<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 日志函数
function logDebug($message, $data = null) {
    $log_file = __DIR__ . '/koututype_debug.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] {$message}";
    
    if ($data !== null) {
        $log_message .= ': ' . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    
    file_put_contents($log_file, $log_message . PHP_EOL, FILE_APPEND);
}

logDebug("接收到抠图类型识别请求", ['method' => $_SERVER['REQUEST_METHOD']]);

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    logDebug("OPTIONS预检请求，直接返回");
    exit;
}

// 检查请求数据
$json = file_get_contents('php://input');
if (empty($json)) {
    logDebug("错误: 缺少请求数据");
    echo json_encode(['success' => false, 'msg' => '缺少请求数据']);
    exit;
}

$data = json_decode($json, true);
if (!$data) {
    logDebug("错误: 无效的JSON数据", ['raw_input' => substr($json, 0, 1000)]);
    echo json_encode(['success' => false, 'msg' => '无效的JSON数据']);
    exit;
}

// 验证必要参数
if (!isset($data['image_base64'])) {
    logDebug("错误: 缺少图片数据");
    echo json_encode(['success' => false, 'msg' => '缺少图片数据']);
    exit;
}

// 获取图片base64数据
$image_base64 = $data['image_base64'];
logDebug("接收到图片数据", ['length' => strlen($image_base64)]);

// 配置Gemini API密钥
$apiKey = 'AIzaSyD1-g64EwoKNcvs0LeAn9hbyHJRuKj0Slg'; // Gemini API密钥
$model = 'gemini-1.5-flash'; // 使用支持图像的模型

// 构建提示词
$prompt = "请分析这张图片中的物品类型，并判断需要使用哪种抠图模式。";
$prompt .= "\n\n我们有两种抠图模式：";
$prompt .= "\n1. 服饰分割(cloth)：适用于衣服等穿在人身上的服饰，可以去除人物只保留衣物本身。";
$prompt .= "\n2. 通用分割(common)：适用于所有物品，但会保留整个物品，包括人物(如果有)。";
$prompt .= "\n\n请根据图片内容回答以下问题:";
$prompt .= "\n1. 图片中主要物品是什么?";
$prompt .= "\n2. 物品是否穿在人身上?";
$prompt .= "\n3. 最适合的抠图类型是什么?('cloth'或'common')";
$prompt .= "\n4. 为什么选择这种抠图类型?";
$prompt .= "\n\n请以JSON格式返回结果，格式如下:";
$prompt .= "\n{";
$prompt .= "\n  \"item_type\": \"物品类型\",";
$prompt .= "\n  \"worn_by_person\": true/false,";
$prompt .= "\n  \"segment_type\": \"cloth或common\",";
$prompt .= "\n  \"reasoning\": \"选择理由\"";
$prompt .= "\n}";

logDebug("构建的提示词", ['prompt_length' => strlen($prompt), 'prompt_excerpt' => $prompt]);

// 调用Gemini API
$geminiUrl = "https://generativelanguage.googleapis.com/v1beta/models/" . $model . ":generateContent?key=" . $apiKey;

$request = [
    "contents" => [
        [
            "parts" => [
                [
                    "text" => $prompt
                ],
                [
                    "inline_data" => [
                        "mime_type" => "image/jpeg",
                        "data" => $image_base64
                    ]
                ]
            ]
        ]
    ],
    "generationConfig" => [
        "temperature" => 0.2,
        "topP" => 0.8,
        "topK" => 40,
        "maxOutputTokens" => 1024
    ]
];

logDebug("准备调用Gemini API", ['url' => $geminiUrl]);

// 发送请求到Gemini API
$ch = curl_init($geminiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

$response = curl_exec($ch);
if (curl_errno($ch)) {
    $error = curl_error($ch);
    logDebug("调用Gemini API失败", ['curl_error' => $error]);
    echo json_encode(['success' => false, 'msg' => '调用Gemini API失败: ' . $error]);
    exit;
}
curl_close($ch);
logDebug("Gemini API调用完成", ['response_length' => strlen($response)]);

// 解析API响应
$api_response = json_decode($response, true);

// 检查API响应是否有效
if (!$api_response || !isset($api_response['candidates']) || empty($api_response['candidates'])) {
    logDebug("无效的API响应", ['response' => substr($response, 0, 1000)]);
    echo json_encode(['success' => false, 'msg' => '无效的API响应']);
    exit;
}

// 提取生成的文本
$generated_text = '';
if (isset($api_response['candidates'][0]['content']['parts'][0]['text'])) {
    $generated_text = $api_response['candidates'][0]['content']['parts'][0]['text'];
}

if (empty($generated_text)) {
    logDebug("API返回的文本为空");
    echo json_encode(['success' => false, 'msg' => 'API返回的文本为空']);
    exit;
}

logDebug("API返回的原始文本", ['text_length' => strlen($generated_text), 'text_excerpt' => substr($generated_text, 0, 500)]);

// 从生成的文本中提取JSON部分
$segment_json = extractJsonFromText($generated_text);
if (!$segment_json) {
    logDebug("无法从API响应中提取有效的JSON", ['text' => $generated_text]);
    
    // 如果无法提取JSON，默认使用通用分割
    echo json_encode([
        'success' => true,
        'data' => [
            'segment_type' => 'common',
            'item_type' => '未知物品',
            'worn_by_person' => false,
            'reasoning' => '无法从API响应中提取有效的JSON，默认使用通用分割'
        ]
    ]);
    exit;
}

logDebug("提取的JSON数据", ['json_data' => $segment_json]);

// 验证并处理提取的JSON数据
$segment_data = json_decode($segment_json, true);
if (!$segment_data) {
    logDebug("无法解析提取的JSON", ['json_string' => $segment_json]);
    
    // 如果无法解析JSON，默认使用通用分割
    echo json_encode([
        'success' => true,
        'data' => [
            'segment_type' => 'common',
            'item_type' => '未知物品',
            'worn_by_person' => false,
            'reasoning' => '无法解析提取的JSON，默认使用通用分割'
        ]
    ]);
    exit;
}

// 验证segment_type字段是否有效
if (!isset($segment_data['segment_type']) || 
    ($segment_data['segment_type'] !== 'cloth' && $segment_data['segment_type'] !== 'common')) {
    
    logDebug("无效的分割类型", ['segment_data' => $segment_data]);
    
    // 从上下文推断合适的分割类型
    $inferredType = 'common'; // 默认为通用分割
    
    // 如果有worn_by_person字段且为true，则使用cloth模式
    if (isset($segment_data['worn_by_person']) && $segment_data['worn_by_person'] === true) {
        $inferredType = 'cloth';
        logDebug("根据穿戴状态推断为服饰分割");
    }
    
    // 如果有item_type字段，检查是否包含服饰相关关键词
    if (isset($segment_data['item_type'])) {
        $itemType = strtolower($segment_data['item_type']);
        $clothKeywords = ['衣', '服', '裙', '裤', '衫', '袜', '外套', 'shirt', 'dress', 'pants', 'cloth', 'jacket', 'skirt'];
        
        foreach ($clothKeywords as $keyword) {
            if (strpos($itemType, $keyword) !== false) {
                $inferredType = 'cloth';
                logDebug("根据物品类型包含关键词 '$keyword' 推断为服饰分割");
                break;
            }
        }
    }
    
    // 设置推断的类型
    $segment_data['segment_type'] = $inferredType;
    $segment_data['reasoning'] = "分割类型无效或缺失，根据上下文推断使用{$inferredType}分割";
    
    logDebug("推断分割类型为: " . $inferredType, ['reasoning' => $segment_data['reasoning']]);
}

// 返回处理后的结果
echo json_encode([
    'success' => true,
    'data' => $segment_data
]);

logDebug("成功返回抠图类型", ['segment_type' => $segment_data['segment_type']]);

/**
 * 从文本中提取JSON部分
 * @param string $text 包含JSON的文本
 * @return string|null 提取的JSON字符串，如果没有找到则返回null
 */
function extractJsonFromText($text) {
    // 尝试匹配花括号包围的JSON部分
    if (preg_match('/\{[\s\S]*\}/m', $text, $matches)) {
        return $matches[0];
    }
    return null;
}
?> 