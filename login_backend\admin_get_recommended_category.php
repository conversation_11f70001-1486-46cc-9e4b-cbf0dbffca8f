<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入配置和辅助函数
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

// 验证管理员权限
$auth = new Auth();

// 检查是否存在Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证管理员token
$adminData = $auth->verifyAdminToken($token);
if (!$adminData) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的管理员令牌']);
    exit();
}

// 验证是否提供了分类ID
if (!isset($_GET['id']) || empty($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少分类ID']);
    exit();
}

$categoryId = (int)$_GET['id'];

try {
    // 获取数据库连接
    $db = new Database();
    $pdo = $db->getConnection();
    
    // 获取分类信息
    $stmt = $pdo->prepare("
        SELECT id, name, description, sort_order, status, created_at, updated_at
        FROM recommended_outfit_categories
        WHERE id = ?
    ");
    $stmt->bindParam(1, $categoryId, PDO::PARAM_INT);
    $stmt->execute();
    
    $category = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$category) {
        http_response_code(404);
        echo json_encode(['error' => true, 'msg' => '找不到指定的分类']);
        exit();
    }
    
    // 获取此分类下的穿搭数量
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as outfit_count 
        FROM recommended_outfits
        WHERE category_id = ?
    ");
    $stmt->bindParam(1, $categoryId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $category['outfit_count'] = (int)$result['outfit_count'];
    
    // 返回成功响应
    http_response_code(200);
    echo json_encode([
        'error' => false,
        'data' => $category
    ]);
    
} catch (PDOException $e) {
    error_log("Database error in admin_get_recommended_category.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '数据库错误: ' . $e->getMessage()]);
} catch (Exception $e) {
    error_log("General error in admin_get_recommended_category.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '服务器错误: ' . $e->getMessage()]);
} 