<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] != 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证管理员身份
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取查询参数
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 10;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$type = isset($_GET['type']) ? trim($_GET['type']) : '';
$sortBy = isset($_GET['sort_by']) ? trim($_GET['sort_by']) : 'id';
$sortOrder = isset($_GET['sort_order']) ? strtoupper(trim($_GET['sort_order'])) : 'DESC';
$userId = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;

// 验证排序字段
$allowedSortFields = ['id', 'user_id', 'type', 'created_at', 'updated_at'];
if (!in_array($sortBy, $allowedSortFields)) {
    $sortBy = 'id';
}

// 验证排序顺序
if ($sortOrder != 'ASC' && $sortOrder != 'DESC') {
    $sortOrder = 'DESC';
}

// 分页参数
$offset = ($page - 1) * $perPage;

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

// 构建查询条件
$conditions = [];
$params = [];

// 用户ID过滤
if ($userId > 0) {
    $conditions[] = 'p.user_id = :user_id';
    $params[':user_id'] = $userId;
}

// 类型过滤
if ($type !== '') {
    $conditions[] = 'p.type = :type';
    $params[':type'] = $type;
}

// 搜索条件
if ($search !== '') {
    $conditions[] = '(p.description LIKE :search OR u.nickname LIKE :search OR u.username LIKE :search)';
    $params[':search'] = "%$search%";
}

// 构建WHERE子句
$whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';

// 查询总记录数
$countQuery = "SELECT COUNT(*) FROM photos p LEFT JOIN users u ON p.user_id = u.id $whereClause";
$countStmt = $conn->prepare($countQuery);
foreach ($params as $key => $value) {
    $countStmt->bindValue($key, $value);
}
$countStmt->execute();
$totalItems = $countStmt->fetchColumn();
$totalPages = ceil($totalItems / $perPage);

// 如果没有记录或页码超出范围
if ($totalItems === 0 || $page > $totalPages) {
    echo json_encode([
        'error' => false,
        'data' => [],
        'pagination' => [
            'total' => 0,
            'per_page' => $perPage,
            'current_page' => $page,
            'total_pages' => 0
        ]
    ]);
    exit();
}

// 查询照片数据
$query = "SELECT 
            p.id, p.user_id, p.image_url, p.type, p.description, p.created_at, p.updated_at,
            u.nickname as user_nickname, u.avatar_url as user_avatar
          FROM photos p
          LEFT JOIN users u ON p.user_id = u.id
          $whereClause
          ORDER BY p.$sortBy $sortOrder
          LIMIT :offset, :per_page";

$stmt = $conn->prepare($query);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':per_page', $perPage, PDO::PARAM_INT);

foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}

$stmt->execute();
$photos = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 返回数据
echo json_encode([
    'error' => false,
    'data' => $photos,
    'pagination' => [
        'total' => (int)$totalItems,
        'per_page' => $perPage,
        'current_page' => $page,
        'total_pages' => $totalPages
    ]
]); 