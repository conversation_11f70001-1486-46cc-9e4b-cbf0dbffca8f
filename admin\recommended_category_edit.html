<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑推荐穿搭分类</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <style>
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .required::after {
            content: " *";
            color: #ff4d4f;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            min-height: 100px;
            resize: vertical;
            box-sizing: border-box;
        }
        
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background-color: white;
            box-sizing: border-box;
        }
        
        .help-text {
            color: #8c8c8c;
            font-size: 12px;
            margin-top: 5px;
        }
        
        .btn-group {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
            border: none;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
        }
        
        .btn-default {
            background-color: #f5f5f5;
            color: #595959;
            border: 1px solid #d9d9d9;
        }
        
        .btn-default:hover {
            background-color: #e8e8e8;
        }
        
        .alert {
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            display: none;
        }
        
        .alert-success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        
        .alert-error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .form-col {
            flex: 1;
            min-width: 200px;
        }
        
        @media (max-width: 768px) {
            .form-col {
                flex: 100%;
            }
        }
        
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2 id="pageTitle">编辑推荐穿搭分类</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <!-- 提示信息 -->
            <div id="successAlert" class="alert alert-success">操作成功！</div>
            <div id="errorAlert" class="alert alert-error">发生错误！</div>
            
            <!-- 表单卡片 -->
            <div class="card">
                <form id="categoryForm">
                    <input type="hidden" id="categoryId">
                    
                    <div class="form-group">
                        <label for="categoryName" class="required">分类名称</label>
                        <input type="text" id="categoryName" class="form-input" required>
                        <div class="help-text">请输入分类的显示名称，如"夏季穿搭"、"通勤风格"等</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="categoryDescription">分类描述</label>
                        <textarea id="categoryDescription" class="form-textarea"></textarea>
                        <div class="help-text">简要描述此分类的特点和包含的穿搭风格</div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="sortOrder">排序顺序</label>
                                <input type="number" id="sortOrder" class="form-input" min="0" value="0">
                                <div class="help-text">数字越小，显示越靠前</div>
                            </div>
                        </div>
                        
                        <div class="form-col">
                            <div class="form-group">
                                <label for="categoryStatus">状态</label>
                                <select id="categoryStatus" class="form-select">
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                                <div class="help-text">禁用的分类在前端不会显示</div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="outfitCountContainer" class="form-group" style="display: none;">
                        <label>包含穿搭数量</label>
                        <div id="outfitCount" class="form-static">0</div>
                    </div>
                    
                    <div class="btn-group">
                        <button type="submit" id="saveBtn" class="btn btn-primary">保存</button>
                        <a href="recommended_category_list.html" class="btn btn-default">取消</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/recommended_category_edit.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为recommended_category
            Sidebar.init('recommended_category');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化推荐穿搭分类编辑页面
            if (typeof RecommendedCategoryEdit !== 'undefined') {
                RecommendedCategoryEdit.init();
            }
        });
    </script>
</body>
</html> 