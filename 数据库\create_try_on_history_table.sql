-- Create try_on_history table
CREATE TABLE IF NOT EXISTS try_on_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    result_image_url TEXT NOT NULL COMMENT '试穿结果图片URL',
    clothes_ids TEXT NOT NULL COMMENT '以JSON数组格式存储的衣物ID',
    photo_id INT NULL COMMENT '关联的照片ID',
    task_id VARCHAR(100) NULL COMMENT '阿里云任务ID',
    status VARCHAR(20) NULL COMMENT '状态：success, failed',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create index on user_id for faster lookups
CREATE INDEX idx_try_on_user_id ON try_on_history(user_id);

-- Create index on created_at for faster sorting
CREATE INDEX idx_try_on_created_at ON try_on_history(created_at); 