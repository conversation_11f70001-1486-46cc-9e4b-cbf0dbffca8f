<?php
/**
 * 测试衣橱筛选功能的脚本
 * 用于验证衣橱筛选在不同数据源下是否正常工作
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['user_id'];

// 获取测试参数
$dataSource = isset($_GET['data_source']) ? $_GET['data_source'] : 'all';
$wardrobeId = isset($_GET['wardrobe_id']) ? intval($_GET['wardrobe_id']) : null;

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $result = [];
    $result['test_params'] = [
        'user_id' => $userId,
        'data_source' => $dataSource,
        'wardrobe_id' => $wardrobeId
    ];
    
    // 1. 测试不同数据源下的衣物查询
    $testCases = [
        'personal' => 'personal',
        'shared' => 'shared', 
        'all' => 'all'
    ];
    
    foreach ($testCases as $testName => $testDataSource) {
        echo "测试数据源: $testDataSource\n";
        
        // 构建SQL查询
        if ($testDataSource === 'personal') {
            $sql = "SELECT c.id, c.name, c.category, c.wardrobe_id, c.circle_id,
                           CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                    FROM clothes c
                    WHERE c.user_id = :user_id AND c.circle_id IS NULL";
            $params = ['user_id' => $userId];
        } elseif ($testDataSource === 'shared') {
            $sql = "SELECT c.id, c.name, c.category, c.wardrobe_id, c.circle_id,
                           CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                    FROM clothes c
                    WHERE c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')";
            $params = ['user_id' => $userId];
        } else { // all
            $sql = "SELECT c.id, c.name, c.category, c.wardrobe_id, c.circle_id,
                           CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                    FROM clothes c
                    WHERE ((c.user_id = :user_id AND c.circle_id IS NULL) OR
                           (c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')))";
            $params = ['user_id' => $userId];
        }
        
        // 如果指定了衣橱ID，添加衣橱筛选
        if ($wardrobeId) {
            $sql .= " AND c.wardrobe_id = :wardrobe_id";
            $params['wardrobe_id'] = $wardrobeId;
        }
        
        $sql .= " ORDER BY c.created_at DESC";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        $clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result[$testName] = [
            'sql' => $sql,
            'params' => $params,
            'count' => count($clothes),
            'clothes' => array_slice($clothes, 0, 5) // 只返回前5条作为示例
        ];
    }
    
    // 2. 检查指定衣橱的详细信息
    if ($wardrobeId) {
        $stmt = $conn->prepare("
            SELECT w.id, w.name, w.user_id, w.circle_id,
                   CASE WHEN w.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source,
                   u.nickname as creator_nickname
            FROM wardrobes w
            LEFT JOIN users u ON w.user_id = u.id
            WHERE w.id = :wardrobe_id
        ");
        $stmt->bindParam(':wardrobe_id', $wardrobeId, PDO::PARAM_INT);
        $stmt->execute();
        $wardrobeInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $result['wardrobe_info'] = $wardrobeInfo;
        
        // 检查该衣橱中的所有衣物（不限制数据源）
        $stmt = $conn->prepare("
            SELECT c.id, c.name, c.category, c.user_id, c.wardrobe_id, c.circle_id,
                   CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source,
                   u.nickname as creator_nickname
            FROM clothes c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE c.wardrobe_id = :wardrobe_id
            ORDER BY c.created_at DESC
        ");
        $stmt->bindParam(':wardrobe_id', $wardrobeId, PDO::PARAM_INT);
        $stmt->execute();
        $allClothesInWardrobe = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result['all_clothes_in_wardrobe'] = [
            'count' => count($allClothesInWardrobe),
            'clothes' => array_slice($allClothesInWardrobe, 0, 10)
        ];
    }
    
    // 3. 检查用户权限
    $stmt = $conn->prepare("
        SELECT cm.circle_id, c.name as circle_name, cm.status, cm.role
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $userCircles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['user_circles'] = $userCircles;
    
    echo json_encode([
        'status' => 'success',
        'data' => $result
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '查询失败: ' . $e->getMessage()
    ]);
}
?>
