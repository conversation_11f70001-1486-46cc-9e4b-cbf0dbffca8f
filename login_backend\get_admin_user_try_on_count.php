<?php
/**
 * 管理员获取用户试衣次数API
 * 
 * 返回指定用户的剩余试衣次数和计数模式
 * 
 * 请求参数:
 * - user_id: 用户ID
 * 
 * 返回:
 * {
 *   "error": false,
 *   "data": {
 *     "free_try_on_count": 1,   // 免费试衣次数
 *     "paid_try_on_count": 5,   // 付费试衣次数
 *     "count_mode": "dual"      // "daily", "database" 或 "dual"
 *   }
 * }
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 检查是否有Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

// 获取管理员Token
$adminToken = $_SERVER['HTTP_AUTHORIZATION'];
// 如果有Bearer前缀，去掉它
if (strpos($adminToken, 'Bearer ') === 0) {
    $adminToken = substr($adminToken, 7);
}

// 验证管理员Token
$auth = new Auth();
$adminData = $auth->verifyAdminToken($adminToken);

if (!$adminData) {
    echo json_encode([
        'error' => true,
        'msg' => '无效的管理员令牌'
    ]);
    exit;
}

// 获取请求参数
$userId = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;

// 验证参数
if ($userId <= 0) {
    echo json_encode([
        'error' => true,
        'msg' => '无效的用户ID'
    ]);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 获取当前使用的计数模式
    $countMode = defined('TRY_ON_COUNT_MODE') ? TRY_ON_COUNT_MODE : 'daily';
    
    // 从users表获取次数
    $stmt = $conn->prepare("SELECT free_try_on_count, paid_try_on_count FROM users WHERE id = :user_id");
    $stmt->execute(['user_id' => $userId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$result) {
        echo json_encode([
            'error' => true,
            'msg' => '用户不存在'
        ]);
        exit;
    }
    
    $freeCount = isset($result['free_try_on_count']) ? (int)$result['free_try_on_count'] : 0;
    $paidCount = isset($result['paid_try_on_count']) ? (int)$result['paid_try_on_count'] : 0;
    
    // 根据不同模式返回不同的数据结构
    if ($countMode === 'dual') {
        // 双层次数模式：返回免费次数和付费次数
        echo json_encode([
            'error' => false,
            'data' => [
                'free_try_on_count' => $freeCount,
                'paid_try_on_count' => $paidCount,
                'count_mode' => $countMode
            ]
        ]);
    } else if ($countMode === 'database') {
        // 数据库模式：将付费次数作为try_on_count返回，保持向后兼容
        echo json_encode([
            'error' => false,
            'data' => [
                'try_on_count' => $paidCount,
                'count_mode' => $countMode
            ]
        ]);
    } else {
        // 每日一次模式：仅返回计数模式，因为次数由daily逻辑决定
        echo json_encode([
            'error' => false,
            'data' => [
                'try_on_count' => 1, // 每日模式固定为1
                'count_mode' => $countMode
            ]
        ]);
    }
    
} catch (PDOException $e) {
    echo json_encode([
        'error' => true,
        'msg' => '数据库操作失败: ' . $e->getMessage()
    ]);
    exit;
} 