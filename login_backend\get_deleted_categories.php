<?php
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $auth = new Auth();
    
    // 验证用户token
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? null;
    
    if (!$authHeader || strpos($authHeader, 'Bearer ') !== 0) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => '未授权访问']);
        exit;
    }
    
    $token = substr($authHeader, 7);
    $tokenData = $auth->verifyToken($token);
    
    if (!$tokenData) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => 'Token无效']);
        exit;
    }
    
    $userId = $tokenData['user_id'] ?? $tokenData['sub'];
    
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查deleted_system_categories表是否存在
    $checkTableSql = "SHOW TABLES LIKE 'deleted_system_categories'";
    $checkTableStmt = $conn->query($checkTableSql);
    
    if ($checkTableStmt->rowCount() === 0) {
        // 表不存在，创建表
        $createTableSql = "CREATE TABLE IF NOT EXISTS deleted_system_categories (
            id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            code VARCHAR(50) NOT NULL,
            deleted_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_code (user_id, code)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $conn->exec($createTableSql);
    }
    
    // 查询该用户的已删除分类
    // 使用DISTINCT确保不返回重复记录
    $sql = "SELECT DISTINCT
                dsc.id, 
                dsc.code, 
                dsc.deleted_at,
                CASE
                    WHEN cc.name IS NOT NULL THEN cc.name
                    WHEN dsc.code = 'tops' THEN '上衣'
                    WHEN dsc.code = 'pants' THEN '裤子'
                    WHEN dsc.code = 'skirts' THEN '裙子'
                    WHEN dsc.code = 'coats' THEN '外套'
                    WHEN dsc.code = 'shoes' THEN '鞋子'
                    WHEN dsc.code = 'bags' THEN '包包'
                    WHEN dsc.code = 'accessories' THEN '配饰'
                    WHEN dsc.code LIKE 'custom_%' THEN '自定义分类'
                    ELSE '未知分类'
                END as name,
                CASE
                    WHEN dsc.code IN ('tops','pants','skirts','coats','shoes','bags','accessories') THEN 1
                    ELSE 0
                END as is_system
            FROM deleted_system_categories dsc
            LEFT JOIN clothing_categories cc ON cc.code = dsc.code AND cc.is_system = 1
            WHERE dsc.user_id = :user_id
            ORDER BY dsc.deleted_at DESC";
            
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    
    $deletedCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 输出调试信息
    error_log("已删除分类查询结果，不重复条数：" . count($deletedCategories));
    error_log("分类详情: " . json_encode($deletedCategories, JSON_UNESCAPED_UNICODE));
    
    // 由于已在SQL中直接转换名称，这里的处理逻辑可以简化
    foreach ($deletedCategories as &$category) {
        // 格式化删除时间
        $category['deleted_at'] = date('Y-m-d H:i:s', strtotime($category['deleted_at']));
        
        // 确保is_system字段是布尔值
        $category['is_system'] = (int)$category['is_system'] === 1;
    }
    
    echo json_encode([
        'error' => false,
        'data' => $deletedCategories
    ]);
    
    // 日志记录用户ID和查询结果
    error_log("用户ID: " . $userId . " 获取已删除分类，共 " . count($deletedCategories) . " 条记录");
    
} catch (Exception $e) {
    http_response_code(500);
    error_log("获取已删除分类失败: " . $e->getMessage());
    echo json_encode([
        'error' => true,
        'msg' => '服务器错误: ' . $e->getMessage()
    ]);
} 