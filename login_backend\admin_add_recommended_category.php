<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入配置和辅助函数
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

// 验证管理员权限
$auth = new Auth();

// 检查是否存在Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证管理员token
$adminData = $auth->verifyAdminToken($token);
if (!$adminData) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的管理员令牌']);
    exit();
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

// 验证必要的字段
if (!isset($input['name']) || empty(trim($input['name']))) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '分类名称不能为空']);
    exit();
}

// 准备分类数据
$categoryData = [
    'name' => trim($input['name']),
    'description' => isset($input['description']) ? trim($input['description']) : '',
    'sort_order' => isset($input['sort_order']) ? (int)$input['sort_order'] : 0,
    'status' => isset($input['status']) ? (int)$input['status'] : 1
];

try {
    // 获取数据库连接
    $db = new Database();
    $pdo = $db->getConnection();
    
    // 插入新分类
    $stmt = $pdo->prepare("
        INSERT INTO recommended_outfit_categories (name, description, sort_order, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, NOW(), NOW())
    ");
    
    $stmt->bindParam(1, $categoryData['name']);
    $stmt->bindParam(2, $categoryData['description']);
    $stmt->bindParam(3, $categoryData['sort_order'], PDO::PARAM_INT);
    $stmt->bindParam(4, $categoryData['status'], PDO::PARAM_INT);
    
    $stmt->execute();
    
    // 获取新插入的ID
    $categoryId = $pdo->lastInsertId();
    
    // 获取新创建的分类
    $stmt = $pdo->prepare("
        SELECT id, name, description, sort_order, status, created_at, updated_at
        FROM recommended_outfit_categories
        WHERE id = ?
    ");
    $stmt->bindParam(1, $categoryId, PDO::PARAM_INT);
    $stmt->execute();
    
    $category = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 返回成功响应
    http_response_code(201); // Created
    echo json_encode([
        'error' => false,
        'msg' => '分类创建成功',
        'data' => $category
    ]);
    
} catch (PDOException $e) {
    error_log("Database error in admin_add_recommended_category.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '数据库错误: ' . $e->getMessage()]);
} catch (Exception $e) {
    error_log("General error in admin_add_recommended_category.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '服务器错误: ' . $e->getMessage()]);
} 