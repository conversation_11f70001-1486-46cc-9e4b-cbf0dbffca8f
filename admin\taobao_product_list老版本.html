<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>淘宝商品管理 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 淘宝商品管理页面样式 */
        .filter-form {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        
        .form-group {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }
        
        .form-group label {
            margin-right: 10px;
            white-space: nowrap;
        }
        
        .form-control {
            padding: 6px 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            min-width: 100px;
        }
        
        .btn-search {
            padding: 6px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn-search:hover {
            background-color: #40a9ff;
        }
        
        .btn-reset {
            padding: 6px 16px;
            background-color: #f2f2f2;
            color: #333;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn-reset:hover {
            background-color: #e6e6e6;
        }
        
        .btn-sync {
            padding: 6px 16px;
            background-color: #52c41a;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: auto;
        }
        
        .btn-sync:hover {
            background-color: #73d13d;
        }
        
        /* 表格样式 */
        .product-list {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .product-list th,
        .product-list td {
            padding: 10px;
            border: 1px solid #e8e8e8;
            text-align: left;
        }
        
        .product-list th {
            background-color: #fafafa;
            font-weight: 500;
        }
        
        .product-list tr:hover {
            background-color: #f5f5f5;
        }
        
        .product-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        .product-title {
            max-width: 300px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .price {
            color: #ff4d4f;
            font-weight: bold;
        }
        
        .discount {
            color: #52c41a;
            font-size: 0.85em;
        }
        
        .action-btns {
            display: flex;
            gap: 5px;
        }
        
        .btn-view {
            padding: 4px 10px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85em;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-view:hover {
            background-color: #40a9ff;
        }
        
        .btn-recommend {
            padding: 4px 10px;
            background-color: #faad14;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85em;
        }
        
        .btn-recommend:hover {
            background-color: #ffc53d;
        }
        
        .btn-remove-recommend {
            padding: 4px 10px;
            background-color: #8c8c8c;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85em;
        }
        
        .btn-remove-recommend:hover {
            background-color: #a6a6a6;
        }
        
        /* 淘口令样式 */
        .tpwd-container {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .tpwd-text {
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .btn-copy {
            padding: 2px 6px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
        }
        
        .btn-copy:hover {
            background-color: #40a9ff;
        }
        
        .tpwd-fake {
            color: #faad14;
            font-size: 0.8em;
            margin-left: 3px;
        }
        
        .tpwd-real {
            color: #52c41a;
            font-size: 0.8em;
            margin-left: 3px;
        }
        
        .tpwd-none {
            color: #8c8c8c;
            font-style: italic;
        }
        
        /* 标签样式 */
        .tags-container {
            max-width: 150px;
        }
        
        .tags-list {
            list-style: none;
            padding: 0;
            margin: 0 0 5px 0;
        }
        
        .tag-item {
            display: inline-block;
            background-color: #e6f7ff;
            color: #1890ff;
            padding: 2px 6px;
            border-radius: 3px;
            margin: 0 3px 3px 0;
            font-size: 0.8em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 140px;
        }
        
        .tag-category {
            color: #52c41a;
            font-size: 0.85em;
            margin-top: 3px;
            font-weight: bold;
        }
        
        .tags-none {
            color: #8c8c8c;
            font-style: italic;
        }
        
        .tags-error {
            color: #ff4d4f;
            font-style: italic;
        }
        
        .tags-raw {
            font-size: 0.8em;
            color: #8c8c8c;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 150px;
        }
        
        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
        }
        
        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            background-color: #fff;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .pagination-btn:hover {
            background-color: #e6f7ff;
        }
        
        .pagination-btn:disabled {
            color: #d9d9d9;
            cursor: not-allowed;
            background-color: #f5f5f5;
        }
        
        .pagination-active {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        
        .pagination-active:hover {
            background-color: #40a9ff;
        }
        
        .pagination-info {
            color: #666;
        }
        
        /* 空数据状态 */
        .empty-state {
            text-align: center;
            padding: 40px 0;
            color: #8c8c8c;
        }
        
        /* 同步状态模态框 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
        }
        
        .modal-content {
            background-color: #fff;
            margin: 15% auto;
            padding: 20px;
            border-radius: 4px;
            width: 400px;
            max-width: 80%;
        }
        
        .modal-title {
            font-size: 18px;
            margin-bottom: 15px;
        }
        
        .modal-body {
            margin-bottom: 20px;
        }
        
        .sync-progress {
            height: 6px;
            background-color: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .sync-progress-fill {
            height: 100%;
            background-color: #1890ff;
            border-radius: 3px;
            transition: width 0.3s ease;
        }
        
        .sync-status {
            color: #8c8c8c;
            font-size: 14px;
        }
        
        .modal-footer {
            text-align: right;
        }
        
        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 40px 0;
            color: #8c8c8c;
        }
        
        /* 错误消息 */
        .error-message {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #f5222d;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        /* 菜单链接样式 - 从dashboard.html复制 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <div class="sidebar-logo">
                <img src="https://cyymj.oss-cn-shanghai.aliyuncs.com/logo.png" alt="次元衣柜">
                <h2>次元衣柜后台</h2>
            </div>
            <ul class="menu">
                <li class="menu-item"><a href="dashboard.html">仪表盘</a></li>
                <li class="menu-item"><a href="user_list.html">用户管理</a></li>
                <li class="menu-item"><a href="merchant_list.html">商户管理</a></li>
                <li class="menu-item"><a href="clothing_list.html">衣物管理</a></li>
                <li class="menu-item"><a href="photo_list.html">照片管理</a></li>
                <li class="menu-item"><a href="try_on_list.html">试衣历史</a></li>
                <li class="menu-item"><a href="outfit_list.html">穿搭管理</a></li>
                <li class="menu-item"><a href="outfit_calendar_list.html">穿搭日历</a></li>
                <li class="menu-item"><a href="recommended_outfit_list.html">推荐穿搭</a></li>
                <li class="menu-item"><a href="recommended_category_list.html">推荐穿搭分类</a></li>
                <li class="menu-item"><a href="image_analysis_list.html">形象分析</a></li>
                <li class="menu-item"><a href="donation_list.html">打赏记录</a></li>
                <li class="menu-item"><a href="invitation_code_list.html">邀请码管理</a></li>
                <li class="menu-item"><a href="announcement_list.html">公告管理</a></li>
                <li class="menu-item active"><a href="taobao_product_list.html">淘宝商品管理</a></li>
                <li class="menu-item"><a href="system_settings.html">系统设置</a></li>
            </ul>
        </div>
        
        <div class="content">
            <div class="header">
                <h2>淘宝商品管理</h2>
                <div class="user-info">
                    <span id="userName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <!-- 过滤表单 -->
            <div class="filter-form">
                <div class="form-group">
                    <label for="keyword">关键词</label>
                    <input type="text" id="keyword" class="form-control" placeholder="标题/店铺名">
                </div>
                <div class="form-group">
                    <label for="material_id">物料分类</label>
                    <select id="material_id" class="form-control">
                        <option value="">全部</option>
                        <!-- 物料分类选项将通过JS动态加载 -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="category">商品分类</label>
                    <select id="category" class="form-control">
                        <option value="">全部</option>
                        <!-- 商品分类选项将通过JS动态加载 -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="price_range">价格区间</label>
                    <input type="number" id="min_price" class="form-control" placeholder="最低价" style="width: 80px;">
                    <span>-</span>
                    <input type="number" id="max_price" class="form-control" placeholder="最高价" style="width: 80px;">
                </div>
                <div class="form-group">
                    <label for="tpwd_type">淘口令类型</label>
                    <select id="tpwd_type" class="form-control">
                        <option value="">全部</option>
                        <option value="real">真实淘口令</option>
                        <option value="fake">模拟淘口令</option>
                        <option value="none">无淘口令</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="coupon_only">
                        仅显示有优惠券
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="is_recommend">
                        仅显示推荐商品
                    </label>
                </div>
                <button id="searchBtn" class="btn-search">搜索</button>
                <button id="resetBtn" class="btn-reset">重置</button>
                <button id="syncBtn" class="btn-sync">手动同步</button>
            </div>
            
            <!-- 错误消息 -->
            <div id="errorMessage" class="error-message" style="display: none;"></div>
            
            <!-- 加载状态 -->
            <div id="loading" class="loading">
                <p>加载中，请稍候...</p>
            </div>
            
            <!-- 商品列表 -->
            <div id="productTableContainer" style="display: none;">
                <table class="product-list">
                    <thead>
                        <tr>
                            <th width="70">图片</th>
                            <th>商品名称</th>
                            <th>价格</th>
                            <th>标签</th>
                            <th>佣金比例</th>
                            <th>淘口令</th>
                            <th>店铺</th>
                            <th>分类</th>
                            <th>更新时间</th>
                            <th width="120">操作</th>
                        </tr>
                    </thead>
                    <tbody id="productList">
                        <!-- 商品列表将通过JS动态加载 -->
                    </tbody>
                </table>
                
                <!-- 分页控件 -->
                <div class="pagination" id="pagination">
                    <button id="prevBtn" class="pagination-btn">上一页</button>
                    <div id="pageNumbers"></div>
                    <button id="nextBtn" class="pagination-btn">下一页</button>
                    <div class="pagination-info" id="paginationInfo"></div>
                </div>
            </div>
            
            <!-- 空状态 -->
            <div id="emptyState" class="empty-state" style="display: none;">
                <p>暂无商品数据，请检查筛选条件或进行同步</p>
            </div>
            
            <!-- 同步状态模态框 -->
            <div id="syncModal" class="modal">
                <div class="modal-content">
                    <div class="modal-title">
                        <h3>商品数据同步</h3>
                    </div>
                    <div class="modal-body">
                        <div class="sync-progress">
                            <div id="syncProgressBar" class="sync-progress-fill" style="width: 0;"></div>
                        </div>
                        <div id="syncStatus" class="sync-status">正在准备同步...</div>
                    </div>
                    <div class="modal-footer">
                        <button id="closeSyncModal" class="btn-reset" disabled>关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 获取DOM元素
            const userName = document.getElementById('userName');
            const logoutBtn = document.getElementById('logoutBtn');
            
            // 显示用户信息
            const user = Auth.getCurrentUser();
            if (user) {
                userName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            logoutBtn.addEventListener('click', function() {
                Auth.logout();
            });
            
            // 修复菜单交互问题
            const menuItems = document.querySelectorAll('.menu-item a');
            menuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // 如果是当前页面，阻止默认行为
                    if (this.getAttribute('href') === location.pathname.split('/').pop()) {
                        e.preventDefault();
                        return;
                    }
                    
                    // 直接跳转，不需要其他处理
                    // 这里不阻止默认行为，让浏览器正常导航
                });
            });
            
            // 淘宝商品管理功能
            const TaobaoProductManager = {
                // 后端API路径
                apiPath: '../login_backend',
                
                // 当前分页信息
                pagination: {
                    current: 1,
                    pageSize: 20,
                    total: 0,
                    totalPages: 0
                },
                
                // 筛选条件
                filters: {
                    keyword: '',
                    materialId: '',
                    category: '',
                    minPrice: 0,
                    maxPrice: 0,
                    tpwdType: '',
                    couponOnly: false,
                    isRecommend: false
                },
                
                // 初始化
                init: function() {
                    this.bindEvents();
                    this.loadProducts();
                },
                
                // 绑定事件
                bindEvents: function() {
                    // 搜索按钮
                    document.getElementById('searchBtn').addEventListener('click', () => {
                        this.collectFilters();
                        this.pagination.current = 1; // 重置为第一页
                        this.loadProducts();
                    });
                    
                    // 重置按钮
                    document.getElementById('resetBtn').addEventListener('click', () => {
                        document.getElementById('keyword').value = '';
                        document.getElementById('material_id').value = '';
                        document.getElementById('category').value = '';
                        document.getElementById('min_price').value = '';
                        document.getElementById('max_price').value = '';
                        document.getElementById('tpwd_type').value = '';
                        document.getElementById('coupon_only').checked = false;
                        document.getElementById('is_recommend').checked = false;
                        
                        this.resetFilters();
                        this.loadProducts();
                    });
                    
                    // 同步按钮
                    document.getElementById('syncBtn').addEventListener('click', () => {
                        this.startSync();
                    });
                    
                    // 关闭同步模态框按钮
                    document.getElementById('closeSyncModal').addEventListener('click', () => {
                        document.getElementById('syncModal').style.display = 'none';
                    });
                    
                    // 上一页按钮
                    document.getElementById('prevBtn').addEventListener('click', () => {
                        if (this.pagination.current > 1) {
                            this.pagination.current--;
                            this.loadProducts();
                        }
                    });
                    
                    // 下一页按钮
                    document.getElementById('nextBtn').addEventListener('click', () => {
                        if (this.pagination.current < this.pagination.totalPages) {
                            this.pagination.current++;
                            this.loadProducts();
                        }
                    });
                },
                
                // 收集筛选条件
                collectFilters: function() {
                    this.filters.keyword = document.getElementById('keyword').value.trim();
                    this.filters.materialId = document.getElementById('material_id').value;
                    this.filters.category = document.getElementById('category').value;
                    this.filters.minPrice = parseFloat(document.getElementById('min_price').value) || 0;
                    this.filters.maxPrice = parseFloat(document.getElementById('max_price').value) || 0;
                    this.filters.tpwdType = document.getElementById('tpwd_type').value;
                    this.filters.couponOnly = document.getElementById('coupon_only').checked;
                    this.filters.isRecommend = document.getElementById('is_recommend').checked;
                },
                
                // 重置筛选条件
                resetFilters: function() {
                    this.filters.keyword = '';
                    this.filters.materialId = '';
                    this.filters.category = '';
                    this.filters.minPrice = 0;
                    this.filters.maxPrice = 0;
                    this.filters.tpwdType = '';
                    this.filters.couponOnly = false;
                    this.filters.isRecommend = false;
                    
                    this.pagination.current = 1;
                },
                
                // 加载商品数据
                loadProducts: function() {
                    this.showLoading(true);
                    this.showError('');
                    
                    const url = new URL(`${this.apiPath}/get_stored_taobao_products.php`, window.location.origin);
                    
                    // 添加筛选参数
                    url.searchParams.append('page', this.pagination.current);
                    url.searchParams.append('page_size', this.pagination.pageSize);
                    
                    if (this.filters.keyword) {
                        url.searchParams.append('keyword', this.filters.keyword);
                    }
                    
                    if (this.filters.materialId) {
                        url.searchParams.append('material_id', this.filters.materialId);
                    }
                    
                    if (this.filters.category) {
                        url.searchParams.append('category', this.filters.category);
                    }
                    
                    if (this.filters.minPrice > 0) {
                        url.searchParams.append('min_price', this.filters.minPrice);
                    }
                    
                    if (this.filters.maxPrice > 0) {
                        url.searchParams.append('max_price', this.filters.maxPrice);
                    }
                    
                    if (this.filters.tpwdType) {
                        url.searchParams.append('tpwd_type', this.filters.tpwdType);
                    }
                    
                    if (this.filters.couponOnly) {
                        url.searchParams.append('coupon_only', 1);
                    }
                    
                    if (this.filters.isRecommend) {
                        url.searchParams.append('is_recommend', 1);
                    }
                    
                    // 添加排序参数，默认按销量降序
                    url.searchParams.append('sort_field', 'volume');
                    url.searchParams.append('sort_order', 'desc');
                    
                    fetch(url, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${Auth.getToken()}`
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('获取商品数据失败，请检查网络连接');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.error) {
                            throw new Error(data.msg || '获取商品数据失败');
                        }
                        
                        this.renderProducts(data.data);
                        this.updatePagination(data.pagination);
                        this.loadFilterOptions(data.filters);
                        
                        this.showLoading(false);
                        
                        // 显示商品表格或空状态
                        document.getElementById('productTableContainer').style.display = 
                            data.data.length > 0 ? 'block' : 'none';
                        document.getElementById('emptyState').style.display = 
                            data.data.length > 0 ? 'none' : 'block';
                    })
                    .catch(error => {
                        this.showError(error.message);
                        this.showLoading(false);
                        document.getElementById('productTableContainer').style.display = 'none';
                        document.getElementById('emptyState').style.display = 'block';
                    });
                },
                
                // 渲染商品列表
                renderProducts: function(products) {
                    const tbody = document.getElementById('productList');
                    tbody.innerHTML = '';
                    
                    products.forEach(product => {
                        const row = document.createElement('tr');
                        
                        // 格式化价格信息
                        const originalPrice = parseFloat(product.original_price).toFixed(2);
                        const finalPrice = parseFloat(product.final_price).toFixed(2);
                        
                        // 格式化佣金信息
                        const commissionRate = (parseFloat(product.commission_rate) * 100).toFixed(1);
                        const commissionAmount = parseFloat(product.commission_amount).toFixed(2);
                        
                        // 格式化更新时间
                        const lastSyncDate = new Date(product.last_sync_time);
                        const formattedDate = `${lastSyncDate.getFullYear()}-${String(lastSyncDate.getMonth() + 1).padStart(2, '0')}-${String(lastSyncDate.getDate()).padStart(2, '0')}`;
                        
                        // 处理淘口令显示
                        const tpwd = product.tpwd || '';
                        const isFakeTpwd = product.is_fake_tpwd === 1;
                        let tpwdHtml = '';
                        
                        if (!tpwd) {
                            // 无淘口令
                            tpwdHtml = '<span class="tpwd-none">无淘口令</span>';
                        } else if (isFakeTpwd) {
                            // 模拟淘口令
                            tpwdHtml = `
                                <div class="tpwd-container">
                                    <span class="tpwd-text" title="${tpwd}">${tpwd}</span>
                                    <span class="tpwd-fake">(模拟)</span>
                                    <button class="btn-copy" data-tpwd="${tpwd}">复制</button>
                                </div>
                            `;
                        } else {
                            // 真实淘口令
                            tpwdHtml = `
                                <div class="tpwd-container">
                                    <span class="tpwd-text" title="${tpwd}">${tpwd}</span>
                                    <span class="tpwd-real">(真实)</span>
                                    <button class="btn-copy" data-tpwd="${tpwd}">复制</button>
                                </div>
                            `;
                        }
                        
                        // 处理标签显示
                        let tagsHtml = '<span class="tags-none">无标签</span>';
                        
                        if (product.tags) {
                            try {
                                const tagsData = JSON.parse(product.tags);
                                if (tagsData) {
                                    tagsHtml = '<div class="tags-container">';
                                    
                                    // 根据不同的商品分类，显示不同的标签内容
                                    if (product.category === '品牌女装' && tagsData['衣物标签']) {
                                        // 女装类商品显示衣物标签
                                        tagsHtml += '<ul class="tags-list">';
                                        tagsData['衣物标签'].slice(0, 3).forEach(tag => {
                                            tagsHtml += `<li class="tag-item">${tag}</li>`;
                                        });
                                        tagsHtml += '</ul>';
                                        if (tagsData['衣物类别']) {
                                            tagsHtml += `<div class="tag-category">${tagsData['衣物类别']}</div>`;
                                        }
                                    } else if (product.category === '鞋包配饰' && tagsData['商品标签']) {
                                        // 鞋包类商品显示商品标签
                                        tagsHtml += '<ul class="tags-list">';
                                        tagsData['商品标签'].slice(0, 3).forEach(tag => {
                                            tagsHtml += `<li class="tag-item">${tag}</li>`;
                                        });
                                        tagsHtml += '</ul>';
                                        if (tagsData['商品类别']) {
                                            tagsHtml += `<div class="tag-category">${tagsData['商品类别']}</div>`;
                                        }
                                    } else if (product.category === '美妆个护' && tagsData['商品标签']) {
                                        // 美妆类商品显示商品标签
                                        tagsHtml += '<ul class="tags-list">';
                                        tagsData['商品标签'].slice(0, 3).forEach(tag => {
                                            tagsHtml += `<li class="tag-item">${tag}</li>`;
                                        });
                                        tagsHtml += '</ul>';
                                        if (tagsData['商品类别']) {
                                            tagsHtml += `<div class="tag-category">${tagsData['商品类别']}</div>`;
                                        }
                                    } else if (tagsData['商品标签']) {
                                        // 其他类商品显示通用标签
                                        tagsHtml += '<ul class="tags-list">';
                                        tagsData['商品标签'].slice(0, 3).forEach(tag => {
                                            tagsHtml += `<li class="tag-item">${tag}</li>`;
                                        });
                                        tagsHtml += '</ul>';
                                        if (tagsData['商品类别']) {
                                            tagsHtml += `<div class="tag-category">${tagsData['商品类别']}</div>`;
                                        }
                                    } else {
                                        // 如果没有找到标准格式的标签，直接显示JSON内容的前30个字符
                                        const tagsStr = JSON.stringify(tagsData);
                                        tagsHtml = `<div class="tags-raw" title="${tagsStr}">${tagsStr.substring(0, 30)}...</div>`;
                                    }
                                    
                                    tagsHtml += '</div>';
                                }
                            } catch (e) {
                                console.error('解析标签JSON失败:', e);
                                tagsHtml = '<span class="tags-error">标签格式错误</span>';
                            }
                        }
                        
                        row.innerHTML = `
                            <td>
                                <img src="${product.image_url}" alt="${product.title}" class="product-image">
                            </td>
                            <td>
                                <div class="product-title" title="${product.title}">${product.title}</div>
                            </td>
                            <td>
                                <div class="price">¥${finalPrice}</div>
                                <div class="discount">原价: ¥${originalPrice}</div>
                            </td>
                            <td>${tagsHtml}</td>
                            <td>${commissionRate}%<br>(¥${commissionAmount})</td>
                            <td>${tpwdHtml}</td>
                            <td>${product.shop_title}</td>
                            <td>${product.category}</td>
                            <td>${formattedDate}</td>
                            <td>
                                <div class="action-btns">
                                    <a href="${product.item_url}" target="_blank" class="btn-view">查看</a>
                                    ${product.is_recommend == 1 ? 
                                        `<button class="btn-remove-recommend" data-id="${product.id}">取消推荐</button>` : 
                                        `<button class="btn-recommend" data-id="${product.id}">推荐</button>`}
                                </div>
                            </td>
                        `;
                        
                        // 添加推荐/取消推荐事件
                        const recommendBtn = row.querySelector('.btn-recommend');
                        if (recommendBtn) {
                            recommendBtn.addEventListener('click', () => {
                                this.setRecommendStatus(product.id, true);
                            });
                        }
                        
                        const removeRecommendBtn = row.querySelector('.btn-remove-recommend');
                        if (removeRecommendBtn) {
                            removeRecommendBtn.addEventListener('click', () => {
                                this.setRecommendStatus(product.id, false);
                            });
                        }
                        
                        // 添加复制淘口令事件
                        const copyBtn = row.querySelector('.btn-copy');
                        if (copyBtn) {
                            copyBtn.addEventListener('click', (e) => {
                                const tpwd = e.target.getAttribute('data-tpwd');
                                this.copyToClipboard(tpwd, e.target);
                            });
                        }
                        
                        tbody.appendChild(row);
                    });
                },
                
                // 更新分页控件
                updatePagination: function(pagination) {
                    this.pagination.current = pagination.current_page;
                    this.pagination.pageSize = pagination.page_size;
                    this.pagination.total = pagination.total;
                    this.pagination.totalPages = pagination.total_pages;
                    
                    const prevBtn = document.getElementById('prevBtn');
                    const nextBtn = document.getElementById('nextBtn');
                    const paginationInfo = document.getElementById('paginationInfo');
                    const pageNumbers = document.getElementById('pageNumbers');
                    
                    // 更新按钮状态
                    prevBtn.disabled = this.pagination.current <= 1;
                    nextBtn.disabled = this.pagination.current >= this.pagination.totalPages;
                    
                    // 更新分页信息
                    paginationInfo.textContent = `第 ${this.pagination.current} 页，共 ${this.pagination.totalPages} 页，共 ${this.pagination.total} 条记录`;
                    
                    // 生成页码按钮
                    pageNumbers.innerHTML = '';
                    
                    const maxPageButtons = 5; // 最多显示的页码按钮数
                    let startPage = Math.max(1, this.pagination.current - Math.floor(maxPageButtons / 2));
                    let endPage = Math.min(this.pagination.totalPages, startPage + maxPageButtons - 1);
                    
                    // 调整起始页确保显示足够的按钮
                    if (endPage - startPage + 1 < maxPageButtons) {
                        startPage = Math.max(1, endPage - maxPageButtons + 1);
                    }
                    
                    for (let i = startPage; i <= endPage; i++) {
                        const pageBtn = document.createElement('button');
                        pageBtn.className = `pagination-btn ${i === this.pagination.current ? 'pagination-active' : ''}`;
                        pageBtn.textContent = i;
                        pageBtn.addEventListener('click', () => {
                            if (i !== this.pagination.current) {
                                this.pagination.current = i;
                                this.loadProducts();
                            }
                        });
                        pageNumbers.appendChild(pageBtn);
                    }
                },
                
                // 加载筛选选项
                loadFilterOptions: function(filters) {
                    // 加载物料分类选项
                    if (filters && filters.materials) {
                        const materialSelect = document.getElementById('material_id');
                        const currentValue = materialSelect.value;
                        
                        // 保留第一个选项(全部)
                        materialSelect.innerHTML = '<option value="">全部</option>';
                        
                        filters.materials.forEach(material => {
                            const option = document.createElement('option');
                            option.value = material.material_id;
                            option.textContent = `${material.name} (${material.count})`;
                            materialSelect.appendChild(option);
                        });
                        
                        // 恢复选择的值
                        materialSelect.value = currentValue;
                    }
                    
                    // 加载商品分类选项
                    if (filters && filters.categories) {
                        const categorySelect = document.getElementById('category');
                        const currentValue = categorySelect.value;
                        
                        // 保留第一个选项(全部)
                        categorySelect.innerHTML = '<option value="">全部</option>';
                        
                        filters.categories.forEach(category => {
                            const option = document.createElement('option');
                            option.value = category;
                            option.textContent = category;
                            categorySelect.appendChild(option);
                        });
                        
                        // 恢复选择的值
                        categorySelect.value = currentValue;
                    }
                    
                    // 加载淘口令类型选项
                    if (filters && filters.tpwd_types) {
                        const tpwdTypeSelect = document.getElementById('tpwd_type');
                        const currentValue = tpwdTypeSelect.value;
                        
                        // 保留第一个选项(全部)
                        tpwdTypeSelect.innerHTML = '<option value="">全部</option>';
                        
                        filters.tpwd_types.forEach(tpwdType => {
                            const option = document.createElement('option');
                            option.value = tpwdType.type;
                            option.textContent = `${tpwdType.name} (${tpwdType.count})`;
                            tpwdTypeSelect.appendChild(option);
                        });
                        
                        // 恢复选择的值
                        tpwdTypeSelect.value = currentValue;
                    }
                },
                
                // 开始同步
                startSync: function() {
                    const modal = document.getElementById('syncModal');
                    const progressBar = document.getElementById('syncProgressBar');
                    const statusText = document.getElementById('syncStatus');
                    const closeBtn = document.getElementById('closeSyncModal');
                    
                    modal.style.display = 'block';
                    progressBar.style.width = '0%';
                    statusText.textContent = '正在执行同步，请稍候...';
                    closeBtn.disabled = true;
                    
                    // 调用后端API执行同步
                    fetch(`${this.apiPath}/trigger_taobao_sync.php`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${Auth.getToken()}`
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            statusText.textContent = `同步失败: ${data.msg}`;
                            progressBar.style.width = '100%';
                            progressBar.style.backgroundColor = '#ff4d4f';
                            closeBtn.disabled = false;
                        } else {
                            // 同步开始，显示进度
                            statusText.textContent = `同步任务已启动，PID: ${data.pid}，开始时间: ${data.start_time}`;
                            
                            // 使用模拟进度条，因为实际同步是后台异步执行的
                            let progress = 0;
                            const interval = setInterval(() => {
                                progress += 5;
                                progressBar.style.width = `${progress}%`;
                                
                                if (progress >= 100) {
                                    clearInterval(interval);
                                    statusText.textContent = '同步请求已完成！实际同步可能需要更长时间在后台继续执行。';
                                    closeBtn.disabled = false;
                                    
                                    // 重新加载商品数据
                                    setTimeout(() => {
                                        this.loadProducts();
                                    }, 1000);
                                }
                            }, 500);
                        }
                    })
                    .catch(error => {
                        statusText.textContent = `同步请求出错: ${error.message}`;
                        progressBar.style.width = '100%';
                        progressBar.style.backgroundColor = '#ff4d4f';
                        closeBtn.disabled = false;
                    });
                },
                
                // 设置商品推荐状态
                setRecommendStatus: function(productId, isRecommend) {
                    // 先显示加载状态或禁用按钮
                    const buttons = document.querySelectorAll(`button[data-id="${productId}"]`);
                    buttons.forEach(btn => {
                        btn.disabled = true;
                        btn.textContent = '处理中...';
                    });
                    
                    // 调用后端API设置商品推荐状态
                    fetch(`${this.apiPath}/set_product_recommend.php`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${Auth.getToken()}`
                        },
                        body: JSON.stringify({
                            product_id: productId,
                            is_recommend: isRecommend ? 1 : 0
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            this.showError(data.msg);
                            
                            // 恢复按钮状态
                            buttons.forEach(btn => {
                                btn.disabled = false;
                                btn.textContent = isRecommend ? '推荐' : '取消推荐';
                            });
                        } else {
                            // 显示成功消息
                            this.showError(''); // 清除错误消息
                            
                            // 刷新产品列表
                            this.loadProducts();
                        }
                    })
                    .catch(error => {
                        this.showError(`设置推荐状态失败: ${error.message}`);
                        
                        // 恢复按钮状态
                        buttons.forEach(btn => {
                            btn.disabled = false;
                            btn.textContent = isRecommend ? '推荐' : '取消推荐';
                        });
                    });
                },
                
                // 显示/隐藏加载状态
                showLoading: function(isLoading) {
                    document.getElementById('loading').style.display = isLoading ? 'block' : 'none';
                },
                
                // 显示错误信息
                showError: function(message) {
                    const errorElement = document.getElementById('errorMessage');
                    errorElement.textContent = message;
                    errorElement.style.display = message ? 'block' : 'none';
                    
                    // 如果是成功消息，3秒后自动隐藏
                    if (message && !message.includes('失败') && !message.includes('错误')) {
                        setTimeout(() => {
                            errorElement.style.display = 'none';
                        }, 3000);
                    }
                },
                
                // 复制淘口令到剪贴板
                copyToClipboard: function(text, button) {
                    // 保存原始按钮文本
                    const originalText = button.textContent;
                    
                    try {
                        // 现代浏览器API
                        if (navigator.clipboard && window.isSecureContext) {
                            navigator.clipboard.writeText(text).then(() => {
                                button.textContent = '已复制';
                                button.style.backgroundColor = '#52c41a';
                                
                                // 3秒后恢复按钮状态
                                setTimeout(() => {
                                    button.textContent = originalText;
                                    button.style.backgroundColor = '';
                                }, 3000);
                            });
                        } else {
                            // 回退方法
                            const textarea = document.createElement('textarea');
                            textarea.value = text;
                            textarea.style.position = 'fixed';
                            textarea.style.opacity = '0';
                            document.body.appendChild(textarea);
                            textarea.select();
                            
                            const successful = document.execCommand('copy');
                            document.body.removeChild(textarea);
                            
                            if (successful) {
                                button.textContent = '已复制';
                                button.style.backgroundColor = '#52c41a';
                                
                                // 3秒后恢复按钮状态
                                setTimeout(() => {
                                    button.textContent = originalText;
                                    button.style.backgroundColor = '';
                                }, 3000);
                            } else {
                                throw new Error('复制失败');
                            }
                        }
                    } catch (err) {
                        console.error('复制淘口令失败:', err);
                        this.showError('复制淘口令失败，请手动复制');
                    }
                }
            };
            
            // 初始化淘宝商品管理
            TaobaoProductManager.init();
        });
    </script>
</body>
</html> 