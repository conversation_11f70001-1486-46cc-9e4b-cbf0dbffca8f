<?php
/**
 * 调试面容分析次数检查
 * 用于排查为什么用户直接跳到上传页面
 */

require_once 'login_backend/db.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "=== 面容分析数据调试 ===\n\n";
    
    // 1. 检查表结构
    echo "1. 检查face_analysis表结构:\n";
    $stmt = $conn->prepare("DESCRIBE face_analysis");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasPaymentStatus = false;
    $hasUsageStatus = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'payment_status') {
            $hasPaymentStatus = true;
        }
        if ($column['Field'] === 'usage_status') {
            $hasUsageStatus = true;
        }
        echo "- {$column['Field']}: {$column['Type']}\n";
    }
    
    echo "\n支付字段检查:\n";
    echo "- payment_status: " . ($hasPaymentStatus ? "✅ 存在" : "❌ 缺失") . "\n";
    echo "- usage_status: " . ($hasUsageStatus ? "✅ 存在" : "❌ 缺失") . "\n\n";
    
    // 2. 检查数据统计
    echo "2. 数据统计:\n";
    $stmt = $conn->prepare("
        SELECT 
            payment_status,
            usage_status,
            COUNT(*) as count,
            COUNT(CASE WHEN analysis_result IS NOT NULL AND analysis_result != '' THEN 1 END) as has_result_count
        FROM face_analysis 
        GROUP BY payment_status, usage_status
        ORDER BY payment_status, usage_status
    ");
    $stmt->execute();
    $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($stats as $stat) {
        echo "- {$stat['payment_status']}/{$stat['usage_status']}: {$stat['count']}条记录 (有结果: {$stat['has_result_count']})\n";
    }
    
    // 3. 检查最近的记录
    echo "\n3. 最近10条记录:\n";
    $stmt = $conn->prepare("
        SELECT 
            id, 
            user_id, 
            payment_status, 
            usage_status, 
            CASE WHEN analysis_result IS NOT NULL AND analysis_result != '' THEN 'YES' ELSE 'NO' END as has_result,
            created_at
        FROM face_analysis 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $recent = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($recent as $record) {
        echo "- ID:{$record['id']} 用户:{$record['user_id']} 支付:{$record['payment_status']} 使用:{$record['usage_status']} 有结果:{$record['has_result']} 时间:{$record['created_at']}\n";
    }
    
    // 4. 模拟检查用户3的可用次数（根据日志中的用户ID）
    echo "\n4. 检查用户3的可用次数:\n";
    $userId = 3;
    $stmt = $conn->prepare("
        SELECT id, payment_status, usage_status, 
               CASE WHEN analysis_result IS NOT NULL AND analysis_result != '' THEN 'YES' ELSE 'NO' END as has_result
        FROM face_analysis 
        WHERE user_id = :user_id 
        AND payment_status = 'paid' 
        AND usage_status = 'unused'
        AND (analysis_result IS NULL OR analysis_result = '')
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $userQuota = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($userQuota)) {
        echo "- 用户3没有可用的分析次数\n";
    } else {
        echo "- 用户3有 " . count($userQuota) . " 个可用次数:\n";
        foreach ($userQuota as $quota) {
            echo "  - ID:{$quota['id']} 支付:{$quota['payment_status']} 使用:{$quota['usage_status']} 有结果:{$quota['has_result']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
