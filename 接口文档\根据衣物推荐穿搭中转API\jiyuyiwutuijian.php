<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 检查请求数据
$json = file_get_contents('php://input');
if (empty($json)) {
    echo json_encode(['error' => '缺少请求数据']);
    exit;
}

$data = json_decode($json, true);
if (!$data) {
    echo json_encode(['error' => '无效的JSON数据']);
    exit;
}

// 验证必要参数
if (!isset($data['base_clothing']) || !isset($data['other_clothes'])) {
    echo json_encode(['error' => '缺少必要参数']);
    exit;
}

// 提取基础衣物信息
$base_clothing = $data['base_clothing'];
$other_clothes = $data['other_clothes'];

// 配置Gemini API密钥
// 注意：实际部署时，应使用环境变量或安全配置存储API密钥
$apiKey = 'AIzaSyD1-g64EwoKNcvs0LeAn9hbyHJRuKj0Slg'; // 实际API密钥
$model = 'gemini-1.5-flash'; // 使用的模型名称

// 根据衣物分类，整理其他衣物
$clothes_by_category = [];
foreach ($other_clothes as $clothing) {
    if (!isset($clothes_by_category[$clothing['category']])) {
        $clothes_by_category[$clothing['category']] = [];
    }
    $clothes_by_category[$clothing['category']][] = $clothing;
}

// 准备基础衣物信息
$base_clothing_info = [
    'id' => $base_clothing['id'],
    'name' => $base_clothing['name'],
    'category' => $base_clothing['category'],
    'tags' => $base_clothing['tags'],
    'image_url' => $base_clothing['image_url']
];

// 添加描述信息（如果存在）
if (isset($base_clothing['description'])) {
    if (isset($base_clothing['description']['颜色'])) {
        $base_clothing_info['color'] = $base_clothing['description']['颜色'];
    }
    if (isset($base_clothing['description']['品牌'])) {
        $base_clothing_info['brand'] = $base_clothing['description']['品牌'];
    }
    if (isset($base_clothing['description']['备注'])) {
        $base_clothing_info['remarks'] = $base_clothing['description']['备注'];
    }
}

// 构建提示词
$prompt = "你是一位专业的穿搭顾问，我想请你根据我的一件衣物创建一套完整的穿搭搭配。\n\n";
$prompt .= "基础衣物信息：\n";
$prompt .= "- 名称：" . $base_clothing_info['name'] . "\n";
$prompt .= "- 分类：" . translateCategory($base_clothing_info['category']) . "\n";

if (isset($base_clothing_info['color'])) {
    $prompt .= "- 颜色：" . $base_clothing_info['color'] . "\n";
}
if (isset($base_clothing_info['brand'])) {
    $prompt .= "- 品牌：" . $base_clothing_info['brand'] . "\n";
}
if (isset($base_clothing_info['remarks'])) {
    $prompt .= "- 备注：" . $base_clothing_info['remarks'] . "\n";
}
if (!empty($base_clothing_info['tags'])) {
    $prompt .= "- 标签：" . $base_clothing_info['tags'] . "\n";
}

$prompt .= "\n我的衣橱中还有以下分类的衣物：\n";

// 添加其他衣物信息
foreach ($clothes_by_category as $category => $clothes) {
    $prompt .= "\n" . translateCategory($category) . "（共" . count($clothes) . "件）：\n";
    
    // 最多列出每个分类的5件衣物作为参考
    $count = 0;
    foreach ($clothes as $cloth) {
        if ($count >= 5) break;
        
        $prompt .= "- " . $cloth['name'];
        
        if (isset($cloth['description']['颜色'])) {
            $prompt .= "（颜色：" . $cloth['description']['颜色'] . "）";
        }
        
        if (!empty($cloth['tags'])) {
            $prompt .= "（标签：" . $cloth['tags'] . "）";
        }
        
        $prompt .= "\n";
        $count++;
    }
}

$prompt .= "\n请根据我的基础衣物，从我衣橱的其他衣物中搭配一套完整的穿搭。请确保搭配合适且时尚，并考虑颜色的协调性。";
$prompt .= "\n请以JSON格式返回结果，格式如下：";
$prompt .= "\n{";
$prompt .= "\n  \"top\": {\"id\": 数字, \"name\": \"名称\", \"category\": \"类别\", \"image_url\": \"图片URL\", \"reason\": \"推荐理由\"},";
$prompt .= "\n  \"bottom\": {\"id\": 数字, \"name\": \"名称\", \"category\": \"类别\", \"image_url\": \"图片URL\", \"reason\": \"推荐理由\"},";
$prompt .= "\n  \"outerwear\": {\"id\": 数字, \"name\": \"名称\", \"category\": \"类别\", \"image_url\": \"图片URL\", \"reason\": \"推荐理由\"},";
$prompt .= "\n  \"shoes\": {\"id\": 数字, \"name\": \"名称\", \"category\": \"类别\", \"image_url\": \"图片URL\", \"reason\": \"推荐理由\"},";
$prompt .= "\n  \"accessories\": {\"id\": 数字, \"name\": \"名称\", \"category\": \"类别\", \"image_url\": \"图片URL\", \"reason\": \"推荐理由\"},";
$prompt .= "\n  \"bag\": {\"id\": 数字, \"name\": \"名称\", \"category\": \"类别\", \"image_url\": \"图片URL\", \"reason\": \"推荐理由\"},";
$prompt .= "\n  \"outfit_summary\": \"整体穿搭风格描述和建议\"";
$prompt .= "\n}";
$prompt .= "\n如果某一类别没有合适的衣物或者不需要搭配，可以省略该类别。";
$prompt .= "\n请从我提供的衣物中进行选择，不要编造不存在的衣物。";

// 调用Gemini API
$geminiUrl = "https://generativelanguage.googleapis.com/v1beta/models/" . $model . ":generateContent?key=" . $apiKey;
$request = [
    "contents" => [
        [
            "parts" => [
                [
                    "text" => $prompt
                ]
            ]
        ]
    ],
    "generationConfig" => [
        "temperature" => 0.2,
        "topP" => 0.8,
        "topK" => 40,
        "maxOutputTokens" => 1024
    ]
];

// 发送请求到Gemini API
$ch = curl_init($geminiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

$response = curl_exec($ch);
if (curl_errno($ch)) {
    echo json_encode(['error' => '调用Gemini API失败: ' . curl_error($ch)]);
    exit;
}
curl_close($ch);

// 解析Gemini API响应
$result = json_decode($response, true);

// 验证响应是否有效
if (!$result || !isset($result['candidates'][0]['content']['parts'][0]['text'])) {
    echo json_encode(['error' => 'Gemini API响应无效']);
    exit;
}

// 提取Gemini生成的JSON内容
$generatedText = $result['candidates'][0]['content']['parts'][0]['text'];

// 从文本中提取JSON部分
preg_match('/\{.*\}/s', $generatedText, $matches);
if (empty($matches)) {
    echo json_encode(['error' => '无法从Gemini响应中解析JSON']);
    exit;
}

$outfitJson = $matches[0];
$outfit = json_decode($outfitJson, true);

if (!$outfit) {
    echo json_encode(['error' => '解析推荐结果JSON失败']);
    exit;
}

// 添加基础衣物到结果中
if ($base_clothing['category'] === 'tops') {
    $outfit['top'] = array_merge($base_clothing, ['reason' => '您选择的基础衣物']);
} elseif ($base_clothing['category'] === 'pants' || $base_clothing['category'] === 'skirts') {
    $outfit['bottom'] = array_merge($base_clothing, ['reason' => '您选择的基础衣物']);
} elseif ($base_clothing['category'] === 'coats') {
    $outfit['outerwear'] = array_merge($base_clothing, ['reason' => '您选择的基础衣物']);
} elseif ($base_clothing['category'] === 'shoes') {
    $outfit['shoes'] = array_merge($base_clothing, ['reason' => '您选择的基础衣物']);
} elseif ($base_clothing['category'] === 'accessories') {
    $outfit['accessories'] = array_merge($base_clothing, ['reason' => '您选择的基础衣物']);
} elseif ($base_clothing['category'] === 'bags') {
    $outfit['bag'] = array_merge($base_clothing, ['reason' => '您选择的基础衣物']);
}

// 返回最终结果
echo json_encode($outfit);

// 辅助函数：将分类英文名转换为中文名
function translateCategory($category) {
    $categoryMap = [
        'tops' => '上衣',
        'pants' => '裤子',
        'skirts' => '裙子',
        'coats' => '外套',
        'shoes' => '鞋子',
        'bags' => '包包',
        'accessories' => '配饰'
    ];
    
    return isset($categoryMap[$category]) ? $categoryMap[$category] : $category;
}
?> 