<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑公告 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-textarea {
            min-height: 150px;
            resize: vertical;
        }
        
        .form-input:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-input[readonly],
        .form-textarea[readonly] {
            background-color: #f5f5f5;
            cursor: not-allowed;
        }
        
        .btn-container {
            display: flex;
            justify-content: flex-end;
            margin-top: 30px;
            gap: 15px;
        }
        
        .btn {
            padding: 0 20px;
            height: 36px;
            line-height: 36px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
        }
        
        .btn-default {
            background-color: #f0f0f0;
            color: rgba(0, 0, 0, 0.65);
        }
        
        .btn-default:hover {
            background-color: #e0e0e0;
        }
        
        .form-hint {
            margin-top: 5px;
            font-size: 12px;
            color: #888;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-col {
            flex: 1;
        }
        
        .alert {
            padding: 10px 15px;
            margin-bottom: 15px;
            border-radius: 4px;
            display: none;
        }
        
        .alert-success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        
        .alert-error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #f5222d;
        }
        
        /* 状态开关 */
        .status-toggle {
            position: relative;
            display: inline-block;
            width: 52px;
            height: 28px;
            vertical-align: middle;
        }
        
        .status-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            border-radius: 34px;
            transition: .4s;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 22px;
            width: 22px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            border-radius: 50%;
            transition: .4s;
        }
        
        input:checked + .toggle-slider {
            background-color: #1890ff;
        }
        
        input:focus + .toggle-slider {
            box-shadow: 0 0 1px #1890ff;
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(24px);
        }
        
        .status-label {
            margin-left: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2 id="pageTitle">添加公告</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="card">
                <div id="successAlert" class="alert alert-success">
                    公告已成功保存！
                </div>
                
                <div id="errorAlert" class="alert alert-error">
                    保存失败：<span id="errorMessage"></span>
                </div>
                
                <form id="announcementForm">
                    <input type="hidden" id="announcementId" name="id">
                    
                    <div class="form-group">
                        <label class="form-label" for="title">公告标题：</label>
                        <input type="text" id="title" name="title" class="form-input" required maxlength="100" placeholder="请输入公告标题">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="content">公告内容：</label>
                        <textarea id="content" name="content" class="form-input form-textarea" required placeholder="请输入公告内容，支持换行和缩进，将保留格式显示" style="min-height: 200px; line-height: 1.6;"></textarea>
                        <div class="form-hint">支持换行和缩进，编辑的格式将在小程序公告弹窗中保留</div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="startTime">开始时间：</label>
                                <input type="datetime-local" id="startTime" name="start_time" class="form-input" required>
                                <div class="form-hint">公告开始生效的时间</div>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="endTime">结束时间：</label>
                                <input type="datetime-local" id="endTime" name="end_time" class="form-input" required>
                                <div class="form-hint">公告结束生效的时间</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">状态：</label>
                        <div>
                            <label class="status-toggle">
                                <input type="checkbox" id="status" name="status" checked>
                                <span class="toggle-slider"></span>
                            </label>
                            <span class="status-label" id="statusLabel">启用</span>
                        </div>
                        <div class="form-hint">启用后，公告将在指定时间内向用户显示</div>
                    </div>
                    
                    <div class="btn-container">
                        <a href="announcement_list.html" class="btn btn-default">返回列表</a>
                        <button type="submit" id="saveBtn" class="btn btn-primary">保存公告</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化侧边栏，设置当前活动项为announcement
            Sidebar.init('announcement');
            
            // 获取DOM元素
            const announcementForm = document.getElementById('announcementForm');
            const pageTitle = document.getElementById('pageTitle');
            const announcementId = document.getElementById('announcementId');
            const titleInput = document.getElementById('title');
            const contentInput = document.getElementById('content');
            const startTimeInput = document.getElementById('startTime');
            const endTimeInput = document.getElementById('endTime');
            const statusCheckbox = document.getElementById('status');
            const statusLabel = document.getElementById('statusLabel');
            const saveBtn = document.getElementById('saveBtn');
            const successAlert = document.getElementById('successAlert');
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');
            
            // 获取管理员信息
            const adminName = document.getElementById('adminName');
            const logoutBtn = document.getElementById('logoutBtn');
            
            // 从localStorage获取token
            const token = localStorage.getItem('admin_token');
            if (!token) {
                window.location.href = 'index.html';
                return;
            }
            
            // 设置管理员名称
            const adminInfo = JSON.parse(localStorage.getItem('admin_info')) || {};
            adminName.textContent = adminInfo.username || '管理员';
            
            // 注销功能
            logoutBtn.addEventListener('click', function() {
                localStorage.removeItem('admin_token');
                localStorage.removeItem('admin_info');
                window.location.href = 'index.html';
            });
            
            // 解析URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const id = urlParams.get('id');
            const isViewMode = urlParams.get('view') === '1';
            
            // 设置当前时间为默认值
            const now = new Date();
            const tomorrow = new Date();
            tomorrow.setDate(now.getDate() + 7);
            
            startTimeInput.value = formatDateTimeForInput(now);
            endTimeInput.value = formatDateTimeForInput(tomorrow);
            
            // 如果有ID，加载公告详情
            if (id) {
                pageTitle.textContent = isViewMode ? '查看公告' : '编辑公告';
                loadAnnouncementDetails(id);
                
                // 如果是查看模式，禁用所有输入
                if (isViewMode) {
                    titleInput.readOnly = true;
                    contentInput.readOnly = true;
                    startTimeInput.readOnly = true;
                    endTimeInput.readOnly = true;
                    statusCheckbox.disabled = true;
                    saveBtn.style.display = 'none';
                }
            }
            
            // 状态开关事件
            statusCheckbox.addEventListener('change', function() {
                statusLabel.textContent = this.checked ? '启用' : '禁用';
            });
            
            // 表单提交
            announcementForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // 表单验证
                if (!validateForm()) {
                    return;
                }
                
                // 收集表单数据
                const formData = {
                    title: titleInput.value.trim(),
                    content: contentInput.value.trim(),
                    start_time: startTimeInput.value.replace('T', ' ') + ':00',
                    end_time: endTimeInput.value.replace('T', ' ') + ':00',
                    status: statusCheckbox.checked ? 1 : 0
                };
                
                // 如果是编辑模式，添加ID
                if (id) {
                    formData.id = id;
                }
                
                // 保存公告
                saveAnnouncement(formData);
            });
            
            // 加载公告详情
            function loadAnnouncementDetails(id) {
                fetch(`../login_backend/get_announcement_detail.php?id=${id}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': token
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        throw new Error(data.msg || '获取公告详情失败');
                    }
                    
                    const announcement = data.data;
                    if (!announcement) {
                        throw new Error('未找到公告');
                    }
                    
                    // 填充表单
                    announcementId.value = announcement.id;
                    titleInput.value = announcement.title;
                    contentInput.value = announcement.content;
                    
                    // 处理日期时间格式
                    const startTime = new Date(announcement.start_time);
                    const endTime = new Date(announcement.end_time);
                    
                    startTimeInput.value = formatDateTimeForInput(startTime);
                    endTimeInput.value = formatDateTimeForInput(endTime);
                    
                    // 设置状态
                    statusCheckbox.checked = parseInt(announcement.status) === 1;
                    statusLabel.textContent = statusCheckbox.checked ? '启用' : '禁用';
                })
                .catch(err => {
                    console.error('获取公告详情失败:', err);
                    showError(err.message);
                });
            }
            
            // 保存公告
            function saveAnnouncement(formData) {
                fetch('../login_backend/admin_save_announcement.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        throw new Error(data.msg || '保存失败');
                    }
                    
                    showSuccess();
                    
                    // 如果是添加模式，清空表单
                    if (!id) {
                        resetForm();
                    } else {
                        // 如果是编辑模式，更新ID（虽然不应该变）
                        announcementId.value = data.data.id;
                    }
                    
                    // 2秒后返回列表
                    setTimeout(function() {
                        window.location.href = 'announcement_list.html';
                    }, 2000);
                })
                .catch(err => {
                    console.error('保存失败:', err);
                    showError(err.message);
                });
            }
            
            // 重置表单
            function resetForm() {
                announcementForm.reset();
                announcementId.value = '';
                
                // 重置日期时间
                const now = new Date();
                const tomorrow = new Date();
                tomorrow.setDate(now.getDate() + 7);
                
                startTimeInput.value = formatDateTimeForInput(now);
                endTimeInput.value = formatDateTimeForInput(tomorrow);
                
                // 重置状态
                statusCheckbox.checked = true;
                statusLabel.textContent = '启用';
            }
            
            // 表单验证
            function validateForm() {
                // 标题验证
                if (!titleInput.value.trim()) {
                    showError('请输入公告标题');
                    titleInput.focus();
                    return false;
                }
                
                // 内容验证
                if (!contentInput.value.trim()) {
                    showError('请输入公告内容');
                    contentInput.focus();
                    return false;
                }
                
                // 时间验证
                const startTime = new Date(startTimeInput.value);
                const endTime = new Date(endTimeInput.value);
                
                if (isNaN(startTime.getTime())) {
                    showError('请输入有效的开始时间');
                    startTimeInput.focus();
                    return false;
                }
                
                if (isNaN(endTime.getTime())) {
                    showError('请输入有效的结束时间');
                    endTimeInput.focus();
                    return false;
                }
                
                if (endTime <= startTime) {
                    showError('结束时间必须晚于开始时间');
                    endTimeInput.focus();
                    return false;
                }
                
                return true;
            }
            
            // 显示成功提示
            function showSuccess() {
                successAlert.style.display = 'block';
                errorAlert.style.display = 'none';
                
                // 3秒后自动隐藏
                setTimeout(function() {
                    successAlert.style.display = 'none';
                }, 3000);
            }
            
            // 显示错误提示
            function showError(message) {
                errorMessage.textContent = message;
                errorAlert.style.display = 'block';
                successAlert.style.display = 'none';
                
                // 3秒后自动隐藏
                setTimeout(function() {
                    errorAlert.style.display = 'none';
                }, 3000);
            }
            
            // 格式化日期时间为input需要的格式
            function formatDateTimeForInput(date) {
                const year = date.getFullYear();
                const month = padZero(date.getMonth() + 1);
                const day = padZero(date.getDate());
                const hours = padZero(date.getHours());
                const minutes = padZero(date.getMinutes());
                
                return `${year}-${month}-${day}T${hours}:${minutes}`;
            }
            
            // 补零函数
            function padZero(num) {
                return num < 10 ? '0' + num : num;
            }
        });
    </script>
</body>
</html> 