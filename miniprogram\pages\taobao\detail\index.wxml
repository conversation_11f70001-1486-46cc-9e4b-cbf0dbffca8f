<!-- pages/taobao/detail/index.wxml -->
<view class="container" wx:if="{{product && !error}}">
  
  <!-- 商品轮播图 -->
  <view class="swiper-container">
    <swiper class="product-swiper" 
            autoplay="{{true}}" 
            interval="4000" 
            duration="500"
            bindchange="onSwiperChange"
            circular="{{true}}">
      <!-- 主图 -->
      <swiper-item bindtap="previewImage">
        <image src="{{product.image_url}}" mode="aspectFill" class="slide-image" lazy-load="true"></image>
      </swiper-item>
      
      <!-- 小图集 -->
      <block wx:if="{{product.small_images && product.small_images.length > 0}}">
        <swiper-item wx:for="{{product.small_images}}" wx:key="*this" bindtap="previewImage">
          <image src="{{item}}" mode="aspectFill" class="slide-image" lazy-load="true"></image>
        </swiper-item>
      </block>
    </swiper>
    
    <!-- 轮播指示器 -->
    <view class="indicator">
      <text>{{currentImageIndex + 1}}/{{(product.small_images && product.small_images.length ? product.small_images.length : 0) + 1}}</text>
    </view>
  </view>
  
  <!-- 价格信息 -->
  <view class="price-section">
    <view class="price-row">
      <view class="final-price">¥{{product.final_price}}</view>
      <view wx:if="{{product.original_price > product.final_price}}" class="original-price">¥{{product.original_price}}</view>
    </view>
    
    <!-- 优惠券信息 -->
    <view wx:if="{{product.coupon_amount > 0}}" class="coupon-info">
      <view class="coupon-tag">优惠券</view>
      <view class="coupon-value">{{product.coupon_amount}}元</view>
    </view>
  </view>
  
  <!-- 商品标题 -->
  <view class="product-title">{{product.title}}</view>
  
  <!-- 店铺信息 -->
  <view class="shop-info">
    <view class="shop-name">{{product.shop_title}}</view>
  </view>
  
  <!-- 商品说明 -->
  <view class="product-description">
    <view class="section-title">商品详情</view>
    <view class="description-content">
      <text>该商品来自淘宝，点击下方按钮可以获取购买链接，复制后打开淘宝APP即可购买。</text>
      <view wx:if="{{product.coupon_amount > 0}}" class="discount-tip">
        该商品有优惠券可用，下单可节省 ¥{{product.coupon_amount}} 元
      </view>
    </view>
  </view>
  
  <!-- 底部固定按钮 -->
  <view class="bottom-bar">
    <view class="price-display">
      <view class="price-label">券后价</view>
      <view class="bottom-price">¥{{product.final_price}}</view>
    </view>
    <view class="action-btn" bindtap="copyPromotionUrl">复制淘口令</view>
  </view>
</view>

<!-- 错误状态 -->
<view class="error-container" wx:if="{{error}}">
  <view class="error-icon"></view>
  <view class="error-text">{{errorMsg || '加载失败'}}</view>
  <view class="error-actions">
    <view class="error-btn retry" bindtap="retry">重新加载</view>
    <view class="error-btn back" bindtap="goBack">返回列表</view>
  </view>
</view>

<!-- 加载中状态 -->
<view class="loading" wx:if="{{loading}}">
  <view class="loading-spinner"></view>
  <view class="loading-text">加载中...</view>
</view> 