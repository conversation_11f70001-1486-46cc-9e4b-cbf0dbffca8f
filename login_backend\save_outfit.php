<?php
// 引入必要的文件
require_once 'auth.php';
require_once 'db.php';
require_once 'config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 验证token获取用户ID
$auth = new Auth();

// 获取token
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 如果有Bearer前缀，去掉它
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
}

if (!$token) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供授权Token'
    ]);
    exit;
}

// 使用Auth类验证token
$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'error' => true,
        'msg' => '未授权，请先登录'
    ]);
    exit;
}

$userId = $payload['sub']; // 从payload中获取用户ID

// 处理POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => '不支持的请求方法'
    ]);
    exit;
}

// 获取请求的JSON数据
$data = json_decode(file_get_contents('php://input'), true);
if (!$data) {
    echo json_encode([
        'error' => true,
        'msg' => '无效的请求数据'
    ]);
    exit;
}

// 验证必填字段
if (!isset($data['name']) || empty(trim($data['name']))) {
    echo json_encode([
        'error' => true,
        'msg' => '穿搭名称不能为空'
    ]);
    exit;
}

// 验证items数组格式
if (!isset($data['items']) || !is_array($data['items'])) {
    $data['items'] = []; // 如果没有提供或格式不正确，设为空数组
}

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();
    
    // 准备穿搭数据
    $name = trim($data['name']);
    $description = isset($data['description']) ? trim($data['description']) : null;
    $thumbnailUrl = isset($data['thumbnail']) ? $data['thumbnail'] : null;
    $categoryId = isset($data['category_id']) ? intval($data['category_id']) : null;
    
    // 验证分类ID是否存在且属于当前用户
    if ($categoryId) {
        $stmt = $conn->prepare("SELECT id FROM outfit_categories WHERE id = :id AND user_id = :user_id");
        $stmt->execute([
            'id' => $categoryId,
            'user_id' => $userId
        ]);
        
        if ($stmt->rowCount() === 0) {
            // 分类不存在或不属于当前用户，使用默认分类
            $categoryId = null;
        }
    }
    
    // 如果没有指定分类或分类无效，尝试获取默认分类
    if (!$categoryId) {
        $stmt = $conn->prepare("SELECT id FROM outfit_categories WHERE user_id = :user_id AND is_default = 1");
        $stmt->execute(['user_id' => $userId]);
        $defaultCategory = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($defaultCategory) {
            $categoryId = $defaultCategory['id'];
        } else {
            // 如果没有默认分类，创建一个
            $stmt = $conn->prepare("
                INSERT INTO outfit_categories (user_id, name, description, is_default, created_at)
                VALUES (:user_id, '默认分类', '自动创建的默认分类', 1, NOW())
            ");
            $stmt->execute(['user_id' => $userId]);
            $categoryId = $conn->lastInsertId();
        }
    }
    
    // 构建outfit_data
    $outfitData = [
        'items' => $data['items']
    ];
    
    // 将outfit_data转为JSON
    $outfitDataJson = json_encode($outfitData);
    
    // 检查是否为更新操作
    $isUpdate = false;
    $outfitId = null;
    
    if (isset($data['id']) && !empty($data['id'])) {
        $outfitId = $data['id'];

        // 修复：检查穿搭是否存在，并验证编辑权限
        $checkStmt = $conn->prepare("SELECT id, user_id, circle_id FROM outfits WHERE id = :id");
        $checkStmt->execute(['id' => $outfitId]);
        $existingOutfit = $checkStmt->fetch(PDO::FETCH_ASSOC);

        if ($existingOutfit) {
            // 穿搭存在，检查编辑权限
            $canEdit = checkOutfitEditPermission($conn, $userId, $outfitId);

            if ($canEdit['allowed']) {
                $isUpdate = true;
                echo "检测到穿搭更新操作，ID: $outfitId, 权限: {$canEdit['reason']}\n";
            } else {
                // 没有编辑权限，返回错误而不是创建新穿搭
                echo json_encode([
                    'error' => true,
                    'msg' => '权限不足，无法编辑此穿搭：' . $canEdit['reason']
                ]);
                exit;
            }
        } else {
            // 穿搭不存在，创建新穿搭
            $outfitId = null;
        }
    }
    
    if ($isUpdate) {
        // 更新现有穿搭（保持原始创建者信息）
        // 权限已在前面检查过
        $stmt = $conn->prepare("
            UPDATE outfits
            SET name = :name,
                description = :description,
                thumbnail_url = :thumbnail_url,
                category_id = :category_id,
                outfit_data = :outfit_data,
                updated_at = NOW()
            WHERE id = :id
        ");

        $stmt->execute([
            'name' => $name,
            'description' => $description,
            'thumbnail_url' => $thumbnailUrl,
            'category_id' => $categoryId,
            'outfit_data' => $outfitDataJson,
            'id' => $outfitId
        ]);
        
        // 获取分类名称
        $stmt = $conn->prepare("SELECT name FROM outfit_categories WHERE id = :id");
        $stmt->execute(['id' => $categoryId]);
        $category = $stmt->fetch(PDO::FETCH_ASSOC);
        $categoryName = $category ? $category['name'] : null;
        
        // 返回结果
        echo json_encode([
            'success' => true,
            'msg' => '穿搭更新成功',
            'data' => [
                'id' => $outfitId,
                'name' => $name,
                'description' => $description,
                'thumbnail' => $thumbnailUrl,
                'category_id' => $categoryId,
                'category_name' => $categoryName,
                'items' => $data['items'],
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ]);
    } else {
        // 创建新穿搭
        $stmt = $conn->prepare("
            INSERT INTO outfits (user_id, name, description, thumbnail_url, category_id, outfit_data, created_at, updated_at)
            VALUES (:user_id, :name, :description, :thumbnail_url, :category_id, :outfit_data, NOW(), NOW())
        ");
        
        $stmt->execute([
            'user_id' => $userId,
            'name' => $name,
            'description' => $description,
            'thumbnail_url' => $thumbnailUrl,
            'category_id' => $categoryId,
            'outfit_data' => $outfitDataJson
        ]);
        
        $newOutfitId = $conn->lastInsertId();
        
        // 获取分类名称
        $stmt = $conn->prepare("SELECT name FROM outfit_categories WHERE id = :id");
        $stmt->execute(['id' => $categoryId]);
        $category = $stmt->fetch(PDO::FETCH_ASSOC);
        $categoryName = $category ? $category['name'] : null;
        
        // 返回结果
        echo json_encode([
            'success' => true,
            'msg' => '穿搭创建成功',
            'data' => [
                'id' => $newOutfitId,
                'name' => $name,
                'description' => $description,
                'thumbnail' => $thumbnailUrl,
                'category_id' => $categoryId,
                'category_name' => $categoryName,
                'items' => $data['items'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '保存穿搭失败: ' . $e->getMessage()
    ]);
}

/**
 * 检查穿搭编辑权限
 */
function checkOutfitEditPermission($conn, $userId, $outfitId) {
    $result = [
        'allowed' => false,
        'reason' => '',
        'is_owner' => false,
        'user_role' => ''
    ];

    // 获取穿搭信息
    $stmt = $conn->prepare("SELECT user_id, circle_id FROM outfits WHERE id = :id");
    $stmt->bindParam(':id', $outfitId, PDO::PARAM_INT);
    $stmt->execute();
    $outfit = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$outfit) {
        $result['reason'] = '穿搭不存在';
        return $result;
    }

    // 检查是否是穿搭的创建者
    $result['is_owner'] = ($outfit['user_id'] == $userId);

    // 对于穿搭创建者，始终允许编辑（即使退出圈子）
    if ($result['is_owner']) {
        $result['allowed'] = true;
        $result['reason'] = '穿搭创建者权限（优先级最高）';
        error_log("穿搭编辑权限检查: 穿搭创建者权限生效，用户ID=$userId, 穿搭ID=$outfitId");
        return $result;
    }

    // 如果是个人穿搭（没有circle_id），只有创建者可以编辑
    if (empty($outfit['circle_id'])) {
        $result['reason'] = '非穿搭创建者，无权编辑个人穿搭';
        return $result;
    }

    // 检查用户在圈子中的角色
    $stmt = $conn->prepare("
        SELECT role, status
        FROM circle_members
        WHERE user_id = :user_id AND circle_id = :circle_id AND status = 'active'
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':circle_id', $outfit['circle_id'], PDO::PARAM_INT);
    $stmt->execute();
    $circleRole = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$circleRole) {
        $result['reason'] = '用户不在该圈子中';
        return $result;
    }

    $result['user_role'] = $circleRole['role'];

    // 权限规则：所有圈子成员都可以编辑共享穿搭
    $result['allowed'] = true;
    if ($circleRole['role'] === 'creator') {
        $result['reason'] = '圈子创建者权限';
    } elseif ($result['is_owner']) {
        $result['reason'] = '穿搭创建者权限';
    } else {
        $result['reason'] = '圈子成员共同管理权限';
    }

    return $result;
}
?>