<?php
header("Content-Type: application/json");
require_once './db.php';
require_once './auth.php';
require_once './config.php';

// 初始化响应数组
$response = [
    'code' => 0,
    'message' => 'success',
    'data' => null
];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['code'] = 405;
    $response['message'] = 'Method Not Allowed';
    echo json_encode($response);
    exit;
}

// 获取POST参数
$postData = json_decode(file_get_contents("php://input"), true);
$token = isset($postData['token']) ? $postData['token'] : '';
$merchantId = isset($postData['merchant_id']) ? intval($postData['merchant_id']) : 0;
$clothesId = isset($postData['clothes_id']) ? intval($postData['clothes_id']) : 0;

if (empty($token) || $merchantId <= 0 || $clothesId <= 0) {
    $response['code'] = 400;
    $response['message'] = 'Missing required parameters';
    echo json_encode($response);
    exit;
}

// 验证用户token
$auth = new Auth();
$verifyResult = $auth->verifyToken($token);

if (!$verifyResult) {
    $response['code'] = 401;
    $response['message'] = '无效或已过期的令牌';
    echo json_encode($response);
    exit;
}

$userId = $verifyResult['sub'];
$db = new Database();
$conn = $db->getConnection();

try {
    // 获取商家信息
    $merchantStmt = $conn->prepare("
        SELECT id, openid, nickname, avatar_url, gender, merchant_status, share_try_on_credits, paid_try_on_count
        FROM users 
        WHERE id = ? AND merchant_status = 'yes'
    ");
    $merchantStmt->bind_param("i", $merchantId);
    $merchantStmt->execute();
    $merchantResult = $merchantStmt->get_result();
    
    if ($merchantResult->num_rows === 0) {
        $response['code'] = 404;
        $response['message'] = '商家不存在或未入驻';
        echo json_encode($response);
        exit;
    }
    
    $merchant = $merchantResult->fetch_assoc();
    
    // 移除敏感信息
    unset($merchant['openid']);
    
    // 获取衣物详情
    $clothesStmt = $conn->prepare("
        SELECT c.*, cc.name as category_name
        FROM clothes c
        LEFT JOIN clothing_categories cc ON c.category_id = cc.id
        WHERE c.id = ? AND c.user_id = ?
    ");
    $clothesStmt->bind_param("ii", $clothesId, $merchantId);
    $clothesStmt->execute();
    $clothesResult = $clothesStmt->get_result();
    
    if ($clothesResult->num_rows === 0) {
        $response['code'] = 404;
        $response['message'] = '衣物不存在或不属于该商家';
        echo json_encode($response);
        exit;
    }
    
    $clothes = $clothesResult->fetch_assoc();
    
    // 处理JSON数据
    if (isset($clothes['description']) && !empty($clothes['description'])) {
        $description = json_decode($clothes['description'], true);
        if (is_array($description)) {
            $clothes['description_obj'] = $description;
        }
    }
    
    $response['data'] = [
        'clothes' => $clothes,
        'merchant' => $merchant
    ];
} catch (Exception $e) {
    $response['code'] = 500;
    $response['message'] = '处理请求时发生错误: ' . $e->getMessage();
} finally {
    $conn->close();
}

echo json_encode($response); 