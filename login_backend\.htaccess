# 启用重写引擎
RewriteEngine On

# 设置基本目录
RewriteBase /

# 允许跨域请求
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization"
    Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
</IfModule>

# 如果是OPTIONS请求，直接返回200
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# 对工作台API的路由
RewriteRule ^gongzuotai/login$ gongzuotai/login.php [L]
RewriteRule ^gongzuotai/users$ gongzuotai/users.php [L]

# 如果是文件或目录，直接访问
RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

# 对其他API的路由保持不变 