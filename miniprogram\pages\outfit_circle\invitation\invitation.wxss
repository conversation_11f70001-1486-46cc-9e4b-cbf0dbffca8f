/* 圈子邀请页面样式 */
/* 模块3：邀请分享模块 */

.container {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding: 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* 邀请容器 */
.invitation-container {
  padding: 40rpx 30rpx;
}

/* 邀请头部 */
.invitation-header {
  text-align: center;
  margin-bottom: 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.invitation-icon {
  margin-bottom: 20rpx;
}

.circle-icon {
  width: 80rpx;
  height: 80rpx;
}

.invitation-title {
  margin-bottom: 30rpx;
}

.title-text {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.circle-name {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: #000;
}

.inviter-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
}

.inviter-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 2rpx solid #f0f0f0;
}

.inviter-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.inviter-label {
  font-size: 26rpx;
  color: #666;
}

/* 圈子信息卡片 */
.circle-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.circle-info {
  margin-bottom: 30rpx;
}

.circle-name-section {
  margin-bottom: 20rpx;
}

.circle-name-large {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
}

.circle-code-section {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
}

.code-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.code-value {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  letter-spacing: 2rpx;
}

.copy-btn {
  padding: 10rpx;
}

.copy-icon {
  width: 32rpx;
  height: 32rpx;
}

.circle-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

/* 圈子统计 */
.circle-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx 10rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}

.stat-number {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 5rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 创建者信息 */
.creator-info {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}

.creator-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.creator-details {
  flex: 1;
}

.creator-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 5rpx;
}

.creator-role {
  font-size: 24rpx;
  color: #666;
}

.create-time {
  font-size: 24rpx;
  color: #999;
}

/* 功能说明 */
.features-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.features-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.feature-icon {
  font-size: 32rpx;
}

.feature-text {
  font-size: 26rpx;
  color: #666;
}

/* 状态提示 */
.status-section {
  margin-bottom: 30rpx;
}

.status-message {
  padding: 20rpx;
  border-radius: 12rpx;
  text-align: center;
  background-color: #fff;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.status-can_join, .status-can_rejoin {
  border-left: 4rpx solid #4caf50;
}

.status-already_member {
  border-left: 4rpx solid #2196f3;
}

.status-in_other_circle {
  border-left: 4rpx solid #ff9800;
}

.status-text {
  font-size: 26rpx;
  color: #333;
}

/* 操作按钮 */
.action-section {
  padding: 20rpx 0;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.refuse-btn, .agree-btn, .single-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refuse-btn {
  background-color: #fff;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.agree-btn {
  background-color: #000;
  color: #fff;
  font-weight: 600;
}

.single-btn {
  background-color: #000;
  color: #fff;
  font-weight: 600;
}

/* 弹框样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.modal-body {
  padding: 30rpx;
}

.modal-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-cancel-btn, .modal-confirm-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-cancel-btn {
  background-color: #f8f8f8;
  color: #666;
}

.modal-confirm-btn {
  background-color: #333;
  color: #fff;
  font-weight: 500;
}
