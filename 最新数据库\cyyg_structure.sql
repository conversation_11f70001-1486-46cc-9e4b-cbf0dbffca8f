-- MySQL dump 10.13  Distrib 5.6.50, for Linux (x86_64)
--
-- Host: localhost    Database: cyyg
-- ------------------------------------------------------
-- Server version	5.6.50-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `ad_watch_log`
--

DROP TABLE IF EXISTS `ad_watch_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `ad_watch_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `watch_time` datetime NOT NULL COMMENT '广告观看时间',
  `reward_type` varchar(20) NOT NULL DEFAULT 'try_on' COMMENT '奖励类型：try_on=试衣次数',
  `reward_amount` int(11) NOT NULL DEFAULT '1' COMMENT '奖励数量',
  `status` enum('success','failed') NOT NULL DEFAULT 'success' COMMENT '状态',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `device_info` text COMMENT '设备信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_watch_time` (`watch_time`),
  KEY `idx_reward_type` (`reward_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_ad_watch_log_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COMMENT='广告观看记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admin_users`
--

DROP TABLE IF EXISTS `admin_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `real_name` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `announcements`
--

DROP TABLE IF EXISTS `announcements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `announcements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `start_time` datetime NOT NULL COMMENT '生效开始时间',
  `end_time` datetime NOT NULL COMMENT '生效结束时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态: 0=禁用, 1=启用',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `api_usage`
--

DROP TABLE IF EXISTS `api_usage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `api_usage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_name` varchar(50) NOT NULL COMMENT '接口名称：try_on, photo_edit等',
  `total_quota` int(11) NOT NULL COMMENT '总配额数量',
  `used_quota` int(11) NOT NULL DEFAULT '0' COMMENT '已使用数量',
  `reset_date` date DEFAULT NULL COMMENT '配额重置日期',
  `last_used` datetime DEFAULT NULL COMMENT '最后使用时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `api_name` (`api_name`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `circle_data_sync_logs`
--

DROP TABLE IF EXISTS `circle_data_sync_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `circle_data_sync_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `circle_id` int(11) NOT NULL COMMENT '圈子ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `sync_type` enum('initial','incremental','manual') NOT NULL DEFAULT 'incremental' COMMENT '同步类型：initial=初始同步，incremental=增量同步，manual=手动同步',
  `data_type` enum('wardrobes','clothes','outfits','categories','all') NOT NULL COMMENT '同步的数据类型',
  `sync_status` enum('pending','processing','completed','failed') NOT NULL DEFAULT 'pending' COMMENT '同步状态',
  `items_count` int(11) NOT NULL DEFAULT '0' COMMENT '同步的数据条数',
  `error_message` text COMMENT '错误信息',
  `started_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_circle_id` (`circle_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_data_type` (`data_type`),
  KEY `idx_started_at` (`started_at`),
  KEY `idx_sync_logs_circle_user` (`circle_id`,`user_id`),
  KEY `idx_sync_logs_status_type` (`sync_status`,`data_type`),
  CONSTRAINT `fk_sync_logs_circle` FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sync_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COMMENT='圈子数据同步日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `circle_invitations`
--

DROP TABLE IF EXISTS `circle_invitations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `circle_invitations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `circle_id` int(11) NOT NULL COMMENT '圈子ID',
  `inviter_id` int(11) NOT NULL COMMENT '邀请者用户ID',
  `invitation_code` varchar(20) NOT NULL COMMENT '邀请码',
  `share_type` enum('wechat','timeline','copy') NOT NULL DEFAULT 'wechat' COMMENT '分享类型：wechat=微信好友，timeline=朋友圈，copy=复制链接',
  `share_count` int(11) NOT NULL DEFAULT '1' COMMENT '分享次数',
  `join_count` int(11) NOT NULL DEFAULT '0' COMMENT '通过此邀请加入的人数',
  `last_shared_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后分享时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_circle_inviter_code` (`circle_id`,`inviter_id`,`invitation_code`),
  KEY `idx_circle_id` (`circle_id`),
  KEY `idx_inviter_id` (`inviter_id`),
  KEY `idx_invitation_code` (`invitation_code`),
  KEY `idx_share_type` (`share_type`),
  KEY `idx_last_shared_at` (`last_shared_at`),
  KEY `idx_circle_invitations_stats` (`circle_id`,`share_count`,`join_count`),
  KEY `idx_inviter_invitations` (`inviter_id`,`last_shared_at`),
  CONSTRAINT `fk_circle_invitations_circle` FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_circle_invitations_inviter` FOREIGN KEY (`inviter_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='圈子邀请记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `circle_member_stats`
--

DROP TABLE IF EXISTS `circle_member_stats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `circle_member_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `circle_id` int(11) NOT NULL COMMENT '圈子ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `wardrobe_count` int(11) NOT NULL DEFAULT '0' COMMENT '贡献的衣橱数量',
  `clothes_count` int(11) NOT NULL DEFAULT '0' COMMENT '贡献的衣物数量',
  `outfit_count` int(11) NOT NULL DEFAULT '0' COMMENT '贡献的穿搭数量',
  `clothing_category_count` int(11) NOT NULL DEFAULT '0' COMMENT '贡献的衣物分类数量',
  `outfit_category_count` int(11) NOT NULL DEFAULT '0' COMMENT '贡献的穿搭分类数量',
  `tag_count` int(11) NOT NULL DEFAULT '0' COMMENT '贡献的标签数量',
  `last_contribution_at` datetime DEFAULT NULL COMMENT '最后贡献时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_circle_user_stats` (`circle_id`,`user_id`),
  KEY `idx_circle_id` (`circle_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_last_contribution` (`last_contribution_at`),
  KEY `idx_circle_stats_contribution` (`circle_id`,`last_contribution_at`),
  KEY `idx_circle_stats_clothes` (`circle_id`,`clothes_count`),
  KEY `idx_circle_stats_outfits` (`circle_id`,`outfit_count`),
  CONSTRAINT `fk_circle_member_stats_circle` FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_circle_member_stats_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COMMENT='圈子成员统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `circle_members`
--

DROP TABLE IF EXISTS `circle_members`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `circle_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `circle_id` int(11) NOT NULL COMMENT '圈子ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `role` enum('creator','member') NOT NULL DEFAULT 'member' COMMENT '角色：creator=创建者，member=成员',
  `joined_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `status` enum('active','removed') NOT NULL DEFAULT 'active' COMMENT '状态：active=活跃，removed=已移除',
  `removed_at` datetime DEFAULT NULL COMMENT '移除时间',
  `removed_by` int(11) DEFAULT NULL COMMENT '移除操作者ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_circle_user` (`circle_id`,`user_id`),
  KEY `idx_circle_id` (`circle_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_joined_at` (`joined_at`),
  KEY `fk_circle_members_removed_by` (`removed_by`),
  KEY `idx_circle_members_active` (`circle_id`,`status`),
  KEY `idx_user_circles_active` (`user_id`,`status`),
  CONSTRAINT `fk_circle_members_circle` FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_circle_members_removed_by` FOREIGN KEY (`removed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_circle_members_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='圈子成员关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `clothes`
--

DROP TABLE IF EXISTS `clothes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clothes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `circle_id` int(11) DEFAULT NULL COMMENT '关联的圈子ID，NULL表示个人数据',
  `wardrobe_id` int(11) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `category` varchar(50) NOT NULL COMMENT 'tops, pants, skirts, coats, shoes, bags, accessories',
  `category_id` int(11) DEFAULT NULL COMMENT '关联的分类ID',
  `image_url` text NOT NULL,
  `tags` varchar(255) DEFAULT NULL COMMENT 'Comma-separated list of tags',
  `description` text COMMENT 'JSON object with additional properties like color, brand, price',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `wear_count` int(11) NOT NULL DEFAULT '0' COMMENT '穿搭次数',
  `wear_frequency` int(11) DEFAULT NULL COMMENT '穿搭频率数值',
  `wear_frequency_unit` varchar(10) DEFAULT NULL COMMENT '穿搭频率单位：day=日, week=周, month=月, year=年',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_category` (`category`),
  KEY `idx_wardrobe_id` (`wardrobe_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_category_id_count` (`category`,`id`),
  KEY `idx_clothes_circle_id` (`circle_id`),
  KEY `idx_clothes_user_circle` (`user_id`,`circle_id`),
  KEY `idx_clothes_wardrobe_circle` (`wardrobe_id`,`circle_id`),
  CONSTRAINT `clothes_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `clothes_wardrobe_id_fk` FOREIGN KEY (`wardrobe_id`) REFERENCES `wardrobes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_clothes_circle` FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=50414 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `clothing_based_recommendations`
--

DROP TABLE IF EXISTS `clothing_based_recommendations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clothing_based_recommendations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `clothing_id` int(11) NOT NULL COMMENT '选中的基础衣物ID',
  `recommendation_data` text NOT NULL COMMENT '推荐数据JSON',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_created` (`user_id`,`created_at`),
  KEY `idx_clothing_id` (`clothing_id`),
  CONSTRAINT `fk_clothing_rec_clothing` FOREIGN KEY (`clothing_id`) REFERENCES `clothes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_clothing_rec_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2982 DEFAULT CHARSET=utf8mb4 COMMENT='基于衣物的穿搭推荐记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `clothing_categories`
--

DROP TABLE IF EXISTS `clothing_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clothing_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID，NULL表示系统默认分类',
  `circle_id` int(11) DEFAULT NULL COMMENT '关联的圈子ID，NULL表示个人数据',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `code` varchar(50) NOT NULL COMMENT '分类代码，用于兼容现有数据',
  `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为系统默认分类',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_code` (`code`),
  KEY `idx_is_system` (`is_system`),
  KEY `idx_clothing_categories_circle_id` (`circle_id`),
  CONSTRAINT `clothing_categories_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_clothing_categories_circle` FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=21525 DEFAULT CHARSET=utf8mb4 COMMENT='衣物分类表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `customer_service_messages`
--

DROP TABLE IF EXISTS `customer_service_messages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customer_service_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `openid` varchar(100) NOT NULL,
  `message_type` varchar(20) NOT NULL COMMENT 'text, image, link, etc.',
  `content` text NOT NULL,
  `direction` enum('in','out') NOT NULL COMMENT 'in: from user, out: from staff',
  `is_read` tinyint(1) DEFAULT '0',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id_direction` (`user_id`,`direction`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_openid` (`openid`),
  CONSTRAINT `customer_service_messages_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `deleted_system_categories`
--

DROP TABLE IF EXISTS `deleted_system_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `deleted_system_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `code` varchar(50) NOT NULL,
  `deleted_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_code` (`user_id`,`code`)
) ENGINE=InnoDB AUTO_INCREMENT=293 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `donations`
--

DROP TABLE IF EXISTS `donations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `donations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `nickname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户昵称',
  `avatar_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户头像',
  `amount` decimal(10,2) NOT NULL COMMENT '打赏金额',
  `order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `transaction_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信交易号',
  `status` enum('pending','success','failed') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '支付状态',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `paid_at` datetime DEFAULT NULL COMMENT '支付时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户打赏记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `face_analysis`
--

DROP TABLE IF EXISTS `face_analysis`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `face_analysis` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `front_photo_url` text COMMENT '正面照片URL',
  `side_photo_url` text COMMENT '侧面照片URL',
  `cdn_front_photo_url` text COMMENT '正面照片CDN URL',
  `cdn_side_photo_url` text COMMENT '侧面照片CDN URL',
  `preferred_style` varchar(255) DEFAULT NULL COMMENT '偏好风格',
  `analysis_result` longtext COMMENT '分析结果JSON',
  `status` enum('pending','processing','completed','failed') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `payment_status` enum('unpaid','paid','refunded') NOT NULL DEFAULT 'unpaid' COMMENT '支付状态',
  `order_id` varchar(64) DEFAULT NULL COMMENT '订单ID',
  `amount` decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '支付金额',
  `usage_status` enum('unused','used','expired') NOT NULL DEFAULT 'unused' COMMENT '使用状态',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_usage_status` (`usage_status`),
  CONSTRAINT `fk_face_analysis_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=150 DEFAULT CHARSET=utf8mb4 COMMENT='面容分析表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `image_analysis_based_recommendations`
--

DROP TABLE IF EXISTS `image_analysis_based_recommendations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `image_analysis_based_recommendations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `analysis_id` int(11) NOT NULL,
  `recommendation_data` text NOT NULL,
  `refresh` int(11) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_analysis` (`user_id`,`analysis_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `fk_image_analysis_rec_analysis` (`analysis_id`),
  CONSTRAINT `fk_image_analysis_rec_analysis` FOREIGN KEY (`analysis_id`) REFERENCES `user_image_analysis` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_image_analysis_rec_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1032 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `invitation_codes`
--

DROP TABLE IF EXISTS `invitation_codes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invitation_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(20) NOT NULL COMMENT '邀请码',
  `status` enum('unused','used','expired') NOT NULL DEFAULT 'unused' COMMENT '状态：unused=未使用, used=已使用, expired=已过期',
  `type` varchar(20) NOT NULL DEFAULT 'image_analysis' COMMENT '邀请码类型，默认为形象分析',
  `created_by` int(11) DEFAULT NULL COMMENT '创建者ID',
  `used_by` int(11) DEFAULT NULL COMMENT '使用者ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expired_at` datetime DEFAULT NULL COMMENT '过期时间',
  `used_at` datetime DEFAULT NULL COMMENT '使用时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`),
  KEY `idx_status` (`status`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_used_by` (`used_by`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COMMENT='邀请码表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `media_check_results`
--

DROP TABLE IF EXISTS `media_check_results`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `media_check_results` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trace_id` varchar(100) NOT NULL COMMENT '微信安全检测唯一标识',
  `user_id` int(11) NOT NULL,
  `openid` varchar(100) NOT NULL,
  `media_url` text NOT NULL COMMENT '检测的媒体文件URL',
  `temp_file_path` varchar(255) NOT NULL COMMENT '临时文件路径',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '检测状态: pending, passed, rejected',
  `suggest` varchar(20) DEFAULT NULL COMMENT '检测建议: pass, risky, review',
  `label` int(11) DEFAULT NULL COMMENT '命中标签值',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `trace_id` (`trace_id`),
  KEY `idx_trace_id` (`trace_id`),
  KEY `idx_media_user_id` (`user_id`),
  KEY `idx_media_status` (`status`),
  CONSTRAINT `media_check_results_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `outfit_calendar`
--

DROP TABLE IF EXISTS `outfit_calendar`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `outfit_calendar` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `outfit_id` int(11) NOT NULL COMMENT '穿搭ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `calendar_date` date NOT NULL COMMENT '日期',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_outfit_date` (`user_id`,`calendar_date`),
  KEY `idx_outfit_id` (`outfit_id`),
  KEY `idx_calendar_date` (`calendar_date`),
  CONSTRAINT `fk_outfit_calendar_outfit` FOREIGN KEY (`outfit_id`) REFERENCES `outfits` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_outfit_calendar_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=500 DEFAULT CHARSET=utf8mb4 COMMENT='穿搭日历关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `outfit_categories`
--

DROP TABLE IF EXISTS `outfit_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `outfit_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `circle_id` int(11) DEFAULT NULL COMMENT '关联的圈子ID，NULL表示个人数据',
  `name` varchar(100) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `sort_order` int(11) DEFAULT '0',
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_outfit_category_name` (`name`),
  KEY `idx_outfit_categories_circle_id` (`circle_id`),
  CONSTRAINT `fk_outfit_categories_circle` FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE SET NULL,
  CONSTRAINT `outfit_categories_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1044 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `outfit_circles`
--

DROP TABLE IF EXISTS `outfit_circles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `outfit_circles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '圈子名称',
  `description` text COMMENT '圈子描述/备注',
  `invitation_code` varchar(20) NOT NULL COMMENT '邀请码',
  `creator_id` int(11) NOT NULL COMMENT '创建者用户ID',
  `member_count` int(11) NOT NULL DEFAULT '1' COMMENT '成员数量',
  `status` enum('active','dissolved') NOT NULL DEFAULT 'active' COMMENT '圈子状态：active=活跃，dissolved=已解散',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_invitation_code` (`invitation_code`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_outfit_circles_creator` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='穿搭圈子表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `outfit_likes`
--

DROP TABLE IF EXISTS `outfit_likes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `outfit_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `outfit_id` int(11) NOT NULL COMMENT '穿搭ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_outfit` (`user_id`,`outfit_id`),
  KEY `idx_outfit_id` (`outfit_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_outfit_likes_outfit` FOREIGN KEY (`outfit_id`) REFERENCES `outfits` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_outfit_likes_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='穿搭点赞记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `outfit_ratings`
--

DROP TABLE IF EXISTS `outfit_ratings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `outfit_ratings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `photo_url` text NOT NULL COMMENT '穿搭照片URL',
  `score` decimal(3,1) NOT NULL DEFAULT '0.0' COMMENT '总体评分(1-10分)',
  `rating_details` text NOT NULL COMMENT '评分详情JSON',
  `ai_comments` text COMMENT 'AI评语',
  `improvement_suggestions` text COMMENT '改进建议',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_outfit_ratings_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=72 DEFAULT CHARSET=utf8mb4 COMMENT='穿搭评分表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `outfit_recommendations`
--

DROP TABLE IF EXISTS `outfit_recommendations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `outfit_recommendations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `recommendation_data` text NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_created` (`user_id`,`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1839 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `outfits`
--

DROP TABLE IF EXISTS `outfits`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `outfits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `circle_id` int(11) DEFAULT NULL COMMENT '关联的圈子ID，NULL表示个人数据',
  `category_id` int(11) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `category` varchar(50) NOT NULL DEFAULT '其他' COMMENT '穿搭分类：正式、休闲、聚会、工作、度假、运动、居家、其他',
  `thumbnail_url` text COMMENT '缩略图URL',
  `outfit_data` longtext NOT NULL COMMENT '以JSON格式存储的穿搭布局数据',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `is_public` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否公开: 0=不公开, 1=公开',
  `likes_count` int(11) NOT NULL DEFAULT '0' COMMENT '点赞数量',
  `calendar_date` date DEFAULT NULL COMMENT '穿搭日期',
  PRIMARY KEY (`id`),
  KEY `idx_outfit_user_id` (`user_id`),
  KEY `idx_outfit_created_at` (`created_at`),
  KEY `idx_outfit_category` (`category`),
  KEY `idx_outfit_category_id` (`category_id`),
  KEY `idx_outfit_is_public` (`is_public`),
  KEY `idx_calendar_date` (`user_id`,`calendar_date`),
  KEY `idx_outfits_circle_id` (`circle_id`),
  KEY `idx_outfits_user_circle` (`user_id`,`circle_id`),
  CONSTRAINT `fk_outfit_category` FOREIGN KEY (`category_id`) REFERENCES `outfit_categories` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_outfits_circle` FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE SET NULL,
  CONSTRAINT `outfits_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5295 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `photos`
--

DROP TABLE IF EXISTS `photos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `photos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `image_url` varchar(255) NOT NULL,
  `type` enum('full','half','other') DEFAULT 'other',
  `description` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `photos_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1561 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `preference_based_recommendations`
--

DROP TABLE IF EXISTS `preference_based_recommendations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `preference_based_recommendations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `preference` varchar(255) NOT NULL,
  `recommendation_data` text NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`,`preference`(191)),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=503 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `processing_tasks`
--

DROP TABLE IF EXISTS `processing_tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `processing_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `task_type` varchar(50) NOT NULL,
  `total_items` int(11) DEFAULT '0',
  `processed_items` int(11) DEFAULT '0',
  `status` enum('pending','processing','completed','failed') DEFAULT 'pending',
  `error_message` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_task` (`user_id`,`task_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `recharge_records`
--

DROP TABLE IF EXISTS `recharge_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `recharge_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `nickname` varchar(255) DEFAULT NULL COMMENT '用户昵称',
  `order_id` varchar(64) NOT NULL COMMENT '订单号',
  `package_id` int(11) NOT NULL COMMENT '套餐ID',
  `amount` decimal(10,2) NOT NULL COMMENT '充值金额',
  `count` int(11) NOT NULL COMMENT '充值次数',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态：pending-待支付，success-支付成功，failed-支付失败',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '微信支付交易号',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `paid_at` datetime DEFAULT NULL COMMENT '支付时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `order_id` (`order_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COMMENT='充值记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `recommended_outfit_categories`
--

DROP TABLE IF EXISTS `recommended_outfit_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `recommended_outfit_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `sort_order` int(11) DEFAULT '0',
  `status` tinyint(4) DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `recommended_outfit_items`
--

DROP TABLE IF EXISTS `recommended_outfit_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `recommended_outfit_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `outfit_id` int(11) NOT NULL COMMENT '关联的推荐穿搭ID',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `image_url` text NOT NULL COMMENT '商品图片URL',
  `price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `purchase_url` text NOT NULL COMMENT '购买链接(淘宝客链接)',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序，值越小越靠前',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_recommended_outfit_item_outfit` (`outfit_id`),
  KEY `idx_recommended_outfit_item_sort` (`sort_order`),
  CONSTRAINT `fk_recommended_outfit_item_outfit` FOREIGN KEY (`outfit_id`) REFERENCES `recommended_outfits` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `recommended_outfit_stats`
--

DROP TABLE IF EXISTS `recommended_outfit_stats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `recommended_outfit_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `outfit_id` int(11) NOT NULL COMMENT '关联的推荐穿搭ID',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '查看次数',
  `copy_link_count` int(11) NOT NULL DEFAULT '0' COMMENT '复制链接次数',
  `last_viewed_at` datetime DEFAULT NULL COMMENT '最后查看时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `outfit_id` (`outfit_id`),
  CONSTRAINT `fk_recommended_outfit_stats_outfit` FOREIGN KEY (`outfit_id`) REFERENCES `recommended_outfits` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `recommended_outfits`
--

DROP TABLE IF EXISTS `recommended_outfits`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `recommended_outfits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '穿搭名称',
  `image_url` text NOT NULL COMMENT '穿搭图片URL',
  `description` text COMMENT '穿搭描述',
  `recommendation_reason` text COMMENT '穿搭推荐理由',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态: 1=启用, 0=禁用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序，值越小越靠前',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_recommended_outfit_category` (`category_id`),
  KEY `idx_recommended_outfit_status` (`status`),
  KEY `idx_recommended_outfit_sort` (`sort_order`),
  CONSTRAINT `fk_recommended_outfit_category` FOREIGN KEY (`category_id`) REFERENCES `outfit_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `taobao_products`
--

DROP TABLE IF EXISTS `taobao_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `taobao_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` varchar(50) NOT NULL COMMENT '淘宝商品ID',
  `title` varchar(255) NOT NULL COMMENT '商品标题',
  `image_url` text NOT NULL COMMENT '商品主图URL',
  `small_images` text COMMENT '商品小图URL列表，JSON格式',
  `original_price` decimal(10,2) NOT NULL COMMENT '商品原价',
  `zk_final_price` decimal(10,2) NOT NULL COMMENT '商品折扣价',
  `final_price` decimal(10,2) NOT NULL COMMENT '商品最终价格',
  `coupon_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠券金额',
  `coupon_info` varchar(255) DEFAULT NULL COMMENT '优惠券信息',
  `coupon_start_time` datetime DEFAULT NULL COMMENT '优惠券开始时间',
  `coupon_end_time` datetime DEFAULT NULL COMMENT '优惠券结束时间',
  `shop_title` varchar(100) DEFAULT NULL COMMENT '店铺名称',
  `seller_id` varchar(50) DEFAULT NULL COMMENT '卖家ID',
  `volume` int(11) DEFAULT '0' COMMENT '30天销量',
  `item_url` text COMMENT '商品推广链接',
  `coupon_click_url` text COMMENT '优惠券推广链接',
  `commission_rate` decimal(10,2) DEFAULT '0.00' COMMENT '佣金比例',
  `commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '佣金金额',
  `material_id` varchar(50) DEFAULT NULL COMMENT '所属物料ID',
  `category` varchar(50) DEFAULT NULL COMMENT '商品分类',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1=上架，0=下架',
  `is_recommend` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否推荐：1=是，0=否',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序',
  `last_sync_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后同步时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `tpwd` varchar(255) DEFAULT NULL COMMENT '淘口令短链接',
  `is_fake_tpwd` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为模拟淘口令：1=是，0=否',
  `tags` text COMMENT '商品标签(JSON)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_item_id` (`item_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_recommend` (`is_recommend`),
  KEY `idx_material_id` (`material_id`),
  KEY `idx_category` (`category`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_last_sync_time` (`last_sync_time`)
) ENGINE=InnoDB AUTO_INCREMENT=19364 DEFAULT CHARSET=utf8mb4 COMMENT='淘宝客商品数据表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `try_on_history`
--

DROP TABLE IF EXISTS `try_on_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `try_on_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `photo_id` int(11) NOT NULL,
  `result_image_url` text NOT NULL,
  `clothes_ids` varchar(255) NOT NULL COMMENT 'JSON array of clothes ids',
  `task_id` varchar(100) DEFAULT NULL,
  `status` enum('success','failed','processing') DEFAULT 'success',
  `error_message` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `photo_id` (`photo_id`),
  KEY `idx_try_on_user_id` (`user_id`),
  KEY `idx_try_on_created_at` (`created_at`),
  CONSTRAINT `try_on_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `try_on_history_ibfk_2` FOREIGN KEY (`photo_id`) REFERENCES `photos` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=898 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_image_analysis`
--

DROP TABLE IF EXISTS `user_image_analysis`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_image_analysis` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `height` int(11) DEFAULT NULL COMMENT '身高(cm)',
  `weight` int(11) DEFAULT NULL COMMENT '体重(kg)',
  `bust` int(11) DEFAULT NULL COMMENT '胸围(cm)',
  `waist` int(11) DEFAULT NULL COMMENT '腰围(cm)',
  `hips` int(11) DEFAULT NULL COMMENT '臀围(cm)',
  `shoulder_width` int(11) DEFAULT NULL COMMENT '肩宽(cm)',
  `skin_tone` varchar(50) DEFAULT NULL COMMENT '肤色',
  `face_shape` varchar(50) DEFAULT NULL COMMENT '脸型',
  `body_shape` varchar(50) DEFAULT NULL COMMENT '体型',
  `gender` tinyint(1) DEFAULT NULL COMMENT '性别：1-男，2-女',
  `photo_urls` text COMMENT '照片URL列表，JSON格式',
  `remarks` text COMMENT '用户备注',
  `analysis_result` longtext COMMENT '分析结果，JSON格式',
  `status` enum('pending','processing','completed','failed') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `payment_status` enum('unpaid','paid','refunded') NOT NULL DEFAULT 'unpaid' COMMENT '支付状态',
  `order_id` varchar(64) DEFAULT NULL COMMENT '订单ID',
  `amount` decimal(10,2) NOT NULL DEFAULT '9.90' COMMENT '支付金额',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `analysis_time` datetime DEFAULT NULL COMMENT '分析完成时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_user_image_analysis_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=703 DEFAULT CHARSET=utf8mb4 COMMENT='用户形象分析表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_message_status`
--

DROP TABLE IF EXISTS `user_message_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_message_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `unread_count` int(11) NOT NULL DEFAULT '0',
  `last_update` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='跟踪用户客服消息状态';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_style_profile`
--

DROP TABLE IF EXISTS `user_style_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_style_profile` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `style_preferences` text COMMENT '风格偏好，JSON格式',
  `color_preferences` text COMMENT '颜色偏好，JSON格式',
  `seasonal_patterns` text COMMENT '季节性穿搭模式，JSON格式',
  `outfit_habits` text COMMENT '穿搭习惯，JSON格式',
  `style_suggestions` text COMMENT '风格建议，JSON格式',
  `wardrobe_analysis` text COMMENT '衣橱分析，JSON格式',
  `advanced_traits` text COMMENT '高级特征分析，JSON格式',
  `analysis_summary` text COMMENT '分析摘要',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `last_analysis_time` datetime DEFAULT NULL COMMENT '最后分析时间',
  `data_sources` text COMMENT '分析数据来源，JSON格式',
  `analysis_status` enum('pending','processing','completed','failed') NOT NULL DEFAULT 'pending',
  `error_message` text COMMENT '错误信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_user_style_profile_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='用户风格画像表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `openid` varchar(100) NOT NULL,
  `unionid` varchar(100) DEFAULT NULL,
  `session_key` varchar(100) NOT NULL,
  `nickname` varchar(100) DEFAULT NULL,
  `avatar_url` text,
  `gender` tinyint(4) DEFAULT NULL COMMENT '0: unknown, 1: male, 2: female',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1: active, 0: disabled',
  `merchant_status` enum('yes','no') NOT NULL DEFAULT 'no' COMMENT '商家入驻状态: yes=已入驻, no=未入驻',
  `share_try_on_credits` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否共享试穿点数: 1=是, 0=否',
  `free_try_on_count` int(11) NOT NULL DEFAULT '1' COMMENT '免费试衣次数，每日自动刷新为1',
  `paid_try_on_count` int(11) NOT NULL DEFAULT '0' COMMENT '付费试衣次数',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `openid` (`openid`),
  KEY `idx_openid` (`openid`),
  KEY `idx_unionid` (`unionid`),
  KEY `idx_try_on_count` (`paid_try_on_count`),
  KEY `idx_free_try_on_count` (`free_try_on_count`),
  KEY `idx_paid_try_on_count` (`paid_try_on_count`),
  KEY `idx_merchant_status` (`merchant_status`),
  KEY `idx_share_try_on_credits` (`share_try_on_credits`)
) ENGINE=InnoDB AUTO_INCREMENT=3890 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wardrobes`
--

DROP TABLE IF EXISTS `wardrobes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wardrobes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `circle_id` int(11) DEFAULT NULL COMMENT '关联的圈子ID，NULL表示个人数据',
  `name` varchar(100) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `sort_order` int(11) DEFAULT '0',
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_wardrobe_name` (`name`),
  KEY `idx_wardrobes_circle_id` (`circle_id`),
  KEY `idx_wardrobes_user_circle` (`user_id`,`circle_id`),
  CONSTRAINT `fk_wardrobes_circle` FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE SET NULL,
  CONSTRAINT `wardrobes_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1214 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `weather_based_recommendations`
--

DROP TABLE IF EXISTS `weather_based_recommendations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `weather_based_recommendations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `weather_key` varchar(100) NOT NULL COMMENT '天气键(城市_天气状况_温度)',
  `weather_data` text NOT NULL COMMENT '天气数据JSON',
  `recommendation_data` text NOT NULL COMMENT '推荐数据JSON',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_weather_key` (`user_id`,`weather_key`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_weather_rec_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1672 DEFAULT CHARSET=utf8mb4 COMMENT='基于天气的穿搭推荐记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `weather_product_recommendations`
--

DROP TABLE IF EXISTS `weather_product_recommendations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `weather_product_recommendations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `weather_key` varchar(100) NOT NULL COMMENT '天气键(城市_天气状况_温度)',
  `weather_data` text NOT NULL COMMENT '天气数据JSON',
  `recommendation_data` text NOT NULL COMMENT '推荐商品数据JSON',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_weather_key` (`user_id`,`weather_key`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_weather_prod_rec_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1745 DEFAULT CHARSET=utf8mb4 COMMENT='基于天气的商品推荐记录';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-02 15:54:19
