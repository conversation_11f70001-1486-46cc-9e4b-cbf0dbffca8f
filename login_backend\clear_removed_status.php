<?php
// 清除用户被踢出状态API
// 模块2：圈子成员管理模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 为用户的被移除记录添加已读标记
    // 使用更可靠的方法：在removed_at字段后添加特殊标记

    // 首先检查是否有未读的被移除记录
    $checkSql = "SELECT id, removed_at FROM circle_members
                 WHERE user_id = :user_id
                 AND status = 'removed'
                 AND removed_at NOT LIKE '%_read'
                 ORDER BY removed_at DESC LIMIT 1";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkStmt->execute();

    $record = $checkStmt->fetch(PDO::FETCH_ASSOC);

    if ($record) {
        // 更新记录为已读
        $updateSql = "UPDATE circle_members
                      SET removed_at = CONCAT(:original_time, '_read')
                      WHERE id = :record_id";
        $updateStmt = $conn->prepare($updateSql);
        $updateStmt->bindParam(':original_time', $record['removed_at']);
        $updateStmt->bindParam(':record_id', $record['id'], PDO::PARAM_INT);
        $updateStmt->execute();

        $affectedRows = $updateStmt->rowCount();
    } else {
        $affectedRows = 0;
    }
    
    if ($affectedRows > 0) {
        echo json_encode([
            'status' => 'success',
            'message' => '已标记通知为已读',
            'debug' => [
                'user_id' => $userId,
                'affected_rows' => $affectedRows,
                'record_id' => $record['id'] ?? null,
                'original_time' => $record['removed_at'] ?? null
            ]
        ]);
    } else {
        echo json_encode([
            'status' => 'success',
            'message' => '没有需要标记的通知',
            'debug' => [
                'user_id' => $userId,
                'affected_rows' => 0,
                'found_record' => $record ? true : false
            ]
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '清除状态失败：' . $e->getMessage()
    ]);
}
?>
