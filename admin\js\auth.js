/**
 * 管理员认证模块
 */
const Auth = {
    // API基础URL
    apiBaseUrl: '../login_backend',
    
    // 令牌存储键名
    tokenKey: 'admin_token',
    userKey: 'admin_user',
    
    /**
     * 管理员登录
     * @param {string} username 用户名
     * @param {string} password 密码
     * @returns {Promise} 登录结果Promise
     */
    login: function(username, password) {
        return new Promise((resolve, reject) => {
            fetch(`${this.apiBaseUrl}/admin_login.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    reject(new Error(data.msg || '登录失败'));
                    return;
                }
                
                // 存储令牌和用户信息
                localStorage.setItem(this.tokenKey, data.data.token);
                localStorage.setItem(this.userKey, JSON.stringify({
                    id: data.data.user_id,
                    username: data.data.username,
                    realName: data.data.real_name
                }));
                
                resolve(data.data);
            })
            .catch(error => {
                console.error('登录请求失败:', error);
                reject(new Error('网络请求失败，请检查网络连接'));
            });
        });
    },
    
    /**
     * 注销登录
     */
    logout: function() {
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.userKey);
        window.location.href = 'index.html';
    },
    
    /**
     * 验证当前登录状态
     * @returns {Promise} 验证结果Promise
     */
    verifyAuth: function() {
        return new Promise((resolve, reject) => {
            const token = this.getToken();
            
            if (!token) {
                reject(new Error('未登录'));
                return;
            }
            
            fetch(`${this.apiBaseUrl}/admin_verify_token.php`, {
                method: 'GET',
                headers: {
                    'Authorization': token
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    // 令牌无效，清除存储
                    this.logout();
                    reject(new Error(data.msg || '登录已过期'));
                    return;
                }
                
                // 更新用户信息
                localStorage.setItem(this.userKey, JSON.stringify({
                    id: data.data.id,
                    username: data.data.username,
                    realName: data.data.real_name
                }));
                
                resolve(data.data);
            })
            .catch(error => {
                console.error('验证令牌失败:', error);
                reject(new Error('网络请求失败，请检查网络连接'));
            });
        });
    },
    
    /**
     * 获取存储的令牌
     * @returns {string|null} 令牌字符串或null
     */
    getToken: function() {
        return localStorage.getItem(this.tokenKey);
    },
    
    /**
     * 获取当前用户信息
     * @returns {Object|null} 用户信息对象或null
     */
    getCurrentUser: function() {
        const userData = localStorage.getItem(this.userKey);
        return userData ? JSON.parse(userData) : null;
    },
    
    /**
     * 检查是否已登录
     * @returns {boolean} 是否已登录
     */
    isLoggedIn: function() {
        return !!this.getToken();
    },
    
    /**
     * 获取用户名
     * @returns {string} 用户名或默认值
     */
    getUsername: function() {
        const user = this.getCurrentUser();
        return user ? user.username : null;
    },
    
    /**
     * 保护页面（未登录则跳转到登录页）
     */
    protectPage: function() {
        if (!this.isLoggedIn()) {
            window.location.href = 'index.html';
            return;
        }
        
        // 验证令牌有效性
        this.verifyAuth().catch(() => {
            // 验证失败会自动调用logout跳转
        });
    }
}; 