<?php
/**
 * 和风天气地理编码API测试脚本
 */

header('Content-Type: application/json;charset=utf-8');

// 加载配置
require_once 'config.php';

// 记录日志
function log_message($message, $data = []) {
    echo "LOG: $message\n";
    if (!empty($data)) {
        echo "DATA: " . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    }
    echo "\n";
}

// 获取测试参数
$latitude = isset($_GET['latitude']) ? $_GET['latitude'] : '30.4380786';
$longitude = isset($_GET['longitude']) ? $_GET['longitude'] : '120';
$city = isset($_GET['city']) ? $_GET['city'] : '';
$debug = isset($_GET['debug']) && $_GET['debug'] === 'true';

log_message("测试参数", [
    'latitude' => $latitude,
    'longitude' => $longitude, 
    'city' => $city,
    'debug' => $debug
]);

// 构建请求参数
$params = [];
if ($city) {
    $params['location'] = $city;
    log_message("使用城市名称查询", ['city' => $city]);
} else {
    // 使用经纬度查询（注意：经度在前，纬度在后）
    $params['location'] = $longitude . ',' . $latitude;
    log_message("使用经纬度查询", ['location' => $params['location']]);
}

// 添加API密钥
$params['key'] = WEATHER_API_KEY;

// 设置语言为中文
$params['lang'] = 'zh';

// 添加防缓存参数
$params['_'] = time();

// 构建API URL - 确保使用正确的API路径
$geoApiHost = defined('WEATHER_GEO_API_HOST') ? WEATHER_GEO_API_HOST : 'geoapi.qweather.com';
$geoApiPath = defined('WEATHER_GEO_API_PATH') ? WEATHER_GEO_API_PATH : '/geo/v2/city/lookup';

$apiUrl = 'https://' . $geoApiHost . $geoApiPath . '?' . http_build_query($params);
log_message("API URL", ['url' => $apiUrl, 'host' => $geoApiHost, 'path' => $geoApiPath]);

// 设置请求选项
$options = [
    'http' => [
        'header' => "Accept: application/json\r\n" .
                   "Accept-Encoding: gzip\r\n" .
                   "Referer: " . API_DOMAIN . "\r\n" .
                   "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36\r\n",
        'method' => 'GET',
        'timeout' => 10,
        'ignore_errors' => true
    ]
];

// 禁用IPv6，强制使用IPv4
$options['socket'] = ['bindto' => '0:0'];

// 创建上下文
$context = stream_context_create($options);

try {
    // 发送请求
    $startTime = microtime(true);
    $response = file_get_contents($apiUrl, false, $context);
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    log_message("API请求完成", ['duration' => $duration . 'ms']);
    
    // 处理HTTP错误
    $http_response_header = $http_response_header ?? ['HTTP/1.1 500 Internal Server Error'];
    preg_match('/^HTTP\/\d\.\d\s+(\d+)/', $http_response_header[0], $matches);
    $statusCode = intval($matches[1] ?? 500);
    
    log_message("HTTP状态码", ['status' => $statusCode, 'header' => $http_response_header[0]]);
    
    // 记录响应头信息
    $responseHeaders = [];
    foreach ($http_response_header as $header) {
        if (preg_match('/^([^:]+):\s*(.+)$/', $header, $matches)) {
            $responseHeaders[$matches[1]] = $matches[2];
        }
    }
    log_message("响应头信息", $responseHeaders);
    
    // 检查是否有内容编码
    $contentEncoding = isset($responseHeaders['Content-Encoding']) ? strtolower($responseHeaders['Content-Encoding']) : '';
    if ($contentEncoding === 'gzip' && function_exists('gzdecode')) {
        $response = gzdecode($response);
        log_message("已解压gzip编码的响应");
    }
    
    // 记录原始响应的字节值（便于调试编码问题）
    if ($debug) {
        $bytes = [];
        for ($i = 0; $i < min(50, strlen($response)); $i++) {
            $bytes[] = ord($response[$i]);
        }
        log_message("原始响应前50个字节", $bytes);
        
        // 设置自定义头以便浏览器查看原始响应
        header('X-Raw-Response: ' . base64_encode(substr($response, 0, 1000)));
    }
    
    // 如果要直接查看原始响应而不是JSON处理结果
    if (isset($_GET['raw']) && $_GET['raw'] === 'true') {
        header('Content-Type: text/plain');
        echo "==== 原始响应开始 ====\n";
        echo $response;
        echo "\n==== 原始响应结束 ====";
        exit;
    }
    
    if ($statusCode !== 200) {
        echo json_encode([
            'success' => false,
            'message' => '城市信息API请求失败：HTTP ' . $statusCode,
            'code' => $statusCode,
            'debug' => $debug ? [
                'url' => $apiUrl,
                'headers' => $http_response_header,
                'response' => substr($response, 0, 1000)
            ] : null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 清理响应中可能存在的BOM和控制字符
    $response = preg_replace('/[\x00-\x1F\x7F]/u', '', $response);
    if (substr($response, 0, 3) === "\xEF\xBB\xBF") {
        $response = substr($response, 3); // 移除UTF-8 BOM
        log_message("已移除UTF-8 BOM");
    }
    
    // 尝试解析响应
    $data = json_decode($response, true);
    $jsonError = json_last_error();
    
    if ($jsonError !== JSON_ERROR_NONE) {
        log_message("JSON解析错误", [
            'error' => json_last_error_msg(),
            'error_code' => $jsonError
        ]);
        
        // 尝试不同的修复方法
        // 1. 尝试UTF8编码修复
        if ($jsonError === JSON_ERROR_UTF8) {
            log_message("尝试UTF8编码修复");
            $response = utf8_encode($response);
            $data = json_decode($response, true);
            $jsonError = json_last_error();
            
            if ($jsonError === JSON_ERROR_NONE) {
                log_message("UTF8编码修复成功");
            }
        }
        
        // 2. 如果还是失败，尝试移除所有非ASCII字符
        if ($jsonError !== JSON_ERROR_NONE) {
            log_message("尝试移除所有非ASCII字符");
            $response = preg_replace('/[^\x20-\x7E]/', '', $response);
            $data = json_decode($response, true);
            $jsonError = json_last_error();
            
            if ($jsonError === JSON_ERROR_NONE) {
                log_message("移除非ASCII字符后解析成功");
            }
        }
        
        // 3. 最后尝试：将响应转义后再解析
        if ($jsonError !== JSON_ERROR_NONE) {
            log_message("尝试对响应进行转义");
            $response = addslashes($response);
            $response = json_encode(['data' => $response]);
            $response = json_decode($response, true);
            $response = $response['data'];
            
            $data = json_decode($response, true);
            $jsonError = json_last_error();
            
            if ($jsonError === JSON_ERROR_NONE) {
                log_message("转义处理后解析成功");
            }
        }
        
        // 如果所有尝试都失败
        if ($jsonError !== JSON_ERROR_NONE) {
            echo json_encode([
                'success' => false,
                'message' => '解析城市信息失败：' . json_last_error_msg(),
                'code' => 500,
                'debug' => $debug ? [
                    'raw_response' => base64_encode(substr($response, 0, 1000)),
                    'error_code' => $jsonError,
                    'error_message' => json_last_error_msg(),
                    'response_length' => strlen($response)
                ] : null
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }
    
    // 检查API返回状态
    if ($data['code'] !== '200') {
        echo json_encode([
            'success' => false,
            'message' => '城市信息API返回错误：' . $data['code'],
            'code' => intval($data['code']),
            'response' => $data
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 输出完整的API响应
    echo json_encode([
        'success' => true,
        'message' => '成功获取城市信息',
        'data' => $data,
        'debug' => [
            'request_url' => $apiUrl,
            'duration_ms' => $duration
        ]
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    // 处理异常
    echo json_encode([
        'success' => false,
        'message' => '城市信息查询异常：' . $e->getMessage(),
        'code' => 500
    ], JSON_UNESCAPED_UNICODE);
} 