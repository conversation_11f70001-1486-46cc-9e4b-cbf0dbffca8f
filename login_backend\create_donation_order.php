<?php
/**
 * 创建打赏订单API
 * 
 * 用于创建微信支付打赏订单
 * 
 * 请求方法：POST
 * 请求参数：
 * - amount: 打赏金额(元)
 * 
 * 返回：
 * {
 *   "error": false,
 *   "data": {
 *     "order_id": "订单号",
 *     "amount": 金额,
 *     "pay_params": {
 *       "timeStamp": "时间戳",
 *       "nonceStr": "随机字符串",
 *       "package": "prepay_id=xxx",
 *       "signType": "RSA",
 *       "paySign": "签名"
 *     }
 *   }
 * }
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';
require_once 'wx_pay_helper.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => '只支持POST请求'
    ]);
    exit;
}

// 验证用户身份
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供授权Token'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token包含Bearer前缀，去掉它
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    echo json_encode([
        'error' => true,
        'msg' => '无效的授权Token或Token已过期'
    ]);
    exit;
}

// 模拟用户不能打赏
if (isset($payload['openid']) && $payload['openid'] === 'mock_openid') {
    echo json_encode([
        'error' => true,
        'msg' => '体验账号不支持打赏功能，请登录后再试'
    ]);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['amount']) || !is_numeric($input['amount'])) {
    echo json_encode([
        'error' => true,
        'msg' => '请求参数不完整或金额格式不正确'
    ]);
    exit;
}

$amount = floatval($input['amount']);
$userId = $payload['sub'];

// 验证打赏金额
if ($amount <= 0) {
    echo json_encode([
        'error' => true,
        'msg' => '打赏金额必须大于0'
    ]);
    exit;
}

// 限制最大打赏金额为1000元
if ($amount > 1000) {
    echo json_encode([
        'error' => true,
        'msg' => '单次打赏金额不能超过1000元'
    ]);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 获取用户信息
    $stmt = $conn->prepare("SELECT nickname, avatar_url FROM users WHERE id = :user_id");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $userNickname = $user ? $user['nickname'] : '';
    $userAvatarUrl = $user ? $user['avatar_url'] : '';
    
    // 创建微信支付订单
    $wxPayHelper = new WxPayHelper();
    
    // 获取用户IP
    $userIp = $_SERVER['REMOTE_ADDR'];
    
    // 生成商户订单号
    $outTradeNo = $wxPayHelper->generateDonationOrderNo($userId);
    
    // 订单描述
    $description = "给开发者打赏";
    
    // 金额换算为分
    $amountInCents = intval($amount * 100);
    
    // 构建请求数据
    $requestData = [
        'appid' => WX_APPID,
        'mchid' => WX_MCH_ID,
        'description' => $description,
        'out_trade_no' => $outTradeNo,
        'notify_url' => WX_PAY_NOTIFY_URL,
        'amount' => [
            'total' => $amountInCents,
            'currency' => 'CNY'
        ],
        'payer' => [
            'openid' => $wxPayHelper->getUserOpenId($userId)
        ]
    ];
    
    // 调用微信支付API
    $result = $wxPayHelper->callWxPayApi('v3/pay/transactions/jsapi', $requestData);
    
    if (isset($result['error']) && $result['error']) {
        echo json_encode([
            'error' => true,
            'msg' => $result['msg']
        ]);
        exit;
    }
    
    // 获取预支付ID
    $prepayId = $result['prepay_id'];
    
    // 生成小程序支付参数
    $payParams = $wxPayHelper->generatePayParams($prepayId);
    
    // 检查donations表是否存在
    $checkTableStmt = $conn->query("SHOW TABLES LIKE 'donations'");
    if ($checkTableStmt->rowCount() === 0) {
        // 表不存在，创建表
        $createTableSql = file_get_contents(__DIR__ . '/create_donations_table.sql');
        $conn->exec($createTableSql);
    }
    
    // 插入打赏记录
    $stmt = $conn->prepare("
        INSERT INTO donations (
            user_id, 
            nickname, 
            avatar_url,
            amount, 
            order_id, 
            status, 
            created_at
        ) VALUES (
            :user_id,
            :nickname,
            :avatar_url,
            :amount,
            :order_id,
            'pending',
            NOW()
        )
    ");
    
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':nickname', $userNickname);
    $stmt->bindParam(':avatar_url', $userAvatarUrl);
    $stmt->bindParam(':amount', $amount);
    $stmt->bindParam(':order_id', $outTradeNo);
    
    $stmt->execute();
    
    // 返回订单信息和支付参数
    echo json_encode([
        'error' => false,
        'data' => [
            'order_id' => $outTradeNo,
            'amount' => $amount,
            'pay_params' => $payParams
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '创建打赏订单失败: ' . $e->getMessage()
    ]);
} 