<?php
/**
 * 城市数据CSV转JSON转换工具
 * 
 * 将城市表CSV文件转换为JSON格式，用于快速查询
 */

// 加载配置和工具函数
require_once 'config.php';
require_once 'city_utils.php';

// 获取执行路径
$scriptPath = __DIR__;
$csvFile = $scriptPath . '/json/China-City-List-latest.csv';
$jsonFile = $scriptPath . '/json/city_map.json';

// 显示开始信息
echo "开始转换CSV城市数据为JSON格式...\n";
echo "CSV文件: $csvFile\n";
echo "JSON输出: $jsonFile\n";

// 检查CSV文件是否存在
if (!file_exists($csvFile)) {
    echo "错误: CSV文件不存在!\n";
    exit(1);
}

// 读取CSV文件
echo "读取CSV文件...\n";
$content = file_get_contents($csvFile);
if ($content === false) {
    echo "错误: 无法读取CSV文件!\n";
    exit(1);
}

// 将内容拆分为行
$lines = explode("\n", $content);
$totalLines = count($lines);
echo "CSV文件共 $totalLines 行\n";

// 移除第一行（标题行）
array_shift($lines);
$totalLines--;
echo "去掉标题行，剩余 $totalLines 行数据\n";

$cityMap = [];
$counter = 0;

// 处理每一行
foreach ($lines as $index => $line) {
    $line = trim($line);
    if (empty($line)) continue;
    
    $fields = str_getcsv($line);
    if (count($fields) < 3) {
        continue; // 跳过格式不正确的行
    }
    
    $cityId = trim($fields[0]);
    $cityName = trim($fields[1]);
    
    // 确保城市ID有效
    if (empty($cityId) || !preg_match('/^\d+$/', $cityId)) {
        continue;
    }
    
    // 提取关键字段
    $adm1 = isset($fields[6]) ? trim($fields[6]) : '';
    $adm2 = isset($fields[7]) ? trim($fields[7]) : '';
    $lat = isset($fields[9]) ? trim($fields[9]) : '';
    $lon = isset($fields[10]) ? trim($fields[10]) : '';
    
    // 存储城市信息
    $cityMap[$cityId] = [
        'id' => $cityId,
        'name' => $cityName,
        'adm1' => $adm1,
        'adm2' => $adm2,
        'lat' => $lat,
        'lon' => $lon
    ];
    
    $counter++;
    
    // 显示处理进度（每1000个城市）
    if ($counter % 1000 === 0 || $counter === $totalLines) {
        echo "已处理 $counter / $totalLines 行数据...\n";
    }
}

// 输出一些关键城市信息作为验证
$keyCities = [
    '101010100' => '北京',
    '101020100' => '上海',
    '101210101' => '杭州'
];

echo "\n验证关键城市信息:\n";
foreach ($keyCities as $id => $name) {
    if (isset($cityMap[$id])) {
        echo "$id: {$cityMap[$id]['name']} ({$cityMap[$id]['adm1']} {$cityMap[$id]['adm2']})\n";
    } else {
        echo "$id: 未找到 ($name)\n";
    }
}

// 保存为JSON文件
echo "\n正在保存城市数据到JSON文件...\n";
$jsonDir = dirname($jsonFile);
if (!is_dir($jsonDir)) {
    if (!mkdir($jsonDir, 0755, true)) {
        echo "错误: 无法创建目录: $jsonDir\n";
        exit(1);
    }
}

$result = file_put_contents($jsonFile, json_encode($cityMap, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

if ($result === false) {
    echo "错误: 保存JSON文件失败!\n";
    exit(1);
}

echo "成功! 已将 " . count($cityMap) . " 个城市数据保存到 $jsonFile\n";
echo "JSON文件大小: " . filesize($jsonFile) . " 字节\n";
echo "转换完成!\n";
exit(0); 