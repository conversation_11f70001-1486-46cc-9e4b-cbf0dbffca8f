<?php
// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入数据库连接
require_once('db.php');
require_once('auth.php');

// 检查授权
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => '未授权访问',
        'code' => 401
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// 验证管理员token
try {
    $auth = new Auth();
    $payload = $auth->verifyAdminToken($token);
    
    if (!$payload) {
        echo json_encode([
            'error' => true,
            'msg' => '无效的授权令牌',
            'code' => 401
        ]);
        exit;
    }
    
    $db = new Database();
    $conn = $db->getConnection();
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => $e->getMessage(),
        'code' => 500
    ]);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => '请求方法不允许',
        'code' => 405
    ]);
    exit;
}

// 获取请求体
$requestBody = file_get_contents('php://input');
$data = json_decode($requestBody, true);

// 验证请求数据
if (!$data || !isset($data['id']) || !is_numeric($data['id'])) {
    echo json_encode([
        'error' => true,
        'msg' => '无效的请求参数',
        'code' => 400
    ]);
    exit;
}

$id = intval($data['id']);

try {
    // 检查公告是否存在
    $checkSql = "SELECT id FROM announcements WHERE id = :id";
    $stmt = $conn->prepare($checkSql);
    $stmt->bindValue(':id', $id, PDO::PARAM_INT);
    $stmt->execute();
    
    // 判断是否有数据
    $exists = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$exists) {
        echo json_encode([
            'error' => true,
            'msg' => '公告不存在',
            'code' => 404
        ]);
        exit;
    }
    
    // 删除公告
    $deleteSql = "DELETE FROM announcements WHERE id = :id";
    $stmt = $conn->prepare($deleteSql);
    $stmt->bindValue(':id', $id, PDO::PARAM_INT);
    $success = $stmt->execute();
    
    if (!$success) {
        throw new Exception('删除公告失败');
    }
    
    // 返回成功结果
    echo json_encode([
        'error' => false,
        'msg' => '公告已成功删除',
        'code' => 200
    ]);
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => $e->getMessage(),
        'code' => 500
    ]);
}
?> 