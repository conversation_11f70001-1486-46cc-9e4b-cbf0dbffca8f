Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentCategory: 'all', // 当前选中的分类
    clothingList: [], // 衣物列表（显示用）
    allClothingList: [], // 所有衣物的完整列表（用于过滤）
    selectedClothes: [], // 已选中的衣物ID列表
    hasSelected: false, // 是否有选中的衣物
    loading: true, // 加载状态
    showClothingName: false // 是否显示衣物名称
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const app = getApp();
    // 从全局获取显示名称的设置
    this.setData({
      showClothingName: app.globalData.showClothingName || false
    });
    
    // 页面加载时获取衣物列表
    this.loadClothingList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    const app = getApp();
    // 更新显示名称设置
    this.setData({
      showClothingName: app.globalData.showClothingName || false
    });
    
    // 页面显示时刷新数据（可能从编辑页返回）
    this.loadClothingList();
  },

  /**
   * 加载衣物列表
   */
  loadClothingList: function () {
    const app = getApp();
    this.setData({ loading: true });
    
    // 打印调试信息
    console.log('正在获取衣物列表...');
    console.log('API URL:', `${app.globalData.apiBaseUrl}/get_clothes.php`);
    console.log('Token:', app.globalData.token);
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothes.php`,
      method: 'GET',
      header: {
        'content-type': 'application/json',
        'Authorization': app.globalData.token // 去掉Bearer前缀，直接使用token
      },
      success: (res) => {
        // 打印响应数据，便于调试
        console.log('获取衣物列表响应:', res);
        
        // 检查响应状态
        if (res.statusCode === 200) {
          // 即使data属性为空也先处理
          const clothingData = res.data.data || [];
          console.log('衣物数据:', clothingData);
          
          // 添加selected属性用于UI状态管理
          const clothingList = clothingData.map(item => ({
            ...item,
            selected: false,
            id: item.id || '',
            image_url: item.image_url || item.imageUrl || '', // 兼容两种字段命名
            category: item.category || 'other'
          }));
          
          this.setData({
            clothingList: clothingList,
            allClothingList: clothingList, // 保存一份完整的衣物列表
            loading: false
          });
          
          // 应用分类过滤
          this.filterByCategory();
          
          // 检查是否有数据
          if (clothingList.length === 0) {
            wx.showToast({
              title: '暂无衣物数据',
              icon: 'none'
            });
          }
        } else {
          console.error('获取衣物列表失败:', res);
          wx.showToast({
            title: '获取衣物列表失败: ' + (res.data.msg || '未知错误'),
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: (err) => {
        console.error('网络请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  /**
   * 切换分类
   */
  switchCategory: function (e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      currentCategory: category
    });
    
    // 过滤显示当前分类的衣物
    this.filterByCategory();
  },

  /**
   * 根据分类过滤衣物
   */
  filterByCategory: function () {
    const { currentCategory, allClothingList } = this.data;
    
    console.log('过滤分类:', currentCategory, '现有衣物:', allClothingList.length);
    
    if (currentCategory === 'all') {
      // 全部分类不需要过滤
      this.setData({
        clothingList: allClothingList
      });
      console.log('显示全部衣物:', allClothingList.length);
    } else {
      // 根据分类过滤
      const filteredList = allClothingList.filter(item => {
        return item.category === currentCategory;
      });
      
      this.setData({
        clothingList: filteredList
      });
      console.log('过滤后衣物:', filteredList.length, '分类:', currentCategory);
    }
  },

  /**
   * 切换选中状态
   */
  toggleSelect: function (e) {
    const id = e.currentTarget.dataset.id;
    const { clothingList, allClothingList } = this.data;
    
    // 更新当前显示列表中的选中状态
    const updatedList = clothingList.map(item => {
      if (item.id === id) {
        return { ...item, selected: !item.selected };
      }
      return item;
    });
    
    // 同时更新完整列表中对应项的选中状态
    const updatedAllList = allClothingList.map(item => {
      if (item.id === id) {
        return { ...item, selected: !item.selected };
      }
      return item;
    });
    
    // 获取所有选中的衣物ID
    const selectedClothes = updatedList.filter(item => item.selected).map(item => item.id);
    const hasSelected = selectedClothes.length > 0;
    
    this.setData({
      clothingList: updatedList,
      allClothingList: updatedAllList,
      selectedClothes,
      hasSelected
    });
  },

  /**
   * 编辑选中的衣物
   * 目前只支持一次编辑一件衣物
   */
  editSelected: function () {
    const { selectedClothes } = this.data;
    
    if (selectedClothes.length !== 1) {
      wx.showToast({
        title: '一次只能编辑一件衣物',
        icon: 'none'
      });
      return;
    }
    
    const clothingId = selectedClothes[0];
    console.log('准备编辑衣物ID:', clothingId);
    
    wx.navigateTo({
      url: `/pages/clothing/edit/edit?id=${clothingId}`,
      events: {
        // 监听编辑页面返回事件
        editComplete: () => {
          // 编辑完成后刷新列表
          this.loadClothingList();
        }
      },
      success: () => {
        console.log('成功跳转到编辑页面');
      }
    });
  },

  /**
   * 删除选中的衣物
   */
  deleteSelected: function () {
    const { selectedClothes } = this.data;
    
    if (selectedClothes.length === 0) {
      wx.showToast({
        title: '请至少选择一件衣物',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedClothes.length} 件衣物吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performDelete();
        }
      }
    });
  },

  /**
   * 执行删除操作
   */
  performDelete: function () {
    const app = getApp();
    const { selectedClothes } = this.data;
    
    wx.showLoading({
      title: '删除中...',
    });
    
    console.log('准备删除衣物:', selectedClothes);
    
    // 对每个选中的ID发送删除请求
    const deletePromises = selectedClothes.map(id => {
      return new Promise((resolve, reject) => {
        console.log(`发送删除请求，衣物ID=${id}`);
        wx.request({
          url: `${app.globalData.apiBaseUrl}/delete_clothing.php`,
          method: 'POST',
          data: { id: id },
          header: {
            'content-type': 'application/json',
            'Authorization': app.globalData.token
          },
          success: (res) => {
            console.log(`删除衣物ID=${id}的响应:`, res);
            
            // 处理404错误 - 当API路径不存在时
            if (res.statusCode === 404) {
              console.error(`删除API不存在(404), 衣物ID=${id}`);
              reject('删除API未找到，请联系管理员');
              return;
            }
            
            // 尝试解析JSON响应
            let responseData = res.data;
            if (typeof responseData === 'string' && responseData.includes('<html>')) {
              console.error('返回了HTML而不是JSON:', responseData.substring(0, 100));
              reject('服务器返回了无效格式，请联系管理员');
              return;
            }
            
            // 检查是否成功，后端可能返回error=false表示成功
            if (res.statusCode === 200 && (responseData.error === false || !responseData.error)) {
              resolve();
            } else {
              console.error(`删除衣物ID=${id}失败:`, responseData.msg || '未知错误');
              reject(responseData.msg || '删除失败');
            }
          },
          fail: (err) => {
            console.error(`删除衣物ID=${id}网络请求失败:`, err);
            reject('网络错误');
          }
        });
      });
    });
    
    // 处理所有删除请求
    Promise.all(deletePromises)
      .then(() => {
        wx.hideLoading();
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        // 清空选中状态
        this.setData({
          selectedClothes: [],
          hasSelected: false
        });
        
        // 重新加载列表
        this.loadClothingList();
      })
      .catch((error) => {
        wx.hideLoading();
        console.error('删除操作失败:', error);
        wx.showToast({
          title: typeof error === 'string' ? error : '删除失败，请重试',
          icon: 'none',
          duration: 2000
        });
      });
  },

  /**
   * 返回上一页
   */
  navigateBack: function () {
    wx.navigateBack({
      fail: function() {
        // 如果返回失败，可能是直接打开的管理页，则跳转到首页
        wx.switchTab({
          url: '/pages/index/index'
        });
      }
    });
  }
}); 