// pages/wardrobes/detail/detail.js
const app = getApp();

Page({
    /**
     * 页面的初始数据
     */
    data: {
        wardrobe: {}, // 衣橱数据
        loading: true, // 加载状态
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        console.log('衣橱详情页参数:', options);
        
        if (options.id) {
            // 保存衣橱ID
            this.setData({
                'wardrobe.id': options.id
            });
            
            // 如果有name参数，使用it
            if (options.name) {
                const decodedName = decodeURIComponent(options.name);
                this.setData({
                    'wardrobe.name': decodedName
                });
            }
            
            // 如果有description参数，使用it
            if (options.description) {
                const decodedDescription = decodeURIComponent(options.description);
                this.setData({
                    'wardrobe.description': decodedDescription
                });
            }
            
            // 获取衣橱详情
            this.getWardrobeDetails(options.id);
        } else {
            wx.showToast({
                title: '缺少衣橱ID',
                icon: 'none'
            });
            
            setTimeout(() => {
                wx.navigateBack();
            }, 1500);
        }
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    },

    /**
     * 获取衣橱详情
     */
    getWardrobeDetails(wardrobeId) {
        // TODO: 调用API获取衣橱详情
        this.setData({ loading: false });
    },

    /**
     * 跳转到衣物列表页
     */
    viewClothes() {
        const { wardrobe } = this.data;
        
        if (!wardrobe.id) {
            wx.showToast({
                title: '衣橱ID无效',
                icon: 'none'
            });
            return;
        }
        
        // 跳转到衣物列表页
        wx.navigateTo({
            url: `/pages/wardrobes/clothes/clothes?id=${wardrobe.id}&name=${encodeURIComponent(wardrobe.name || '衣橱')}`
        });
    },

    /**
     * 编辑衣橱
     */
    editWardrobe() {
        const { wardrobe } = this.data;
        
        if (!wardrobe.id) {
            wx.showToast({
                title: '衣橱ID无效',
                icon: 'none'
            });
            return;
        }
        
        // 跳转到编辑页
        wx.navigateTo({
            url: `/pages/wardrobes/edit/edit?id=${wardrobe.id}&name=${encodeURIComponent(wardrobe.name || '')}&description=${encodeURIComponent(wardrobe.description || '')}`
        });
    }
})