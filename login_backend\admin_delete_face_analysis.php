<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查Authorization头是否存在
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '无效或已过期的令牌']);
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => '不支持的请求方法']);
    exit();
}

// 检查请求参数
if (!isset($_POST['action']) || $_POST['action'] !== 'delete' || !isset($_POST['analysis_id'])) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => '缺少必要参数']);
    exit();
}

// 获取分析ID
$analysisId = intval($_POST['analysis_id']);
if ($analysisId <= 0) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => '无效的分析ID']);
    exit();
}

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查记录是否存在
    $stmt = $conn->prepare("SELECT id FROM face_analysis WHERE id = :id");
    $stmt->bindParam(':id', $analysisId);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        http_response_code(404);
        echo json_encode(['status' => 'error', 'message' => '分析记录不存在']);
        exit();
    }
    
    // 删除记录
    $stmt = $conn->prepare("DELETE FROM face_analysis WHERE id = :id");
    $stmt->bindParam(':id', $analysisId);
    $result = $stmt->execute();
    
    if ($result) {
        // 删除成功
        echo json_encode([
            'status' => 'success',
            'message' => '分析记录已成功删除'
        ]);
    } else {
        // 删除失败
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => '删除分析记录失败: ' . implode(' ', $stmt->errorInfo())
        ]);
    }
} catch (Exception $e) {
    // 异常处理
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => '服务器错误: ' . $e->getMessage()
    ]);
} 