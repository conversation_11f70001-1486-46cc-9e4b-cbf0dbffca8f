const app = getApp();

Page({
  data: {
    name: '',
    description: '',
    submitting: false, // 添加提交状态标志
    categories: [], // 添加分类列表
    categoryIndex: 0, // 选中的分类索引
    categoryId: null, // 选中的分类ID
    categoryName: '默认分类', // 选中的分类名称
    loadingCategories: false, // 分类加载状态
    isUserCancelled: false, // 用户是否主动取消创建
    fromCalendar: false, // 标记是否来自日历页面
    calendarDate: null // 选中的日历日期
  },
  
  onLoad: function(options) {
    // 获取参数中的fromCalendar标记，判断是否来自日历页面
    const fromCalendar = options.fromCalendar === 'true';
    
    // 如果来自日历页面，从全局变量获取选中的日期
    if (fromCalendar && app.globalData.selectedCalendarDate) {
      this.setData({
        fromCalendar: true,
        calendarDate: app.globalData.selectedCalendarDate
      });
    }
    
    // 加载分类列表
    this.loadCategories();
  },
  
  onUnload: function() {
    // 清除全局变量中的选中日期，避免影响下次创建
    if (app.globalData.selectedCalendarDate) {
      app.globalData.selectedCalendarDate = null;
    }
    
    // 如果用户没有点击"创建穿搭"按钮，也没有主动点击取消按钮，则认为是直接返回
    // 这种情况下不需要创建空白穿搭
    console.log("页面卸载，用户取消状态:", this.data.isUserCancelled);
  },
  
  onShow: function() {
    // 当从创建分类页面返回时，重新加载分类列表
    if (app.globalData.needRefreshOutfitCategories) {
      this.loadCategories();
      app.globalData.needRefreshOutfitCategories = false;
    }
  },
  
  // 加载分类列表
  loadCategories: function() {
    // 如果已经在加载中，直接返回
    if (this.data.loadingCategories) {
      return;
    }
    
    this.setData({ loadingCategories: true });
    
    // 检查登录状态
    if (!app.globalData.token) {
      this.setData({ 
        categories: [],
        loadingCategories: false
      });
      return;
    }
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_outfit_categories.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      data: {
        page: 1,
        per_page: 100 // 获取足够多的分类
      },
      success: (res) => {
        console.log('获取穿搭分类列表:', res.data);
        
        if (res.statusCode === 200 && res.data.success) {
          const categories = res.data.data || [];
          
          // 查找默认分类
          const defaultCategory = categories.find(item => item.is_default == 1);
          
          if (defaultCategory) {
            // 如果有默认分类，设置为选中状态
            const defaultIndex = categories.findIndex(item => item.id === defaultCategory.id);
            
            this.setData({
              categories: categories,
              categoryIndex: defaultIndex >= 0 ? defaultIndex : 0,
              categoryId: defaultCategory.id,
              categoryName: defaultCategory.name
            });
          } else if (categories.length > 0) {
            // 如果没有默认分类但有其他分类，选择第一个
            this.setData({
              categories: categories,
              categoryIndex: 0,
              categoryId: categories[0].id,
              categoryName: categories[0].name
            });
          } else {
            // 没有任何分类
            this.setData({
              categories: categories,
              categoryIndex: 0,
              categoryId: null,
              categoryName: '默认分类'
            });
          }
        }
      },
      fail: (err) => {
        console.error('获取分类列表失败:', err);
      },
      complete: () => {
        this.setData({ loadingCategories: false });
      }
    });
  },
  
  // 分类选择改变事件
  onCategoryChange: function(e) {
    const index = Number(e.detail.value);
    const category = this.data.categories[index];
    
    this.setData({
      categoryIndex: index,
      categoryId: category.id,
      categoryName: category.name
    });
  },
  
  // 跳转到创建分类页面
  goToAddCategory: function() {
    wx.navigateTo({
      url: '/pages/outfit_categories/add/add'
    });
  },
  
  // 输入穿搭名称
  onNameInput: function(e) {
    this.setData({
      name: e.detail.value
    });
  },
  
  // 输入穿搭描述
  onDescriptionInput: function(e) {
    this.setData({
      description: e.detail.value
    });
  },
  
  // 创建穿搭并前往编辑页
  createOutfit: function() {
    // 检查名称是否为空
    if (!this.data.name.trim()) {
      wx.showToast({
        title: '请输入穿搭名称',
        icon: 'none'
      });
      return;
    }
    
    // 设置提交状态，防止重复提交
    this.setData({ submitting: true });
    
    // 创建新穿搭对象
    const now = new Date();
    const timestamp = Date.now();
    
    // 为了与后端兼容，这里我们简化ID格式，使用纯数字
    // 服务器会分配自己的ID，这个ID只是临时使用
    const tempId = `tmp_${timestamp}`;
    
    console.log('创建新穿搭，临时ID:', tempId);
    
    // 从全局变量获取选中的日历日期
    const calendarDate = this.data.fromCalendar ? app.globalData.selectedCalendarDate : null;
    console.log('关联的日历日期:', calendarDate);
    
    const outfit = {
      id: tempId,
      name: this.data.name.trim(),
      description: this.data.description.trim(),
      category_id: this.data.categoryId, // 添加分类ID
      thumbnail: '', // 初始无缩略图
      created_at: now.toISOString(),
      updated_at: now.toISOString(),
      items: [], // 初始无衣物
      forceAdd: true, // 强制添加，即使没有衣物
      calendar_date: calendarDate // 设置选中的日历日期
    };
    
    console.log('穿搭数据:', outfit);
    
    // 保存穿搭，包括同步到服务器
    app.saveOutfit(outfit, (result) => {
      this.setData({ submitting: false });
      
      if (result.success) {
        // 使用服务器返回的数据（包含服务器生成的ID）
        const savedOutfit = result.data || outfit;
        console.log('保存成功，服务器返回的穿搭数据:', savedOutfit);
        
        // 修改：无论是否从日历页面来，都先跳转到编辑页面
        // 通过参数传递fromCalendar标记，以便编辑完成后可以返回日历
        wx.navigateTo({
          url: `/pages/outfits/edit/edit?id=${savedOutfit.id}${this.data.fromCalendar ? '&fromCalendar=true' : ''}`
        });
      } else {
        console.error('创建穿搭失败:', result.error);
        wx.showToast({
          title: result.error || '创建穿搭失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 取消创建
  cancel: function() {
    this.setData({
      isUserCancelled: true
    });
    wx.navigateBack();
  }
}); 