<view class="container">
  <view class="form-header">
    <view class="step-indicator">
      <view class="step active">1</view>
      <view class="step-line"></view>
      <view class="step">2</view>
      <view class="step-line"></view>
      <view class="step">3</view>
    </view>
    <view class="step-text">填写身材数据</view>
  </view>
  
  <scroll-view scroll-y class="form-container">
    <view class="section">
      <view class="section-title">基本信息</view>
      
      <view class="form-item">
        <view class="form-label">性别</view>
        <view class="gender-selector">
          <view class="gender-option {{gender == 1 ? 'active' : ''}}" bindtap="selectGender" data-gender="1">
            <view class="gender-icon">👨</view>
            <view class="gender-text">男</view>
          </view>
          <view class="gender-option {{gender == 2 ? 'active' : ''}}" bindtap="selectGender" data-gender="2">
            <view class="gender-icon">👩</view>
            <view class="gender-text">女</view>
          </view>
        </view>
      </view>
      
      <view class="form-item">
        <view class="form-label required">身高 (cm)</view>
        <input class="form-input" type="number" value="{{height}}" bindinput="inputHeight" placeholder="请输入身高，如：175"/>
      </view>
      
      <view class="form-item">
        <view class="form-label required">体重 (kg)</view>
        <input class="form-input" type="number" value="{{weight}}" bindinput="inputWeight" placeholder="请输入体重，如：65"/>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">身材数据</view>
      <view class="subtitle">选填项，提供更多数据有助于获得更精准的分析</view>
      
      <view class="form-item">
        <view class="form-label">胸围 (cm)</view>
        <input class="form-input" type="number" value="{{bust}}" bindinput="inputBust" placeholder="请输入胸围"/>
      </view>
      
      <view class="form-item">
        <view class="form-label">腰围 (cm)</view>
        <input class="form-input" type="number" value="{{waist}}" bindinput="inputWaist" placeholder="请输入腰围"/>
      </view>
      
      <view class="form-item">
        <view class="form-label">臀围 (cm)</view>
        <input class="form-input" type="number" value="{{hips}}" bindinput="inputHips" placeholder="请输入臀围"/>
      </view>
      
      <view class="form-item">
        <view class="form-label">肩宽 (cm)</view>
        <input class="form-input" type="number" value="{{shoulderWidth}}" bindinput="inputShoulderWidth" placeholder="请输入肩宽"/>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">外观特征</view>
      
      <view class="form-item">
        <view class="form-label">肤色</view>
        <input class="form-input" type="text" value="{{skinTone}}" bindinput="inputSkinTone" placeholder="例如：偏白、小麦色、黄皮等"/>
      </view>
      
      <view class="form-item">
        <view class="form-label">脸型</view>
        <input class="form-input" type="text" value="{{faceShape}}" bindinput="inputFaceShape" placeholder="例如：圆脸、方脸、瓜子脸等"/>
      </view>
      
      <view class="form-item">
        <view class="form-label">体型</view>
        <input class="form-input" type="text" value="{{bodyShape}}" bindinput="inputBodyShape" placeholder="例如：梨形、苹果形、沙漏型等"/>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">照片上传</view>
      <view class="subtitle">上传1-3张清晰的照片，有助于更准确的分析</view>
      
      <view class="photo-upload-area">
        <view class="photo-tip">
          <text>建议上传：</text>
          <text>1. 全身站姿照片</text>
          <text>2. 正面半身照片</text>
          <text>3. 侧面照片</text>
        </view>
        
        <view class="photo-list">
          <view class="photo-item" wx:for="{{photos}}" wx:key="index">
            <image src="{{item}}" mode="aspectFill" class="photo-image"></image>
            <view class="photo-delete" catchtap="deletePhoto" data-index="{{index}}">×</view>
          </view>
          
          <view class="photo-add" bindtap="addPhoto" wx:if="{{photos.length < 3}}">
            <view class="add-icon">+</view>
            <view class="add-text">添加照片</view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">其他信息</view>
      
      <view class="form-item">
        <view class="form-label">备注信息</view>
        <textarea class="form-textarea" value="{{remarks}}" bindinput="inputRemarks" placeholder="请输入您希望分析师了解的其他信息，如喜欢的风格、职业场合需求等"></textarea>
      </view>
    </view>
    
    <view class="privacy-notice">
      <text>提交信息即表示您同意我们收集并使用您提供的数据用于形象分析服务。您的照片和个人数据将严格保密。</text>
    </view>
  </scroll-view>
  
  <view class="form-footer">
    <button class="next-btn" bindtap="nextStep">下一步</button>
  </view>
</view> 