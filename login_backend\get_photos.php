<?php
/**
 * Get Photos API
 * 
 * Retrieves a user's photos based on their authorization token
 * Supports optional photo ID filtering
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * GET Parameters:
 * - id (optional): Filter by specific photo item ID
 * 
 * Response:
 * {
 *   "error": false,
 *   "data": [
 *     {
 *       "id": 1,
 *       "image_url": "https://example.com/photo.jpg",
 *       "type": "full",
 *       "description": "Beach day",
 *       "created_at": "2023-03-31 12:00:00"
 *     },
 *     ...
 *   ]
 * }
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// Set response content type
header('Content-Type: application/json');

// Handle CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if Authorization header exists
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// Verify token
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid or expired token'
    ]);
    exit;
}

// Get user ID from token data
$userId = $tokenData['sub'];

// Get optional photo ID filter
$photoId = isset($_GET['id']) ? $_GET['id'] : null;

try {
    $db = new Database();
    $conn = $db->getConnection();

    // 记录API调用信息
    if ($photoId) {
        error_log("获取单个照片详情: 用户ID=$userId, 照片ID=$photoId");
    } else {
        error_log("获取照片列表: 用户ID=$userId");
    }

    $sql = "SELECT id, image_url, type, description, created_at FROM photos WHERE user_id = :user_id";
    $params = ['user_id' => $userId];

    // Add ID filter if provided
    if ($photoId) {
        $sql .= " AND id = :id";
        $params['id'] = $photoId;
    }

    $sql .= " ORDER BY created_at DESC";
    
    // 记录SQL查询信息
    error_log("SQL查询: $sql");
    error_log("参数: " . json_encode($params));

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);

    $photos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 记录结果
    $count = count($photos);
    error_log("查询到 $count 张照片");

    // Return photos data
    echo json_encode([
        'error' => false,
        'data' => $photos
    ]);

} catch (PDOException $e) {
    // 详细记录错误信息
    $errorMessage = $e->getMessage();
    $errorCode = $e->getCode();
    error_log("获取照片列表错误: Code=$errorCode, Message=$errorMessage");

    // Return error response
    echo json_encode([
        'error' => true,
        'msg' => "获取照片失败: $errorCode - " . substr($errorMessage, 0, 100)
    ]);
}
?>
