<?php
/**
 * 删除邀请码API
 */
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

// 获取Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '需要授权']);
    exit;
}

// 验证token
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或过期的令牌']);
    exit;
}

// 获取管理员ID
$adminId = $payload['admin_id'];

// 检查管理员是否存在
$db = new Database();
$conn = $db->getConnection();
$adminStmt = $conn->prepare("SELECT id, username FROM admin_users WHERE id = :id");
$adminStmt->bindParam(':id', $adminId);
$adminStmt->execute();
$admin = $adminStmt->fetch(PDO::FETCH_ASSOC);

if (!$admin) {
    http_response_code(403);
    echo json_encode(['error' => true, 'msg' => '权限不足']);
    exit;
}

// 获取请求数据
$rawData = file_get_contents('php://input');
$requestData = json_decode($rawData, true);

// 验证参数
if (!isset($requestData['id']) || !is_numeric($requestData['id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '无效的ID']);
    exit;
}

$id = (int)$requestData['id'];

try {
    // 首先检查邀请码是否存在且未使用
    $checkSql = "SELECT status FROM invitation_codes WHERE id = :id";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bindParam(':id', $id, PDO::PARAM_INT);
    $checkStmt->execute();
    $code = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$code) {
        http_response_code(404);
        echo json_encode(['error' => true, 'msg' => '未找到邀请码']);
        exit;
    }
    
    if ($code['status'] !== 'unused') {
        http_response_code(400);
        echo json_encode(['error' => true, 'msg' => '只能删除未使用的邀请码']);
        exit;
    }
    
    // 删除邀请码
    $sql = "DELETE FROM invitation_codes WHERE id = :id AND status = 'unused'";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':id', $id, PDO::PARAM_INT);
    $stmt->execute();
    
    $rowCount = $stmt->rowCount();
    
    if ($rowCount === 0) {
        http_response_code(400);
        echo json_encode(['error' => true, 'msg' => '删除失败，邀请码不存在或已被使用']);
        exit;
    }
    
    // 返回结果
    echo json_encode([
        'error' => false,
        'msg' => '邀请码删除成功'
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '删除邀请码失败: ' . $e->getMessage()]);
} 