/* pages/wardrobes/edit/edit.wxss */
.container {
  padding: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
}

.form-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 40rpx;
}

.form-group {
  margin-bottom: 30rpx;
  position: relative;
}

.form-label {
  font-size: 28rpx;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.required {
  color: #333;
  font-weight: bold;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  border: 1px solid #ebebeb;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  border: 1px solid #ebebeb;
}

.character-count {
  position: absolute;
  right: 10rpx;
  bottom: -24rpx;
  font-size: 22rpx;
  color: #999;
}

.form-hint {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

.button-container {
  display: flex;
  justify-content: space-between;
  margin-top: 60rpx;
}

.cancel-btn, .submit-btn {
  width: 45%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #333;
}

.submit-btn {
  background-color: #333;
  color: #fff;
}

.submit-btn[disabled] {
  background-color: #cccccc;
  color: #ffffff;
}