-- Create api_usage table to track API quota usage
CREATE TABLE IF NOT EXISTS `api_usage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_name` varchar(50) NOT NULL COMMENT '接口名称：try_on, photo_edit等',
  `total_quota` int(11) NOT NULL COMMENT '总配额数量',
  `used_quota` int(11) NOT NULL DEFAULT 0 COMMENT '已使用数量',
  `reset_date` date NULL COMMENT '配额重置日期',
  `last_used` datetime NULL COMMENT '最后使用时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `api_name` (`api_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 初始化数据
INSERT INTO `api_usage` (`api_name`, `total_quota`, `used_quota`, `reset_date`, `last_used`, `updated_at`) 
VALUES 
('try_on', 10000, 0, NULL, NULL, NOW()),
('photo_edit', 5000, 0, NULL, NULL, NOW());