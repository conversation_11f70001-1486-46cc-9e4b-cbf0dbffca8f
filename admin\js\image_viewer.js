/**
 * 通用图片查看器组件
 */
const ImageViewer = {
    // 初始状态
    isInitialized: false,
    isVisible: false, // 添加可见状态标记
    
    // HTML元素
    viewer: null,
    image: null,
    closeBtn: null,
    
    /**
     * 初始化图片查看器
     */
    init: function() {
        if (this.isInitialized) return;
        
        // 创建图片查看器DOM结构
        this.createViewerDOM();
        
        // 绑定事件
        this.bindEvents();
        
        this.isInitialized = true;
        console.log('ImageViewer 初始化成功');
    },
    
    /**
     * 创建图片查看器DOM结构
     */
    createViewerDOM: function() {
        // 创建查看器容器
        this.viewer = document.createElement('div');
        this.viewer.className = 'image-viewer';
        
        // 首先设置基本样式
        this.viewer.style.position = 'fixed';
        this.viewer.style.top = '0';
        this.viewer.style.left = '0';
        this.viewer.style.width = '100%';
        this.viewer.style.height = '100%';
        this.viewer.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
        this.viewer.style.zIndex = '9999';
        this.viewer.style.alignItems = 'center';
        this.viewer.style.justifyContent = 'center';
        
        // 确保初始状态为隐藏 - 最后设置显示属性
        this.viewer.style.display = 'none';
        
        // 创建图片元素
        this.image = document.createElement('img');
        this.image.className = 'viewer-image';
        this.image.style.cssText = 'max-width: 80%; max-height: 80%; object-fit: contain; ' + 
                                  'background-color: white; padding: 15px; border-radius: 8px; ' + 
                                  'box-shadow: 0 3px 15px rgba(0, 0, 0, 0.3);';
        this.viewer.appendChild(this.image);
        
        // 创建关闭按钮
        this.closeBtn = document.createElement('button');
        this.closeBtn.className = 'viewer-close';
        this.closeBtn.innerHTML = '&times;';
        this.closeBtn.style.cssText = 'position: absolute; top: 15px; right: 20px; ' +
            'background: none; border: none; color: white; font-size: 30px; ' +
            'cursor: pointer; z-index: 10000;';
        this.viewer.appendChild(this.closeBtn);
        
        // 将查看器添加到页面
        document.body.appendChild(this.viewer);
    },
    
    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 关闭按钮点击事件
        this.closeBtn.addEventListener('click', this.close.bind(this));
        
        // 点击图片外区域关闭查看器
        this.viewer.addEventListener('click', (e) => {
            if (e.target === this.viewer) {
                this.close();
            }
        });
        
        // ESC键关闭查看器
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible) {
                this.close();
            }
        });
    },
    
    /**
     * 显示图片
     * @param {String} imageSrc 图片地址
     */
    show: function(imageSrc) {
        if (!this.isInitialized) {
            this.init();
        }
        
        // 检查图片URL是否有效
        if (!imageSrc || typeof imageSrc !== 'string' || imageSrc.trim() === '') {
            console.error('无效的图片URL');
            return;
        }
        
        // 设置图片地址
        this.image.src = imageSrc;
        
        // 只有当图片加载成功后才显示查看器
        this.image.onload = () => {
            // 显示查看器
            this.viewer.style.display = 'flex';
            this.isVisible = true;
            
            // 阻止页面滚动
            document.body.style.overflow = 'hidden';
        };
        
        // 处理图片加载失败
        this.image.onerror = () => {
            console.error('图片加载失败:', imageSrc);
            alert('图片加载失败');
        };
    },
    
    /**
     * 公开方法：显示图片，可直接被外部调用
     * @param {String} imageSrc 图片地址
     */
    showImage: function(imageSrc) {
        this.show(imageSrc);
    },
    
    /**
     * 关闭查看器
     */
    close: function() {
        if (!this.isInitialized) return;
        
        // 隐藏查看器
        this.viewer.style.display = 'none';
        this.isVisible = false;
        
        // 恢复页面滚动
        document.body.style.overflow = '';
    },
    
    /**
     * 添加图片点击事件
     * @param {String|NodeList|Array} selector 选择器字符串或元素集合
     */
    bindImages: function(selector) {
        if (!this.isInitialized) {
            this.init();
        }
        
        // 延迟执行，确保DOM完全加载
        setTimeout(() => {
            let elements;
            if (typeof selector === 'string') {
                elements = document.querySelectorAll(selector);
            } else if (selector instanceof NodeList || Array.isArray(selector)) {
                elements = selector;
            } else {
                console.error('无效的选择器类型');
                return;
            }
            
            // 如果没有找到元素，直接返回
            if (elements.length === 0) {
                console.warn(`未找到匹配的元素: ${selector}`);
                return;
            }
            
            console.log(`为${elements.length}个元素绑定图片查看器`);
            
            elements.forEach(img => {
                // 确保元素上没有已经绑定过事件
                if (!img.dataset.hasImageViewer) {
                    img.style.cursor = 'pointer';
                    
                    // 绑定点击事件
                    img.addEventListener('click', (event) => {
                        // 阻止事件冒泡，避免触发其他事件
                        event.preventDefault();
                        event.stopPropagation();
                        
                        // 优先使用data-origin属性的URL（如果存在）
                        const imageUrl = img.getAttribute('data-origin') || img.src;
                        
                        // 只有当图片加载完成且有效时才显示
                        if (img.complete && img.naturalWidth > 0) {
                            this.show(imageUrl);
                        } else {
                            console.warn('图片尚未完全加载');
                        }
                    });
                    
                    // 标记已绑定
                    img.dataset.hasImageViewer = 'true';
                }
            });
        }, 500); // 使用较长的延迟时间，确保DOM完全加载
    }
}; 

// 页面加载完成后自动初始化ImageViewer
document.addEventListener('DOMContentLoaded', function() {
    // 确保ImageViewer能被全局访问
    if (typeof ImageViewer !== 'undefined') {
        // 提前初始化，确保在使用前已准备就绪
        ImageViewer.init();
        console.log('ImageViewer 加载成功');
    } else {
        console.error('ImageViewer 对象未找到！图片预览功能将不可用');
    }
}); 