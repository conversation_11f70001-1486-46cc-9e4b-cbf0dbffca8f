<?php
/**
 * 图片迁移脚本
 * 将现有的本地图片和远程URL图片迁移到阿里云OSS
 * 
 * 使用方法：直接通过命令行运行此脚本
 * php migrate_to_oss.php
 */

// 定义根目录
$rootDir = dirname(__DIR__);

// 加载配置文件
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/db.php';
require_once __DIR__ . '/oss_helper.php';
require_once $rootDir . '/vendor/aliyuncs/oss-sdk-php/autoload.php'; // 使用绝对路径引入SDK autoload

// 引入OSS命名空间
use OSS\OssClient;
use OSS\Core\OssException;

// 初始化数据库连接
$db = new Database();
$conn = $db->getConnection();

// 初始化OSS辅助类
$ossHelper = new OssHelper();

// 调试信息
echo "OSS配置信息:\n";
echo "ENDPOINT: " . (defined('ALIYUN_OSS_ENDPOINT') ? ALIYUN_OSS_ENDPOINT : '未定义') . "\n";
echo "BUCKET: " . (defined('ALIYUN_OSS_BUCKET') ? ALIYUN_OSS_BUCKET : '未定义') . "\n";
echo "DOMAIN: " . (defined('ALIYUN_OSS_BUCKET_DOMAIN') ? ALIYUN_OSS_BUCKET_DOMAIN : '未定义') . "\n";
echo "PHOTOS_PATH: " . (defined('OSS_PATH_PHOTOS') ? OSS_PATH_PHOTOS : '未定义') . "\n";
echo "CLOTHES_PATH: " . (defined('OSS_PATH_CLOTHES') ? OSS_PATH_CLOTHES : '未定义') . "\n";
echo "TRY_ON_PATH: " . (defined('OSS_PATH_TRY_ON') ? OSS_PATH_TRY_ON : '未定义') . "\n\n";

/**
 * 记录日志
 */
function log_message($message) {
    echo date('Y-m-d H:i:s') . " - " . $message . PHP_EOL;
    error_log($message);
}

/**
 * 迁移照片表中的图片
 */
function migrate_photos($conn, $ossHelper) {
    log_message("开始迁移photos表中的图片...");
    
    // 获取所有照片记录
    $stmt = $conn->query("SELECT id, image_url FROM photos");
    $photos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $totalCount = count($photos);
    $successCount = 0;
    $failCount = 0;
    
    log_message("共发现 {$totalCount} 条照片记录");
    
    foreach ($photos as $photo) {
        $id = $photo['id'];
        $imageUrl = $photo['image_url'];
        
        // 检查是否已经是OSS URL
        if ($ossHelper->isOssUrl($imageUrl)) {
            log_message("照片 #{$id} 已经是OSS URL，跳过");
            $successCount++;
            continue;
        }
        
        log_message("处理照片 #{$id}，URL: {$imageUrl}");
        
        // 解析URL，判断是本地文件还是远程URL
        $urlParts = parse_url($imageUrl);
        $isLocalFile = false;
        $localPath = null;
        
        if (isset($urlParts['path'])) {
            $relativePath = ltrim($urlParts['path'], '/');
            // 如果URL包含login_backend，提取其后部分
            if (strpos($relativePath, 'login_backend/') === 0) {
                $relativePath = substr($relativePath, strlen('login_backend/'));
                $localPath = __DIR__ . '/' . $relativePath;
                $isLocalFile = file_exists($localPath);
            }
        }
        
        // 生成OSS Key
        $filename = basename($imageUrl);
        if (empty($filename)) {
            $filename = 'photo_' . $id . '_' . time() . '_' . rand(1000, 9999) . '.jpg';
        }
        $ossKey = OSS_PATH_PHOTOS . $filename;
        
        $result = false;
        
        if ($isLocalFile && $localPath) {
            // 本地文件上传到OSS
            log_message("本地文件: {$localPath}");
            $result = $ossHelper->uploadFile($localPath, $ossKey);
        } else {
            // 远程URL下载到OSS
            log_message("远程URL: {$imageUrl}");
            $result = $ossHelper->downloadUrlToOss($imageUrl, $ossKey);
        }
        
        if ($result && $result['success']) {
            // 更新数据库中的URL
            $newUrl = $result['url'];
            $updateStmt = $conn->prepare("UPDATE photos SET image_url = :url WHERE id = :id");
            $updateResult = $updateStmt->execute([
                'url' => $newUrl,
                'id' => $id
            ]);
            
            if ($updateResult) {
                log_message("照片 #{$id} 迁移成功，新URL: {$newUrl}");
                $successCount++;
            } else {
                log_message("照片 #{$id} 已上传到OSS，但数据库更新失败");
                $failCount++;
            }
        } else {
            log_message("照片 #{$id} 迁移失败: " . ($result ? $result['error'] : '未知错误'));
            $failCount++;
        }
    }
    
    log_message("照片迁移完成，总数: {$totalCount}，成功: {$successCount}，失败: {$failCount}");
    return [
        'total' => $totalCount,
        'success' => $successCount,
        'fail' => $failCount
    ];
}

/**
 * 迁移衣物表中的图片
 */
function migrate_clothes($conn, $ossHelper) {
    log_message("开始迁移clothes表中的图片...");
    
    // 获取所有衣物记录
    $stmt = $conn->query("SELECT id, image_url FROM clothes");
    $clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $totalCount = count($clothes);
    $successCount = 0;
    $failCount = 0;
    
    log_message("共发现 {$totalCount} 条衣物记录");
    
    foreach ($clothes as $cloth) {
        $id = $cloth['id'];
        $imageUrl = $cloth['image_url'];
        
        // 检查是否已经是OSS URL
        if ($ossHelper->isOssUrl($imageUrl)) {
            log_message("衣物 #{$id} 已经是OSS URL，跳过");
            $successCount++;
            continue;
        }
        
        log_message("处理衣物 #{$id}，URL: {$imageUrl}");
        
        // 生成OSS Key
        $filename = basename($imageUrl);
        if (empty($filename)) {
            $filename = 'cloth_' . $id . '_' . time() . '_' . rand(1000, 9999) . '.jpg';
        }
        $ossKey = OSS_PATH_CLOTHES . $filename;
        
        // 远程URL下载到OSS
        $result = $ossHelper->downloadUrlToOss($imageUrl, $ossKey);
        
        if ($result && $result['success']) {
            // 更新数据库中的URL
            $newUrl = $result['url'];
            $updateStmt = $conn->prepare("UPDATE clothes SET image_url = :url WHERE id = :id");
            $updateResult = $updateStmt->execute([
                'url' => $newUrl,
                'id' => $id
            ]);
            
            if ($updateResult) {
                log_message("衣物 #{$id} 迁移成功，新URL: {$newUrl}");
                $successCount++;
            } else {
                log_message("衣物 #{$id} 已上传到OSS，但数据库更新失败");
                $failCount++;
            }
        } else {
            log_message("衣物 #{$id} 迁移失败: " . ($result ? $result['error'] : '未知错误'));
            $failCount++;
        }
    }
    
    log_message("衣物迁移完成，总数: {$totalCount}，成功: {$successCount}，失败: {$failCount}");
    return [
        'total' => $totalCount,
        'success' => $successCount,
        'fail' => $failCount
    ];
}

/**
 * 迁移试衣记录表中的图片
 */
function migrate_try_on_history($conn, $ossHelper) {
    // 检查表是否存在
    $tables = $conn->query("SHOW TABLES LIKE 'try_on_history'")->fetchAll();
    if (count($tables) === 0) {
        log_message("try_on_history表不存在，跳过");
        return [
            'total' => 0,
            'success' => 0,
            'fail' => 0
        ];
    }
    
    log_message("开始迁移try_on_history表中的图片...");
    
    // 获取所有试衣记录
    $stmt = $conn->query("SELECT id, result_image_url FROM try_on_history");
    $histories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $totalCount = count($histories);
    $successCount = 0;
    $failCount = 0;
    
    log_message("共发现 {$totalCount} 条试衣记录");
    
    foreach ($histories as $history) {
        $id = $history['id'];
        $imageUrl = $history['result_image_url'];
        
        // 检查是否已经是OSS URL
        if ($ossHelper->isOssUrl($imageUrl)) {
            log_message("试衣记录 #{$id} 已经是OSS URL，跳过");
            $successCount++;
            continue;
        }
        
        log_message("处理试衣记录 #{$id}，URL: {$imageUrl}");
        
        // 解析URL，判断是本地文件还是远程URL
        $urlParts = parse_url($imageUrl);
        $isLocalFile = false;
        $localPath = null;
        
        if (isset($urlParts['path'])) {
            $relativePath = ltrim($urlParts['path'], '/');
            // 如果URL包含login_backend，提取其后部分
            if (strpos($relativePath, 'login_backend/') === 0) {
                $relativePath = substr($relativePath, strlen('login_backend/'));
                $localPath = __DIR__ . '/' . $relativePath;
                $isLocalFile = file_exists($localPath);
            }
        }
        
        // 生成OSS Key
        $filename = basename($imageUrl);
        if (empty($filename)) {
            $filename = 'tryon_' . $id . '_' . time() . '_' . rand(1000, 9999) . '.jpg';
        }
        $ossKey = OSS_PATH_TRY_ON . $filename;
        
        $result = false;
        
        if ($isLocalFile && $localPath) {
            // 本地文件上传到OSS
            log_message("本地文件: {$localPath}");
            $result = $ossHelper->uploadFile($localPath, $ossKey);
        } else {
            // 远程URL下载到OSS
            log_message("远程URL: {$imageUrl}");
            $result = $ossHelper->downloadUrlToOss($imageUrl, $ossKey);
        }
        
        if ($result && $result['success']) {
            // 更新数据库中的URL
            $newUrl = $result['url'];
            $updateStmt = $conn->prepare("UPDATE try_on_history SET result_image_url = :url WHERE id = :id");
            $updateResult = $updateStmt->execute([
                'url' => $newUrl,
                'id' => $id
            ]);
            
            if ($updateResult) {
                log_message("试衣记录 #{$id} 迁移成功，新URL: {$newUrl}");
                $successCount++;
            } else {
                log_message("试衣记录 #{$id} 已上传到OSS，但数据库更新失败");
                $failCount++;
            }
        } else {
            log_message("试衣记录 #{$id} 迁移失败: " . ($result ? $result['error'] : '未知错误'));
            $failCount++;
        }
    }
    
    log_message("试衣记录迁移完成，总数: {$totalCount}，成功: {$successCount}，失败: {$failCount}");
    return [
        'total' => $totalCount,
        'success' => $successCount,
        'fail' => $failCount
    ];
}

// 执行迁移
try {
    log_message("开始图片迁移到OSS...");
    
    // 迁移照片
    $photosResult = migrate_photos($conn, $ossHelper);
    
    // 迁移衣物图片
    $clothesResult = migrate_clothes($conn, $ossHelper);
    
    // 迁移试衣结果图片
    $tryOnResult = migrate_try_on_history($conn, $ossHelper);
    
    // 输出迁移总结
    log_message("所有迁移完成！");
    log_message("照片：总数={$photosResult['total']}，成功={$photosResult['success']}，失败={$photosResult['fail']}");
    log_message("衣物：总数={$clothesResult['total']}，成功={$clothesResult['success']}，失败={$clothesResult['fail']}");
    log_message("试衣记录：总数={$tryOnResult['total']}，成功={$tryOnResult['success']}，失败={$tryOnResult['fail']}");
    
    $grandTotal = $photosResult['total'] + $clothesResult['total'] + $tryOnResult['total'];
    $grandSuccess = $photosResult['success'] + $clothesResult['success'] + $tryOnResult['success'];
    $grandFail = $photosResult['fail'] + $clothesResult['fail'] + $tryOnResult['fail'];
    
    log_message("总计：总数={$grandTotal}，成功={$grandSuccess}，失败={$grandFail}");
    
} catch (Exception $e) {
    log_message("迁移过程中发生错误: " . $e->getMessage());
}
?> 