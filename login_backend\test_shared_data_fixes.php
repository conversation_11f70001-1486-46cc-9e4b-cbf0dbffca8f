<?php
/**
 * 测试共享数据修复
 * 验证衣物查询和穿搭保存的修复效果
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    echo "=== 共享数据修复功能测试 ===\n";
    echo "当前用户ID: $userId\n\n";
    
    $db = new Database();
    $conn = $db->getConnection();
    
    // 1. 测试权限检查API
    echo "1. 测试权限检查API\n";
    
    // 获取一个共享衣物进行测试
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.user_id, c.circle_id
        FROM clothes c
        WHERE c.circle_id IN (
            SELECT circle_id FROM circle_members 
            WHERE user_id = :user_id AND status = 'active'
        )
        AND c.user_id != :user_id
        LIMIT 1
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $sharedClothes = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($sharedClothes) {
        echo "测试共享衣物: {$sharedClothes['name']} (ID: {$sharedClothes['id']})\n";
        
        // 测试权限检查API
        $apiUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/check_circle_permission.php";
        
        foreach (['view', 'edit', 'delete'] as $operation) {
            $params = http_build_query([
                'data_type' => 'clothes',
                'data_id' => $sharedClothes['id'],
                'operation' => $operation
            ]);
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "$apiUrl?$params");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: ' . $_SERVER['HTTP_AUTHORIZATION']
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                echo "- $operation: ❌ CURL错误: $error\n";
            } elseif ($httpCode !== 200) {
                echo "- $operation: ❌ HTTP错误: $httpCode\n";
                echo "  响应: $response\n";
            } else {
                $data = json_decode($response, true);
                if ($data && $data['status'] === 'success') {
                    $permission = $data['data'];
                    $status = $permission['allowed'] ? '✅' : '❌';
                    echo "- $operation: $status {$permission['reason']}\n";
                } else {
                    echo "- $operation: ❌ API错误: " . ($data['message'] ?? '未知错误') . "\n";
                    if (isset($data['debug'])) {
                        echo "  调试信息: " . json_encode($data['debug']) . "\n";
                    }
                }
            }
        }
    } else {
        echo "❌ 没有找到共享衣物进行测试\n";
    }
    
    // 2. 测试穿搭数据加载
    echo "\n2. 测试穿搭数据加载\n";
    
    // 获取一个共享穿搭进行测试
    $stmt = $conn->prepare("
        SELECT o.id, o.name, o.user_id, o.circle_id
        FROM outfits o
        WHERE o.circle_id IN (
            SELECT circle_id FROM circle_members 
            WHERE user_id = :user_id AND status = 'active'
        )
        LIMIT 1
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $sharedOutfit = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($sharedOutfit) {
        echo "测试共享穿搭: {$sharedOutfit['name']} (ID: {$sharedOutfit['id']})\n";
        
        // 测试穿搭数据API
        $apiUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/get_outfits.php";
        
        $testCases = [
            ['include_circle_data' => 'false', 'label' => '个人数据模式'],
            ['include_circle_data' => 'true', 'data_source' => 'shared', 'label' => '共享数据模式'],
            ['include_circle_data' => 'true', 'data_source' => 'all', 'label' => '全部数据模式']
        ];
        
        foreach ($testCases as $testCase) {
            echo "\n测试 {$testCase['label']}:\n";
            
            $params = array_filter($testCase, function($key) {
                return $key !== 'label';
            }, ARRAY_FILTER_USE_KEY);
            
            $queryString = http_build_query($params);
            $fullUrl = "$apiUrl?$queryString";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $fullUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: ' . $_SERVER['HTTP_AUTHORIZATION']
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                echo "❌ CURL错误: $error\n";
            } elseif ($httpCode !== 200) {
                echo "❌ HTTP错误: $httpCode\n";
            } else {
                $data = json_decode($response, true);
                if ($data && $data['success']) {
                    $outfits = $data['data'];
                    echo "✅ 成功获取 " . count($outfits) . " 个穿搭\n";
                    
                    // 检查是否包含目标穿搭
                    $found = false;
                    foreach ($outfits as $outfit) {
                        if ($outfit['id'] == $sharedOutfit['id']) {
                            $found = true;
                            echo "✅ 找到目标穿搭: {$outfit['name']}\n";
                            echo "   数据源: " . ($outfit['data_source'] ?? '未知') . "\n";
                            echo "   创建者: " . ($outfit['creator_nickname'] ?? '未知') . "\n";
                            break;
                        }
                    }
                    
                    if (!$found) {
                        echo "⚠️ 未找到目标穿搭 (ID: {$sharedOutfit['id']})\n";
                    }
                } else {
                    echo "❌ API错误: " . ($data['message'] ?? '未知错误') . "\n";
                }
            }
        }
    } else {
        echo "❌ 没有找到共享穿搭进行测试\n";
    }
    
    // 3. 测试数据完整性
    echo "\n3. 测试数据完整性\n";
    
    // 检查圈子成员状态
    $stmt = $conn->prepare("
        SELECT cm.circle_id, cm.role, c.name as circle_name, cm.status
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $circles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "用户圈子信息:\n";
    foreach ($circles as $circle) {
        $status = $circle['status'] === 'active' ? '✅' : '❌';
        echo "- {$circle['circle_name']} (ID: {$circle['circle_id']}) - 角色: {$circle['role']} - 状态: {$circle['status']} $status\n";
    }
    
    // 检查共享数据统计
    $activeCircles = array_filter($circles, function($c) { return $c['status'] === 'active'; });
    $activeCircleIds = array_column($activeCircles, 'circle_id');
    
    if (!empty($activeCircleIds)) {
        $circleIdList = implode(',', $activeCircleIds);
        
        // 统计共享衣物
        $stmt = $conn->prepare("
            SELECT COUNT(*) as total,
                   COUNT(CASE WHEN user_id = :user_id THEN 1 END) as own,
                   COUNT(CASE WHEN user_id != :user_id THEN 1 END) as others
            FROM clothes 
            WHERE circle_id IN ($circleIdList)
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $clothesStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "\n共享衣物统计:\n";
        echo "- 总数: {$clothesStats['total']}\n";
        echo "- 自己的: {$clothesStats['own']}\n";
        echo "- 他人的: {$clothesStats['others']}\n";
        
        // 统计共享穿搭
        $stmt = $conn->prepare("
            SELECT COUNT(*) as total,
                   COUNT(CASE WHEN user_id = :user_id THEN 1 END) as own,
                   COUNT(CASE WHEN user_id != :user_id THEN 1 END) as others
            FROM outfits 
            WHERE circle_id IN ($circleIdList)
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $outfitStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "\n共享穿搭统计:\n";
        echo "- 总数: {$outfitStats['total']}\n";
        echo "- 自己的: {$outfitStats['own']}\n";
        echo "- 他人的: {$outfitStats['others']}\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    echo "\n💡 前端测试建议:\n";
    echo "1. 切换到共享数据源，点击衣物详情页，检查是否显示操作按钮\n";
    echo "2. 切换到共享数据源，点击穿搭详情页，检查是否正常加载数据\n";
    echo "3. 检查权限提示是否正确显示\n";
    echo "4. 验证编辑操作是否按权限正常工作\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
?>
