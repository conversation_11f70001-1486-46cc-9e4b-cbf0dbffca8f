const app = getApp();

Page({
  data: {
    categoryId: 0,
    name: '',
    sortOrder: 0,
    isSystem: false,
    originalName: ''
  },

  onLoad: function (options) {
    console.log('编辑分类页面参数:', options);
    
    if (options.id) {
      const isSystem = options.is_system === '1';
      const name = decodeURIComponent(options.name || '');
      
      this.setData({
        categoryId: parseInt(options.id),
        name: name,
        originalName: name,
        sortOrder: parseInt(options.sort_order || 0),
        isSystem: isSystem
      });
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1000);
    }
  },

  onReady: function () {
    // 页面初次渲染完成
  },

  onShow: function () {
    // 页面显示
  },

  onHide: function () {
    // 页面隐藏
  },

  onUnload: function () {
    // 页面卸载
  },

  // 输入分类名称
  onNameInput: function(e) {
    if (!this.data.isSystem) {
      this.setData({
        name: e.detail.value.trim()
      });
    }
  },

  // 输入排序序号
  onSortOrderInput: function(e) {
    const value = parseInt(e.detail.value) || 0;
    this.setData({
      sortOrder: value
    });
  },

  // 取消
  onCancel: function() {
    wx.navigateBack();
  },

  // 提交
  onSubmit: function() {
    const { categoryId, name, sortOrder, isSystem, originalName } = this.data;
    
    let finalName = name;
    
    if (isSystem) {
      finalName = originalName;
    } else if (!name) {
      wx.showToast({
        title: '请输入分类名称',
        icon: 'none'
      });
      return;
    } else if (name.length > 30) {
      wx.showToast({
        title: '分类名称不能超过30个字符',
        icon: 'none'
      });
      return;
    }

    const token = app.globalData.token;
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '更新中...'
    });

    wx.request({
      url: `${app.globalData.apiBaseUrl}/update_clothing_category.php`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      data: {
        id: categoryId,
        name: finalName,
        sort_order: sortOrder,
        is_system: isSystem
      },
      success: (res) => {
        wx.hideLoading();
        
        console.log('更新分类响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          wx.showToast({
            title: '更新成功',
            icon: 'success'
          });
          
          app.globalData.needRefreshCategories = true;
          
          setTimeout(() => {
            wx.navigateBack();
          }, 1000);
        } else {
          wx.showToast({
            title: res.data.msg || '更新失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('更新分类失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  }
}); 