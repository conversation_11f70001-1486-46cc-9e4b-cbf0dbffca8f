<view class="container">
  <!-- 判断是否登录 -->
  <block wx:if="{{hasUserInfo}}">
    <!-- 显示衣物选择界面 -->
    <block wx:if="{{!selectedClothing && recommendationType === 'clothing'}}">
      <view class="header">
        <text class="header-title">选择一件衣物作为基础</text>
        <text class="header-desc">我们将围绕这件衣物为您推荐完整穿搭</text>
      </view>
      
      <!-- 衣物选择器 -->
      <view class="clothes-selector">
        <!-- 筛选条件 -->
        <view class="filter-container">
          <!-- 衣橱筛选 -->
          <scroll-view scroll-x class="filter-scroll">
            <view class="filter-tabs">
              <view 
                wx:for="{{wardrobes}}" 
                wx:key="id" 
                class="filter-tab {{selectedWardrobe == item.id ? 'active' : ''}}"
                bindtap="changeWardrobe"
                data-id="{{item.id}}">
                {{item.name}}
              </view>
            </view>
          </scroll-view>
          
          <!-- 分类筛选 -->
          <scroll-view scroll-x class="filter-scroll">
            <view class="filter-tabs">
              <view 
                wx:for="{{clothingCategories}}" 
                wx:key="code"
                class="filter-tab {{selectedCategory == item.code ? 'active' : ''}}" 
                bindtap="changeCategory" 
                data-item="{{item}}">
                {{item.name}}
              </view>
            </view>
          </scroll-view>
        </view>
        
        <!-- 衣物列表 -->
        <scroll-view scroll-y class="clothes-list">
          <view class="clothes-grid layout-mode-4">
            <view 
              wx:for="{{clothingList}}" 
              wx:key="id"
              class="clothes-item {{selectedClothingId === item.id ? 'selected' : ''}}"
              bindtap="selectClothing"
              data-id="{{item.id}}">
              <image src="{{item.image_url}}" mode="aspectFit" class="clothes-img"></image>
              <view class="clothes-name">{{item.name || '未命名衣物'}}</view>
              <view class="select-indicator" wx:if="{{selectedClothingId === item.id}}">✓</view>
            </view>
          </view>
          
          <!-- 空状态 -->
          <view class="empty-clothes" wx:if="{{clothingList.length === 0 && !loadingClothes}}">
            <text class="empty-icon">👕</text>
            <text class="empty-text">没有找到衣物</text>
          </view>
        </scroll-view>
        
        <!-- 底部按钮 -->
        <view class="bottom-button {{selectedClothingId ? 'active' : ''}}" bindtap="startGenerate">
          开始生成
        </view>
      </view>
    </block>
    
    <!-- 穿搭推荐结果页 -->
    <block wx:else>
      <view class="header">
        <text class="header-title" wx:if="{{recommendationType === 'clothing'}}">基于衣物的穿搭推荐</text>
        <text class="header-title" wx:elif="{{recommendationType === 'image_analysis'}}">基于形象分析的穿搭推荐</text>
        <text class="header-desc" wx:if="{{recommendationType === 'clothing'}}">围绕您选择的衣物，为您推荐完整穿搭</text>
        <text class="header-desc" wx:elif="{{recommendationType === 'image_analysis'}}">根据您的个人形象分析，为您推荐最合适的穿搭</text>
      </view>
      
      <!-- 已加载内容 -->
      <block wx:if="{{!loading}}">
        <!-- 基础衣物卡片 - 仅在基于衣物推荐时显示 -->
        <view class="base-clothing-card" wx:if="{{recommendationType === 'clothing'}}">
          <view class="base-clothing-title">基础衣物</view>
          <view class="base-clothing-content">
            <image src="{{selectedClothing.image_url}}" mode="aspectFit" class="base-clothing-image"></image>
            <view class="base-clothing-info">
              <view class="base-clothing-name">{{selectedClothing.name || '未命名衣物'}}</view>
              <view class="base-clothing-category">{{getCategoryName(selectedClothing.category)}}</view>
              <view class="base-clothing-desc" wx:if="{{selectedClothing.description && selectedClothing.description['颜色']}}">颜色：{{selectedClothing.description['颜色']}}</view>
              <view class="base-clothing-tags" wx:if="{{selectedClothing.tags}}">{{selectedClothing.tags}}</view>
            </view>
          </view>
        </view>
        
        
        
        <!-- 穿搭推荐卡片 -->
        <view class="outfit-card">
          <view class="outfit-title">智能穿搭推荐</view>
          
          <!-- 上衣 -->
          <view class="outfit-item" wx:if="{{outfit.top && outfit.top.id && outfit.top.name}}" data-id="{{outfit.top.id}}" bindtap="viewClothingDetail">
            <view class="outfit-item-image-container">
              <image src="{{outfit.top.image_url}}" class="outfit-item-image" mode="aspectFit"></image>
            </view>
            <view class="outfit-item-info">
              <view class="outfit-item-category">上衣</view>
              <view class="outfit-item-name">{{outfit.top.name}}</view>
              <view class="outfit-item-desc" wx:if="{{outfit.top.description && outfit.top.description['颜色']}}">颜色：{{outfit.top.description['颜色']}}</view>
              <view class="outfit-item-reason" wx:if="{{outfit.top.reason}}">{{outfit.top.reason}}</view>
            </view>
          </view>
          
          <!-- 外套，如果需要 -->
          <view class="outfit-item" wx:if="{{outfit.outerwear && outfit.outerwear.id && outfit.outerwear.name}}" data-id="{{outfit.outerwear.id}}" bindtap="viewClothingDetail">
            <view class="outfit-item-image-container">
              <image src="{{outfit.outerwear.image_url}}" class="outfit-item-image" mode="aspectFit"></image>
            </view>
            <view class="outfit-item-info">
              <view class="outfit-item-category">外套</view>
              <view class="outfit-item-name">{{outfit.outerwear.name}}</view>
              <view class="outfit-item-desc" wx:if="{{outfit.outerwear.description && outfit.outerwear.description['颜色']}}">颜色：{{outfit.outerwear.description['颜色']}}</view>
              <view class="outfit-item-reason" wx:if="{{outfit.outerwear.reason}}">{{outfit.outerwear.reason}}</view>
            </view>
          </view>
          
          <!-- 下装 -->
          <view class="outfit-item" wx:if="{{outfit.bottom && outfit.bottom.id && outfit.bottom.name}}" data-id="{{outfit.bottom.id}}" bindtap="viewClothingDetail">
            <view class="outfit-item-image-container">
              <image src="{{outfit.bottom.image_url}}" class="outfit-item-image" mode="aspectFit"></image>
            </view>
            <view class="outfit-item-info">
              <view class="outfit-item-category">{{outfit.bottom.category === 'pants' ? '裤子' : '裙子'}}</view>
              <view class="outfit-item-name">{{outfit.bottom.name}}</view>
              <view class="outfit-item-desc" wx:if="{{outfit.bottom.description && outfit.bottom.description['颜色']}}">颜色：{{outfit.bottom.description['颜色']}}</view>
              <view class="outfit-item-reason" wx:if="{{outfit.bottom.reason}}">{{outfit.bottom.reason}}</view>
            </view>
          </view>
          
          <!-- 鞋子 -->
          <view class="outfit-item" wx:if="{{outfit.shoes && outfit.shoes.id && outfit.shoes.name}}" data-id="{{outfit.shoes.id}}" bindtap="viewClothingDetail">
            <view class="outfit-item-image-container">
              <image src="{{outfit.shoes.image_url}}" class="outfit-item-image" mode="aspectFit"></image>
            </view>
            <view class="outfit-item-info">
              <view class="outfit-item-category">鞋子</view>
              <view class="outfit-item-name">{{outfit.shoes.name}}</view>
              <view class="outfit-item-desc" wx:if="{{outfit.shoes.description && outfit.shoes.description['颜色']}}">颜色：{{outfit.shoes.description['颜色']}}</view>
              <view class="outfit-item-reason" wx:if="{{outfit.shoes.reason}}">{{outfit.shoes.reason}}</view>
            </view>
          </view>
          
          <!-- 配饰 -->
          <view class="outfit-item" wx:if="{{outfit.accessories && outfit.accessories.id && outfit.accessories.name}}" data-id="{{outfit.accessories.id}}" bindtap="viewClothingDetail">
            <view class="outfit-item-image-container">
              <image src="{{outfit.accessories.image_url}}" class="outfit-item-image" mode="aspectFit"></image>
            </view>
            <view class="outfit-item-info">
              <view class="outfit-item-category">配饰</view>
              <view class="outfit-item-name">{{outfit.accessories.name}}</view>
              <view class="outfit-item-desc" wx:if="{{outfit.accessories.description && outfit.accessories.description['颜色']}}">颜色：{{outfit.accessories.description['颜色']}}</view>
              <view class="outfit-item-reason" wx:if="{{outfit.accessories.reason}}">{{outfit.accessories.reason}}</view>
            </view>
          </view>
          
          <!-- 包包 -->
          <view class="outfit-item" wx:if="{{outfit.bag && outfit.bag.id && outfit.bag.name}}" data-id="{{outfit.bag.id}}" bindtap="viewClothingDetail">
            <view class="outfit-item-image-container">
              <image src="{{outfit.bag.image_url}}" class="outfit-item-image" mode="aspectFit"></image>
            </view>
            <view class="outfit-item-info">
              <view class="outfit-item-category">包包</view>
              <view class="outfit-item-name">{{outfit.bag.name}}</view>
              <view class="outfit-item-desc" wx:if="{{outfit.bag.description && outfit.bag.description['颜色']}}">颜色：{{outfit.bag.description['颜色']}}</view>
              <view class="outfit-item-reason" wx:if="{{outfit.bag.reason}}">{{outfit.bag.reason}}</view>
            </view>
          </view>
          
          <!-- 穿搭总结 -->
          <view class="outfit-summary" wx:if="{{outfit.outfit_summary}}">
            <view class="summary-title">穿搭总结</view>
            <view class="summary-content">{{outfit.outfit_summary}}</view>
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="action-buttons">
          <button class="action-btn refresh-btn {{refreshing ? 'refreshing' : ''}}" bindtap="refreshRecommendation">
            <block wx:if="{{refreshing}}">加载中...</block>
            <block wx:else>换一批</block>
          </button>
          <button class="action-btn save-btn" bindtap="saveOutfit">保存穿搭</button>
        </view>
        
        <!-- 剩余次数提示 
        <view class="quota-tip" wx:if="{{availableQuota !== undefined}}">
          今日剩余<text class="quota-number">{{availableQuota}}</text>次推荐机会
        </view>-->
        
        <!-- 穿搭推荐提示 -->
        <view class="recommendation-tips">
          <view class="tip-item">* 点击衣物可查看详情</view>
          <view class="tip-item">* 推荐基于您选择的衣物和衣橱内容</view>
        </view>
      </block>
    </block>
  </block>
  
  <!-- 未登录状态 -->
  <block wx:else>
    <view class="empty-state">
      <view class="empty-icon">
        <image src="/images/emtpy.png" mode="aspectFit" class="empty-image"></image>
      </view>
      <view class="empty-text">登录后即可使用基于衣物的智能穿搭推荐</view>
      <view class="empty-text text-sm">选择一件您喜欢的衣物，系统将为您智能推荐搭配</view>
      <button class="login-btn" bindtap="goToLogin">
        立即登录
      </button>
    </view>
  </block>
</view>

<!-- 加载遮罩 - 轻量级白色版本 -->
<view class="loading-overlay-light" wx:if="{{loading || refreshing || loadingClothes}}">
  <view class="loading-icon"></view>
  <view class="loading-text">{{refreshing ? '换一批加载中...' : (loadingClothes ? '加载衣物中...' : '智能生成推荐中...')}}</view>
</view> 

<!-- 分享提示弹窗 -->
<view class="share-tip-modal" wx:if="{{showShareTip}}">
  <view class="share-tip-overlay" bindtap="closeShareTip"></view>
  <view class="share-tip-content">
    <view class="share-tip-title">今日推荐次数已用完</view>
    <view class="share-tip-icon">🔄</view>
    <view class="share-tip-message">分享给好友即可获得1次推荐机会</view>
    <view class="share-tip-buttons">
      <button class="share-tip-btn cancel-btn" bindtap="closeShareTip">取消</button>
      <button class="share-tip-btn share-btn" open-type="share" bindtap="shareToGetMoreQuota">
        去分享
      </button>
    </view>
  </view>
</view> 