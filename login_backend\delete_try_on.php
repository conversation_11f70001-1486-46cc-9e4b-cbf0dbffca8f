<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';
require_once 'oss_helper.php';

/**
 * 删除试穿结果API
 * 
 * 请求方法: POST
 * 请求头:
 *   - Content-Type: application/json
 *   - Authorization: Bearer <token>
 * 
 * 请求参数 (至少提供一个):
 *   - task_id: 试穿任务ID
 *   - image_url: 结果图片URL
 * 
 * 响应:
 *   成功:
 *     {
 *       "error": false,
 *       "msg": "试衣历史记录已成功删除",
 *       "data": {
 *         "id": 123  // 被删除的记录ID
 *       }
 *     }
 * 
 *   失败:
 *     {
 *       "error": true,
 *       "msg": "错误信息"
 *     }
 */

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证用户身份
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取用户ID - 首先尝试'sub'字段，然后尝试其他可能的字段
if (isset($payload['sub'])) {
    $userId = $payload['sub'];
} elseif (isset($payload['user_id'])) {
    $userId = $payload['user_id'];
} elseif (isset($payload['id'])) {
    $userId = $payload['id'];
} else {
    // 尝试模拟用户
    $userId = 1; // 默认模拟用户ID
    error_log("警告：未在payload中找到用户ID，使用默认模拟用户ID (1)", 3, LOG_PATH . '/debug.log');
}

// 记录使用的userId
error_log("Using userId: $userId from payload fields: " . json_encode(array_keys($payload)), 3, LOG_PATH . '/debug.log');

// 获取请求体
$data = json_decode(file_get_contents('php://input'), true);

// 添加调试日志
if (!defined('LOG_PATH')) {
    define('LOG_PATH', __DIR__ . '/../logs'); // 定义日志路径
}

// 确保日志目录存在
if (!file_exists(LOG_PATH)) {
    mkdir(LOG_PATH, 0755, true);
}

// 调试日志 - 记录请求参数和用户信息
$debugLog = [
    'api' => 'delete_try_on - DEBUG',
    'time' => date('Y-m-d H:i:s'),
    'user_id' => $userId,
    'payload' => $payload,
    'request_data' => $data,
    'request_headers' => getallheaders()
];
error_log(json_encode($debugLog, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), 3, LOG_PATH . '/debug.log');

// 验证参数
$hasTaskId = isset($data['task_id']) && !empty($data['task_id']);
$hasImageUrl = isset($data['image_url']) && !empty($data['image_url']);

if (!$hasTaskId && !$hasImageUrl) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少必要参数: task_id 或 image_url']);
    exit();
}

// 连接数据库
$db = new Database();
try {
    $conn = $db->getConnection();
} catch (Exception $connEx) {
    error_log("数据库连接失败: " . $connEx->getMessage(), 3, LOG_PATH . '/error.log');
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '服务器错误: 无法连接到数据库']);
    exit();
}

// 检查连接是否有效
if (!$conn) {
    error_log("数据库连接无效", 3, LOG_PATH . '/error.log');
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '服务器错误: 数据库连接失败']);
    exit();
}

try {
    // 开始事务
    $conn->beginTransaction();
    
    // 记录SQL查询
    $query = "SELECT id, result_image_url FROM try_on_history WHERE user_id = :user_id";
    $params = [':user_id' => $userId];
    
    if ($hasTaskId) {
        $query .= " AND task_id = :task_id";
        $params[':task_id'] = $data['task_id'];
    } elseif ($hasImageUrl) {
        // 使用LIKE操作符提高URL匹配灵活性
        $imageUrl = $data['image_url'];
        
        // 移除协议部分（http:// 或 https://）以提高匹配率
        $imageUrl = preg_replace('#^https?://#', '', $imageUrl);
        
        // 对URL进行模糊匹配
        $query .= " AND result_image_url LIKE :image_url";
        $params[':image_url'] = '%' . $imageUrl . '%';
        
        // 记录转换后的URL匹配参数
        error_log("Modified URL match parameter: " . $params[':image_url'], 3, LOG_PATH . '/debug.log');
    }
    
    // 记录SQL查询到日志
    error_log("SQL Query: $query\nParams: " . json_encode($params, JSON_UNESCAPED_UNICODE), 3, LOG_PATH . '/debug.log');
    
    $stmt = $conn->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    
    $history = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 记录查询结果
    error_log("Query Result: " . json_encode($history, JSON_UNESCAPED_UNICODE), 3, LOG_PATH . '/debug.log');
    
    if (!$history) {
        // 查询未找到记录，进一步检查
        // 查询该用户所有历史记录，帮助排查问题
        $checkStmt = $conn->prepare("SELECT id, task_id, result_image_url FROM try_on_history WHERE user_id = :user_id LIMIT 5");
        $checkStmt->bindValue(':user_id', $userId);
        $checkStmt->execute();
        $userHistory = $checkStmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 记录用户历史记录
        error_log("User History: " . json_encode($userHistory, JSON_UNESCAPED_UNICODE), 3, LOG_PATH . '/debug.log');
        
        throw new Exception('试衣历史记录不存在或不属于当前用户');
    }
    
    $historyId = $history['id'];
    $imageUrl = $history['result_image_url'];
    
    // 删除记录
    $deleteQuery = "DELETE FROM try_on_history WHERE id = :id AND user_id = :user_id";
    $deleteStmt = $conn->prepare($deleteQuery);
    $deleteStmt->bindValue(':id', $historyId, PDO::PARAM_INT);
    $deleteStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
    $deleteStmt->execute();
    
    if ($deleteStmt->rowCount() === 0) {
        throw new Exception('删除失败');
    }
    
    // 可选：尝试从OSS删除图片文件（如果图片是存储在OSS中）
    if (!empty($imageUrl) && strpos($imageUrl, OSS_URL_PREFIX) !== false) {
        try {
            $ossHelper = new OssHelper();
            $objectName = str_replace(OSS_URL_PREFIX, '', $imageUrl);
            $ossHelper->deleteObject($objectName);
            // 这里我们不抛出异常，因为即使图片删除失败，数据库记录已经删除
        } catch (Exception $ossException) {
            // 记录日志但不中断流程
            error_log('OSS删除失败: ' . $ossException->getMessage());
        }
    }
    
    // 提交事务
    $conn->commit();
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'msg' => '试衣历史记录已成功删除',
        'data' => ['id' => $historyId]
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    // 返回错误响应
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '操作失败: ' . $e->getMessage()
    ]);
} finally {
    // 记录操作日志
    if (!defined('LOG_PATH')) {
        define('LOG_PATH', __DIR__ . '/../logs'); // 定义日志路径
    }

    // 确保日志目录存在
    if (!file_exists(LOG_PATH)) {
        mkdir(LOG_PATH, 0755, true);
    }
    
    $logData = [
        'api' => 'delete_try_on',
        'user_id' => $userId,
        'params' => [
            'task_id' => $data['task_id'] ?? null,
            'image_url' => $data['image_url'] ?? null
        ],
        'status' => isset($e) ? 'error' : 'success',
        'message' => isset($e) ? $e->getMessage() : null,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    try {
        $logFile = LOG_PATH . '/api.log';
        $logMessage = json_encode($logData, JSON_UNESCAPED_UNICODE);
        error_log($logMessage . "\n", 3, $logFile);
    } catch (Exception $logEx) {
        // 如果日志记录失败，至少尝试在PHP错误日志中记录
        error_log('Failed to write to API log: ' . $logEx->getMessage());
    }
} 