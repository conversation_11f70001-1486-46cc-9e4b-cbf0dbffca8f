<?php
// 测试被踢出通知功能的API
// 用于调试和验证功能

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // 查看用户的被移除记录
        $sql = "SELECT cm.*, c.name as circle_name, u.nickname as removed_by_nickname
                FROM circle_members cm
                JOIN outfit_circles c ON cm.circle_id = c.id
                LEFT JOIN users u ON cm.removed_by = u.id
                WHERE cm.user_id = :user_id AND cm.status = 'removed'
                ORDER BY cm.removed_at DESC";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'status' => 'success',
            'data' => [
                'user_id' => $userId,
                'removed_records' => $records,
                'unread_count' => count(array_filter($records, function($r) {
                    return strpos($r['removed_at'], '_read') === false;
                }))
            ]
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // 重置所有已读状态（用于测试）
        $input = json_decode(file_get_contents('php://input'), true);
        $action = isset($input['action']) ? $input['action'] : '';
        
        if ($action === 'reset_read_status') {
            $resetSql = "UPDATE circle_members 
                         SET removed_at = REPLACE(removed_at, '_read', '')
                         WHERE user_id = :user_id AND status = 'removed'";
            $resetStmt = $conn->prepare($resetSql);
            $resetStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $resetStmt->execute();
            
            echo json_encode([
                'status' => 'success',
                'message' => '已重置所有已读状态',
                'affected_rows' => $resetStmt->rowCount()
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => '不支持的操作'
            ]);
        }
    }
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '操作失败：' . $e->getMessage()
    ]);
}
?>
