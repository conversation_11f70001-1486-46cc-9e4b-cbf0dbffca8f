<?php
header("Content-Type: application/json");
require_once './db.php';
require_once './auth.php';
require_once './config.php';

// 初始化响应数组
$response = [
    'code' => 0,
    'message' => 'success',
    'data' => []
];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['code'] = 405;
    $response['message'] = 'Method Not Allowed';
    echo json_encode($response);
    exit;
}

// 获取POST参数
$postData = json_decode(file_get_contents("php://input"), true);
$token = isset($postData['token']) ? $postData['token'] : '';
$keyword = isset($postData['keyword']) ? trim($postData['keyword']) : '';
$page = isset($postData['page']) ? intval($postData['page']) : 1;
$limit = isset($postData['limit']) ? intval($postData['limit']) : 20;

// 改进：更健壮的demo_mode参数处理
$demoMode = false;
if (isset($postData['demo_mode'])) {
    // 支持各种可能的true值: true, "true", "1", 1
    if ($postData['demo_mode'] === true || 
        $postData['demo_mode'] === "true" || 
        $postData['demo_mode'] === "1" || 
        $postData['demo_mode'] === 1 ||
        $postData['demo_mode'] == true) {  // 添加宽松比较
        $demoMode = true;
    }
}

// 记录日志，帮助调试
error_log("get_merchants.php - 请求参数: " . json_encode($postData));
error_log("get_merchants.php - 体验模式状态: " . ($demoMode ? "开启" : "关闭") . ", demo_mode值: " . (isset($postData['demo_mode']) ? var_export($postData['demo_mode'], true) : "未设置"));

// 验证token，除非是体验模式
$userId = null;
if (!$demoMode) {
    $auth = new Auth();
    $verifyResult = $auth->verifyToken($token);

    if ($verifyResult === false) {
        $response['code'] = 401;
        $response['message'] = '无效或已过期的令牌';
        echo json_encode($response);
        exit;
    }
    $userId = $verifyResult['sub']; // 设置用户ID
} else {
    // 在体验模式下，不需要有效token
    error_log("get_merchants.php - 体验模式：跳过token验证，直接获取商家数据");
}

$db = new Database();
$conn = $db->getConnection();

try {
    $offset = ($page - 1) * $limit;
    
    // 构建SQL查询，获取商家列表
    $sql = "SELECT id, nickname, avatar_url, merchant_status, share_try_on_credits, paid_try_on_count 
            FROM users 
            WHERE merchant_status = 'yes'";
    
    $params = [];
    
    // 如果有关键词，添加搜索条件
    if (!empty($keyword)) {
        $sql .= " AND nickname LIKE :keyword";
        $searchKeyword = "%{$keyword}%";
        $params[':keyword'] = $searchKeyword;
    }
    
    $sql .= " ORDER BY id DESC LIMIT :limit OFFSET :offset";
    $params[':limit'] = $limit;
    $params[':offset'] = $offset;
    
    $stmt = $conn->prepare($sql);
    
    // 绑定参数
    foreach ($params as $key => $value) {
        $paramType = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
        $stmt->bindValue($key, $value, $paramType);
    }
    
    $stmt->execute();
    $merchants = [];
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        // 获取该商家的衣物数量
        $clothesCountSql = "SELECT COUNT(*) as count FROM clothes WHERE user_id = :merchant_id";
        $clothesCountStmt = $conn->prepare($clothesCountSql);
        $clothesCountStmt->bindValue(':merchant_id', $row['id'], PDO::PARAM_INT);
        $clothesCountStmt->execute();
        $clothesCount = $clothesCountStmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
        
        $merchants[] = [
            'id' => $row['id'],
            'nickname' => $row['nickname'] ?: '商家用户',
            'avatar_url' => $row['avatar_url'],
            'share_try_on_credits' => (int)$row['share_try_on_credits'],
            'paid_try_on_count' => (int)$row['paid_try_on_count'],
            'clothes_count' => (int)$clothesCount // 添加衣物数量字段
        ];
    }
    
    // 获取总数量
    $countSql = "SELECT COUNT(*) as total FROM users WHERE merchant_status = 'yes'";
    $countParams = [];
    
    if (!empty($keyword)) {
        $countSql .= " AND nickname LIKE :keyword";
        $countParams[':keyword'] = $searchKeyword;
    }
    
    $countStmt = $conn->prepare($countSql);
    
    // 绑定参数
    foreach ($countParams as $key => $value) {
        $paramType = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
        $countStmt->bindValue($key, $value, $paramType);
    }
    
    $countStmt->execute();
    $totalRow = $countStmt->fetch(PDO::FETCH_ASSOC);
    $total = $totalRow['total'];
    
    $response['data'] = [
        'list' => $merchants,
        'total' => $total,
        'page' => $page,
        'limit' => $limit
    ];
} catch (Exception $e) {
    $response['code'] = 500;
    $response['message'] = '处理请求时发生错误: ' . $e->getMessage();
} finally {
    // PDO connections are closed automatically when the variable is unset
    $conn = null;
}

echo json_encode($response); 