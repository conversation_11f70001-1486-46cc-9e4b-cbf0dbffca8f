<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>次元衣柜 - 编辑推荐穿搭</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <style>
        /* 表单样式 */
        .form-section {
            margin-bottom: 30px;
        }
        
        .form-section-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 15px;
            color: #333;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #333;
        }
        
        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-input:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
            min-height: 100px;
        }
        
        .form-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            background-color: white;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 4px;
            border: none;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
        }
        
        .btn-default {
            background-color: #f0f0f0;
            color: #666;
        }
        
        .btn-default:hover {
            background-color: #d9d9d9;
        }
        
        /* 图片上传样式 */
        .image-preview {
            width: 100%;
            height: 200px;
            border: 1px dashed #d9d9d9;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
            background-color: #fafafa;
            position: relative;
            overflow: hidden;
        }
        
        .image-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        .image-preview.empty {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMzEuMjUgMTIuNUgxOC43NVYxOC43NUgxMi41VjMxLjI1SDE4Ljc1VjM3LjVIMzEuMjVWMzEuMjVIMzcuNVYxOC43NUgzMS4yNVYxMi41WiIgZmlsbD0iI2Q5ZDlkOSIvPjwvc3ZnPg==');
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .upload-btn {
            display: block;
            width: 100%;
            padding: 10px;
            background-color: #f0f0f0;
            color: #666;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-align: center;
        }
        
        .upload-btn:hover {
            background-color: #e0e0e0;
        }
        
        .image-url-input {
            margin-top: 10px;
        }
        
        /* 商品列表样式 */
        .items-container {
            margin-top: 20px;
        }
        
        .item-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 4px;
            margin-bottom: 15px;
            position: relative;
            background-color: #fafafa;
        }
        
        .item-col {
            flex: 1;
            min-width: 200px;
        }
        
        .item-image-preview {
            width: 100%;
            height: 100px;
            border: 1px dashed #d9d9d9;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
            background-color: white;
            position: relative;
            overflow: hidden;
        }
        
        .item-image-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        .remove-item-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: #f5222d;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            border: none;
        }
        
        .add-item-btn {
            display: block;
            width: 100%;
            padding: 10px;
            background-color: #e6f7ff;
            color: #1890ff;
            border: 1px dashed #1890ff;
            border-radius: 4px;
            cursor: pointer;
            text-align: center;
            margin-top: 10px;
        }
        
        .add-item-btn:hover {
            background-color: #bae7ff;
        }
        
        /* 加载和错误样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            display: none;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #f5222d;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }
        
        .success-message {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }
        
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>编辑推荐穿搭</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div id="errorMessage" class="error-message"></div>
            <div id="successMessage" class="success-message"></div>
            
            <div class="card">
                <!-- 穿搭基本信息 -->
                <div class="form-section">
                    <h3 class="form-section-title">基本信息</h3>
                    
                    <div class="form-group">
                        <label class="form-label">穿搭名称</label>
                        <input type="text" id="outfitName" class="form-input" placeholder="请输入推荐穿搭名称">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">穿搭分类</label>
                        <select id="categoryId" class="form-select">
                            <option value="">请选择分类</option>
                            <!-- 分类选项将通过JavaScript动态添加 -->
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">穿搭图片</label>
                        <div id="imagePreview" class="image-preview empty"></div>
                        <input type="text" id="imageUrl" class="form-input image-url-input" placeholder="请输入图片URL或上传图片">
                        <button id="uploadImageBtn" class="upload-btn">上传图片</button>
                        <input type="file" id="imageUpload" style="display: none;" accept="image/*">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">穿搭描述</label>
                        <textarea id="description" class="form-textarea" placeholder="请输入推荐穿搭描述"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">推荐理由</label>
                        <textarea id="recommendationReason" class="form-textarea" placeholder="请输入推荐理由，展示在小程序端"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">排序值</label>
                        <input type="number" id="sortOrder" class="form-input" value="0" min="0">
                        <small style="color: #999;">数值越小排序越靠前</small>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">状态</label>
                        <select id="status" class="form-select">
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                </div>
                
                <!-- 商品列表 -->
                <div class="form-section">
                    <h3 class="form-section-title">商品列表</h3>
                    
                    <div id="itemsContainer" class="items-container">
                        <!-- 商品项将通过JavaScript动态添加 -->
                    </div>
                    
                    <button id="addItemBtn" class="add-item-btn">添加商品</button>
                </div>
                
                <!-- 操作按钮 -->
                <div class="form-actions">
                    <button id="cancelBtn" class="btn btn-default">取消</button>
                    <button id="saveBtn" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>
    
    <!-- 商品模板，用于复制生成新商品项 -->
    <template id="itemTemplate">
        <div class="item-row">
            <div class="item-header">
                <h4>商品 #<span class="item-number"></span></h4>
                <button class="remove-item-btn"><i class="fas fa-times"></i></button>
            </div>
            
            <div class="form-group">
                <label class="form-label required">商品名称</label>
                <input type="text" class="form-input item-name" placeholder="请输入商品名称" required>
            </div>
            
            <div class="form-group">
                <label class="form-label required">商品图片</label>
                <div class="item-image-preview image-preview empty"></div>
                <div class="image-input-group">
                    <input type="text" class="form-input item-image-url" placeholder="请输入图片URL或上传图片" required>
                    <button type="button" class="upload-btn item-upload-btn">上传</button>
                    <input type="file" class="item-image-upload" style="display: none;" accept="image/*">
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label required">商品价格</label>
                <input type="number" class="form-input item-price" placeholder="请输入商品价格" step="0.01" min="0" required>
            </div>
            
            <div class="form-group">
                <label class="form-label">购买链接</label>
                <input type="text" class="form-input item-purchase-url" placeholder="请输入商品购买链接">
            </div>
            
            <div class="form-group">
                <label class="form-label">排序值</label>
                <input type="number" class="form-input item-sort-order" value="0" min="0">
            </div>
        </div>
    </template>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/recommended_outfit_edit.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为recommended_outfit
            Sidebar.init('recommended_outfit');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化推荐穿搭编辑页面
            if (typeof RecommendedOutfitEdit !== 'undefined') {
                RecommendedOutfitEdit.init();
            }
        });
    </script>
</body>
</html> 