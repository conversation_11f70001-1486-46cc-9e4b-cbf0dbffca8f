<?php
/**
 * OSS上传回调API
 * 
 * 处理OSS上传成功后的回调，记录文件信息
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * POST Parameters:
 * - file_key: OSS中的文件路径
 * - file_url: 文件完整URL
 * - file_type: 文件类型
 * - user_id: 用户ID
 * - segment_image: 是否需要进行服饰分割（可选，默认false）
 * 
 * Response:
 * {
 *   "error": false,
 *   "data": {
 *     "image_url": "https://bucket.oss-cn-region.aliyuncs.com/key",
 *     "segmented_image_url": "分割后的图片URL（如果有）"
 *   }
 * }
 */

// 引入阿里云SDK
use AlibabaCloud\SDK\Imageseg\V20191230\Imageseg;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Imageseg\V20191230\Models\SegmentClothRequest;
use AlibabaCloud\SDK\Imageseg\V20191230\Models\SegmentCommonImageRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;

// 设置响应内容类型
header('Content-Type: application/json');

// 处理CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';
require_once 'oss_helper.php';
require_once 'wardrobe_logger.php';

// 错误日志
error_log("===== 开始处理OSS上传回调请求 =====");

// 检查是否存在Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// 验证token
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid or expired token'
    ]);
    exit;
}

// 获取用户ID
$userId = $tokenData['sub'];

// 检查必要参数
if (!isset($_POST['file_key']) || !isset($_POST['file_url'])) {
    echo json_encode([
        'error' => true,
        'msg' => '缺少必要参数：file_key或file_url'
    ]);
    exit;
}

$fileKey = $_POST['file_key'];
$fileUrl = $_POST['file_url'];
$fileType = isset($_POST['file_type']) ? $_POST['file_type'] : '';
$segmentImage = isset($_POST['segment_image']) ? (bool)$_POST['segment_image'] : false;

// 记录信息
error_log("处理文件: $fileKey, URL: $fileUrl, 用户ID: $userId, 是否分割: " . ($segmentImage ? 'true' : 'false'));

// 创建OSS助手
try {
    $ossHelper = new OssHelper();
} catch (Exception $e) {
    error_log("OSS助手初始化失败: " . $e->getMessage());
    echo json_encode([
        'error' => true,
        'msg' => 'Failed to initialize OSS helper'
    ]);
    exit;
}

// 验证文件是否确实存在于OSS中（可选）
// 这一步可以省略以减少API调用，因为前端上传成功后才会调用此回调

// 如果需要服饰分割，调用阿里云分割API
$segmentedImageUrl = '';
if ($segmentImage) {
    try {
        // 根据配置选择使用哪种分割API
        $segmentApiType = defined('SEGMENT_API_TYPE') ? SEGMENT_API_TYPE : 'cloth';
        error_log("使用分割API类型: $segmentApiType");
        
        if ($segmentApiType === 'common') {
            // 使用通用分割API
            error_log("尝试调用通用分割API");
            
            // 初始化SDK客户端
            $client = createSegmentClient(ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET);
            
            // 调用通用分割API
            $segmentResult = callSegmentCommonImageAPI($client, $fileUrl);
        } else {
            // 使用服饰分割API (默认)
            error_log("尝试调用服饰分割API");
            
            // 初始化SDK客户端
            $client = createSegmentClient(ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET);
            
            // 调用服饰分割API
            $segmentResult = callSegmentClothAPI($client, $fileUrl);
        }
        
        if ($segmentResult['success']) {
            $segmentedImageUrl = $segmentResult['url'];
            error_log("图片分割成功，结果URL: $segmentedImageUrl");
        } else {
            error_log("图片分割失败: " . $segmentResult['error']);
        }
    } catch (Exception $e) {
        error_log("图片分割异常: " . $e->getMessage());
    }
}

// 返回结果
$response = [
    'error' => false,
    'data' => [
        'image_url' => $fileUrl
    ]
];

// 如果有分割图片，添加到结果中
if (!empty($segmentedImageUrl)) {
    $response['data']['segmented_image_url'] = $segmentedImageUrl;
}

echo json_encode($response);
exit;

/**
 * 创建分割API客户端
 * 
 * @param string $accessKeyId
 * @param string $accessKeySecret
 * @return \AlibabaCloud\SDK\Imageseg\V20191230\Imageseg
 */
function createSegmentClient($accessKeyId, $accessKeySecret) {
    // 创建配置
    $config = new Config([
        'accessKeyId' => $accessKeyId,
        'accessKeySecret' => $accessKeySecret,
        'regionId' => 'cn-shanghai',
        'type' => 'access_key',
        'protocol' => 'https',
        'connectTimeout' => 10000,
        'readTimeout' => 10000
    ]);
    
    // 设置域名
    $config->endpoint = 'imageseg.cn-shanghai.aliyuncs.com';
    
    // 创建客户端
    return new Imageseg($config);
}

/**
 * 调用服饰分割API
 * 
 * @param \AlibabaCloud\SDK\Imageseg\V20191230\Imageseg $client
 * @param string $imageUrl
 * @return array
 */
function callSegmentClothAPI($client, $imageUrl) {
    try {
        // 首先确保使用OSS URL而非CDN URL
        $ossHelper = new OssHelper();
        $originalImageUrl = $ossHelper->convertCdnUrlToOssUrl($imageUrl);
        error_log("调用服饰分割API，转换后的图片URL: $originalImageUrl");
        
        // 创建请求对象
        $request = new SegmentClothRequest([
            'imageURL' => $originalImageUrl
        ]);
        
        // 运行时选项
        $runtime = new RuntimeOptions([
            'ignoreSSL' => true,
            'autoretry' => false,
            'maxAttempts' => 3
        ]);
        
        // 调用API
        $response = $client->segmentClothWithOptions($request, $runtime);
        
        // 检查响应
        if (isset($response->body) && 
            isset($response->body->data) && 
            isset($response->body->data->elements) && 
            isset($response->body->data->elements[0]->imageURL)) {
            
            // 提取分割后的图片URL
            $segmentedUrl = $response->body->data->elements[0]->imageURL;
            
            // 创建OSS助手
            $ossHelper = new OssHelper();
            
            // 下载分割后的图片并保存到OSS
            $ossKey = 'clothes_segmented/segmented_' . basename(parse_url($imageUrl, PHP_URL_PATH));
            $downloadResult = $ossHelper->downloadUrlToOss($segmentedUrl, $ossKey);
            
            if ($downloadResult['success']) {
                return [
                    'success' => true,
                    'url' => $downloadResult['url']
                ];
            } else {
                return [
                    'success' => false,
                    'error' => '下载分割图片失败: ' . $downloadResult['error']
                ];
            }
        } else {
            return [
                'success' => false,
                'error' => '服饰分割响应格式错误'
            ];
        }
    } catch (\Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * 调用通用分割API
 * 
 * @param \AlibabaCloud\SDK\Imageseg\V20191230\Imageseg $client
 * @param string $imageUrl
 * @return array
 */
function callSegmentCommonImageAPI($client, $imageUrl) {
    try {
        // 首先确保使用OSS URL而非CDN URL
        $ossHelper = new OssHelper();
        $originalImageUrl = $ossHelper->convertCdnUrlToOssUrl($imageUrl);
        error_log("调用通用分割API，转换后的图片URL: $originalImageUrl");
        
        // 获取ReturnForm配置
        $returnForm = defined('SEGMENT_COMMON_RETURN_FORM') ? SEGMENT_COMMON_RETURN_FORM : 'crop';
        
        // 创建请求对象
        $request = new SegmentCommonImageRequest([
            'imageURL' => $originalImageUrl,
            'returnForm' => $returnForm
        ]);
        
        // 运行时选项
        $runtime = new RuntimeOptions([
            'ignoreSSL' => true,
            'autoretry' => false,
            'maxAttempts' => 3
        ]);
        
        // 调用API
        $response = $client->segmentCommonImageWithOptions($request, $runtime);
        
        // 检查响应
        if (isset($response->body) && 
            isset($response->body->data) && 
            isset($response->body->data->imageURL)) {
            
            // 提取分割后的图片URL
            $segmentedUrl = $response->body->data->imageURL;
            
            // 创建OSS助手
            $ossHelper = new OssHelper();
            
            // 下载分割后的图片并保存到OSS
            $ossKey = 'clothes_segmented/segmented_' . basename(parse_url($imageUrl, PHP_URL_PATH));
            $downloadResult = $ossHelper->downloadUrlToOss($segmentedUrl, $ossKey);
            
            if ($downloadResult['success']) {
                return [
                    'success' => true,
                    'url' => $downloadResult['url']
                ];
            } else {
                return [
                    'success' => false,
                    'error' => '下载分割图片失败: ' . $downloadResult['error']
                ];
            }
        } else {
            return [
                'success' => false,
                'error' => '通用分割响应格式错误'
            ];
        }
    } catch (\Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
} 