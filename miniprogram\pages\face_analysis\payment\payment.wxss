.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30px;
  box-sizing: border-box;
}

.form-header {
  background-color: #fff;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.step {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #ddd;
  color: #fff;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step.completed {
  background-color: #000;
}

.step.active {
  background-color: #000;
  transform: scale(1.1);
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.step-line {
  flex: 1;
  height: 1px;
  background-color: #ddd;
  margin: 0 10px;
}

.step-line.completed {
  background-color: #000;
}

.step-text {
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  color: #000;
}

.content {
  flex: 1;
  padding: 15px;
  padding-bottom: 30px;
}

/* 网格布局 */
.access-options-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 15px;
}

.option-card {
  flex: 1;
  min-width: calc(50% - 8px); /* 在小屏幕上最多显示两个卡片 */
  max-width: 100%;
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  border: 1px solid #f5f5f5;
}

/* 确保在宽屏上三个卡片并排显示 */
@media (min-width: 768px) {
  .option-card {
    min-width: calc(33.3% - 10px);
  }
}

.option-icon {
  font-size: 28px;
  margin-bottom: 10px;
}

.option-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #000;
  text-align: center;
}

.option-desc {
  font-size: 13px;
  color: #666;
  margin-bottom: 16px;
  text-align: center;
  line-height: 1.4;
}

.option-btn {
  width: 90%;
  background-color: #000;
  color: #fff;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  border-radius: 20px;
  text-align: center;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: auto;
}

.option-btn:after {
  border: none;
}

/* 卡片特殊样式 */
.share-card {
  position: relative;
}

.share-card::after {
  content: "推荐";
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #000;
  color: #fff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  z-index: 1;
}
