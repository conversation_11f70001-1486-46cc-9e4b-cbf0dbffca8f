<?php
// 获取圈子共享衣物API
// 模块4：数据共享基础模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';
require_once 'Logger.php';

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

// 获取查询参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$perPage = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
$offset = ($page - 1) * $perPage;
$category = isset($_GET['category']) ? $_GET['category'] : null;
// 处理前端传递的"null"或"undefined"字符串
if ($category === 'null' || $category === 'undefined' || $category === '') {
    $category = null;
}
$wardrobeId = isset($_GET['wardrobe_id']) ? intval($_GET['wardrobe_id']) : null;

// 记录API请求
Logger::apiRequest('/get_circle_clothes.php', 'GET', [
    'user_id' => $userId,
    'page' => $page,
    'per_page' => $perPage,
    'category' => $category,
    'wardrobe_id' => $wardrobeId
], null, 'circle_clothes');

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查找用户所在的圈子
    $findCircleSql = "SELECT cm.circle_id, cm.role, c.name as circle_name
                      FROM circle_members cm 
                      JOIN outfit_circles c ON cm.circle_id = c.id 
                      WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
    $findCircleStmt = $conn->prepare($findCircleSql);
    $findCircleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $findCircleStmt->execute();
    
    $userCircle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);

    // 记录圈子查询结果
    Logger::debug('用户圈子查询结果', [
        'user_id' => $userId,
        'user_circle' => $userCircle
    ], 'circle_clothes');

    if (!$userCircle) {
        Logger::warn('用户未加入圈子', ['user_id' => $userId], 'circle_clothes');
        echo json_encode([
            'status' => 'error',
            'message' => '您当前未加入任何圈子'
        ]);
        exit;
    }
    
    $circleId = $userCircle['circle_id'];

    // 记录基本信息
    Logger::debug('圈子和用户基本信息', [
        'user_id' => $userId,
        'circle_id' => $circleId,
        'user_role' => $userCircle['role']
    ], 'circle_clothes');

    // 检查圈子成员
    $membersSql = "SELECT user_id, status FROM circle_members WHERE circle_id = :circle_id";
    $membersStmt = $conn->prepare($membersSql);
    $membersStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $membersStmt->execute();
    $members = $membersStmt->fetchAll(PDO::FETCH_ASSOC);

    Logger::debug('圈子成员信息', [
        'circle_id' => $circleId,
        'members' => $members
    ], 'circle_clothes');

    // 检查用户衣物总数
    $totalClothesSQL = "SELECT COUNT(*) as total FROM clothes WHERE user_id = :user_id";
    $totalClothesStmt = $conn->prepare($totalClothesSQL);
    $totalClothesStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $totalClothesStmt->execute();
    $totalClothes = $totalClothesStmt->fetch(PDO::FETCH_ASSOC);

    Logger::debug('用户衣物统计', [
        'user_id' => $userId,
        'total_clothes' => $totalClothes['total']
    ], 'circle_clothes');

    // 检查用户衣物的circle_id分布
    $circleDistributionSQL = "SELECT
        COUNT(*) as total,
        SUM(CASE WHEN circle_id IS NULL THEN 1 ELSE 0 END) as personal,
        SUM(CASE WHEN circle_id IS NOT NULL THEN 1 ELSE 0 END) as shared,
        SUM(CASE WHEN circle_id = :circle_id THEN 1 ELSE 0 END) as in_current_circle
        FROM clothes WHERE user_id = :user_id";
    $distributionStmt = $conn->prepare($circleDistributionSQL);
    $distributionStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $distributionStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $distributionStmt->execute();
    $distribution = $distributionStmt->fetch(PDO::FETCH_ASSOC);

    Logger::debug('用户衣物circle_id分布', [
        'user_id' => $userId,
        'circle_id' => $circleId,
        'distribution' => $distribution
    ], 'circle_clothes');

    // 构建查询条件
    $whereConditions = [];
    $params = ['circle_id' => $circleId];
    
    // 基础条件：圈子共享数据或圈子成员的个人数据
    $whereConditions[] = "(c.circle_id = :circle_id OR 
                          (c.user_id IN (SELECT user_id FROM circle_members 
                                        WHERE circle_id = :circle_id AND status = 'active') 
                           AND c.circle_id IS NULL))";
    
    // 分类过滤
    if ($category && $category !== 'all') {
        $whereConditions[] = "c.category = :category";
        $params['category'] = $category;
    }
    
    // 衣橱过滤
    if ($wardrobeId) {
        $whereConditions[] = "c.wardrobe_id = :wardrobe_id";
        $params['wardrobe_id'] = $wardrobeId;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // 获取圈子中的所有衣物
    $clothesSql = "SELECT c.id, c.name, c.category, c.category_id, c.image_url, c.tags, 
                          c.description, c.created_at, c.updated_at, c.user_id, c.circle_id,
                          c.wardrobe_id, c.wear_count, c.wear_frequency, c.wear_frequency_unit,
                          u.nickname as creator_nickname,
                          w.name as wardrobe_name
                   FROM clothes c
                   JOIN users u ON c.user_id = u.id
                   LEFT JOIN wardrobes w ON c.wardrobe_id = w.id
                   WHERE $whereClause
                   ORDER BY c.created_at DESC
                   LIMIT :offset, :per_page";
    
    // 记录主查询信息
    Logger::sql('主衣物查询', $clothesSql, [
        'params' => $params,
        'offset' => $offset,
        'per_page' => $perPage,
        'where_clause' => $whereClause
    ], 'circle_clothes');

    // 测试简化查询：只查询当前用户的衣物
    $testSql = "SELECT COUNT(*) as count FROM clothes c JOIN users u ON c.user_id = u.id WHERE c.user_id = :user_id";
    $testStmt = $conn->prepare($testSql);
    $testStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $testStmt->execute();
    $testResult = $testStmt->fetch(PDO::FETCH_ASSOC);

    Logger::debug('用户衣物JOIN测试', [
        'user_id' => $userId,
        'count_with_join' => $testResult['count']
    ], 'circle_clothes');

    $clothesStmt = $conn->prepare($clothesSql);
    foreach ($params as $key => $value) {
        $clothesStmt->bindValue(":$key", $value);
    }
    $clothesStmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $clothesStmt->bindParam(':per_page', $perPage, PDO::PARAM_INT);
    $clothesStmt->execute();

    // 记录查询执行结果
    $rowCount = $clothesStmt->rowCount();
    Logger::debug('主查询执行结果', [
        'row_count' => $rowCount,
        'expected_more_than_zero' => $rowCount > 0
    ], 'circle_clothes');

    $clothes = [];
    while ($clothing = $clothesStmt->fetch(PDO::FETCH_ASSOC)) {
        $clothingData = [
            'id' => $clothing['id'],
            'name' => $clothing['name'],
            'category' => $clothing['category'],
            'category_id' => $clothing['category_id'],
            'image_url' => $clothing['image_url'],
            'tags' => $clothing['tags'],
            'description' => $clothing['description'],
            'created_at' => $clothing['created_at'],
            'updated_at' => $clothing['updated_at'],
            'user_id' => $clothing['user_id'],
            'circle_id' => $clothing['circle_id'],
            'wardrobe_id' => $clothing['wardrobe_id'],
            'wardrobe_name' => $clothing['wardrobe_name'],
            'wear_count' => intval($clothing['wear_count']),
            'wear_frequency' => $clothing['wear_frequency'],
            'wear_frequency_unit' => $clothing['wear_frequency_unit'],
            'creator_nickname' => $clothing['creator_nickname'],
            'is_shared' => !is_null($clothing['circle_id']),
            'is_own' => $clothing['user_id'] == $userId,
            'data_source' => is_null($clothing['circle_id']) ? 'personal' : 'shared'
        ];
        
        $clothes[] = $clothingData;
    }
    
    // 获取总数量
    $countSql = "SELECT COUNT(*) as total
                 FROM clothes c
                 JOIN users u ON c.user_id = u.id
                 WHERE $whereClause";
    $countStmt = $conn->prepare($countSql);
    foreach ($params as $key => $value) {
        $countStmt->bindValue(":$key", $value);
    }
    $countStmt->execute();
    
    $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    $totalPages = ceil($totalCount / $perPage);
    
    // 获取分类统计
    $categorySql = "SELECT c.category, COUNT(*) as count
                    FROM clothes c
                    JOIN users u ON c.user_id = u.id
                    WHERE (c.circle_id = :circle_id OR
                           (c.user_id IN (SELECT user_id FROM circle_members
                                         WHERE circle_id = :circle_id AND status = 'active')
                            AND c.circle_id IS NULL))
                    GROUP BY c.category
                    ORDER BY count DESC";
    $categoryStmt = $conn->prepare($categorySql);
    $categoryStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $categoryStmt->execute();

    // 记录分类统计查询
    Logger::sql('分类统计查询', $categorySql, [
        'circle_id' => $circleId,
        'row_count' => $categoryStmt->rowCount()
    ], 'circle_clothes');

    $categoryStats = [];
    while ($categoryStat = $categoryStmt->fetch(PDO::FETCH_ASSOC)) {
        $categoryStats[] = [
            'category' => $categoryStat['category'],
            'count' => intval($categoryStat['count'])
        ];
    }

    Logger::debug('分类统计结果', [
        'category_stats' => $categoryStats,
        'total_categories' => count($categoryStats)
    ], 'circle_clothes');
    
    $response = [
        'status' => 'success',
        'data' => [
            'circle_info' => [
                'circle_id' => $circleId,
                'circle_name' => $userCircle['circle_name'],
                'user_role' => $userCircle['role']
            ],
            'clothes' => $clothes,
            'pagination' => [
                'total' => intval($totalCount),
                'page' => $page,
                'per_page' => $perPage,
                'total_pages' => $totalPages
            ],
            'category_stats' => $categoryStats,
            'filters' => [
                'category' => $category,
                'wardrobe_id' => $wardrobeId
            ]
        ]
    ];

    // 记录API响应
    Logger::apiRequest('/get_circle_clothes.php', 'GET', [
        'user_id' => $userId,
        'circle_id' => $circleId
    ], [
        'clothes_count' => count($clothes),
        'category_stats_count' => count($categoryStats),
        'total_count' => $totalCount
    ], 'circle_clothes');

    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '获取圈子衣物失败：' . $e->getMessage()
    ]);
}
?>
