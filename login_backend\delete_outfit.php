<?php
// 引入必要的文件
require_once 'auth.php';
require_once 'db.php';
require_once 'config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, DELETE');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 验证token获取用户ID
$auth = new Auth();

// 获取token
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 如果有Bearer前缀，去掉它
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
}

if (!$token) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供授权Token'
    ]);
    exit;
}

// 使用Auth类验证token
$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'error' => true,
        'msg' => '未授权，请先登录'
    ]);
    exit;
}

$userId = $payload['sub']; // 从payload中获取用户ID

// 处理POST或DELETE请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST' && $_SERVER['REQUEST_METHOD'] !== 'DELETE') {
    echo json_encode([
        'error' => true,
        'msg' => '不支持的请求方法'
    ]);
    exit;
}

// 获取穿搭ID
$outfitId = null;

if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    // 从URL参数中获取穿搭ID
    $pathInfo = $_SERVER['PATH_INFO'] ?? '';
    $segments = explode('/', trim($pathInfo, '/'));
    if (count($segments) > 0) {
        $outfitId = $segments[0];
    }
} else {
    // 从POST数据中获取穿搭ID
    $data = json_decode(file_get_contents('php://input'), true);
    $outfitId = isset($data['id']) ? $data['id'] : null;
}

// 验证穿搭ID
if (!$outfitId) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供穿搭ID'
    ]);
    exit;
}

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查穿搭是否存在且属于当前用户
    $checkStmt = $conn->prepare("SELECT id FROM outfits WHERE id = :id AND user_id = :user_id");
    $checkStmt->execute([
        'id' => $outfitId,
        'user_id' => $userId
    ]);
    
    if ($checkStmt->rowCount() === 0) {
        echo json_encode([
            'error' => true,
            'msg' => '穿搭不存在或无权删除'
        ]);
        exit;
    }
    
    // 删除穿搭
    $stmt = $conn->prepare("DELETE FROM outfits WHERE id = :id AND user_id = :user_id");
    $result = $stmt->execute([
        'id' => $outfitId,
        'user_id' => $userId
    ]);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'msg' => '穿搭删除成功',
            'data' => [
                'id' => $outfitId
            ]
        ]);
    } else {
        echo json_encode([
            'error' => true,
            'msg' => '删除操作失败'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '删除穿搭失败: ' . $e->getMessage()
    ]);
} 