<view class="container" wx:if="{{!isLoading && photo}}">
  <!-- 照片主体 -->
  <view class="photo-container">
    <image src="{{photo.image_url}}" mode="aspectFit" class="photo-image"></image>
    <view class="photo-type" wx:if="{{photo.type === 'full'}}">全身照</view>
    <view class="photo-type half" wx:elif="{{photo.type === 'half'}}">半身照</view>
  </view>
  
  <!-- 照片信息 -->
  <view class="photo-info" wx:if="{{photo.description}}">
    <text>{{photo.description}}</text>
  </view>
  
  <!-- 底部删除按钮 -->
  <view class="footer-actions">
    <view class="action-item delete" bindtap="deletePhoto">
      <view class="action-icon">
        <text class="delete-icon"></text>
      </view>
      <text class="action-text">删除</text>
    </view>
  </view>
</view>

<!-- 加载中 -->
<view class="loading" wx:if="{{isLoading}}">
  <view class="loading-icon"></view>
</view>
