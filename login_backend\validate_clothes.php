    <?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => true, 'msg' => '仅支持POST请求']);
    exit();
}

// 检查Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode(['error' => true, 'msg' => '缺少认证信息']);
    exit();
}

// 获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取用户ID
$userId = $payload['sub'];

// 获取请求数据
$requestBody = file_get_contents('php://input');
$requestData = json_decode($requestBody, true);

// 如果请求体为空，则尝试从POST数据获取
if (empty($requestData)) {
    $requestData = $_POST;
}

// 检查必要的参数
if (!isset($requestData['wardrobe_id']) || empty($requestData['wardrobe_id'])) {
    echo json_encode(['error' => true, 'msg' => '缺少衣柜ID']);
    exit();
}

if (!isset($requestData['clothes_ids']) || !is_array($requestData['clothes_ids']) || empty($requestData['clothes_ids'])) {
    echo json_encode(['error' => true, 'msg' => '缺少衣物ID列表']);
    exit();
}

$wardrobeId = $requestData['wardrobe_id'];
$clothesIds = $requestData['clothes_ids'];

// 获取数据库连接
$db = new Database();
$conn = $db->getConnection();

try {
    // 1. 验证衣柜是否存在并且属于当前用户
    $checkWardrobeStmt = $conn->prepare("
        SELECT * FROM wardrobes 
        WHERE id = :wardrobe_id AND user_id = :user_id
    ");
    $checkWardrobeStmt->bindParam(':wardrobe_id', $wardrobeId);
    $checkWardrobeStmt->bindParam(':user_id', $userId);
    $checkWardrobeStmt->execute();
    
    $wardrobeExists = $checkWardrobeStmt->rowCount() > 0;
    
    if (!$wardrobeExists) {
        echo json_encode(['error' => true, 'msg' => '衣柜不存在或无权操作']);
        exit();
    }
    
    // 2. 验证所有衣物是否存在并且属于指定衣柜
    $invalidClothes = [];
    
    foreach ($clothesIds as $clothingId) {
        $checkClothingStmt = $conn->prepare("
            SELECT * FROM clothes 
            WHERE id = :clothing_id AND wardrobe_id = :wardrobe_id
        ");
        $checkClothingStmt->bindParam(':clothing_id', $clothingId);
        $checkClothingStmt->bindParam(':wardrobe_id', $wardrobeId);
        $checkClothingStmt->execute();
        
        if ($checkClothingStmt->rowCount() === 0) {
            $invalidClothes[] = $clothingId;
        }
    }
    
    // 如果有无效的衣物，返回错误
    if (!empty($invalidClothes)) {
        echo json_encode([
            'error' => true, 
            'msg' => '部分衣物不存在或无权操作',
            'invalid_clothes' => $invalidClothes
        ]);
        exit();
    }
    
    // 所有验证通过
    echo json_encode([
        'error' => false,
        'msg' => '验证通过',
        'data' => [
            'wardrobe_id' => $wardrobeId,
            'clothes_count' => count($clothesIds)
        ]
    ]);
    
} catch (PDOException $e) {
    error_log('验证衣物错误: ' . $e->getMessage());
    echo json_encode(['error' => true, 'msg' => '验证衣物时发生错误']);
    exit();
} 