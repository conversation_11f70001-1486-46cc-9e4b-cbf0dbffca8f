https://dev.qweather.com/docs/api/weather/weather-now/
实时天气
平台: API iOS Android
获取中国3000+市县区和海外20万个城市实时天气数据，包括实时温度、体感温度、风力风向、相对湿度、大气压强、降水量、能见度、露点温度、云量等。

注意：实况数据均为近实时数据，相比真实的物理世界有5-20分钟的延迟，请根据实况数据中的obsTime确定数据对应的准确时间。

请求路径 
/v7/weather/now
参数 
查询参数
location(必选)需要查询地区的LocationID或以英文逗号分隔的经度,纬度坐标（十进制，最多支持小数点后两位），LocationID可通过GeoAPI获取。例如 location=101010100 或 location=116.41,39.92
lang多语言设置，请阅读多语言文档，了解我们的多语言是如何工作、如何设置以及数据是否支持多语言。
unit数据单位设置，可选值包括unit=m（公制单位，默认）和unit=i（英制单位）。更多选项和说明参考度量衡单位。
请求示例 
curl -X GET --compressed \
-H 'Authorization: Bearer your_token' \
'https://your_api_host/v7/weather/now?location=101010100'
请将your_token替换为你的JWT身份认证，将your_api_host替换为你的API Host

返回数据 
返回数据是JSON格式并进行了Gzip压缩。

{
  "code": "200",
  "updateTime": "2020-06-30T22:00+08:00",
  "fxLink": "http://hfx.link/2ax1",
  "now": {
    "obsTime": "2020-06-30T21:40+08:00",
    "temp": "24",
    "feelsLike": "26",
    "icon": "101",
    "text": "多云",
    "wind360": "123",
    "windDir": "东南风",
    "windScale": "1",
    "windSpeed": "3",
    "humidity": "72",
    "precip": "0.0",
    "pressure": "1003",
    "vis": "16",
    "cloud": "10",
    "dew": "21"
  },
  "refer": {
    "sources": [
      "QWeather",
      "NMC",
      "ECMWF"
    ],
    "license": [
      "QWeather Developers License"
    ]
  }
}
code 请参考状态码
updateTime 当前API的最近更新时间
fxLink 当前数据的响应式页面，便于嵌入网站或应用
now.obsTime 数据观测时间
now.temp 温度，默认单位：摄氏度
now.feelsLike 体感温度，默认单位：摄氏度
now.icon 天气状况的图标代码，另请参考天气图标项目
now.text 天气状况的文字描述，包括阴晴雨雪等天气状态的描述
now.wind360 风向360角度
now.windDir 风向
now.windScale 风力等级
now.windSpeed 风速，公里/小时
now.humidity 相对湿度，百分比数值
now.precip 过去1小时降水量，默认单位：毫米
now.pressure 大气压强，默认单位：百帕
now.vis 能见度，默认单位：公里
now.cloud 云量，百分比数值。可能为空
now.dew 露点温度。可能为空
refer.sources 原始数据来源，或数据源说明，可能为空
refer.license 数据许可或版权声明，可能为空

错误码
当出现错误时，你会收到对应的错误码，本文档将介绍和风天气API的错误码和错误信息。

提示：目前同时存在两种版本的错误码，我们将陆续从v1迁移到v2版本，在此期间根据API和错误类型可能返回不同版本的错误码。如果你希望立即迁移到v2版本，请发送工单。。

注意：你应该妥善的处理遇到的错误，当错误发生时，请暂停请求并进行排查。你不应该放任错误的继续发生，否则这些错误的请求看起来是一种DDoS攻击，极端情况下，我们的安全策略可能冻结你的帐号。

错误码v2 
v2版本将错误进行了细分和更加详细的描述，以便用户可以更容易了解错误的原因，同时也将HTTP Status Code与错误码保持了一致。

错误码包括以下类型：

INVALID PARAMETER 
HTTP response status code: 400

错误的参数，一般指的是传入了错误的参数值，具体错误的参数请参考响应中的error.invalidParams。

MISSING PARAMETER 
HTTP response status code: 400

缺失参数，当一些必选参数没有传递时将报错，具体缺失的参数请参考响应中的error.invalidParams。

NOT FOUND 
HTTP response status code: 400

没有找到所查询的数据。例如查询一个不存在的城市或者一个错误的LocationID，此时你应该检查并更改查询的内容。

DATA NOT AVAILABLE 
HTTP response status code: 400

数据暂时不可用。当你查询的数据超过我们支持的范围后将收到此错误码，例如查询一个地点的空气质量，而我们还不支持这个地点的空气质量，请尝试其他地点进行查询。

UNAUTHORIZED 
HTTP response status code: 401

身份认证失败，你需要检查你的KEY或Token，考虑到安全因素，我们不会返回具体错误的原因。

NO CREDIT 
HTTP response status code: 403

你的帐号内没有足够的可用额度、节省计划或其他额度，请求被拒绝。你需要先增加可用额度或购买其他额度之后再继续请求数据。

OVERDUE 
HTTP response status code: 403

由于你帐号内有逾期未支付的账单，请求被拒绝。你需要先完成逾期账单的支付再继续请求数据。

SECURITY RESTRICTION 
HTTP response status code: 403

当前请求违反了你设置的请求限制，请求被拒绝，考虑到安全因素，我们不会返回具体违反了哪些请求限制。请检查：

该请求是否与你的请求限制有冲突
你的请求限制是否合理
如果请求不是你发送的，请考虑你的凭据可能已经泄露
INVALID HOST 
HTTP response status code: 403

使用了错误的API Host，请求被拒绝。请在控制台设置中查看自己的API Host。了解如何创建API请求。

ACCOUNT SUSPENSION 
HTTP response status code: 403

由于用户帐号被冻结，请求被拒绝。了解帐号冻结。

FORBIDDEN 
HTTP response status code: 403

你暂时无权限请求这个数据。你可以提交工单向我们了解详情。

404 
HTTP response status code: 404

输入了错误的路径或错误的路径参数，无法找到该资源。请注意，404错误不会返回response body。

TOO MANY REQUESTS 
HTTP response status code: 429

短时间内请求过多，超过了QPM限制或累积了大量错误请求。你必须等待一段时间或修复错误后再进行重试，否则持续的429状态可能会被认为是滥用服务资源或DDoS攻击，这将导致你的账号被冻结。关于如何设置重试时间，请参考指数退避算法。

OVER FREE DAILY LIMIT 
HTTP response status code: 429

超过了每日的免费请求量，此时你应该停止发送请求，等待到第二天再试。

OVER MONTHLY LIMIT 
HTTP response status code: 429

对于包年包月订阅用户，当本月请求量超过限额后将收到此错误码，请等待至下个月再试，或者联系你的商务经理升级订阅方案。

UNKNOWN ERROR 
HTTP response status code: 500

我们的服务发生了未知故障，请提交工单与我们联系。

响应 
HTTP/2 400
Content-Type: application/problem+json

{
  "error": {
      "status": 400,
      "type": "https://dev.qweather.com/docs/resource/error-code/#invalid-parameters",
      "title": "Invalid Parameters",
      "detail": "Invalid parameters, please check your request.",
      "invalidParams": [
          "lang"
      ]
  }
}
error.status 对应这个错误的HTTP status code
error.type 这是一个URL用于标识错误类型
error.title 对错误的简短描述
error.detail 对错误的详细描述
error.invalidParams 标识错误或缺失的参数
错误码v1 
通过API接口中的code字段，可以获取到当前请求的状态，判断请求是否成功或出现错误。

代码	说明
200	请求成功
204	请求成功，但你查询的地区暂时没有你需要的数据。
400	请求错误，可能包含错误的请求参数或缺少必选的请求参数。
401	认证失败，可能使用了错误的KEY、KEY的类型错误（如使用SDK的KEY去访问Web API）。
402	超过访问次数或余额不足以支持继续访问服务，你可以充值、升级访问量或等待访问量重置。
403	无访问权限，可能是绑定的PackageName、BundleID、域名IP地址不一致，或者是需要额外付费的数据。
404	查询的数据或地区不存在。
429	超过限定的QPM（每分钟访问次数），请参考QPM说明
500	无响应或超时，接口服务异常请提交工单与我们联系。
响应 
HTTP/2 200
content-type: application/json

{
  "code": "401"
}
1 vs 2 
你可以简单参考下表了解v1和v2的差异，并快速的进行迁移。但是请注意，在我们完全迁移到v2之前，你还需要对v1进行兼容性设计。

 	v1	v2
HTTP Status Code	200	根据不同错误响应对应的HTTP Status Code
错误分类	❌	✅
错误描述	❌	✅
识别错误参数	❌	✅

城市搜索
https://dev.qweather.com/docs/api/geoapi/city-lookup/
城市搜索
平台: API iOS Android
城市搜索API提供全球地理位位置、全球城市搜索服务，支持经纬度坐标反查、多语言、模糊搜索等功能。

天气数据是基于地理位置的数据，因此获取天气之前需要先知道具体的位置信息。使用城市搜索，可获取到该城市的基本信息，包括城市的Location ID（你需要这个ID去查询天气），多语言名称、经纬度、时区、海拔、Rank值、归属上级行政区域、所在行政区域等。

另外，城市搜索也可以帮助你在你的APP中实现模糊搜索，用户只需要输入1-2个字即可获得结果。

请求路径 
/geo/v2/city/lookup
参数 
查询参数
location(必选)需要查询地区的名称，支持文字、以英文逗号分隔的经度,纬度坐标（十进制，最多支持小数点后两位）、LocationID或Adcode（仅限中国城市）。例如 location=北京 或 location=116.41,39.92
模糊搜索，当location传递的为文字时，支持模糊搜索，即用户可以只输入城市名称一部分进行搜索，最少一个汉字或2个字符，结果将按照相关性和Rank值进行排列，便于开发或用户进行选择他们需要查看哪个城市的天气。例如location=bei，将返回与bei相关性最强的若干结果，包括黎巴嫩的贝鲁特和中国的北京市

重名，当location传递的为文字时，可能会出现重名的城市，例如陕西省西安市、吉林省辽源市下辖的西安区和黑龙江省牡丹江市下辖的西安区，此时会根据Rank值排序返回所有结果。在这种情况下，可以通过adm参数的方式进一步确定需要查询的城市或地区，例如location=西安&adm=黑龙江

adm城市的上级行政区划，可设定只在某个行政区划范围内进行搜索，用于排除重名城市或对结果进行过滤。例如 adm=beijing
如请求参数为location=chaoyang&adm=beijing时，返回的结果只包括北京市的朝阳区，而不包括辽宁省的朝阳市

如请求参数仅为location=chaoyang时，返回的结果包括北京市的朝阳区、辽宁省的朝阳市以及长春市的朝阳区

range搜索范围，可设定只在某个国家或地区范围内进行搜索，国家和地区名称需使用ISO 3166 所定义的国家代码。如果不设置此参数，搜索范围将在所有城市。例如 range=cn
number返回结果的数量，取值范围1-20，默认返回10个结果。
lang多语言设置，请阅读多语言文档，了解我们的多语言是如何工作、如何设置以及数据是否支持多语言。
请求示例 
curl -X GET --compressed \
-H 'Authorization: Bearer your_token' \
'https://your_api_host/geo/v2/city/lookup?location=beij'
请将your_token替换为你的JWT身份认证，将your_api_host替换为你的API Host

返回数据 
返回数据是JSON格式并进行了Gzip压缩。

{
  "code":"200",
  "location":[
    {
      "name":"北京",
      "id":"101010100",
      "lat":"39.90499",
      "lon":"116.40529",
      "adm2":"北京",
      "adm1":"北京市",
      "country":"中国",
      "tz":"Asia/Shanghai",
      "utcOffset":"+08:00",
      "isDst":"0",
      "type":"city",
      "rank":"10",
      "fxLink":"https://www.qweather.com/weather/beijing-101010100.html"
    },
    {
      "name":"海淀",
      "id":"101010200",
      "lat":"39.95607",
      "lon":"116.31032",
      "adm2":"北京",
      "adm1":"北京市",
      "country":"中国",
      "tz":"Asia/Shanghai",
      "utcOffset":"+08:00",
      "isDst":"0",
      "type":"city",
      "rank":"15",
      "fxLink":"https://www.qweather.com/weather/haidian-101010200.html"
    },
    {
      "name":"朝阳",
      "id":"101010300",
      "lat":"39.92149",
      "lon":"116.48641",
      "adm2":"北京",
      "adm1":"北京市",
      "country":"中国",
      "tz":"Asia/Shanghai",
      "utcOffset":"+08:00",
      "isDst":"0",
      "type":"city",
      "rank":"15",
      "fxLink":"https://www.qweather.com/weather/chaoyang-101010300.html"
    },
    {
      "name":"昌平",
      "id":"101010700",
      "lat":"40.21809",
      "lon":"116.23591",
      "adm2":"北京",
      "adm1":"北京市",
      "country":"中国",
      "tz":"Asia/Shanghai",
      "utcOffset":"+08:00",
      "isDst":"0",
      "type":"city",
      "rank":"23",
      "fxLink":"https://www.qweather.com/weather/changping-101010700.html"
    },
    {
      "name":"房山",
      "id":"101011200",
      "lat":"39.73554",
      "lon":"116.13916",
      "adm2":"北京",
      "adm1":"北京市",
      "country":"中国",
      "tz":"Asia/Shanghai",
      "utcOffset":"+08:00",
      "isDst":"0",
      "type":"city",
      "rank":"23",
      "fxLink":"https://www.qweather.com/weather/fangshan-101011200.html"
    },
    {
      "name":"通州",
      "id":"101010600",
      "lat":"39.90249",
      "lon":"116.65860",
      "adm2":"北京",
      "adm1":"北京市",
      "country":"中国",
      "tz":"Asia/Shanghai",
      "utcOffset":"+08:00",
      "isDst":"0",
      "type":"city",
      "rank":"23",
      "fxLink":"https://www.qweather.com/weather/tongzhou-101010600.html"
    },
    {
      "name":"丰台",
      "id":"101010900",
      "lat":"39.86364",
      "lon":"116.28696",
      "adm2":"北京",
      "adm1":"北京市",
      "country":"中国",
      "tz":"Asia/Shanghai",
      "utcOffset":"+08:00",
      "isDst":"0",
      "type":"city",
      "rank":"25",
      "fxLink":"https://www.qweather.com/weather/fengtai-101010900.html"
    },
    {
      "name":"大兴",
      "id":"101011100",
      "lat":"39.72891",
      "lon":"116.33804",
      "adm2":"北京",
      "adm1":"北京市",
      "country":"中国",
      "tz":"Asia/Shanghai",
      "utcOffset":"+08:00",
      "isDst":"0",
      "type":"city",
      "rank":"25",
      "fxLink":"https://www.qweather.com/weather/daxing-101011100.html"
    },
    {
      "name":"延庆",
      "id":"101010800",
      "lat":"40.46532",
      "lon":"115.98501",
      "adm2":"北京",
      "adm1":"北京市",
      "country":"中国",
      "tz":"Asia/Shanghai",
      "utcOffset":"+08:00",
      "isDst":"0",
      "type":"city",
      "rank":"33",
      "fxLink":"https://www.qweather.com/weather/yanqing-101010800.html"
    },
    {
      "name":"平谷",
      "id":"101011500",
      "lat":"40.14478",
      "lon":"117.11234",
      "adm2":"北京",
      "adm1":"北京市",
      "country":"中国",
      "tz":"Asia/Shanghai",
      "utcOffset":"+08:00",
      "isDst":"0",
      "type":"city",
      "rank":"33",
      "fxLink":"https://www.qweather.com/weather/pinggu-101011500.html"
    }
  ],
  "refer":{
    "sources":[
      "QWeather"
    ],
    "license":[
      "QWeather Developers License"
    ]
  }
}
code 请参考状态码
location.name 地区/城市名称
location.id 地区/城市ID
location.lat 地区/城市纬度
location.lon 地区/城市经度
location.adm2 地区/城市的上级行政区划名称
location.adm1 地区/城市所属一级行政区域
location.country 地区/城市所属国家名称
location.tz 地区/城市所在时区
location.utcOffset 地区/城市目前与UTC时间偏移的小时数，参考详细说明
location.isDst 地区/城市是否当前处于夏令时。1 表示当前处于夏令时，0 表示当前不是夏令时。
location.type 地区/城市的属性
location.rank 地区评分
location.fxLink 该地区的天气预报网页链接，便于嵌入你的网站或应用
refer.sources 原始数据来源，或数据源说明，可能为空
refer.license 数据许可或版权声明，可能为空