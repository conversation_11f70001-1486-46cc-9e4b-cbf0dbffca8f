// pages/face_analysis/history/history.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    analysisRecords: [],
    page: 1,
    hasMore: true,
    isLoading: false,
    statusText: {
      'pending': '待分析',
      'processing': '分析中',
      'completed': '已完成',
      'failed': '分析失败'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadAnalysisHistory();
  },

  /**
   * 加载分析历史记录
   */
  loadAnalysisHistory() {
    if (this.data.isLoading || !this.data.hasMore) {
      return;
    }

    this.setData({ isLoading: true });

    const token = wx.getStorageSync('token');
    if (!token && !app.globalData.useMockUser) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再查看分析历史',
        showCancel: false,
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/profile/profile'
            });
          }
        }
      });
      this.setData({ isLoading: false });
      return;
    }

    // 获取有效token
    let validToken = token;
    if (!validToken && app.globalData.useMockUser) {
      validToken = app.globalData.token || 'mock_token_' + Date.now();
    }

    wx.request({
      url: `${app.globalData.baseUrl}/face_analysis.php`,
      method: 'GET',
      header: {
        'Authorization': validToken
      },
      success: (res) => {
        if (res.data && !res.data.error) {
          const records = res.data.data || [];
          
          this.setData({
            analysisRecords: [...this.data.analysisRecords, ...records],
            page: this.data.page + 1,
            hasMore: records.length > 0
          });
        } else {
          wx.showToast({
            title: res.data.msg || '获取历史记录失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isLoading: false });
      }
    });
  },

  /**
   * 查看分析详情
   */
  viewDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/face_analysis/result/index?id=${id}`
    });
  },

  /**
   * 开始新的分析
   */
  goToAnalysis() {
    wx.navigateTo({
      url: '/pages/face_analysis/index/index'
    });
  },

  /**
   * 加载更多
   */
  loadMore() {
    this.loadAnalysisHistory();
  },

  /**
   * 显示删除确认
   */
  showDeleteConfirm(e) {
    const id = e.currentTarget.dataset.id;
    wx.showModal({
      title: '确认删除',
      content: '是否删除这条分析记录？',
      success: (res) => {
        if (res.confirm) {
          this.deleteAnalysisRecord(id);
        }
      }
    });
  },

  /**
   * 删除分析记录
   */
  deleteAnalysisRecord(id) {
    // 获取token
    const token = wx.getStorageSync('token');
    if (!token && !app.globalData.useMockUser) {
      wx.showModal({
        title: '提示',
        content: '登录信息已过期，请重新登录',
        showCancel: false
      });
      return;
    }

    // 获取有效token
    let validToken = token;
    if (!validToken && app.globalData.useMockUser) {
      validToken = app.globalData.token || 'mock_token_' + Date.now();
    }

    wx.showLoading({ title: '删除中...' });

    wx.request({
      url: `${app.globalData.baseUrl}/face_analysis.php`,
      method: 'POST',
      header: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': validToken
      },
      data: {
        action: 'delete',
        analysis_id: id
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.data && !res.data.error) {
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
          
          // 更新列表，移除已删除的记录
          this.setData({
            analysisRecords: this.data.analysisRecords.filter(item => item.id !== id)
          });
        } else {
          wx.showToast({
            title: res.data.msg || '删除失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('删除请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.setData({
      analysisRecords: [],
      page: 1,
      hasMore: true
    }, () => {
      this.loadAnalysisHistory();
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadAnalysisHistory();
    }
  }
}) 