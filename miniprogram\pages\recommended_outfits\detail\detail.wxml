<view class="container">
  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{ loading }}">
    <view class="loading-icon"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:elif="{{ error }}">
    <view class="error-icon"></view>
    <view class="error-text">{{ errorMsg || '加载失败' }}</view>
    <view class="error-button" bindtap="reload">重新加载</view>
  </view>

  <!-- 内容区域 -->
  <block wx:else>
    <!-- 主图轮播区域 -->
    <view class="outfit-image-container">
      <swiper class="outfit-swiper" indicator-dots="{{false}}" autoplay="{{false}}" circular="{{true}}" bindchange="handleSwiperChange">
        <!-- 主图作为第一张 -->
        <swiper-item>
          <image class="outfit-image" src="{{ outfit.image_url }}" mode="aspectFit" lazy-load bindtap="showLargeImage" data-url="{{ outfit.image_url }}"></image>
        </swiper-item>
        <!-- 每件衣物的图片 -->
        <swiper-item wx:for="{{ outfit.items }}" wx:key="id">
          <image class="outfit-image" src="{{ item.image_url }}" mode="aspectFit" lazy-load bindtap="showLargeImage" data-url="{{ item.image_url }}"></image>
        </swiper-item>
      </swiper>
    </view>
    
    <!-- 轮播指示点 -->
    <view class="swiper-dots-container">
      <view class="swiper-dots">
        <view class="swiper-dot {{currentSwiperIndex === 0 ? 'active' : ''}}" bindtap="switchSwiper" data-index="0"></view>
        <view wx:for="{{ outfit.items }}" wx:key="id" class="swiper-dot {{currentSwiperIndex === index+1 ? 'active' : ''}}" bindtap="switchSwiper" data-index="{{index+1}}"></view>
      </view>
    </view>
    
    <!-- 描述和推荐理由合并区域 -->
    <view class="outfit-content-section" wx:if="{{ outfit.description || outfit.recommendation_reason }}">
      <view class="section-title">穿搭信息</view>
      <view wx:if="{{ outfit.description }}" class="outfit-description">{{ outfit.description }}</view>
      <view wx:if="{{ outfit.recommendation_reason }}" class="outfit-recommendation">{{ outfit.recommendation_reason }}</view>
    </view>

    <!-- 衣物组合区域 -->
    <view class="outfit-items-section">
      <view class="section-title">衣物链接</view>
      <view class="item-list">
        <view class="item-card" wx:for="{{ outfit.items }}" wx:key="id" bindtap="navigateToProductDetail" data-item="{{ item }}">
          <image class="item-image" src="{{ item.image_url }}" mode="aspectFit"></image>
          <view class="item-info">
            <view class="item-name">{{ item.name }}</view>
            <view class="item-price">¥{{ item.price }}</view>
            <view class="item-buy-button" catchtap="copyPurchaseLink" data-link="{{ item.purchase_url }}" data-item="{{ item.id }}">购买</view>
          </view>
        </view>
      </view>
    </view>
  </block>
  
  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <button class="share-button" open-type="share">
      <image class="icon-share" src="/images/share.png"></image>
      <view>分享穿搭</view>
    </button>
  </view>
  
  <!-- 大图弹窗 -->
  <view class="image-modal {{showImageModal ? 'show' : ''}}">
    <view class="modal-content">
      <swiper class="modal-swiper" indicator-dots="{{true}}" current="{{currentImageIndex}}" bindchange="swiperChange" circular="{{true}}">
        <swiper-item wx:for="{{allImageUrls}}" wx:key="*this">
          <image src="{{item}}" 
                mode="aspectFit" 
                class="large-image"></image>
        </swiper-item>
      </swiper>
      <!-- 关闭按钮 -->
      <view class="close-btn-container" bindtap="hideLargeImage">
        <view class="close-icon"></view>
      </view>
    </view>
  </view>
  
  <!-- Toast提示 -->
  <van-toast id="van-toast" />
</view>