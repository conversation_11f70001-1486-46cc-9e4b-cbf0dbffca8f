/* pages/face_analysis/result/index.wxss */

/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500rpx;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 30rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 30rpx;
}

/* 状态栏样式 */
.status-bar {
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  display: flex;
  flex-direction: column;
}

.status-bar.pending {
  background-color: #fffbf0;
}

.status-bar.processing {
  background-color: #f0f8ff;
}

.status-bar.completed {
  background-color: #f0fff0;
}

.status-bar.failed {
  background-color: #fff0f0;
}

.status-text {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.status-bar.pending .status-text {
  color: #e6a23c;
}

.status-bar.processing .status-text {
  color: #409eff;
}

.status-bar.completed .status-text {
  color: #67c23a;
}

.status-bar.failed .status-text {
  color: #f56c6c;
}

.status-desc {
  font-size: 28rpx;
  color: #666;
}

/* 内容区域样式 */
.content {
  flex: 1;
  padding-bottom: 140rpx;
  box-sizing: border-box;
}

.analysis-result {
  padding: 20rpx;
}

/* 照片区域样式 */
.photo-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.photo-list {
  display: flex;
  overflow-x: auto;
  gap: 20rpx;
}

.photo-item {
  flex-shrink: 0;
  width: 260rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #f5f5f5;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.photo-image {
  width: 260rpx;
  height: 360rpx;
  object-fit: cover;
}

.photo-label {
  padding: 16rpx;
  font-size: 26rpx;
  color: #666;
  text-align: center;
  background-color: #fff;
}

/* 警告提示样式 */
.warning-section {
  background-color: #fff8e6;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.warning-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.warning-text {
  font-size: 26rpx;
  color: #e6a23c;
}

/* 结果段落样式 */
.result-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.item {
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 20rpx;
}

.item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.item-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.item-value {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 原始文本样式 */
.raw-text {
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  font-size: 24rpx;
  background-color: #f9f9f9;
  padding: 16rpx;
  border-radius: 8rpx;
}

/* Markdown格式文本样式 */
.markdown-text {
  line-height: 1.8;
  word-wrap: break-word;
  word-break: break-all;
  font-size: 28rpx;
  color: #333;
}

.markdown-bold {
  font-weight: bold;
  color: #000;
  display: inline;
}

/* 五官特点子项目样式 */
.sub-items {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-top: 10rpx;
}

.sub-item {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 16rpx;
}

.sub-item-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.sub-item-value {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 等待状态样式 */
.waiting-content {
  padding: 40rpx;
}

.waiting-message {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.waiting-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.waiting-text {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
  text-align: center;
}

.waiting-subtext {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

/* 无结果样式 */
.no-result {
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.no-result-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.no-result-text {
  font-size: 32rpx;
  color: #666;
  text-align: center;
}

/* 错误提示样式 */
.error-container {
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.error-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
}

/* 底部按钮区域 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.action-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #000;
  color: #fff;
  font-size: 32rpx;
  border-radius: 8rpx;
  text-align: center;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

.refresh-btn {
  background-color: #409eff;
}

/* 标签列表样式 */
.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 12rpx;
}

.tag {
  padding: 10rpx 20rpx;
  background-color: #f5f5f5;
  color: #666;
  font-size: 24rpx;
  border-radius: 30rpx;
  display: inline-block;
} 