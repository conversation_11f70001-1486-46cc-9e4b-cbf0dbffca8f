<?php
/**
 * TOP API: taobao.openuid.get request
 * 
 * <AUTHOR> create
 * @since 1.0, 2023.07.07
 */
class OpenuidGetRequest
{
	
	private $apiParas = array();
	
	public function getApiMethodName()
	{
		return "taobao.openuid.get";
	}
	
	public function getApiParas()
	{
		return $this->apiParas;
	}
	
	public function check()
	{
		
	}
	
	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
