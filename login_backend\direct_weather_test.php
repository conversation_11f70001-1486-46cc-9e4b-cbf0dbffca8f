<?php
/**
 * 和风天气API直接调用测试
 * 
 * 此脚本专门用于测试直接调用和风天气API，并提供详细的错误诊断信息
 */

// 加载配置
require_once 'config.php';

// 设置页面编码
header('Content-Type: text/html; charset=utf-8');

// 输出测试信息
echo "<h1>和风天气API直接调用测试</h1>\n";
echo "<pre>\n";
echo "API Host: " . WEATHER_API_HOST . "\n";
echo "API Key: " . substr(WEATHER_API_KEY, 0, 5) . "...\n";
echo "</pre>\n";

// 构建API请求URL
$apiHost = 'https://' . WEATHER_API_HOST;
$apiPath = WEATHER_API_PATH;
$location = isset($_GET['location']) ? $_GET['location'] : WEATHER_DEFAULT_LOCATION;

$queryParams = [
    'location' => $location,
    'key' => WEATHER_API_KEY,
    'lang' => 'zh',
    'unit' => 'm'
];
$queryString = http_build_query($queryParams);
$weatherApiUrl = $apiHost . $apiPath . '?' . $queryString;

echo "<h2>API请求详情</h2>\n";
echo "<pre>\n";
echo "请求URL: " . $weatherApiUrl . "\n";

// 设置请求头部
$headers = [
    'Accept: application/json',
    'Accept-Encoding: gzip',
    'Referer: https://cyyg.alidog.cn',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36'
];

echo "请求头: " . json_encode($headers, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . "\n";
echo "</pre>\n";

// 收集详细的调试信息
$verbose = fopen('php://temp', 'w+');

// 发送请求
$ch = curl_init($weatherApiUrl);
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_SSL_VERIFYPEER => true,
    CURLOPT_ENCODING => 'gzip',
    CURLOPT_HTTPHEADER => $headers,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_VERBOSE => true,
    CURLOPT_STDERR => $verbose
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
$info = curl_getinfo($ch);

// 获取详细日志
rewind($verbose);
$verboseLog = stream_get_contents($verbose);
fclose($verbose);

curl_close($ch);

// 输出结果
echo "<h2>API响应结果</h2>\n";
echo "<pre>\n";
echo "HTTP状态码: " . $httpCode . "\n";
if ($error) {
    echo "cURL错误: " . $error . "\n";
}

echo "\n请求信息:\n";
echo json_encode($info, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "\n";

echo "\ncURL详细日志:\n";
echo htmlspecialchars($verboseLog) . "\n";

if ($httpCode === 200) {
    echo "\n响应数据:\n";
    $result = json_decode($response, true);
    if ($result) {
        echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    } else {
        echo "无法解析JSON响应: " . htmlspecialchars($response);
    }
} else {
    echo "\n响应原始内容:\n";
    echo htmlspecialchars($response);
    
    // 尝试解析错误信息
    $errorData = json_decode($response, true);
    if ($errorData && isset($errorData['error'])) {
        echo "\n\n解析后的错误信息:\n";
        echo json_encode($errorData['error'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
}

echo "\n</pre>\n";

// 添加请求参数表单，方便测试不同参数
echo "<h2>测试不同请求参数</h2>\n";
echo "<form method='get' action=''>\n";
echo "<p>位置参数 (城市ID、城市名称或经纬度): <input type='text' name='location' value='" . htmlspecialchars($location) . "' style='width:250px;'></p>\n";
echo "<p><input type='submit' value='测试'></p>\n";
echo "</form>\n";

// 添加链接，可以方便地测试不同的参数
echo "<h3>快速测试链接</h3>\n";
echo "<ul>\n";
echo "<li><a href='?location=101210101'>测试杭州 (默认ID: 101210101)</a></li>\n";
echo "<li><a href='?location=101010100'>测试北京 (ID: 101010100)</a></li>\n";
echo "<li><a href='?location=上海'>测试上海 (城市名称)</a></li>\n";
echo "<li><a href='?location=30.29,120.16'>测试杭州经纬度 (30.29,120.16)</a></li>\n";
echo "</ul>\n";

// 添加类型参考
echo "<h2>位置参数类型参考</h2>\n";
echo "<pre>\n";
echo "1. 城市ID形式: 101210101 (杭州)\n";
echo "2. 城市名称形式: 上海, 北京, 广州\n";
echo "3. 经纬度形式: 纬度,经度 - 如 30.29,120.16 (杭州)\n";
echo "</pre>\n";

// 添加和风天气API参考链接
echo "<h2>API参考</h2>\n";
echo "<ul>\n";
echo "<li><a href='https://dev.qweather.com/docs/api/' target='_blank'>和风天气API文档</a></li>\n";
echo "<li><a href='https://dev.qweather.com/docs/resource/glossary/' target='_blank'>参数及术语表</a></li>\n";
echo "<li><a href='https://dev.qweather.com/docs/start/status-code/' target='_blank'>状态码和错误码</a></li>\n";
echo "</ul>\n";
?> 