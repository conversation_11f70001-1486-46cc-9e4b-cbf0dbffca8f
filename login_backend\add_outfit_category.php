<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入配置和辅助函数
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// 验证用户Token
$auth = new Auth();
$token = null;

// 从请求头或查询参数中获取token
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 移除可能存在的Bearer前缀
    $token = str_replace('Bearer ', '', $token);
} elseif (isset($_GET['token'])) {
    $token = $_GET['token'];
}

if (!$token) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'No token provided']);
    exit;
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Invalid or expired token']);
    exit;
}

$user_id = $payload['sub'];

// 获取POST数据
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// 检查必要参数
if (!isset($data['name']) || empty(trim($data['name']))) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Category name is required']);
    exit;
}

// 准备数据
$name = trim($data['name']);
$description = isset($data['description']) ? trim($data['description']) : '';
$sort_order = isset($data['sort_order']) ? (int)$data['sort_order'] : 0;

try {
    // 获取数据库连接
    $db = new Database();
    $pdo = $db->getConnection();
    
    // 检查是否存在同名分类
    $stmt = $pdo->prepare("SELECT id FROM outfit_categories WHERE user_id = :user_id AND name = :name");
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->bindParam(':name', $name, PDO::PARAM_STR);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Category with this name already exists']);
        exit;
    }
    
    // 检查该用户是否已有默认分类，如果没有，则将此分类设为默认
    $stmt = $pdo->prepare("SELECT id FROM outfit_categories WHERE user_id = :user_id AND is_default = 1");
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    $is_default = ($stmt->rowCount() === 0) ? 1 : 0;
    
    // 插入新分类
    $stmt = $pdo->prepare("
        INSERT INTO outfit_categories (user_id, name, description, sort_order, is_default, created_at)
        VALUES (:user_id, :name, :description, :sort_order, :is_default, NOW())
    ");
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->bindParam(':name', $name, PDO::PARAM_STR);
    $stmt->bindParam(':description', $description, PDO::PARAM_STR);
    $stmt->bindParam(':sort_order', $sort_order, PDO::PARAM_INT);
    $stmt->bindParam(':is_default', $is_default, PDO::PARAM_INT);
    $stmt->execute();
    
    $category_id = $pdo->lastInsertId();
    
    // 记录日志
    error_log("User ID: $user_id created new outfit category ID: $category_id");
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => 'Outfit category created successfully',
        'data' => [
            'id' => $category_id,
            'name' => $name,
            'description' => $description,
            'sort_order' => $sort_order,
            'is_default' => $is_default,
            'outfit_count' => 0
        ]
    ]);
    
} catch (PDOException $e) {
    error_log("Database error in add_outfit_category.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database error', 'message' => 'An error occurred while creating outfit category']);
} catch (Exception $e) {
    error_log("General error in add_outfit_category.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Server error', 'message' => 'An unexpected error occurred']);
} 