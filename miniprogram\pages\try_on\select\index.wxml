<!-- 照片预览 -->
<view class="photo-preview">
  <view class="preview-image">
    <image src="{{selectedPhoto.image_url}}" mode="aspectFill" class="preview-img"></image>
  </view>
  <view class="preview-desc">
    <view class="heading-text">已选择的照片</view>
    <view class="sub-text">选择下方衣物进行试穿</view>
  </view>
  <view class="reselect-btn" bindtap="navigateBack">
    重新选择
  </view>
</view>

<view class="main-content">
  <!-- 衣柜选择器 -->
  <view class="wardrobe-switcher">
    <scroll-view scroll-x="true" class="wardrobe-scroll" enhanced show-scrollbar="{{false}}">
      <view class="wardrobe-option {{selectedWardrobeId === null ? 'active' : ''}}" 
            bindtap="switchWardrobe" 
            data-id="{{null}}">全部衣物</view>
      <view wx:for="{{wardrobeList}}" 
            wx:key="id" 
            class="wardrobe-option {{selectedWardrobeId == item.id ? 'active' : ''}}"
            bindtap="switchWardrobe"
            data-id="{{item.id}}">{{item.name}}</view>
    </scroll-view>
  </view>
  
  <!-- 选择衣物类别 -->
  <view class="category-tabs">
    <view wx:for="{{categories}}" wx:key="id" 
          class="tab-item {{currentCategory === item.id ? 'active' : ''}}" 
          bindtap="switchCategory" data-id="{{item.id}}">
      {{item.name}}
    </view>
  </view>
  
  <!-- 衣物选择网格 -->
  <view class="clothes-grid">
    <block wx:if="{{currentClothes.length > 0}}">
      <view wx:for="{{currentClothes}}" wx:key="id" 
            class="clothes-item {{selectedClothes[item.id] ? 'selected' : ''}} {{item.category === 'skirts' ? 'skirt-item' : ''}}" 
            bindtap="selectClothing" data-id="{{item.id}}" data-category="{{item.category}}">
        <image src="{{item.image_url}}" mode="aspectFit" class="clothes-img"></image>
        <view class="select-icon">
          <text class="check-icon">✓</text>
        </view>
      </view>
    </block>
    <block wx:else>
      <view class="empty-clothes-tip">
        <view class="empty-icon">👔</view>
        <view class="empty-text">当前类别暂无衣物</view>
      </view>
    </block>
  </view>
</view>

<!-- 试穿按钮 -->
<view class="bottom-bar">
  <!-- 已选衣物预览 -->
  <view class="selected-preview" wx:if="{{selectedCount > 0}}">
    <view class="preview-title">已选衣物:</view>
    <view class="selected-items">
      <view wx:for="{{originalAllClothes}}" wx:key="id" wx:if="{{selectedClothes[item.id]}}" 
            class="selected-item" data-id="{{item.id}}" data-category="{{item.category}}"
            bindtap="selectClothing">
        <image src="{{item.image_url}}" mode="aspectFill" class="selected-item-img"></image>
        <view class="remove-icon">
          <image src="/images/remove.png" mode="aspectFit" class="remove-img"></image>
        </view>
      </view>
    </view>
  </view>
  
  <view class="bottom-actions">
    <!--  <view class="guide-btn" bindtap="showTryOnGuide">试衣须知</view>-->
    <view class="try-on-btn {{canTryOn ? 'active' : 'disabled'}}" bindtap="tryOnClothing">
      <text class="magic-icon">✨</text> 一键试穿
    </view>
  </view>
</view>

<!-- 试衣须知弹窗 -->
<view class="guide-popup-mask" wx:if="{{showTryOnGuidePopup}}" bindtap="hideTryOnGuide"></view>
<view class="guide-popup" wx:if="{{showTryOnGuidePopup}}">
  <view class="guide-content">
    <view class="guide-header">
      <text class="guide-title">试衣须知</text>
      <view class="guide-close" bindtap="hideTryOnGuide">×</view>
    </view>
    <scroll-view class="guide-scroll" scroll-y>
      <view class="guide-section">
        <view class="guide-section-title">试衣模特人物图的要求</view>
        <view class="guide-item">
          <view class="guide-item-title">基本要求：</view>
          <view class="guide-item-content">
            <view>• 人群要求：支持不同性别、肤色、年龄（6岁以上）的人物图</view>
            <view>• 姿势要求：人物全身正面照，光照良好</view>
            <view>• 手部展示完整，避免手臂交叉遮挡等情况</view>
          </view>
        </view>
        <view class="guide-item">
          <view class="guide-item-title">常见错误示例：</view>
          <view class="guide-item-content">
            <view>• 非正面全身照（避免上传侧身、坐姿、躺姿、半身照片）</view>
            <view>• 多人照片（仅支持单人照片）</view>
            <view>• 人物服装遮挡（避免手持物、包等）</view>
            <view>• 光线过暗/模糊不清</view>
          </view>
        </view>
      </view>
      
      <view class="guide-section">
        <view class="guide-section-title">试衣服饰图的要求</view>
        <view class="guide-item">
          <view class="guide-item-title">基本要求：</view>
          <view class="guide-item-content">
            <view>• 服饰类型：支持单件上装、下装、连衣裙；支持套装、上下装组合</view>
            <view>• 不支持内衣、婚纱礼服、特色民族服饰等</view>
          </view>
        </view>
        <view class="guide-item">
          <view class="guide-item-title">图片要求：</view>
          <view class="guide-item-content">
            <view>• 服饰平铺拍摄，仅含单件服装，衣服应舒展、平整，无褶皱或折叠遮挡</view>
            <view>• 图片背景简洁、色彩统一，保持服饰主体清晰</view>
            <view>• 服饰的画面占比尽可能大，图片中过多的背景留白会降低试衣效果</view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 裙子搭配提示弹框 -->
<view class="skirt-tip-modal" wx:if="{{showSkirtTip}}">
  <view class="skirt-tip-content">
    <view class="skirt-tip-title">裙子搭配提示</view>
    <view class="skirt-tip-text">
      <view>• 选择<text class="highlight">全身裙</text>时，建议不要再搭配其他衣物，以获得最佳试穿效果</view>
      <view>• 选择<text class="highlight">短裙/半身裙</text>时，可以搭配上衣来完成整体造型</view>
    </view>
    <view class="skirt-tip-btn" bindtap="closeSkirtTip">我明白了</view>
  </view>
</view>

<!-- 试穿进度弹框 -->
<view class="progress-modal" wx:if="{{showProgress}}">
  <view class="progress-content">
    <view class="progress-title">试衣中</view>
    
    <view class="progress-status-container">
      <view class="progress-status">{{progressMessage}}</view>
      <view class="progress-timer" wx:if="{{countdownTime > 0}}">预计剩余 {{countdownTime}} 秒</view>
    </view>
    
    <view class="progress-bar-container">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{progressPercentStr}};"></view>
      </view>
      <view class="progress-percent">{{progressPercentStr}}</view>
    </view>
    
    <view class="progress-stages">
      <view class="stage {{progressStatus === 'queued' || progressStatus === 'uploading' || progressStatus === 'processing' || progressStatus === 'finishing' || progressStatus === 'completed' ? 'active' : ''}}">
        <text class="stage-icon">📋</text>
        <text class="stage-text">排队中</text>
      </view>
      <view class="stage {{progressStatus === 'uploading' || progressStatus === 'processing' || progressStatus === 'finishing' || progressStatus === 'completed' ? 'active' : ''}}">
        <text class="stage-icon">📤</text>
        <text class="stage-text">提交照片中</text>
      </view>
      <view class="stage {{progressStatus === 'processing' || progressStatus === 'finishing' || progressStatus === 'completed' ? 'active' : ''}}">
        <text class="stage-icon">🔄</text>
        <text class="stage-text">试衣中</text>
      </view>
      <view class="stage {{progressStatus === 'finishing' || progressStatus === 'completed' ? 'active' : ''}}">
        <text class="stage-icon">✅</text>
        <text class="stage-text">即将完成</text>
      </view>
    </view>
    
    <view class="progress-tip">试衣过程需要一定时间，请耐心等待...</view>
  </view>
</view> 