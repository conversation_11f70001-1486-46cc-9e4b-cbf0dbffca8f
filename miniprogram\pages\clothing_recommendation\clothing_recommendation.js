const app = getApp();
// 引入每日配额管理工具
const dailyQuota = require('../../utils/dailyQuota');

// 定义推荐功能名称常量
const FEATURE_NAME = 'clothing_outfit_recommendation';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    hasUserInfo: false,
    userInfo: null,
    wardrobes: [],
    selectedWardrobe: null,
    clothingCategories: [],
    selectedCategory: 'all',
    clothingList: [],
    selectedClothingId: null,
    selectedClothing: null,
    loading: false,
    loadingClothes: false,
    refreshing: false,
    outfit: null,
    pageNum: 1,
    pageSize: 20,
    hasMoreClothes: true,
    recommendationType: 'clothing', // 推荐类型：clothing(基于衣物)或image_analysis(基于形象分析)
    analysisId: null, // 形象分析ID
    // 新增属性，用于控制分享提示弹窗
    showShareTip: false,
    // 剩余可用次数
    availableQuota: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检查是否是从分享链接进入
    if (options.source === 'outfit_share') {
      // 是分享链接进入，直接跳转到首页
      wx.switchTab({
        url: '/pages/index/index'
      });
      return; // 不继续执行后面的代码
    }

    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        hasUserInfo: true
      });
      this.fetchWardrobes();
      this.fetchClothingCategories();
    } else {
      // 监听用户登录
      app.userInfoReadyCallback = res => {
        this.setData({
          userInfo: res,
          hasUserInfo: true
        });
        this.fetchWardrobes();
        this.fetchClothingCategories();
      };
    }

    // 更新可用次数状态
    this.updateQuotaStatus();

    // 检查推荐类型
    if (options.type === 'image_analysis' && options.analysis_id) {
      // 基于形象分析的推荐
      this.setData({
        recommendationType: 'image_analysis',
        analysisId: parseInt(options.analysis_id)
      });
      // 直接获取基于形象分析的推荐
      this.fetchImageAnalysisRecommendation();
    } else if (options.clothing_id) {
      // 基于衣物的推荐
      this.setData({
        selectedClothingId: parseInt(options.clothing_id)
      });
      // 加载指定衣物并生成推荐
      this.fetchSpecificClothing(options.clothing_id);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 更新可用次数状态
    this.updateQuotaStatus();
  },

  /**
   * 获取衣橱列表
   */
  fetchWardrobes: function() {
    const that = this;
    
    // 确保serverUrl存在
    const serverUrl = app.globalData.apiBaseUrl;
    if (!serverUrl) {
      console.error('Server URL is not defined');
      wx.showToast({
        title: '服务器地址未配置',
        icon: 'none'
      });
      return;
    }
    
    wx.request({
      url: serverUrl + '/get_wardrobes.php',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token
      },
      success: function(res) {
        console.log('获取衣橱列表响应:', res.data);
        if (res.data.success) {
          // 添加"全部衣橱"选项
          const wardrobes = [{
            id: null,
            name: '全部衣橱'
          }].concat(res.data.data);
          
          that.setData({
            wardrobes: wardrobes
          });
          that.fetchClothing();
        } else {
          wx.showToast({
            title: res.data.message || '获取衣橱失败',
            icon: 'none'
          });
        }
      },
      fail: function(error) {
        console.error('Failed to fetch wardrobes:', error);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 获取衣物分类
   */
  fetchClothingCategories: function() {
    const that = this;
    
    // 确保serverUrl存在
    const serverUrl = app.globalData.apiBaseUrl;
    if (!serverUrl) {
      console.error('Server URL is not defined');
      return;
    }
    
    wx.request({
      url: serverUrl + '/get_clothing_categories.php',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token
      },
      success: function(res) {
        console.log('获取衣物分类响应:', res.data);
        if (res.data.error === false) {
          // 添加"全部"选项
          const categories = [{
            code: 'all',
            name: '全部'
          }].concat(res.data.data);
          
          that.setData({
            clothingCategories: categories
          });
        } else {
          console.error('获取衣物分类失败:', res.data.msg || '未知错误');
        }
      },
      fail: function(error) {
        console.error('Failed to fetch categories:', error);
      }
    });
  },

  /**
   * 获取衣物列表
   */
  fetchClothing: function(reset = true) {
    if (reset) {
      this.setData({
        pageNum: 1,
        hasMoreClothes: true,
        clothingList: [],
        loadingClothes: true
      });
    } else {
      this.setData({
        loadingClothes: true
      });
    }

    const that = this;
    
    // 确保serverUrl存在
    const serverUrl = app.globalData.apiBaseUrl;
    if (!serverUrl) {
      console.error('Server URL is not defined');
      this.setData({ loadingClothes: false });
      return;
    }
    
    // 准备请求参数
    const requestData = {
      page: that.data.pageNum,
      per_page: that.data.pageSize
    };
    
    // 只有当selectedWardrobe不为null时才添加wardrobe_id参数
    if (that.data.selectedWardrobe !== null) {
      requestData.wardrobe_id = that.data.selectedWardrobe;
    }
    
    // 只有当selectedCategory不为'all'时才添加category参数
    if (that.data.selectedCategory !== 'all') {
      requestData.category = that.data.selectedCategory;
    }
    
    wx.request({
      url: serverUrl + '/get_clothes.php',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token
      },
      data: requestData,
      success: function(res) {
        console.log('获取衣物列表响应:', res.data);
        if (res.data.error === false) {
          const newList = reset ? res.data.data : that.data.clothingList.concat(res.data.data);
          const hasMore = res.data.data.length === that.data.pageSize;
          
          that.setData({
            clothingList: newList,
            hasMoreClothes: hasMore,
            loadingClothes: false
          });
        } else {
          that.setData({
            loadingClothes: false
          });
          wx.showToast({
            title: res.data.msg || '获取衣物失败',
            icon: 'none'
          });
        }
      },
      fail: function(error) {
        console.error('Failed to fetch clothes:', error);
        that.setData({
          loadingClothes: false
        });
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 获取特定衣物信息
   */
  fetchSpecificClothing: function(clothingId) {
    const that = this;
    
    // 检查用户是否还有可用次数（初次加载也需要检查）
    if (!dailyQuota.hasAvailableQuota(FEATURE_NAME)) {
      console.log("用户今日推荐次数已用完");
      // 显示分享提示
      this.setData({
        showShareTip: true
      });
      return;
    }
    
    // 确保serverUrl存在
    const serverUrl = app.globalData.apiBaseUrl;
    if (!serverUrl) {
      console.error('Server URL is not defined');
      return;
    }
    
    wx.request({
      url: serverUrl + '/get_clothes.php',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + app.globalData.token
      },
      data: {
        id: clothingId,
        include_circle_data: 'true',
        data_source: 'all'
      },
      success: function(res) {
        console.log('获取特定衣物响应:', res.data);
        if (res.data.error === false && res.data.data && res.data.data.length > 0) {
          const clothing = res.data.data[0];
          that.setData({
            selectedClothing: clothing
          });
          // 将第三个参数设为true，表示这是初次加载，需要消耗配额
          that.generateRecommendation(false, true);
        } else {
          wx.showToast({
            title: res.data.msg || '未找到衣物',
            icon: 'none'
          });
        }
      },
      fail: function(error) {
        console.error('Failed to fetch specific clothing:', error);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 切换衣橱
   */
  changeWardrobe: function(e) {
    const wardrobeId = e.currentTarget.dataset.id;
    this.setData({
      selectedWardrobe: wardrobeId
    });
    this.fetchClothing();
  },

  /**
   * 切换分类
   */
  changeCategory: function(e) {
    const category = e.currentTarget.dataset.item;
    this.setData({
      selectedCategory: category.code
    });
    this.fetchClothing();
  },

  /**
   * 选择衣物
   */
  selectClothing: function(e) {
    const clothingId = e.currentTarget.dataset.id;
    this.setData({
      selectedClothingId: clothingId
    });
  },

  /**
   * 点击开始生成按钮
   */
  startGenerate: function() {
    // 检查用户是否还有可用次数
    if (!dailyQuota.hasAvailableQuota(FEATURE_NAME)) {
      console.log("用户今日推荐次数已用完");
      // 显示分享提示
      this.setData({
        showShareTip: true
      });
      return;
    }
    
    // 调用生成推荐函数，传入isInitialLoad为true
    this.generateRecommendation(false, true);
  },

  /**
   * 生成推荐穿搭
   */
  generateRecommendation: function(refresh = false, isInitialLoad = false) {
    if (!this.data.selectedClothingId) {
      wx.showToast({
        title: '请先选择一件衣物',
        icon: 'none'
      });
      return;
    }

    // 如果是强制刷新（换一批）或初次加载，检查配额
    if (refresh || isInitialLoad) {
      // 检查用户是否还有可用次数
      if (!dailyQuota.hasAvailableQuota(FEATURE_NAME)) {
        console.log("用户今日推荐次数已用完");
        // 显示分享提示
        this.setData({
          showShareTip: true
        });
        return;
      }
    }

    // 从clothingList中找到对应的衣物对象
    const clothingId = this.data.selectedClothingId;
    console.log('选中的衣物ID:', clothingId, '类型:', typeof clothingId);
    
    const clothing = this.data.clothingList.find(item => item.id == clothingId);
    
    if (!clothing && !this.data.selectedClothing) {
      console.error('未找到所选衣物, clothingId:', clothingId);
      console.log('衣物列表:', this.data.clothingList);
      wx.showToast({
        title: '未找到所选衣物',
        icon: 'none'
      });
      return;
    }
    
    const selectedClothing = clothing || this.data.selectedClothing;
    console.log('找到的衣物对象:', selectedClothing);
    
    // 确保clothing.id是有效的数字
    const numericId = parseInt(selectedClothing.id);
    if (isNaN(numericId) || numericId <= 0) {
      console.error('无效的衣物ID:', selectedClothing.id, '转换后:', numericId);
      wx.showToast({
        title: '无效的衣物ID',
        icon: 'none'
      });
      return;
    }
    
    // 设置selectedClothing和loading状态
    this.setData({
      selectedClothing: selectedClothing,
      loading: !this.data.refreshing // 只有非刷新状态才设置loading
    });

    const that = this;
    
    // 确保serverUrl存在
    const serverUrl = app.globalData.apiBaseUrl;
    if (!serverUrl) {
      console.error('Server URL is not defined');
      this.setData({ 
        loading: false,
        refreshing: false
      });
      wx.showToast({
        title: '服务器地址未配置',
        icon: 'none'
      });
      return;
    }
    
    // 确保token存在
    const token = app.globalData.token;
    if (!token) {
      console.error('Authorization token is not defined');
      this.setData({ 
        loading: false,
        refreshing: false
      });
      wx.showToast({
        title: '未登录或授权已过期',
        icon: 'none'
      });
      return;
    }
    
    // 构建请求数据，添加刷新参数
    const requestData = {
      clothing_id: numericId,
      refresh: refresh || this.data.refreshing || isInitialLoad ? 1 : 0 // 根据刷新状态设置参数
    };
    
    console.log('准备发送请求:', {
      url: serverUrl + '/get_clothing_recommendation.php',
      method: 'POST',
      token: token ? token.substring(0, 10) + '...' : 'undefined',
      clothing_id: numericId,
      refresh: requestData.refresh,
      isInitialLoad: isInitialLoad
    });
    
    // 发送请求获取推荐
    wx.request({
      url: serverUrl + '/get_clothing_recommendation.php',
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: requestData,
      success: function(res) {
        console.log('获取推荐响应:', res.data);
        console.log('响应状态码:', res.statusCode);
        console.log('响应头:', res.header);
        
        that.setData({
          loading: false,
          refreshing: false // 无论成功与否，都重置refreshing状态
        });
        
        if (res.data.status === 'success') {
          that.setData({
            outfit: res.data.recommendation
          });
          
          // 如果是刷新请求或初次加载，消耗一次配额
          if (refresh || that.data.refreshing || isInitialLoad) {
            dailyQuota.useQuota(FEATURE_NAME);
            // 更新可用次数状态
            that.updateQuotaStatus();
          }
        } else {
          wx.showToast({
            title: res.data.message || '获取推荐失败',
            icon: 'none'
          });
        }
      },
      fail: function(error) {
        console.error('Failed to generate recommendation:', error);
        that.setData({
          loading: false,
          refreshing: false // 失败时也重置refreshing状态
        });
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 获取基于形象分析的推荐
   */
  fetchImageAnalysisRecommendation: function(refresh = false) {
    // 如果是强制刷新，检查配额
    if (refresh) {
      // 检查用户是否还有可用次数
      if (!dailyQuota.hasAvailableQuota(FEATURE_NAME)) {
        console.log("用户今日推荐次数已用完");
        // 显示分享提示
        this.setData({
          showShareTip: true,
          refreshing: false
        });
        return;
      }
    }
    
    // 检查是否是首次加载（非刷新情况）
    const isInitialLoad = !refresh && !this.data.outfit;
    if (isInitialLoad) {
      // 首次加载也需要检查配额
      if (!dailyQuota.hasAvailableQuota(FEATURE_NAME)) {
        console.log("用户今日推荐次数已用完（首次加载）");
        // 显示分享提示
        this.setData({
          showShareTip: true
        });
        return;
      }
    }

    this.setData({
      loading: true
    });

    const serverUrl = app.globalData.apiBaseUrl;
    if (!serverUrl) {
      console.error('Server URL is not defined');
      this.setData({ loading: false });
      wx.showToast({
        title: '服务器地址未配置',
        icon: 'none'
      });
      return;
    }

    // 检查analysisId是否存在
    if (!this.data.analysisId) {
      console.error('形象分析ID不存在');
      this.setData({ 
        loading: false,
        refreshing: false
      });
      wx.showToast({
        title: '缺少形象分析ID参数',
        icon: 'none'
      });
      return;
    }

    console.log('发送形象分析推荐请求，参数:', {
      analysis_id: this.data.analysisId,
      refresh: refresh ? 1 : 0,
      isInitialLoad: isInitialLoad
    });

    wx.request({
      url: serverUrl + '/get_image_analysis_recommendation.php',
      method: 'POST',
      data: {
        analysis_id: this.data.analysisId,
        refresh: refresh || isInitialLoad ? 1 : 0
      },
      header: {
        'Authorization': 'Bearer ' + app.globalData.token,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        console.log('形象分析推荐响应:', res.data);
        this.setData({
          loading: false,
          refreshing: false
        });

        if (res.data.status === 'success') {
          this.setData({
            outfit: res.data.recommendation
          });
          
          // 如果是刷新请求或首次加载，消耗一次配额
          if (refresh || isInitialLoad) {
            dailyQuota.useQuota(FEATURE_NAME);
            // 更新可用次数状态
            this.updateQuotaStatus();
          }
        } else {
          wx.showToast({
            title: res.data.message || '获取推荐失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('Failed to fetch recommendation:', error);
        this.setData({
          loading: false,
          refreshing: false
        });
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 刷新推荐
   */
  refreshRecommendation: function() {
    // 检查用户是否还有可用次数
    if (!dailyQuota.hasAvailableQuota(FEATURE_NAME)) {
      console.log("用户今日推荐次数已用完");
      // 显示分享提示
      this.setData({
        showShareTip: true
      });
      return;
    }
    
    this.setData({
      refreshing: true
    });
    
    if (this.data.recommendationType === 'image_analysis') {
      // 刷新基于形象分析的推荐
      this.fetchImageAnalysisRecommendation(true);
    } else {
      // 刷新基于衣物的推荐
      this.generateRecommendation(true);
    }
  },

  /**
   * 保存穿搭
   */
  saveOutfit: function() {
    console.log("保存穿搭按钮被点击");
    
    // 检查是否有穿搭数据
    if (!this.data.outfit) {
      wx.showToast({
        title: '暂无穿搭推荐',
        icon: 'none'
      });
      return;
    }
    
    // 检查用户是否已登录
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载中
    wx.showLoading({
      title: '保存中...',
      mask: true
    });
    
    // 获取或创建AI推荐分类
    this.getOrCreateAICategory()
      .then(categoryId => {
        console.log("获取到AI推荐分类ID:", categoryId);
        
        // 构建穿搭对象
        const now = new Date();
        const outfitName = this.data.selectedClothing ? `基于${this.data.selectedClothing.name}的推荐穿搭` : "AI推荐穿搭";
        const outfit = {
          name: outfitName, // 使用基础衣物名称作为穿搭名称
          description: this.data.outfit.outfit_summary || "基于衣物的AI智能推荐穿搭", // 使用穿搭总结作为描述
          category_id: categoryId, // AI推荐分类
          thumbnail: "", // 由后端生成缩略图
          created_at: now.toISOString(),
          updated_at: now.toISOString(),
          items: this.convertAIOutfitToItems(this.data.outfit), // 转换穿搭项
          forceAdd: true // 强制添加，即使可能为空
        };
        
        console.log("保存的穿搭数据:", outfit);
        
        // 保存穿搭，包括同步到服务器
        app.saveOutfit(outfit, (result) => {
          wx.hideLoading();
          
          if (result.success) {
            const savedOutfit = result.data || outfit;
            console.log('保存穿搭成功:', savedOutfit);
            
            // 显示成功提示，并询问是否前往编辑
            wx.showModal({
              title: '保存成功',
              content: '穿搭已保存至"AI推荐"分类，是否立即编辑?',
              confirmText: '去编辑',
              cancelText: '稍后再说',
              success: (res) => {
                if (res.confirm) {
                  // 跳转到编辑页
                  wx.navigateTo({
                    url: `/pages/outfits/edit/edit?id=${savedOutfit.id}`
                  });
                }
              }
            });
          } else {
            console.error('保存穿搭失败:', result.error);
            wx.showToast({
              title: result.error || '保存失败',
              icon: 'none'
            });
          }
        });
      })
      .catch(error => {
        wx.hideLoading();
        console.error("保存穿搭失败:", error);
        wx.showToast({
          title: '保存失败: ' + error.message,
          icon: 'none'
        });
      });
  },

  // 获取或创建AI推荐分类
  getOrCreateAICategory: function() {
    return new Promise((resolve, reject) => {
      // 获取穿搭分类列表
      wx.request({
        url: `${app.globalData.apiBaseUrl}/get_outfit_categories.php`,
        method: 'GET',
        header: {
          'Authorization': app.globalData.token
        },
        data: {
          page: 1,
          per_page: 100 // 获取足够多的分类
        },
        success: (res) => {
          console.log('获取穿搭分类列表:', res.data);
          
          if (res.statusCode === 200 && res.data.success) {
            const categories = res.data.data || [];
            
            // 查找名为"AI推荐"的分类
            const aiCategory = categories.find(cat => cat.name === 'AI推荐');
            
            if (aiCategory) {
              // 已存在AI推荐分类，直接使用
              console.log('已存在AI推荐分类:', aiCategory);
              resolve(aiCategory.id);
            } else {
              // 不存在则创建新分类
              console.log('需要创建AI推荐分类');
              this.createAICategory().then(resolve).catch(reject);
            }
          } else {
            reject(new Error('获取分类列表失败'));
          }
        },
        fail: (err) => {
          console.error('获取分类列表失败:', err);
          reject(err);
        }
      });
    });
  },

  // 创建AI推荐分类
  createAICategory: function() {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiBaseUrl}/add_outfit_category.php`,
        method: 'POST',
        header: {
          'Authorization': app.globalData.token,
          'Content-Type': 'application/json'
        },
        data: {
          name: 'AI推荐',
          description: 'AI智能推荐的穿搭集合'
        },
        success: (res) => {
          console.log('创建AI推荐分类响应:', res.data);
          
          if (res.statusCode === 200 && res.data.success) {
            resolve(res.data.data.id);
          } else {
            reject(new Error(res.data.message || '创建分类失败'));
          }
        },
        fail: (err) => {
          console.error('创建AI推荐分类失败:', err);
          reject(err);
        }
      });
    });
  },

  // 将AI推荐的穿搭转换为编辑器可用的items数组
  convertAIOutfitToItems: function(aiOutfit) {
    const items = [];
    const canvasWidth = 375; // 假设画布宽度为375px（微信小程序默认设计宽度）
    const canvasHeight = 500; // 假设画布高度为500px
    
    // 定义画布区域
    const leftX = 30;
    const rightX = canvasWidth - 150; // 假设物品宽度约120px
    const topY = 30;
    const bottomY = canvasHeight - 200; // 假设物品高度约170px
    const centerX = canvasWidth / 2 - 75; // 中心X
    const centerY = canvasHeight / 2 - 100; // 中心Y
    
    // 提取所有衣物并保存到一个数组
    let clothingItems = [];
    
    // 按照显示顺序添加衣物
    if (aiOutfit.top && aiOutfit.top.id && aiOutfit.top.name) {
      clothingItems.push(this.createClothingItem(aiOutfit.top, "上衣"));
    }
    
    if (aiOutfit.outerwear && aiOutfit.outerwear.id && aiOutfit.outerwear.name) {
      clothingItems.push(this.createClothingItem(aiOutfit.outerwear, "外套"));
    }
    
    if (aiOutfit.bottom && aiOutfit.bottom.id && aiOutfit.bottom.name) {
      const category = aiOutfit.bottom.category === 'pants' ? '裤子' : '裙子';
      clothingItems.push(this.createClothingItem(aiOutfit.bottom, category));
    }
    
    if (aiOutfit.shoes && aiOutfit.shoes.id && aiOutfit.shoes.name) {
      clothingItems.push(this.createClothingItem(aiOutfit.shoes, "鞋子"));
    }
    
    if (aiOutfit.accessories && aiOutfit.accessories.id && aiOutfit.accessories.name) {
      clothingItems.push(this.createClothingItem(aiOutfit.accessories, "配饰"));
    }
    
    if (aiOutfit.bag && aiOutfit.bag.id && aiOutfit.bag.name) {
      clothingItems.push(this.createClothingItem(aiOutfit.bag, "包包"));
    }
    
    // 根据要求排列衣物
    clothingItems.forEach((item, index) => {
      const outfitItem = {
        clothing_id: item.id,
        clothing_data: {
          name: item.name,
          category: item.category,
          image_url: item.image_url
        },
        position: { x: 0, y: 0 },
        size: { width: 150, height: 200 },
        rotation: 0,
        z_index: index + 1
      };
      
      // 根据索引设置位置
      switch (index) {
        case 0: // 第一件：左上
          outfitItem.position = { x: leftX, y: topY };
          break;
        case 1: // 第二件：右上
          outfitItem.position = { x: rightX, y: topY };
          break;
        case 2: // 第三件：左下
          outfitItem.position = { x: leftX, y: bottomY };
          break;
        case 3: // 第四件：右下
          outfitItem.position = { x: rightX, y: bottomY };
          break;
        case 4: // 第五件：中间
          outfitItem.position = { x: centerX, y: centerY };
          break;
        default: // 其余：围绕中心均匀分布
          const radius = 80; // 分布半径
          const angle = (index - 5) * (2 * Math.PI / (clothingItems.length - 5));
          outfitItem.position = {
            x: centerX + radius * Math.cos(angle),
            y: centerY + radius * Math.sin(angle)
          };
      }
      
      items.push(outfitItem);
    });
    
    return items;
  },

  // 创建衣物项辅助函数
  createClothingItem: function(item, displayCategory) {
    return {
      id: item.id,
      name: item.name || `${displayCategory}`,
      category: item.category || 'unknown',
      image_url: item.image_url
    };
  },

  /**
   * 返回选择衣物
   */
  backToSelection: function() {
    this.setData({
      selectedClothing: null,
      outfit: null
    });
  },

  /**
   * 查看衣物详情
   */
  viewClothingDetail: function(e) {
    const clothingId = e.currentTarget.dataset.id;
    if (!clothingId) return;
    
    wx.navigateTo({
      url: '/pages/clothing/detail/detail?id=' + clothingId
    });
  },

  /**
   * 前往登录
   */
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  /**
   * 获取分类名称
   */
  getCategoryName: function(categoryCode) {
    const categoryMap = {
      'tops': '上衣',
      'pants': '裤子',
      'skirts': '裙子',
      'coats': '外套',
      'shoes': '鞋子',
      'bags': '包包',
      'accessories': '配饰'
    };
    
    return categoryMap[categoryCode] || categoryCode;
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    if (this.data.hasMoreClothes && !this.data.loadingClothes && !this.data.selectedClothing) {
      this.setData({
        pageNum: this.data.pageNum + 1
      });
      this.fetchClothing(false);
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {
    // 分享后增加配额
    setTimeout(() => {
      this.onShareSuccess();
    }, 1000);
    
    if (this.data.selectedClothing && this.data.outfit) {
      return {
        title: '不知道怎么搭配穿搭，快用次元衣帽间',
        path: '/pages/index/index?source=outfit_share',
        imageUrl: 'https://images.alidog.cn/logo/xxfx.png' // 使用固定的分享图片
      };
    }
    
    return {
      title: '不知道怎么搭配穿搭，快用次元衣帽间',
      path: '/pages/index/index?source=outfit_share',
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png' // 使用固定的分享图片
    };
  },
  
  /**
   * 用户点击右上角分享到朋友圈
   */
  onShareTimeline: function() {
    // 分享后增加配额
    setTimeout(() => {
      this.onShareSuccess();
    }, 1000);
    
    return {
      title: '不知道怎么搭配穿搭，快用次元衣帽间',
      query: 'source=outfit_share',
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png' // 使用固定的分享图片
    };
  },

  // 更新配额状态
  updateQuotaStatus: function() {
    const availableQuota = dailyQuota.getAvailableQuota(FEATURE_NAME);
    this.setData({
      availableQuota: availableQuota
    });
  },

  // 关闭分享提示弹窗
  closeShareTip: function() {
    this.setData({
      showShareTip: false
    });
  },
  
  // 通过分享获取更多次数
  shareToGetMoreQuota: function() {
    // 分享之前先关闭弹窗
    this.setData({
      showShareTip: false
    });
    
    // 唤起系统分享
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },
  
  // 分享成功回调（需要用户手动告知分享成功）
  onShareSuccess: function() {
    // 增加分享次数
    dailyQuota.addShareQuota(FEATURE_NAME);
    
    // 更新可用次数状态
    this.updateQuotaStatus();
    
    // 显示成功提示
    wx.showToast({
      title: '获得1次推荐机会',
      icon: 'success',
      duration: 2000
    });
    
    // 自动刷新推荐
    setTimeout(() => {
      if (this.data.recommendationType === 'image_analysis') {
        this.fetchImageAnalysisRecommendation(true);
      } else {
        this.generateRecommendation(true);
      }
    }, 2000);
  }
}); 