<?php
/**
 * Delete Clothing API
 * 
 * Deletes a clothing item for the user
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * POST Parameters:
 * - id: Clothing ID to delete (required)
 * 
 * Response:
 * {
 *   "error": false,
 *   "msg": "Clothing item deleted successfully"
 * }
 */

require_once 'config.php';
require_once 'verify_token.php';
require_once 'db.php';
require_once '../vendor/autoload.php'; // 引入阿里云OSS SDK
require_once 'oss_helper.php';

// 引入OSS命名空间
use OSS\OssClient;
use OSS\Core\OssException;

// Set response content type
header('Content-Type: application/json');

// Handle CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if Authorization header exists
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

// Check if the request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => 'Only POST method is allowed'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// Verify token
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid or expired token'
    ]);
    exit;
}

// Get user ID from token data
$userId = $tokenData['sub'];

// Get JSON POST data
$json = file_get_contents('php://input');
$data = json_decode($json, true);

if (!$data) {
    // If JSON parsing failed, try regular POST data
    $data = $_POST;
}

// Validate required fields
if (empty($data['id'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Clothing ID is required'
    ]);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 记录执行信息
    error_log("准备删除衣物数据: 衣物ID={$data['id']}, 用户ID=$userId");
    
    // 首先检查该记录是否属于当前用户，并获取图片URL
    $checkSql = "SELECT id, image_url FROM clothes WHERE id = :id AND user_id = :user_id";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->execute([
        'id' => $data['id'],
        'user_id' => $userId
    ]);
    
    $clothing = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$clothing) {
        echo json_encode([
            'error' => true,
            'msg' => '没有权限删除此衣物或衣物不存在'
        ]);
        exit;
    }
    
    // 获取图片URL
    $imageUrl = $clothing['image_url'];
    
    // 初始化OSS辅助类
    $ossHelper = new OssHelper();
    
    // 检查是否为OSS URL，如果是则删除OSS对象
    if ($ossHelper->isOssUrl($imageUrl)) {
        $ossKey = $ossHelper->getKeyFromUrl($imageUrl);
        if ($ossKey) {
            $deleteResult = $ossHelper->deleteFile($ossKey);
            if ($deleteResult['success']) {
                error_log("OSS文件已删除: $ossKey");
            } else {
                error_log("OSS文件删除失败: " . $deleteResult['error']);
            }
        }
    }
    
    // 执行删除操作
    $sql = "DELETE FROM clothes WHERE id = :id AND user_id = :user_id";
    $stmt = $conn->prepare($sql);
    
    $params = [
        'id' => $data['id'],
        'user_id' => $userId
    ];
    
    // 记录SQL和参数
    error_log("SQL: $sql");
    error_log("参数: " . json_encode($params));
    
    $stmt->execute($params);
    
    if ($stmt->rowCount() > 0) {
        error_log("成功删除衣物数据，ID: {$data['id']}");
        
        // 返回成功消息
        echo json_encode([
            'error' => false,
            'msg' => '衣物已成功删除'
        ]);
    } else {
        error_log("衣物数据未删除，可能ID不存在: {$data['id']}");
        echo json_encode([
            'error' => true,
            'msg' => '没有找到要删除的衣物'
        ]);
    }
} catch (PDOException $e) {
    // 详细记录错误信息
    $errorMessage = $e->getMessage();
    $errorCode = $e->getCode();
    error_log("数据库错误码: $errorCode");
    error_log("数据库错误信息: $errorMessage");
    
    // 返回通用错误，但带有更多信息
    echo json_encode([
        'error' => true,
        'msg' => "数据库错误: $errorCode - " . substr($errorMessage, 0, 100)
    ]);
} catch (Exception $e) {
    // 其他错误
    error_log("删除衣物失败: " . $e->getMessage());
    echo json_encode([
        'error' => true,
        'msg' => '删除衣物失败: ' . $e->getMessage()
    ]);
}
?> 