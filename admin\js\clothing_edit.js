/**
 * 衣物编辑脚本
 */
const ClothingEdit = {
    // 衣物ID
    clothingId: null,
    
    // 标签数组
    tags: [],
    
    // DOM元素
    elements: {},
    
    /**
     * 初始化
     */
    init: function() {
        // 获取URL参数中的衣物ID
        const urlParams = new URLSearchParams(window.location.search);
        this.clothingId = urlParams.get('id');
        
        // 初始化DOM元素引用
        this.elements = {
            pageTitle: document.getElementById('pageTitle'),
            form: document.getElementById('clothingForm'),
            nameInput: document.getElementById('name'),
            categorySelect: document.getElementById('category'),
            userSelect: document.getElementById('userId'),
            imageUrlInput: document.getElementById('imageUrl'),
            imagePreview: document.getElementById('imagePreview'),
            tagInput: document.getElementById('tagInput'),
            addTagBtn: document.getElementById('addTagBtn'),
            tagContainer: document.getElementById('tagContainer'),
            tagsHidden: document.getElementById('tags'),
            descriptionInput: document.getElementById('description')
        };
        
        // 设置页面标题
        if (this.clothingId) {
            this.elements.pageTitle.textContent = '编辑衣物';
            this.loadClothingDetails();
        } else {
            this.elements.pageTitle.textContent = '添加衣物';
        }
        
        // 加载用户列表
        this.loadUserList();
        
        // 绑定事件
        this.bindEvents();
    },
    
    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 图片URL输入变化时更新预览
        this.elements.imageUrlInput.addEventListener('input', this.updateImagePreview.bind(this));
        
        // 添加标签按钮
        this.elements.addTagBtn.addEventListener('click', this.addTag.bind(this));
        
        // 标签输入框按Enter添加
        this.elements.tagInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.addTag();
            }
        });
        
        // 表单提交
        this.elements.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitForm();
        });
    },
    
    /**
     * 加载衣物详情
     */
    loadClothingDetails: function() {
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        fetch(`${Auth.apiBaseUrl}/get_admin_clothing_details.php?id=${this.clothingId}`, {
            method: 'GET',
            headers: {
                'Authorization': token
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '加载失败');
            }
            
            const clothing = data.data.clothing;
            
            // 填充表单数据
            this.elements.nameInput.value = clothing.name || '';
            this.elements.categorySelect.value = clothing.category || '';
            this.elements.imageUrlInput.value = clothing.image_url || '';
            
            // 延迟设置用户选择，确保用户列表已加载
            setTimeout(() => {
                this.elements.userSelect.value = clothing.user_id || '';
            }, 500);
            
            // 更新图片预览
            this.updateImagePreview();
            
            // 填充标签
            if (clothing.tags_array && clothing.tags_array.length > 0) {
                this.tags = [...clothing.tags_array];
                this.updateTagsDisplay();
            }
            
            // 填充描述
            if (clothing.description) {
                if (typeof clothing.description === 'object') {
                    this.elements.descriptionInput.value = JSON.stringify(clothing.description, null, 2);
                } else {
                    this.elements.descriptionInput.value = clothing.description;
                }
            }
        })
        .catch(error => {
            console.error('获取衣物详情失败:', error);
            alert(`加载衣物详情失败: ${error.message}`);
        });
    },
    
    /**
     * 加载用户列表
     */
    loadUserList: function() {
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        fetch(`${Auth.apiBaseUrl}/get_admin_user_list.php`, {
            method: 'GET',
            headers: {
                'Authorization': token
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '加载失败');
            }
            
            const users = data.data.users;
            
            if (!users || users.length === 0) {
                this.elements.userSelect.innerHTML = '<option value="">没有可选用户</option>';
                return;
            }
            
            let options = '<option value="">请选择用户</option>';
            users.forEach(user => {
                const displayName = user.nickname || user.username || `用户${user.id}`;
                options += `<option value="${user.id}">${displayName}</option>`;
            });
            
            this.elements.userSelect.innerHTML = options;
        })
        .catch(error => {
            console.error('获取用户列表失败:', error);
            this.elements.userSelect.innerHTML = '<option value="">加载失败</option>';
        });
    },
    
    /**
     * 更新图片预览
     */
    updateImagePreview: function() {
        const imageUrl = this.elements.imageUrlInput.value.trim();
        if (imageUrl) {
            // 清空预览区域并添加图片
            this.elements.imagePreview.innerHTML = '';
            const img = document.createElement('img');
            img.src = imageUrl;
            img.alt = '衣物图片';
            img.onerror = () => {
                this.elements.imagePreview.innerHTML = '<span class="image-preview-text">图片无法加载</span>';
            };
            this.elements.imagePreview.appendChild(img);
        } else {
            this.elements.imagePreview.innerHTML = '<span class="image-preview-text">预览图片</span>';
        }
    },
    
    /**
     * 添加标签
     */
    addTag: function() {
        const tagValue = this.elements.tagInput.value.trim();
        
        if (!tagValue) return;
        
        // 检查标签是否已存在
        if (this.tags.includes(tagValue)) {
            alert('该标签已存在');
            return;
        }
        
        // 添加到标签数组
        this.tags.push(tagValue);
        
        // 更新显示
        this.updateTagsDisplay();
        
        // 清空输入框
        this.elements.tagInput.value = '';
        this.elements.tagInput.focus();
    },
    
    /**
     * 移除标签
     * @param {Number} index 标签索引
     */
    removeTag: function(index) {
        if (index >= 0 && index < this.tags.length) {
            this.tags.splice(index, 1);
            this.updateTagsDisplay();
        }
    },
    
    /**
     * 更新标签显示
     */
    updateTagsDisplay: function() {
        // 清空标签容器
        this.elements.tagContainer.innerHTML = '';
        
        // 将标签数组转换为逗号分隔的字符串
        this.elements.tagsHidden.value = this.tags.join(',');
        
        // 如果没有标签，直接返回
        if (this.tags.length === 0) return;
        
        // 创建标签元素
        this.tags.forEach((tag, index) => {
            const tagElement = document.createElement('div');
            tagElement.className = 'tag-item';
            tagElement.innerHTML = `
                <span>${tag}</span>
                <span class="tag-remove" data-index="${index}">&times;</span>
            `;
            
            // 添加到容器
            this.elements.tagContainer.appendChild(tagElement);
            
            // 绑定删除事件
            const removeBtn = tagElement.querySelector('.tag-remove');
            removeBtn.addEventListener('click', () => this.removeTag(index));
        });
    },
    
    /**
     * 表单验证
     * @returns {Boolean} 验证结果
     */
    validateForm: function() {
        const name = this.elements.nameInput.value.trim();
        const category = this.elements.categorySelect.value;
        const userId = this.elements.userSelect.value;
        
        if (!name) {
            alert('请输入衣物名称');
            this.elements.nameInput.focus();
            return false;
        }
        
        if (!category) {
            alert('请选择衣物分类');
            this.elements.categorySelect.focus();
            return false;
        }
        
        if (!userId) {
            alert('请选择所属用户');
            this.elements.userSelect.focus();
            return false;
        }
        
        // 验证JSON格式的描述
        const description = this.elements.descriptionInput.value.trim();
        if (description) {
            try {
                JSON.parse(description);
            } catch (e) {
                alert('详细属性必须是有效的JSON格式');
                this.elements.descriptionInput.focus();
                return false;
            }
        }
        
        return true;
    },
    
    /**
     * 提交表单
     */
    submitForm: function() {
        if (!this.validateForm()) return;
        
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        // 构建请求数据
        const formData = {
            name: this.elements.nameInput.value.trim(),
            category: this.elements.categorySelect.value,
            user_id: parseInt(this.elements.userSelect.value),
            image_url: this.elements.imageUrlInput.value.trim(),
            tags: this.elements.tagsHidden.value,
            description: this.elements.descriptionInput.value.trim()
        };
        
        // 如果是编辑模式，添加ID
        if (this.clothingId) {
            formData.id = parseInt(this.clothingId);
        }
        
        // 发送请求
        fetch(`${Auth.apiBaseUrl}/update_admin_clothing.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': token
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '操作失败');
            }
            
            alert(data.msg || '保存成功');
            
            // 如果是新增模式，跳转到编辑模式
            if (!this.clothingId && data.data && data.data.id) {
                window.location.href = `clothing_edit.html?id=${data.data.id}`;
            } else {
                // 如果是编辑模式，重新加载数据
                this.loadClothingDetails();
            }
        })
        .catch(error => {
            console.error('保存衣物失败:', error);
            alert(`操作失败: ${error.message}`);
        });
    }
}; 