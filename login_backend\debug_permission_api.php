<?php
/**
 * 权限检查API调试工具
 * 用于调试权限检查API的问题
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

echo "=== 权限检查API调试 ===\n";
echo "请求时间: " . date('Y-m-d H:i:s') . "\n";
echo "请求方法: " . $_SERVER['REQUEST_METHOD'] . "\n";
echo "请求URI: " . $_SERVER['REQUEST_URI'] . "\n\n";

// 显示所有GET参数
echo "GET参数:\n";
foreach ($_GET as $key => $value) {
    echo "- $key: $value\n";
}

// 显示所有POST参数
echo "\nPOST参数:\n";
foreach ($_POST as $key => $value) {
    echo "- $key: $value\n";
}

// 显示请求头
echo "\n请求头:\n";
foreach (getallheaders() as $name => $value) {
    echo "- $name: $value\n";
}

// 检查授权头
echo "\n授权检查:\n";
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "❌ 缺少Authorization头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
echo "原始Token: $token\n";

if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
    echo "处理后Token: $token\n";
}

// 验证token
require_once 'config.php';
require_once 'auth.php';

try {
    $auth = new Auth();
    $payload = $auth->verifyToken($token);
    
    if ($payload) {
        echo "✅ Token验证成功\n";

        // 修复：从payload中正确获取用户ID
        $userId = $payload['user_id'] ?? $payload['sub'] ?? null;
        echo "用户ID: " . ($userId ?? 'null') . "\n";
        echo "用户名: " . ($payload['username'] ?? '未知') . "\n";

        if (!$userId) {
            echo "❌ Token中缺少用户ID信息\n";
            exit;
        }
    } else {
        echo "❌ Token验证失败\n";
        exit;
    }
} catch (Exception $e) {
    echo "❌ Token验证异常: " . $e->getMessage() . "\n";
    exit;
}

// 获取参数
$dataType = $_GET['data_type'] ?? $_POST['data_type'] ?? '';
$dataId = $_GET['data_id'] ?? $_POST['data_id'] ?? '';
$operation = $_GET['operation'] ?? $_POST['operation'] ?? '';

echo "\n参数检查:\n";
echo "- data_type: '$dataType'\n";
echo "- data_id: '$dataId'\n";
echo "- operation: '$operation'\n";

if (empty($dataType)) {
    echo "❌ 缺少data_type参数\n";
}
if (empty($operation)) {
    echo "❌ 缺少operation参数\n";
}

// 如果参数完整，尝试调用权限检查
if (!empty($dataType) && !empty($operation)) {
    echo "\n权限检查结果:\n";
    
    try {
        require_once 'db.php';
        
        $db = new Database();
        $conn = $db->getConnection();
        $userId = $payload['user_id'] ?? $payload['sub'] ?? null;
        
        // 调用权限检查函数
        $permission = checkCirclePermission($conn, $userId, $dataType, $dataId, $operation);
        
        echo "✅ 权限检查完成\n";
        echo "结果: " . json_encode($permission, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        
    } catch (Exception $e) {
        echo "❌ 权限检查异常: " . $e->getMessage() . "\n";
        echo "文件: " . $e->getFile() . "\n";
        echo "行号: " . $e->getLine() . "\n";
        echo "堆栈: " . $e->getTraceAsString() . "\n";
    }
}

echo "\n=== 调试完成 ===\n";

/**
 * 权限检查函数（从check_circle_permission.php复制）
 */
function checkCirclePermission($conn, $userId, $dataType, $dataId, $operation) {
    $result = [
        'allowed' => false,
        'reason' => '',
        'user_role' => '',
        'is_owner' => false,
        'is_circle_member' => false
    ];
    
    echo "开始权限检查: userId=$userId, dataType=$dataType, dataId=$dataId, operation=$operation\n";
    
    // 如果是创建操作，只需要检查用户是否在圈子中
    if ($operation === 'create') {
        return checkCreatePermission($conn, $userId, $result);
    }
    
    // 如果没有指定数据ID，无法检查权限
    if (empty($dataId)) {
        $result['reason'] = '缺少数据ID';
        return $result;
    }
    
    // 根据数据类型获取数据信息
    $dataInfo = getDataInfo($conn, $dataType, $dataId);
    
    if (!$dataInfo) {
        $result['reason'] = '数据不存在';
        return $result;
    }
    
    echo "数据信息: " . json_encode($dataInfo) . "\n";
    
    // 检查是否是数据的创建者
    $result['is_owner'] = ($dataInfo['user_id'] == $userId);
    
    // 如果是个人数据（没有circle_id），只有创建者可以操作
    if (empty($dataInfo['circle_id'])) {
        if ($result['is_owner']) {
            $result['allowed'] = true;
            $result['reason'] = '数据创建者';
        } else {
            $result['reason'] = '非数据创建者，无权操作个人数据';
        }
        return $result;
    }
    
    // 检查用户在圈子中的角色
    $circleRole = getUserCircleRole($conn, $userId, $dataInfo['circle_id']);
    
    if (!$circleRole) {
        $result['reason'] = '用户不在该圈子中';
        return $result;
    }
    
    echo "圈子角色: " . json_encode($circleRole) . "\n";
    
    $result['is_circle_member'] = true;
    $result['user_role'] = $circleRole['role'];
    
    // 权限规则
    switch ($operation) {
        case 'view':
            $result['allowed'] = true;
            $result['reason'] = '圈子成员查看权限';
            break;
            
        case 'edit':
            if ($circleRole['role'] === 'creator' || $result['is_owner']) {
                $result['allowed'] = true;
                $result['reason'] = $circleRole['role'] === 'creator' ? '圈子创建者权限' : '数据创建者权限';
            } else {
                $result['reason'] = '只有圈子创建者或数据创建者可以编辑';
            }
            break;
            
        case 'delete':
            if ($circleRole['role'] === 'creator' || $result['is_owner']) {
                $result['allowed'] = true;
                $result['reason'] = $circleRole['role'] === 'creator' ? '圈子创建者权限' : '数据创建者权限';
            } else {
                $result['reason'] = '只有圈子创建者或数据创建者可以删除';
            }
            break;
            
        default:
            $result['reason'] = '未知操作类型';
    }
    
    return $result;
}

function checkCreatePermission($conn, $userId, $result) {
    $stmt = $conn->prepare("
        SELECT cm.circle_id, cm.role, c.name as circle_name
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'
        LIMIT 1
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $circle = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($circle) {
        $result['allowed'] = true;
        $result['is_circle_member'] = true;
        $result['user_role'] = $circle['role'];
        $result['reason'] = '圈子成员创建权限';
    } else {
        $result['reason'] = '用户不在任何圈子中';
    }
    
    return $result;
}

function getDataInfo($conn, $dataType, $dataId) {
    $table = '';
    switch ($dataType) {
        case 'clothes':
            $table = 'clothes';
            break;
        case 'outfits':
            $table = 'outfits';
            break;
        case 'wardrobes':
            $table = 'wardrobes';
            break;
        case 'categories':
            $table = 'clothing_categories';
            break;
        default:
            return null;
    }
    
    $stmt = $conn->prepare("SELECT user_id, circle_id FROM $table WHERE id = :id");
    $stmt->bindParam(':id', $dataId, PDO::PARAM_INT);
    $stmt->execute();
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getUserCircleRole($conn, $userId, $circleId) {
    $stmt = $conn->prepare("
        SELECT role, status 
        FROM circle_members 
        WHERE user_id = :user_id AND circle_id = :circle_id AND status = 'active'
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->execute();
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}
?>
