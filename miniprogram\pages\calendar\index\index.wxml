<view class="container">

  
  <!-- 月份选择区域 -->
  <view class="month-selector">
    <view class="month-btn" bindtap="changeMonth" data-direction="prev">
      <text class="arrow">〈</text>
    </view>
    <view class="month-text">{{year}}年{{month}}月</view>
    <view class="month-btn" bindtap="changeMonth" data-direction="next">
      <text class="arrow">〉</text>
    </view>
    <view class="today-btn" bindtap="goToToday">今天</view>
  </view>
  
  <!-- 星期表头 -->
  <view class="weekday-header">
    <view class="weekday">日</view>
    <view class="weekday">一</view>
    <view class="weekday">二</view>
    <view class="weekday">三</view>
    <view class="weekday">四</view>
    <view class="weekday">五</view>
    <view class="weekday">六</view>
  </view>
  
  <!-- 日期网格 -->
  <view class="calendar-grid">
    <view 
      class="day-cell {{item.currentMonth ? 'current-month' : 'other-month'}} {{item.isToday ? 'today' : ''}} {{outfitCalendarData[item.date] ? 'has-outfit' : ''}} {{item.date === selectedDate ? 'selected' : ''}}"
      wx:for="{{days}}" 
      wx:key="date"
      bindtap="tapDay"
      data-date="{{item.date}}"
    >
      <view class="day-number">{{item.day}}</view>
      <view class="outfit-indicator" wx:if="{{outfitCalendarData[item.date]}}">
        <view class="outfit-name-tag">{{outfitCalendarData[item.date].name || '穿搭'}}</view>
        <image 
          class="outfit-thumbnail" 
          src="{{outfitCalendarData[item.date].thumbnail_url || '/images/outfit_placeholder.png'}}" 
          mode="aspectFill"
        ></image>
      </view>
    </view>
  </view>

  <!-- 无穿搭时的添加按钮 -->
  <view class="add-outfit-container" wx:if="{{selectedDate && !currentDayOutfit}}">
    <view class="selected-date-info">
      <text class="selected-date-text">{{selectedDate}}</text>
      <text class="no-outfit-text">暂无穿搭安排</text>
    </view>
    <view class="action-buttons">
      <view class="action-btn add-btn" bindtap="addOutfitTap">选择已有穿搭</view>
      <view class="action-btn create-btn" bindtap="createOutfit">创建新穿搭</view>
    </view>
  </view>

  <!-- 当前选中日期的穿搭展示 - 使用detail风格 -->
  <view class="outfit-detail-container" wx:if="{{selectedDate && currentDayOutfit}}">
    <view class="date-header">
      <view class="header-left">
        <text class="date-text">{{selectedDate}} 的穿搭</text>
        <text class="outfit-name">{{currentDayOutfit.name}}</text>
      </view>
      <view class="delete-icon-container" bindtap="cancelOutfit">
        <view class="delete-icon">
          <text class="delete-icon-line">×</text>
        </view>
      </view>
    </view>
    
    <!-- 穿搭信息 - 衣物列表 -->
    <view class="outfit-info" wx:if="{{currentDayOutfit.items && currentDayOutfit.items.length > 0}}">
      <view class="outfit-items">
        <view class="items-title">包含的衣物 ({{currentDayOutfit.items.length}})</view>
        <scroll-view scroll-x class="items-scroll" enhanced="true" show-scrollbar="false">
          <view class="items-container">
            <view 
              wx:for="{{currentDayOutfit.items}}" 
              wx:key="clothing_id"
              class="item-preview"
              bindtap="viewOutfit">
              <image src="{{item.clothing_data.image_url || '/images/outfit_placeholder.png'}}" mode="aspectFit" class="preview-image"></image>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
  
  <!-- 加载指示器 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
  </view>
</view>

<!-- 穿搭选择弹出层 -->
<view class="popup-mask" wx:if="{{showOutfitPopup}}" bindtap="closeOutfitPopup"></view>
<view class="popup-container {{showOutfitPopup ? 'popup-show' : ''}}">
  <view class="popup-header">
    <text class="popup-title">选择穿搭</text>
    <view class="popup-close" bindtap="closeOutfitPopup">×</view>
  </view>
  
  <!-- 添加穿搭分类水平滚动列表 -->
  <view class="categories-scroll-container">
    <scroll-view scroll-x class="categories-scroll" enable-flex enhanced show-scrollbar="{{false}}">
      <view 
        class="category-item {{currentCategoryId === 'all' ? 'category-active' : ''}}" 
        bindtap="switchPopupCategory" 
        data-id="all">
        全部
      </view>
      <view 
        wx:for="{{categories}}" 
        wx:key="id"
        class="category-item {{currentCategoryId === item.id ? 'category-active' : ''}}"
        bindtap="switchPopupCategory"
        data-id="{{item.id}}">
        {{item.name}}
      </view>
    </scroll-view>
  </view>
  
  <view class="popup-content">
    <view class="popup-hint" wx:if="{{outfits.length === 0 && !loading}}">
      <text>您还没有创建穿搭</text>
    </view>
    
    <view class="popup-loading" wx:if="{{loading}}">
      <image class="loading-icon" src="/images/loading.gif" mode="aspectFit"></image>
      <text>加载中...</text>
    </view>
    
    <view class="popup-hint" wx:if="{{outfits.length > 0 && filteredOutfits.length === 0 && !loading}}">
      <text>该分类下暂无穿搭</text>
    </view>
    
    <scroll-view 
      scroll-y 
      class="outfits-scroll-view" 
      wx:if="{{filteredOutfits.length > 0 && !loading}}"
      enable-flex="true"
      enhanced="true"
      show-scrollbar="true"
      bounces="true">
      <view class="outfit-items-container">
        <view class="outfit-item" wx:for="{{filteredOutfits}}" wx:key="id" bindtap="selectOutfit" data-id="{{item.id}}">
          <view class="outfit-preview">
            <!-- 穿搭预览 -->
            <view class="outfit-canvas">
              <view 
                wx:for="{{item.items}}" 
                wx:for-item="clothing" 
                wx:key="id"
                class="clothing-item"
                style="left:{{clothing.previewPosition.x}}px; top:{{clothing.previewPosition.y}}px; width:{{clothing.size.width * clothing.previewPosition.scale}}px; height:{{clothing.size.height * clothing.previewPosition.scale}}px; transform:rotate({{clothing.rotation || 0}}deg); z-index:{{clothing.z_index || 1}};"
              >
                <image 
                  src="{{clothing.clothing_data.image_url || '/images/outfit_placeholder.png'}}" 
                  mode="aspectFit"
                  style="width: 100%; height: 100%;"
                ></image>
              </view>
              <!-- 当没有衣物时显示占位图 -->
              <image 
                wx:if="{{!item.items || item.items.length === 0}}"
                src="/images/outfit_placeholder.png" 
                mode="aspectFit"
                class="outfit-placeholder"
              ></image>
            </view>
          </view>
          <view class="outfit-info">
            <text class="outfit-name">{{item.name || '未命名穿搭'}}</text>
            <text class="outfit-date">{{item.formatted_date || item.created_at}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view> 