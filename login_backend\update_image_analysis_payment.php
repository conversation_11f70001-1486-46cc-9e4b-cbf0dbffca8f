<?php
/**
 * 更新个人形象分析支付状态API
 * 提供给前端在支付成功后调用，更新形象分析记录的支付状态
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 日志功能
function writePaymentLog($message, $data = null) {
    $logDir = __DIR__ . '/logs';
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/payment_update_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $logData = "[{$timestamp}] {$message}";
    
    if ($data !== null) {
        $logData .= " - " . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    file_put_contents($logFile, $logData . PHP_EOL, FILE_APPEND);
}

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

writePaymentLog("收到支付更新请求", $_SERVER);

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    writePaymentLog("请求方法错误", ['method' => $_SERVER['REQUEST_METHOD']]);
    echo json_encode(['error' => true, 'msg' => 'Method Not Allowed']);
    exit;
}

// 检查是否有Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    writePaymentLog("缺少Authorization头");
    echo json_encode(['error' => true, 'msg' => 'Authorization header is required']);
    exit;
}

// 验证token
$token = $_SERVER['HTTP_AUTHORIZATION'];
$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    http_response_code(401);
    writePaymentLog("无效或过期的Token");
    echo json_encode(['error' => true, 'msg' => 'Invalid or expired token']);
    exit;
}

// 获取用户ID
$userId = $payload['sub'];
writePaymentLog("用户ID", ['user_id' => $userId]);

// 获取请求数据
$rawData = file_get_contents('php://input');
$requestData = json_decode($rawData, true);
writePaymentLog("请求数据", $requestData);

// 验证必要字段
if (!isset($requestData['analysis_id']) || empty($requestData['analysis_id'])) {
    http_response_code(400);
    writePaymentLog("缺少必要字段", ['missing' => 'analysis_id']);
    echo json_encode(['error' => true, 'msg' => '缺少必要字段: analysis_id']);
    exit;
}

$analysisId = intval($requestData['analysis_id']);
writePaymentLog("分析ID", ['analysis_id' => $analysisId]);

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

try {
    // 先验证形象分析记录是否存在且属于当前用户
    $verifyStmt = $conn->prepare("
        SELECT id, payment_status FROM user_image_analysis 
        WHERE id = :analysis_id AND user_id = :user_id
    ");
    $verifyStmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
    $verifyStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $verifyStmt->execute();
    
    $analysis = $verifyStmt->fetch(PDO::FETCH_ASSOC);
    writePaymentLog("验证查询结果", $analysis);
    
    if (!$analysis) {
        http_response_code(404);
        writePaymentLog("未找到记录", ['analysis_id' => $analysisId, 'user_id' => $userId]);
        echo json_encode(['error' => true, 'msg' => '未找到形象分析记录或该记录不属于您']);
        exit;
    }
    
    // 检查是否已经是已支付状态
    if ($analysis['payment_status'] === 'paid') {
        writePaymentLog("记录已处于已支付状态", ['analysis_id' => $analysisId]);
        echo json_encode([
            'error' => false,
            'msg' => '该记录已经是已支付状态'
        ]);
        exit;
    }
    
    // 更新支付状态为已支付
    $updateStmt = $conn->prepare("
        UPDATE user_image_analysis 
        SET 
            payment_status = 'paid'
        WHERE id = :analysis_id
    ");
    $updateStmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
    $updateStmt->execute();
    
    $rowsAffected = $updateStmt->rowCount();
    writePaymentLog("更新支付状态结果", ['analysis_id' => $analysisId, 'rows_affected' => $rowsAffected]);
    
    // 验证更新是否成功
    $checkStmt = $conn->prepare("
        SELECT payment_status FROM user_image_analysis 
        WHERE id = :analysis_id
    ");
    $checkStmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
    $checkStmt->execute();
    $updatedStatus = $checkStmt->fetch(PDO::FETCH_ASSOC);
    writePaymentLog("更新后的支付状态", $updatedStatus);
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'msg' => '支付状态更新成功'
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    writePaymentLog("异常", ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
    echo json_encode(['error' => true, 'msg' => '更新支付状态失败: ' . $e->getMessage()]);
} 