<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>次元衣柜 - 管理后台</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* 仪表盘样式 */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-gap: 20px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 1200px) {
            .dashboard-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .stats-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            text-align: center;
        }
        
        .stats-card .number {
            font-size: 2rem;
            font-weight: 600;
            color: #1890ff;
            margin: 10px 0;
        }
        
        .stats-card .title {
            font-size: 1rem;
            color: #666;
            margin-bottom: 10px;
        }
        
        .stats-card .sub-info {
            font-size: 0.85rem;
            color: #999;
        }
        
        .stats-card .sub-info .highlight {
            color: #1890ff;
            font-weight: 500;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }
        
        .half-width-charts {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 20px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .half-width-charts {
                grid-template-columns: 1fr;
            }
        }
        
        .api-quota-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .api-quota-card h3 {
            font-size: 1.1rem;
            margin-bottom: 15px;
            color: #333;
        }
        
        .quota-item {
            margin-bottom: 15px;
        }
        
        .quota-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .quota-name {
            font-weight: 500;
        }
        
        .quota-value {
            color: #1890ff;
        }
        
        .progress-bar {
            height: 6px;
            background-color: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #1890ff;
            border-radius: 3px;
            transition: width 0.3s ease;
        }
        
        .loading-indicator {
            text-align: center;
            padding: 20px;
            color: #666;
            display: none;
        }
        
        /* 替换错误消息样式 */
        #dashboardError {
            display: none;
            margin: 15px 0;
        }
        
        /* 日期筛选样式 */
        .date-filter {
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .filter-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
        }
        
        .predefined-periods {
            min-width: 120px;
        }
        
        .period-select {
            padding: 6px 10px;
            border-radius: 4px;
            border: 1px solid #d9d9d9;
            width: 100%;
        }
        
        .custom-date-range {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }
        
        .date-input-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .date-input {
            padding: 6px 10px;
            border-radius: 4px;
            border: 1px solid #d9d9d9;
        }
        
        .filter-btn {
            padding: 6px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .filter-btn:hover {
            background-color: #40a9ff;
        }
        
        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .predefined-periods {
                width: 100%;
            }
            
            .custom-date-range {
                width: 100%;
            }
        }
        
        /* 添加刷新按钮样式 */
        .refresh-btn {
            padding: 6px 16px;
            background-color: #52c41a;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .refresh-btn:hover {
            background-color: #73d13d;
        }
        
        /* 添加缓存信息样式 */
        .cache-info {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>仪表盘</h2>
                <div class="user-info">
                    <span id="userName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div id="dashboardLoading" class="loading-indicator">正在加载仪表盘数据...</div>
            
            <div id="dashboardError" class="alert alert-danger mx-3 mt-3" style="display: none;"></div>
            
            <!-- 概览卡片 -->
            <div class="dashboard-grid">
                <!-- 用户统计 -->
                <div class="stats-card users">
                    <div class="stats-card-icon">
                        <svg viewBox="64 64 896 896" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                            <path d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"/>
                        </svg>
                    </div>
                    <div class="title">用户总数</div>
                    <div class="number" id="totalUsers">0</div>
                    <div class="sub-info">近7天新增：<span class="highlight" id="newUsers">0</span></div>
                </div>
                
                <!-- 衣物统计 -->
                <div class="stats-card clothes">
                    <div class="stats-card-icon">
                        <svg viewBox="64 64 896 896" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                            <path d="M830.24 372.98L541.8 84.87a64.15 64.15 0 00-45.56-18.89h-45.5l45.64 45.64 3.78 3.77c10.24 11.76 16.08 26.92 16.08 43.2 0 35.35-28.65 64-64 64a64.15 64.15 0 01-43.2-16.08l-3.77-3.78-45.98-45.98h-44.8c-17.24 0-33.79 6.94-45.82 19.28L193.7 372.92l81.56 81.56 108.79-108.79L548.74 509.4c-12.31-7.68-26.88-12.08-42.43-12.08-44.18 0-80 35.82-80 80 0 15.56 4.39 30.13 12.08 42.43L274.7 783.44l-81.56-81.56-47.06 47.06a64.26 64.26 0 00-18.83 45.56c0 17.24 6.94 33.79 19.28 45.82l45.24 45.86a63.63 63.63 0 0045.82 19.28c17.24 0 33.79-6.94 45.56-18.83l289.21-289.21c12.31 7.68 26.88 12.08 42.43 12.08a80.13 80.13 0 0080-80c0-15.56-4.39-30.13-12.08-42.43l.35-.35 164.99-164.99-17.42-17.42z"/>
                        </svg>
                    </div>
                    <div class="title">衣物总数</div>
                    <div class="number" id="totalClothes">0</div>
                    <div class="sub-info">近7天新增：<span class="highlight" id="newClothes">0</span></div>
                </div>
                
                <!-- 照片统计 -->
                <div class="stats-card photos">
                    <div class="stats-card-icon">
                        <svg viewBox="64 64 896 896" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                            <path d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2zM304 456a88 88 0 100-176 88 88 0 000 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z"/>
                        </svg>
                    </div>
                    <div class="title">照片总数</div>
                    <div class="number" id="totalPhotos">0</div>
                    <div class="sub-info">近7天新增：<span class="highlight" id="newPhotos">0</span></div>
                </div>
                
                <!-- 试衣统计 -->
                <div class="stats-card try-ons">
                    <div class="stats-card-icon">
                        <svg viewBox="64 64 896 896" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                            <path d="M300 328a60 60 0 10120 0 60 60 0 10-120 0zM852 64H172c-17.7 0-32 14.3-32 32v660c0 17.7 14.3 32 32 32h680c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-32 660H204V128h616v596zM604 328a60 60 0 10120 0 60 60 0 10-120 0zm250.2 556H169.8c-16.5 0-29.8 14.3-29.8 32v36c0 4.4 3.3 8 7.4 8h729.1c4.1 0 7.4-3.6 7.4-8v-36c.1-17.7-13.2-32-29.7-32zM664 508H360c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h304c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"/>
                        </svg>
                    </div>
                    <div class="title">试衣总次数</div>
                    <div class="number" id="totalTryOns">0</div>
                    <div class="sub-info">近7天新增：<span class="highlight" id="newTryOns">0</span></div>
                </div>
                
                <!-- 智能推荐统计 -->
                <div class="stats-card recommendations">
                    <div class="stats-card-icon">
                        <svg viewBox="64 64 896 896" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                            <path d="M832 64H192c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32zm0 136H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32zm0 136H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32zm0 136H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32zm0 136H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"/>
                        </svg>
                    </div>
                    <div class="title">推荐总次数</div>
                    <div class="number" id="totalRecommendations">0</div>
                    <div class="sub-info">近7天新增：<span class="highlight" id="newRecommendations">0</span></div>
                </div>
                
                <!-- 打赏统计 -->
                <div class="stats-card donations">
                    <div class="stats-card-icon">
                        <svg viewBox="64 64 896 896" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                            <path d="M911.5 699.7a8 8 0 00-10.3-4.8L840 717.2V179c0-37.6-30.4-68-68-68H252c-37.6 0-68 30.4-68 68v538.2l-61.3-22.3a8 8 0 00-10.3 4.8L82 763.3a8 8 0 004.8 10.3l175.9 64c5.7 2 11.8 2 17.5 0l146-53.3 146.2 53.3c5.7 2 11.8 2 17.5 0l176-64a8 8 0 004.8-10.3l-30.3-82zM512 480c-35.3 0-64-28.7-64-64s28.7-64 64-64 64 28.7 64 64-28.7 64-64 64zm184-152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm0 210H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z"/>
                        </svg>
                    </div>
                    <div class="title">打赏总次数</div>
                    <div class="number" id="totalDonations">0</div>
                    <div class="sub-info">打赏总金额：<span class="highlight" id="totalDonationAmount">¥0.00</span></div>
                </div>

                <!-- 淘宝商品统计 -->
                <div class="stats-card taobao-products">
                    <div class="stats-card-icon">
                        <svg viewBox="64 64 896 896" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                            <path d="M832 312H696v-16c0-101.6-82.4-184-184-184s-184 82.4-184 184v16H192c-17.7 0-32 14.3-32 32v536c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V344c0-17.7-14.3-32-32-32zm-432-16c0-61.9 50.1-112 112-112s112 50.1 112 112v16H400v-16zm392 544H232V384h96v88c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-88h224v88c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-88h96v456z"/>
                        </svg>
                    </div>
                    <div class="title">淘宝商品总数</div>
                    <div class="number" id="totalTaobaoProducts">加载中...</div>
                    <div class="sub-info"><a href="taobao_product_list.html" style="color:#1890ff;text-decoration:none;">点击管理淘宝商品 >></a></div>
                </div>

                <!-- 推荐分布卡片 -->
                <div class="stats-card recommendation-types">
                    <div class="stats-card-icon">
                        <svg viewBox="64 64 896 896" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                            <path d="M304 280h416c4.4 0 8-3.6 8-8 0-40-8.8-76.7-25.9-108.1-17.2-31.5-42.5-56.8-74-74C596.7 72.8 560 64 520 64h-16c-40 0-76.7 8.8-108.1 25.9-31.5 17.2-56.8 42.5-74 74C305.8 195.3 297 232 297 272c0 4.4 3.6 8 8 8zm66.3-113.1c12.9-22.8 31.1-41 53.9-53.9 23-13 49.1-19.9 75.8-19.9h16c26.7 0 52.8 6.9 75.8 19.9 22.8 12.9 41 31.1 53.9 53.9 13 23 19.9 49.1 19.9 75.8H350.4c.1-26.7 7-52.8 19.9-75.8zM880 356H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32zm0 136H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32zm0 136H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32zm0 136H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"/>
                        </svg>
                    </div>
                    <div class="title">推荐类型分布</div>
                    <div style="height: 120px;" class="chart-container">
                        <canvas id="recommendationTypeChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 趋势图表 -->
            <div class="card">
                <div class="chart-title">数据趋势</div>
                
                <!-- 添加日期筛选组件 -->
                <div class="date-filter">
                    <div class="filter-row">
                        <div class="predefined-periods">
                            <select id="periodSelector" class="period-select">
                                <option value="7">最近7天</option>
                                <option value="30">最近30天</option>
                                <option value="90">最近90天</option>
                                <option value="180">最近半年</option>
                                <option value="365">最近一年</option>
                                <option value="custom">自定义时间段</option>
                            </select>
                        </div>
                        
                        <div class="custom-date-range">
                            <div class="date-input-group">
                                <label for="startDate">开始日期:</label>
                                <input type="date" id="startDate" class="date-input">
                            </div>
                            <div class="date-input-group">
                                <label for="endDate">结束日期:</label>
                                <input type="date" id="endDate" class="date-input">
                            </div>
                            <button id="filterBtn" class="filter-btn">筛选</button>
                            <button id="refreshBtn" class="refresh-btn" title="强制刷新数据">刷新</button>
                        </div>
                    </div>
                    <div class="cache-info" id="cacheInfo"></div>
                </div>
                
                <div class="chart-container">
                    <canvas id="trendsChart"></canvas>
                </div>
            </div>
            
            <!-- 分布图表（2x2网格） -->
            <div class="half-width-charts">
                <!-- 照片类型分布 -->
                <div class="card">
                    <div class="chart-title">照片类型分布</div>
                    <div class="chart-container">
                        <canvas id="photoTypeChart"></canvas>
                    </div>
                </div>
                
                <!-- 试衣状态分布 -->
                <div class="card">
                    <div class="chart-title">试衣状态分布</div>
                    <div class="chart-container">
                        <canvas id="tryOnStatusChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 系统信息卡片 -->
            <div class="card">
                <div class="card-title">系统信息</div>
                <div id="systemInfo">
                    <p>当前账号：<span id="currentUser">加载中...</span></p>
                    <p>登录身份：管理员</p>
                    <p>登录时间：<span id="loginTime">加载中...</span></p>
                    <p>系统版本：1.0.0</p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/dashboard.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为dashboard
            Sidebar.init('dashboard');
            
            // 获取DOM元素
            const currentUser = document.getElementById('currentUser');
            const loginTime = document.getElementById('loginTime');
            const userName = document.getElementById('userName');
            const logoutBtn = document.getElementById('logoutBtn');
            const refreshBtn = document.getElementById('refreshBtn');
            const cacheInfo = document.getElementById('cacheInfo');
            
            // 显示用户信息
            const user = Auth.getCurrentUser();
            if (user) {
                userName.textContent = user.realName || user.username;
                currentUser.textContent = user.username;
            }
            
            // 显示登录时间
            loginTime.textContent = new Date().toLocaleString();
            
            // 退出登录
            logoutBtn.addEventListener('click', function() {
                Auth.logout();
            });
            
            // 强制刷新数据
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    Dashboard.forceRefresh();
                });
            }
            
            // 显示缓存信息
            function updateCacheInfo() {
                if (cacheInfo && Dashboard.cache && Dashboard.cache.enabled) {
                    const timestamp = localStorage.getItem(Dashboard.cache.keys.timestamp);
                    if (timestamp) {
                        const cacheTime = new Date(parseInt(timestamp)).toLocaleString();
                        cacheInfo.textContent = `上次更新: ${cacheTime}`;
                    } else {
                        cacheInfo.textContent = '';
                    }
                }
            }
            
            // 定期更新缓存信息
            updateCacheInfo();
            setInterval(updateCacheInfo, 60000); // 每分钟更新一次
            
            // 获取淘宝商品数量
            function loadTaobaoProductCount() {
                console.log('开始加载淘宝商品数量...');
                const totalTaobaoProducts = document.getElementById('totalTaobaoProducts');
                
                if (!totalTaobaoProducts) {
                    console.error('找不到淘宝商品总数元素');
                    return;
                }
                
                // 显示加载状态
                totalTaobaoProducts.textContent = '加载中...';
                
                fetch('../login_backend/get_stored_taobao_products.php?page=1&page_size=1', {
                    headers: {
                        'Authorization': `Bearer ${Auth.getToken()}`
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('淘宝商品数据加载成功:', data);
                    if (!data.error && data.pagination) {
                        totalTaobaoProducts.textContent = data.pagination.total || '0';
                    } else {
                        throw new Error('数据格式不正确');
                    }
                })
                .catch(error => {
                    console.error('获取淘宝商品数据失败:', error);
                    totalTaobaoProducts.textContent = '加载失败';
                });
            }
            
            // 确保在DOM加载完成后调用
            loadTaobaoProductCount();
        });
    </script>
</body>
</html> 