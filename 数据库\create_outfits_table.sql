-- Create outfits table for storing outfit data
CREATE TABLE IF NOT EXISTS outfits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    thumbnail_url TEXT NULL COMMENT '缩略图URL',
    outfit_data LONGTEXT NOT NULL COMMENT '以JSON格式存储的穿搭布局数据',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NULL ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create index on user_id for faster lookups
CREATE INDEX idx_outfit_user_id ON outfits(user_id);

-- Create index on created_at for faster sorting
CREATE INDEX idx_outfit_created_at ON outfits(created_at); 