<?php
// 引入必要的文件
require_once 'auth.php';
require_once 'db.php';
require_once 'config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 验证管理员token
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 如果有Bearer前缀，去掉它
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
}

if (!$token) {
    echo json_encode([
        'status' => 'error',
        'message' => '未提供授权Token'
    ]);
    exit;
}

// 验证管理员token
$auth = new Auth();
$adminData = $auth->verifyAdminToken($token);

if (!$adminData) {
    echo json_encode([
        'status' => 'error',
        'message' => '未授权，请先登录管理后台'
    ]);
    exit;
}

// 处理GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 检查必要参数
if (!isset($_GET['outfit_id']) || empty($_GET['outfit_id'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少穿搭ID参数'
    ]);
    exit;
}

$outfitId = intval($_GET['outfit_id']);

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();
    
    // 先检查穿搭是否存在，并获取outfit_data
    $stmt = $conn->prepare("SELECT id, user_id, outfit_data FROM outfits WHERE id = :outfit_id");
    $stmt->bindParam(':outfit_id', $outfitId, PDO::PARAM_INT);
    $stmt->execute();
    
    $outfit = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$outfit) {
        echo json_encode([
            'status' => 'error',
            'message' => '穿搭不存在'
        ]);
        exit;
    }
    
    // 解析outfit_data JSON
    $outfitData = json_decode($outfit['outfit_data'], true);
    
    // 检查是否存在衣物项
    if (!isset($outfitData['items']) || empty($outfitData['items'])) {
        // 如果没有衣物项，返回空数组
        echo json_encode([
            'status' => 'success',
            'data' => [
                'clothes' => [],
                'total' => 0
            ]
        ]);
        exit;
    }
    
    // 提取所有衣物ID
    $clothesIds = [];
    $clothesPositions = [];
    
    foreach ($outfitData['items'] as $item) {
        if (isset($item['id'])) {
            $clothesIds[] = $item['id'];
            $clothesPositions[$item['id']] = [
                'position_x' => isset($item['x']) ? $item['x'] : 0,
                'position_y' => isset($item['y']) ? $item['y'] : 0,
                'z_index' => isset($item['zIndex']) ? $item['zIndex'] : 0,
                'scale' => isset($item['scale']) ? $item['scale'] : 1,
                'rotation' => isset($item['rotation']) ? $item['rotation'] : 0
            ];
        }
    }
    
    if (empty($clothesIds)) {
        // 如果没有有效的衣物ID，返回空数组
        echo json_encode([
            'status' => 'success',
            'data' => [
                'clothes' => [],
                'total' => 0
            ]
        ]);
        exit;
    }
    
    // 将ID数组转换为占位符字符串
    $placeholders = implode(',', array_fill(0, count($clothesIds), '?'));
    
    // 获取衣物数据
    $stmt = $conn->prepare("
        SELECT * FROM clothes
        WHERE id IN ($placeholders)
    ");
    
    // 绑定所有衣物ID
    foreach ($clothesIds as $index => $id) {
        $stmt->bindValue($index + 1, $id, PDO::PARAM_INT);
    }
    
    $stmt->execute();
    $clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 添加位置信息到衣物数据
    foreach ($clothes as &$cloth) {
        if (isset($clothesPositions[$cloth['id']])) {
            $cloth = array_merge($cloth, $clothesPositions[$cloth['id']]);
        }
        
        // 转换描述字段的JSON数据（如果有）
        if (isset($cloth['description']) && !empty($cloth['description'])) {
            $cloth['description'] = json_decode($cloth['description'], true);
        }
        
        // 设置衣物的类型字段
        $cloth['type'] = $cloth['category'];
    }
    
    // 返回结果
    echo json_encode([
        'status' => 'success',
        'data' => [
            'clothes' => $clothes,
            'total' => count($clothes)
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => '获取穿搭衣物失败: ' . $e->getMessage()
    ]);
} 