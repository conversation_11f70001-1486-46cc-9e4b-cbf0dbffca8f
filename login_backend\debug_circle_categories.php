<?php
/**
 * 调试圈子分类数据的脚本
 * 用于排查分类数据加载问题
 */

require_once 'config.php';
require_once 'db.php';

// 测试用户ID（请替换为实际的测试用户ID）
$testUserId = 3;

echo "=== 圈子分类数据调试 ===\n\n";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 1. 检查用户所在的圈子
    echo "1. 检查用户 $testUserId 所在的圈子:\n";
    $stmt = $conn->prepare("
        SELECT cm.circle_id, c.name as circle_name, cm.status, cm.role
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id
    ");
    $stmt->bindParam(':user_id', $testUserId, PDO::PARAM_INT);
    $stmt->execute();
    $circles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($circles)) {
        echo "用户不在任何圈子中\n\n";
    } else {
        foreach ($circles as $circle) {
            echo "- 圈子ID: {$circle['circle_id']}, 名称: {$circle['circle_name']}, 状态: {$circle['status']}, 角色: {$circle['role']}\n";
        }
        echo "\n";
    }
    
    // 2. 检查个人分类
    echo "2. 检查用户个人分类:\n";
    $stmt = $conn->prepare("
        SELECT id, name, code, is_system, circle_id
        FROM clothing_categories
        WHERE (is_system = 1 AND user_id = :user_id) OR (is_system = 0 AND user_id = :user_id)
        ORDER BY sort_order ASC
    ");
    $stmt->bindParam(':user_id', $testUserId, PDO::PARAM_INT);
    $stmt->execute();
    $personalCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "个人分类数量: " . count($personalCategories) . "\n";
    foreach ($personalCategories as $cat) {
        echo "- ID: {$cat['id']}, 名称: {$cat['name']}, 代码: {$cat['code']}, 系统: {$cat['is_system']}, 圈子ID: {$cat['circle_id']}\n";
    }
    echo "\n";
    
    // 3. 检查圈子共享分类
    echo "3. 检查圈子共享分类:\n";
    if (!empty($circles)) {
        $circleIds = array_column($circles, 'circle_id');
        $placeholders = str_repeat('?,', count($circleIds) - 1) . '?';
        
        $stmt = $conn->prepare("
            SELECT c.id, c.name, c.code, c.is_system, c.circle_id, u.nickname as creator_nickname
            FROM clothing_categories c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE c.circle_id IN ($placeholders)
            ORDER BY c.sort_order ASC
        ");
        $stmt->execute($circleIds);
        $sharedCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "圈子共享分类数量: " . count($sharedCategories) . "\n";
        foreach ($sharedCategories as $cat) {
            echo "- ID: {$cat['id']}, 名称: {$cat['name']}, 代码: {$cat['code']}, 圈子ID: {$cat['circle_id']}, 创建者: {$cat['creator_nickname']}\n";
        }
    } else {
        echo "用户不在任何圈子中，无共享分类\n";
    }
    echo "\n";
    
    // 4. 测试API查询
    echo "4. 测试API查询:\n";
    
    // 测试个人数据源
    echo "个人数据源查询:\n";
    $stmt = $conn->prepare("
        SELECT id, user_id, name, code, is_system, sort_order, created_at
        FROM clothing_categories
        WHERE (is_system = 1 AND user_id = :user_id) OR (is_system = 0 AND user_id = :user_id)
        ORDER BY sort_order ASC, is_system DESC, created_at ASC
    ");
    $stmt->bindParam(':user_id', $testUserId, PDO::PARAM_INT);
    $stmt->execute();
    $personalResult = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "结果数量: " . count($personalResult) . "\n";
    
    // 测试共享数据源
    if (!empty($circles)) {
        echo "共享数据源查询:\n";
        $stmt = $conn->prepare("
            SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                   u.nickname as creator_nickname,
                   CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
            FROM clothing_categories c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
            ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC
        ");
        $stmt->bindParam(':user_id', $testUserId, PDO::PARAM_INT);
        $stmt->execute();
        $sharedResult = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "结果数量: " . count($sharedResult) . "\n";
        
        // 测试全部数据源
        echo "全部数据源查询:\n";
        $stmt = $conn->prepare("
            SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                   u.nickname as creator_nickname,
                   CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
            FROM clothing_categories c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE ((c.is_system = 1 AND c.user_id = :user_id) OR (c.is_system = 0 AND c.user_id = :user_id AND c.circle_id IS NULL)) OR
                  c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
            ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC
        ");
        $stmt->bindParam(':user_id', $testUserId, PDO::PARAM_INT);
        $stmt->execute();
        $allResult = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "结果数量: " . count($allResult) . "\n";
        
        foreach ($allResult as $cat) {
            echo "- {$cat['name']} ({$cat['code']}) - 数据源: {$cat['data_source']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 调试完成 ===\n";
?>
