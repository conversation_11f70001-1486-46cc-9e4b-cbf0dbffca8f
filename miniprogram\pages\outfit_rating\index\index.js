const app = getApp();

Page({
  data: {
    userInfo: {},
    tempImagePath: '',
    isUploading: false
  },

  onLoad: function(options) {
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo
      });
    }
  },

  // 选择照片
  chooseImage: function() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      camera: 'back',
      success: (res) => {
        this.setData({
          tempImagePath: res.tempFiles[0].tempFilePath
        });
      }
    });
  },

  // 上传照片并获取评分
  uploadImage: function() {
    if (!this.data.tempImagePath) {
      wx.showToast({
        title: '请先选择照片',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isUploading: true
    });

    wx.showLoading({
      title: '正在评分中...',
    });

    // 上传图片
    wx.uploadFile({
      url: `${app.globalData.apiBaseUrl}/outfit_rating.php`,
      filePath: this.data.tempImagePath,
      name: 'image',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        let data;
        try {
          data = JSON.parse(res.data);
        } catch (e) {
          console.error('解析响应数据失败:', e);
          wx.showToast({
            title: '评分失败，请重试',
            icon: 'none'
          });
          return;
        }

        if (data.error) {
          wx.showToast({
            title: data.msg || '评分失败',
            icon: 'none'
          });
        } else {
          // 将评分结果数据保存到全局，然后导航到结果页面
          app.globalData.tempRatingData = data.data;
          app.globalData.tempPhotoUrl = this.data.tempImagePath;
          
          wx.navigateTo({
            url: '/pages/outfit_rating/result/result',
            success: () => {
              // 导航成功后清空临时图片路径
              this.setData({
                tempImagePath: ''
              });
            }
          });
        }
      },
      fail: (err) => {
        console.error('上传失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({
          isUploading: false
        });
        wx.hideLoading();
      }
    });
  },

  // 查看历史评分
  viewHistory: function() {
    wx.navigateTo({
      url: '/pages/outfit_rating/history/history'
    });
  },

  // 分享给好友
  onShareAppMessage: function() {
    return {
      title: '穿搭打分 - 次元衣帽间',
      path: '/pages/outfit_rating/index/index',
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png'
    }
  }
}); 