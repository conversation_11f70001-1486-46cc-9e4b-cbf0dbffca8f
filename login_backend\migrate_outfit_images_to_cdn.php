<?php
/**
 * 穿搭临时衣物图片迁移脚本
 * 
 * 这个脚本专门处理outfits表中的临时衣物图片URL
 * 将服务器本地URL上传到OSS并转换为CDN URL
 * 
 * 使用方法：
 * php migrate_outfit_images_to_cdn.php [--dry-run] [--all] [--user=用户ID]
 * 
 * 参数：
 * --dry-run: 模拟运行，不实际更新数据库
 * --all: 处理所有用户的穿搭
 * --user=ID: 只处理指定用户ID的穿搭
 */

// 设置执行时间限制和内存限制
set_time_limit(0);
ini_set('memory_limit', '512M');

// 定义根目录
$rootDir = dirname(__DIR__);

// 引入配置和依赖项
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/db.php';
require_once __DIR__ . '/oss_helper.php';
require_once $rootDir . '/vendor/aliyuncs/oss-sdk-php/autoload.php'; // 使用绝对路径引入SDK autoload

// 引入OSS命名空间
use OSS\OssClient;
use OSS\Core\OssException;

// 解析命令行参数
$options = getopt('', ['dry-run', 'all', 'user:']);
$dryRun = isset($options['dry-run']);
$processAll = isset($options['all']);
$userId = isset($options['user']) ? (int)$options['user'] : null;

// 验证参数
if (!$processAll && $userId === null) {
    echo "错误: 请指定--all参数处理所有用户，或使用--user=ID参数处理特定用户\n";
    exit(1);
}

// 检查OSS配置
if (!defined('ALIYUN_ACCESS_KEY_ID') || !defined('ALIYUN_ACCESS_KEY_SECRET') || 
    !defined('ALIYUN_OSS_ENDPOINT') || !defined('ALIYUN_OSS_BUCKET')) {
    echo "错误: 请在config.php中配置OSS相关参数\n";
    exit(1);
}

// 初始化日志文件
$logFile = __DIR__ . '/logs/migrate_outfit_images_' . date('Ymd_His') . '.log';
if (!file_exists(dirname($logFile))) {
    mkdir(dirname($logFile), 0775, true);
}

// 日志函数
function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logMsg = "[$timestamp] $message";
    echo $logMsg . PHP_EOL;
    file_put_contents($logFile, $logMsg . PHP_EOL, FILE_APPEND);
}

// 检查URL是否是本地URL
function isLocalUrl($url) {
    // 检查是否为绝对路径或相对路径的本地URL
    if (strpos($url, 'http://') === 0 || strpos($url, 'https://') === 0) {
        // 检查是否是指向当前服务器的URL
        $hostInfo = parse_url($url);
        $host = $hostInfo['host'] ?? '';
        // 判断是否是本地服务器的URL（根据实际情况调整判断条件）
        $localHosts = ['localhost', '127.0.0.1', $_SERVER['HTTP_HOST'] ?? '', 'cyyg.alidog.cn'];
        return in_array($host, $localHosts);
    } else {
        // 相对路径被视为本地URL
        return true;
    }
}

// 检查是否为OSS URL
function isOssUrl($url) {
    return strpos($url, ALIYUN_OSS_BUCKET_DOMAIN) !== false;
}

// 检查是否为CDN URL
function isCdnUrl($url) {
    return defined('ALIYUN_CDN_DOMAIN') && strpos($url, ALIYUN_CDN_DOMAIN) !== false;
}

// 获取本地图片的绝对路径
function getLocalFilePath($url) {
    if (strpos($url, 'http://') === 0 || strpos($url, 'https://') === 0) {
        // 从URL中提取路径
        $parsedUrl = parse_url($url);
        $path = $parsedUrl['path'] ?? '';
        
        // 如果URL包含login_backend，提取其后部分
        if (strpos($path, '/login_backend/') !== false) {
            $relativePath = substr($path, strpos($path, '/login_backend/') + strlen('/login_backend/'));
            return __DIR__ . '/' . $relativePath;
        }
        
        // 尝试直接获取相对于网站根目录的路径
        $docRoot = $_SERVER['DOCUMENT_ROOT'] ?? dirname(__DIR__);
        return $docRoot . $path;
    } else {
        // 处理相对路径
        if (strpos($url, '/') === 0) {
            // 相对于网站根目录的路径
            $docRoot = $_SERVER['DOCUMENT_ROOT'] ?? dirname(__DIR__);
            return $docRoot . $url;
        } else {
            // 相对于当前脚本的路径
            return __DIR__ . '/' . $url;
        }
    }
}

// 开始执行
logMessage("开始执行穿搭临时衣物图片迁移" . ($dryRun ? "（模拟模式）" : ""));

// 连接数据库
try {
    $db = new Database();
    $conn = $db->getConnection();
} catch (Exception $e) {
    logMessage("数据库连接失败: " . $e->getMessage());
    exit(1);
}

// 初始化OSS助手
try {
    $ossHelper = new OssHelper();
    logMessage("OSS助手初始化成功");
} catch (Exception $e) {
    logMessage("OSS助手初始化失败: " . $e->getMessage());
    exit(1);
}

// 准备查询SQL
$sql = "SELECT id, user_id, outfit_data FROM outfits WHERE outfit_data IS NOT NULL";
$params = [];

// 如果指定了特定用户
if (!$processAll && $userId !== null) {
    $sql .= " AND user_id = :user_id";
    $params[':user_id'] = $userId;
    logMessage("只处理用户ID为 $userId 的穿搭");
} else {
    logMessage("处理所有用户的穿搭");
}

// 执行查询
try {
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $outfits = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $totalOutfits = count($outfits);
    logMessage("查询到 $totalOutfits 条穿搭记录");
} catch (Exception $e) {
    logMessage("查询穿搭数据失败: " . $e->getMessage());
    exit(1);
}

// 初始化统计数据
$stats = [
    'total_outfits' => $totalOutfits,
    'total_images' => 0,
    'uploaded_to_oss' => 0,
    'already_oss' => 0,
    'already_cdn' => 0,
    'failed' => 0,
    'updated_outfits' => 0
];

// 处理每条穿搭记录
foreach ($outfits as $index => $outfit) {
    $outfitId = $outfit['id'];
    $outfitUserId = $outfit['user_id'];
    
    logMessage("处理穿搭 #$outfitId (用户 #$outfitUserId) - " . ($index + 1) . "/$totalOutfits");
    
    // 解析JSON数据
    $outfitData = json_decode($outfit['outfit_data'], true);
    if ($outfitData === null) {
        logMessage("  错误: 穿搭 #$outfitId 的JSON数据无效，跳过");
        continue;
    }
    
    // 检查是否有items字段
    if (!isset($outfitData['items']) || !is_array($outfitData['items'])) {
        logMessage("  穿搭 #$outfitId 没有衣物项，跳过");
        continue;
    }
    
    $itemCount = count($outfitData['items']);
    logMessage("  穿搭包含 $itemCount 个衣物项");
    
    $outfitUpdated = false;
    
    // 处理每个衣物项
    foreach ($outfitData['items'] as &$item) {
        // 检查是否有clothing_data和image_url
        if (!isset($item['clothing_data']) || !isset($item['clothing_data']['image_url'])) {
            continue;
        }
        
        $imageUrl = $item['clothing_data']['image_url'];
        $stats['total_images']++;
        
        // 检查是否已经是CDN URL
        if (isCdnUrl($imageUrl)) {
            logMessage("  图片URL已经是CDN格式，跳过: $imageUrl");
            $stats['already_cdn']++;
            continue;
        }
        
        // 检查是否已经是OSS URL
        if (isOssUrl($imageUrl)) {
            // 如果是OSS URL，转换为CDN URL
            if (defined('ALIYUN_CDN_DOMAIN')) {
                $cdnUrl = str_replace(
                    'https://' . ALIYUN_OSS_BUCKET_DOMAIN, 
                    'https://' . ALIYUN_CDN_DOMAIN, 
                    $imageUrl
                );
                logMessage("  将OSS URL转换为CDN URL: $imageUrl => $cdnUrl");
                $item['clothing_data']['image_url'] = $cdnUrl;
                $outfitUpdated = true;
                $stats['already_oss']++;
            } else {
                logMessage("  警告: OSS URL未转换，因为未定义CDN域名: $imageUrl");
            }
            continue;
        }
        
        // 处理本地URL
        if (isLocalUrl($imageUrl)) {
            logMessage("  发现本地URL: $imageUrl");
            
            // 获取本地文件路径
            $localFilePath = getLocalFilePath($imageUrl);
            $isLocalFile = file_exists($localFilePath);
            
            if ($isLocalFile) {
                logMessage("  本地文件存在: $localFilePath");
                
                // 生成OSS Key
                $filename = basename($localFilePath);
                $ossKey = 'clothes/cloth_' . $outfitUserId . '_' . time() . '_' . rand(1000, 9999) . '_' . $filename;
                
                // 上传文件到OSS
                if (!$dryRun) {
                    try {
                        $uploadResult = $ossHelper->uploadFile($localFilePath, $ossKey);
                        
                        if ($uploadResult && $uploadResult['success']) {
                            // 获取OSS URL
                            $ossUrl = $uploadResult['url'];
                            
                            // 转换为CDN URL
                            $cdnUrl = $ossUrl;
                            if (defined('ALIYUN_CDN_DOMAIN')) {
                                $cdnUrl = str_replace(
                                    'https://' . ALIYUN_OSS_BUCKET_DOMAIN, 
                                    'https://' . ALIYUN_CDN_DOMAIN, 
                                    $ossUrl
                                );
                            }
                            
                            logMessage("  上传成功，CDN URL: $cdnUrl");
                            $item['clothing_data']['image_url'] = $cdnUrl;
                            $outfitUpdated = true;
                            $stats['uploaded_to_oss']++;
                        } else {
                            logMessage("  上传到OSS失败: " . ($uploadResult ? $uploadResult['error'] : '未知错误'));
                            $stats['failed']++;
                        }
                    } catch (Exception $e) {
                        logMessage("  上传到OSS时发生异常: " . $e->getMessage());
                        $stats['failed']++;
                    }
                } else {
                    // 模拟模式
                    logMessage("  [模拟] 将上传: $localFilePath => clothes/cloth_${outfitUserId}_...");
                    $stats['uploaded_to_oss']++;
                }
            } else {
                logMessage("  本地文件不存在: $localFilePath");
                
                // 尝试从URL下载并上传到OSS
                logMessage("  尝试从URL下载并上传到OSS: $imageUrl");
                
                if (!$dryRun) {
                    try {
                        // 生成OSS Key
                        $filename = basename($imageUrl);
                        if (empty($filename)) {
                            $filename = 'cloth_' . $outfitUserId . '_' . time() . '_' . rand(1000, 9999) . '.jpg';
                        }
                        $ossKey = 'clothes/' . $filename;
                        
                        // 从URL下载并上传到OSS
                        $downloadResult = $ossHelper->downloadUrlToOss($imageUrl, $ossKey);
                        
                        if ($downloadResult && $downloadResult['success']) {
                            // 获取OSS URL
                            $ossUrl = $downloadResult['url'];
                            
                            // 转换为CDN URL
                            $cdnUrl = $ossUrl;
                            if (defined('ALIYUN_CDN_DOMAIN')) {
                                $cdnUrl = str_replace(
                                    'https://' . ALIYUN_OSS_BUCKET_DOMAIN, 
                                    'https://' . ALIYUN_CDN_DOMAIN, 
                                    $ossUrl
                                );
                            }
                            
                            logMessage("  下载并上传成功，CDN URL: $cdnUrl");
                            $item['clothing_data']['image_url'] = $cdnUrl;
                            $outfitUpdated = true;
                            $stats['uploaded_to_oss']++;
                        } else {
                            logMessage("  下载并上传到OSS失败: " . ($downloadResult ? $downloadResult['error'] : '未知错误'));
                            $stats['failed']++;
                        }
                    } catch (Exception $e) {
                        logMessage("  下载并上传到OSS时发生异常: " . $e->getMessage());
                        $stats['failed']++;
                    }
                } else {
                    // 模拟模式
                    logMessage("  [模拟] 将从URL下载并上传: $imageUrl => clothes/...");
                    $stats['uploaded_to_oss']++;
                }
            }
        } else {
            logMessage("  未处理的URL类型: $imageUrl");
            $stats['failed']++;
        }
    }
    
    // 更新数据库
    if ($outfitUpdated && !$dryRun) {
        try {
            $updateSql = "UPDATE outfits SET outfit_data = :outfit_data WHERE id = :id";
            $updateStmt = $conn->prepare($updateSql);
            $newOutfitData = json_encode($outfitData);
            $updateStmt->bindParam(':outfit_data', $newOutfitData);
            $updateStmt->bindParam(':id', $outfitId);
            $updateResult = $updateStmt->execute();
            
            if ($updateResult) {
                logMessage("  更新穿搭 #$outfitId 成功");
                $stats['updated_outfits']++;
            } else {
                logMessage("  更新穿搭 #$outfitId 失败");
            }
        } catch (Exception $e) {
            logMessage("  更新穿搭 #$outfitId 时发生错误: " . $e->getMessage());
        }
    } elseif ($outfitUpdated && $dryRun) {
        logMessage("  [模拟] 将更新穿搭 #$outfitId");
        $stats['updated_outfits']++;
    }
}

// 输出统计信息
logMessage("\n迁移统计:");
logMessage("总穿搭数: " . $stats['total_outfits']);
logMessage("总图片数: " . $stats['total_images']);
logMessage("上传到OSS的图片数: " . $stats['uploaded_to_oss']);
logMessage("已经是OSS格式的图片数: " . $stats['already_oss']);
logMessage("已经是CDN格式的图片数: " . $stats['already_cdn']);
logMessage("处理失败的图片数: " . $stats['failed']);
logMessage("更新的穿搭记录数: " . $stats['updated_outfits']);
logMessage("处理" . ($dryRun ? "模拟" : "实际") . "完成"); 