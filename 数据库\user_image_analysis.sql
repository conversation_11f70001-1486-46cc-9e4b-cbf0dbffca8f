-- 创建用户形象分析表
CREATE TABLE `user_image_analysis` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `height` int(11) DEFAULT NULL COMMENT '身高(cm)',
  `weight` int(11) DEFAULT NULL COMMENT '体重(kg)',
  `bust` int(11) DEFAULT NULL COMMENT '胸围(cm)',
  `waist` int(11) DEFAULT NULL COMMENT '腰围(cm)',
  `hips` int(11) DEFAULT NULL COMMENT '臀围(cm)',
  `shoulder_width` int(11) DEFAULT NULL COMMENT '肩宽(cm)',
  `skin_tone` varchar(50) DEFAULT NULL COMMENT '肤色',
  `face_shape` varchar(50) DEFAULT NULL COMMENT '脸型',
  `body_shape` varchar(50) DEFAULT NULL COMMENT '体型',
  `gender` tinyint(1) DEFAULT NULL COMMENT '性别：1-男，2-女',
  `photo_urls` text DEFAULT NULL COMMENT '照片URL列表，JSON格式',
  `remarks` text DEFAULT NULL COMMENT '用户备注',
  `analysis_result` longtext DEFAULT NULL COMMENT '分析结果，JSON格式',
  `status` enum('pending','processing','completed','failed') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `payment_status` enum('unpaid','paid','refunded') NOT NULL DEFAULT 'unpaid' COMMENT '支付状态',
  `order_id` varchar(64) DEFAULT NULL COMMENT '订单ID',
  `amount` decimal(10,2) NOT NULL DEFAULT 9.90 COMMENT '支付金额',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `analysis_time` datetime DEFAULT NULL COMMENT '分析完成时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_user_image_analysis_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户形象分析表'; 