<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 日志函数
function logDebug($message, $data = null) {
    $log_file = __DIR__ . '/image_analysis_recommendation_debug.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] {$message}";
    
    if ($data !== null) {
        $log_message .= ': ' . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    
    file_put_contents($log_file, $log_message . PHP_EOL, FILE_APPEND);
}

// 确保image_analysis_based_recommendations表存在
function ensureImageAnalysisRecommendationTableExists($pdo) {
    try {
        $sql = "CREATE TABLE IF NOT EXISTS `image_analysis_based_recommendations` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `analysis_id` int(11) NOT NULL,
            `recommendation_data` text NOT NULL,
            `refresh` int(11) NOT NULL DEFAULT 0,
            `created_at` datetime NOT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_user_analysis` (`user_id`,`analysis_id`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        $pdo->exec($sql);
        logDebug("确保image_analysis_based_recommendations表存在");
        return true;
    } catch (PDOException $e) {
        logDebug("创建表失败", ['error' => $e->getMessage()]);
        return false;
    }
}

// 初始化数据库连接
$db = new Database();
$pdo = $db->getConnection();

// 确保表存在
ensureImageAnalysisRecommendationTableExists($pdo);

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 验证用户令牌
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '缺少授权头']);
    exit();
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证用户token
$userData = $auth->verifyToken($token);
if (!$userData) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '无效或已过期的令牌']);
    exit();
}

// 获取用户ID
$user_id = $userData['sub'];
logDebug("处理用户请求", ['user_id' => $user_id]);

// 根据请求方法获取参数
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $analysis_id = isset($_GET['analysis_id']) ? intval($_GET['analysis_id']) : 0;
    $refresh = isset($_GET['refresh']) ? intval($_GET['refresh']) : 0;
} else {
    $analysis_id = isset($_POST['analysis_id']) ? intval($_POST['analysis_id']) : 0;
    $refresh = isset($_POST['refresh']) ? intval($_POST['refresh']) : 0;
}

logDebug("请求参数", ['analysis_id' => $analysis_id, 'refresh' => $refresh]);

// 检查请求参数
if (!$analysis_id) {
    echo json_encode(['status' => 'error', 'message' => '缺少形象分析ID参数']);
    logDebug("错误: 缺少形象分析ID参数");
    exit;
}

try {
    // 检查形象分析是否属于该用户
    $stmt = $pdo->prepare("SELECT * FROM user_image_analysis WHERE id = :analysis_id AND user_id = :user_id");
    $stmt->execute([
        ':analysis_id' => $analysis_id,
        ':user_id' => $user_id
    ]);

    $analysis_data = $stmt->fetch(PDO::FETCH_ASSOC);
    if (!$analysis_data) {
        echo json_encode(['status' => 'error', 'message' => '未找到形象分析或该分析不属于您']);
        logDebug("错误: 未找到形象分析或该分析不属于用户", ['analysis_id' => $analysis_id, 'user_id' => $user_id]);
        exit;
    }

    logDebug("形象分析信息", $analysis_data);

    // 解析形象分析结果JSON
    if (!empty($analysis_data['analysis_result'])) {
        $analysis_data['analysis_result'] = json_decode($analysis_data['analysis_result'], true);
    } else {
        $analysis_data['analysis_result'] = [];
    }

    // 解析用户数据JSON
    if (!empty($analysis_data['user_data'])) {
        $analysis_data['user_data'] = json_decode($analysis_data['user_data'], true);
    } else {
        $analysis_data['user_data'] = [];
    }

    // 如果是刷新请求或者没有缓存的推荐结果，则生成新的推荐
    $should_generate_new = $refresh == 1;
    $cached_recommendation = null;

    // 获取最近的推荐结果，无论是否刷新都需要获取
    $stmt = $pdo->prepare("SELECT * FROM image_analysis_based_recommendations 
                           WHERE user_id = :user_id AND analysis_id = :analysis_id
                           ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([
        ':user_id' => $user_id,
        ':analysis_id' => $analysis_id
    ]);
    $cached_recommendation = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$should_generate_new && $cached_recommendation) {
        // 检查缓存是否在近期（比如24小时内）
        $cached_time = strtotime($cached_recommendation['created_at']);
        $current_time = time();
        $time_diff = $current_time - $cached_time;
        
        // 如果缓存时间超过24小时，则生成新的推荐
        if ($time_diff > 86400) { // 86400秒=24小时
            $should_generate_new = true;
            logDebug("缓存已过期，需要生成新推荐", ['缓存时间' => $cached_recommendation['created_at'], '过期时间差(秒)' => $time_diff]);
        } else {
            logDebug("使用缓存的推荐结果", ['缓存时间' => $cached_recommendation['created_at']]);
        }
    } else if (!$should_generate_new) {
        $should_generate_new = true;
        logDebug("没有找到缓存的推荐结果，需要生成新推荐");
    } else {
        logDebug("请求强制刷新推荐结果");
    }

    if ($should_generate_new) {
        // 获取用户所有衣物
        $stmt = $pdo->prepare("SELECT * FROM clothes WHERE user_id = :user_id");
        $stmt->execute([':user_id' => $user_id]);
        $all_clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        logDebug("获取的用户衣物数量", ['count' => count($all_clothes)]);

        // 处理衣物数据，将description解析为JSON
        foreach ($all_clothes as $key => $clothing) {
            if (!empty($clothing['description'])) {
                $all_clothes[$key]['description'] = json_decode($clothing['description'], true);
            } else {
                $all_clothes[$key]['description'] = [];
            }
        }

        // 获取上一次的推荐结果（用于"换一批"功能）
        $previous_outfit = null;
        if ($refresh == 1 && $cached_recommendation) {
            $previous_outfit = json_decode($cached_recommendation['recommendation_data'], true);
            logDebug("获取上一次的推荐结果用于换一批功能", ['has_previous' => !empty($previous_outfit)]);
        }

        // 准备发送到中转API的数据
        $api_data = [
            'analysis_data' => $analysis_data,
            'user_clothes' => $all_clothes,
            'refresh' => $refresh
        ];
        
        // 如果是换一批请求且有上一次的推荐结果，添加到请求中
        if ($refresh == 1 && !empty($previous_outfit)) {
            $api_data['previous_outfit'] = $previous_outfit;
        }

        // 添加随机因子，确保每次请求都不同
        $api_data['random_factor'] = time() . '_' . mt_rand(1000, 9999);

        logDebug("准备发送到API的数据结构", [
            'user_clothes_count' => count($all_clothes),
            'refresh' => $refresh,
            'has_previous_outfit' => isset($api_data['previous_outfit']),
            'random_factor' => $api_data['random_factor']
        ]);

        // 调用中转API获取推荐结果
        $ch = curl_init('https://www.furrywoo.com/gemini/grxxtuijian.php');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($api_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);

        logDebug("正在调用中转API");
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            logDebug("API调用失败", ['curl_error' => $error]);
            echo json_encode(['status' => 'error', 'message' => '调用推荐服务失败: ' . $error]);
            exit;
        }
        curl_close($ch);
        logDebug("中转API调用完成");

        // 解析响应
        $recommendation_result = json_decode($response, true);
        if (!$recommendation_result || isset($recommendation_result['error'])) {
            $error_message = isset($recommendation_result['error']) ? $recommendation_result['error'] : '解析推荐结果失败';
            logDebug("解析API响应失败", ['error' => $error_message, 'raw_response' => substr($response, 0, 1000)]);
            echo json_encode([
                'status' => 'error', 
                'message' => $error_message
            ]);
            exit;
        }
        logDebug("API返回的推荐结果结构", array_keys($recommendation_result));

        // 检查并修复推荐结果中的图片URL
        logDebug("开始修复图片URL");
        $recommendation_result = fixClothingImageUrls($recommendation_result, $pdo, $user_id);

        // 存储推荐结果到数据库
        $stmt = $pdo->prepare("INSERT INTO image_analysis_based_recommendations 
                              (user_id, analysis_id, recommendation_data, created_at) 
                              VALUES (:user_id, :analysis_id, :recommendation_data, NOW())");
        $stmt->execute([
            ':user_id' => $user_id,
            ':analysis_id' => $analysis_id,
            ':recommendation_data' => json_encode($recommendation_result)
        ]);
        logDebug("已保存推荐结果到数据库");

        // 返回推荐结果
        echo json_encode([
            'status' => 'success',
            'recommendation' => $recommendation_result
        ]);
        logDebug("已向客户端返回新的推荐结果");
    } else {
        // 获取缓存的推荐结果
        $cached_data = json_decode($cached_recommendation['recommendation_data'], true);
        logDebug("从缓存获取的推荐结果结构", array_keys($cached_data));
        
        // 检查缓存的推荐结果中的图片URL是否需要更新
        logDebug("开始检查缓存结果的图片URL");
        $cached_data = fixClothingImageUrls($cached_data, $pdo, $user_id);
        
        // 返回缓存的推荐结果
        echo json_encode([
            'status' => 'success',
            'recommendation' => $cached_data,
            'cached' => true
        ]);
        logDebug("已向客户端返回缓存的推荐结果");
    }
} catch (Exception $e) {
    logDebug("处理过程中发生异常", ['exception' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}

/**
 * 检查并修复推荐结果中的图片URL
 * @param array $recommendation_result 推荐结果
 * @param PDO $pdo 数据库连接
 * @param int $user_id 用户ID
 * @return array 修复后的推荐结果
 */
function fixClothingImageUrls($recommendation_result, $pdo, $user_id) {
    logDebug("进入fixClothingImageUrls函数", ['result_type' => gettype($recommendation_result)]);
    
    if (!is_array($recommendation_result)) {
        logDebug("推荐结果不是数组，无法处理", ['actual_type' => gettype($recommendation_result)]);
        return $recommendation_result;
    }
    
    // 记录推荐结果的原始结构
    logDebug("推荐结果原始结构", array_keys($recommendation_result));
    
    // 定义各类别的默认图片URL
    $default_image_urls = [
        'top' => 'https://images.alidog.cn/default/top_default.jpg',
        'bottom' => 'https://images.alidog.cn/default/bottom_default.jpg',
        'outerwear' => 'https://images.alidog.cn/default/outerwear_default.jpg',
        'shoes' => 'https://images.alidog.cn/default/shoes_default.jpg',
        'accessories' => 'https://images.alidog.cn/default/accessories_default.jpg',
        'bag' => 'https://images.alidog.cn/default/bag_default.jpg'
    ];
    
    // 收集需要查询的所有衣物ID
    $clothing_ids = [];
    $categories = ['top', 'bottom', 'outerwear', 'shoes', 'accessories', 'bag'];
    
    foreach ($categories as $category) {
        if (isset($recommendation_result[$category]) && 
            isset($recommendation_result[$category]['id']) && 
            is_numeric($recommendation_result[$category]['id'])) {
            $clothing_ids[] = $recommendation_result[$category]['id'];
            logDebug("找到衣物ID", ['category' => $category, 'id' => $recommendation_result[$category]['id']]);
        } else {
            if (isset($recommendation_result[$category])) {
                logDebug("无法从该类别获取有效ID", [
                    'category' => $category, 
                    'has_id_property' => isset($recommendation_result[$category]['id']),
                    'id_value' => isset($recommendation_result[$category]['id']) ? $recommendation_result[$category]['id'] : 'undefined',
                    'is_numeric' => isset($recommendation_result[$category]['id']) ? is_numeric($recommendation_result[$category]['id']) : false
                ]);
            } else {
                logDebug("推荐结果中缺少类别", ['missing_category' => $category]);
            }
        }
    }
    
    logDebug("收集到的衣物ID", ['ids' => $clothing_ids, 'count' => count($clothing_ids)]);
    
    // 创建ID到图片URL的映射
    $id_to_image_url = [];
    
    if (!empty($clothing_ids)) {
        // 准备IN查询的占位符
        $placeholders = implode(',', array_fill(0, count($clothing_ids), '?'));
        
        // 查询所有相关衣物的信息
        $query = "SELECT id, image_url, category FROM clothes WHERE id IN ($placeholders) AND user_id = ?";
        logDebug("准备执行数据库查询", ['query' => $query, 'param_count' => count($clothing_ids) + 1]);
        
        $stmt = $pdo->prepare($query);
        
        // 绑定所有参数
        $params = array_merge($clothing_ids, [$user_id]);
        $stmt->execute($params);
        
        // 获取所有查询结果
        $clothes_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        logDebug("数据库查询结果", ['found_items' => count($clothes_data), 'items' => $clothes_data]);
        
        // 创建ID到图片URL的映射
        foreach ($clothes_data as $clothing) {
            $id_to_image_url[$clothing['id']] = $clothing['image_url'];
            logDebug("映射衣物ID到图片URL", ['id' => $clothing['id'], 'image_url' => $clothing['image_url'], 'category' => $clothing['category']]);
        }
    }
    
    // 更新推荐结果中的image_url
    foreach ($categories as $category) {
        if (isset($recommendation_result[$category])) {
            // 确保每个类别都有image_url字段
            if (!isset($recommendation_result[$category]['image_url']) || 
                $recommendation_result[$category]['image_url'] === null || 
                $recommendation_result[$category]['image_url'] === 'null') {
                
                // 尝试从ID映射中获取图片URL
                if (isset($recommendation_result[$category]['id']) && 
                    isset($id_to_image_url[$recommendation_result[$category]['id']])) {
                    
                    $clothing_id = $recommendation_result[$category]['id'];
                    $recommendation_result[$category]['image_url'] = $id_to_image_url[$clothing_id];
                    logDebug("已更新图片URL", [
                        'category' => $category,
                        'id' => $clothing_id,
                        'new_image_url' => $id_to_image_url[$clothing_id]
                    ]);
                } else {
                    // 使用默认图片URL
                    if (isset($default_image_urls[$category])) {
                        $recommendation_result[$category]['image_url'] = $default_image_urls[$category];
                        logDebug("使用默认图片URL", [
                            'category' => $category,
                            'default_url' => $default_image_urls[$category]
                        ]);
                    } else {
                        // 如果没有该类别的默认图片，使用通用默认图片
                        $recommendation_result[$category]['image_url'] = 'https://images.alidog.cn/default/general_default.jpg';
                        logDebug("使用通用默认图片URL", [
                            'category' => $category,
                            'default_url' => 'https://images.alidog.cn/default/general_default.jpg'
                        ]);
                    }
                }
            } else {
                logDebug("保留现有图片URL", [
                    'category' => $category,
                    'existing_url' => $recommendation_result[$category]['image_url']
                ]);
            }
            
            // 记录当前类别的图片URL状态
            logDebug("类别图片URL最终状态", [
                'category' => $category,
                'id' => isset($recommendation_result[$category]['id']) ? $recommendation_result[$category]['id'] : 'undefined',
                'image_url' => $recommendation_result[$category]['image_url']
            ]);
        }
    }
    
    // 记录修复后的结构
    logDebug("修复后的推荐结果", ['top_keys' => array_keys($recommendation_result)]);
    
    return $recommendation_result;
}
?> 