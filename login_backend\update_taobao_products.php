<?php
/**
 * 淘宝客商品数据自动更新脚本
 * 
 * 【宝塔面板计划任务设置指南】
 * 1. 登录宝塔面板 -> 计划任务 -> 添加计划任务
 * 2. 任务类型选择：PHP脚本
 * 3. 任务名称自定义，例如：淘宝商品同步
 * 4. 执行周期：根据需要设置，建议设置为每天一次，例如：0 3 * * * （每天凌晨3点执行）
 * 5. 脚本内容：填写此脚本的绝对路径，例如：/www/wwwroot/your_domain/login_backend/update_taobao_products.php
 * 6. 脚本超时时间：建议设置为300秒或更多
 *
 * 本脚本会在执行时自动生成日志文件，可在logs目录下查看
 * 注意：API每日调用限制为100,000次/天，本脚本已进行优化控制，请勿随意增加页数参数
 */

// 设置执行时间限制（秒）
set_time_limit(300);

// 记录起始时间
$startTime = microtime(true);

// 设置错误报告
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// 确保日志目录存在
$logDir = __DIR__ . '/logs';
if (!file_exists($logDir)) {
    mkdir($logDir, 0755, true);
}

// 记录执行开始
log_message("开始执行淘宝商品数据同步...");

require_once 'config.php';
require_once 'db.php';
require_once TAOBAO_SDK_PATH . '/TopSdk.php';

// 加载淘宝API相关类
require_once TAOBAO_SDK_PATH . '/top/request/TbkDgMaterialOptionalUpgradeRequest.php';
require_once TAOBAO_SDK_PATH . '/top/domain/TbkItemInfo.php';
require_once TAOBAO_SDK_PATH . '/top/domain/MapData.php';

/**
 * 将消息记录到日志中
 * @param string $message 要记录的消息
 */
function log_message($message) {
    echo '[' . date('Y-m-d H:i:s') . '] ' . $message . PHP_EOL;
    
    // 确保日志目录存在
    $logDir = __DIR__ . '/logs';
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    // 设置日志文件名（使用下划线格式）
    $logFile = $logDir . '/taobao_update_' . date('Ymd') . '.log';
    
    // 添加UTF-8 BOM标记，确保宝塔面板能正确识别编码
    if (!file_exists($logFile)) {
        file_put_contents($logFile, "\xEF\xBB\xBF"); // UTF-8 BOM标记
    }
    
    // 写入日志，确保使用UTF-8编码
    file_put_contents(
        $logFile, 
        '[' . date('Y-m-d H:i:s') . '] ' . $message . PHP_EOL,
        FILE_APPEND
    );
}

/**
 * 确保字段值格式正确
 * @param mixed $value 字段值
 * @return mixed 标准化后的值
 */
function normalizeValue($value) {
    if (is_object($value)) {
        // 如果是对象，转换为字符串或获取第一个属性
        if (isset($value->{0})) {
            return $value->{0};
        }
        return strval($value);
    } elseif (is_array($value)) {
        // 如果是数组，递归处理每个元素
        return array_map('normalizeValue', $value);
    }
    return $value;
}

/**
 * 获取数据库连接
 * @return PDO 数据库连接对象
 */
function getDbConnection() {
    try {
        $db = new Database();
        return $db->getConnection();
    } catch (Exception $e) {
        log_message("数据库连接失败: " . $e->getMessage());
        exit(1);
    }
}

/**
 * 生成淘口令短链接
 * @param string $url 商品链接
 * @param string $title 商品标题
 * @param string $itemId 商品ID
 * @return array 包含淘口令和类型的数组 ['tpwd' => 淘口令, 'is_fake' => 是否为模拟淘口令]
 */
function generateTpwd($url, $title, $itemId) {
    if (empty($url)) {
        log_message("生成淘口令失败: URL为空");
        return [
            'tpwd' => "￥" . substr(md5($itemId . time()), 0, 8) . "￥",
            'is_fake' => true
        ];
    }
    
    try {
        // 构建请求参数
        $requestText = substr($title, 0, 30) . '...';  // 截取标题前30个字符
        
        log_message("尝试直接调用淘口令API");
        
        // 加载淘宝淘口令创建API
        if (!class_exists('TbkTpwdCreateRequest')) {
            require_once TAOBAO_SDK_PATH . '/top/request/TbkTpwdCreateRequest.php';
        }
        
        // 初始化淘宝客SDK
        $c = new TopClient();
        $c->appkey = TAOBAO_APPKEY;
        $c->secretKey = TAOBAO_APPSECRET;
        $c->gatewayUrl = "https://eco.taobao.com/router/rest";
        
        // 创建淘口令请求
        $req = new TbkTpwdCreateRequest;
        $req->setUrl($url);
        $req->setText($requestText);
        $req->setLogo("");
        
        // 执行API请求
        $tpwdResp = $c->execute($req);
        
        // 查找多种可能的返回路径
        $model = null;
        if (isset($tpwdResp->data->model)) {
            $model = $tpwdResp->data->model;
        } elseif (isset($tpwdResp->tbk_tpwd_create_response->data->model)) {
            $model = $tpwdResp->tbk_tpwd_create_response->data->model;
        } elseif (isset($tpwdResp->tbk_tpwd_create_response->data->password_simple)) {
            $model = $tpwdResp->tbk_tpwd_create_response->data->password_simple;
        }
        
        if ($model) {
            log_message("直接调用API成功生成淘口令");
            return [
                'tpwd' => $model,
                'is_fake' => false
            ];
        } else {
            // 检查是否有错误信息
            if (isset($tpwdResp->error_response)) {
                $errorMsg = '';
                if (isset($tpwdResp->error_response->code)) {
                    $errorMsg .= 'Code: ' . $tpwdResp->error_response->code . ' ';
                }
                if (isset($tpwdResp->error_response->msg)) {
                    $errorMsg .= 'Msg: ' . $tpwdResp->error_response->msg;
                }
                if (isset($tpwdResp->error_response->sub_msg)) {
                    $errorMsg .= ' - ' . $tpwdResp->error_response->sub_msg;
                }
                log_message("淘口令API返回错误: " . $errorMsg);
            } else {
                log_message("淘口令API返回格式异常: " . json_encode($tpwdResp));
            }
            
            // 生成一个模拟的淘口令作为备用
            log_message("使用模拟淘口令");
            return [
                'tpwd' => "￥" . substr(md5($url . $itemId), 0, 8) . "￥",
                'is_fake' => true
            ];
        }
    } catch (Exception $e) {
        log_message("生成淘口令异常: " . $e->getMessage());
        // 生成一个模拟的淘口令作为备用
        return [
            'tpwd' => "￥" . substr(md5($url . $itemId), 0, 8) . "￥",
            'is_fake' => true
        ];
    }
}

/**
 * 获取商品标签
 * @param string $title 商品标题
 * @param string $category 商品分类
 * @return array|null 商品标签信息，获取失败则返回null
 */
function getProductTags($title, $category) {
    try {
        // 中转API地址
        $apiUrl = "https://www.furrywoo.com/gemini/shangpinbiaoqian.php";
        
        // 构建请求数据
        $requestData = [
            'title' => $title,
            'category' => $category
        ];
        
        // 初始化cURL
        $ch = curl_init($apiUrl);
        
        // 设置cURL选项
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 设置10秒超时
        
        // 执行请求
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        
        // 关闭cURL
        curl_close($ch);
        
        // 检查是否有错误
        if ($curlError) {
            log_message("调用标签API出错: " . $curlError);
            return null;
        }
        
        // 检查HTTP状态码
        if ($httpCode !== 200) {
            log_message("标签API返回非200状态码: " . $httpCode);
            return null;
        }
        
        // 解析返回结果
        $responseData = json_decode($response, true);
        
        // 检查API返回是否成功
        if (isset($responseData['success']) && $responseData['success'] && isset($responseData['tags'])) {
            log_message("成功获取商品[{$title}]的标签信息");
            return $responseData['tags'];
        } else {
            log_message("获取标签失败: " . ($responseData['message'] ?? '未知错误'));
            return null;
        }
    } catch (Exception $e) {
        log_message("获取商品标签异常: " . $e->getMessage());
        return null;
    }
}

/**
 * 同步指定物料的商品
 * @param string $materialId 物料ID
 * @param string $category 分类标识
 * @param int $pageSize 每页商品数量
 * @param int $maxPages 最大页数
 * @param string $keyword 搜索关键词
 * @param int $apiDelayMs API请求间隔(毫秒)
 * @return array 同步统计信息
 */
function syncMaterialProducts($materialId, $category, $pageSize = 50, $maxPages = 10, $keyword = '', $apiDelayMs = 500) {
    $stats = [
        'total' => 0,
        'added' => 0,
        'updated' => 0,
        'deleted' => 0,
        'errors' => 0,
        'real_tpwd' => 0,
        'fake_tpwd' => 0
    ];
    
    log_message("开始同步物料 {$materialId} ({$category}) 的商品数据...");
    
    // 初始化淘宝客SDK
    $c = new TopClient();
    $c->appkey = TAOBAO_APPKEY;
    $c->secretKey = TAOBAO_APPSECRET;
    $c->gatewayUrl = "https://eco.taobao.com/router/rest";
    
    // 记录API配置信息
    log_message("API配置 - AppKey: " . substr(TAOBAO_APPKEY, 0, 4) . "****" . 
                ", PID: " . TAOBAO_PID . 
                ", 物料ID: " . $materialId);
    
    $conn = getDbConnection();
    
    // 记录当前的同步时间，用于后续识别过期商品
    $syncTime = date('Y-m-d H:i:s');
    
    // 记录当前物料下已有的商品ID
    $existingItemIds = [];
    $existingItemsStmt = $conn->prepare("SELECT item_id, tpwd, is_fake_tpwd FROM taobao_products WHERE material_id = :material_id");
    $existingItemsStmt->bindParam(':material_id', $materialId);
    $existingItemsStmt->execute();
    while ($row = $existingItemsStmt->fetch(PDO::FETCH_ASSOC)) {
        $existingItemIds[$row['item_id']] = [
            'tpwd' => $row['tpwd'],
            'is_fake_tpwd' => $row['is_fake_tpwd']
        ];
    }
    
    $newItemIds = [];
    
    try {
        for ($page = 1; $page <= $maxPages; $page++) {
            log_message("处理物料 {$materialId} 第{$page}页数据...");
            
            // 创建物料搜索请求
            $req = new TbkDgMaterialOptionalUpgradeRequest();
            $adzoneId = preg_replace('/^.+_(\d+)$/', '$1', TAOBAO_PID); // 从PID中提取广告位ID
            $req->setAdzoneId($adzoneId);
            log_message("从PID中提取的广告位ID: " . $adzoneId);
            
            $req->setPageSize($pageSize);
            $req->setPageNo($page);
            $req->setBizSceneId(1);
            $req->setMaterialId($materialId);
            
            // 添加搜索关键词参数，解决"参数q与cat不能都为空"的错误
            // 根据物料分类设置相应的搜索关键词
            if (empty($keyword)) {
                $searchKeyword = '';
                switch ($category) {
                    case '品牌女装':
                        $searchKeyword = '女装';
                        break;
                    case '品牌精选':
                        $searchKeyword = '精选';
                        break;
                    case '鞋包配饰':
                        $searchKeyword = '鞋包';
                        break;
                    case '美妆个护':
                        $searchKeyword = '美妆';
                        break;
                    case '内衣推荐':
                        $searchKeyword = '内衣';
                        break;
                    case '母婴用品':
                        $searchKeyword = '母婴';
                        break;
                    case '天猫爆款':
                        $searchKeyword = '爆款';
                        break;
                    case '直营补贴':
                        $searchKeyword = '补贴';
                        break;
                    case '天天特卖':
                        $searchKeyword = '特卖';
                        break;
                    default:
                        $searchKeyword = '热销'; // 默认搜索关键词
                }
            } else {
                $searchKeyword = $keyword;
            }
            
            $req->setQ($searchKeyword);
            log_message("设置搜索关键词: " . $searchKeyword);
            
            // 调用API获取商品数据
            log_message("发送API请求...");
            $resp = $c->execute($req);
            
            // 处理API返回的错误
            if (isset($resp->error_response)) {
                $errorMsg = json_encode($resp->error_response, JSON_UNESCAPED_UNICODE);
                log_message("API错误: " . $errorMsg);
                
                // 记录更详细的错误信息
                if (isset($resp->error_response->code)) {
                    log_message("错误代码: " . $resp->error_response->code);
                }
                if (isset($resp->error_response->msg)) {
                    log_message("错误消息: " . $resp->error_response->msg);
                }
                if (isset($resp->error_response->sub_code)) {
                    log_message("子错误代码: " . $resp->error_response->sub_code);
                }
                if (isset($resp->error_response->sub_msg)) {
                    log_message("子错误消息: " . $resp->error_response->sub_msg);
                }
                
                $stats['errors']++;
                break;
            }
            
            // 获取商品数据
            if (!isset($resp->result_list->map_data)) {
                log_message("未获取到商品数据或数据格式错误");
                log_message("API响应: " . json_encode($resp, JSON_UNESCAPED_UNICODE));
                break;
            }
            
            $results = $resp->result_list->map_data;
            $totalResults = $resp->total_results;
            $stats['total'] = $totalResults;
            
            log_message("获取到{$totalResults}条商品数据，当前页有" . count($results) . "条");
            
            // 处理每个商品
            foreach ($results as $index => $item) {
                try {
                    // 获取基本信息和价格信息
                    $basicInfo = $item->item_basic_info;
                    $priceInfo = $item->price_promotion_info;
                    $publishInfo = $item->publish_info;
                    
                    // 商品ID
                    $itemId = normalizeValue($item->item_id);
                    $newItemIds[] = $itemId;
                    
                    // 计算优惠券信息
                    $couponAmount = 0;
                    $couponInfo = '';
                    $couponStartTime = null;
                    $couponEndTime = null;
                    
                    // 处理优惠券和促销信息
                    if (isset($priceInfo->final_promotion_path_list->final_promotion_path_map_data)) {
                        $promotions = $priceInfo->final_promotion_path_list->final_promotion_path_map_data;
                        
                        // 转换为数组处理单个对象情况
                        if (!is_array($promotions)) {
                            $promotions = [$promotions];
                        }
                        
                        foreach ($promotions as $promo) {
                            if (strpos($promo->promotion_title, '券') !== false) {
                                $couponAmount += floatval($promo->promotion_fee);
                                $couponInfo = $promo->promotion_desc;
                                
                                if (isset($promo->promotion_start_time) && isset($promo->promotion_end_time)) {
                                    $couponStartTime = date('Y-m-d H:i:s', intval($promo->promotion_start_time/1000));
                                    $couponEndTime = date('Y-m-d H:i:s', intval($promo->promotion_end_time/1000));
                                }
                            }
                        }
                    }
                    
                    // 获取原价和最终价格
                    $originalPrice = floatval($priceInfo->reserve_price);
                    $zkFinalPrice = floatval($priceInfo->zk_final_price);
                    $finalPrice = floatval($priceInfo->final_promotion_price);
                    
                    // 计算佣金比例和金额
                    $commissionRate = 0;
                    $commissionAmount = 0;
                    
                    if (isset($publishInfo->income_info)) {
                        $commissionRate = floatval($publishInfo->income_info->commission_rate) / 100;
                        $commissionAmount = floatval($publishInfo->income_info->commission_amount);
                    } else if (isset($publishInfo->income_rate)) {
                        $commissionRate = floatval($publishInfo->income_rate) / 100;
                        $commissionAmount = $finalPrice * $commissionRate;
                    }
                    
                    // 商品图片处理
                    $imageUrl = normalizeValue($basicInfo->pict_url);
                    if (!empty($imageUrl) && strpos($imageUrl, 'http') !== 0) {
                        $imageUrl = 'https:' . $imageUrl;
                    }
                    
                    // 商品小图处理
                    $smallImages = [];
                    if (isset($basicInfo->small_images) && isset($basicInfo->small_images->string)) {
                        $smallImages = normalizeValue($basicInfo->small_images->string);
                        if (is_array($smallImages)) {
                            foreach ($smallImages as &$img) {
                                if (!empty($img) && strpos($img, 'http') !== 0) {
                                    $img = 'https:' . $img;
                                }
                            }
                        }
                    }
                    
                    // 推广链接处理
                    $itemUrl = isset($publishInfo->click_url) ? normalizeValue($publishInfo->click_url) : '';
                    if (!empty($itemUrl) && strpos($itemUrl, 'http') !== 0) {
                        $itemUrl = 'https:' . $itemUrl;
                    }
                    
                    $couponClickUrl = isset($publishInfo->coupon_share_url) ? normalizeValue($publishInfo->coupon_share_url) : '';
                    if (!empty($couponClickUrl) && strpos($couponClickUrl, 'http') !== 0) {
                        $couponClickUrl = 'https:' . $couponClickUrl;
                    }
                    
                    // 获取商品标题
                    $title = normalizeValue($basicInfo->title);
                    
                    // 生成淘口令短链接
                    $tpwdData = null;
                    $isFakeTpwd = 1; // 默认为模拟淘口令
                    
                    // 检查是否已存在真实淘口令，如果是模拟淘口令则尝试重新生成
                    if (isset($existingItemIds[$itemId]) && !empty($existingItemIds[$itemId]['tpwd']) && $existingItemIds[$itemId]['is_fake_tpwd'] == 0) {
                        // 使用已有的真实淘口令
                        $tpwdData = [
                            'tpwd' => $existingItemIds[$itemId]['tpwd'],
                            'is_fake' => false
                        ];
                        log_message("使用已有的真实淘口令: " . $tpwdData['tpwd']);
                    } else {
                        // 需要生成新淘口令或更新模拟淘口令
                        if (!empty($itemUrl)) {
                            // 优先使用优惠券链接
                            $urlForTpwd = !empty($couponClickUrl) ? $couponClickUrl : $itemUrl;
                            
                            // 添加随机延迟，避免API调用过于频繁
                            $delayMs = rand($apiDelayMs, $apiDelayMs * 2);
                            usleep($delayMs * 1000); // 将毫秒转换为微秒
                            
                            $tpwdData = generateTpwd($urlForTpwd, $title, $itemId);
                            log_message("成功为商品 {$itemId} 生成淘口令: " . $tpwdData['tpwd'] . ($tpwdData['is_fake'] ? " (模拟)" : " (真实)"));
                            
                            // 统计真实和模拟淘口令数量
                            if ($tpwdData['is_fake']) {
                                $stats['fake_tpwd']++;
                            } else {
                                $stats['real_tpwd']++;
                            }
                        } else {
                            // 没有URL，生成模拟淘口令
                            $tpwdData = [
                                'tpwd' => "￥" . substr(md5($itemId . time()), 0, 8) . "￥",
                                'is_fake' => true
                            ];
                            log_message("没有URL，生成模拟淘口令: " . $tpwdData['tpwd']);
                            $stats['fake_tpwd']++;
                        }
                    }
                    
                    // 确保is_fake_tpwd字段正确设置
                    $isFakeTpwd = $tpwdData['is_fake'] ? 1 : 0;
                    log_message("设置商品 {$itemId} 的is_fake_tpwd为: " . $isFakeTpwd);
                    
                    // 检查是否为真实淘口令，如果是则获取商品标签
                    $productTags = null;
                    if (!$isFakeTpwd) {
                        // 检查是否已有标签
                        if (isset($existingItemIds[$itemId])) {
                            $checkTagsStmt = $conn->prepare("SELECT tags FROM taobao_products WHERE item_id = :item_id AND tags IS NOT NULL AND tags != ''");
                            $checkTagsStmt->bindParam(':item_id', $itemId);
                            $checkTagsStmt->execute();
                            $existingTags = $checkTagsStmt->fetch(PDO::FETCH_ASSOC);
                            
                            // 如果没有标签，则获取
                            if (!$existingTags || empty($existingTags['tags'])) {
                                log_message("商品 {$itemId} 没有标签，尝试获取标签信息...");
                                $productTags = getProductTags($title, $category);
                            } else {
                                log_message("商品 {$itemId} 已有标签，跳过标签获取");
                            }
                        } else {
                            // 新商品，直接获取标签
                            log_message("新商品 {$itemId}，尝试获取标签信息...");
                            $productTags = getProductTags($title, $category);
                        }
                        
                        // 如果获取到标签，记录日志
                        if ($productTags) {
                            $tagsJson = json_encode($productTags, JSON_UNESCAPED_UNICODE);
                            log_message("成功获取商品 {$itemId} 的标签: " . substr($tagsJson, 0, 100) . (strlen($tagsJson) > 100 ? '...' : ''));
                        }
                    }
                    
                    // 将标签转为JSON字符串
                    $tagsJson = $productTags ? json_encode($productTags, JSON_UNESCAPED_UNICODE) : null;
                    
                    // 检查商品是否已存在
                    $checkStmt = $conn->prepare("SELECT id FROM taobao_products WHERE item_id = :item_id");
                    $checkStmt->bindParam(':item_id', $itemId);
                    $checkStmt->execute();
                    $exists = $checkStmt->fetch(PDO::FETCH_ASSOC);
                    
                    // 将小图列表转换为JSON
                    $smallImagesJson = !empty($smallImages) ? json_encode($smallImages) : null;
                    
                    if ($exists) {
                        // 更新现有商品
                        $updateStmt = $conn->prepare("
                            UPDATE taobao_products SET
                            title = :title,
                            image_url = :image_url,
                            small_images = :small_images,
                            original_price = :original_price,
                            zk_final_price = :zk_final_price,
                            final_price = :final_price,
                            coupon_amount = :coupon_amount,
                            coupon_info = :coupon_info,
                            coupon_start_time = :coupon_start_time,
                            coupon_end_time = :coupon_end_time,
                            shop_title = :shop_title,
                            seller_id = :seller_id,
                            volume = :volume,
                            item_url = :item_url,
                            coupon_click_url = :coupon_click_url,
                            tpwd = :tpwd,
                            is_fake_tpwd = :is_fake_tpwd,
                            commission_rate = :commission_rate,
                            commission_amount = :commission_amount,
                            category = :category,
                            tags = COALESCE(:tags, tags),
                            status = 1,
                            last_sync_time = :last_sync_time
                            WHERE item_id = :item_id
                        ");
                        
                        $stats['updated']++;
                    } else {
                        // 插入新商品
                        $updateStmt = $conn->prepare("
                            INSERT INTO taobao_products (
                                item_id, title, image_url, small_images, original_price,
                                zk_final_price, final_price, coupon_amount, coupon_info,
                                coupon_start_time, coupon_end_time, shop_title, seller_id,
                                volume, item_url, coupon_click_url, tpwd, is_fake_tpwd, commission_rate,
                                commission_amount, material_id, category, tags, status, last_sync_time
                            ) VALUES (
                                :item_id, :title, :image_url, :small_images, :original_price,
                                :zk_final_price, :final_price, :coupon_amount, :coupon_info,
                                :coupon_start_time, :coupon_end_time, :shop_title, :seller_id,
                                :volume, :item_url, :coupon_click_url, :tpwd, :is_fake_tpwd, :commission_rate,
                                :commission_amount, :material_id, :category, :tags, 1, :last_sync_time
                            )
                        ");
                        $updateStmt->bindParam(':material_id', $materialId);
                        
                        $stats['added']++;
                    }
                    
                    // 绑定共同参数
                    $shopTitle = normalizeValue($basicInfo->shop_title ?? '');
                    $sellerId = normalizeValue($basicInfo->seller_id ?? '');
                    $volume = intval(normalizeValue($basicInfo->volume ?? 0));
                    
                    $updateStmt->bindParam(':item_id', $itemId);
                    $updateStmt->bindParam(':title', $title);
                    $updateStmt->bindParam(':image_url', $imageUrl);
                    $updateStmt->bindParam(':small_images', $smallImagesJson);
                    $updateStmt->bindParam(':original_price', $originalPrice);
                    $updateStmt->bindParam(':zk_final_price', $zkFinalPrice);
                    $updateStmt->bindParam(':final_price', $finalPrice);
                    $updateStmt->bindParam(':coupon_amount', $couponAmount);
                    $updateStmt->bindParam(':coupon_info', $couponInfo);
                    $updateStmt->bindParam(':coupon_start_time', $couponStartTime);
                    $updateStmt->bindParam(':coupon_end_time', $couponEndTime);
                    $updateStmt->bindParam(':shop_title', $shopTitle);
                    $updateStmt->bindParam(':seller_id', $sellerId);
                    $updateStmt->bindParam(':volume', $volume);
                    $updateStmt->bindParam(':item_url', $itemUrl);
                    $updateStmt->bindParam(':coupon_click_url', $couponClickUrl);
                    $updateStmt->bindParam(':tpwd', $tpwdData['tpwd']);
                    $updateStmt->bindParam(':is_fake_tpwd', $isFakeTpwd, PDO::PARAM_INT);
                    $updateStmt->bindParam(':commission_rate', $commissionRate);
                    $updateStmt->bindParam(':commission_amount', $commissionAmount);
                    $updateStmt->bindParam(':category', $category);
                    $updateStmt->bindParam(':tags', $tagsJson);
                    $updateStmt->bindParam(':last_sync_time', $syncTime);
                    
                    // 执行更新并验证
                    if ($updateStmt->execute()) {
                        log_message("成功" . ($exists ? "更新" : "添加") . "商品 {$itemId}，淘口令类型: " . ($isFakeTpwd ? "模拟" : "真实"));
                    } else {
                        log_message("商品 {$itemId} " . ($exists ? "更新" : "添加") . "失败: " . json_encode($updateStmt->errorInfo()));
                        $stats['errors']++;
                    }
                } catch (Exception $itemEx) {
                    log_message("处理商品时出错: " . $itemEx->getMessage());
                    $stats['errors']++;
                }
                
                // 每处理10个商品，暂停一下，避免API调用过于频繁
                if (($index + 1) % 10 === 0) {
                    $pauseMs = rand(1000, 2000); // 1-2秒的随机暂停
                    log_message("已处理10个商品，暂停{$pauseMs}毫秒...");
                    usleep($pauseMs * 1000);
                }
            }
            
            // 页面间添加延迟，避免API调用过于频繁
            if ($page < $maxPages && count($results) >= $pageSize) {
                $pauseMs = rand(2000, 3000); // 2-3秒的随机暂停
                log_message("处理下一页前暂停{$pauseMs}毫秒...");
                usleep($pauseMs * 1000);
            }
            
            // 如果当前页商品数量小于页大小，说明已经没有更多数据
            if (count($results) < $pageSize) {
                break;
            }
        }
        
        // 处理已下架的商品（不在新获取的商品中的已有商品）
        foreach ($existingItemIds as $itemId => $value) {
            if (!in_array($itemId, $newItemIds)) {
                // 标记商品为下架状态
                $deleteStmt = $conn->prepare("UPDATE taobao_products SET status = 0 WHERE item_id = :item_id AND material_id = :material_id");
                $deleteStmt->bindParam(':item_id', $itemId);
                $deleteStmt->bindParam(':material_id', $materialId);
                $deleteStmt->execute();
                $stats['deleted']++;
            }
        }
        
    } catch (Exception $e) {
        log_message("同步物料商品时出错: " . $e->getMessage());
        log_message("错误堆栈: " . $e->getTraceAsString());
        $stats['errors']++;
    }
    
    log_message("物料 {$materialId} ({$category}) 同步完成: 总共{$stats['total']}条, 新增{$stats['added']}条, 更新{$stats['updated']}条, 下架{$stats['deleted']}条, 错误{$stats['errors']}条");
    log_message("淘口令统计: 真实淘口令{$stats['real_tpwd']}条, 模拟淘口令{$stats['fake_tpwd']}条");
    
    return $stats;
}

// 主执行逻辑
try {
    // 获取淘宝物料分类列表
    $materialCategories = getTaobaoMaterialCategories();
    
    // 确保taobao_products表存在
    $conn = getDbConnection();
    $tableExistsQuery = $conn->query("SHOW TABLES LIKE 'taobao_products'");
    $tableExists = $tableExistsQuery->rowCount() > 0;
    
    if (!$tableExists) {
        log_message("表taobao_products不存在，创建表...");
        
        // 从SQL文件读取创建表语句
        $sqlFile = __DIR__ . '/../taobao_products.sql';
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            $conn->exec($sql);
            log_message("已创建表taobao_products");
        } else {
            throw new Exception("SQL文件不存在: " . $sqlFile);
        }
    }
    
    // 检查是否需要添加is_fake_tpwd字段
    $columnExistsQuery = $conn->query("SHOW COLUMNS FROM taobao_products LIKE 'is_fake_tpwd'");
    $columnExists = $columnExistsQuery->rowCount() > 0;
    
    if (!$columnExists) {
        log_message("表taobao_products缺少is_fake_tpwd字段，添加该字段...");
        try {
            $conn->exec("ALTER TABLE `taobao_products` ADD COLUMN `is_fake_tpwd` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为模拟淘口令：1=是，0=否' AFTER `tpwd`");
            log_message("已添加is_fake_tpwd字段");
        } catch (Exception $alterEx) {
            log_message("添加is_fake_tpwd字段失败: " . $alterEx->getMessage());
        }
    }
    
    $totalStats = [
        'total' => 0,
        'added' => 0,
        'updated' => 0,
        'deleted' => 0,
        'errors' => 0,
        'real_tpwd' => 0,
        'fake_tpwd' => 0
    ];
    
    // 定义要同步的物料ID和分类
    $materialsToSync = [
        ['id' => '86623', 'name' => '品牌女装', 'keyword' => '女装'],
        ['id' => '86595', 'name' => '品牌精选', 'keyword' => '精选'],
        ['id' => '86620', 'name' => '鞋包配饰', 'keyword' => '鞋包'],
        ['id' => '86619', 'name' => '美妆个护', 'keyword' => '美妆']
    ];
    
    log_message("开始同步指定物料分类的商品数据...");
    
    // 获取命令行参数
    $options = getopt("m:p:d:", ["material:", "pages:", "delay:"]);
    
    // 检查是否指定了特定物料ID
    $specificMaterialId = isset($options['m']) ? $options['m'] : (isset($options['material']) ? $options['material'] : null);
    
    // 检查是否指定了页数
    $maxPages = isset($options['p']) ? intval($options['p']) : (isset($options['pages']) ? intval($options['pages']) : 2);
    if ($maxPages <= 0) $maxPages = 2;
    
    // 检查是否指定了API延迟
    $apiDelay = isset($options['d']) ? intval($options['d']) : (isset($options['delay']) ? intval($options['delay']) : 500);
    if ($apiDelay < 100) $apiDelay = 500;
    
    log_message("同步配置 - 最大页数: {$maxPages}, API延迟: {$apiDelay}毫秒" . 
               ($specificMaterialId ? ", 指定物料ID: {$specificMaterialId}" : ""));
    
    // 过滤要同步的物料
    if ($specificMaterialId) {
        $filteredMaterials = [];
        foreach ($materialsToSync as $material) {
            if ($material['id'] == $specificMaterialId) {
                $filteredMaterials[] = $material;
                break;
            }
        }
        
        if (empty($filteredMaterials)) {
            log_message("未找到指定的物料ID: {$specificMaterialId}");
            exit(1);
        }
        
        $materialsToSync = $filteredMaterials;
    }
    
    // 同步每个指定物料分类的商品
    foreach ($materialsToSync as $index => $material) {
        $materialId = $material['id'];
        $categoryName = $material['name'];
        $keyword = $material['keyword'];
        
        log_message("准备同步物料 {$materialId} ({$categoryName})，搜索关键词：{$keyword}");
        
        try {
            // 添加物料间的时间间隔
            if ($index > 0) {
                $pauseMs = rand(3000, 5000); // 3-5秒的随机暂停
                log_message("同步下一个物料前暂停{$pauseMs}毫秒...");
                usleep($pauseMs * 1000);
            }
            
            $categoryStats = syncMaterialProducts($materialId, $categoryName, 50, $maxPages, $keyword, $apiDelay);
            
            // 累加统计数据
            foreach ($categoryStats as $key => $value) {
                if (isset($totalStats[$key])) {
                    $totalStats[$key] += $value;
                }
            }
        } catch (Exception $e) {
            log_message("同步物料 {$materialId} ({$categoryName}) 时出错: " . $e->getMessage());
            $totalStats['errors']++;
            // 继续处理下一个物料
            continue;
        }
    }
    
    // 输出总统计信息
    $endTime = microtime(true);
    $executionTime = round($endTime - $startTime, 2);
    
    log_message("所有物料同步完成!");
    log_message("总计 - 商品数: {$totalStats['total']}, 新增: {$totalStats['added']}, 更新: {$totalStats['updated']}, 下架: {$totalStats['deleted']}, 错误: {$totalStats['errors']}");
    log_message("淘口令统计 - 真实淘口令: {$totalStats['real_tpwd']}, 模拟淘口令: {$totalStats['fake_tpwd']}");
    log_message("执行时间: {$executionTime} 秒");
    log_message("提示：本次API调用总数约为: " . ($totalStats['added'] + $totalStats['updated'] + 10) . "次");
    
    // 如果有模拟淘口令，提供更新建议
    if ($totalStats['fake_tpwd'] > 0) {
        log_message("注意：有{$totalStats['fake_tpwd']}个商品使用了模拟淘口令，建议稍后运行以下命令更新这些淘口令：");
        log_message("php " . basename(__FILE__) . " --delay=1000");
    }
    
} catch (Exception $e) {
    log_message("执行出错: " . $e->getMessage());
    log_message("堆栈跟踪: " . $e->getTraceAsString());
} 