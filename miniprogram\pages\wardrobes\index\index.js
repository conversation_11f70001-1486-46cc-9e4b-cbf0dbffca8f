const app = getApp();

Page({
  data: {
    wardrobes: [],
    page: 1,
    per_page: 10,
    hasMoreData: false,
    isLoading: false,
    showClothingName: false // 是否显示衣物名称
  },

  // 页面加载时
  onLoad: function() {
    // 从本地存储获取显示名称的设置
    const showClothingName = wx.getStorageSync('showClothingName') || false;
    this.setData({ showClothingName });
    
    this.loadWardrobes(true);
  },

  // 页面显示时
  onShow: function() {
    // 如果需要刷新数据
    if (app.globalData.needRefreshWardrobes) {
      this.setData({ page: 1 });
      this.loadWardrobes(true);
      app.globalData.needRefreshWardrobes = false;
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.setData({ page: 1 });
    this.loadWardrobes(true, () => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载衣橱列表
  loadWardrobes: function(refresh = false, callback) {
    // 检查登录状态
    if (!app.globalData.token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      if (typeof callback === 'function') callback();
      return;
    }

    this.setData({ isLoading: true });

    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_wardrobes.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      data: {
        page: this.data.page,
        per_page: this.data.per_page
      },
      success: (res) => {
        console.log('获取衣橱列表:', res.data);

        if (res.statusCode === 200 && res.data.success) {
          const newWardrobes = res.data.data || [];
          const pagination = res.data.pagination || {};
          
          // 更新数据
          this.setData({
            wardrobes: refresh ? newWardrobes : [...this.data.wardrobes, ...newWardrobes],
            hasMoreData: pagination.current_page < pagination.total_pages
          });
        } else {
          wx.showToast({
            title: res.data.message || '获取衣橱列表失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isLoading: false });
        if (typeof callback === 'function') callback();
      }
    });
  },

  // 加载更多
  loadMore: function() {
    if (this.data.hasMoreData && !this.data.isLoading) {
      this.setData({ page: this.data.page + 1 });
      this.loadWardrobes();
    }
  },

  // 跳转到衣橱详情
  goToWardrobeDetail: function(e) {
    const { id, name } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/wardrobes/detail/detail?id=${id}&name=${encodeURIComponent(name)}`
    });
  },

  // 跳转到添加衣橱
  goToAddWardrobe: function() {
    wx.navigateTo({
      url: '/pages/wardrobes/add/add'
    });
  },

  // 编辑衣橱
  editWardrobe: function(e) {
    const wardrobe = e.currentTarget.dataset.wardrobe;
    wx.navigateTo({
      url: `/pages/wardrobes/edit/edit?id=${wardrobe.id}&name=${encodeURIComponent(wardrobe.name)}&description=${encodeURIComponent(wardrobe.description || '')}&sort_order=${wardrobe.sort_order || 0}`
    });
  },

  // 删除衣橱
  deleteWardrobe: function(e) {
    const { id, isDefault } = e.currentTarget.dataset;
    
    console.log('衣橱ID:', id, '是否默认:', isDefault, '类型:', typeof isDefault);
    
    // 不允许删除默认衣橱
    if (isDefault === true || isDefault === 'true' || isDefault === 1 || isDefault === '1') {
      wx.showToast({
        title: '默认衣橱不能删除',
        icon: 'none'
      });
      return;
    }

    // 显示确认弹窗
    wx.showModal({
      title: '确认删除',
      content: '确定要删除该衣橱吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '正在删除...' });
          
          // 调用删除API
          wx.request({
            url: `${app.globalData.apiBaseUrl}/delete_wardrobe.php`,
            method: 'POST',
            header: {
              'content-type': 'application/json',
              'Authorization': app.globalData.token
            },
            data: {
              id: id
            },
            success: (res) => {
              wx.hideLoading();
              console.log('删除衣橱响应:', res.data);
              
              if (res.statusCode === 200 && res.data.success) {
                // 删除成功
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                
                // 设置全局刷新标记，确保首页衣橱选择器也会更新
                app.globalData.needRefreshWardrobes = true;
                
                // 刷新衣橱列表
                this.setData({ page: 1 });
                this.loadWardrobes(true);
              } else if (res.data.error === 'Wardrobe is not empty') {
                // 衣橱不为空
                wx.showModal({
                  title: '无法删除',
                  content: '该衣橱中还有衣物，请先清空衣橱再删除。',
                  showCancel: false
                });
              } else if (res.data.error === 'Cannot delete default wardrobe') {
                // 默认衣橱
                wx.showToast({
                  title: '默认衣橱不能删除',
                  icon: 'none'
                });
              } else {
                // 其他错误
                wx.showToast({
                  title: res.data.message || '删除失败',
                  icon: 'none'
                });
              }
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('请求失败:', err);
              wx.showToast({
                title: '网络请求失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 查看衣橱的衣物
   */
  viewClothes: function(e) {
    const id = e.currentTarget.dataset.id;
    const name = e.currentTarget.dataset.name;
    
    console.log('查看衣橱衣物:', id, name);
    
    wx.navigateTo({
      url: `/pages/wardrobes/clothes/clothes?id=${id}&name=${encodeURIComponent(name)}`
    });
  },

  // 切换显示名称设置
  toggleShowClothingName: function(e) {
    const showClothingName = e.detail.value === true;
    this.setData({ showClothingName });
    
    // 保存设置到全局和本地存储
    app.globalData.showClothingName = showClothingName;
    wx.setStorageSync('showClothingName', showClothingName);
    console.log('保存显示名称设置:', showClothingName);
    
    // 显示提示
    wx.showToast({
      title: showClothingName ? '已开启名称显示' : '已关闭名称显示',
      icon: 'none'
    });
  }
}); 