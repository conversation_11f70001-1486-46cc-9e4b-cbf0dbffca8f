-- 修复模拟淘口令标记
-- 此脚本用于修复数据库中的模拟淘口令标记，将符合特定模式的淘口令标记为模拟淘口令

-- 开始事务
START TRANSACTION;

-- 1. 修复以"￥"开头和结尾且长度小于15的淘口令
UPDATE taobao_products 
SET is_fake_tpwd = 1
WHERE tpwd LIKE '￥%￥' AND LENGTH(tpwd) < 15 AND is_fake_tpwd = 0;

-- 2. 修复以"¥"开头和结尾且长度小于15的淘口令（全角符号）
UPDATE taobao_products 
SET is_fake_tpwd = 1
WHERE tpwd LIKE '¥%¥' AND LENGTH(tpwd) < 15 AND is_fake_tpwd = 0;

-- 3. 修复包含"模拟"字样的淘口令
UPDATE taobao_products 
SET is_fake_tpwd = 1
WHERE tpwd LIKE '%模拟%' AND is_fake_tpwd = 0;

-- 4. 修复MD5格式的淘口令（通常是模拟生成的）
UPDATE taobao_products 
SET is_fake_tpwd = 1
WHERE tpwd REGEXP '^[￥¥][0-9a-f]{8,10}[￥¥]$' AND is_fake_tpwd = 0;

-- 5. 修复长度为10-12字符的淘口令（通常是模拟生成的）
UPDATE taobao_products 
SET is_fake_tpwd = 1
WHERE LENGTH(tpwd) BETWEEN 10 AND 12 AND is_fake_tpwd = 0;

-- 6. 修复不包含字母和数字混合的短淘口令（通常是模拟生成的）
UPDATE taobao_products 
SET is_fake_tpwd = 1
WHERE LENGTH(tpwd) < 15 
  AND (tpwd NOT REGEXP '[a-zA-Z]' OR tpwd NOT REGEXP '[0-9]')
  AND is_fake_tpwd = 0;

-- 如果MySQL版本不支持REGEXP，可以使用以下替代方案
-- UPDATE taobao_products 
-- SET is_fake_tpwd = 1
-- WHERE LENGTH(tpwd) < 15 AND is_fake_tpwd = 0;

-- 提交事务
COMMIT;

-- 输出修复结果
SELECT 
    SUM(CASE WHEN is_fake_tpwd = 1 THEN 1 ELSE 0 END) AS fake_tpwd_count,
    SUM(CASE WHEN is_fake_tpwd = 0 THEN 1 ELSE 0 END) AS real_tpwd_count,
    COUNT(*) AS total_count
FROM taobao_products
WHERE tpwd IS NOT NULL AND tpwd != '';

-- 执行方法：
-- 1. 登录MySQL数据库
-- 2. 选择对应的数据库：USE your_database_name;
-- 3. 执行此脚本：SOURCE /path/to/fix_fake_tpwd.sql;
-- 或者通过phpMyAdmin等工具导入执行 