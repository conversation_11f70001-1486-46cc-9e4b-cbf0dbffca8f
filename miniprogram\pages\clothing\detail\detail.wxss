/* 全局容器 */
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh; /* 恢复固定高度 */
  background-color: #ffffff;
  box-sizing: border-box;
  position: relative;
}

/* 滚动区域 */
.scroll-container {
  flex: 1;
  height: 100%;
  padding-top: 10px; /* 移除为固定导航栏留出的空间 */
  padding-bottom: 80px; /* 为底部按钮留出空间 */
  box-sizing: border-box;
}

/* 底部占位 */
.bottom-space {
  height: 20px;
  width: 100%;
}

/* 衣物图片区域 */
.clothing-image {
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  background-color: #ffffff;
  margin-bottom: 2px;
  margin-top: 20px;
}

.image-container {
  width: 100%;
  height: 560rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  padding: 8px;
  box-sizing: border-box;
}

.clothing-img {
  width: 95%;
  height: 95%;
  object-fit: contain;
}

.placeholder-img {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 12px;
}

.loading-text {
  color: #999;
  font-size: 14px;
}

/* 衣物信息区域 */
.clothing-info {
  width: 100%;
  padding: 20px 16px;
  box-sizing: border-box;
  background-color: #ffffff;
}

.info-title {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 16px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

/* 标签样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
  gap: 10px;
}

.tag {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 13px;
  background-color: #f5f5f5;
  color: #333;
  box-shadow: none;
  
  /* 随机颜色 - 更柔和的白底样式 */
  --tag-bg: var(--random-color, hsla(var(--hue, 200), 70%, 95%, 1));
  --hue: calc(var(--index, 0) * 60);
}

/* 基本信息样式 */
.info-section {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 4px 0;
}

.info-row {
  display: flex;
  padding: 14px 0;
  border-bottom: 1px solid #f5f5f5;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  width: 120rpx;
  color: #666;
  font-size: 14px;
}

.info-value {
  flex: 1;
  color: #000000;
  font-size: 14px;
  font-weight: 500;
}

/* 操作按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 16px;
  background-color: #ffffff;
  box-shadow: 0 -1px 0 rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  display: flex;
  justify-content: space-between; /* Restore original value */
  gap: 12px;
}

/* 商家模式下的操作按钮容器 */
.merchant-action-buttons {
  justify-content: center;
}

.edit-btn {
  flex: 0.4;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000000;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  border-radius: 10px;
  transition: all 0.2s;
}

.edit-btn-hover {
  background-color: #333333;
  transform: translateY(1px);
}

.delete-btn {
  flex: 0.25;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
  color: #999999;
  font-size: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  transition: all 0.2s;
}

.delete-btn-hover {
  background-color: #f5f5f5;
  color: #e74c3c;
  transform: translateY(1px);
}

/* 删除图标 */
.delete-icon {
  font-size: 17px;
  margin-right: 4px;
  color: #999999;
}

.delete-btn:hover .delete-icon,
.delete-btn-hover .delete-icon {
  color: #e74c3c;
}

/* 大图弹框样式 */
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.image-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 90%;
  height: 80vh;
  box-sizing: border-box;
  overflow: hidden;
  background-color: #F5F5F5;
  border-radius: 12px;
  padding: 12px;
}

.large-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  font-weight: 300;
  z-index: 1010;
}

/* 保存按钮样式 */
.save-btn {
  margin-top: 20px;
  width: 120px;
  height: 40px;
  border-radius: 20px;
  background-color: #ffffff;
  color: #000000;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.2s;
  z-index: 1010;
} 

/* 试穿按钮 */
.try-on-btn {
  flex: 0.4;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000000;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  border-radius: 10px;
  transition: all 0.2s;
}

.try-on-btn-hover {
  background-color: #333333;
  transform: translateY(1px);
} 

/* 关联穿搭模块 */
.related-outfits {
  width: 100%;
  padding: 20px 16px;
  box-sizing: border-box;
  background-color: #ffffff;
  margin-top: 10px;
}

/* 关联穿搭标题样式 */
.related-outfits .info-title {
  display: flex;
  flex-direction: row;
  align-items: center;
}

/* 穿搭数量标记 */
.outfit-count {
  margin-left: 8rpx;
  font-size: 14px;
  color: #999;
  font-weight: 400;
}

/* 关联穿搭滚动容器 */
.outfits-scroll {
  width: 100%;
  white-space: nowrap;
  margin-top: 10px;
}

.outfits-scroll-inner {
  display: inline-flex;
  padding: 5px 0;
}

/* 关联穿搭项 */
.related-outfit-item {
  width: 160rpx;
  margin-right: 20rpx;
  display: inline-block;
  vertical-align: top;
  transition: transform 0.2s;
}

.related-outfit-item:active {
  transform: scale(0.95);
}

/* 穿搭预览 */
.related-outfit-item .outfit-preview {
  width: 160rpx;
  height: 160rpx;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-bottom: 8rpx;
  position: relative;
}

.related-outfit-item .outfit-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.related-outfit-item .outfit-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  position: relative;
  overflow: hidden;
}

.related-outfit-item .outfit-icon {
  font-size: 40rpx;
  color: #aaaaaa;
}

.related-outfit-item .outfit-name {
  font-size: 24rpx;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}

/* outfit-view-mini样式，用于在预览中显示全部衣物 */
.outfit-view-mini {
  width: 80px;
  height: 80px;
  position: relative;
  background-color: transparent;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  transform: translateX(-5px);
}

/* 迷你版穿搭衣物项 */
.outfit-item-mini {
  position: absolute;
  overflow: hidden;
  transform-origin: center center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 迷你版衣物图片 */
.item-image-mini {
  width: 95%;
  height: 95%;
  object-fit: contain;
  display: block;
  margin: auto;
}

/* 衣物数量标签 */
.placeholder-count {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 10px;
  padding: 1px 6px;
  border-radius: 10px;
  z-index: 100;
}

.outfit-no-items {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 无关联穿搭 */
.no-related-outfits {
  width: 100%;
  padding: 30rpx 0;
  text-align: center;
  color: #999;
  font-size: 26rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  margin-top: 10rpx;
}

/* 加载中 */
.related-loading {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-spinner-small {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #000000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 15rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #999999;
}

/* 权限相关样式 */
.permission-loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

.permission-info {
  text-align: center;
  padding: 20rpx 40rpx;
  background-color: #f8f8f8;
  margin: 20rpx;
  border-radius: 12rpx;
}

.permission-text {
  font-size: 24rpx;
  color: #666;
}