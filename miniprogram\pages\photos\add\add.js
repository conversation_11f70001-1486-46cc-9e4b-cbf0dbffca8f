const app = getApp();

Page({
  data: {
    isUploading: false
  },
  
  onLoad: function () {
    wx.setNavigationBarTitle({
      title: '添加照片'
    });
  },
  
  // 拍摄新照片
  takePhoto: function () {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['camera'],
      camera: 'back',
      success: (res) => {
        const tempFile = res.tempFiles[0];
        this.uploadPhoto(tempFile.tempFilePath);
      }
    });
  },
  
  // 从相册选择
  chooseFromAlbum: function () {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album'],
      success: (res) => {
        const tempFile = res.tempFiles[0];
        this.uploadPhoto(tempFile.tempFilePath);
      }
    });
  },
  
  // 上传照片
  uploadPhoto: function (filePath) {
    const token = app.globalData.token;
    if (!token) {
      console.error('未登录');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 显示照片类型选择
    wx.showActionSheet({
      itemList: ['全身照', '半身照', '其他'],
      success: (res) => {
        let photoType = 'other';
        switch (res.tapIndex) {
          case 0:
            photoType = 'full';
            break;
          case 1:
            photoType = 'half';
            break;
        }
        
        this.startUpload(filePath, photoType);
      }
    });
  },
  
  // 开始上传
  startUpload: function (filePath, photoType) {
    this.setData({
      isUploading: true
    });
    
    wx.showLoading({
      title: '上传中...',
      mask: true
    });
    
    const token = app.globalData.token;
    
    wx.uploadFile({
      url: `${app.globalData.apiBaseUrl}/upload_photo.php`,
      filePath: filePath,
      name: 'image',
      header: {
        'Authorization': token
      },
      formData: {
        type: photoType
      },
      success: (res) => {
        console.log('上传照片响应:', res);
        
        let response;
        try {
          // 尝试检测和修复JSON格式问题
          let responseText = res.data;
          
          // 如果字符串中包含多个JSON对象（如结尾有}{ 这种格式），则尝试提取最后一个JSON对象
          const lastJsonStartIndex = responseText.lastIndexOf('{"error":');
          if (lastJsonStartIndex > 0) {
            console.log('检测到可能有多个JSON对象，提取最后一个');
            responseText = responseText.substring(lastJsonStartIndex);
          }
          
          response = JSON.parse(responseText);
          console.log('解析后的响应:', response);
        } catch (e) {
          console.error('解析上传响应失败:', e, '原始响应:', res.data);
          wx.showToast({
            title: '上传成功，但解析响应失败',
            icon: 'none'
          });
          
          // 即使解析失败，也认为上传成功，延迟返回上一页
          setTimeout(() => {
            this.setData({ isUploading: false });
            wx.navigateBack();
          }, 1500);
          return;
        }
        
        if (res.statusCode === 200 && !response.error) {
          wx.showToast({
            title: '上传成功',
            icon: 'success'
          });
          
          // 延迟返回上一页
          setTimeout(() => {
            this.setData({ isUploading: false });
            wx.navigateBack();
          }, 1500);
        } else {
          this.setData({ isUploading: false });
          wx.showToast({
            title: response.msg || '上传失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('上传请求失败:', err);
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({
          isUploading: false
        });
        wx.hideLoading();
      }
    });
  },
  
  // 取消上传
  cancel: function () {
    wx.navigateBack();
  }
})
