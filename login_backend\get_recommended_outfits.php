<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 验证小程序用户权限
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证用户token
$userData = $auth->verifyToken($token);
if (!$userData) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取数据库连接
$db = new Database();
$conn = $db->getConnection();

// 获取查询参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 10;
$category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : null;
$sort_by = isset($_GET['sort_by']) ? strtolower($_GET['sort_by']) : 'default';

// 验证分页参数
if ($page < 1) $page = 1;
if ($per_page < 1) $per_page = 10;
if ($per_page > 100) $per_page = 100;

// 验证排序参数
$allowed_sort_options = ['default', 'popular', 'newest'];
if (!in_array($sort_by, $allowed_sort_options)) {
    $sort_by = 'default';
}

// 计算偏移量
$offset = ($page - 1) * $per_page;

// 构建SQL查询 - 先获取记录总数
$countQuery = "SELECT COUNT(*) as total FROM recommended_outfits WHERE status = 1";
$paramsCount = [];

// 添加分类过滤条件
if ($category_id !== null) {
    $countQuery .= " AND category_id = ?";
    $paramsCount[] = $category_id;
}

// 构建SQL查询 - 查询具体数据
$query = "SELECT ro.*, roc.name as category_name, IFNULL(ros.view_count, 0) as view_count
          FROM recommended_outfits ro 
          LEFT JOIN recommended_outfit_categories roc ON ro.category_id = roc.id 
          LEFT JOIN recommended_outfit_stats ros ON ro.id = ros.outfit_id ";

$query .= "WHERE ro.status = 1";
$params = [];

// 添加分类过滤条件
if ($category_id !== null) {
    $query .= " AND ro.category_id = ?";
    $params[] = $category_id;
}

// 添加排序
switch ($sort_by) {
    case 'popular':
        $query .= " ORDER BY view_count DESC, ro.sort_order ASC, ro.created_at DESC";
        break;
    case 'newest':
        $query .= " ORDER BY ro.created_at DESC, ro.sort_order ASC";
        break;
    default:
        $query .= " ORDER BY ro.sort_order ASC, ro.created_at DESC";
        break;
}

// 添加分页
$query .= " LIMIT ? OFFSET ?";
$params[] = $per_page;
$params[] = $offset;

try {
    // 执行总数查询
    $countStmt = $conn->prepare($countQuery);
    if (!empty($paramsCount)) {
        $i = 1;
        foreach ($paramsCount as $param) {
            $countStmt->bindValue($i++, $param);
        }
    }
    $countStmt->execute();
    $totalRecords = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 执行数据查询
    $stmt = $conn->prepare($query);
    if (!empty($params)) {
        $i = 1;
        foreach ($params as $index => $param) {
            // 为LIMIT和OFFSET参数指定整数类型
            if ($index >= count($params) - 2) {
                $stmt->bindValue($i++, $param, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($i++, $param);
            }
        }
    }
    $stmt->execute();
    $outfits = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 对每个穿搭对象进行处理，获取第一个商品作为预览
    foreach ($outfits as &$outfit) {
        // 获取每个穿搭的第一个商品信息
        $itemsQuery = "SELECT * FROM recommended_outfit_items 
                      WHERE outfit_id = ? 
                      ORDER BY sort_order ASC, id ASC 
                      LIMIT 1";
        $itemsStmt = $conn->prepare($itemsQuery);
        $itemsStmt->bindValue(1, $outfit['id']);
        $itemsStmt->execute();
        $firstItem = $itemsStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($firstItem) {
            $outfit['first_item'] = $firstItem;
        } else {
            $outfit['first_item'] = null;
        }
        
        // 获取商品数量
        $itemsCountQuery = "SELECT COUNT(*) as item_count FROM recommended_outfit_items WHERE outfit_id = ?";
        $itemsCountStmt = $conn->prepare($itemsCountQuery);
        $itemsCountStmt->bindValue(1, $outfit['id']);
        $itemsCountStmt->execute();
        $itemsCount = $itemsCountStmt->fetch(PDO::FETCH_ASSOC);
        $outfit['item_count'] = $itemsCount['item_count'];
        
        // 增加查看次数（异步操作，不影响当前响应）
        incrementViewCount($conn, $outfit['id']);
    }
    
    // 计算总页数
    $totalPages = ceil($totalRecords / $per_page);
    
    // 构建分页信息
    $pagination = [
        'total' => $totalRecords,
        'per_page' => $per_page,
        'current_page' => $page,
        'total_pages' => $totalPages,
        'has_more' => $page < $totalPages
    ];
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'data' => $outfits,
        'pagination' => $pagination
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '数据库错误: ' . $e->getMessage()]);
}

// 异步增加查看次数的辅助函数
function incrementViewCount($conn, $outfitId) {
    try {
        // 检查是否存在统计记录
        $checkQuery = "SELECT id FROM recommended_outfit_stats WHERE outfit_id = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bindValue(1, $outfitId, PDO::PARAM_INT);  // 明确指定为整数类型
        $checkStmt->execute();
        $statsRecord = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($statsRecord) {
            // 更新现有记录
            $updateQuery = "UPDATE recommended_outfit_stats 
                           SET view_count = view_count + 1
                           WHERE outfit_id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bindValue(1, $outfitId, PDO::PARAM_INT);  // 明确指定为整数类型
            $updateStmt->execute();
        } else {
            // 创建新记录
            $insertQuery = "INSERT INTO recommended_outfit_stats 
                           (outfit_id, view_count, copy_link_count, created_at) 
                           VALUES (?, 1, 0, NOW())";
            $insertStmt = $conn->prepare($insertQuery);
            $insertStmt->bindValue(1, $outfitId, PDO::PARAM_INT);  // 明确指定为整数类型
            $insertStmt->execute();
        }
    } catch (Exception $e) {
        // 只记录错误，不影响主要响应
        error_log('增加查看次数失败: ' . $e->getMessage());
    }
} 