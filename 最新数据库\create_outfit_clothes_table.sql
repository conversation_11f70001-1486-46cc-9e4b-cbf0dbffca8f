-- 创建outfit_clothes表（穿搭和衣物关联表）
-- 这个表用于存储穿搭中包含的衣物关系

CREATE TABLE `outfit_clothes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `outfit_id` int(11) NOT NULL COMMENT '穿搭ID',
  `clothes_id` int(11) NOT NULL COMMENT '衣物ID',
  `position_x` decimal(10,2) DEFAULT NULL COMMENT 'X坐标位置',
  `position_y` decimal(10,2) DEFAULT NULL COMMENT 'Y坐标位置',
  `scale` decimal(5,2) DEFAULT '1.00' COMMENT '缩放比例',
  `rotation` decimal(5,2) DEFAULT '0.00' COMMENT '旋转角度',
  `z_index` int(11) DEFAULT '0' COMMENT '层级顺序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_outfit_clothes` (`outfit_id`, `clothes_id`),
  KEY `idx_outfit_id` (`outfit_id`),
  KEY `idx_clothes_id` (`clothes_id`),
  KEY `idx_z_index` (`outfit_id`, `z_index`),
  CONSTRAINT `fk_outfit_clothes_outfit` FOREIGN KEY (`outfit_id`) REFERENCES `outfits` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_outfit_clothes_clothes` FOREIGN KEY (`clothes_id`) REFERENCES `clothes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='穿搭和衣物关联表';

-- 创建索引以优化查询性能
CREATE INDEX `idx_outfit_clothes_outfit_order` ON `outfit_clothes` (`outfit_id`, `z_index`, `id`);

-- 如果需要从现有的outfit_data JSON中迁移数据，可以使用以下脚本
-- 注意：这个脚本需要根据实际的JSON结构进行调整

/*
-- 数据迁移脚本示例（需要根据实际JSON结构调整）
INSERT INTO outfit_clothes (outfit_id, clothes_id, position_x, position_y, scale, rotation, z_index)
SELECT 
    o.id as outfit_id,
    JSON_UNQUOTE(JSON_EXTRACT(clothes_item.value, '$.id')) as clothes_id,
    JSON_UNQUOTE(JSON_EXTRACT(clothes_item.value, '$.x')) as position_x,
    JSON_UNQUOTE(JSON_EXTRACT(clothes_item.value, '$.y')) as position_y,
    JSON_UNQUOTE(JSON_EXTRACT(clothes_item.value, '$.scale')) as scale,
    JSON_UNQUOTE(JSON_EXTRACT(clothes_item.value, '$.rotation')) as rotation,
    JSON_UNQUOTE(JSON_EXTRACT(clothes_item.value, '$.zIndex')) as z_index
FROM outfits o
CROSS JOIN JSON_TABLE(
    o.outfit_data,
    '$.clothes[*]' COLUMNS (
        value JSON PATH '$'
    )
) as clothes_item
WHERE JSON_VALID(o.outfit_data) = 1
  AND JSON_EXTRACT(o.outfit_data, '$.clothes') IS NOT NULL;
*/
