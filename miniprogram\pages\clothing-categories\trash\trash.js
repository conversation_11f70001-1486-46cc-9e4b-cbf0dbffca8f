const app = getApp();

Page({
  data: {
    deletedCategories: [],
    isLoading: false
  },

  onLoad: function () {
    this.loadDeletedCategories();
  },

  onShow: function () {
    this.loadDeletedCategories();
  },

  onPullDownRefresh: function () {
    this.loadDeletedCategories();
  },

  // 加载已删除的分类
  loadDeletedCategories: function() {
    if (this.data.isLoading) {
      return;
    }

    this.setData({
      isLoading: true
    });

    const token = app.globalData.token;
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      this.setData({
        isLoading: false
      });
      return;
    }

    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_deleted_categories.php`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: (res) => {
        console.log('获取已删除分类列表响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          // 添加调试代码，检查每个分类的is_system字段
          const categories = res.data.data || [];
          console.log('分类列表详情:', categories);
          
          // 确保每个分类的is_system字段是布尔值
          const processedCategories = categories.map(cat => {
            // 对于以custom_开头的分类代码，强制设置is_system为false
            if (cat.code && cat.code.startsWith('custom_')) {
              cat.is_system = false;
            }
            // 对于系统预设分类代码，强制设置is_system为true
            else if (cat.code && ['tops', 'pants', 'skirts', 'coats', 'shoes', 'bags', 'accessories'].includes(cat.code)) {
              cat.is_system = true;
            }
            return cat;
          });
          
          this.setData({
            deletedCategories: processedCategories
          });
        } else {
          wx.showToast({
            title: res.data.msg || '获取已删除分类失败',
            icon: 'none'
          });
        }
        
        this.setData({
          isLoading: false
        });
        
        wx.stopPullDownRefresh();
      },
      fail: (err) => {
        console.error('获取已删除分类列表失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        
        this.setData({
          isLoading: false
        });
        
        wx.stopPullDownRefresh();
      }
    });
  },

  // 恢复分类
  restoreCategory: function(e) {
    const categoryCode = e.currentTarget.dataset.code;
    const categoryName = e.currentTarget.dataset.name;
    
    wx.showModal({
      title: '确认恢复',
      content: `确定要恢复分类"${categoryName}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performRestore(categoryCode);
        }
      }
    });
  },

  // 执行恢复操作
  performRestore: function(categoryCode) {
    const token = app.globalData.token;
    
    wx.showLoading({
      title: '恢复中...'
    });

    wx.request({
      url: `${app.globalData.apiBaseUrl}/restore_category.php`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      data: {
        code: categoryCode
      },
      success: (res) => {
        wx.hideLoading();
        
        console.log('恢复分类响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          wx.showToast({
            title: '恢复成功',
            icon: 'success'
          });
          
          // 设置全局刷新标志
          app.globalData.needRefreshCategories = true;
          
          // 刷新列表
          this.loadDeletedCategories();
        } else {
          wx.showToast({
            title: res.data.msg || '恢复失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('恢复分类失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  }
}); 