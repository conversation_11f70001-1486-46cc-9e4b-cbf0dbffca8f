<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天气推荐穿搭中转API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        textarea {
            width: 100%;
            height: 200px;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            overflow-x: auto;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .response-container {
            margin-top: 20px;
        }
        .loading {
            text-align: center;
            margin: 20px 0;
            display: none;
        }
        .loading:after {
            content: " .";
            animation: dots 1s steps(5, end) infinite;
        }
        @keyframes dots {
            0%, 20% { content: " ."; }
            40% { content: " .."; }
            60% { content: " ..."; }
            80%, 100% { content: " ...."; }
        }
    </style>
</head>
<body>
    <h1>天气推荐穿搭中转API测试</h1>
    
    <div class="form-group">
        <label for="request-data">请求参数 (JSON格式)：</label>
        <textarea id="request-data">{
  "user_clothes": [
    {
      "id": 1,
      "name": "白色T恤",
      "category": "tops",
      "image_url": "https://example.com/white-tshirt.jpg",
      "description": {"颜色": "白色", "材质": "棉"}
    },
    {
      "id": 2,
      "name": "黑色长裤",
      "category": "pants",
      "image_url": "https://example.com/black-pants.jpg",
      "description": {"颜色": "黑色", "材质": "涤纶"}
    },
    {
      "id": 3,
      "name": "蓝色外套",
      "category": "coats",
      "image_url": "https://example.com/blue-coat.jpg",
      "description": {"颜色": "蓝色", "材质": "棉"}
    },
    {
      "id": 4,
      "name": "运动鞋",
      "category": "shoes",
      "image_url": "https://example.com/sneakers.jpg",
      "description": {"颜色": "白色", "材质": "帆布"}
    }
  ],
  "weather_data": {
    "location": {
      "name": "杭州",
      "id": "101210101",
      "lat": "30.28745842",
      "lon": "120.15357971",
      "adm2": "杭州",
      "adm1": "浙江省",
      "country": "中国",
      "tz": "Asia/Shanghai",
      "utcOffset": "+08:00",
      "isDst": "0",
      "type": "city",
      "rank": "10",
      "fxLink": "http://hfx.link/1"
    },
    "now": {
      "obsTime": "2023-06-25T15:35+08:00",
      "temp": "28",
      "feelsLike": "29",
      "icon": "100",
      "text": "晴",
      "wind360": "135",
      "windDir": "东南风",
      "windScale": "3",
      "windSpeed": "16",
      "humidity": "42",
      "precip": "0.0",
      "pressure": "1002",
      "vis": "30",
      "cloud": "0",
      "dew": "15"
    }
  },
  "refresh": 0
}</textarea>
    </div>
    
    <button id="send-request">发送请求</button>
    
    <div class="loading" id="loading">处理中</div>
    
    <div class="response-container">
        <h3>响应结果：</h3>
        <pre id="response-output">// 响应将显示在这里</pre>
    </div>
    
    <script>
        document.getElementById('send-request').addEventListener('click', async function() {
            const requestTextarea = document.getElementById('request-data');
            const responseOutput = document.getElementById('response-output');
            const loadingIndicator = document.getElementById('loading');
            
            try {
                // 显示加载指示器
                loadingIndicator.style.display = 'block';
                responseOutput.textContent = '正在发送请求...';
                
                // 解析JSON请求数据
                const requestData = JSON.parse(requestTextarea.value);
                
                // 发送请求到API
                const response = await fetch('tianqituijian.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                // 解析响应
                const responseData = await response.json();
                
                // 格式化并显示响应
                responseOutput.textContent = JSON.stringify(responseData, null, 2);
            } catch (error) {
                // 显示错误
                responseOutput.textContent = `错误: ${error.message}`;
                console.error('API调用错误:', error);
            } finally {
                // 隐藏加载指示器
                loadingIndicator.style.display = 'none';
            }
        });
    </script>
</body>
</html> 