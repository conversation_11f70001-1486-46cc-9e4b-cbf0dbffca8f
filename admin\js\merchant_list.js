/**
 * 商户管理模块
 */
const MerchantManager = {
    // 当前页码
    currentPage: 1,
    
    // 每页条数
    pageSize: 10,
    
    // 搜索关键词
    keyword: '',
    
    // 商户状态筛选
    status: 'all',
    
    // 总记录数
    totalRecords: 0,
    
    // 初始化
    init: function() {
        // 获取DOM元素
        this.searchInput = document.getElementById('searchKeyword');
        this.searchBtn = document.getElementById('searchBtn');
        this.statusFilter = document.getElementById('statusFilter');
        this.merchantTable = document.getElementById('merchantTable');
        this.paginationContainer = document.getElementById('paginationContainer');
        this.loadingIndicator = document.getElementById('merchantLoading');
        this.errorMessage = document.getElementById('merchantError');
        
        // 绑定事件
        this.bindEvents();
        
        // 加载商户列表
        this.loadMerchantList();
        
        // 初始化确认弹窗
        this.initConfirmModal();
    },
    
    // 绑定事件
    bindEvents: function() {
        // 搜索按钮点击事件
        this.searchBtn.addEventListener('click', () => {
            this.keyword = this.searchInput.value.trim();
            this.currentPage = 1;
            this.loadMerchantList();
        });
        
        // 搜索框回车事件
        this.searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.keyword = this.searchInput.value.trim();
                this.currentPage = 1;
                this.loadMerchantList();
            }
        });
        
        // 状态筛选事件
        this.statusFilter.addEventListener('change', () => {
            this.status = this.statusFilter.value;
            this.currentPage = 1;
            this.loadMerchantList();
        });
    },
    
    // 初始化确认弹窗
    initConfirmModal: function() {
        const modal = document.getElementById('confirmModal');
        const closeBtn = modal.querySelector('.close');
        const cancelBtn = document.getElementById('cancelBtn');
        
        // 关闭弹窗的点击事件
        closeBtn.addEventListener('click', () => {
            modal.style.display = 'none';
        });
        
        cancelBtn.addEventListener('click', () => {
            modal.style.display = 'none';
        });
        
        // 点击弹窗外部关闭
        window.addEventListener('click', (event) => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    },
    
    // 显示确认弹窗
    showConfirmModal: function(message, callback) {
        const modal = document.getElementById('confirmModal');
        const confirmMessage = document.getElementById('confirmMessage');
        const confirmBtn = document.getElementById('confirmBtn');
        
        // 设置确认消息
        confirmMessage.textContent = message;
        
        // 清除之前的事件监听
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
        
        // 添加新的确认事件
        newConfirmBtn.addEventListener('click', () => {
            modal.style.display = 'none';
            callback();
        });
        
        // 显示弹窗
        modal.style.display = 'block';
    },
    
    // 加载商户列表
    loadMerchantList: function() {
        // 显示加载指示器
        this.loadingIndicator.style.display = 'block';
        
        // 隐藏错误消息
        this.errorMessage.style.display = 'none';
        
        // 清空商户表格
        this.merchantTable.innerHTML = '';
        
        // 获取商户列表数据
        this.fetchMerchantList()
            .then(data => {
                // 隐藏加载指示器
                this.loadingIndicator.style.display = 'none';
                
                // 渲染商户列表
                this.renderMerchantList(data.list);
                
                // 更新总记录数
                this.totalRecords = data.total;
                
                // 渲染分页
                this.renderPagination();
            })
            .catch(error => {
                // 隐藏加载指示器
                this.loadingIndicator.style.display = 'none';
                
                // 显示错误消息
                this.errorMessage.textContent = `加载失败: ${error.message}`;
                this.errorMessage.style.display = 'block';
                
                console.error('Error loading merchant list:', error);
            });
    },
    
    // 获取商户列表数据
    fetchMerchantList: function() {
        return new Promise((resolve, reject) => {
            // 构建API URL - 使用管理员专用API路径
            const url = new URL('../login_backend/admin_get_merchants.php', window.location.origin);
            
            // 准备请求数据
            const requestData = {
                page: this.currentPage,
                limit: this.pageSize,
                status: this.status
            };
            
            // 如果有搜索关键词，添加到请求数据
            if (this.keyword) {
                requestData.keyword = this.keyword;
            }
            
            // 发送请求
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + Auth.getToken() // 使用Bearer认证方式
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    resolve(data.data);
                } else {
                    reject(new Error(data.message || '获取商户列表失败'));
                }
            })
            .catch(error => {
                reject(error);
            });
        });
    },
    
    // 渲染商户列表
    renderMerchantList: function(merchants) {
        if (!merchants || merchants.length === 0) {
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `<td colspan="8" class="text-center">暂无商户数据</td>`;
            this.merchantTable.appendChild(emptyRow);
            return;
        }
        
        // 遍历商户数据，创建表格行
        merchants.forEach(merchant => {
            const row = document.createElement('tr');
            
            // 默认头像
            const avatarUrl = merchant.avatar_url || 'https://cyymj.oss-cn-shanghai.aliyuncs.com/images/default-avatar.png';
            
            // 商户状态
            const merchantStatus = merchant.merchant_status || 'no';
            const statusClass = merchantStatus === 'yes' ? 'status-yes' : 'status-no';
            const statusText = merchantStatus === 'yes' ? '已入驻' : '未入驻';
            
            // 共享试穿点数状态
            const shareCredits = parseInt(merchant.share_try_on_credits) === 1;
            const shareClass = shareCredits ? 'share-badge' : 'share-badge no-share';
            const shareText = shareCredits ? '已开启' : '未开启';
            
            // 付费试穿点数
            const paidTryOnCount = merchant.paid_try_on_count || 0;
            
            row.innerHTML = `
                <td>${merchant.id}</td>
                <td><img src="${avatarUrl}" alt="头像" class="merchant-avatar"></td>
                <td>${merchant.nickname || '未设置昵称'}</td>
                <td>${merchant.clothes_count || 0}</td>
                <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                <td><span class="${shareClass}">${shareText}</span></td>
                <td>${paidTryOnCount}</td>
                <td>
                    ${merchantStatus === 'yes' ? 
                        `<button class="action-btn disable-btn" data-id="${merchant.id}">禁用商户</button>` : 
                        `<button class="action-btn enable-btn" data-id="${merchant.id}">启用商户</button>`}
                    <button class="action-btn view-btn" data-id="${merchant.id}">查看衣物</button>
                </td>
            `;
            
            // 添加到表格
            this.merchantTable.appendChild(row);
            
            // 获取操作按钮并添加点击事件
            const actionBtn = row.querySelector('.disable-btn, .enable-btn');
            if (actionBtn) {
                actionBtn.addEventListener('click', () => {
                    if (merchantStatus === 'yes') {
                        this.confirmDisableMerchant(merchant.id, merchant.nickname);
                    } else {
                        this.confirmEnableMerchant(merchant.id, merchant.nickname);
                    }
                });
            }
            
            // 获取查看按钮并添加点击事件
            const viewBtn = row.querySelector('.view-btn');
            viewBtn.addEventListener('click', () => {
                this.viewMerchantClothes(merchant.id);
            });
        });
    },
    
    // 渲染分页
    renderPagination: function() {
        // 清空分页容器
        this.paginationContainer.innerHTML = '';
        
        // 计算总页数
        const totalPages = Math.ceil(this.totalRecords / this.pageSize);
        
        if (totalPages <= 1) {
            return;
        }
        
        // 创建"上一页"按钮
        if (this.currentPage > 1) {
            const prevBtn = document.createElement('button');
            prevBtn.className = 'page-btn';
            prevBtn.textContent = '上一页';
            prevBtn.addEventListener('click', () => {
                this.currentPage--;
                this.loadMerchantList();
            });
            this.paginationContainer.appendChild(prevBtn);
        }
        
        // 创建页码按钮
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, startPage + 4);
        
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = i === this.currentPage ? 'page-btn active' : 'page-btn';
            pageBtn.textContent = i;
            pageBtn.addEventListener('click', () => {
                this.currentPage = i;
                this.loadMerchantList();
            });
            this.paginationContainer.appendChild(pageBtn);
        }
        
        // 创建"下一页"按钮
        if (this.currentPage < totalPages) {
            const nextBtn = document.createElement('button');
            nextBtn.className = 'page-btn';
            nextBtn.textContent = '下一页';
            nextBtn.addEventListener('click', () => {
                this.currentPage++;
                this.loadMerchantList();
            });
            this.paginationContainer.appendChild(nextBtn);
        }
    },
    
    // 确认禁用商户
    confirmDisableMerchant: function(merchantId, merchantName) {
        const message = `确定要禁用商户 "${merchantName || merchantId}" 吗？禁用后该用户将不再具有商户身份。`;
        
        this.showConfirmModal(message, () => {
            this.updateMerchantStatus(merchantId, 'exit');
        });
    },
    
    // 确认启用商户
    confirmEnableMerchant: function(merchantId, merchantName) {
        const message = `确定要将用户 "${merchantName || merchantId}" 设置为商户吗？`;
        
        this.showConfirmModal(message, () => {
            this.updateMerchantStatus(merchantId, 'join');
        });
    },
    
    // 更新商户状态
    updateMerchantStatus: function(merchantId, action) {
        // 显示加载指示器
        this.loadingIndicator.style.display = 'block';
        
        // 构建API URL - 使用管理员专用API路径
        const url = new URL('../login_backend/admin_update_merchant_status.php', window.location.origin);
        
        // 准备请求数据
        const requestData = {
            action: action, // 'exit'禁用或'join'启用
            merchant_id: merchantId
        };
        
        // 发送请求
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + Auth.getToken() // 使用Bearer认证方式
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            // 隐藏加载指示器
            this.loadingIndicator.style.display = 'none';
            
            if (data.code === 0) {
                // 显示成功消息
                alert(action === 'exit' ? '商户已成功禁用' : '商户已成功启用');
                
                // 重新加载商户列表
                this.loadMerchantList();
            } else {
                // 显示错误消息
                alert(`操作失败: ${data.message || '未知错误'}`);
            }
        })
        .catch(error => {
            // 隐藏加载指示器
            this.loadingIndicator.style.display = 'none';
            
            // 显示错误消息
            alert(`操作失败: ${error.message}`);
        });
    },
    
    // 查看商户衣物
    viewMerchantClothes: function(merchantId) {
        window.location.href = `merchant_clothes.html?id=${merchantId}`;
    }
};

// 文档加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 商户管理初始化
    MerchantManager.init();
}); 