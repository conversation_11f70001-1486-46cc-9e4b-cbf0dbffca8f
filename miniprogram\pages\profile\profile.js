const app = getApp();
const tagParser = require('../../utils/tagParser.js');

Page({
  data: {
    userInfo: {},
    hasUserInfo: false,
    clothesCount: 0,  // 实际衣物数量
    categoryCount: 0, // 实际类别数量
    tagCount: 0,      // 实际标签数量
    wardrobeCount: 0, // 衣橱数量
    outfitCategoryCount: 0, // 穿搭分类数量
    freeCount: 0,     // 免费试衣次数
    paidCount: 0,     // 付费试衣次数
    statusBarHeight: 20, // 默认状态栏高度
    titleBarHeight: 44,   // 标题栏高度
    isMerchant: false, // 是否已入驻商家
    isShareCredits: false, // 是否共享试穿点数
    showMerchantTips: false, // 是否显示商家入驻提示
    showCustomToast: false,
    customToastText: '',
  },

  onLoad: function (options) {
    // 获取系统信息
    this.getSystemInfo();
    
    // 初始化时强制延迟获取一次用户信息，确保页面状态同步
    this.checkLoginStatus();
    
    // 延迟执行一次用户信息获取，确保数据同步
    setTimeout(() => {
      this.getUserInfo();
      
      // 如果已登录，立即刷新商家状态
      if (app.globalData.token) {
        this.refreshMerchantStatus();
      }
    }, 200);
  },

  // 获取系统信息
  getSystemInfo: function () {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight
      });
      console.log('状态栏高度:', systemInfo.statusBarHeight);
    } catch (e) {
      console.error('获取系统信息失败:', e);
    }
  },

  onShow: function () {
    console.log("页面显示，重新获取数据");
    
    // 每次页面显示时检查登录状态和刷新用户信息
    this.checkLoginStatus();
    
    // 重新获取用户信息以确保商家状态是最新的
    this.getUserInfo();
    
    // 如果已登录，获取衣物统计数据和衣橱数量
    if (app.globalData.token) {
      // 强制刷新商家状态，确保状态同步
      this.refreshMerchantStatus();
      
      // 获取其他统计数据
      this.loadClothingStats();
      this.loadWardrobeCount();
      this.loadOutfitCategoryCount();
      this.loadTryOnCount();
    }
  },

  // 检查用户登录状态
  checkLoginStatus: function () {
    console.log("检查登录状态", app.globalData.token, app.globalData.userInfo);
    
    if (app.globalData.token && app.globalData.userInfo) {
      // 处理字段名称差异 (后端用 nickname, avatar_url，前端用 nickName, avatarUrl)
      const userInfo = {
        id: app.globalData.userInfo.id,
        nickname: app.globalData.userInfo.nickname,
        avatar_url: app.globalData.userInfo.avatar_url,
        gender: app.globalData.userInfo.gender,
        merchant_status: app.globalData.userInfo.merchant_status || 'no',
        share_try_on_credits: app.globalData.userInfo.share_try_on_credits || 0
      };
      
      console.log("设置用户信息:", userInfo);
      
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true,
        isMerchant: userInfo.merchant_status === 'yes',
        isShareCredits: parseInt(userInfo.share_try_on_credits) === 1 || userInfo.share_try_on_credits === true || userInfo.share_try_on_credits === '1'
      });
      
      console.log("当前商家状态:", this.data.isMerchant, "共享状态:", this.data.isShareCredits, "原始值:", userInfo.share_try_on_credits, "类型:", typeof userInfo.share_try_on_credits);
      
      // 加载衣物统计数据和衣橱数量
      this.loadClothingStats();
      this.loadWardrobeCount();
      this.loadOutfitCategoryCount();
      this.loadTryOnCount();
    } else {
      this.setData({
        userInfo: {},
        hasUserInfo: false,
        clothesCount: 0,
        categoryCount: 0,
        tagCount: 0,
        wardrobeCount: 0,
        outfitCategoryCount: 0,
        freeCount: 0,
        paidCount: 0,
        isMerchant: false,
        isShareCredits: false
      });
    }
  },

  // 加载衣物统计数据
  loadClothingStats: function() {
    if (!app.globalData.token) {
      return;
    }

    console.log("开始获取衣物统计数据");
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothes.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log("获取衣物列表成功:", res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          const clothes = res.data.data || [];
          
          // 计算衣物总数
          const clothesCount = clothes.length;
          
          // 计算衣物类别数量（去重）
          const categories = new Set();
          clothes.forEach(item => {
            if (item.category) {
              categories.add(item.category);
            }
          });
          const categoryCount = categories.size;
          
          // 计算所有标签数量（去重）
          const allTags = new Set();
          clothes.forEach(item => {
            if (item.tags) {
              try {
                // 使用标签解析工具解析标签
                const tags = tagParser.parseTags(item.tags);
                
                // 添加到集合中（自动去重）
                tags.forEach(tag => allTags.add(tag));
              } catch (e) {
                console.error("解析标签失败:", e, item.tags);
              }
            }
          });
          const tagCount = allTags.size;
          
          console.log(`统计结果: 衣物数量=${clothesCount}, 类别数量=${categoryCount}, 标签数量=${tagCount}`);
          
          // 更新数据
          this.setData({
            clothesCount,
            categoryCount,
            tagCount
          });
        }
      },
      fail: (err) => {
        console.error("获取衣物统计数据失败:", err);
      }
    });
  },

  // 加载衣橱统计数据
  loadWardrobeCount: function() {
    if (!app.globalData.token) {
      return;
    }

    console.log("开始获取衣橱统计数据");
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_wardrobes.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log("获取衣橱列表成功:", res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          const wardrobes = res.data.data || [];
          
          // 计算衣橱总数
          const wardrobeCount = wardrobes.length;
          
          console.log(`衣橱数量=${wardrobeCount}`);
          
          // 更新数据
          this.setData({
            wardrobeCount
          });
        }
      },
      fail: (err) => {
        console.error("获取衣橱统计数据失败:", err);
      }
    });
  },
  
  // 加载穿搭分类统计数据
  loadOutfitCategoryCount: function() {
    if (!app.globalData.token) {
      return;
    }

    console.log("开始获取穿搭分类统计数据");
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_outfit_categories.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log("获取穿搭分类列表成功:", res.data);
        
        if (res.statusCode === 200 && res.data.success && res.data.data) {
          const categories = res.data.data || [];
          
          // 计算穿搭分类总数
          const outfitCategoryCount = categories.length;
          
          console.log(`穿搭分类数量=${outfitCategoryCount}`);
          
          // 更新数据
          this.setData({
            outfitCategoryCount
          });
        }
      },
      fail: (err) => {
        console.error("获取穿搭分类统计数据失败:", err);
      }
    });
  },

  // 加载试衣次数
  loadTryOnCount: function() {
    if (!app.globalData.token) {
      return;
    }
    
    console.log("开始获取试衣次数");
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_user_try_on_count.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log("获取试衣次数成功:", res.data);
        
        if (res.statusCode === 200 && !res.data.error) {
          const data = res.data.data || {};
          
          // 更新试衣次数数据，使用与后端一致的字段名
          this.setData({
            freeCount: data.free_try_on_count !== undefined ? data.free_try_on_count : 
                       (data.try_on_count !== undefined ? data.try_on_count : 0),
            paidCount: data.paid_try_on_count || 0
          });
          
          console.log(`试衣次数: 免费=${this.data.freeCount}, 付费=${this.data.paidCount}`);
        }
      },
      fail: (err) => {
        console.error("获取试衣次数失败:", err);
      }
    });
  },

  // 处理头像加载失败
  onAvatarError: function(e) {
    console.log("头像加载失败，使用默认头像");
    this.setData({
      'userInfo.avatar_url': '/images/default-avatar.png'
    });
  },

  // 跳转到登录页
  goToLogin: function () {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  // 跳转到教程页
  goToTutorial: function () {
    wx.navigateTo({
      url: '/pages/tutorial/tutorial'
    });
  },

  // 跳转到打赏页面
  navigateToDonation: function() {
    wx.navigateTo({
      url: '/pages/donation/index/index'
    });
  },

  // 跳转到我的照片页
  goToMyPhotos: function () {
    wx.navigateTo({
      url: '/pages/photos/index/index'
    });
  },

  // 跳转到试穿历史页面
  goToTryOnHistory: function() {
    wx.navigateTo({
      url: '/pages/try_on/history/index'
    });
  },

  // 跳转到管理衣橱页面
  navigateToManage: function() {
    wx.navigateTo({
      url: '/pages/clothing/manage/manage'
    });
  },

  // 跳转到衣橱管理页面
  navigateToWardrobes: function() {
    wx.navigateTo({
      url: '/pages/wardrobes/index/index'
    });
  },
  
  // 跳转到穿搭分类页面
  navigateToOutfitCategories: function() {
    wx.navigateTo({
      url: '/pages/outfit_categories/index/index'
    });
  },
  
  // 跳转到衣物分类页面
  navigateToClothingCategories: function() {
    wx.navigateTo({
      url: '/pages/clothing-categories/index/index'
    });
  },
  
  // 跳转到推荐穿搭页面
  navigateToRecommendedOutfits: function() {
    wx.navigateTo({
      url: '/pages/recommended_outfits/index/index'
    });
  },
  
  // 跳转到虚拟试衣页面
  navigateToTryOn: function() {
    wx.navigateTo({
      url: '/pages/try_on/index/index'
    });
  },

  // 联系客服成功回调
  handleContact: function(e) {
    console.log('进入客服会话', e.detail);
  },

  // 复制微信号
  copyWechatId: function() {
    const wechatId = "shawii"; // 预设的微信号
    wx.setClipboardData({
      data: wechatId,
      success: () => {
        // 使用自定义toast样式，显示更长的文字
        this.setData({
          showCustomToast: true,
          customToastText: '复制成功，请前往微信搜索添加'
        });
        
        // 2秒后自动隐藏提示
        setTimeout(() => {
          this.setData({
            showCustomToast: false
          });
        }, 2500);
      },
      // 添加fail回调确保错误处理
      fail: (err) => {
        console.error('复制失败:', err);
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      },
      // 关键：添加complete回调并防止系统默认toast显示
      complete: () => {
        // 通过创建一个不可见的元素并立即隐藏来阻止系统toast
        wx.hideToast();
      }
    });
  },

  // 切换底部导航标签
  switchTab: function (e) {
    const url = e.currentTarget.dataset.url;
    wx.switchTab({
      url: url
    });
  },

  // 退出登录
  logout: function () {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录信息
          wx.removeStorageSync('token');
          wx.removeStorageSync('userInfo');
          app.globalData.token = null;
          app.globalData.userInfo = null;
          
          // 更新页面状态
          this.setData({
            userInfo: {},
            hasUserInfo: false,
            clothesCount: 0,
            categoryCount: 0,
            tagCount: 0,
            wardrobeCount: 0,
            outfitCategoryCount: 0,
            freeCount: 0,
            paidCount: 0
          });
          
          // 显示提示
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  // 跳转到购买试衣次数页面
  navigateToTryOnCount: function() {
    if (!this.data.hasUserInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: '/pages/purchase/try_on_count/index'
    });
  },

  // 获取用户信息
  getUserInfo: function() {
    const that = this;
    
    // 先检查全局变量中是否有用户信息
    if (app.globalData.userInfo) {
      console.log("从全局变量获取用户信息:", app.globalData.userInfo);
      that.setData({
        hasUserInfo: true,
        userInfo: app.globalData.userInfo,
        isMerchant: app.globalData.userInfo.merchant_status === 'yes',
        isShareCredits: parseInt(app.globalData.userInfo.share_try_on_credits) === 1 || app.globalData.userInfo.share_try_on_credits === true || app.globalData.userInfo.share_try_on_credits === '1'
      });
      console.log("设置商家状态:", that.data.isMerchant, "共享状态:", that.data.isShareCredits);
      return;
    }
    
    // 从本地存储获取
    wx.getStorage({
      key: 'userInfo',
      success: function(res) {
        if (res.data) {
          console.log("从本地存储获取用户信息:", res.data);
          // 确保用户信息是对象而不是字符串
          let userInfo = res.data;
          if (typeof userInfo === 'string') {
            try {
              userInfo = JSON.parse(userInfo);
              console.log("解析后的用户信息:", userInfo);
            } catch (e) {
              console.error("解析用户信息字符串失败:", e);
              return;
            }
          }
          
          that.setData({
            hasUserInfo: true,
            userInfo: userInfo,
            isMerchant: userInfo.merchant_status === 'yes',
            isShareCredits: parseInt(userInfo.share_try_on_credits) === 1 || userInfo.share_try_on_credits === true || userInfo.share_try_on_credits === '1'
          });
          console.log("设置商家状态:", that.data.isMerchant, "共享状态:", that.data.isShareCredits, "原始值:", userInfo.share_try_on_credits, "类型:", typeof userInfo.share_try_on_credits);
        }
      },
      fail: function(err) {
        console.error("获取用户信息失败:", err);
      }
    });
  },

  // 点击商家入驻按钮
  toggleMerchantStatus: function() {
    this.setData({
      showMerchantTips: true
    });
  },

  // 关闭商家入驻提示
  closeMerchantTips: function() {
    this.setData({
      showMerchantTips: false
    });
  },

  // 确认商家入驻或退出
  confirmMerchantAction: function() {
    const that = this;
    const action = this.data.isMerchant ? 'exit' : 'join';
    
    // 先关闭弹窗，避免UI卡住
    that.setData({
      showMerchantTips: false
    });
    
    console.log('开始处理商家' + (action === 'join' ? '入驻' : '退出') + '请求');
    
    wx.showLoading({
      title: action === 'join' ? '入驻中...' : '退出中...',
      mask: true // 添加mask防止用户重复点击
    });
    
    // 获取token
    wx.getStorage({
      key: 'token',
      success: function(res) {
        if (res.data) {
          // 发送API请求
          wx.request({
            url: getApp().globalData.apiBaseUrl + '/update_merchant_status.php',
            method: 'POST',
            data: {
              token: res.data,
              action: action
            },
            success: function(response) {
              console.log('商家入驻API响应:', response.data);
              
              if (response.data.code === 0) {
                console.log('商家' + (action === 'join' ? '入驻' : '退出') + '成功');
                
                // 先隐藏loading，否则toast可能不显示
                wx.hideLoading();
                
                // 显示成功提示
                setTimeout(function() {
                  wx.showToast({
                    title: response.data.message,
                    icon: 'success',
                    duration: 2000
                  });
                }, 100);
                
                // 立即更新页面状态，不等待存储操作完成
                that.setData({
                  isMerchant: action === 'join',
                  isShareCredits: action === 'join' ? false : that.data.isShareCredits
                });
                console.log('页面状态已立即更新: isMerchant =', action === 'join');
                
                // 更新本地存储的数据
                wx.getStorage({
                  key: 'userInfo',
                  success: (userRes) => {
                    if (userRes.data) {
                      // 确保userInfo是对象而不是字符串
                      let userInfo = userRes.data;
                      if (typeof userInfo === 'string') {
                        try {
                          userInfo = JSON.parse(userInfo);
                        } catch (e) {
                          console.error('解析用户信息出错:', e);
                          return;
                        }
                      }
                      
                      if (userInfo.merchant_status) {
                        userInfo.merchant_status = action === 'join' ? 'yes' : 'no';
                      }
                      if (userInfo.share_try_on_credits !== undefined) {
                        userInfo.share_try_on_credits = action === 'join' ? 1 : 0;
                      }
                      
                      wx.setStorage({
                        key: 'userInfo',
                        data: userInfo,
                        success: () => {
                          console.log('本地用户状态数据已更新');
                          
                          // 更新全局状态
                          if (app.globalData.userInfo) {
                            app.globalData.userInfo.merchant_status = action === 'join' ? 'yes' : 'no';
                            if (action === 'exit') {
                              app.globalData.userInfo.share_try_on_credits = 0;
                            }
                            console.log('全局用户信息已更新');
                          }
                        },
                        fail: function(err) {
                          console.error('保存用户信息失败:', err);
                        }
                      });
                    }
                  },
                  fail: function(err) {
                    console.error('获取用户信息失败:', err);
                  }
                });
              } else {
                console.error('商家入驻失败:', response.data);
                wx.hideLoading();
                wx.showToast({
                  title: response.data.message || '操作失败',
                  icon: 'none'
                });
              }
            },
            fail: function(err) {
              console.error('网络请求失败:', err);
              wx.hideLoading();
              wx.showToast({
                title: '网络错误，请稍后再试',
                icon: 'none'
              });
            },
            complete: function() {
              // 确保无论如何都隐藏loading
              console.log('API请求完成，确保隐藏loading');
              wx.hideLoading();
            }
          });
        } else {
          wx.hideLoading();
          wx.showToast({
            title: '获取登录信息失败',
            icon: 'none'
          });
        }
      },
      fail: function(err) {
        console.error('获取token失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
      },
      complete: function() {
        // 再次确保loading隐藏
        setTimeout(function() {
          wx.hideLoading();
        }, 500);
      }
    });
  },

  // 切换共享试穿点数开关
  toggleShareCredits: function(e) {
    const that = this;
    const shareStatus = e && e.detail ? e.detail.value : !this.data.isShareCredits;
    
    if (!this.data.isMerchant) {
      wx.showToast({
        title: '请先入驻商家',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: shareStatus ? '开启中...' : '关闭中...',
    });
    
    wx.getStorage({
      key: 'token',
      success: function(res) {
        if (res.data) {
          wx.request({
            url: getApp().globalData.apiBaseUrl + '/update_share_credits.php',
            method: 'POST',
            data: {
              token: res.data,
              share_status: shareStatus ? 1 : 0
            },
            success: function(response) {
              wx.hideLoading();
              if (response.data.code === 0) {
                // 更新本地存储的用户信息
                wx.getStorage({
                  key: 'userInfo',
                  success: function(userRes) {
                    if (userRes.data) {
                      // 确保userInfo是对象而不是字符串
                      let userInfo = userRes.data;
                      if (typeof userInfo === 'string') {
                        try {
                          userInfo = JSON.parse(userInfo);
                        } catch (e) {
                          console.error('解析用户信息出错:', e);
                          return;
                        }
                      }
                      
                      userInfo.share_try_on_credits = shareStatus ? 1 : 0;
                      wx.setStorage({
                        key: 'userInfo',
                        data: userInfo,
                        success: function() {
                          console.log('用户共享设置已更新');
                        }
                      });
                      
                      that.setData({
                        isShareCredits: shareStatus
                      });
                      
                      wx.showToast({
                        title: response.data.message,
                        icon: 'success'
                      });
                    }
                  }
                });
              } else {
                that.setData({
                  isShareCredits: !shareStatus // 恢复原来的状态
                });
                wx.showToast({
                  title: response.data.message || '操作失败',
                  icon: 'none'
                });
              }
            },
            fail: function() {
              wx.hideLoading();
              that.setData({
                isShareCredits: !shareStatus // 恢复原来的状态
              });
              wx.showToast({
                title: '网络错误，请稍后再试',
                icon: 'none'
              });
            }
          });
        }
      },
      fail: function() {
        wx.hideLoading();
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
      }
    });
  },

  // 刷新商家状态
  refreshMerchantStatus: function() {
    if (!app.globalData.token) {
      return;
    }
    
    console.log("开始刷新商家状态");
    
    // 直接使用token请求验证API获取用户信息
    wx.request({
      url: getApp().globalData.apiBaseUrl + '/verify_token.php',
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (response) => {
        if (response.data && !response.data.error && response.data.data) {
          const userData = response.data.data;
          console.log('刷新获取到的用户数据:', userData);
          
          // 更新全局状态
          if (app.globalData.userInfo) {
            if (userData.merchant_status) {
              app.globalData.userInfo.merchant_status = userData.merchant_status;
            }
            if (userData.share_try_on_credits !== undefined) {
              app.globalData.userInfo.share_try_on_credits = userData.share_try_on_credits;
            }
          }
          
          // 更新页面状态
          this.setData({
            isMerchant: userData.merchant_status === 'yes',
            isShareCredits: parseInt(userData.share_try_on_credits) === 1 || userData.share_try_on_credits === true || userData.share_try_on_credits === '1'
          });
          
          console.log('已刷新商家状态:', this.data.isMerchant, '共享状态:', this.data.isShareCredits, '原始值:', userData.share_try_on_credits, '类型:', typeof userData.share_try_on_credits);
          
          // 更新本地存储数据
          wx.getStorage({
            key: 'userInfo',
            success: (userRes) => {
              if (userRes.data) {
                // 确保userInfo是对象而不是字符串
                let userInfo = userRes.data;
                if (typeof userInfo === 'string') {
                  try {
                    userInfo = JSON.parse(userInfo);
                  } catch (e) {
                    console.error('解析用户信息出错:', e);
                    return;
                  }
                }
                
                if (userData.merchant_status) {
                  userInfo.merchant_status = userData.merchant_status;
                }
                if (userData.share_try_on_credits !== undefined) {
                  userInfo.share_try_on_credits = userData.share_try_on_credits;
                }
                
                wx.setStorage({
                  key: 'userInfo',
                  data: userInfo,
                  success: () => {
                    console.log('本地用户状态数据已更新');
                  }
                });
              }
            }
          });
        }
      }
    });
  },

  // 添加跳转到个人形象分析页面的方法
  goToImageAnalysis: function() {
    // 如果未登录，弹出登录提示
    if (!app.globalData.token) {
      wx.showModal({
        title: '提示',
        content: '需要登录才能使用个人形象分析功能',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    // 已登录，直接跳转
    wx.navigateTo({
      url: '/pages/image_analysis/index/index'
    });
  },
  
  // 添加跳转到形象分析历史页面的方法
  goToImageAnalysisHistory: function() {
    // 如果未登录，弹出登录提示
    if (!app.globalData.token) {
      wx.showModal({
        title: '提示',
        content: '需要登录才能查看形象分析历史',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    // 已登录，直接跳转到形象分析历史页面
    wx.navigateTo({
      url: '/pages/image_analysis/history/history'
    });
  },
  
  // 添加跳转到面容分析历史页面的方法
  goToFaceAnalysisHistory: function() {
    // 如果未登录，弹出登录提示
    if (!app.globalData.token) {
      wx.showModal({
        title: '提示',
        content: '需要登录才能查看面容分析历史',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    // 已登录，直接跳转到面容分析历史页面
    wx.navigateTo({
      url: '/pages/face_analysis/history/history'
    });
  },
  
  // 在首页显示Banner
  showBannerInHome: function(e) {
    // 阻止冒泡，避免触发Banner点击事件
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    
    // 判断是否需要阻止默认的点击事件传递
    if (e && e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.preventTap) {
      // 这里不需要做额外处理，只是标记这个点击不应该传递到父元素
    }
    
    // 在本地存储中设置为不隐藏
    wx.setStorageSync('hideImageAnalysisBanner', false);
    
    // 显示提示
    wx.showToast({
      title: '已在首页显示',
      icon: 'success',
      duration: 1500
    });
  },

  // 跳转到衣物标签页面
  navigateToClothingTags: function() {
    if (!this.data.hasUserInfo) {
      this.showLoginModal();
      return;
    }
    
    wx.navigateTo({
      url: '/pages/clothing-tags/index/index'
    });
  },
}); 