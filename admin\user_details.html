<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户详情 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        .back-link {
            display: inline-flex;
            align-items: center;
            margin-bottom: 15px;
            color: #1890ff;
            font-size: 14px;
            cursor: pointer;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .user-profile {
            display: flex;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        .user-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 20px;
            border: 1px solid #eee;
        }
        .user-info-main {
            flex: 1;
        }
        .user-name {
            font-size: 20px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        .user-id {
            color: #999;
            margin-bottom: 15px;
        }
        .user-actions {
            margin-top: 15px;
        }
        .user-action-btn {
            padding: 5px 15px;
            border-radius: 4px;
            border: 1px solid;
            background-color: transparent;
            cursor: pointer;
            margin-right: 10px;
        }
        .user-disable-btn {
            color: #f5222d;
            border-color: #f5222d;
        }
        .user-disable-btn:hover {
            background-color: #fff1f0;
        }
        .user-enable-btn {
            color: #52c41a;
            border-color: #52c41a;
        }
        .user-enable-btn:hover {
            background-color: #f6ffed;
        }
        .user-danger-btn {
            color: #ff4d4f;
            border-color: #ff4d4f;
            margin-left: 10px;
        }
        .user-danger-btn:hover {
            background-color: #fff1f0;
        }
        .user-meta {
            display: flex;
            flex-wrap: wrap;
        }
        .meta-item {
            width: 50%;
            margin-bottom: 10px;
        }
        .meta-label {
            color: #999;
            margin-right: 5px;
        }
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
        }
        .status-active {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        .status-disabled {
            background-color: #fff1f0;
            color: #f5222d;
        }
        .stats-container {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }
        .stat-card {
            flex: 1;
            min-width: 200px;
            margin: 10px;
            padding: 15px;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
        }
        .stat-title {
            font-size: 14px;
            color: #999;
            margin-bottom: 10px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: 500;
            color: #333;
        }
        .section-title {
            font-size: 16px;
            font-weight: 500;
            margin: 20px 0 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .section-title .view-all-btn {
            font-size: 14px;
            color: #1890ff;
            cursor: pointer;
            background: none;
            border: none;
        }
        .section-title .view-all-btn:hover {
            text-decoration: underline;
        }
        .item-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
        }
        .item-card {
            border: 1px solid #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
        }
        .item-img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }
        .item-info {
            padding: 10px;
        }
        .item-name {
            font-size: 14px;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .item-meta {
            font-size: 12px;
            color: #999;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        .empty-text {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            position: relative;
            width: 800px;
            max-width: 90%;
            max-height: 90vh;
            margin: 50px auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 20px;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .close-btn {
            font-size: 20px;
            cursor: pointer;
            background: none;
            border: none;
            color: #999;
        }
        
        .modal-body {
            margin-bottom: 20px;
        }
        
        .modal-pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        
        .page-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            background-color: white;
            margin: 0 5px;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .page-btn:disabled {
            color: #d9d9d9;
            cursor: not-allowed;
        }
        
        .page-info {
            padding: 6px 12px;
        }
        
        /* 试衣次数管理样式 */
        .try-on-count-container {
            display: flex;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        .try-on-count-status {
            flex: 1;
            min-width: 200px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 6px;
            margin-right: 20px;
            margin-bottom: 15px;
        }
        .try-on-count-editor {
            flex: 2;
            min-width: 300px;
            padding: 15px;
            background-color: #f0f7ff;
            border-radius: 6px;
        }
        .count-label, .editor-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        .count-value {
            font-size: 32px;
            font-weight: 500;
            color: #1890ff;
        }
        .count-note {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }
        .editor-controls {
            display: flex;
            flex-direction: column;
            margin-bottom: 10px;
        }
        .editor-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .editor-row label {
            width: 80px;
            font-size: 14px;
            color: #666;
        }
        .count-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            outline: none;
            font-size: 16px;
            margin-right: 10px;
        }
        .update-count-btn {
            padding: 8px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
            align-self: flex-end;
        }
        .update-count-btn:hover {
            background-color: #40a9ff;
        }
        .editor-hint {
            font-size: 12px;
            color: #999;
        }
        .count-mode-label {
            font-size: 14px;
            padding: 2px 8px;
            border-radius: 10px;
            background-color: #e6f7ff;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>用户详情</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="back-link" onclick="location.href='user_list.html'">
                &lt; 返回用户列表
            </div>
            
            <div class="card" id="userDetailContainer">
                <div class="loading">加载中...</div>
            </div>
            
            <div class="card">
                <div class="section-title">统计信息</div>
                <div class="stats-container" id="statsContainer">
                    <div class="loading">加载中...</div>
                </div>
            </div>
            
            <!-- 添加试衣次数管理卡片 -->
            <div class="card" id="tryOnCountCard">
                <div class="section-title">
                    <span>试衣次数管理</span>
                    <span id="countModeLabel" class="count-mode-label"></span>
                </div>
                <div class="try-on-count-container">
                    <div class="try-on-count-status">
                        <div class="count-label">当前免费试衣次数</div>
                        <div class="count-value" id="currentFreeCount">-</div>
                        <div class="count-note">每日自动刷新为1次</div>
                    </div>
                    <div class="try-on-count-status">
                        <div class="count-label">当前付费试衣次数</div>
                        <div class="count-value" id="currentPaidCount">-</div>
                        <div class="count-note">需要付费购买，不自动刷新</div>
                    </div>
                    <div class="try-on-count-editor">
                        <div class="editor-label">设置试衣次数</div>
                        <div class="editor-controls">
                            <div class="editor-row">
                                <label for="newFreeCountInput">免费次数:</label>
                                <input type="number" id="newFreeCountInput" min="0" max="10" class="count-input" placeholder="免费次数">
                            </div>
                            <div class="editor-row">
                                <label for="newPaidCountInput">付费次数:</label>
                                <input type="number" id="newPaidCountInput" min="0" class="count-input" placeholder="付费次数">
                            </div>
                            <button id="updateCountBtn" class="update-count-btn">更新</button>
                        </div>
                        <div class="editor-hint">注: 设置为0表示用户无法使用该类型试衣次数</div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="section-title">
                    <span>最近上传的衣物</span>
                    <button class="view-all-btn" onclick="UserDetails.viewAllItems('clothes')">查看全部</button>
                </div>
                <div class="item-grid" id="clothesContainer">
                    <div class="loading">加载中...</div>
                </div>
            </div>
            
            <div class="card">
                <div class="section-title">
                    <span>最近上传的照片</span>
                    <button class="view-all-btn" onclick="UserDetails.viewAllItems('photos')">查看全部</button>
                </div>
                <div class="item-grid" id="photosContainer">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模态框 -->
    <div id="itemsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">所有衣物</h3>
                <button class="close-btn" onclick="UserDetails.closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="item-grid" id="modalItemsContainer">
                    <div class="loading">加载中...</div>
                </div>
            </div>
            <div class="modal-pagination">
                <button id="modalPrevBtn" class="page-btn" disabled>&lt; 上一页</button>
                <div id="modalPageInfo" class="page-info">第 1/1 页</div>
                <button id="modalNextBtn" class="page-btn" disabled>下一页 &gt;</button>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/user_details.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为user
            Sidebar.init('user');
            
            // 获取管理员信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化用户详情页面
            if (typeof UserDetails !== 'undefined') {
                UserDetails.init();
            }
        });
    </script>
</body>
</html> 