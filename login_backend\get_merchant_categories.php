<?php
header("Content-Type: application/json");
require_once './db.php';
require_once './auth.php';
require_once './config.php';

// 初始化响应数组
$response = [
    'code' => 0,
    'message' => 'success',
    'data' => []
];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['code'] = 405;
    $response['message'] = 'Method Not Allowed';
    echo json_encode($response);
    exit;
}

// 获取POST参数
$postData = json_decode(file_get_contents("php://input"), true);
$token = isset($postData['token']) ? $postData['token'] : '';
$merchantId = isset($postData['merchant_id']) ? intval($postData['merchant_id']) : 0;
$wardrobeId = isset($postData['wardrobe_id']) ? intval($postData['wardrobe_id']) : 0;

// 处理 demo_mode 参数
$demoMode = false;
if (isset($postData['demo_mode'])) {
    // 支持各种可能的true值: true, "true", "1", 1
    if ($postData['demo_mode'] === true || 
        $postData['demo_mode'] === "true" || 
        $postData['demo_mode'] === "1" || 
        $postData['demo_mode'] === 1 ||
        $postData['demo_mode'] == true) {
        $demoMode = true;
    }
}

if (empty($token) && !$demoMode) {
    $response['code'] = 400;
    $response['message'] = 'Missing required parameters';
    echo json_encode($response);
    exit;
}

if ($merchantId <= 0 || $wardrobeId <= 0) {
    $response['code'] = 400;
    $response['message'] = 'Missing required parameters';
    echo json_encode($response);
    exit;
}

// 验证用户token，除非是体验模式
$userId = null;
if (!$demoMode) {
    $auth = new Auth();
    $verifyResult = $auth->verifyToken($token);

    if (!$verifyResult) {
        $response['code'] = 401;
        $response['message'] = '无效或已过期的令牌';
        echo json_encode($response);
        exit;
    }
    
    $userId = $verifyResult['sub'];
} else {
    // 在体验模式下，使用默认用户ID
    $userId = 1;
}

$db = new Database();
$conn = $db->getConnection();

try {
    // 首先验证商家是否存在且已入驻
    $checkStmt = $conn->prepare("
        SELECT merchant_status 
        FROM users 
        WHERE id = :merchant_id AND merchant_status = 'yes'
    ");
    $checkStmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_INT);
    $checkStmt->execute();
    
    if ($checkStmt->rowCount() === 0) {
        $response['code'] = 404;
        $response['message'] = '商家不存在或未入驻';
        echo json_encode($response);
        exit;
    }
    
    // 验证衣橱是否属于该商家
    $wardrobeStmt = $conn->prepare("
        SELECT id 
        FROM wardrobes 
        WHERE id = :wardrobe_id AND user_id = :merchant_id
    ");
    $wardrobeStmt->bindParam(':wardrobe_id', $wardrobeId, PDO::PARAM_INT);
    $wardrobeStmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_INT);
    $wardrobeStmt->execute();
    
    if ($wardrobeStmt->rowCount() === 0) {
        $response['code'] = 404;
        $response['message'] = '衣橱不存在或不属于该商家';
        echo json_encode($response);
        exit;
    }
    
    // 获取该商家的自定义衣物分类和系统分类
    $stmt = $conn->prepare("
        SELECT cc.id, cc.name, cc.code, cc.is_system, cc.user_id, cc.sort_order, cc.created_at
        FROM clothing_categories cc
        WHERE cc.user_id = :merchant_id OR cc.is_system = 1
        ORDER BY cc.sort_order ASC, cc.id ASC
    ");
    $stmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_INT);
    $stmt->execute();
    
    $categories = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        // 检查该分类下是否有衣物
        $checkClothesStmt = $conn->prepare("
            SELECT COUNT(*) as count
            FROM clothes
            WHERE user_id = :merchant_id AND wardrobe_id = :wardrobe_id AND category_id = :category_id
        ");
        $checkClothesStmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_INT);
        $checkClothesStmt->bindParam(':wardrobe_id', $wardrobeId, PDO::PARAM_INT);
        $checkClothesStmt->bindParam(':category_id', $row['id'], PDO::PARAM_INT);
        $checkClothesStmt->execute();
        $clothesCount = $checkClothesStmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 确保is_system是布尔值
        $row['is_system'] = (bool)$row['is_system'];
        // 将id确保为整数
        $row['id'] = (int)$row['id'];
        // 确保clothes_count是整数
        $row['clothes_count'] = (int)$clothesCount;
        
        $categories[] = $row;
    }
    
    // 添加调试日志
    error_log("分类数据: " . json_encode($categories));
    
    $response['data'] = $categories;
} catch (Exception $e) {
    $response['code'] = 500;
    $response['message'] = '处理请求时发生错误: ' . $e->getMessage();
} finally {
    // PDO不需要手动关闭连接
    $conn = null;
}

echo json_encode($response); 