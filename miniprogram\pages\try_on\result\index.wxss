/* 页面容器 */
page {
  background-color: #f9f9f9;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 主内容区域 */
.result-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-bottom: 40px;
  overflow-y: auto;
}

/* 结果图片区域 */
.result-image-wrapper {
  width: 90%;
  padding: 20px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  border-radius: 16px;
  margin-bottom: 12px;
}

.result-image {
  width: 100%;
  position: relative;
  background-color: #f5f5f5;
  margin: 0 auto;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.image-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: #f5f5f5;
  text-align: center;
  cursor: pointer;
}

.result-img {
  width: 100%;
  display: block;
  margin: 0 auto;
  background-color: #f5f5f5;
  object-fit: contain;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(to top, rgba(0,0,0,0.6), transparent);
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.watermark {
  position: absolute;
  bottom: 12px;
  right: 12px;
  color: rgba(255,255,255,0.8);
  font-size: 12px;
  display: flex;
  align-items: center;
}

.watermark-icon {
  margin-right: 4px;
}

/* 衣物缩略图容器 - 新样式 */
.clothes-thumbnails-container {
  width: 90%;
  margin: 0 auto;
  background-color: #fff;
  padding: 16px;
  border-radius: 0 0 16px 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  margin-bottom: 12px;
}

/* 衣物缩略图选择器 */
.clothes-thumbnails {
  display: flex;
  overflow-x: auto;
  gap: 12px;
  justify-content: center;
  padding-bottom: 8px;
}

.clothes-thumb {
  position: relative;
  flex-shrink: 0;
  width: 70px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.clothes-thumb.active {
  transform: translateY(-4px);
  box-shadow: 0 4px 10px rgba(0,0,0,0.15);
  border: 2px solid #000;
}

.thumb-image {
  width: 70px;
  height: 70px;
  object-fit: cover;
}

.thumb-label {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 4px;
  background-color: rgba(0,0,0,0.6);
  color: white;
  font-size: 10px;
  text-align: center;
}

/* 操作按钮 */
.action-buttons {
  width: 90%;
  margin: 0 auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 14px;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  margin-bottom: 12px;
}

.action-row {
  display: flex;
  gap: 14px;
}

.action-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 15px;
  font-weight: 500;
  background-color: #ffffff;
  color: #000000;
  border: 1px solid #000000;
  transition: all 0.2s ease;
}

/* 重置button标签的默认样式 */
.share-button {
  padding: 0;
  line-height: normal; /* 覆盖button的默认行高 */
  background-color: #ffffff;
  color: #000000;
  font-size: 15px;
  border: 1px solid #000000;
  outline: none;
}

.share-button::after {
  border: none; /* 移除button点击后的边框 */
  outline: none;
}

.action-btn:active {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 衣物信息 */
.clothes-info {
  background-color: #fff;
  padding: 20px;
  margin-top: 12px;
  border-radius: 16px 16px 0 0;
}

.info-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

/* 标签样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 16px;
}

.tag {
  padding: 8px 16px;
  border-radius: 20px;
  background-color: #f5f5f5;
  font-size: 13px;
  color: #333;
  font-weight: 500;
}

/* 详细信息区域 */
.details-section {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 16px;
}

.detail-item {
  flex: 1;
  min-width: 45%;
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 12px;
}

.full-width {
  flex-basis: 100%;
  width: 100%;
}

.detail-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 6px;
}

.detail-value {
  font-size: 15px;
  font-weight: 500;
}

.detail-parsed-item {
  margin-bottom: 8px;
  padding: 4px 0;
  border-bottom: 1px dashed rgba(0,0,0,0.1);
  font-size: 14px;
}

.detail-parsed-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

/* 衣物详情区域 */
.clothes-detail {
  display: none;
  animation: fadeIn 0.3s ease;
}

.clothes-detail.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 重新选择按钮 */
.retry-row {
  width: 90%;
  margin: 12px auto;
}

.retry-btn {
  background: #f5f5f5;
  color: #333;
  border: 1px solid rgba(0,0,0,0.1);
  position: relative;
}

.retry-icon-wrapper {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

/* 免责声明 */
.disclaimer {
  width: 90%;
  margin: 16px auto;
  padding: 12px;
  background-color: #f8f8f8;
  border-radius: 12px;
}

.disclaimer-text {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
}

/* 空状态样式 */
.empty-details-notice {
  text-align: center;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 12px;
  color: #999;
  font-size: 14px;
  width: 100%;
  font-style: italic;
}

/* 加载样式 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-content {
  background-color: #fff;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #000;
  border-radius: 50%;
  margin-bottom: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #333;
}

/* 错误样式 */
.error-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.error-content {
  background-color: #fff;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 80%;
}

.error-icon {
  font-size: 40px;
  margin-bottom: 16px;
}

.error-text {
  font-size: 16px;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.error-btn {
  padding: 10px 20px;
  background-color: #000;
  color: #fff;
  border-radius: 20px;
  font-size: 14px;
}

/* 图片预览弹窗 */
.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.preview-close {
  position: absolute;
  top: 30px;
  right: 30px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
}

/* 水印Canvas */
.watermark-canvas {
  position: fixed;
  top: -9999px;
  left: -9999px;
  width: 300px;
  height: 300px;
  visibility: hidden;
} 