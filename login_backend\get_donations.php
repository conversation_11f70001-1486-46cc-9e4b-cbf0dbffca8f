<?php
/**
 * 获取打赏记录API
 * 
 * 用于获取所有用户的打赏记录
 * 
 * 请求方法：GET
 * 请求参数：
 * - page: 页码
 * - per_page: 每页记录数
 * 
 * 返回：
 * {
 *   "error": false,
 *   "data": [
 *     {
 *       "id": 打赏ID,
 *       "user_id": 用户ID,
 *       "nickname": "用户昵称",
 *       "avatar_url": "用户头像URL",
 *       "amount": 打赏金额,
 *       "created_at": "打赏时间"
 *     },
 *     ...
 *   ],
 *   "pagination": {
 *     "current_page": 当前页码,
 *     "total_pages": 总页数,
 *     "total_items": 总记录数
 *   }
 * }
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'error' => true,
        'msg' => '只支持GET请求'
    ]);
    exit;
}

// 验证用户身份
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供授权Token'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token包含Bearer前缀，去掉它
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    echo json_encode([
        'error' => true,
        'msg' => '无效的授权Token或Token已过期'
    ]);
    exit;
}

// 获取分页参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$perPage = isset($_GET['per_page']) ? intval($_GET['per_page']) : 10;

// 确保页码和每页数量有效
if ($page < 1) $page = 1;
if ($perPage < 1) $perPage = 10;
if ($perPage > 50) $perPage = 50;

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 计算偏移量
    $offset = ($page - 1) * $perPage;
    
    // 查询成功支付的打赏记录
    $stmt = $conn->prepare("
        SELECT 
            id, user_id, nickname, avatar_url, amount, created_at, paid_at
        FROM 
            donations
        WHERE 
            status = 'success'
        ORDER BY 
            paid_at DESC, created_at DESC
        LIMIT :offset, :limit
    ");
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':limit', $perPage, PDO::PARAM_INT);
    $stmt->execute();
    
    $donations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取总记录数
    $countStmt = $conn->query("SELECT COUNT(*) FROM donations WHERE status = 'success'");
    $totalItems = $countStmt->fetchColumn();
    
    // 计算总页数
    $totalPages = ceil($totalItems / $perPage);
    
    // 返回数据
    echo json_encode([
        'error' => false,
        'data' => $donations,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_items' => $totalItems
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '获取打赏记录失败: ' . $e->getMessage()
    ]);
} 