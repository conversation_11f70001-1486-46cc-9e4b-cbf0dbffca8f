<?php
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

try {
    $auth = new Auth();
    
    // 验证用户token
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? null;
    
    if (!$authHeader || strpos($authHeader, 'Bearer ') !== 0) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => '未授权访问']);
        exit;
    }
    
    $token = substr($authHeader, 7);
    $tokenData = $auth->verifyToken($token);
    
    if (!$tokenData) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => 'Token无效']);
        exit;
    }
    
    $userId = $tokenData['user_id'] ?? $tokenData['sub'];
    
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || empty($input['code'])) {
        http_response_code(400);
        echo json_encode(['error' => true, 'msg' => '请求数据无效或缺少code字段']);
        exit;
    }
    
    $categoryCode = $input['code'];
    
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查该分类是否存在于deleted_system_categories表中
    $checkSql = "SELECT * FROM deleted_system_categories WHERE user_id = :user_id AND code = :code";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkStmt->bindParam(':code', $categoryCode, PDO::PARAM_STR);
    $checkStmt->execute();
    
    if ($checkStmt->rowCount() === 0) {
        http_response_code(404);
        echo json_encode(['error' => true, 'msg' => '找不到该删除记录']);
        exit;
    }
    
    // 获取系统分类名称和信息
    $getSysCategorySql = "SELECT * FROM clothing_categories WHERE code = :code AND is_system = 1 LIMIT 1";
    $getSysCategoryStmt = $conn->prepare($getSysCategorySql);
    $getSysCategoryStmt->bindParam(':code', $categoryCode, PDO::PARAM_STR);
    $getSysCategoryStmt->execute();
    $sysCategory = $getSysCategoryStmt->fetch(PDO::FETCH_ASSOC);
    
    // 检查该分类是否已被用户恢复（是否已存在于用户的clothing_categories中）
    $checkExistSql = "SELECT * FROM clothing_categories WHERE user_id = :user_id AND code = :code";
    $checkExistStmt = $conn->prepare($checkExistSql);
    $checkExistStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkExistStmt->bindParam(':code', $categoryCode, PDO::PARAM_STR);
    $checkExistStmt->execute();
    
    if ($checkExistStmt->rowCount() > 0) {
        // 分类已存在于用户的clothing_categories中，直接删除deleted_system_categories记录
        $deleteRecordSql = "DELETE FROM deleted_system_categories WHERE user_id = :user_id AND code = :code";
        $deleteRecordStmt = $conn->prepare($deleteRecordSql);
        $deleteRecordStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $deleteRecordStmt->bindParam(':code', $categoryCode, PDO::PARAM_STR);
        $deleteRecordStmt->execute();
        
        echo json_encode([
            'error' => false,
            'msg' => '分类已恢复',
            'data' => ['already_exists' => true]
        ]);
        exit;
    }
    
    // 开始事务
    $conn->beginTransaction();
    
    try {
        // 创建分类记录
        $name = $sysCategory ? $sysCategory['name'] : '';
        $sort_order = $sysCategory ? $sysCategory['sort_order'] : 0;
        $is_system = $sysCategory ? 1 : 0;
        
        // 如果没有找到分类名称，尝试获取可能的名称
        if (empty($name)) {
            // 先检查是否是自定义分类格式
            if (strpos($categoryCode, 'custom_') === 0) {
                $name = '恢复的自定义分类';
                $is_system = 0;
            } else {
                // 系统预设分类转换
                switch ($categoryCode) {
                    case 'tops':
                        $name = '上衣';
                        $is_system = 1;
                        break;
                    case 'pants':
                        $name = '裤子';
                        $is_system = 1;
                        break;
                    case 'skirts':
                        $name = '裙子';
                        $is_system = 1;
                        break;
                    case 'coats':
                        $name = '外套';
                        $is_system = 1;
                        break;
                    case 'shoes':
                        $name = '鞋子';
                        $is_system = 1;
                        break;
                    case 'bags':
                        $name = '包包';
                        $is_system = 1;
                        break;
                    case 'accessories':
                        $name = '配饰';
                        $is_system = 1;
                        break;
                    default:
                        $name = '恢复的分类';
                        $is_system = 0;
                }
            }
        }

        // 记录恢复前的日志
        error_log("开始恢复分类: 用户ID=$userId, 分类代码=$categoryCode, 名称=$name, 系统分类=$is_system");
        
        // 插入分类到用户的clothing_categories
        $insertSql = "INSERT INTO clothing_categories (user_id, name, code, is_system, sort_order) 
                     VALUES (:user_id, :name, :code, :is_system, :sort_order)";
        
        try {
            $insertStmt = $conn->prepare($insertSql);
            $insertStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $insertStmt->bindParam(':name', $name, PDO::PARAM_STR);
            $insertStmt->bindParam(':code', $categoryCode, PDO::PARAM_STR);
            $insertStmt->bindParam(':is_system', $is_system, PDO::PARAM_INT);
            $insertStmt->bindParam(':sort_order', $sort_order, PDO::PARAM_INT);
            $insertStmt->execute();
            
            $newCategoryId = $conn->lastInsertId();
            error_log("分类插入成功：新ID=$newCategoryId");
        } catch (Exception $insertEx) {
            error_log("分类插入失败：" . $insertEx->getMessage());
            throw $insertEx;
        }
        
        // 从deleted_system_categories表中删除记录
        $deleteRecordSql = "DELETE FROM deleted_system_categories WHERE user_id = :user_id AND code = :code";
        
        try {
            $deleteRecordStmt = $conn->prepare($deleteRecordSql);
            $deleteRecordStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $deleteRecordStmt->bindParam(':code', $categoryCode, PDO::PARAM_STR);
            $deleteRecordStmt->execute();
            
            $deletedCount = $deleteRecordStmt->rowCount();
            error_log("删除垃圾箱记录数量：$deletedCount");
        } catch (Exception $deleteEx) {
            error_log("删除垃圾箱记录失败：" . $deleteEx->getMessage());
            throw $deleteEx;
        }
        
        // 提交事务
        $conn->commit();
        
        // 记录调试日志
        error_log("成功恢复分类: " . $name . ", 代码: " . $categoryCode . ", 是否系统分类: " . $is_system);
        
        echo json_encode([
            'error' => false,
            'msg' => '分类已恢复',
            'data' => [
                'id' => $newCategoryId,
                'name' => $name,
                'code' => $categoryCode,
                'is_system' => $is_system == 1
            ]
        ]);
        
    } catch (Exception $innerEx) {
        $conn->rollBack();
        throw $innerEx;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '服务器错误: ' . $e->getMessage()
    ]);
} 