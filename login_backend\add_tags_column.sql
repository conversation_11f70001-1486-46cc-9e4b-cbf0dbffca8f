-- 向taobao_products表添加tags字段的SQL脚本
-- 执行此脚本可为现有数据库添加商品标签字段

-- 开始事务
START TRANSACTION;

-- 检查是否已存在tags字段，如果不存在则添加
SET @exist := (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_SCHEMA = DATABASE() 
               AND TABLE_NAME = 'taobao_products' 
               AND COLUMN_NAME = 'tags');

SET @query = IF(@exist = 0, 
                'ALTER TABLE `taobao_products` ADD COLUMN `tags` TEXT COMMENT "商品标签(JSON)" AFTER `category`', 
                'SELECT "标签字段已存在，无需添加" AS message');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 提交事务
COMMIT;

-- 显示表结构
DESCRIBE taobao_products;

-- 执行方法：
-- 1. 登录MySQL数据库
-- 2. 选择对应的数据库：USE your_database_name;
-- 3. 执行此脚本：SOURCE /path/to/add_tags_column.sql;
-- 或者通过phpMyAdmin等工具导入执行 