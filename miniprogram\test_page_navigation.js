// 测试页面导航功能
// 在开发者工具的控制台中运行此代码来测试页面跳转

// 测试跳转到穿搭详情页
function testNavigateToOutfitDetail() {
  console.log('测试跳转到穿搭详情页...');
  
  wx.navigateTo({
    url: '/pages/outfits/detail/detail?id=1',
    success: () => {
      console.log('✅ 跳转成功');
    },
    fail: (err) => {
      console.error('❌ 跳转失败:', err);
    }
  });
}

// 测试跳转到穿搭编辑页
function testNavigateToOutfitEdit() {
  console.log('测试跳转到穿搭编辑页...');
  
  wx.navigateTo({
    url: '/pages/outfits/edit/edit',
    success: () => {
      console.log('✅ 跳转成功');
    },
    fail: (err) => {
      console.error('❌ 跳转失败:', err);
    }
  });
}

// 检查页面是否在app.json中注册
function checkPageRegistration() {
  console.log('检查页面注册状态...');
  
  const pages = [
    'pages/outfits/detail/detail',
    'pages/outfits/edit/edit',
    'pages/outfits/index/index',
    'pages/outfits/add/add'
  ];
  
  pages.forEach(page => {
    try {
      // 尝试获取页面信息
      const currentPages = getCurrentPages();
      console.log(`页面 ${page}: 可能已注册`);
    } catch (err) {
      console.error(`页面 ${page}: 注册失败`, err);
    }
  });
}

// 导出测试函数
module.exports = {
  testNavigateToOutfitDetail,
  testNavigateToOutfitEdit,
  checkPageRegistration
};

// 如果在控制台中运行，可以直接调用
if (typeof console !== 'undefined') {
  console.log('页面导航测试工具已加载');
  console.log('可用函数:');
  console.log('- testNavigateToOutfitDetail(): 测试跳转到穿搭详情页');
  console.log('- testNavigateToOutfitEdit(): 测试跳转到穿搭编辑页');
  console.log('- checkPageRegistration(): 检查页面注册状态');
}
