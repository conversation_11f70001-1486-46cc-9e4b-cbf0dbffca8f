<?php
/**
 * 更新衣物标签 API
 * 
 * 检测用户没有标签的衣物，调用分析接口为其生成标签
 * 
 * 请求方法: GET
 * 
 * 响应:
 * {
 *   "success": true,
 *   "data": {
 *     "total_processed": 5,
 *     "updated_count": 3,
 *     "skipped_count": 2,
 *     "updated_items": [
 *       {"id": 1, "name": "白T恤", "added_tags": ["休闲", "基础款", "百搭"]}
 *     ]
 *   }
 * }
 */

// 增加执行时间至5分钟
set_time_limit(300);
ini_set('max_execution_time', 300);

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 清洗标签函数，确保标签格式统一
function cleanTags($tags) {
    if (is_array($tags)) {
        // 如果是数组，确保每个元素都是纯文本
        $cleanedTags = [];
        foreach ($tags as $tag) {
            if (is_array($tag)) {
                // 递归处理嵌套数组
                $subTags = cleanTags($tag);
                $cleanedTags = array_merge($cleanedTags, $subTags);
                continue;
            }
            
            $tag = trim($tag);
            if (!empty($tag)) {
                // 排除任何可能的JSON格式或包含特殊字符的标签
                if (strpos($tag, '[') === false && strpos($tag, ']') === false && 
                    strpos($tag, '{') === false && strpos($tag, '}') === false &&
                    strpos($tag, '"') === false && strpos($tag, "'") === false) {
                    $cleanedTags[] = $tag;
                } else {
                    // 尝试清理JSON格式标签
                    $tag = str_replace(['[', ']', '"', "'", '{', '}'], '', $tag);
                    if (!empty(trim($tag))) {
                        $cleanedTags[] = trim($tag);
                    }
                }
            }
        }
        return $cleanedTags;
    } elseif (is_string($tags)) {
        // 如果是字符串，先尝试解析JSON
        $firstChar = substr($tags, 0, 1);
        if ($firstChar === '[' || $firstChar === '{') {
            try {
                $jsonTags = json_decode($tags, true);
                if (is_array($jsonTags)) {
                    return cleanTags($jsonTags);
                }
            } catch (Exception $e) {
                // JSON解析失败，当作普通字符串继续处理
            }
        }
        
        // 移除可能的JSON数组部分（处理混合格式）
        // 例如: ["标签1","标签2"],标签3,标签4
        $cleanedStr = preg_replace('/\[.*?\]/', '', $tags);
        
        // 分割成数组并清理每个标签
        $tagArray = explode(',', $cleanedStr);
        $cleanedArray = [];
        
        foreach ($tagArray as $tag) {
            $tag = trim($tag);
            // 再次清理可能的JSON字符
            $tag = str_replace(['[', ']', '"', "'", '{', '}'], '', $tag);
            if (!empty($tag)) {
                $cleanedArray[] = $tag;
            }
        }
        
        return $cleanedArray;
    }
    
    return [];
}

// 检查是否是进度查询请求
if (isset($_GET['check_progress'])) {
    // 设置返回内容类型
    header('Content-Type: application/json');
    
    // 处理CORS
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    
    // 检查Authorization头
    if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
        echo json_encode([
            'success' => false,
            'msg' => '缺少授权信息'
        ]);
        exit;
    }
    
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    $auth = new Auth();
    $tokenData = $auth->verifyToken($token);
    
    if (!$tokenData) {
        echo json_encode([
            'success' => false,
            'msg' => '无效或已过期的授权信息'
        ]);
        exit;
    }
    
    // 获取用户ID
    $userId = $tokenData['sub'];
    
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        // 查询当前进度
        $stmt = $conn->prepare("
            SELECT * FROM processing_tasks 
            WHERE user_id = :user_id AND task_type = 'update_clothing_tags'
            ORDER BY created_at DESC LIMIT 1
        ");
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        $task = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($task) {
            echo json_encode([
                'success' => true,
                'data' => [
                    'total_items' => $task['total_items'],
                    'processed_items' => $task['processed_items'],
                    'status' => $task['status'],
                    'progress' => $task['total_items'] > 0 ? 
                        round(($task['processed_items'] / $task['total_items']) * 100) : 0
                ]
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'msg' => '没有找到处理中的任务'
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'msg' => '查询进度时出错: ' . $e->getMessage()
        ]);
    }
    
    exit;
}

// 设置返回内容类型
header('Content-Type: application/json');

// 处理CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 对于OPTIONS请求，直接返回
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'success' => false,
        'msg' => '缺少授权信息'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
$auth = new Auth();
$tokenData = $auth->verifyToken($token);

if (!$tokenData) {
    echo json_encode([
        'success' => false,
        'msg' => '无效或已过期的授权信息'
    ]);
    exit;
}

// 获取用户ID
$userId = $tokenData['sub'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 获取用户所有没有标签或标签不完善的衣物
    $stmt = $conn->prepare("
        SELECT id, name, category, image_url, tags 
        FROM clothes 
        WHERE user_id = :user_id AND (
            tags IS NULL 
            OR tags = '' 
            OR (
                LENGTH(tags) - LENGTH(REPLACE(tags, ',', '')) < 6 
                /* 如果逗号数量少于6，意味着标签数量少于7，认为标签不完善 */
            )
        )
        LIMIT 50 /* 增加一次处理的上限，可根据服务器情况调整 */
    ");
    $stmt->bindParam(':user_id', $userId);
    $stmt->execute();
    
    $clothesWithoutTags = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $totalCount = count($clothesWithoutTags);
    
    if ($totalCount == 0) {
        echo json_encode([
            'success' => true,
            'data' => [
                'total_processed' => 0,
                'updated_count' => 0,
                'skipped_count' => 0,
                'message' => '没有找到需要更新标签的衣物'
            ]
        ]);
        exit;
    }
    
    // 创建处理任务记录
    $taskStmt = $conn->prepare("
        INSERT INTO processing_tasks 
        (user_id, task_type, total_items, processed_items, status, created_at, updated_at)
        VALUES (:user_id, 'update_clothing_tags', :total_items, 0, 'processing', NOW(), NOW())
    ");
    $taskStmt->bindParam(':user_id', $userId);
    $taskStmt->bindParam(':total_items', $totalCount);
    $taskStmt->execute();
    $taskId = $conn->lastInsertId();
    
    $updatedCount = 0;
    $skippedCount = 0;
    $updatedItems = [];
    
    // 遍历每件衣物，调用分析接口生成标签
    foreach ($clothesWithoutTags as $index => $clothing) {
        $imageUrl = $clothing['image_url'];
        
        // 更新处理进度
        $processedCount = $index + 1;
        $updateTaskStmt = $conn->prepare("
            UPDATE processing_tasks 
            SET processed_items = :processed_items, 
                updated_at = NOW() 
            WHERE id = :task_id
        ");
        $updateTaskStmt->bindParam(':processed_items', $processedCount);
        $updateTaskStmt->bindParam(':task_id', $taskId);
        $updateTaskStmt->execute();
        
        // 下载图片并转换为base64
        $imageBase64 = null;
        $errorMsg = null;
        
        try {
            // 下载图片
            $imageData = file_get_contents($imageUrl);
            if ($imageData === false) {
                throw new Exception("无法下载图片: $imageUrl");
            }
            
            // 转换为base64
            $imageBase64 = base64_encode($imageData);
            
            // 构建分析提示词，优化为适合穿搭推荐的标签
            $prompt = "分析图片中的衣物，返回详细的穿搭相关信息，必须严格按照以下JSON格式返回结果，不要有任何额外的文本：\n" .
                "{\n" .
                '  "衣物类别": "上衣、裤子、裙子、外套、鞋子、包包、配饰中的一个",\n' .
                '  "衣物标签": [\n' . 
                '    "季节标签(如：春季/夏季/秋季/冬季)",\n' . 
                '    "场合标签(如：休闲/通勤/派对/运动/正式)",\n' . 
                '    "风格标签(如：简约/复古/优雅/街头/学院)",\n' . 
                '    "设计特点(如：修身/宽松/短款/长款/高腰/格纹/纯色)",\n' . 
                '    "搭配建议(如：百搭/适合搭配牛仔裤/适合内搭/适合叠穿)",\n' . 
                '    "适合天气标签(必须生成：晴天/雨天/阴天/多云/闷热/寒冷/微凉中的至少一个)",\n' . 
                '    "其他特点(如：经典款/必备单品/亮点单品)"\n' . 
                '  ],\n' .
                '  "衣物信息": {\n' .
                '    "衣物名称": "具体名称，如白色T恤、牛仔外套等",\n' .
                '    "颜色": "具体颜色",\n' .
                '    "材质": "面料材质",\n' .
                '    "适合体型": "适合什么体型穿着"\n' .
                '  },\n' .
                '  "穿搭建议": ["可以和什么单品搭配", "适合什么风格穿搭"]\n' .
                "}";
            
            // 调用分析接口
            $ch = curl_init(GEMINI_API_PROXY_URL);
            curl_setopt_array($ch, [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => [
                    'image_base64' => $imageBase64,
                    'prompt' => $prompt
                ],
                CURLOPT_TIMEOUT => 30
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode !== 200) {
                throw new Exception("API请求失败，状态码: $httpCode");
            }
            
            // 解析响应
            $result = json_decode($response, true);
            
            // 提取分析结果
            $textContent = null;
            if (isset($result['contents'][0]['parts'][0]['text'])) {
                $textContent = $result['contents'][0]['parts'][0]['text'];
            } else if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
                $textContent = $result['candidates'][0]['content']['parts'][0]['text'];
            }
            
            if (!$textContent) {
                throw new Exception("分析结果格式不正确");
            }
            
            // 从文本中提取JSON
            if (preg_match('/{.*}/s', $textContent, $matches)) {
                $jsonString = $matches[0];
                $jsonContent = json_decode($jsonString, true);
                
                if (!$jsonContent) {
                    throw new Exception("JSON解析失败: " . json_last_error_msg());
                }
                
                // 提取标签和衣物信息
                $tags = [];
                
                if (isset($jsonContent['衣物标签']) && is_array($jsonContent['衣物标签'])) {
                    foreach ($jsonContent['衣物标签'] as $tag) {
                        if (!empty($tag)) {
                            $tags[] = $tag;
                        }
                    }
                }
                
                // 添加穿搭建议作为标签
                if (isset($jsonContent['穿搭建议']) && is_array($jsonContent['穿搭建议'])) {
                    foreach ($jsonContent['穿搭建议'] as $suggestion) {
                        if (!empty($suggestion)) {
                            $tags[] = $suggestion;
                        }
                    }
                }
                
                // 清洗所有新生成的标签
                $tags = cleanTags($tags);
                
                // 确保标签不为空
                if (empty($tags)) {
                    throw new Exception("未能生成有效标签");
                }
                
                // 如果衣物已有标签，合并现有标签和新标签
                $existingTags = [];
                if (isset($clothing['tags']) && !empty($clothing['tags'])) {
                    // 清洗现有标签
                    $existingTags = cleanTags($clothing['tags']);
                }
                
                // 合并标签并去重
                if (!empty($existingTags)) {
                    $allTags = array_merge($existingTags, $tags);
                    $uniqueTags = array_unique($allTags);
                    $tags = array_values($uniqueTags); // 重新索引数组
                }
                
                // 重新清洗一次，确保没有问题
                $tags = cleanTags($tags);
                
                // 构建描述信息
                $description = [];
                if (isset($jsonContent['衣物信息'])) {
                    $info = $jsonContent['衣物信息'];
                    
                    // 更新衣物名称(如果当前名称为空或默认名称)
                    $name = isset($info['衣物名称']) ? $info['衣物名称'] : null;
                    
                    if (isset($info['颜色'])) {
                        $description['color'] = $info['颜色'];
                    }
                    if (isset($info['材质'])) {
                        $description['material'] = $info['材质'];
                    }
                    if (isset($info['适合体型'])) {
                        $description['body_type'] = $info['适合体型'];
                    }
                }
                
                // 准备更新的数据
                // 确保没有JSON数组或其他特殊格式
                $tagsString = implode(',', $tags);
                
                // 验证一下tagsString是否包含"["或"]"，如果有则再次清理
                if (strpos($tagsString, '[') !== false || strpos($tagsString, ']') !== false) {
                    $tags = cleanTags($tagsString);
                    $tagsString = implode(',', $tags);
                }
                
                $descriptionJson = !empty($description) ? json_encode($description, JSON_UNESCAPED_UNICODE) : null;
                
                // 更新数据库
                $updateStmt = $conn->prepare("
                    UPDATE clothes 
                    SET tags = :tags, 
                        description = CASE 
                            WHEN description IS NULL OR description = '' THEN :description 
                            ELSE description 
                        END,
                        name = CASE 
                            WHEN (name IS NULL OR name = '' OR name = '未命名衣物') AND :name IS NOT NULL THEN :name 
                            ELSE name 
                        END,
                        updated_at = NOW()
                    WHERE id = :id AND user_id = :user_id
                ");
                
                $updateStmt->bindParam(':tags', $tagsString);
                $updateStmt->bindParam(':description', $descriptionJson);
                $updateStmt->bindParam(':name', $name);
                $updateStmt->bindParam(':id', $clothing['id']);
                $updateStmt->bindParam(':user_id', $userId);
                $updateStmt->execute();
                
                // 记录更新结果
                $updatedCount++;
                $updatedItems[] = [
                    'id' => $clothing['id'],
                    'name' => $name ?: $clothing['name'],
                    'added_tags' => $tags,
                    'had_existing_tags' => !empty($existingTags)
                ];
                
            } else {
                throw new Exception("未能从分析结果中提取JSON数据");
            }
            
        } catch (Exception $e) {
            // 记录错误但继续处理其他衣物
            error_log("分析衣物ID {$clothing['id']}失败: " . $e->getMessage());
            $skippedCount++;
        }
    }
    
    // 统计已有标签但进行了更新的衣物
    $enhancedCount = 0;
    foreach ($updatedItems as $item) {
        if ($item['had_existing_tags']) {
            $enhancedCount++;
        }
    }
    
    // 清理所有标签格式（批量处理）
    $cleanedTagsCount = 0;
    $cleanupCount = 0;
    $validTagsCount = 0;
    
    try {
        // 步骤1: 格式清理 - 获取该用户所有非空标签的衣物（排除刚刚已处理过的）
        $idsToExclude = !empty($updatedItems) ? implode(',', array_column($updatedItems, 'id')) : '0';
        $cleanStmt = $conn->prepare("
            SELECT id, tags FROM clothes 
            WHERE user_id = :user_id AND tags IS NOT NULL AND tags != '' 
            AND id NOT IN ({$idsToExclude})
        ");
        $cleanStmt->bindParam(':user_id', $userId);
        $cleanStmt->execute();
        $clothesToClean = $cleanStmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($clothesToClean as $clothing) {
            $originalTags = $clothing['tags'];
            $cleanedTags = cleanTags($originalTags);
            
            // 只有当清理后的标签与原标签不同时才更新
            if ($cleanedTags) {
                $tagsString = implode(',', $cleanedTags);
                
                if ($tagsString !== $originalTags) {
                    $updateStmt = $conn->prepare("UPDATE clothes SET tags = :tags, updated_at = NOW() WHERE id = :id");
                    $updateStmt->bindParam(':tags', $tagsString);
                    $updateStmt->bindParam(':id', $clothing['id']);
                    $updateStmt->execute();
                    $cleanedTagsCount++;
                }
            }
        }
        
        // 步骤2: 无效标签清理 - 获取用户所有衣物的标签
        $validTagsStmt = $conn->prepare("
            SELECT DISTINCT tags FROM clothes 
            WHERE user_id = :user_id AND tags IS NOT NULL AND tags != ''
        ");
        $validTagsStmt->bindParam(':user_id', $userId);
        $validTagsStmt->execute();
        
        $validTagsRows = $validTagsStmt->fetchAll(PDO::FETCH_COLUMN);
        $allValidTags = [];
        
        // 收集所有有效标签
        foreach ($validTagsRows as $tagsString) {
            $tagArray = explode(',', $tagsString);
            foreach ($tagArray as $tag) {
                $tag = trim($tag);
                if (!empty($tag)) {
                    $allValidTags[$tag] = true;
                }
            }
        }
        
        $validTagsCount = count($allValidTags);
        
        // 更新所有衣物，移除无效标签
        if ($validTagsCount > 0) {
            $allClothesStmt = $conn->prepare("
                SELECT id, tags FROM clothes 
                WHERE user_id = :user_id AND tags IS NOT NULL AND tags != ''
            ");
            $allClothesStmt->bindParam(':user_id', $userId);
            $allClothesStmt->execute();
            
            $allClothes = $allClothesStmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($allClothes as $cloth) {
                $currentTags = explode(',', $cloth['tags']);
                $validClothTags = [];
                $hasInvalidTags = false;
                
                foreach ($currentTags as $tag) {
                    $tag = trim($tag);
                    if (!empty($tag) && isset($allValidTags[$tag])) {
                        $validClothTags[] = $tag;
                    } else if (!empty($tag)) {
                        $hasInvalidTags = true;
                    }
                }
                
                // 如果有无效标签，更新衣物
                if ($hasInvalidTags) {
                    $newTagsString = implode(',', $validClothTags);
                    $updateClothStmt = $conn->prepare("
                        UPDATE clothes SET tags = :tags, updated_at = NOW()
                        WHERE id = :id AND user_id = :user_id
                    ");
                    $updateClothStmt->bindParam(':tags', $newTagsString);
                    $updateClothStmt->bindParam(':id', $cloth['id']);
                    $updateClothStmt->bindParam(':user_id', $userId);
                    $updateClothStmt->execute();
                    $cleanupCount++;
                }
            }
        }
        
    } catch (Exception $e) {
        // 记录错误，但不影响主流程
        error_log("清理标签时出错: " . $e->getMessage());
    }
    
    // 返回处理结果
    echo json_encode([
        'success' => true,
        'data' => [
            'total_processed' => $totalCount,
            'updated_count' => $updatedCount,
            'skipped_count' => $skippedCount,
            'updated_items' => $updatedItems,
            'enhanced_count' => $enhancedCount,
            'tag_cleanup' => [
                'format_cleaned_count' => $cleanedTagsCount,
                'invalid_cleaned_count' => $cleanupCount,
                'valid_tags_count' => $validTagsCount
            ]
        ]
    ]);
    
    // 更新任务状态为完成
    $completeTaskStmt = $conn->prepare("
        UPDATE processing_tasks 
        SET status = 'completed', 
            updated_at = NOW() 
        WHERE id = :task_id
    ");
    $completeTaskStmt->bindParam(':task_id', $taskId);
    $completeTaskStmt->execute();
    
} catch (Exception $e) {
    // 更新任务状态为失败
    if (isset($taskId)) {
        try {
            $failTaskStmt = $conn->prepare("
                UPDATE processing_tasks 
                SET status = 'failed', 
                    error_message = :error_message,
                    updated_at = NOW() 
                WHERE id = :task_id
            ");
            $errorMessage = $e->getMessage();
            $failTaskStmt->bindParam(':error_message', $errorMessage);
            $failTaskStmt->bindParam(':task_id', $taskId);
            $failTaskStmt->execute();
        } catch (Exception $ex) {
            // 忽略更新失败的异常
        }
    }
    
    // 记录错误，但不影响主流程
    error_log("更新衣物标签时出错: " . $e->getMessage());
    
    // 使用原始返回数据
    $returnData = [
        'total_processed' => $totalCount,
        'updated_count' => $updatedCount,
        'skipped_count' => $skippedCount,
        'updated_items' => $updatedItems
    ];
    
    echo json_encode([
        'success' => false,
        'data' => $returnData
    ]);
} 