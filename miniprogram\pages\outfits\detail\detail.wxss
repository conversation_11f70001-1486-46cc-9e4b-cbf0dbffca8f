/* pages/outfits/detail/detail.wxss */

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #ffffff;
  position: relative;
  overflow: hidden;
  padding-bottom: 80px; /* 为固定底部按钮留出空间 */
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #000000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 穿搭画布 - 优化占比 */
.outfit-canvas {
  width: 100%;
  flex: 1;
  min-height: 400px; /* 确保有最小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  position: relative;
  overflow: hidden;
}

.outfit-view {
  width: 100%;
  height: 100%;
  min-height: 400px; /* 确保有最小高度 */
  max-height: calc(100vw * 1.33); /* 保持与编辑页面一致的宽高比 */
  background-color: #ffffff;
  position: relative;
  overflow: hidden;
  border-radius: 8px; /* 添加圆角 */
  box-shadow: 0 2px 8px rgba(0,0,0,0.1); /* 添加阴影 */
}

/* 下载按钮 */
.download-btn {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
}

.download-btn:active {
  transform: scale(0.95);
  background-color: rgba(0, 0, 0, 0.9);
}

.download-icon {
  width: 40rpx;
  height: 40rpx;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7,10 12,15 17,10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.outfit-item {
  position: absolute;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-image {
  width: 95%;
  height: 95%;
  object-fit: contain;
}

/* 空画布状态 */
.empty-canvas {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
  color: #999999;
  width: 80%;
}

/* 穿搭信息 - 更加紧凑 */
.outfit-info {
  padding: 12px 20px;
  padding-bottom: 20px; /* 增加底部间距，避免被固定按钮遮挡 */
  background-color: #ffffff;
  flex-shrink: 0;
  border-top: 1px solid #f0f0f0;
  margin-bottom: 20px; /* 额外的底部边距 */
}

.outfit-name {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.outfit-description {
  font-size: 14px;
  color: #666666;
  margin-bottom: 16px;
  line-height: 1.5;
}

/* 衣物列表 - 更加紧凑美观 */
.outfit-items {
  margin: 0;
}

/* 衣物标题和开关容器 */
.items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.items-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.public-switch-container {
  display: flex;
  align-items: center;
}

.switch-label {
  font-size: 14px;
  margin-right: 8px;
  color: #333;
}

/* 确保开关组件适应黑白主题 */
switch {
  transform: scale(0.8);
}

.items-scroll {
  width: 100%;
  white-space: nowrap;
}

.items-container {
  display: inline-flex;
  padding: 2px 0;
}

.item-preview {
  width: 60px;
  margin-right: 8px;
  display: inline-block;
  cursor: pointer;
  transition: transform 0.2s ease, opacity 0.2s ease;
  border-radius: 6px;
  overflow: hidden;
}

.item-preview-hover {
  transform: scale(0.95);
  opacity: 0.8;
}

.preview-image {
  width: 60px;
  height: 80px;
  background-color: #f5f5f5;
  border-radius: 6px;
  object-fit: contain;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.preview-name {
  font-size: 12px;
  color: #333333;
  margin-top: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}

/* 底部操作按钮 - 固定在底部 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  display: flex;
  padding: 12px 20px;
  padding-bottom: calc(12px + env(safe-area-inset-bottom)); /* 适配安全区域 */
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
  align-items: center;
  justify-content: space-between;
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
}

.action-btn {
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  margin: 0 8px;
  padding: 0 24px;
  min-width: 100px;
  transition: opacity 0.2s;
}

.action-btn:active {
  opacity: 0.8;
}

.edit-btn {
  background-color: #000000;
  color: #ffffff;
}

.delete-btn {
  background-color: #f5f5f5;
  color: #ff3b30;
}

/* 添加"我也要穿搭"按钮样式 */
.want-btn {
  background-color: #000000;
  color: #ffffff;
  flex: 1;
  text-align: center;
  font-weight: 500;
}

.want-btn:active {
  background-color: #333333;
} 

/* 点赞按钮样式 */
.action-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.like-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 30rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  transition: all 0.3s;
}

.like-btn.liked {
  background-color: #ffecec;
}

.like-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.like-btn-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.like-btn-count {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
} 

/* 创建者信息样式 */
.creator-info {
  display: flex;
  align-items: center;
  padding-left: 20rpx;
  flex: 1;
  position: relative;
}

.creator-info:active {
  opacity: 0.7;
}

.creator-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 12rpx;
  border: 1px solid #f0f0f0;
}

.creator-name {
  font-size: 26rpx;
  color: #333333;
  max-width: 200rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.view-more-icon {
  margin-left: 8rpx;
  font-size: 32rpx;
  color: #999;
  font-weight: bold;
}

/* 隐藏的canvas */
.hidden-canvas {
  position: fixed;
  top: -9999px;
  left: -9999px;
  visibility: hidden;
}

/* wxml2canvas相关样式 */
.hidden-template {
  position: fixed;
  top: -9999px;
  left: -9999px;
  visibility: hidden;
  background-color: #ffffff;
}

.wxml2canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.outfit-item-template {
  position: absolute;
}

.item-image-template {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 创建者操作按钮容器 - 确保居中 */
.creator-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px; /* 按钮之间的间距 */
}

/* 穿搭广场模式 - 左右分布 */
.bottom-actions.from-square {
  justify-content: flex-start;
  gap: 20rpx; /* 用户信息和点赞按钮之间的间距 */
}

.bottom-actions.from-square .creator-info {
  flex: none; /* 取消flex: 1，让用户信息不占据所有空间 */
  width: auto;
}

.bottom-actions.from-square .action-container {
  margin-left: auto; /* 让点赞按钮靠右，但不贴边 */
  margin-right: 60rpx; /* 右边距 */
}