.container {
  padding: 0;
  background-color: #f8f8f8;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 120rpx; /* 为固定底部按钮留出空间 */
}

/* 未登录状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  height: 400rpx;
  margin-top: 100rpx;
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background-color:rgb(255, 255, 255);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  color: #aaa;
  font-size: 36px;
}

.empty-image {
  width: 60px;
  height: 60px;
  object-fit: contain;
  display: block;
  margin: 0 auto;
}

.empty-text {
  font-size: 16px;
  color: #888;
  margin-bottom: 10px;
  text-align: center;
}

.login-btn {
  width: 200px;
  height: 44px;
  background-color: #000;
  color: #fff;
  border-radius: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  margin-top: 20px;
  font-size: 16px;
}

/* 已登录状态 - 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 0 50rpx;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #333;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 已登录状态 - 已加载 */
/* 天气卡片 */
.weather-card {
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  margin: 30rpx;
  padding: 30rpx;
}

/* 天气位置样式 */
.weather-location {
  margin-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-text {
  font-size: 28rpx;
  color: #999;
  display: flex;
  align-items: center;
}

/* 定位图标样式 */
.location-icon {
  font-size: 28rpx;
  color: #333;
  margin-left: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #f8f8f8;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

/* 定位按钮样式 - 保留但不使用 */
.location-btn {
  background-color: #f5f5f5;
  color: #666;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  margin: 0 0 0 20rpx;
  line-height: 1.6;
  min-height: auto;
}

.weather-main {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.weather-icon {
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.weather-icon-img {
  width: 100rpx;
  height: 100rpx;
}

/* 文字图标样式 */
.weather-text-icon {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  background-color: #f5f5f5;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  text-align: center;
  line-height: 1.2;
}

.weather-info {
  flex: 1;
}

.weather-temp {
  font-size: 50rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
  color: #333;
}

.weather-text {
  font-size: 28rpx;
  color: #666;
}

/* 天气详情四列布局 */
.weather-details {
  display: flex;
  justify-content: space-between;
  padding-top: 20rpx;
  flex-wrap: nowrap;
}

.weather-detail-item {
  flex: 1;
  text-align: center;
  padding: 0 10rpx;
}

.weather-detail-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.weather-detail-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 穿搭卡片 */
.outfit-card {
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  margin: 0 30rpx 30rpx;
  padding: 30rpx;
  overflow-y: visible;
  height: auto;
}

/* 标题容器 */
.outfit-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.outfit-title-left {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  text-align: left;
}

.outfit-title-right {
  display: flex;
  align-items: center;
}

/* 紧凑型天气信息 */
.weather-info-compact {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.weather-city {
  max-width: 180rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 20rpx;
}

.weather-temp-text {
  font-weight: 500;
  margin-right: 10rpx;
}

.weather-condition {
  margin-right: 10rpx;
}

.location-icon-small {
  font-size: 24rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background-color: #f8f8f8;
}

/* 原有标题样式保留，但不再使用 */
.outfit-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.outfit-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.outfit-item:last-child {
  border-bottom: none;
}

.outfit-item-image-container {
  width: 140rpx;
  height: 140rpx;
  background-color: #f9f9f9;
  border-radius: 10rpx;
  margin-right: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.outfit-item-image {
  max-width: 120rpx;
  max-height: 120rpx;
}

.outfit-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.outfit-item-category {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.outfit-item-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.outfit-item-desc {
  font-size: 24rpx;
  color: #666;
}

/* 推荐理由样式 */
.outfit-item-reason {
  font-size: 24rpx;
  color: #4a6582;
  margin-top: 8rpx;
  line-height: 1.4;
  background-color: rgba(74, 101, 130, 0.05);
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin: 0 15rpx;
}

.refresh-btn {
  background-color: #fff;
  color: #333;
  border: 1rpx solid #ddd;
}

.refresh-btn.refreshing {
  opacity: 0.7;
}

.save-btn {
  background-color: #000;
  color: #fff;
  border: 1rpx solid #000;
}

.save-btn:active {
  opacity: 0.8;
}

/* 提示文本 */
.recommendation-tips {
  padding: 20rpx 50rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

/* 体验账号提示 */
.mock-user-banner {
  position: fixed;
  bottom: 180rpx;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 30rpx;
  color: #fff;
  z-index: 100;
}

.mock-banner-text {
  font-size: 24rpx;
  flex: 1;
}

.mock-login-btn {
  background-color: #fff;
  color: #333;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  margin-left: 20rpx;
}

/* 分享提示弹窗样式 */
.share-tip-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.share-tip-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.share-tip-content {
  position: relative;
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 50rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

.share-tip-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.share-tip-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.share-tip-message {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
  line-height: 1.6;
}

.share-tip-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.share-tip-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0 15rpx;
  padding: 0;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.share-btn {
  background-color: #000;
  color: #fff;
}

/* 工具类 */
.text-sm {
  font-size: 14px;
}

/* 穿搭总结样式 */
.outfit-summary {
  margin-top: 20rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.summary-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
}

.summary-title::before {
  content: '';
  width: 6rpx;
  height: 28rpx;
  background-color: #4a6582;
  margin-right: 12rpx;
  border-radius: 3rpx;
  display: inline-block;
}

.summary-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  background-color: rgba(74, 101, 130, 0.05);
  padding: 16rpx 20rpx;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

/* 商品推荐卡片 */
.product-card {
  margin-bottom: 30rpx;
}

/* 商品网格布局 */
.product-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
  justify-content: space-between;
}

.product-item {
  width: calc(50% - 20rpx);
  margin: 0 10rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f9f9f9;
  border: 1rpx solid #f0f0f0;
  box-sizing: border-box;
  flex-shrink: 0;
}

.product-image-container {
  width: 100%;
  height: 0;
  padding-bottom: 100%; /* 创建1:1的宽高比 */
  overflow: hidden;
  position: relative;
  background-color: #f9f9f9;
}

.product-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 16rpx;
  background-color: #fff;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  max-height: 2.8em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 10rpx;
}

.product-price-container {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.product-price {
  font-size: 30rpx;
  color: #f04142;
  font-weight: 600;
  margin-right: 10rpx;
}

.product-original-price {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
}

.product-coupon {
  display: inline-block;
  background-color: #fff0f0;
  border: 1px solid #ffdddd;
  border-radius: 6rpx;
  padding: 2rpx 8rpx;
}

.coupon-text {
  font-size: 22rpx;
  color: #f04142;
}

/* 商品推荐提示 */
.product-tips {
  margin-top: 20rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 小型加载状态 */
.loading-mini-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.loading-mini-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #333;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-mini-text {
  font-size: 24rpx;
  color: #999;
}

/* 确保动画效果正常 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 商品推荐部分 */
.product-section {
  margin-top: 30rpx;
}

.section-divider {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 30rpx 0;
}

.product-section-title {
  margin-top: 10rpx;
  margin-bottom: 20rpx;
} 