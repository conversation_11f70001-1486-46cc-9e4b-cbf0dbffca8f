.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
  width: 100%;
  overflow-x: hidden; /* 防止水平滑动 */
  position: fixed; /* 固定容器位置 */
  left: 0;
  top: 0;
}

.form-header {
  background-color: #fff;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.step-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.step {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #e0e0e0;
  color: #999;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
}

.step.active {
  background-color: #000;
  color: white;
}

.step-line {
  width: 80rpx;
  height: 2rpx;
  background-color: #e0e0e0;
}

.step-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.form-container {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;
  margin: 0 auto;
  max-width: 680rpx; /* 限制最大宽度并居中 */
}

.section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.subtitle {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.required:after {
  content: " *";
  color: #ff4d4f;
}

.form-input {
  background-color: #f5f5f5;
  height: 80rpx;
  padding: 0 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.form-textarea {
  background-color: #f5f5f5;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  width: 100%;
  height: 180rpx;
  box-sizing: border-box;
}

.gender-selector {
  display: flex;
  justify-content: space-between;
}

.gender-option {
  flex: 1;
  height: 140rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 0 10rpx;
}

.gender-option:first-child {
  margin-left: 0;
}

.gender-option:last-child {
  margin-right: 0;
}

.gender-option.active {
  background-color: #000;
  color: white;
}

.gender-icon {
  font-size: 44rpx;
  margin-bottom: 10rpx;
}

.gender-text {
  font-size: 28rpx;
}

.photo-upload-area {
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
}

.photo-tip {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
}

.photo-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.photo-item, .photo-add {
  width: calc(33.333% - 20rpx);
  margin: 10rpx;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}

.photo-item:before, .photo-add:before {
  content: "";
  display: block;
  padding-top: 100%; /* 1:1 Aspect Ratio */
}

.photo-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-delete {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0,0,0,0.5);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  z-index: 1;
}

.photo-add {
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  border: 1px dashed #ddd;
}

.add-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -60%);
  font-size: 44rpx;
  color: #999;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0;
}

.add-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 20%);
  font-size: 24rpx;
  color: #999;
  text-align: center;
  width: 100%;
}

.privacy-notice {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  margin-bottom: 30rpx;
}

.form-footer {
  padding: 30rpx;
  background-color: #fff;
  box-shadow: 0 -2px 5px rgba(0,0,0,0.05);
}

.next-btn {
  background-color: #000;
  color: white;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.next-btn::after {
  border: none;
}

.next-btn[disabled] {
  background-color: #e0e0e0;
  color: #999;
}

.disabled-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-top: 20rpx;
} 