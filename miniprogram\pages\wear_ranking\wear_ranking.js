const app = getApp();

Page({
  data: {
    rankingData: [],
    loading: true,
    hasMore: true,
    currentPage: 1,
    perPage: 20,
    categoriesMap: {} // 存储完整的分类映射
  },

  // 基础分类映射
  baseCategoryMap: {
    'tops': '上装',
    'bottoms': '下装',
    'dresses': '连衣裙',
    'outerwear': '外套',
    'shoes': '鞋子',
    'accessories': '配饰',
    'bags': '包包',
    'underwear': '内衣',
    'sportswear': '运动装',
    'sleepwear': '睡衣',
    'swimwear': '泳装',
    'formal': '正装',
    'casual': '休闲装',
    'other': '其他'
  },

  // 获取中文分类名称
  getCategoryName(category) {
    // 首先检查完整的分类映射（包含自定义分类）
    if (this.data.categoriesMap[category]) {
      return this.data.categoriesMap[category];
    }

    // 然后检查基础分类映射
    if (this.baseCategoryMap[category]) {
      return this.baseCategoryMap[category];
    }

    // 如果是自定义分类但没有找到映射，返回友好的默认名称
    if (category && category.startsWith('custom_')) {
      return '自定义分类';
    }

    return category || '未分类';
  },

  onLoad() {
    console.log('穿搭次数排行榜页面开始加载');
    this.loadCategories(() => {
      this.loadRankingData();
    });
  },

  onPullDownRefresh() {
    this.setData({
      rankingData: [],
      currentPage: 1,
      hasMore: true
    });
    this.loadCategories(() => {
      this.loadRankingData(true);
    });
  },

  // 加载分类信息
  loadCategories(callback) {
    // 获取token
    let token = app.globalData.token;
    if (!token) {
      token = wx.getStorageSync('token');
    }

    if (!token) {
      console.log('无token，跳过分类加载');
      if (callback) callback();
      return;
    }

    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_clothing_categories.php`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: (res) => {
        console.log('分类数据响应:', res);

        if (res.data && !res.data.error && res.data.data) {
          const categories = res.data.data;
          const categoriesMap = {};

          // 构建分类映射
          categories.forEach(cat => {
            categoriesMap[cat.code] = cat.name;
          });

          this.setData({
            categoriesMap: categoriesMap
          });

          console.log('分类映射构建完成:', categoriesMap);
        }

        if (callback) callback();
      },
      fail: (err) => {
        console.error('加载分类失败:', err);
        if (callback) callback();
      }
    });
  },

  // 加载排行榜数据
  loadRankingData(isRefresh = false) {
    if (!isRefresh && !this.data.hasMore) {
      return;
    }

    this.setData({ loading: true });

    // 获取token
    let token = app.globalData.token;
    if (!token) {
      token = wx.getStorageSync('token');
    }

    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    const apiUrl = `${app.globalData.apiBaseUrl}/get_wear_count_ranking.php`;
    const params = {
      page: this.data.currentPage,
      per_page: this.data.perPage
    };

    const queryString = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&');
    const fullUrl = `${apiUrl}?${queryString}`;

    console.log('请求排行榜数据:', fullUrl);

    wx.request({
      url: fullUrl,
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + token
      },
      success: (res) => {
        console.log('排行榜数据响应:', res);

        if (res.data && !res.data.error) {
          const newData = res.data.data || [];
          const pagination = res.data.pagination || {};

          // 处理分类名称转换为中文
          const processedData = newData.map(item => ({
            ...item,
            categoryName: this.getCategoryName(item.category)
          }));

          let rankingData = [];
          if (isRefresh || this.data.currentPage === 1) {
            rankingData = processedData;
          } else {
            rankingData = [...this.data.rankingData, ...processedData];
          }

          this.setData({
            rankingData: rankingData,
            loading: false,
            hasMore: pagination.current_page < pagination.total_pages,
            currentPage: pagination.current_page
          });

          if (isRefresh) {
            wx.stopPullDownRefresh();
          }
        } else {
          wx.showToast({
            title: res.data?.message || '加载失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({ loading: false });
        if (isRefresh) {
          wx.stopPullDownRefresh();
        }
      }
    });
  },

  // 加载更多
  loadMore() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }

    this.setData({
      currentPage: this.data.currentPage + 1
    });
    this.loadRankingData();
  },

  // 点击衣物项
  onItemTap(e) {
    const itemId = e.currentTarget.dataset.id;
    console.log('点击衣物:', itemId);
    
    // 跳转到衣物详情页
    wx.navigateTo({
      url: `/pages/clothing/detail/detail?id=${itemId}`
    });
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '我的穿搭次数排行榜',
      path: '/pages/wear_ranking/wear_ranking'
    };
  }
});
