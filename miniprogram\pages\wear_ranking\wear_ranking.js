const app = getApp();

Page({
  data: {
    rankingData: [],
    loading: true,
    hasMore: true,
    currentPage: 1,
    perPage: 20
  },

  onLoad() {
    console.log('穿搭次数排行榜页面开始加载');
    this.loadRankingData();
  },

  onPullDownRefresh() {
    this.setData({
      rankingData: [],
      currentPage: 1,
      hasMore: true
    });
    this.loadRankingData(true);
  },

  // 加载排行榜数据
  loadRankingData(isRefresh = false) {
    if (!isRefresh && !this.data.hasMore) {
      return;
    }

    this.setData({ loading: true });

    // 获取token
    let token = app.globalData.token;
    if (!token) {
      token = wx.getStorageSync('token');
    }

    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    const apiUrl = `${app.globalData.apiBaseUrl}/get_wear_count_ranking.php`;
    const params = {
      page: this.data.currentPage,
      per_page: this.data.perPage
    };

    const queryString = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&');
    const fullUrl = `${apiUrl}?${queryString}`;

    console.log('请求排行榜数据:', fullUrl);

    wx.request({
      url: fullUrl,
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + token
      },
      success: (res) => {
        console.log('排行榜数据响应:', res);

        if (res.data && !res.data.error) {
          const newData = res.data.data || [];
          const pagination = res.data.pagination || {};

          let rankingData = [];
          if (isRefresh || this.data.currentPage === 1) {
            rankingData = newData;
          } else {
            rankingData = [...this.data.rankingData, ...newData];
          }

          this.setData({
            rankingData: rankingData,
            loading: false,
            hasMore: pagination.current_page < pagination.total_pages,
            currentPage: pagination.current_page
          });

          if (isRefresh) {
            wx.stopPullDownRefresh();
          }
        } else {
          wx.showToast({
            title: res.data?.message || '加载失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({ loading: false });
        if (isRefresh) {
          wx.stopPullDownRefresh();
        }
      }
    });
  },

  // 加载更多
  loadMore() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }

    this.setData({
      currentPage: this.data.currentPage + 1
    });
    this.loadRankingData();
  },

  // 点击衣物项
  onItemTap(e) {
    const itemId = e.currentTarget.dataset.id;
    console.log('点击衣物:', itemId);
    
    // 跳转到衣物详情页
    wx.navigateTo({
      url: `/pages/clothing/detail/detail?id=${itemId}`
    });
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '我的穿搭次数排行榜',
      path: '/pages/wear_ranking/wear_ranking'
    };
  }
});
