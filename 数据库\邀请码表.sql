-- 创建邀请码表
CREATE TABLE `invitation_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(20) NOT NULL COMMENT '邀请码',
  `status` enum('unused','used','expired') NOT NULL DEFAULT 'unused' COMMENT '状态：unused=未使用, used=已使用, expired=已过期',
  `type` varchar(20) NOT NULL DEFAULT 'image_analysis' COMMENT '邀请码类型，默认为形象分析',
  `created_by` int(11) DEFAULT NULL COMMENT '创建者ID',
  `used_by` int(11) DEFAULT NULL COMMENT '使用者ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expired_at` datetime DEFAULT NULL COMMENT '过期时间',
  `used_at` datetime DEFAULT NULL COMMENT '使用时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`),
  KEY `idx_status` (`status`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_used_by` (`used_by`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请码表'; 