<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once 'config.php';
require_once 'db.php';

// Handle preflight request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 获取活跃公告
$db = new Database();
$conn = $db->getConnection();

try {
    // 查询当前有效的公告（状态为启用且当前时间在start_time和end_time之间）
    $sql = "SELECT id, title, content, start_time, end_time FROM announcements 
            WHERE status = 1 
            AND NOW() BETWEEN start_time AND end_time 
            ORDER BY created_at DESC 
            LIMIT 1";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    
    $announcement = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 返回数据
    if ($announcement) {
        echo json_encode([
            'error' => false,
            'data' => [
                'id' => $announcement['id'],
                'title' => $announcement['title'],
                'content' => $announcement['content'],
                'start_time' => $announcement['start_time'],
                'end_time' => $announcement['end_time']
            ]
        ]);
    } else {
        // 没有活跃公告
        echo json_encode([
            'error' => false,
            'data' => null
        ]);
    }
} catch (Exception $e) {
    // 发生错误
    echo json_encode([
        'error' => true,
        'msg' => '获取公告失败: ' . $e->getMessage()
    ]);
} 