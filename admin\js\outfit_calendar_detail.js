/**
 * 穿搭日历详情页面脚本
 */
const OutfitCalendarDetail = {
    // 状态数据
    data: {
        calendarId: null,
        calendarDetail: null,
        userCalendars: []
    },
    
    /**
     * 初始化
     */
    init: function() {
        // 获取URL参数
        const urlParams = new URLSearchParams(window.location.search);
        this.data.calendarId = urlParams.get('id');
        
        if (!this.data.calendarId) {
            alert('缺少必要的日历ID参数');
            window.location.href = 'outfit_calendar_list.html';
            return;
        }
        
        // 绑定事件
        document.getElementById('backBtn').addEventListener('click', function() {
            window.location.href = 'outfit_calendar_list.html';
        });
        
        // 加载数据
        this.loadData();
    },
    
    /**
     * 加载日历详情数据
     */
    loadData: function() {
        // 显示加载中
        document.getElementById('loadingContainer').style.display = 'flex';
        document.getElementById('detailContainer').style.display = 'none';
        
        // 构建API URL
        const url = `../login_backend/admin_get_outfit_calendar_detail.php?id=${this.data.calendarId}`;
        
        // 发起请求
        fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + Auth.getToken()
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败');
            }
            return response.json();
        })
        .then(data => {
            console.log('API Response:', data);
            
            if (data.error === false && data.data) {
                // 保存数据
                this.data.calendarDetail = data.data.calendar;
                this.data.userCalendars = data.data.user_calendars;
                
                // 渲染日历导航
                this.renderCalendarNav();
                
                // 渲染所有穿搭节点
                this.renderAllOutfits();
                
                // 隐藏加载中，显示详情
                document.getElementById('loadingContainer').style.display = 'none';
                document.getElementById('detailContainer').style.display = 'block';
            } else {
                throw new Error(data.msg || '获取数据失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('loadingContainer').style.display = 'none';
            alert(`加载数据失败: ${error.message}`);
        });
    },
    
    /**
     * 渲染所有穿搭节点
     */
    renderAllOutfits: function() {
        const container = document.getElementById('allOutfits');
        if (!container) return;
        
        // 清空容器
        container.innerHTML = '';
        
        // 如果没有用户日历记录，显示提示
        if (!this.data.userCalendars || this.data.userCalendars.length === 0) {
            container.innerHTML = '<p>暂无穿搭日历记录</p>';
            return;
        }
        
        // 按日期倒序排序
        const sortedCalendars = [...this.data.userCalendars].sort((a, b) => {
            const dateA = a.calendar_date ? new Date(a.calendar_date) : new Date(0);
            const dateB = b.calendar_date ? new Date(b.calendar_date) : new Date(0);
            return dateB - dateA;
        });
        
        // 默认图片
        const defaultImage = 'https://cyyg.alidog.cn/admin/images/default-cloth.png';
        
        // 为每个日历记录创建时间线节点
        sortedCalendars.forEach(calendar => {
            // 确定是否是当前选中的记录
            const isActive = parseInt(calendar.calendar_id) === parseInt(this.data.calendarId);
            
            // 格式化日期
            const calendarDate = calendar.calendar_date ? new Date(calendar.calendar_date) : null;
            const formattedDate = calendarDate ? 
                `${calendarDate.getFullYear()}-${String(calendarDate.getMonth() + 1).padStart(2, '0')}-${String(calendarDate.getDate()).padStart(2, '0')}` : '-';
            
            // 创建节点元素
            const nodeElement = document.createElement('div');
            nodeElement.className = 'timeline-node';
            nodeElement.innerHTML = `
                <div class="node-marker"></div>
                <div class="node-content">
                    <div class="node-header">
                        <div class="node-title">${calendar.outfit_name || '穿搭日历'}</div>
                        <div class="node-date">${formattedDate}</div>
                    </div>
                    <div class="node-body" id="outfit-body-${calendar.calendar_id}">
                        <div class="loading-spinner" style="margin: 20px auto;"></div>
                        <p style="text-align: center;">加载中...</p>
                    </div>
                </div>
            `;
            
            container.appendChild(nodeElement);
            
            // 加载该日历记录的详细信息
            this.loadOutfitDetails(calendar.calendar_id);
        });
    },
    
    /**
     * 加载特定日历记录的详细信息
     */
    loadOutfitDetails: function(calendarId) {
        // 如果是当前查看的记录，直接使用已加载的数据
        if (parseInt(calendarId) === parseInt(this.data.calendarId) && this.data.calendarDetail) {
            this.renderOutfitNode(this.data.calendarDetail);
            return;
        }
        
        // 构建API URL
        const url = `../login_backend/admin_get_outfit_calendar_detail.php?id=${calendarId}`;
        
        // 发起请求
        fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + Auth.getToken()
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败');
            }
            return response.json();
        })
        .then(data => {
            if (data.error === false && data.data && data.data.calendar) {
                this.renderOutfitNode(data.data.calendar);
            } else {
                throw new Error(data.msg || '获取数据失败');
            }
        })
        .catch(error => {
            console.error(`Error loading outfit details for calendar ID ${calendarId}:`, error);
            const nodeBody = document.getElementById(`outfit-body-${calendarId}`);
            if (nodeBody) {
                nodeBody.innerHTML = '<p style="text-align: center; color: #ff4d4f;">加载失败，请刷新页面重试</p>';
            }
        });
    },
    
    /**
     * 渲染单个穿搭节点的内容
     */
    renderOutfitNode: function(outfitData) {
        const nodeBody = document.getElementById(`outfit-body-${outfitData.calendar_id}`);
        if (!nodeBody) return;
        
        // 默认图片
        const defaultImage = 'https://cyyg.alidog.cn/admin/images/default-cloth.png';
        
        // 处理缩略图：当thumbnail_url为空时，尝试从outfit_data中获取第一个衣物的图片
        let thumbnailUrl = defaultImage;
        if (outfitData.thumbnail_url && outfitData.thumbnail_url.trim() !== '') {
            // 使用穿搭缩略图
            thumbnailUrl = this.ensureValidImageUrl(outfitData.thumbnail_url, defaultImage);
        } else if (outfitData.outfit_data && outfitData.outfit_data.items && outfitData.outfit_data.items.length > 0) {
            // 尝试使用第一个衣物的图片
            const firstClothing = outfitData.outfit_data.items[0];
            if (firstClothing.clothing_data && firstClothing.clothing_data.image_url) {
                thumbnailUrl = this.ensureValidImageUrl(firstClothing.clothing_data.image_url, defaultImage);
            }
        }
        
        // 格式化时间
        const createdAt = outfitData.calendar_created_at ? new Date(outfitData.calendar_created_at) : null;
        const updatedAt = outfitData.calendar_updated_at ? new Date(outfitData.calendar_updated_at) : null;
        
        const formatDateTime = (date) => {
            if (!date) return '-';
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
        };
        
        // 构建基本信息HTML
        let infoHtml = `
            <div class="outfit-info">
                <div class="outfit-meta">
                    <div class="outfit-name">${outfitData.outfit_name || '未命名穿搭'}</div>
                    <ul class="meta-list">
                        <li class="meta-item">
                            <div class="meta-label">日历ID:</div>
                            <div class="meta-value">${outfitData.calendar_id || '-'}</div>
                        </li>
                        <li class="meta-item">
                            <div class="meta-label">穿搭ID:</div>
                            <div class="meta-value">${outfitData.outfit_id || '-'}</div>
                        </li>
                        <li class="meta-item">
                            <div class="meta-label">分类:</div>
                            <div class="meta-value">${outfitData.category_name || '未分类'}</div>
                        </li>
                        <li class="meta-item">
                            <div class="meta-label">创建时间:</div>
                            <div class="meta-value">${formatDateTime(createdAt)}</div>
                        </li>
                        <li class="meta-item">
                            <div class="meta-label">更新时间:</div>
                            <div class="meta-value">${formatDateTime(updatedAt)}</div>
                        </li>
                        <li class="meta-item">
                            <div class="meta-label">用户:</div>
                            <div class="meta-value">${outfitData.nickname || '未知用户'} (ID: ${outfitData.user_id || '-'})</div>
                        </li>
                    </ul>
                    ${outfitData.description ? `<div class="outfit-description">${outfitData.description}</div>` : ''}
                </div>
            </div>
        `;
        
        // 构建图片容器HTML
        let imagesHtml = '<div class="outfit-images-container">';
        
        // 添加主图
        imagesHtml += `<img class="outfit-image-item" src="${thumbnailUrl}" alt="${outfitData.outfit_name || '穿搭预览'}" onerror="this.src='${defaultImage}'">`;
        
        // 添加衣物图片
        if (outfitData.outfit_data && outfitData.outfit_data.items && outfitData.outfit_data.items.length > 0) {
            const items = outfitData.outfit_data.items;
            const maxImages = Math.min(5, items.length);
            
            for (let i = 0; i < maxImages; i++) {
                const item = items[i];
                if (item.clothing_data && item.clothing_data.image_url) {
                    const itemImage = this.ensureValidImageUrl(item.clothing_data.image_url, defaultImage);
                    imagesHtml += `<img class="outfit-image-item" src="${itemImage}" alt="${item.clothing_data.name || '衣物'}" onerror="this.src='${defaultImage}'">`;
                }
            }
        }
        
        imagesHtml += '</div>';
        
        // 构建衣物列表HTML
        let itemsHtml = '<div class="outfit-items-title">穿搭物品</div>';
        itemsHtml += '<div class="outfit-items-grid">';
        
        if (outfitData.outfit_data && outfitData.outfit_data.items && outfitData.outfit_data.items.length > 0) {
            outfitData.outfit_data.items.forEach(item => {
                if (!item.clothing_data) return;
                
                const itemImage = this.ensureValidImageUrl(item.clothing_data.image_url, defaultImage);
                
                itemsHtml += `
                    <div class="clothing-item">
                        <img src="${itemImage}" alt="${item.clothing_data.name || '衣物'}" class="clothing-image" onerror="this.src='${defaultImage}'">
                        <div class="clothing-info">
                            <div class="clothing-name">${item.clothing_data.name || '未命名衣物'}</div>
                            <div class="clothing-category">${item.clothing_data.category_name || item.clothing_data.category || '未分类'}</div>
                        </div>
                    </div>
                `;
            });
        } else {
            itemsHtml += '<p>暂无穿搭物品数据</p>';
        }
        
        itemsHtml += '</div>';
        
        // 更新节点内容
        nodeBody.innerHTML = infoHtml + imagesHtml + itemsHtml;
    },
    
    /**
     * 渲染日历导航
     */
    renderCalendarNav: function() {
        const navContainer = document.getElementById('calendarNav');
        navContainer.innerHTML = '';
        
        if (!this.data.userCalendars || this.data.userCalendars.length === 0) {
            navContainer.innerHTML = '<p>暂无更多日历记录</p>';
            return;
        }
        
        // 默认图片
        const defaultImage = 'https://cyyg.alidog.cn/admin/images/default-cloth.png';
        
        // 更新日历数量
        const calendarCount = document.getElementById('calendarCount');
        if (calendarCount) {
            calendarCount.textContent = `共 ${this.data.userCalendars.length} 条记录`;
        }
        
        // 渲染每个日历项
        this.data.userCalendars.forEach(calendar => {
            // 确保图片URL是完整的
            // 当thumbnail_url为空时，使用默认图片
            let thumbnailUrl = defaultImage;
            if (calendar.thumbnail_url && calendar.thumbnail_url.trim() !== '') {
                thumbnailUrl = this.ensureValidImageUrl(calendar.thumbnail_url, defaultImage);
            }
            
            // 格式化日期
            const calendarDate = calendar.calendar_date ? new Date(calendar.calendar_date) : null;
            const formattedDate = calendarDate ? 
                `${calendarDate.getFullYear()}-${String(calendarDate.getMonth() + 1).padStart(2, '0')}-${String(calendarDate.getDate()).padStart(2, '0')}` : '-';
            
            // 当前选中项
            const isActive = parseInt(calendar.calendar_id) === parseInt(this.data.calendarId);
            
            const itemElement = document.createElement('div');
            itemElement.className = `calendar-item${isActive ? ' active' : ''}`;
            itemElement.innerHTML = `
                <div class="calendar-date">${formattedDate}</div>
                <img src="${thumbnailUrl}" alt="${calendar.outfit_name || '穿搭'}" class="calendar-thumbnail" onerror="this.src='${defaultImage}'">
            `;
            
            // 点击跳转
            if (!isActive) {
                itemElement.addEventListener('click', () => {
                    window.location.href = `outfit_calendar_detail.html?id=${calendar.calendar_id}`;
                });
            } else {
                // 如果是当前选中项，点击时滚动到对应的节点
                itemElement.addEventListener('click', () => {
                    const targetNode = document.getElementById(`outfit-body-${calendar.calendar_id}`);
                    if (targetNode) {
                        targetNode.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            }
            
            navContainer.appendChild(itemElement);
        });
    },
    
    /**
     * 确保图片URL有效，返回有效的图片URL或默认图片
     * @param {string} url 原始图片URL
     * @param {string} defaultUrl 默认图片URL
     * @returns {string} 处理后的图片URL
     */
    ensureValidImageUrl: function(url, defaultUrl) {
        // 默认使用远程图片地址
        const remoteDefaultImage = 'https://cyyg.alidog.cn/admin/images/default-cloth.png';
        defaultUrl = defaultUrl || remoteDefaultImage;
        
        if (!url) {
            return defaultUrl;
        }
        
        // 如果是相对URL，添加基础路径
        if (url.startsWith('/')) {
            return url;
        } else if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('data:')) {
            // 相对路径处理，添加../，确保能从admin目录正确访问
            if (!url.startsWith('../')) {
                return '../' + url;
            }
            return url;
        }
        
        return url;
    }
}; 