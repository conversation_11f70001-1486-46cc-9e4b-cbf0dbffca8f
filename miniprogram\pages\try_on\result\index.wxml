<!-- 主要内容容器 -->
<view class="result-container">
  <!-- 试穿效果图片区域 -->
  <view class="result-image-wrapper">
    <view class="result-image">
      <view class="image-container" bindtap="previewImage">
        <image src="{{resultImage}}" class="result-img" mode="widthFix"></image>
      </view>
      <view class="image-overlay">
        <view class="watermark">
          <text class="watermark-icon">✨</text> 次元衣帽间
        </view>
      </view>
    </view>
  </view>
  
  <!-- 操作按钮 -->
  <view class="action-buttons">
    <view class="action-row">
      <view class="action-btn" bindtap="saveImage">保存</view>
      <button class="action-btn share-button" open-type="share">分享</button>
      <view class="action-btn" bindtap="deleteImage">删除</view>
    </view>
  </view>
</view>

<!-- 加载中 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-content">
    <view class="loading-icon"></view>
    <view class="loading-text">生成试衣效果中...</view>
  </view>
</view>

<!-- 错误提示 -->
<view class="error-container" wx:if="{{error}}">
  <view class="error-content">
    <view class="error-icon">❌</view>
    <view class="error-text">{{error}}</view>
    <view class="error-btn" bindtap="retryClothing">返回重试</view>
  </view>
</view>

<!-- 图片预览弹窗 -->
<view class="preview-modal" wx:if="{{showPreview}}" bindtap="closePreview">
  <image src="{{resultImage}}" class="preview-image" mode="aspectFit"></image>
  <view class="preview-close">×</view>
</view>

<!-- 用于绘制水印的Canvas，设置为隐藏 -->
<canvas type="2d" id="watermarkCanvas" class="watermark-canvas"></canvas> 