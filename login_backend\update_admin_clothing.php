<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证管理员身份
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取POST数据
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

// 检查必要参数
if (!isset($input['user_id']) || empty($input['user_id']) || 
    !isset($input['name']) || empty($input['name']) || 
    !isset($input['category']) || empty($input['category']) || 
    !isset($input['image_url']) || empty($input['image_url'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少必要参数: user_id, name, category, image_url']);
    exit();
}

// 提取数据
$clothingId = isset($input['id']) ? (int)$input['id'] : null;
$userId = (int)$input['user_id'];
$name = trim($input['name']);
$category = trim($input['category']);
$imageUrl = trim($input['image_url']);
$tags = isset($input['tags']) ? trim($input['tags']) : '';
$description = isset($input['description']) ? $input['description'] : null;

// 验证分类
$allowedCategories = ['tops', 'pants', 'skirts', 'coats', 'shoes', 'bags', 'accessories'];
if (!in_array($category, $allowedCategories)) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '无效的分类']);
    exit();
}

// 验证图片URL格式
if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '无效的图片URL']);
    exit();
}

// 验证用户存在
$db = new Database();
$conn = $db->getConnection();

$userQuery = "SELECT id FROM users WHERE id = :user_id";
$userStmt = $conn->prepare($userQuery);
$userStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
$userStmt->execute();

if (!$userStmt->fetch()) {
    http_response_code(404);
    echo json_encode(['error' => true, 'msg' => '用户不存在']);
    exit();
}

// 处理描述字段，确保它是JSON字符串
if (is_array($description)) {
    $description = json_encode($description, JSON_UNESCAPED_UNICODE);
} elseif ($description === null) {
    $description = '';
}

// 准备更新或插入
$isNewRecord = $clothingId === null;
$now = date('Y-m-d H:i:s');

try {
    // 开始事务
    $conn->beginTransaction();
    
    if ($isNewRecord) {
        // 插入新记录
        $insertQuery = "INSERT INTO clothes (user_id, name, category, image_url, tags, description, created_at, updated_at) 
                       VALUES (:user_id, :name, :category, :image_url, :tags, :description, :created_at, :updated_at)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindValue(':name', $name);
        $stmt->bindValue(':category', $category);
        $stmt->bindValue(':image_url', $imageUrl);
        $stmt->bindValue(':tags', $tags);
        $stmt->bindValue(':description', $description);
        $stmt->bindValue(':created_at', $now);
        $stmt->bindValue(':updated_at', $now);
        $stmt->execute();
        
        $clothingId = $conn->lastInsertId();
        $message = '衣物添加成功';
    } else {
        // 检查记录是否存在
        $checkQuery = "SELECT id FROM clothes WHERE id = :id";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bindValue(':id', $clothingId, PDO::PARAM_INT);
        $checkStmt->execute();
        
        if (!$checkStmt->fetch()) {
            throw new Exception('衣物不存在');
        }
        
        // 更新现有记录
        $updateQuery = "UPDATE clothes 
                       SET user_id = :user_id, 
                           name = :name, 
                           category = :category, 
                           image_url = :image_url, 
                           tags = :tags, 
                           description = :description, 
                           updated_at = :updated_at 
                       WHERE id = :id";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bindValue(':id', $clothingId, PDO::PARAM_INT);
        $stmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindValue(':name', $name);
        $stmt->bindValue(':category', $category);
        $stmt->bindValue(':image_url', $imageUrl);
        $stmt->bindValue(':tags', $tags);
        $stmt->bindValue(':description', $description);
        $stmt->bindValue(':updated_at', $now);
        $stmt->execute();
        
        $message = '衣物更新成功';
    }
    
    // 提交事务
    $conn->commit();
    
    // 查询更新后的记录
    $query = "SELECT id, user_id, name, category, image_url, tags, description, created_at, updated_at 
              FROM clothes 
              WHERE id = :id";
    $stmt = $conn->prepare($query);
    $stmt->bindValue(':id', $clothingId, PDO::PARAM_INT);
    $stmt->execute();
    
    $clothing = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 格式化description字段
    if (!empty($clothing['description'])) {
        $descriptionData = json_decode($clothing['description'], true);
        if (json_last_error() === JSON_ERROR_NONE) {
            $clothing['description'] = $descriptionData;
        }
    }
    
    // 格式化tags字段
    if (!empty($clothing['tags'])) {
        $clothing['tags_array'] = explode(',', $clothing['tags']);
    }
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'msg' => $message,
        'data' => $clothing
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    // 返回错误响应
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '操作失败: ' . $e->getMessage()
    ]);
} 