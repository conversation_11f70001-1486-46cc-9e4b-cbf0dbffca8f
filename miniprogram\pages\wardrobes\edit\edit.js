// pages/wardrobes/edit/edit.js
// 获取应用实例
const app = getApp();

Page({

    /**
     * 页面的初始数据
     */
    data: {
        id: null,
        name: '',
        description: '',
        sortOrder: '0',
        submitting: false,
        isDefault: false,
        originalName: '' // 用于比较是否修改了名称
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function(options) {
        console.log('编辑衣橱页面参数:', options);
        
        if (options.id && options.name) {
            // 解码URL编码的中文字符
            const decodedName = decodeURIComponent(options.name);
            const decodedDescription = options.description ? decodeURIComponent(options.description) : '';
            
            // 初始化数据
            this.setData({
                id: options.id,
                name: decodedName,
                description: decodedDescription,
                sortOrder: options.sort_order || options.sortOrder || '0',
                originalName: decodedName,
                isDefault: options.is_default === '1' || options.isDefault === true
            });
        } else {
            wx.showToast({
                title: '参数错误',
                icon: 'none'
            });
            
            setTimeout(() => {
                wx.navigateBack();
            }, 1500);
        }
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    },

    /**
     * 名称输入事件
     */
    onNameInput: function(e) {
        this.setData({
            name: e.detail.value
        });
    },

    /**
     * 描述输入事件
     */
    onDescriptionInput: function(e) {
        this.setData({
            description: e.detail.value
        });
    },

    /**
     * 排序输入事件
     */
    onSortOrderInput: function(e) {
        this.setData({
            sortOrder: e.detail.value
        });
    },

    /**
     * 取消操作
     */
    onCancel: function() {
        wx.navigateBack();
    },

    /**
     * 提交表单
     */
    onSubmit: function() {
        // 检查必填字段
        if (!this.data.name.trim()) {
            wx.showToast({
                title: '请输入衣橱名称',
                icon: 'none'
            });
            return;
        }

        // 防止重复提交
        if (this.data.submitting) {
            return;
        }

        this.setData({ submitting: true });

        // 整理数据
        const data = {
            id: this.data.id,
            name: this.data.name.trim(),
            description: this.data.description.trim(),
            sort_order: parseInt(this.data.sortOrder || 0)
        };

        // 发送请求
        wx.showLoading({ title: '正在保存...' });

        wx.request({
            url: `${app.globalData.apiBaseUrl}/update_wardrobe.php`,
            method: 'POST',
            header: {
                'content-type': 'application/json',
                'Authorization': app.globalData.token
            },
            data: data,
            success: (res) => {
                wx.hideLoading();
                console.log('更新衣橱响应:', res.data);

                if (res.statusCode === 200 && res.data.success) {
                    wx.showToast({
                        title: '保存成功',
                        icon: 'success',
                        duration: 1500
                    });

                    // 标记需要刷新列表
                    app.globalData.needRefreshWardrobes = true;

                    // 短暂延迟后返回上一页
                    setTimeout(() => {
                        wx.navigateBack();
                    }, 1500);
                } else if (res.data.error === 'Name already exists') {
                    wx.showToast({
                        title: '衣橱名称已存在',
                        icon: 'none',
                        duration: 2000
                    });
                    this.setData({ submitting: false });
                } else {
                    wx.showToast({
                        title: res.data.message || '保存失败',
                        icon: 'none',
                        duration: 2000
                    });
                    this.setData({ submitting: false });
                }
            },
            fail: (err) => {
                wx.hideLoading();
                console.error('请求失败:', err);
                wx.showToast({
                    title: '网络请求失败',
                    icon: 'none',
                    duration: 2000
                });
                this.setData({ submitting: false });
            }
        });
    }
})