<?php
/**
 * 天气API服务
 * 
 * 从和风天气API获取天气数据
 * 已优化：直接调用和风天气API，而不是通过proxy_weather_api.php
 */

header('Content-Type: application/json;charset=utf-8');

// 加载配置和工具函数
require_once 'config.php';
require_once 'city_utils.php';

// 定义常量
define('WEATHER_CACHE_DIR', __DIR__ . '/cache/weather'); // 天气数据缓存目录
define('WEATHER_CACHE_TIME', 0); // 缓存有效期设为0秒，禁用缓存功能

// 确保缓存目录存在
if (!is_dir(WEATHER_CACHE_DIR)) {
    mkdir(WEATHER_CACHE_DIR, 0755, true);
}

// 禁用IPv6，强制使用IPv4
// 解决和风天气API的Akamai CDN在IPv6上的403错误问题
$contextOptions = [
    'socket' => [
        'bindto' => '0:0', // 强制使用IPv4
    ],
];

// 设置默认的stream context
stream_context_set_default($contextOptions);

// 创建context用于特定请求
$context = stream_context_create($contextOptions);

// 日志函数
function writeLog($message) {
    error_log("[天气API] " . $message);
    
    // 同时写入到天气日志文件
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/weather_' . date('Y-m-d') . '.log';
    file_put_contents($logFile, date('Y-m-d H:i:s') . ' ' . $message . "\n", FILE_APPEND);
}

/**
 * 记录天气API诊断信息到专门的日志文件
 * @param string $source 数据来源(API/CACHE/MOCK)
 * @param array $data 天气数据
 * @param array $requestInfo 请求信息
 * @param array $responseInfo 响应信息
 */
function logWeatherDiagnostics($source, $data = [], $requestInfo = [], $responseInfo = []) {
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/weather_diagnostics_' . date('Y-m-d') . '.log';
    
    $timestamp = date('Y-m-d H:i:s');
    $logData = [
        'timestamp' => $timestamp,
        'source' => $source,
        'client_ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'request_info' => $requestInfo,
        'response_info' => $responseInfo,
        'data' => $data
    ];
    
    // 记录天气数据的关键字段，但不记录完整数据以节省空间
    $dataSummary = [];
    if (!empty($data)) {
        $dataSummary = [
            'success' => isset($data['success']) ? $data['success'] : null,
            'city' => isset($data['city']) ? $data['city'] : null,
            'cityid' => isset($data['cityid']) ? $data['cityid'] : null,
            'temp' => isset($data['temp']) ? $data['temp'] : null,
            'text' => isset($data['text']) ? $data['text'] : null,
            'is_mock' => isset($data['_is_mock']) ? $data['_is_mock'] : false,
            'is_cached' => ($source === 'CACHE'),
            'timestamp' => isset($data['_timestamp']) ? $data['_timestamp'] : null,
        ];
    }
    
    $logData['data_summary'] = $dataSummary;
    
    $logMessage = json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    file_put_contents($logFile, $logMessage . "\n---\n", FILE_APPEND);
    
    // 同时写入常规日志
    $statusMessage = "天气数据诊断 - 来源: {$source}";
    if (!empty($dataSummary)) {
        $statusMessage .= ", 城市: " . ($dataSummary['city'] ?? '未知');
        $statusMessage .= ", 温度: " . ($dataSummary['temp'] ?? '未知');
        $statusMessage .= ", 状态: " . ($dataSummary['text'] ?? '未知');
    }
    writeLog($statusMessage);
}

// 添加详细日志函数
function writeDetailedLog($type, $message, $data = null) {
    $logMessage = "[{$type}] {$message}";
    if ($data !== null) {
        $logMessage .= "\nData: " . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    writeLog($logMessage);
}

/**
 * 检查是否存在有效的天气数据缓存
 * @param string $locationParam 位置参数
 * @return bool 是否存在有效缓存
 */
function hasValidWeatherCache($locationParam) {
    // 直接返回false，强制禁用缓存，确保每次都获取最新数据
    return false;
    
    // 以下代码保留但不再执行
    /*
    // 检查是否有强制刷新的参数
    if (isset($_GET['_nocache'])) {
        writeLog("检测到_nocache参数，强制跳过缓存");
        return false;
    }
    
    $cacheFile = WEATHER_CACHE_DIR . '/' . md5($locationParam) . '.json';
    
    if (!file_exists($cacheFile)) {
        return false;
    }
    
    $fileTime = filemtime($cacheFile);
    $currentTime = time();
    
    // 检查缓存是否过期
    if (($currentTime - $fileTime) > WEATHER_CACHE_TIME) {
        writeLog("缓存已过期: " . $cacheFile);
        return false;
    }
    
    return true;
    */
}

/**
 * 从缓存获取天气数据
 * @param string $locationParam 位置参数
 * @return array|null 天气数据或null
 */
function getWeatherFromCache($locationParam) {
    // 直接返回null，强制禁用缓存
    return null;
    
    // 以下代码保留但不再执行
    /*
    $cacheFile = WEATHER_CACHE_DIR . '/' . md5($locationParam) . '.json';
    
    if (!hasValidWeatherCache($locationParam)) {
        return null;
    }
    
    $data = file_get_contents($cacheFile);
    $weatherData = json_decode($data, true);
    
    if (!$weatherData) {
        writeLog("缓存数据无效: " . $cacheFile);
        return null;
    }
    
    writeLog("从缓存获取天气数据: " . $cacheFile);
    return $weatherData;
    */
}

/**
 * 将天气数据写入缓存
 * @param string $locationParam 位置参数
 * @param array $weatherData 天气数据
 * @return bool 是否成功写入缓存
 */
function saveWeatherToCache($locationParam, $weatherData) {
    // 禁用缓存写入功能，直接返回true表示"成功"
    writeLog("缓存功能已禁用，跳过写入");
    return true;
    
    // 以下代码保留但不再执行
    /*
    $cacheFile = WEATHER_CACHE_DIR . '/' . md5($locationParam) . '.json';
    
    $result = file_put_contents($cacheFile, json_encode($weatherData, JSON_UNESCAPED_UNICODE));
    
    if ($result === false) {
        writeLog("写入缓存失败: " . $cacheFile);
        return false;
    }
    
    writeLog("天气数据已缓存: " . $cacheFile);
    return true;
    */
}

/**
 * 获取模拟的天气数据（当API调用失败时使用）
 * @param string $cityName 城市名称
 * @param string $cityId 城市ID
 * @param string $locationParam 位置参数
 * @return array 模拟的天气数据
 */
function getMockWeatherData($cityName = null, $cityId = null, $locationParam = null) {
    writeLog("生成模拟天气数据");
    
    // 使用随机但合理的数据范围
    $temp = mt_rand(5, 35); // 5-35摄氏度
    $feelsLike = $temp + mt_rand(-3, 3); // 体感温度在实际温度上下3度
    
    // 天气类型和图标
    $weatherTypes = [
        ['text' => '晴', 'icon' => '100'],
        ['text' => '多云', 'icon' => '101'],
        ['text' => '阴', 'icon' => '104'],
        ['text' => '小雨', 'icon' => '305'],
        ['text' => '中雨', 'icon' => '306'],
        ['text' => '大雨', 'icon' => '307'],
        ['text' => '小雪', 'icon' => '400'],
        ['text' => '中雪', 'icon' => '401'],
        ['text' => '大雪', 'icon' => '402']
    ];
    $weatherType = $weatherTypes[mt_rand(0, count($weatherTypes) - 1)];
    
    // 风向和风力
    $windDirs = ['东风', '南风', '西风', '北风', '东北风', '东南风', '西北风', '西南风'];
    $windDir = $windDirs[mt_rand(0, count($windDirs) - 1)];
    $windScale = mt_rand(0, 7); // 0-7级风
    
    // 构造一个符合和风天气API格式的响应
    $mockData = [
        'temp' => (string)$temp,
        'feelsLike' => (string)$feelsLike,
        'icon' => $weatherType['icon'],
        'text' => $weatherType['text'],
        'wind360' => (string)(mt_rand(0, 359)),
        'windDir' => $windDir,
        'windScale' => (string)$windScale,
        'windSpeed' => (string)(mt_rand(1, 30)),
        'humidity' => (string)(mt_rand(30, 95)),
        'precip' => sprintf("%.1f", mt_rand(0, 100) / 10),
        'pressure' => (string)(mt_rand(980, 1040)),
        'vis' => (string)(mt_rand(5, 30)),
        'cloud' => (string)(mt_rand(0, 100)),
        'dew' => (string)(mt_rand(-5, 25)),
        'updateTime' => date('Y-m-d\TH:i:sP'),
        'fxLink' => 'http://hfx.link/2ax1',
        '_realtime' => false,
        '_timestamp' => time(),
        '_is_mock' => true,
        '_debug' => [
            'source' => 'mock',
            'timestamp' => time(),
            'reason' => 'API request failed',
            'cityName' => $cityName,
            'cityId' => $cityId,
            'locationParam' => $locationParam
        ]
    ];
    
    // 添加城市信息
    if ($cityName) {
        $mockData['city'] = $cityName;
    } else {
        $mockData['city'] = '未知城市';
    }
    
    if ($cityId) {
        $mockData['cityid'] = $cityId;
    } else {
        $mockData['cityid'] = $locationParam ?: WEATHER_DEFAULT_LOCATION;
    }
    
    writeLog("模拟天气数据已生成: 城市=" . $mockData['city'] . ", 温度=" . $mockData['temp']);
    
    return $mockData;
}

/**
 * 格式化城市显示名称
 * @param array $location 位置信息数组，包含adm1和name字段
 * @return string 格式化后的城市名称
 */
function formatCityDisplayName($location) {
    if (empty($location) || !is_array($location)) {
        return '未知城市';
    }
    
    $name = $location['name'] ?? '';
    $nameEn = $location['name_en'] ?? '';  // 新增：获取中文名称
    $adm1 = $location['adm1'] ?? '';
    
    // 如果有中文名称，优先使用
    if (!empty($nameEn) && preg_match('/[\x{4e00}-\x{9fa5}]/u', $nameEn)) {
        $name = $nameEn;
    }
    
    if (empty($name)) {
        return '未知城市';
    }
    
    // 检查name是否为英文名，如果是英文名但有中文name_en，则使用name_en
    if (preg_match('/^[a-zA-Z\s]+$/', $name) && !empty($nameEn) && preg_match('/[\x{4e00}-\x{9fa5}]/u', $nameEn)) {
        $name = $nameEn;
    }
    
    // 如果adm1和name相同，或者没有adm1，只返回name（直辖市）
    if (empty($adm1) || $adm1 === $name) {
        // 英文城市名映射
        $englishToChinese = [
            'Beijing' => '北京市',
            'Shanghai' => '上海市',
            'Guangzhou' => '广州市',
            'Shenzhen' => '深圳市',
            'Hangzhou' => '杭州市',
            'Nanjing' => '南京市',
            'Chongqing' => '重庆市',
            'Wuhan' => '武汉市',
            'Tianjin' => '天津市',
            'Chengdu' => '成都市',
            'Suzhou' => '苏州市',
            'Xiamen' => '厦门市',
            'Dalian' => '大连市',
            'Qingdao' => '青岛市',
            'Shenyang' => '沈阳市',
            'Linping' => '临平区'  // 添加临平的映射
        ];
        
        // 检查是否是英文城市名
        if (isset($englishToChinese[$name])) {
            return $englishToChinese[$name];
        }
        
        // 检查是否全是字母（英文名）
        if (preg_match('/^[a-zA-Z\s]+$/', $name)) {
            // 添加默认后缀
            return $name . '市';
        }
        
        return $name;
    }
    
    // 否则返回"省份 城市"的格式
    // 先检查adm1是否为英文
    if (preg_match('/^[a-zA-Z\s]+$/', $adm1)) {
        // 如果是英文，尝试映射成中文
        $provinceMap = [
            'Zhejiang' => '浙江省',
            'Jiangsu' => '江苏省',
            'Anhui' => '安徽省',
            'Fujian' => '福建省',
            'Shandong' => '山东省',
            'Guangdong' => '广东省',
            'Jiangxi' => '江西省',
            'Hubei' => '湖北省',
            'Hunan' => '湖南省',
            'Henan' => '河南省',
            'Hebei' => '河北省',
            'Sichuan' => '四川省',
            'Yunnan' => '云南省',
            'Guizhou' => '贵州省',
            'Shaanxi' => '陕西省',
            'Gansu' => '甘肃省',
            'Ningxia' => '宁夏',
            'Inner Mongolia' => '内蒙古',
            'Heilongjiang' => '黑龙江省',
            'Jilin' => '吉林省',
            'Liaoning' => '辽宁省',
            'CN' => '' // 特殊处理"CN"前缀
        ];
        
        $adm1 = $provinceMap[$adm1] ?? $adm1;
    }
    
    // 检查name是否为英文
    if (preg_match('/^[a-zA-Z\s]+$/', $name)) {
        // 英文城市名映射
        $englishToChinese = [
            'Beijing' => '北京市',
            'Shanghai' => '上海市',
            'Guangzhou' => '广州市',
            'Shenzhen' => '深圳市',
            'Hangzhou' => '杭州市',
            'Nanjing' => '南京市',
            'Chongqing' => '重庆市',
            'Wuhan' => '武汉市',
            'Tianjin' => '天津市',
            'Chengdu' => '成都市',
            'Suzhou' => '苏州市',
            'Xiamen' => '厦门市',
            'Dalian' => '大连市',
            'Qingdao' => '青岛市',
            'Shenyang' => '沈阳市',
            'Linping' => '临平区'  // 添加临平的映射
        ];
        
        $name = $englishToChinese[$name] ?? ($name . '市');
    }
    
    // 如果adm1为空（如CN前缀被移除）或者是常见省市直辖市，只返回城市名
    if (empty($adm1) || in_array($name, ['北京市', '上海市', '天津市', '重庆市'])) {
        return $name;
    }
    
    return $adm1 . ' ' . $name;
}

// 获取请求参数
$cityName = isset($_GET['city']) ? $_GET['city'] : null;
$cityId = isset($_GET['cityid']) ? $_GET['cityid'] : null;
$lat = isset($_GET['lat']) ? $_GET['lat'] : null;
$lon = isset($_GET['lon']) ? $_GET['lon'] : null;
$latitude = isset($_GET['latitude']) ? $_GET['latitude'] : $lat;  // 兼容两种参数名
$longitude = isset($_GET['longitude']) ? $_GET['longitude'] : $lon;

// 新增：检查location参数（经度,纬度格式）
$location = isset($_GET['location']) ? $_GET['location'] : null;
if ($location) {
    // 检查location是否为城市ID格式（纯数字且通常为9位数）
    if (preg_match('/^[0-9]{9}$/', $location)) {
        // 如果是城市ID格式，直接作为cityId使用
        $cityId = $location;
        writeDetailedLog('PARAM_PARSED', "从location参数解析城市ID", [
            'location' => $location,
            'parsed_city_id' => $cityId
        ]);
    } 
    // 如果不是城市ID，尝试作为经纬度解析
    elseif (!$latitude || !$longitude) {
        // 尝试解析location参数（格式：经度,纬度）
        $locationParts = explode(',', $location);
        if (count($locationParts) === 2) {
            $longitude = trim($locationParts[0]);
            $latitude = trim($locationParts[1]);
            writeDetailedLog('PARAM_PARSED', "从location参数解析经纬度", [
                'location' => $location,
                'parsed_longitude' => $longitude,
                'parsed_latitude' => $latitude
            ]);
        }
    }
}

// 日志请求信息
writeLog("收到请求: city=$cityName, cityid=$cityId, lat=$latitude, lon=$longitude, location=$location");

// 记录详细参数信息，用于调试
writeDetailedLog('REQUEST_PARAMS', "详细请求参数", [
    'city' => $cityName,
    'cityid' => $cityId,
    'lat' => $lat,
    'lon' => $lon,
    'latitude' => $latitude,
    'longitude' => $longitude,
    'location' => $location,
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
    'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'query_string' => $_SERVER['QUERY_STRING'] ?? ''
]);

// 重要：确保不使用静态测试数据
$useRealTimeData = true; // 强制使用实时数据，而非静态数据

// 如果有经纬度但没有城市名，先尝试反查城市信息
if ($latitude && $longitude && !$cityId) {
    writeLog("尝试通过经纬度($latitude,$longitude)反查城市信息");
    
    // 使用新的城市搜索API进行查询
    $cityApiUrl = API_DOMAIN . '/login_backend/get_city_by_location.php?latitude=' . $latitude . '&longitude=' . $longitude;
    
    writeDetailedLog('CITY_API_REQUEST', "调用城市查询API", [
        'url' => $cityApiUrl,
        'latitude' => $latitude,
        'longitude' => $longitude
    ]);
    
    // 发送请求到城市查询API
    $ch = curl_init($cityApiUrl);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 5,
        CURLOPT_SSL_VERIFYPEER => false, // 开发环境可设为false
        CURLOPT_SSL_VERIFYHOST => 0,     // 开发环境可设为0
        CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4 // 强制使用IPv4
    ]);
    
    $cityApiResponse = curl_exec($ch);
    $cityApiHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $cityApiError = curl_error($ch);
    curl_close($ch);
    
    // 检查响应
    if ($cityApiHttpCode === 200 && !$cityApiError) {
        $cityApiData = json_decode($cityApiResponse, true);
        
        if ($cityApiData && isset($cityApiData['success']) && $cityApiData['success'] && isset($cityApiData['city'])) {
            // 从API响应中提取城市信息
            $cityId = $cityApiData['city']['id'];
            $cityName = $cityApiData['city']['name'];
            $cityFullName = $cityApiData['city']['fullName'] ?? $cityName;
            
            // 新增：保存中文城市名称到全局变量，以便后续使用
            $GLOBALS['chinese_city_name'] = $cityName;
            $GLOBALS['chinese_city_full_name'] = $cityFullName;
            
            writeLog("经纬度反查城市成功（新API）: ID=$cityId, 名称=$cityName");
            writeDetailedLog('CITY_API_SUCCESS', "城市查询API成功", [
                'city_id' => $cityId,
                'city_name' => $cityName,
                'full_name' => $cityFullName
            ]);
            
            // 重要修改：更新locationParam使用查询到的城市ID
            $locationParam = $cityId;
            writeLog("更新位置参数为城市ID: $locationParam");
        } else {
            writeLog("经纬度反查城市失败（新API）: " . ($cityApiData['message'] ?? '未知错误'));
            writeDetailedLog('CITY_API_ERROR', "城市查询API返回错误", [
                'response' => $cityApiResponse,
                'decoded_data' => $cityApiData
            ]);
            
            // 如果新API失败，回退到旧方法
            $cityInfo = getCityByCoordinates($latitude, $longitude);
            
            if ($cityInfo && !empty($cityInfo['id'])) {
                $cityId = $cityInfo['id'];
                $cityName = $cityInfo['name'];
                writeLog("回退到旧方法反查城市成功: ID=$cityId, 名称=$cityName");
                
                // 新增：保存中文城市名称到全局变量
                $GLOBALS['chinese_city_name'] = $cityName;
                
                // 重要修改：更新locationParam使用回退方法查询到的城市ID
                $locationParam = $cityId;
                writeLog("更新位置参数为城市ID（回退方法）: $locationParam");
            } else {
                writeLog("经纬度反查城市完全失败");
            }
        }
    } else {
        writeLog("调用城市查询API失败: HTTP=$cityApiHttpCode, Error=" . ($cityApiError ?: '无'));
        writeDetailedLog('CITY_API_HTTP_ERROR', "城市查询API请求失败", [
            'http_code' => $cityApiHttpCode,
            'curl_error' => $cityApiError
        ]);
        
        // 如果API调用失败，回退到旧方法
        $cityInfo = getCityByCoordinates($latitude, $longitude);
        
        if ($cityInfo && !empty($cityInfo['id'])) {
            $cityId = $cityInfo['id'];
            $cityName = $cityInfo['name'];
            writeLog("回退到旧方法反查城市成功: ID=$cityId, 名称=$cityName");
            
            // 新增：保存中文城市名称到全局变量
            $GLOBALS['chinese_city_name'] = $cityName;
            
            // 重要修改：更新locationParam使用回退方法查询到的城市ID
            $locationParam = $cityId;
            writeLog("更新位置参数为城市ID（回退方法）: $locationParam");
        } else {
            writeLog("经纬度反查城市完全失败");
        }
    }
}

// 确定查询位置参数
$locationParam = '';
$useDefaultLocation = false;

// 优先级：城市ID > 经纬度 > 城市名称 > 默认城市
// 修改优先级顺序，确保城市ID优先使用
if ($cityId) {
    // 首先使用城市ID
    $locationParam = $cityId;
    writeLog("使用城市ID查询: $cityId");
    
    // 添加详细日志
    writeDetailedLog('CITY_ID_PARAM', "使用城市ID参数", [
        'city_id' => $cityId,
        'source' => isset($_GET['cityid']) ? 'cityid参数' : (isset($_GET['location']) && preg_match('/^[0-9]{9}$/', $_GET['location']) ? 'location参数' : '城市转换')
    ]);
} elseif ($latitude && $longitude) {
    // 其次使用经纬度
    // 注意：和风天气API要求经纬度格式为"经度,纬度"
    $locationParam = $longitude . ',' . $latitude;
    
    // 移除：不要添加额外参数到locationParam
    // $locationParam .= '&exact_lat=' . $latitude . '&exact_lon=' . $longitude;
    
    writeLog("使用经纬度查询 (格式：经度,纬度): $locationParam");
    
    // 添加详细日志
    writeDetailedLog('COORDS_PARAM', "经纬度参数格式化", [
        'raw_latitude' => $latitude,
        'raw_longitude' => $longitude,
        'formatted_location' => $locationParam
    ]);
} elseif ($cityName) {
    // 尝试通过城市名获取城市ID
    $tempCityId = getCityIdByName($cityName);
    if ($tempCityId) {
        $locationParam = $tempCityId;
        $cityId = $tempCityId; // 更新城市ID
        writeLog("城市名转换为ID: $cityName => $cityId");
    } else {
        // 如果没有找到城市ID，直接使用城市名
        $locationParam = $cityName;
        writeLog("使用城市名查询: $cityName");
    }
} else {
    // 不再默认使用杭州，而是提供一个明确的错误响应
    $useDefaultLocation = true;
    
    // 记录警告信息
    writeLog("未提供任何位置参数，请求端需要更新以提供位置信息");
    
    // 尝试使用IP地址获取大致位置
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    if ($clientIP && $clientIP !== '127.0.0.1' && $clientIP !== 'localhost') {
        writeLog("尝试通过IP地址获取位置: $clientIP");
        // 这里可以添加IP地理位置查询逻辑
        // 简单起见，目前仍使用配置的默认位置
    }
    
    // 使用配置的默认位置，但不再写死为杭州
    $locationParam = WEATHER_DEFAULT_LOCATION;
    $cityId = WEATHER_DEFAULT_LOCATION;
    writeLog("使用默认城市ID（配置值）: " . WEATHER_DEFAULT_LOCATION);
}

// 记录日志
writeDetailedLog('REQUEST', "请求天气数据", [
    'longitude' => $longitude,
    'latitude' => $latitude,
    'location_param' => $locationParam,
    'api_key' => substr(WEATHER_API_KEY, 0, 6) . '...' . substr(WEATHER_API_KEY, -4),
    'api_host' => defined('WEATHER_API_USE_PROD') && WEATHER_API_USE_PROD ? WEATHER_API_HOST_PROD : WEATHER_API_HOST
]);

// 检查缓存
$cachedWeatherData = getWeatherFromCache($locationParam);
if ($cachedWeatherData) {
    writeLog("使用缓存的天气数据");
    
    // 添加额外调试信息
    $cachedWeatherData['_debug'] = [
        'source' => 'cache',
        'timestamp' => time(),
        'cache_file' => md5($locationParam) . '.json',
        'cache_time' => filemtime(WEATHER_CACHE_DIR . '/' . md5($locationParam) . '.json'),
        'cache_age_seconds' => time() - filemtime(WEATHER_CACHE_DIR . '/' . md5($locationParam) . '.json')
    ];
    
    // 记录缓存天气API诊断信息
    logWeatherDiagnostics('CACHE', $cachedWeatherData, [
        'longitude' => $longitude,
        'latitude' => $latitude,
        'location_param' => $locationParam,
        'cache_file' => WEATHER_CACHE_DIR . '/' . md5($locationParam) . '.json'
    ]);
    
    // 返回缓存结果
    echo json_encode([
        'success' => true,
        'data' => $cachedWeatherData,
        'cached' => true,
        'is_default_location' => $useDefaultLocation
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取城市信息（用于后续处理，如果是通过ID查询）
$cityInfo = null;
if ($cityId) {
    $cityInfo = getCityInfoById($cityId);
    if ($cityInfo) {
        writeLog("城市信息: " . json_encode($cityInfo, JSON_UNESCAPED_UNICODE));
    }
}

// 构建直接请求和风天气API的URL
$apiHost = 'https://';
if (defined('WEATHER_API_USE_PROD') && WEATHER_API_USE_PROD && defined('WEATHER_API_HOST_PROD')) {
    $apiHost .= WEATHER_API_HOST_PROD;
    writeLog("使用正式环境API: " . WEATHER_API_HOST_PROD);
    
    // 添加详细日志
    writeDetailedLog('CONFIG', "使用正式环境API", [
        'api_host' => WEATHER_API_HOST_PROD,
        'api_key' => substr(WEATHER_API_KEY, 0, 6) . '...' . substr(WEATHER_API_KEY, -4)
    ]);
} else {
    $apiHost .= WEATHER_API_HOST;
    writeLog("使用开发环境API: " . WEATHER_API_HOST);
    
    // 添加详细日志
    writeDetailedLog('CONFIG', "使用开发环境API", [
        'api_host' => WEATHER_API_HOST,
        'api_key' => substr(WEATHER_API_KEY, 0, 6) . '...' . substr(WEATHER_API_KEY, -4)
    ]);
}
$apiPath = WEATHER_API_PATH;

// 检查位置参数格式，确保其正确性
if ($latitude && $longitude && strpos($locationParam, '&') !== false) {
    // 检测到包含&字符的经纬度参数，说明格式可能有误
    // 重置为正确格式：经度,纬度
    $locationParam = $longitude . ',' . $latitude;
    writeLog("检测到位置参数格式错误，已重置为: $locationParam");
}

$queryParams = [
    'location' => $locationParam,
    'key' => WEATHER_API_KEY,
    'lang' => 'zh',  // 使用中文
    'unit' => 'm',    // 使用公制单位
    't' => time()  // 添加时间戳，确保每次请求都是唯一的
];
$queryString = http_build_query($queryParams);
$weatherApiUrl = $apiHost . $apiPath . '?' . $queryString;

writeLog("直接请求和风天气API: $weatherApiUrl");

// 标记API请求开始时间，用于计算耗时
$apiRequestStartTime = microtime(true);

// 记录API请求详细信息
writeDetailedLog('API_REQUEST_DETAILED', "天气API详细请求信息", [
    'url' => $weatherApiUrl,
    'method' => 'GET',
    'host' => parse_url($apiHost, PHP_URL_HOST),
    'path' => $apiPath,
    'params' => $queryParams,
    'location_param' => $locationParam,
    'start_time' => date('Y-m-d H:i:s.u'),
    'location_type' => $latitude && $longitude ? 'coordinates' : ($cityId ? 'city_id' : ($cityName ? 'city_name' : 'default'))
]);

// 设置请求头
$headers = [
    'User-Agent: WeatherApp/1.0',
    'Referer: https://cyyg.alidog.cn'  // 添加Referer头，解决403域名限制问题
];

// 添加详细日志
writeLog("使用请求头: " . json_encode($headers));

// 发送API请求
$ch = curl_init($weatherApiUrl);
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_SSL_VERIFYPEER => false, // 禁用SSL验证以解决证书问题
    CURLOPT_SSL_VERIFYHOST => 0,     // 不验证主机名
    CURLOPT_ENCODING => 'gzip', // 支持gzip压缩
    CURLOPT_HTTPHEADER => $headers,
    CURLOPT_FOLLOWLOCATION => true, // 跟随重定向
    CURLOPT_VERBOSE => true, // 启用详细输出以便调试
    CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4 // 强制使用IPv4
]);

// 添加详细日志 - API请求
writeDetailedLog('API_REQUEST', "发送API请求", [
    'url' => $weatherApiUrl,
    'headers' => $headers,
    'use_prod' => defined('WEATHER_API_USE_PROD') ? WEATHER_API_USE_PROD : false
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
$info = curl_getinfo($ch);
curl_close($ch);

// 计算API请求耗时
$apiRequestEndTime = microtime(true);
$apiRequestDuration = round(($apiRequestEndTime - $apiRequestStartTime) * 1000, 2); // 毫秒

// 添加详细日志 - API响应
writeDetailedLog('API_RESPONSE', "API请求结果", [
    'http_code' => $httpCode,
    'error' => $error ?: "无",
    'response_preview' => substr($response, 0, 200),
    'duration_ms' => $apiRequestDuration,
    'is_success' => ($httpCode === 200 && !$error)
]);

// 添加API响应详细信息
writeDetailedLog('API_RESPONSE_DETAILED', "天气API详细响应信息", [
    'url' => $weatherApiUrl,
    'http_code' => $httpCode,
    'error' => $error ?: "无",
    'duration_ms' => $apiRequestDuration,
    'content_type' => $info['content_type'] ?? 'unknown',
    'size_download' => $info['size_download'] ?? 0,
    'response_code' => 'unknown', // 此时$result尚未定义，移除对它的引用
    'is_valid_json' => json_decode($response) !== null, // 使用直接检查而非依赖$result
    'has_weather_data' => strpos($response, '"now"') !== false, // 简单检查是否包含now字段
    'weather_summary' => null // 移除对$result的依赖
]);

// 记录详细的API请求信息
writeLog("API请求结果: HTTP状态码=$httpCode, 错误=" . ($error ?: "无"));
writeLog("API响应信息: " . json_encode($info, JSON_UNESCAPED_UNICODE));
writeLog("API原始响应: " . substr($response, 0, 1000));

// 检查响应
if ($httpCode !== 200 || $error) {
    writeLog("请求失败: " . ($error ?: "HTTP状态码: $httpCode"));
    
    // 尝试使用备用密钥
    writeLog("尝试使用备用API密钥");
    $queryParams['key'] = WEATHER_API_KEY_ALTERNATIVE;
    $queryString = http_build_query($queryParams);
    
    // 如果第一次请求失败，尝试切换到正式环境
    if (defined('WEATHER_API_USE_PROD') && !WEATHER_API_USE_PROD && defined('WEATHER_API_HOST_PROD')) {
        $apiHost = 'https://' . WEATHER_API_HOST_PROD;
        writeLog("第一次请求失败，尝试切换到正式环境API: " . WEATHER_API_HOST_PROD);
        
        // 添加详细日志
        writeDetailedLog('FALLBACK', "第一次请求失败，尝试切换到正式环境API", [
            'api_host' => WEATHER_API_HOST_PROD,
            'alternative_key' => substr(WEATHER_API_KEY_ALTERNATIVE, 0, 6) . '...' . substr(WEATHER_API_KEY_ALTERNATIVE, -4)
        ]);
    }
    
    $weatherApiUrl = $apiHost . $apiPath . '?' . $queryString;
    
    // 添加详细日志 - 备用API请求
    writeDetailedLog('FALLBACK_REQUEST', "使用备用密钥发送API请求", [
        'url' => $weatherApiUrl,
        'headers' => $headers
    ]);
    
    $ch = curl_init($weatherApiUrl);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false, // 禁用SSL验证
        CURLOPT_SSL_VERIFYHOST => 0,     // 不验证主机名
        CURLOPT_ENCODING => 'gzip',
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_VERBOSE => true, // 启用详细输出
        CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4 // 强制使用IPv4
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    writeLog("备用API请求结果: HTTP状态码=$httpCode, 错误=" . ($error ?: "无"));
    
    // 如果备用方法也失败，返回错误
    if ($httpCode !== 200 || $error) {
        writeLog("备用API请求也失败，使用模拟数据");
        
        // 生成模拟数据
        $weatherData = getMockWeatherData($cityName, $cityId, $locationParam);
        
        // 记录模拟天气API诊断信息
        logWeatherDiagnostics('MOCK', $weatherData, [
            'longitude' => $longitude,
            'latitude' => $latitude,
            'location_param' => $locationParam,
            'reason' => '主API和备用API均请求失败'
        ], [
            'http_code' => $httpCode,
            'error' => $error ?: "无"
        ]);
        
        // 返回模拟数据
        echo json_encode([
            'success' => true,
            'data' => $weatherData,
            'mocked' => true
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// 记录API返回的原始数据
writeLog("API响应原始数据: " . substr($response, 0, 500) . (strlen($response) > 500 ? '...' : ''));

// 解析响应
$result = json_decode($response, true);
if (!$result) {
    writeLog("响应格式无效");
    echo json_encode([
        'success' => false,
        'message' => '天气数据格式无效',
        'city' => $cityName ?: ($cityInfo['name'] ?? '未知城市'),
        'cityid' => $cityId ?: $locationParam
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查API返回状态
if (isset($result['code']) && $result['code'] != '200') {
    writeLog("API返回错误: " . $result['code']);
    echo json_encode([
        'success' => false,
        'message' => '天气API返回错误: ' . $result['code'],
        'city' => $cityName ?: ($cityInfo['name'] ?? '未知城市'),
        'cityid' => $cityId ?: $locationParam
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 构建符合旧格式的响应数据
$weatherData = [];

// 添加基础天气信息
if (isset($result['now'])) {
    // 复制now对象的所有数据
    $weatherData = $result['now'];
    
    // 复制顶层数据
    $weatherData['updateTime'] = $result['updateTime'] ?? date('Y-m-d\TH:i:sP');
    $weatherData['fxLink'] = $result['fxLink'] ?? '';
    
    // 添加强制刷新标识
    $weatherData['_realtime'] = true;
    $weatherData['_timestamp'] = time();
}

// 处理城市信息
$cityDisplayName = '';
$hasCityInfo = false;

// 最后，如果是经纬度查询但没有城市名，再次尝试反查城市名
if ($latitude && $longitude && !$cityId) {  // 添加检查，只有在没有城市ID时才尝试反查
    // 构建地理编码API Host
    $geoHost = "";
    
    // 使用配置中的Geo API Host
    if (defined('WEATHER_GEO_API_HOST')) {
        $geoHost = WEATHER_GEO_API_HOST;
    } 
    // 备选：从当前API Host构建Geo API Host
    else if (strpos(WEATHER_API_HOST, 're.qweatherapi.com') !== false) {
        // 使用正则表达式提取前缀，更安全
        preg_match('/^([^\.]+)\./', WEATHER_API_HOST, $matches);
        if (!empty($matches[1])) {
            $prefix = $matches[1];
        $geoHost = 'geo-' . $prefix . '.re.qweatherapi.com';
        } else {
            // 如果无法提取前缀，使用默认值
            $geoHost = "geoapi.qweather.com";
        }
    } else {
        $geoHost = "geoapi.qweather.com";
    }
    
    // 使用配置中的Geo API Path
    $geoApiPath = defined('WEATHER_GEO_API_PATH') ? WEATHER_GEO_API_PATH : '/geo/v2/city/lookup';
    
    // 构建地理编码API URL - 注意：经度在前，纬度在后
    $geoApiUrl = "https://" . $geoHost . $geoApiPath . "?location=$longitude,$latitude&key=" . WEATHER_API_KEY;
    writeLog("尝试使用地理编码API反查城市: $geoApiUrl");
    
    $ch = curl_init($geoApiUrl);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 5,
        CURLOPT_SSL_VERIFYPEER => false, // 禁用SSL验证
        CURLOPT_SSL_VERIFYHOST => 0,      // 不验证主机名
        CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4, // 强制使用IPv4
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Accept-Encoding: gzip',
            'Referer: https://cyyg.alidog.cn',
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36'
        ]
    ]);
    
    $geoResponse = curl_exec($ch);
    $geoHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($geoHttpCode === 200) {
        $geoResult = json_decode($geoResponse, true);
        if ($geoResult && isset($geoResult['location']) && is_array($geoResult['location']) && count($geoResult['location']) > 0) {
            $location = $geoResult['location'][0];
            // 使用统一的城市名称格式化函数
            $cityDisplayName = formatCityDisplayName($location);
            
                // 保存城市ID
                $cityId = $location['id'] ?? $cityId;
                $hasCityInfo = true;
                writeLog("地理编码反查成功: 名称=$cityDisplayName, ID=$cityId");
        } else if ($geoResult && isset($geoResult['data']) && is_array($geoResult['data']) && count($geoResult['data']) > 0) {
            // 处理proxy_geo_api.php的自定义格式
            $location = $geoResult['data'][0];
            $cityDisplayName = formatCityDisplayName($location);
            
                // 保存城市ID
                $cityId = $location['id'] ?? $cityId;
                $hasCityInfo = true;
                writeLog("自定义格式地理编码反查成功: 名称=$cityDisplayName, ID=$cityId");
        }
    }
    
    // 如果地理编码API失败，尝试使用城市工具函数
    if (empty($cityDisplayName)) {
        $citySuggestion = getCityByCoordinates($latitude, $longitude);
        if ($citySuggestion && isset($citySuggestion['name'])) {
            $cityDisplayName = formatCityDisplayName($citySuggestion);
            $cityId = $citySuggestion['id'] ?? $cityId;
            $hasCityInfo = true;
            writeLog("使用本地工具反查城市成功: 名称=$cityDisplayName, ID=$cityId");
            
            // 重要：确保locationParam使用城市ID
            if ($cityId && preg_match('/^[0-9]{9}$/', $cityId)) {
                $locationParam = $cityId;
                writeLog("更新位置参数为城市ID（本地工具）: $locationParam");
            }
        }
    }
    
    // 如果所有方法都失败，使用坐标作为城市名
    if (empty($cityDisplayName)) {
        $cityDisplayName = "位置($longitude,$latitude)";
        $hasCityInfo = true;
        writeLog("所有城市反查方法均失败，使用坐标作为城市名");
    }
}

// 如果经纬度查询没有成功获得城市信息，再尝试其他方法
if (!$hasCityInfo) {
    // 确定城市名称的优先级：
    // 1. 如果API返回的结果中包含location，使用其中的城市信息
    // 2. 如果已经有cityInfo信息，使用它
    // 3. 如果有传入的城市名，使用它
    // 4. 否则使用默认城市名

    // 如果响应中包含location数据（地理查询API）
    if (isset($result['location']) && is_array($result['location']) && count($result['location']) > 0) {
        $location = $result['location'][0];
        writeLog("API返回了location信息: " . json_encode($location, JSON_UNESCAPED_UNICODE));
        
        // 使用统一的城市名称格式化函数
        $cityDisplayName = formatCityDisplayName($location);
        
        // 保存城市ID
        $cityId = $location['id'] ?? $cityId;
        writeLog("从API响应中提取城市信息: 名称=$cityDisplayName, ID=$cityId");
    } 
    // 尝试从响应的fxLink中提取城市名称
    elseif (isset($result['fxLink']) && strpos($result['fxLink'], 'qweather.com/weather/') !== false) {
        $fxLink = $result['fxLink'];
        $matches = [];
        if (preg_match('/qweather\.com\/weather\/([^-]+)-(\d+)\.html/', $fxLink, $matches)) {
            $extractedCityName = $matches[1];
            $extractedCityId = $matches[2];
            
            // 使用提取的城市名和ID
            if (!empty($extractedCityName) && $extractedCityName != 'undefined') {
                $cityDisplayName = $extractedCityName;
                
                // 如果提取出的城市ID与使用的不同，使用提取的ID
                if (!empty($extractedCityId) && $extractedCityId != $cityId) {
                    $cityId = $extractedCityId;
                    writeLog("从fxLink提取城市信息: 名称=$cityDisplayName, ID=$cityId");
                }
            }
        }
    }
    // 如果没有location数据，但有现有的城市信息
    elseif ($cityInfo) {
        $cityDisplayName = formatCityDisplayName($cityInfo);
        writeLog("使用预先获取的城市信息: 名称=$cityDisplayName");
    }
    // 如果有传入的城市名，直接使用
    elseif (!empty($cityName)) {
        $cityDisplayName = $cityName;
        writeLog("使用传入的城市名: $cityDisplayName");
    }
    // 如果所有方法都失败，使用默认
    else {
        $cityDisplayName = '未知城市';
        writeLog("无法确定城市，使用默认名称");
    }
}

// 添加城市名和ID到结果中
// 优先使用从城市查询API获取的中文名称
if (isset($GLOBALS['chinese_city_name']) && !empty($GLOBALS['chinese_city_name'])) {
    $weatherData['city'] = isset($GLOBALS['chinese_city_full_name']) && !empty($GLOBALS['chinese_city_full_name']) 
        ? $GLOBALS['chinese_city_full_name'] 
        : $GLOBALS['chinese_city_name'];
    writeLog("使用保存的中文城市名称: " . $weatherData['city']);
} else {
    // 使用从其他途径获取的城市名
    $weatherData['city'] = $cityDisplayName;
}
$weatherData['cityid'] = $cityId ?: $locationParam;

// 添加额外调试信息
$weatherData['_debug'] = [
    'source' => 'api',
    'timestamp' => time(),
    'query_type' => $latitude && $longitude ? 'coordinates' : ($cityId ? 'city_id' : ($cityName ? 'city_name' : 'default')),
    'api_host' => parse_url($apiHost, PHP_URL_HOST),
    'response_time_ms' => $apiRequestDuration ?? 0,
    'api_version' => 'v7'
];

// 记录成功响应
writeLog("成功获取天气数据: 城市=" . $weatherData['city'] . ", 温度=" . ($weatherData['temp'] ?? '未知'));

// 保存到缓存
if (!isset($weatherData['_is_mock'])) {
    saveWeatherToCache($locationParam, $weatherData);
    writeLog("天气数据已保存到缓存");
}

// 返回结果
echo json_encode([
    'success' => true,
    'data' => $weatherData,
    'is_default_location' => $useDefaultLocation
], JSON_UNESCAPED_UNICODE);

// 记录天气API诊断信息
logWeatherDiagnostics('API', $weatherData, [
    'longitude' => $longitude,
    'latitude' => $latitude,
    'location_param' => $locationParam,
    'api_key' => substr(WEATHER_API_KEY, 0, 6) . '...' . substr(WEATHER_API_KEY, -4),
    'api_host' => defined('WEATHER_API_USE_PROD') && WEATHER_API_USE_PROD ? WEATHER_API_HOST_PROD : WEATHER_API_HOST
], [
    'http_code' => $httpCode,
    'error' => $error ?: "无"
]);

/**
 * 特殊路由: 运行API诊断
 * 如果URL参数中包含diagnose=true，则执行API诊断测试
 */
if (isset($_GET['diagnose']) && $_GET['diagnose'] === 'true') {
    // 确保这是一个特权请求
    $isPrivilegedRequest = false;
    
    // 检查请求是否来自已知IP或包含特定头
    $knownIPs = ['127.0.0.1', '::1', '***********']; // 可以配置您的内部IP
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    
    if (in_array($clientIP, $knownIPs) || isset($_GET['admin_key']) && $_GET['admin_key'] === md5(ADMIN_SECRET_KEY)) {
        $isPrivilegedRequest = true;
    }
    
    if (!$isPrivilegedRequest) {
        echo json_encode([
            'success' => false,
            'message' => '无权访问诊断工具'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 检查是否要清除缓存
    if (isset($_GET['clear_cache']) && $_GET['clear_cache'] === 'true') {
        clearWeatherCache();
        echo json_encode([
            'success' => true,
            'message' => '天气缓存已清除'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 执行API诊断测试
    runWeatherApiDiagnostics();
    exit;
}

/**
 * 清除所有天气缓存
 * @return int 清除的文件数量
 */
function clearWeatherCache() {
    $cacheDir = WEATHER_CACHE_DIR;
    $count = 0;
    
    if (!is_dir($cacheDir)) {
        writeLog("缓存目录不存在: " . $cacheDir);
        return 0;
    }
    
    $files = glob($cacheDir . '/*.json');
    if ($files === false) {
        writeLog("无法获取缓存文件列表");
        return 0;
    }
    
    foreach ($files as $file) {
        if (is_file($file) && unlink($file)) {
            writeLog("已删除缓存文件: " . basename($file));
            $count++;
        }
    }
    
    writeLog("天气缓存已清除，共删除 $count 个文件");
    return $count;
}

/**
 * 运行和风天气API诊断测试
 * 测试不同的API端点和参数组合
 */
function runWeatherApiDiagnostics() {
    header('Content-Type: text/html; charset=utf-8');
    
    echo "<html><head><title>和风天气API诊断</title>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        .test { margin-bottom: 20px; border: 1px solid #ccc; padding: 10px; border-radius: 5px; }
        .success { background-color: #dff0d8; border-color: #d6e9c6; }
        .error { background-color: #f2dede; border-color: #ebccd1; }
        .pending { background-color: #fcf8e3; border-color: #faebcc; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; }
        .btn { display: inline-block; padding: 6px 12px; margin-bottom: 0; font-size: 14px; font-weight: 400; 
               line-height: 1.42857143; text-align: center; white-space: nowrap; vertical-align: middle; 
               cursor: pointer; background-image: none; border: 1px solid transparent; border-radius: 4px; 
               text-decoration: none; }
        .btn-primary { color: #fff; background-color: #337ab7; border-color: #2e6da4; }
        .btn-danger { color: #fff; background-color: #d9534f; border-color: #d43f3a; }
        .btn-warning { color: #fff; background-color: #f0ad4e; border-color: #eea236; }
        .btn:hover { opacity: 0.9; }
        .toolbar { margin-bottom: 20px; }
    </style></head><body>";
    
    echo "<h1>和风天气API诊断工具</h1>";
    echo "<p>时间: " . date('Y-m-d H:i:s') . "</p>";
    
    // 添加工具栏
    echo "<div class='toolbar'>";
    echo "<a href='get_weather.php?diagnose=true&clear_cache=true' class='btn btn-danger'>清除天气缓存</a> ";
    echo "<a href='get_weather.php?diagnose=true' class='btn btn-primary'>刷新诊断</a> ";
    
    // 添加缓存状态检查
    $cacheFiles = glob(WEATHER_CACHE_DIR . '/*.json');
    $cacheCount = count($cacheFiles);
    $cacheStatus = "当前缓存文件数: $cacheCount";
    if ($cacheCount > 0) {
        $latestCacheFile = max($cacheFiles, function($a, $b) {
            return filemtime($a) - filemtime($b);
        });
        $latestCacheTime = date('Y-m-d H:i:s', filemtime($latestCacheFile));
        $cacheStatus .= ", 最新缓存时间: $latestCacheTime";
    }
    echo "<p>$cacheStatus</p>";
    echo "</div>";
    
    // 测试用例
    $testCases = [
        [
            'name' => '默认城市天气',
            'params' => []
        ],
        [
            'name' => '通过城市ID查询 (北京)',
            'params' => ['cityid' => '101010100']
        ],
        [
            'name' => '通过城市名称查询 (上海)',
            'params' => ['city' => '上海']
        ],
        [
            'name' => '通过经纬度查询 (广州)',
            'params' => ['longitude' => '113.2644', 'latitude' => '23.1291']
        ],
        [
            'name' => '通过用户当前经纬度查询 (杭州)',
            'params' => ['longitude' => '120.298501', 'latitude' => '30.41875']
        ],
        [
            'name' => '强制刷新缓存的经纬度查询',
            'params' => ['longitude' => '120.298501', 'latitude' => '30.41875', '_nocache' => time()]
        ]
    ];
    
    // 获取API配置信息
    echo "<div class='test'>";
    echo "<h2>API配置信息</h2>";
    echo "<pre>";
    echo "WEATHER_API_KEY: " . (defined('WEATHER_API_KEY') ? substr(WEATHER_API_KEY, 0, 6) . '...' . substr(WEATHER_API_KEY, -4) : '未定义') . "\n";
    echo "WEATHER_API_HOST: " . (defined('WEATHER_API_HOST') ? WEATHER_API_HOST : '未定义') . "\n";
    echo "WEATHER_API_HOST_PROD: " . (defined('WEATHER_API_HOST_PROD') ? WEATHER_API_HOST_PROD : '未定义') . "\n";
    echo "WEATHER_API_USE_PROD: " . (defined('WEATHER_API_USE_PROD') ? (WEATHER_API_USE_PROD ? 'true' : 'false') : '未定义') . "\n";
    echo "WEATHER_GEO_API_HOST: " . (defined('WEATHER_GEO_API_HOST') ? WEATHER_GEO_API_HOST : '未定义') . "\n";
    echo "当前使用的API Host: " . (defined('WEATHER_API_USE_PROD') && WEATHER_API_USE_PROD && defined('WEATHER_API_HOST_PROD') ? WEATHER_API_HOST_PROD : (defined('WEATHER_API_HOST') ? WEATHER_API_HOST : '未定义')) . "\n";
    echo "</pre>";
    echo "</div>";
    
    // 执行测试
    foreach ($testCases as $testCase) {
        echo "<div class='test pending' id='test-" . md5(json_encode($testCase)) . "'>";
        echo "<h2>" . htmlspecialchars($testCase['name']) . "</h2>";
        echo "<p>参数: " . json_encode($testCase['params'], JSON_UNESCAPED_UNICODE) . "</p>";
        echo "<p class='status'>状态: 测试中...</p>";
        echo "<div class='result'></div>";
        echo "</div>";
        
        flush();
    }
    
    // JavaScript执行测试
    echo "<script>
    async function runTests() {
        const testCases = " . json_encode($testCases) . ";
        
        for (const testCase of testCases) {
            const testId = 'test-' + MD5(JSON.stringify(testCase));
            const testElement = document.getElementById(testId);
            const resultElement = testElement.querySelector('.result');
            const statusElement = testElement.querySelector('.status');
            
            try {
                statusElement.textContent = '状态: 测试中...';
                
                // 构建参数
                const params = new URLSearchParams(testCase.params);
                params.append('format', 'json');
                
                // 执行请求
                const startTime = new Date().getTime();
                const response = await fetch('get_weather.php?' + params.toString());
                const endTime = new Date().getTime();
                const duration = endTime - startTime;
                
                // 解析响应
                const data = await response.json();
                
                // 显示结果
                resultElement.innerHTML = '<p>响应时间: ' + duration + 'ms</p>' +
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
                if (data.success) {
                    testElement.className = 'test success';
                    statusElement.textContent = '状态: 成功';
                    
                    // 添加天气数据摘要
                    if (data.data) {
                        const weatherSummary = document.createElement('p');
                        weatherSummary.innerHTML = '城市: <strong>' + (data.data.city || '未知') + '</strong>, ' +
                            '温度: <strong>' + (data.data.temp || '未知') + '°C</strong>, ' +
                            '天气: <strong>' + (data.data.text || '未知') + '</strong>';
                            
                        if (data.data._is_mock) {
                            weatherSummary.innerHTML += ' <span style=\"color:red\">(模拟数据)</span>';
                        } else if (data.cached) {
                            weatherSummary.innerHTML += ' <span style=\"color:orange\">(缓存数据)</span>';
                        }
                        
                        resultElement.prepend(weatherSummary);
                    }
                } else {
                    testElement.className = 'test error';
                    statusElement.textContent = '状态: 失败 - ' + (data.message || '未知错误');
                }
            } catch (error) {
                testElement.className = 'test error';
                statusElement.textContent = '状态: 错误 - ' + error.message;
                resultElement.innerHTML = '<pre>请求失败: ' + error.message + '</pre>';
            }
        }
    }
    
    // MD5函数 (简化版，用于生成测试ID)
    function MD5(d){var r = M(V(Y(X(d),8*d.length)));return r.toLowerCase()};function M(d){for(var _,m='0123456789ABCDEF',f='',r=0;r<d.length;r++)_=d.charCodeAt(r),f+=m.charAt(_>>>4&15)+m.charAt(15&_);return f}function X(d){for(var _=Array(d.length>>2),m=0;m<_.length;m++)_[m]=0;for(m=0;m<8*d.length;m+=8)_[m>>5]|=(255&d.charCodeAt(m/8))<<m%32;return _}function V(d){for(var _='',m=0;m<32*d.length;m+=8)_+=String.fromCharCode(d[m>>5]>>>m%32&255);return _}function Y(d,_){d[_>>5]|=128<<_%32,d[14+(_+64>>>9<<4)]=_;for(var m=1732584193,f=-271733879,r=-1732584194,i=271733878,n=0;n<d.length;n+=16){var h=m,t=f,g=r,e=i;f=md5_ii(f=md5_ii(f=md5_ii(f=md5_ii(f=md5_hh(f=md5_hh(f=md5_hh(f=md5_hh(f=md5_gg(f=md5_gg(f=md5_gg(f=md5_gg(f=md5_ff(f=md5_ff(f=md5_ff(f=md5_ff(f,r=md5_ff(r,i=md5_ff(i,m=md5_ff(m,f,r,i,d[n+0],7,-680876936),f,r,d[n+1],12,-389564586),m,f,d[n+2],17,606105819),i,m,d[n+3],22,-1044525330),r=md5_ff(r,i=md5_ff(i,m=md5_ff(m,f,r,i,d[n+4],7,-176418897),f,r,d[n+5],12,1200080426),m,f,d[n+6],17,-1473231341),i,m,d[n+7],22,-45705983),r=md5_ff(r,i=md5_ff(i,m=md5_ff(m,f,r,i,d[n+8],7,1770035416),f,r,d[n+9],12,-1958414417),m,f,d[n+10],17,-42063),i,m,d[n+11],22,-1990404162),r=md5_ff(r,i=md5_ff(i,m=md5_ff(m,f,r,i,d[n+12],7,1804603682),f,r,d[n+13],12,-40341101),m,f,d[n+14],17,-1502002290),i,m,d[n+15],22,1236535329),r=md5_gg(r,i=md5_gg(i,m=md5_gg(m,f,r,i,d[n+1],5,-165796510),f,r,d[n+6],9,-1069501632),m,f,d[n+11],14,643717713),i,m,d[n+0],20,-373897302),r=md5_gg(r,i=md5_gg(i,m=md5_gg(m,f,r,i,d[n+5],5,-701558691),f,r,d[n+10],9,38016083),m,f,d[n+15],14,-660478335),i,m,d[n+4],20,-405537848),r=md5_gg(r,i=md5_gg(i,m=md5_gg(m,f,r,i,d[n+9],5,568446438),f,r,d[n+14],9,-1019803690),m,f,d[n+3],14,-187363961),i,m,d[n+8],20,1163531501),r=md5_gg(r,i=md5_gg(i,m=md5_gg(m,f,r,i,d[n+13],5,-1444681467),f,r,d[n+2],9,-51403784),m,f,d[n+7],14,1735328473),i,m,d[n+12],20,-1926607734),r=md5_hh(r,i=md5_hh(i,m=md5_hh(m,f,r,i,d[n+5],4,-378558),f,r,d[n+8],11,-2022574463),m,f,d[n+11],16,1839030562),i,m,d[n+14],23,-35309556),r=md5_hh(r,i=md5_hh(i,m=md5_hh(m,f,r,i,d[n+1],4,-1530992060),f,r,d[n+4],11,1272893353),m,f,d[n+7],16,-155497632),i,m,d[n+10],23,-1094730640),r=md5_hh(r,i=md5_hh(i,m=md5_hh(m,f,r,i,d[n+13],4,681279174),f,r,d[n+0],11,-358537222),m,f,d[n+3],16,-722521979),i,m,d[n+6],23,76029189),r=md5_hh(r,i=md5_hh(i,m=md5_hh(m,f,r,i,d[n+9],4,-640364487),f,r,d[n+12],11,-421815835),m,f,d[n+15],16,530742520),i,m,d[n+2],23,-995338651),r=md5_ii(r,i=md5_ii(i,m=md5_ii(m,f,r,i,d[n+0],6,-198630844),f,r,d[n+7],10,1126891415),m,f,d[n+14],15,-1416354905),i,m,d[n+5],21,-57434055),r=md5_ii(r,i=md5_ii(i,m=md5_ii(m,f,r,i,d[n+12],6,1700485571),f,r,d[n+3],10,-1894986606),m,f,d[n+10],15,-1051523),i,m,d[n+1],21,-2054922799),r=md5_ii(r,i=md5_ii(i,m=md5_ii(m,f,r,i,d[n+8],6,1873313359),f,r,d[n+15],10,-30611744),m,f,d[n+6],15,-1560198380),i,m,d[n+13],21,1309151649),r=md5_ii(r,i=md5_ii(i,m=md5_ii(m,f,r,i,d[n+4],6,-145523070),f,r,d[n+11],10,-1120210379),m,f,d[n+2],15,718787259),i,m,d[n+9],21,-343485551),m=safe_add(m,h),f=safe_add(f,t),r=safe_add(r,g),i=safe_add(i,e)}return Array(m,f,r,i)}function md5_cmn(d,_,m,f,r,i){return safe_add(bit_rol(safe_add(safe_add(_,d),safe_add(f,i)),r),m)}function md5_ff(d,_,m,f,r,i,n){return md5_cmn(_&m|~_&f,d,_,r,i,n)}function md5_gg(d,_,m,f,r,i,n){return md5_cmn(_&f|m&~f,d,_,r,i,n)}function md5_hh(d,_,m,f,r,i,n){return md5_cmn(_^m^f,d,_,r,i,n)}function md5_ii(d,_,m,f,r,i,n){return md5_cmn(m^(_|~f),d,_,r,i,n)}function safe_add(d,_){var m=(65535&d)+(65535&_);return(d>>16)+(_>>16)+(m>>16)<<16|65535&m}function bit_rol(d,_){return d<<_|d>>>32-_}
    
    // 启动测试
    runTests();
    </script>";
    
    echo "</body></html>";
}

/**
 * 清除特定经纬度的天气缓存
 * @param string $latitude 纬度
 * @param string $longitude 经度
 * @return bool 是否成功清除缓存
 */
function clearLocationWeatherCache($latitude, $longitude) {
    if (empty($latitude) || empty($longitude)) {
        writeLog("清除位置缓存失败: 缺少经纬度参数");
        return false;
    }
    
    // 构建位置参数
    $locationParam = $longitude . ',' . $latitude;
    
    // 计算缓存文件路径
    $cacheFile = WEATHER_CACHE_DIR . '/' . md5($locationParam) . '.json';
    
    if (!file_exists($cacheFile)) {
        writeLog("位置缓存不存在: " . $locationParam);
        return false;
    }
    
    // 删除缓存文件
    if (unlink($cacheFile)) {
        writeLog("成功清除位置缓存: " . $locationParam);
        return true;
    } else {
        writeLog("清除位置缓存失败: " . $locationParam);
        return false;
    }
}

// 在特殊路由部分添加清除特定位置缓存的功能
if (isset($_GET['clear_location_cache']) && $_GET['clear_location_cache'] === 'true') {
    $latitude = isset($_GET['latitude']) ? $_GET['latitude'] : null;
    $longitude = isset($_GET['longitude']) ? $_GET['longitude'] : null;
    
    // 检查权限
    $isPrivilegedRequest = false;
    $knownIPs = ['127.0.0.1', '::1', '***********']; // 可以配置您的内部IP
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    
    if (in_array($clientIP, $knownIPs) || isset($_GET['admin_key']) && $_GET['admin_key'] === md5(ADMIN_SECRET_KEY)) {
        $isPrivilegedRequest = true;
    }
    
    if (!$isPrivilegedRequest) {
        echo json_encode([
            'success' => false,
            'message' => '无权清除缓存'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $result = clearLocationWeatherCache($latitude, $longitude);
    
    echo json_encode([
        'success' => $result,
        'message' => $result ? '成功清除位置缓存' : '清除位置缓存失败',
        'location' => [
            'latitude' => $latitude,
            'longitude' => $longitude
        ]
    ], JSON_UNESCAPED_UNICODE);
    exit;
}
?>