<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证管理员权限
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证管理员token
$adminData = $auth->verifyAdminToken($token);
if (!$adminData) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取数据库连接
$db = new Database();
$conn = $db->getConnection();

// 获取并验证POST数据
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '无效的JSON数据']);
    exit();
}

// 验证必要字段
if (!isset($input['id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少必要参数：id']);
    exit();
}

$id = intval($input['id']);

// 验证穿搭是否存在
$checkQuery = "SELECT id FROM recommended_outfits WHERE id = ?";
$checkStmt = $conn->prepare($checkQuery);
$checkStmt->bindValue(1, $id);
$checkStmt->execute();

if (!$checkStmt->fetch()) {
    http_response_code(404);
    echo json_encode(['error' => true, 'msg' => '找不到指定的推荐穿搭']);
    exit();
}

// 开始数据库事务
try {
    $conn->beginTransaction();
    
    // 删除穿搭的统计记录
    $deleteStatsQuery = "DELETE FROM recommended_outfit_stats WHERE outfit_id = ?";
    $deleteStatsStmt = $conn->prepare($deleteStatsQuery);
    $deleteStatsStmt->bindValue(1, $id);
    $deleteStatsStmt->execute();
    
    // 删除穿搭的商品记录
    $deleteItemsQuery = "DELETE FROM recommended_outfit_items WHERE outfit_id = ?";
    $deleteItemsStmt = $conn->prepare($deleteItemsQuery);
    $deleteItemsStmt->bindValue(1, $id);
    $deleteItemsStmt->execute();
    
    // 删除穿搭记录
    $deleteQuery = "DELETE FROM recommended_outfits WHERE id = ?";
    $deleteStmt = $conn->prepare($deleteQuery);
    $deleteStmt->bindValue(1, $id);
    $deleteStmt->execute();
    
    // 提交事务
    $conn->commit();
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'msg' => '推荐穿搭删除成功',
        'data' => ['id' => $id]
    ]);
    
} catch (PDOException $e) {
    // 回滚事务
    $conn->rollBack();
    
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '数据库错误: ' . $e->getMessage()]);
} 