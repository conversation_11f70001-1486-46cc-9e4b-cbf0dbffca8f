/* pages/outfits/add/add.wxss */

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #ffffff;
}

.form-container {
  flex: 1;
  padding: 30px 24px;
}

.form-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 30px;
  color: #333333;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333333;
}

.form-input {
  width: 100%;
  height: 48px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 0 16px;
  box-sizing: border-box;
  font-size: 16px;
}

/* 分类选择容器 */
.category-select-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 分类选择器 */
.category-picker {
  flex: 1;
  margin-right: 12px;
}

.picker-value {
  height: 48px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 0 16px;
  box-sizing: border-box;
  font-size: 16px;
  display: flex;
  align-items: center;
  background-color: #ffffff;
}

.picker-value.disabled {
  color: #999999;
  background-color: #f5f5f5;
}

/* 新增分类按钮 */
.add-category-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  height: 48px;
  background-color: #f8f8f8;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.add-icon {
  font-size: 18px;
  margin-right: 4px;
  color: #333333;
}

.add-text {
  font-size: 14px;
  color: #333333;
}

.form-textarea {
  width: 100%;
  height: 120px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px 16px;
  box-sizing: border-box;
  font-size: 16px;
}

.form-tips {
  font-size: 14px;
  color: #999999;
  margin-top: 20px;
}

.bottom-buttons {
  display: flex;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
}

.cancel-btn, .submit-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin: 0 8px;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333333;
}

.submit-btn {
  background-color: #000000;
  color: #ffffff;
}

/* 禁用状态的按钮 */
.submit-btn[disabled] {
  opacity: 0.6;
} 