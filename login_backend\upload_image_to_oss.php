<?php
/**
 * Upload Image to OSS API
 * 
 * 专门用于将临时图片上传到OSS，适用于手动上传穿搭衣物但不保存到衣橱的情况
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * POST Parameters:
 * - image_url: 原始图片URL (必需)
 * 
 * Response:
 * {
 *   "error": false,
 *   "data": {
 *     "image_url": "https://cdn-domain.com/oss-path/image.jpg"
 *   }
 * }
 */

require_once 'config.php';
require_once 'auth.php'; // 只引入auth.php，不引入verify_token.php
require_once 'db.php';
require_once '../vendor/autoload.php'; // 引入阿里云OSS SDK
require_once 'oss_helper.php';

// Set response content type
header('Content-Type: application/json');

// Handle CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 记录执行日志
function writeLog($message) {
    $logFile = __DIR__ . '/logs/upload_oss_api.log';
    $logDir = dirname($logFile);
    
    // 确保日志目录存在
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    // 生成日志消息
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
    
    // 写入日志
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// 记录API调用开始
writeLog("API调用开始：upload_image_to_oss.php");

// Check if Authorization header exists
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    writeLog("错误：缺少Authorization头");
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

// Check if the request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    writeLog("错误：不支持的请求方法 " . $_SERVER['REQUEST_METHOD']);
    echo json_encode([
        'error' => true,
        'msg' => 'Only POST method is allowed'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// 处理Bearer前缀，与其他API保持一致
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7); // 去除 "Bearer " 前缀
}

// Verify token
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    writeLog("错误：无效或过期的令牌");
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid or expired token'
    ]);
    exit;
}

// Get user ID from token data
$userId = $tokenData['sub'];
writeLog("用户ID: {$userId}");

// Get JSON POST data
$json = file_get_contents('php://input');
$data = json_decode($json, true);

if (!$data) {
    // If JSON parsing failed, try regular POST data
    $data = $_POST;
}

// 验证图片URL参数
if (empty($data['image_url'])) {
    writeLog("错误：缺少image_url参数");
    echo json_encode([
        'error' => true,
        'msg' => 'image_url is required'
    ]);
    exit;
}

$imageUrl = $data['image_url'];
writeLog("原始图片URL: {$imageUrl}");

// 初始化OSS助手
try {
    $ossHelper = new OssHelper();
    writeLog("OSS助手初始化成功");
} catch (Exception $e) {
    writeLog("OSS助手初始化失败: " . $e->getMessage());
    echo json_encode([
        'error' => true,
        'msg' => 'Failed to initialize OSS helper: ' . $e->getMessage()
    ]);
    exit;
}

// 检查图片URL是否已经是OSS URL
if ($ossHelper->isOssUrl($imageUrl)) {
    writeLog("图片已经是OSS URL，直接返回");
    echo json_encode([
        'error' => false,
        'data' => [
            'image_url' => $imageUrl
        ]
    ]);
    exit;
}

// 将图片从原始URL下载到OSS
$extension = pathinfo($imageUrl, PATHINFO_EXTENSION);
if (empty($extension)) {
    // 如果URL中没有扩展名，默认使用jpg
    $extension = 'jpg';
}

$ossFilename = 'cloth_' . $userId . '_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
$ossKey = 'clothes/' . $ossFilename;

writeLog("准备将图片下载到OSS，Key: {$ossKey}");
$downloadResult = $ossHelper->downloadUrlToOss($imageUrl, $ossKey);

if ($downloadResult['success']) {
    // 上传成功，返回OSS URL
    $ossUrl = $downloadResult['url'];
    writeLog("图片已成功上传到OSS: {$ossUrl}");
    
    echo json_encode([
        'error' => false,
        'data' => [
            'image_url' => $ossUrl
        ]
    ]);
} else {
    // 上传失败，返回错误信息
    writeLog("上传到OSS失败: " . $downloadResult['error']);
    echo json_encode([
        'error' => true,
        'msg' => 'Failed to upload image to OSS: ' . $downloadResult['error']
    ]);
} 