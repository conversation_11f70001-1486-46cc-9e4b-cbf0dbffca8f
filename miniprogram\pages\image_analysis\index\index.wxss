.container {
  background-color: #fff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header {
  position: relative;
  height: 220rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #000 0%, #333 100%);
}

.header-content {
  position: relative;
  z-index: 1;
  padding: 40rpx 40rpx;
  color: #fff;
}

.title {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 30rpx;
}

.section {
  margin-bottom: 50rpx;
}

.section-title {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  color: #333;
}

/* 特点列表 */
.feature-list {
  background: #f8f8f8;
  border-radius: 16rpx;
  overflow: hidden;
}

.feature-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.feature-info {
  flex: 1;
}

.feature-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  color: #333;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 步骤列表 */
.step-list {
  background: #f8f8f8;
  border-radius: 16rpx;
  overflow: hidden;
}

.step-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.step-item:last-child {
  border-bottom: none;
}

.step-number {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background-color: #000;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.step-info {
  flex: 1;
}

.step-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  color: #333;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 获得结果列表 */
.result-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.result-item {
  background: #f8f8f8;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.result-icon {
  font-size: 44rpx;
  margin-bottom: 16rpx;
}

.result-title {
  font-size: 26rpx;
  text-align: center;
  color: #333;
}

/* 价格卡片 */
.price-card {
  background: linear-gradient(135deg, #000 0%, #333 100%);
  border-radius: 16rpx;
  padding: 40rpx;
  color: #fff;
  text-align: center;
}

.price-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.price-value {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.price-desc {
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 10rpx;
}

.price-note {
  font-size: 24rpx;
  opacity: 0.7;
}

/* 底部 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  padding-bottom: calc(30rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
  background-color: #fff;
  z-index: 10;
}

.start-btn {
  background: #000;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  height: 88rpx;
  line-height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.start-btn::after {
  border: none;
}

/* 历史入口 */
.history-entry {
  position: fixed;
  right: 30rpx;
  bottom: 220rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
}

.history-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.history-text {
  font-size: 26rpx;
  color: #333;
} 