<?php
// 引入必要的文件
require_once 'db.php';
require_once 'config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 处理GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'error' => true,
        'msg' => '不支持的请求方法'
    ]);
    exit;
}

// 验证参数
if (!isset($_GET['outfit_id'])) {
    echo json_encode([
        'error' => true,
        'msg' => '缺少穿搭ID参数'
    ]);
    exit;
}

$outfitId = intval($_GET['outfit_id']);
$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : null;

// 记录请求信息，方便调试
$logFile = fopen('logs/outfit_detail_access.log', 'a');
if ($logFile) {
    $timestamp = date('Y-m-d H:i:s');
    $requestInfo = json_encode($_GET);
    fwrite($logFile, "[$timestamp] 请求公开穿搭详情: outfit_id=$outfitId, user_id=$userId, 请求参数=$requestInfo\n");
    fclose($logFile);
}

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();
    
    // 构建查询条件
    $whereClause = "WHERE o.id = :outfit_id AND o.is_public = 1";
    $params = ['outfit_id' => $outfitId];
    
    // 如果指定了用户ID，添加用户过滤条件
    if ($userId) {
        $whereClause .= " AND o.user_id = :user_id";
        $params['user_id'] = $userId;
    }
    
    // 查询公开穿搭详情
    $stmt = $conn->prepare("
        SELECT o.id, o.name, o.description, o.thumbnail_url, o.outfit_data, o.category_id, 
               c.name as category_name, o.created_at, o.updated_at, o.is_public, o.user_id,
               o.likes_count, u.nickname, u.avatar_url, u.gender
        FROM outfits o
        LEFT JOIN outfit_categories c ON o.category_id = c.id
        LEFT JOIN users u ON o.user_id = u.id
        $whereClause
        LIMIT 1
    ");
    
    foreach ($params as $key => $value) {
        $stmt->bindValue(":$key", $value);
    }
    
    $stmt->execute();
    
    $outfit = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($outfit) {
        // 解析JSON数据
        $outfitData = json_decode($outfit['outfit_data'], true);
        
        // 构建返回的穿搭对象
        $resultOutfit = [
            'id' => $outfit['id'],
            'name' => $outfit['name'],
            'description' => $outfit['description'],
            'thumbnail' => $outfit['thumbnail_url'],
            'category_id' => $outfit['category_id'],
            'category_name' => $outfit['category_name'],
            'created_at' => $outfit['created_at'],
            'updated_at' => $outfit['updated_at'],
            'items' => $outfitData['items'] ?? [],
            'is_public' => (int)$outfit['is_public'],
            'user_id' => $outfit['user_id'],
            'likes_count' => (int)$outfit['likes_count'],
            'creator_nickname' => $outfit['nickname'] ?: '匿名用户',
            'creator_avatar' => $outfit['avatar_url'] ?: '',
            'creator_gender' => (int)$outfit['gender']
        ];
        
        // 返回结果
        echo json_encode([
            'success' => true,
            'data' => $resultOutfit
        ]);
    } else {
        echo json_encode([
            'error' => true,
            'msg' => '找不到指定的公开穿搭'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '获取穿搭详情失败: ' . $e->getMessage()
    ]);
} 