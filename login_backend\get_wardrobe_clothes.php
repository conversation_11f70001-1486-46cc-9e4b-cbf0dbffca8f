<?php
/**
 * 获取衣橱中衣物API
 * 
 * 获取指定衣橱中的所有衣物，支持分页
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * GET 参数:
 * - wardrobe_id: 衣橱ID（必填）
 * - category: 分类过滤（选填）
 * - page: 页码（默认1）
 * - per_page: 每页数量（默认10）
 * 
 * 返回:
 * {
 *   "error": false,
 *   "data": [
 *     {
 *       "id": 1,
 *       "name": "黑色T恤",
 *       "category": "上衣",
 *       "image_url": "https://example.com/image.jpg",
 *       "tags": "夏季,休闲",
 *       "created_at": "2023-04-01 12:00:00"
 *     },
 *     ...
 *   ],
 *   "pagination": {
 *     "total": 20,
 *     "page": 1,
 *     "per_page": 10,
 *     "total_pages": 2
 *   },
 *   "wardrobe": {
 *     "id": 1,
 *     "name": "夏季衣橱",
 *     "description": "夏季服装收纳"
 *   }
 * }
 */

// 生成唯一请求ID
$requestId = uniqid('get_ward_clothes_', true);
$startTime = microtime(true);

// 记录到系统日志，便于调试
error_log("[$requestId] 开始处理获取衣橱衣物请求");

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';
require_once 'wardrobe_logger.php';  // 引入衣橱日志工具

// 测试日志系统
if (function_exists('wardrobe_log_test')) {
    $logTestResult = wardrobe_log_test();
    error_log("[$requestId] 日志系统测试: " . ($logTestResult ? "成功" : "失败"));
}

// 设置响应内容类型
header('Content-Type: application/json');

// 记录API请求
wardrobe_log_request('get_wardrobe_clothes');
wardrobe_log_info("[$requestId] 开始处理获取衣橱衣物请求");

// 处理CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否存在Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    wardrobe_log_warning("[$requestId] 缺少Authorization头");
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// 验证token
try {
    $auth = new Auth();
    $tokenVerifyStart = microtime(true);
    $tokenData = $auth->verifyToken($token);
    $tokenVerifyTime = microtime(true) - $tokenVerifyStart;
    wardrobe_log_info("[$requestId] Token验证耗时: " . number_format($tokenVerifyTime * 1000, 2) . "ms");
    
    if (!$tokenData) {
        wardrobe_log_warning("[$requestId] 无效或过期的token");
        echo json_encode([
            'error' => true,
            'msg' => 'Invalid or expired token'
        ]);
        exit;
    }
} catch (Exception $e) {
    wardrobe_log_exception($e, "[$requestId] token验证");
    echo json_encode([
        'error' => true,
        'msg' => 'Token验证失败: ' . $e->getMessage()
    ]);
    exit;
}

// 获取用户ID
$userId = $tokenData['sub'];
wardrobe_log_info("[$requestId] 用户ID: $userId 请求获取衣橱衣物");

// 检查必填参数
if (!isset($_GET['wardrobe_id']) || !is_numeric($_GET['wardrobe_id'])) {
    wardrobe_log_warning("[$requestId] 缺少有效的wardrobe_id参数");
    echo json_encode([
        'error' => true,
        'msg' => 'wardrobe_id参数必须提供且为数字'
    ]);
    exit;
}

$wardrobeId = intval($_GET['wardrobe_id']);

// 获取分页参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$perPage = isset($_GET['per_page']) ? intval($_GET['per_page']) : 10;

// 获取分类过滤
$category = isset($_GET['category']) ? $_GET['category'] : null;

// 验证分页参数
if ($page < 1) {
    $page = 1;
}
if ($perPage < 1 || $perPage > 50) {
    $perPage = 10;
}

wardrobe_log_info("[$requestId] 请求参数: 衣橱ID=$wardrobeId, 分类=" . ($category ?: "全部") . ", 页码=$page, 每页数量=$perPage");

// 计算偏移量
$offset = ($page - 1) * $perPage;

// 在执行查询前添加详细日志
error_log("[$requestId] wardrobeId类型: " . gettype($wardrobeId) . ", 值: $wardrobeId");

try {
    error_log("[$requestId] 尝试创建数据库连接");
    $db = new Database();
    error_log("[$requestId] 数据库对象已创建，尝试获取连接");
    $conn = $db->getConnection();
    error_log("[$requestId] 数据库连接已获取");
    wardrobe_log_info("[$requestId] 数据库连接成功");
    
    // 检查衣橱是否存在且属于当前用户
    $wardrobeCheckSql = "SELECT id, name, description FROM wardrobes WHERE id = :id AND user_id = :user_id";
    wardrobe_log_db('select', $wardrobeCheckSql, ['id' => $wardrobeId, 'user_id' => $userId]);
    
    $wardrobeStmt = $conn->prepare($wardrobeCheckSql);
    $wardrobeStmt->bindParam(':id', $wardrobeId);
    $wardrobeStmt->bindParam(':user_id', $userId);
    
    error_log("[$requestId] 执行衣橱检查查询");
    $wardrobeStmt->execute();
    $wardrobe = $wardrobeStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$wardrobe) {
        wardrobe_log_warning("[$requestId] 衣橱不存在或无权访问: ID=$wardrobeId, 用户ID=$userId");
        echo json_encode([
            'error' => true,
            'msg' => '衣橱不存在或无权访问'
        ]);
        exit;
    }
    
    wardrobe_log_info("[$requestId] 找到衣橱: " . json_encode($wardrobe, JSON_UNESCAPED_UNICODE));
    
    // 构建查询条件 - 修改为只通过wardrobe_id筛选
    $whereClause = "wardrobe_id = :wardrobe_id";
    $params = [
        ':wardrobe_id' => $wardrobeId
    ];
    
    // 添加分类过滤
    if ($category && $category !== 'all') {
        $whereClause .= " AND category = :category";
        $params[':category'] = $category;
    }
    
    // 获取总记录数
    $countSql = "SELECT COUNT(*) FROM clothes WHERE " . $whereClause;
    wardrobe_log_db('select', $countSql, $params);
    
    $countStmt = $conn->prepare($countSql);
    foreach ($params as $param => $value) {
        $countStmt->bindValue($param, $value, PDO::PARAM_INT);  // 显式指定参数类型
    }
    
    error_log("[$requestId] 执行衣物数量查询");
    $countStmt->execute();
    $totalCount = $countStmt->fetchColumn();
    error_log("[$requestId] 查询到衣物数量: $totalCount");
    
    wardrobe_log_info("[$requestId] 查询到总衣物数: $totalCount");
    
    // 计算总页数
    $totalPages = ceil($totalCount / $perPage);
    
    // 获取衣物列表
    $sql = "SELECT id, name, category, image_url, tags, description, created_at 
            FROM clothes 
            WHERE wardrobe_id = :wardrobe_id 
            ORDER BY created_at DESC 
            LIMIT :offset, :per_page";
    
    wardrobe_log_db('select', $sql, array_merge($params, [':offset' => $offset, ':per_page' => $perPage]));
    
    $stmt = $conn->prepare($sql);
    foreach ($params as $param => $value) {
        $stmt->bindValue($param, $value, is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR);  // 根据值类型绑定
    }
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':per_page', $perPage, PDO::PARAM_INT);
    
    error_log("[$requestId] 执行衣物列表查询");
    $stmt->execute();
    $clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    error_log("[$requestId] 获取到 " . count($clothes) . " 件衣物");
    
    wardrobe_log_info("[$requestId] 成功获取衣物列表，返回" . count($clothes) . "条记录");
    
    // 准备响应
    $response = [
        'error' => false,
        'data' => $clothes,
        'pagination' => [
            'total' => $totalCount,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => $totalPages
        ],
        'wardrobe' => $wardrobe
    ];
    
    $totalTime = microtime(true) - $startTime;
    wardrobe_log_info("[$requestId] 获取衣橱衣物请求处理完成，耗时: " . number_format($totalTime * 1000, 2) . "ms");
    error_log("[$requestId] 请求成功完成，耗时: " . number_format($totalTime * 1000, 2) . "ms");
    
    echo json_encode($response);
    
} catch (PDOException $e) {
    // 捕获PDO异常，记录详细信息
    $errorDetails = "错误代码: " . $e->getCode() . 
                  ", 错误信息: " . $e->getMessage() . 
                  ", 文件: " . $e->getFile() . 
                  ", 行号: " . $e->getLine();
    
    error_log("[$requestId] 数据库操作失败: " . $errorDetails);
    wardrobe_log_exception($e, "[$requestId] get_wardrobe_clothes");
    
    // 记录SQL和参数以便调试
    error_log("[$requestId] 失败的SQL: $sql");
    error_log("[$requestId] 参数: " . json_encode($params));
    
    // 尝试简单的连接测试，不调用不存在的方法
    try {
        if (class_exists('Database')) {
            $testDb = new Database();
            error_log("[$requestId] 尝试创建新的数据库连接成功");
            // 可以尝试执行一个简单的SQL来测试连接
            $testConn = $testDb->getConnection();
            $testResult = $testConn->query("SELECT 1")->fetch();
            error_log("[$requestId] 测试查询结果: " . json_encode($testResult));
        }
    } catch (Exception $testEx) {
        error_log("[$requestId] 数据库测试失败: " . $testEx->getMessage());
    }
    
    $totalTime = microtime(true) - $startTime;
    wardrobe_log_info("[$requestId] 请求处理失败，耗时: " . number_format($totalTime * 1000, 2) . "ms");
    
    echo json_encode([
        'error' => true,
        'msg' => '获取衣物失败',
        // 在开发环境下可以提供更多错误详情
        'debug_info' => [
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'request_id' => $requestId
        ]
    ]);
} catch (Exception $e) {
    // 捕获其他异常
    error_log("[$requestId] 非数据库异常: " . $e->getMessage());
    wardrobe_log_exception($e, "[$requestId] get_wardrobe_clothes_non_db");
    
    $totalTime = microtime(true) - $startTime;
    wardrobe_log_info("[$requestId] 请求处理失败，耗时: " . number_format($totalTime * 1000, 2) . "ms");
    
    echo json_encode([
        'error' => true,
        'msg' => '获取衣物失败',
        // 在开发环境下可以提供更多错误详情
        'debug_info' => [
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'request_id' => $requestId
        ]
    ]);
}
?> 