/* pages/taobao/detail/index.wxss */
.container {
  padding: 0;
  background-color: #f8f8f8;
  padding-bottom: 70px; /* 为底部按钮留出空间 */
}

/* 返回按钮 */
.back-btn {
  position: fixed;
  top: 40px;
  left: 15px;
  width: 36px;
  height: 36px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.back-icon {
  width: 12px;
  height: 12px;
  border-top: 2px solid #fff;
  border-left: 2px solid #fff;
  transform: rotate(-45deg);
  margin-left: 4px;
}

/* 商品轮播图 - 增大高度，提高清晰度 */
.swiper-container {
  position: relative;
  width: 100%;
  height: 420px; /* 增加高度 */
  background-color: #fff;
}

.product-swiper {
  width: 100%;
  height: 100%;
}

.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 确保图片不变形 */
}

.indicator {
  position: absolute;
  right: 15px;
  bottom: 15px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
}

/* 价格部分 - 减少顶部间距 */
.price-section {
  background-color: #fff;
  padding: 15px;
  margin-top: -1px; /* 消除与轮播图的间距 */
  margin-bottom: 1px; /* 减少下方间距 */
  border-bottom: 1px solid #f0f0f0;
}

.price-row {
  display: flex;
  align-items: baseline;
}

.final-price {
  font-size: 26px;
  color: #ff4e33;
  font-weight: 600;
}

.original-price {
  font-size: 14px;
  color: #999;
  margin-left: 10px;
  text-decoration: line-through;
}

.coupon-info {
  margin-top: 10px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.coupon-tag {
  font-size: 12px;
  color: #ff4e33;
  background-color: #fff1f0;
  border: 1px solid #ffded9;
  padding: 2px 8px;
  border-radius: 4px;
  margin-right: 8px;
}

.coupon-value {
  font-size: 14px;
  color: #ff4e33;
  font-weight: 500;
}

.coupon-date {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
  width: 100%;
}

/* 商品标题 - 减少间距，增加重点突出 */
.product-title {
  background-color: #fff;
  padding: 15px;
  font-size: 17px;
  line-height: 1.5;
  color: #333;
  font-weight: 500;
  margin-bottom: 1px; /* 减少下方间距 */
  border-bottom: 1px solid #f0f0f0;
}

/* 店铺信息 - 减少间距，简化样式 */
.shop-info {
  background-color: #fff;
  padding: 12px 15px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.shop-name {
  font-size: 15px;
  color: #333;
  margin: 0;
}

.shop-stats {
  display: flex;
  font-size: 13px;
  color: #666;
}

.stat-item {
  margin-right: 15px;
}

/* 商品描述 - 优化样式，减少间距 */
.product-description {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 80px; /* 增加底部间距，为底部按钮留出空间 */
}

.section-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-bottom: 12px;
  position: relative;
  padding-left: 10px;
}

.section-title:before {
  content: '';
  position: absolute;
  left: 0;
  top: 3px;
  width: 4px;
  height: 18px;
  background-color: #ff6700;
  border-radius: 2px;
}

.description-content {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
}

.discount-tip {
  margin-top: 12px;
  color: #ff4e33;
  font-weight: 500;
  font-size: 15px;
  background-color: #fff8f8;
  padding: 8px 12px;
  border-radius: 6px;
}

/* 底部固定按钮 - 增加高度和清晰度 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 64px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.08);
  z-index: 100;
}

.price-display {
  display: flex;
  flex-direction: column;
}

.price-label {
  font-size: 12px;
  color: #999;
}

.bottom-price {
  font-size: 22px;
  color: #ff4e33;
  font-weight: 600;
}

.action-btn {
  height: 44px;
  line-height: 44px;
  padding: 0 30px;
  background-color: #ff4e33;
  color: #fff;
  font-size: 16px;
  border-radius: 22px;
  font-weight: 500;
}

/* 加载中 */
.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #ff6700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #666;
  margin-top: 10px;
}

/* 错误状态 */
.error-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.error-icon {
  width: 60px;
  height: 60px;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM5OTkiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCI+PC9jaXJjbGU+PGxpbmUgeDE9IjEyIiB5MT0iOCIgeDI9IjEyIiB5Mj0iMTIiPjwvbGluZT48bGluZSB4MT0iMTIiIHkxPSIxNiIgeDI9IjEyIiB5Mj0iMTYiPjwvbGluZT48L3N2Zz4=');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-bottom: 15px;
}

.error-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
  text-align: center;
}

.error-actions {
  display: flex;
  justify-content: center;
}

.error-btn {
  padding: 8px 20px;
  border-radius: 20px;
  margin: 0 10px;
  font-size: 14px;
}

.error-btn.retry {
  background-color: #ff4e33;
  color: #fff;
}

.error-btn.back {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #eee;
} 