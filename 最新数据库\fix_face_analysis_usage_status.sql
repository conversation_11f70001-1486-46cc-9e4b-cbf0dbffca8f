-- 修复face_analysis表的使用状态
-- 执行时间：2025-01-01

-- 1. 对于已经有分析结果的记录，标记为已使用
UPDATE `face_analysis` 
SET `usage_status` = 'used' 
WHERE `analysis_result` IS NOT NULL 
AND `analysis_result` != '' 
AND `usage_status` = 'unused';

-- 2. 对于已支付但没有分析结果的记录，保持为未使用状态
UPDATE `face_analysis` 
SET `usage_status` = 'unused' 
WHERE `payment_status` = 'paid' 
AND (`analysis_result` IS NULL OR `analysis_result` = '') 
AND `usage_status` != 'unused';

-- 3. 对于未支付的记录，标记为未使用
UPDATE `face_analysis` 
SET `usage_status` = 'unused' 
WHERE `payment_status` = 'unpaid';

-- 4. 查看修复后的状态统计
SELECT 
    payment_status,
    usage_status,
    COUNT(*) as count,
    COUNT(CASE WHEN analysis_result IS NOT NULL AND analysis_result != '' THEN 1 END) as has_result_count
FROM `face_analysis` 
GROUP BY payment_status, usage_status
ORDER BY payment_status, usage_status;

SELECT 'face_analysis表使用状态修复完成' as message;
