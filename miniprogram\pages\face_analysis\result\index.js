// pages/face_analysis/result/index.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    error: false,
    errorMsg: '',
    analysis: null,
    analysisResult: null,
    statusText: {
      'pending': '待分析',
      'processing': '分析中',
      'completed': '已完成',
      'failed': '分析失败'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (!options.id) {
      this.setData({
        loading: false,
        error: true,
        errorMsg: '未提供分析ID'
      });
      return;
    }

    this.analysisId = options.id;
    this.loadAnalysisResult();
  },

  /**
   * 加载分析结果
   */
  loadAnalysisResult() {
    console.log('开始加载分析结果，ID:', this.analysisId);
    console.log('useMockUser:', app.globalData.useMockUser);
    
    // 获取token，优先使用全局token（适用于体验账号）
    let token = app.globalData.token;
    
    // 如果没有全局token，尝试从本地存储获取
    if (!token) {
      token = wx.getStorageSync('token');
      console.log('从本地存储获取token');
    } else {
      console.log('使用全局token');
    }
    
    console.log('token存在:', !!token);
    console.log('是否为模拟用户:', app.globalData.useMockUser);
    
    // 检查是否有token或是否为体验账号
    if (!token && !app.globalData.useMockUser) {
      this.setData({
        loading: false,
        error: true,
        errorMsg: '请先登录以查看分析结果'
      });
      return;
    }
    
    // 如果是体验账号但没有token，创建一个模拟token
    if (!token && app.globalData.useMockUser) {
      token = 'mock_token_' + Date.now();
      console.log('为体验账号创建模拟token:', token);
    }

    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    this.setData({
      loading: true,
      error: false
    });

    // 调用API获取分析结果
    wx.request({
      url: `${app.globalData.baseUrl}/face_analysis.php`,
      method: 'GET',
      data: {
        action: 'get_result',
        analysis_id: this.analysisId
      },
      header: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      success: (res) => {
        wx.hideLoading();
        this.setData({ loading: false });
        
        console.log('分析结果响应:', res.data);
        
        if (res.data && !res.data.error) {
          // 处理分析结果
          const analysisData = res.data.data;
          
          // 解析JSON格式的分析结果
          let parsedResult = null;
          if (analysisData.analysis_result) {
            try {
              const resultObj = typeof analysisData.analysis_result === 'string' 
                ? JSON.parse(analysisData.analysis_result) 
                : analysisData.analysis_result;
              
              // 检查是否需要进一步解析数据层级
              if (resultObj.data && resultObj.data.analysis) {
                parsedResult = resultObj.data.analysis;
              } else if (resultObj.data && resultObj.data.raw_text) {
                // 尝试从raw_text中提取JSON
                parsedResult = this.extractJsonFromText(resultObj.data.raw_text);
              } else {
                parsedResult = resultObj;
              }
            } catch (e) {
              console.error('解析分析结果失败:', e);
            }
          }
          
          this.setData({
            analysis: analysisData,
            analysisResult: parsedResult
          });
          
          console.log('解析后的分析结果:', parsedResult);
          
        } else {
          console.error('获取分析结果失败:', res.data ? res.data.msg : '服务器响应异常');
          this.setData({
            error: true,
            errorMsg: res.data ? res.data.msg || '获取分析结果失败' : '服务器响应异常'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('请求失败:', err);
        this.setData({
          loading: false,
          error: true,
          errorMsg: '网络错误，请重试'
        });
      }
    });
  },

  /**
   * 从文本中提取JSON
   */
  extractJsonFromText(text) {
    if (!text) return null;
    
    // 尝试查找JSON开始和结束的位置
    const startPos = text.indexOf('{');
    const endPos = text.lastIndexOf('}');
    
    if (startPos !== -1 && endPos !== -1 && endPos > startPos) {
      const jsonStr = text.substring(startPos, endPos + 1);
      try {
        return JSON.parse(jsonStr);
      } catch (e) {
        console.error('提取的JSON字符串解析失败:', e);
      }
    }
    
    // 尝试从Markdown代码块中提取
    const codeBlockRegex = /```(?:json)?\s*({[\s\S]*?})\s*```/m;
    const match = text.match(codeBlockRegex);
    if (match && match[1]) {
      try {
        return JSON.parse(match[1]);
      } catch (e) {
        console.error('从代码块提取的JSON解析失败:', e);
      }
    }
    
    return null;
  },

  /**
   * 刷新分析结果
   */
  refreshResult() {
    this.loadAnalysisResult();
  },

  /**
   * 预览照片
   */
  previewPhoto(e) {
    const url = e.currentTarget.dataset.url;
    let urls = [];
    
    // 正面照片，优先使用display URL，然后是CDN URL，最后是本地URL
    if (this.data.analysis.display_front_photo_url) {
      urls.push(this.data.analysis.display_front_photo_url);
    } else if (this.data.analysis.cdn_front_photo_url) {
      urls.push(this.data.analysis.cdn_front_photo_url);
    } else if (this.data.analysis.front_photo_url) {
      urls.push(this.data.analysis.front_photo_url);
    }
    
    // 侧面照片，优先使用display URL，然后是CDN URL，最后是本地URL
    if (this.data.analysis.display_side_photo_url) {
      urls.push(this.data.analysis.display_side_photo_url);
    } else if (this.data.analysis.cdn_side_photo_url) {
      urls.push(this.data.analysis.cdn_side_photo_url);
    } else if (this.data.analysis.side_photo_url) {
      urls.push(this.data.analysis.side_photo_url);
    }
    
    // 如果没有找到任何照片URL，使用当前点击的URL
    if (urls.length === 0 && url) {
      urls.push(url);
    }
    
    wx.previewImage({
      current: url,
      urls: urls
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 返回首页
   */
  goToIndex() {
    wx.redirectTo({
      url: '/pages/face_analysis/index/index'
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    // 获取token，用于分享链接
    const token = wx.getStorageSync('token') || app.globalData.token || '';
    
    return {
      title: '想知道自己适合什么妆容，快试试次元衣帽间',
      path: `/pages/face_analysis/result/index?id=${this.analysisId}&token=${token}`,
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png'
    };
  }
})