<?php
// 调试圈子数据API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode(['status' => 'error', 'message' => '缺少授权头']);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode(['status' => 'error', 'message' => '无效或已过期的令牌']);
    exit;
}

$userId = $payload['sub'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 1. 检查用户信息
    $userSql = "SELECT id, nickname FROM users WHERE id = :user_id";
    $userStmt = $conn->prepare($userSql);
    $userStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $userStmt->execute();
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);
    
    // 2. 检查用户所在圈子
    $circleSql = "SELECT cm.circle_id, cm.role, cm.status, c.name as circle_name
                  FROM circle_members cm 
                  JOIN outfit_circles c ON cm.circle_id = c.id 
                  WHERE cm.user_id = :user_id";
    $circleStmt = $conn->prepare($circleSql);
    $circleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $circleStmt->execute();
    $circles = $circleStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 3. 检查用户的衣物数据
    $clothesSql = "SELECT COUNT(*) as total, 
                          SUM(CASE WHEN circle_id IS NULL THEN 1 ELSE 0 END) as personal,
                          SUM(CASE WHEN circle_id IS NOT NULL THEN 1 ELSE 0 END) as shared
                   FROM clothes WHERE user_id = :user_id";
    $clothesStmt = $conn->prepare($clothesSql);
    $clothesStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $clothesStmt->execute();
    $clothesStats = $clothesStmt->fetch(PDO::FETCH_ASSOC);
    
    // 4. 检查用户的穿搭数据
    $outfitsSql = "SELECT COUNT(*) as total, 
                          SUM(CASE WHEN circle_id IS NULL THEN 1 ELSE 0 END) as personal,
                          SUM(CASE WHEN circle_id IS NOT NULL THEN 1 ELSE 0 END) as shared
                   FROM outfits WHERE user_id = :user_id";
    $outfitsStmt = $conn->prepare($outfitsSql);
    $outfitsStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $outfitsStmt->execute();
    $outfitsStats = $outfitsStmt->fetch(PDO::FETCH_ASSOC);
    
    // 5. 检查用户的衣橱数据
    $wardrobesSql = "SELECT COUNT(*) as total, 
                            SUM(CASE WHEN circle_id IS NULL THEN 1 ELSE 0 END) as personal,
                            SUM(CASE WHEN circle_id IS NOT NULL THEN 1 ELSE 0 END) as shared
                     FROM wardrobes WHERE user_id = :user_id";
    $wardrobesStmt = $conn->prepare($wardrobesSql);
    $wardrobesStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $wardrobesStmt->execute();
    $wardrobesStats = $wardrobesStmt->fetch(PDO::FETCH_ASSOC);
    
    // 6. 如果用户在圈子中，检查圈子的所有数据
    $circleData = null;
    if (!empty($circles)) {
        $activeCircle = null;
        foreach ($circles as $circle) {
            if ($circle['status'] === 'active') {
                $activeCircle = $circle;
                break;
            }
        }
        
        if ($activeCircle) {
            $circleId = $activeCircle['circle_id'];
            
            // 检查圈子中所有成员的数据
            $circleClothesSQL = "SELECT COUNT(*) as total FROM clothes c 
                                JOIN users u ON c.user_id = u.id
                                WHERE (c.circle_id = :circle_id OR 
                                      (c.user_id IN (SELECT user_id FROM circle_members 
                                                    WHERE circle_id = :circle_id AND status = 'active') 
                                       AND c.circle_id IS NULL))";
            $circleClothesStmt = $conn->prepare($circleClothesSQL);
            $circleClothesStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
            $circleClothesStmt->execute();
            $circleClothesCount = $circleClothesStmt->fetch(PDO::FETCH_ASSOC);
            
            $circleOutfitsSQL = "SELECT COUNT(*) as total FROM outfits o 
                                JOIN users u ON o.user_id = u.id
                                WHERE (o.circle_id = :circle_id OR 
                                      (o.user_id IN (SELECT user_id FROM circle_members 
                                                    WHERE circle_id = :circle_id AND status = 'active') 
                                       AND o.circle_id IS NULL))";
            $circleOutfitsStmt = $conn->prepare($circleOutfitsSQL);
            $circleOutfitsStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
            $circleOutfitsStmt->execute();
            $circleOutfitsCount = $circleOutfitsStmt->fetch(PDO::FETCH_ASSOC);
            
            // 检查圈子成员
            $membersSql = "SELECT cm.user_id, cm.role, cm.status, u.nickname 
                          FROM circle_members cm 
                          JOIN users u ON cm.user_id = u.id 
                          WHERE cm.circle_id = :circle_id";
            $membersStmt = $conn->prepare($membersSql);
            $membersStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
            $membersStmt->execute();
            $members = $membersStmt->fetchAll(PDO::FETCH_ASSOC);
            
            $circleData = [
                'circle_info' => $activeCircle,
                'members' => $members,
                'clothes_count' => intval($circleClothesCount['total']),
                'outfits_count' => intval($circleOutfitsCount['total'])
            ];
        }
    }
    
    echo json_encode([
        'status' => 'success',
        'data' => [
            'user' => $user,
            'circles' => $circles,
            'user_data' => [
                'clothes' => [
                    'total' => intval($clothesStats['total']),
                    'personal' => intval($clothesStats['personal']),
                    'shared' => intval($clothesStats['shared'])
                ],
                'outfits' => [
                    'total' => intval($outfitsStats['total']),
                    'personal' => intval($outfitsStats['personal']),
                    'shared' => intval($outfitsStats['shared'])
                ],
                'wardrobes' => [
                    'total' => intval($wardrobesStats['total']),
                    'personal' => intval($wardrobesStats['personal']),
                    'shared' => intval($wardrobesStats['shared'])
                ]
            ],
            'circle_data' => $circleData
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '调试失败：' . $e->getMessage()
    ]);
}
?>
