<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户管理 - 次元衣柜管理后台</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <style>
        /* 商户列表样式 */
        .merchant-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            color: white;
        }
        
        .status-yes {
            background-color: #52c41a;
        }
        
        .status-no {
            background-color: #f5222d;
        }
        
        .share-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            color: white;
            background-color: #1890ff;
        }
        
        .no-share {
            background-color: #d9d9d9;
            color: #666;
        }
        
        .action-btn {
            padding: 4px 10px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 12px;
            margin-right: 5px;
        }
        
        .disable-btn {
            background-color: #ff4d4f;
            color: white;
        }
        
        .enable-btn {
            background-color: #52c41a;
            color: white;
        }
        
        .view-btn {
            background-color: #1890ff;
            color: white;
        }
        
        .search-box {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }
        
        .search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            min-width: 120px;
        }
        
        .search-btn {
            padding: 8px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .search-btn:hover {
            background-color: #40a9ff;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            gap: 5px;
        }
        
        .page-btn {
            padding: 5px 10px;
            border: 1px solid #d9d9d9;
            background-color: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .page-btn.active {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        
        .page-btn:hover:not(.active) {
            border-color: #40a9ff;
            color: #40a9ff;
        }
        
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>商户管理</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button class="logout-btn" id="logoutBtn">退出</button>
                </div>
            </div>
            
            <div class="card">
                <h3 class="card-title">商户列表</h3>
                
                <div class="search-container">
                    <select id="statusFilter" class="filter-select">
                        <option value="all">全部用户</option>
                        <option value="yes">已入驻商户</option>
                        <option value="no">未入驻用户</option>
                    </select>
                    <input type="text" id="searchKeyword" class="search-input" placeholder="输入商户昵称搜索">
                    <button id="searchBtn" class="search-btn">搜索</button>
                </div>
                
                <div id="merchantError" class="error-container"></div>
                
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>头像</th>
                            <th>昵称</th>
                            <th>衣物数</th>
                            <th>商户状态</th>
                            <th>共享试穿</th>
                            <th>付费点数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="merchantTable">
                        <!-- 数据行将通过JavaScript动态添加 -->
                    </tbody>
                </table>
                
                <div id="merchantLoading" class="loading-indicator">加载中...</div>
                
                <div id="paginationContainer" class="pagination"></div>
            </div>
        </div>
    </div>
    
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3 class="modal-title">确认操作</h3>
            <div id="confirmMessage" class="modal-message"></div>
            <div class="modal-actions">
                <button id="cancelBtn" class="cancel-btn">取消</button>
                <button id="confirmBtn" class="confirm-btn">确认</button>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/merchant_list.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为merchant
            Sidebar.init('merchant');
            
            // 获取DOM元素
            const adminName = document.getElementById('adminName');
            const logoutBtn = document.getElementById('logoutBtn');
            
            // 显示用户信息
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            logoutBtn.addEventListener('click', function() {
                Auth.logout();
            });
        });
    </script>
</body>
</html> 