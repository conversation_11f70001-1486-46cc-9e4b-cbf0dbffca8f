<?php
/**
 * 调试圈子成员访问权限问题
 * 检查为什么圈子成员无法看到其他成员的数据
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "=== 圈子成员访问权限调试 ===\n";
    echo "当前用户ID: $userId\n\n";
    
    // 1. 检查用户的圈子成员身份
    echo "1. 检查用户圈子成员身份:\n";
    $stmt = $conn->prepare("
        SELECT cm.circle_id, cm.status, cm.role, cm.joined_at,
               c.name as circle_name, c.created_by, c.created_at as circle_created_at
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id
        ORDER BY cm.joined_at DESC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $userMemberships = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($userMemberships)) {
        echo "用户不在任何圈子中\n";
        exit;
    }
    
    foreach ($userMemberships as $membership) {
        echo "- 圈子ID: {$membership['circle_id']}\n";
        echo "  名称: {$membership['circle_name']}\n";
        echo "  状态: {$membership['status']}\n";
        echo "  角色: {$membership['role']}\n";
        echo "  创建者: {$membership['created_by']}\n";
        echo "  加入时间: {$membership['joined_at']}\n\n";
    }
    
    // 获取活跃圈子
    $activeCircles = [];
    foreach ($userMemberships as $membership) {
        if ($membership['status'] === 'active') {
            $activeCircles[] = $membership['circle_id'];
        }
    }
    
    if (empty($activeCircles)) {
        echo "用户没有活跃的圈子成员身份\n";
        exit;
    }
    
    echo "活跃圈子ID: " . implode(', ', $activeCircles) . "\n\n";
    $circleIds = implode(',', $activeCircles);
    
    // 2. 检查圈子中的所有成员
    echo "2. 检查圈子中的所有成员:\n";
    $stmt = $conn->prepare("
        SELECT cm.user_id, cm.status, cm.role, u.nickname,
               COUNT(cl.id) as clothes_count,
               COUNT(o.id) as outfits_count
        FROM circle_members cm
        LEFT JOIN users u ON cm.user_id = u.id
        LEFT JOIN clothes cl ON cl.user_id = cm.user_id
        LEFT JOIN outfits o ON o.user_id = cm.user_id
        WHERE cm.circle_id IN ($circleIds)
        GROUP BY cm.user_id, cm.status, cm.role, u.nickname
        ORDER BY cm.role DESC, cm.user_id
    ");
    $stmt->execute();
    $allMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($allMembers as $member) {
        $isCurrent = $member['user_id'] == $userId ? ' (当前用户)' : '';
        echo "- 用户ID: {$member['user_id']}{$isCurrent}\n";
        echo "  昵称: {$member['nickname']}\n";
        echo "  状态: {$member['status']}\n";
        echo "  角色: {$member['role']}\n";
        echo "  总衣物数: {$member['clothes_count']}\n";
        echo "  总穿搭数: {$member['outfits_count']}\n\n";
    }
    
    // 3. 检查圈子中的共享数据
    echo "3. 检查圈子中的共享数据:\n";
    
    // 检查衣物数据
    echo "3.1 圈子中的衣物数据:\n";
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.user_id, c.circle_id, u.nickname as creator
        FROM clothes c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.circle_id IN ($circleIds)
        ORDER BY c.user_id, c.created_at DESC
        LIMIT 20
    ");
    $stmt->execute();
    $circleClothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($circleClothes)) {
        echo "圈子中没有共享的衣物数据\n";
    } else {
        foreach ($circleClothes as $item) {
            echo "- 衣物ID: {$item['id']}, 名称: {$item['name']}, 创建者: {$item['creator']} (用户{$item['user_id']}), 圈子ID: {$item['circle_id']}\n";
        }
    }
    echo "\n";
    
    // 检查穿搭数据
    echo "3.2 圈子中的穿搭数据:\n";
    $stmt = $conn->prepare("
        SELECT o.id, o.name, o.user_id, o.circle_id, u.nickname as creator
        FROM outfits o
        LEFT JOIN users u ON o.user_id = u.id
        WHERE o.circle_id IN ($circleIds)
        ORDER BY o.user_id, o.created_at DESC
        LIMIT 20
    ");
    $stmt->execute();
    $circleOutfits = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($circleOutfits)) {
        echo "圈子中没有共享的穿搭数据\n";
    } else {
        foreach ($circleOutfits as $item) {
            echo "- 穿搭ID: {$item['id']}, 名称: {$item['name']}, 创建者: {$item['creator']} (用户{$item['user_id']}), 圈子ID: {$item['circle_id']}\n";
        }
    }
    echo "\n";
    
    // 4. 测试API查询逻辑
    echo "4. 测试API查询逻辑:\n";
    
    // 测试衣物查询 - 共享数据源
    echo "4.1 测试衣物查询 (共享数据源):\n";
    $sql = "SELECT c.id, c.name, c.user_id, c.circle_id,
                   u.nickname as creator_nickname,
                   CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
            FROM clothes c 
            LEFT JOIN users u ON c.user_id = u.id 
            WHERE c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
            ORDER BY c.created_at DESC
            LIMIT 10";
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $sharedClothesQuery = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "查询SQL: $sql\n";
    echo "查询参数: user_id = $userId\n";
    echo "查询结果数量: " . count($sharedClothesQuery) . "\n";
    
    if (!empty($sharedClothesQuery)) {
        foreach ($sharedClothesQuery as $item) {
            echo "- {$item['name']} (ID: {$item['id']}) - 创建者: {$item['creator_nickname']} (用户{$item['user_id']})\n";
        }
    }
    echo "\n";
    
    // 测试衣物查询 - 全部数据源
    echo "4.2 测试衣物查询 (全部数据源):\n";
    $sql = "SELECT c.id, c.name, c.user_id, c.circle_id,
                   u.nickname as creator_nickname,
                   CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
            FROM clothes c 
            LEFT JOIN users u ON c.user_id = u.id 
            WHERE ((c.user_id = :user_id AND c.circle_id IS NULL) OR
                   (c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')))
            ORDER BY c.created_at DESC
            LIMIT 10";
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $allClothesQuery = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "查询结果数量: " . count($allClothesQuery) . "\n";
    
    if (!empty($allClothesQuery)) {
        foreach ($allClothesQuery as $item) {
            echo "- {$item['name']} (ID: {$item['id']}) - 创建者: {$item['creator_nickname']} (用户{$item['user_id']}) - 数据源: {$item['data_source']}\n";
        }
    }
    echo "\n";
    
    // 5. 检查子查询
    echo "5. 检查子查询结果:\n";
    $stmt = $conn->prepare("
        SELECT circle_id FROM circle_members 
        WHERE user_id = :user_id AND status = 'active'
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $subqueryResult = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "子查询 'SELECT circle_id FROM circle_members WHERE user_id = $userId AND status = 'active'' 结果:\n";
    echo "圈子ID: " . implode(', ', $subqueryResult) . "\n\n";
    
    // 6. 总结分析
    echo "6. 问题分析:\n";
    
    if (empty($circleClothes) && empty($circleOutfits)) {
        echo "- 圈子中没有任何共享数据，可能是数据同步问题\n";
    } elseif (empty($sharedClothesQuery)) {
        echo "- 圈子中有共享数据，但API查询返回空结果\n";
        echo "- 可能是SQL查询逻辑或权限验证问题\n";
    } else {
        echo "- API查询正常，返回了共享数据\n";
    }
    
    echo "\n=== 调试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
