<view class="container">
  <view class="content">
    <view class="upload-section">
      <view class="section-title">上传照片</view>
      <view class="upload-tips">请上传清晰的正面照片，侧面照片可选</view>
      
      <view class="photo-uploader">
        <!-- 正面照片上传区域 -->
        <view class="photo-upload-item">
          <view class="photo-label">
            <text class="required-mark">*</text>正面照
          </view>
          <view class="photo-upload-area" bindtap="chooseFrontPhoto">
            <block wx:if="{{!frontPhotoPath}}">
              <view class="upload-placeholder">
                <view class="upload-icon"></view>
                <text>点击上传</text>
              </view>
            </block>
            <block wx:else>
              <image src="{{frontPhotoPath}}" mode="aspectFit" class="photo-preview"></image>
              <view class="delete-icon" catchtap="clearFrontPhoto"></view>
            </block>
          </view>
        </view>
        
        <!-- 侧面照片上传区域 -->
        <view class="photo-upload-item">
          <view class="photo-label">侧面照（可选）</view>
          <view class="photo-upload-area" bindtap="chooseSidePhoto">
            <block wx:if="{{!sidePhotoPath}}">
              <view class="upload-placeholder">
                <view class="upload-icon"></view>
                <text>点击上传</text>
              </view>
            </block>
            <block wx:else>
              <image src="{{sidePhotoPath}}" mode="aspectFit" class="photo-preview"></image>
              <view class="delete-icon" catchtap="clearSidePhoto"></view>
            </block>
          </view>
        </view>
      </view>
    </view>
    
    <view class="style-section">
      <view class="section-title">风格偏好</view>
      <view class="section-desc">告诉我们您喜欢的风格，帮助我们提供更精准的建议</view>
      <textarea class="style-input" placeholder="例如：简约、优雅、活力、职场、休闲等" bindinput="onStyleInput" value="{{preferredStyle}}" maxlength="200"></textarea>
    </view>
    
    <view class="tips-section">
      <view class="tips-title">拍摄小技巧</view>
      <view class="tips-content">
        <view class="tip-item">
          <text class="tip-num">1.</text>
          <text class="tip-text">请使用自然光线，避免过暗或过曝</text>
        </view>
        <view class="tip-item">
          <text class="tip-num">2.</text>
          <text class="tip-text">保持面部正对相机，表情自然</text>
        </view>
        <view class="tip-item">
          <text class="tip-num">3.</text>
          <text class="tip-text">尽量使用纯色背景，减少干扰</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 上传进度条（仅在上传时显示） -->
  <view class="progress-bar" wx:if="{{isUploading}}">
    <view class="progress-inner" style="width: {{uploadProgress}}%"></view>
  </view>
  
  <view class="footer">
    <button class="submit-btn" bindtap="submitAnalysis" disabled="{{!frontPhotoPath || isUploading}}">
      {{isUploading ? '上传中...' : '开始分析'}}
    </button>
  </view>
</view> 