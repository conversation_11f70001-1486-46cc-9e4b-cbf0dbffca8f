<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] != 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证管理员身份
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 检查必要参数
if (!isset($_GET['user_id']) || empty($_GET['user_id']) || !isset($_GET['type']) || empty($_GET['type'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少必要参数: user_id或type']);
    exit();
}

$userId = (int)$_GET['user_id'];
$type = $_GET['type']; // 'clothes'或'photos'
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 20;

// 验证并限制参数
$page = max(1, $page);
$perPage = max(1, min(100, $perPage)); // 限制每页最大数量为100
$offset = ($page - 1) * $perPage;

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

// 检查用户是否存在
$checkUserQuery = "SELECT id FROM users WHERE id = :user_id";
$checkUserStmt = $conn->prepare($checkUserQuery);
$checkUserStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
$checkUserStmt->execute();

if (!$checkUserStmt->fetch()) {
    http_response_code(404);
    echo json_encode(['error' => true, 'msg' => '用户不存在']);
    exit();
}

$data = [];
$total = 0;

// 根据类型获取不同的数据
if ($type === 'clothes') {
    // 获取衣物总数
    $countQuery = "SELECT COUNT(*) AS total FROM clothes WHERE user_id = :user_id";
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
    $countStmt->execute();
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 获取衣物列表
    $query = "SELECT id, name, category, image_url, tags, created_at 
              FROM clothes 
              WHERE user_id = :user_id 
              ORDER BY created_at DESC 
              LIMIT :offset, :limit";
    $stmt = $conn->prepare($query);
    $stmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':limit', $perPage, PDO::PARAM_INT);
    $stmt->execute();
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
} 
elseif ($type === 'photos') {
    // 获取照片总数
    $countQuery = "SELECT COUNT(*) AS total FROM photos WHERE user_id = :user_id";
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
    $countStmt->execute();
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 获取照片列表
    $query = "SELECT id, image_url, type, description, created_at 
              FROM photos 
              WHERE user_id = :user_id 
              ORDER BY created_at DESC 
              LIMIT :offset, :limit";
    $stmt = $conn->prepare($query);
    $stmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':limit', $perPage, PDO::PARAM_INT);
    $stmt->execute();
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
}
else {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '无效的type参数，必须为clothes或photos']);
    exit();
}

// 计算总页数
$totalPages = ceil($total / $perPage);

// 构建响应数据
$result = [
    'error' => false,
    'data' => $data,
    'pagination' => [
        'total' => (int)$total,
        'per_page' => $perPage,
        'current_page' => $page,
        'total_pages' => $totalPages
    ]
];

// 设置上一页和下一页链接
if ($page > 1) {
    $result['pagination']['prev_page'] = $page - 1;
}
if ($page < $totalPages) {
    $result['pagination']['next_page'] = $page + 1;
}

echo json_encode($result); 