# 图片存储迁移到阿里云OSS指南

本文档说明如何将次元衣柜系统中的图片存储从本地服务器迁移到阿里云对象存储服务(OSS)。

## 1. 环境准备

### 1.1 确认阿里云OSS配置

已在`config.php`中添加以下配置项:

```php
// 阿里云OSS配置
define('ALIYUN_OSS_ENDPOINT', 'oss-cn-shanghai.aliyuncs.com');  // OSS服务的Endpoint
define('ALIYUN_OSS_BUCKET', 'cyymj');  // OSS Bucket名称
define('ALIYUN_OSS_BUCKET_DOMAIN', 'cyymj.oss-cn-shanghai.aliyuncs.com');  // Bucket域名

// OSS存储路径前缀
define('OSS_PATH_CLOTHES', 'clothes/');  // 衣物图片存储路径
define('OSS_PATH_PHOTOS', 'photos/');    // 用户照片存储路径
define('OSS_PATH_TRY_ON', 'try_on/');    // 试衣结果图片存储路径
```

### 1.2 安装阿里云OSS SDK

1. 确保服务器已安装Composer
2. 执行以下命令安装SDK：
   ```bash
   cd /www/wwwroot/cyyg.alidog.cn
   composer require aliyuncs/oss-sdk-php
   ```

### 1.3 注意事项

- vendor目录位于项目根目录(`/www/wwwroot/cyyg.alidog.cn/vendor`)，而非login_backend目录内
- 所有引用SDK的PHP文件都需要正确引用autoload.php的路径，例如：`require_once '../vendor/autoload.php';`

## 2. 代码修改说明

已修改的文件及功能:

- **oss_helper.php**: 新增OSS操作辅助类
- **upload_photo.php**: 修改为直接上传图片到OSS
- **delete_photo.php**: 修改为删除OSS中的图片
- **add_clothing.php**: 修改为自动将非OSS图片URL下载到OSS
- **delete_clothing.php**: 修改为同时删除OSS中的图片
- **try_on.php**: 修改为将试衣结果保存到OSS

## 3. 迁移步骤

### 3.1 部署新代码

1. 备份原有代码
2. 部署包含OSS支持的新代码

### 3.2 迁移历史数据

使用提供的`migrate_to_oss.php`脚本将现有图片迁移到OSS：

**Linux服务器环境:**
```bash
cd /www/wwwroot/cyyg.alidog.cn/login_backend
php migrate_to_oss.php
```

**Windows开发环境:**
```powershell
# 进入项目目录
cd "项目所在路径\cyyg.alidog.cn\login_backend"
# 执行迁移脚本
php migrate_to_oss.php
```

此脚本会:
- 迁移 photos 表中的本地照片到OSS
- 迁移 clothes 表中的衣物图片到OSS 
- 迁移 try_on_history 表中的试衣结果图片到OSS

### 3.3 验证迁移

验证以下功能是否正常:

1. 上传新照片
2. 添加新衣物
3. 试衣功能
4. 删除照片和衣物
5. 查看历史图片

## 4. 注意事项

### 4.1 图片访问权限

- OSS存储桶需要配置为公共读权限，否则图片将无法直接访问
- 配置步骤：
  1. 登录阿里云控制台，进入OSS服务
  2. 选择`cyymj`存储桶，点击"基础设置"
  3. 在"读写权限"区域，选择"公共读"并保存

### 4.2 小程序域名白名单配置

为了让小程序能够访问OSS中的图片，需要配置域名白名单：

1. 登录微信公众平台，进入小程序管理后台
2. 前往"开发"->"开发管理"->"开发设置"->"服务器域名"
3. 在"request合法域名"和"downloadFile合法域名"中，添加OSS域名：
   ```
   https://cyymj.oss-cn-shanghai.aliyuncs.com
   ```
4. 保存并重启微信开发者工具

### 4.3 OSS跨域设置

如网页前端直接访问OSS资源，需要配置跨域规则：

1. 登录阿里云控制台，进入OSS服务
2. 选择`cyymj`存储桶，点击"基础设置"
3. 在"跨域设置"区域，添加规则：
   - 来源：`*`（或限定为您的网站域名）
   - 允许Methods：`GET`
   - 允许Headers：`*`
   - 缓存时间：`86400`
   - 返回Vary: Origin：`是`

### 4.4 存储路径规则

- 照片: `photos/photo_{userId}_{timestamp}_{random}.jpg`
- 衣物: `clothes/cloth_{userId}_{timestamp}_{random}.jpg`
- 试衣结果: `try_on/try_on_{userId}_{timestamp}_{random}.jpg`

### 4.5 本地备份

- 建议在迁移完成后保留本地图片一段时间，确认OSS访问稳定后再清理

## 5. 回滚方案

如遇到问题需要回滚:

1. 恢复原代码
2. 数据库中的图片URL已被修改为OSS链接，将继续使用OSS上的图片
3. 如需完全回滚，需要将数据库中的图片URL恢复为本地URL

## 6. 维护与监控

- 定期检查OSS存储使用量和费用
- 监控OSS访问日志，确保图片访问正常
- 考虑设置OSS生命周期规则，自动清理长期未使用的图片

## 7. 故障排除

常见问题:

1. **OSS图片无法访问**: 检查OSS存储桶权限设置
2. **上传失败**: 检查阿里云AccessKey权限
3. **迁移脚本错误**: 查看PHP错误日志和try_on.log日志文件 
4. **OSS常量为定义错误**: 如果遇到 `Use of undefined constant ALIYUN_OSS_ENDPOINT - assumed 'ALIYUN_OSS_ENDPOINT'` 这样的错误，说明OSS命名空间未正确载入，解决方法：
   ```php
   // 在脚本顶部添加OSS命名空间引用
   use OSS\OssClient;
   use OSS\Core\OssException;

   // 修改OssClient实例化代码，从
   $ossClient = new \OSS\OssClient(...)
   // 改为
   $ossClient = new OssClient(...)
   ```
5. **bucket name is invalid错误**: 检查config.php文件是否被正确引入和加载
6. **微信小程序图片无法显示**: 微信小程序有严格的域名白名单机制，需要在小程序管理后台添加OSS域名到白名单
7. **微信头像获取失败**: 微信开发者工具在使用chooseAvatar组件时可能出现路径错误，解决方法有两种：
   - 方案1：替换为wx.chooseMedia API获取头像，然后上传到OSS
   - 方案2：使用临时路径作为中间变量，避免直接操作原始文件路径
8. **前端图片加载失败**: 检查网络请求，检查图片URL格式是否正确，前端开发者工具控制台是否显示跨域错误
9. **试衣功能无法生成结果**: 常见问题和解决方法：
   - 确保API返回的是单一JSON对象而不是两个对象拼接
   - 试衣功能依赖于OSS存储，确保OSS配置正确
   - 检查试衣API的请求参数格式是否正确
   - 查看try_on.log日志文件了解详细错误
   - 使用test_try_on.php测试工具验证API响应是否正确

## 8. 故障验证工具

提供了简单的测试工具，用于验证系统功能：

1. **OSS连接测试工具**：验证OSS配置和权限：
   ```
   https://cyyg.alidog.cn/login_backend/test_oss_connection.php
   ```

2. **试衣API测试工具**：验证试衣功能是否正常：
   ```
   https://cyyg.alidog.cn/login_backend/test_try_on.php
   ```

这些工具将检测：
- OSS配置是否正确
- 是否能成功上传文件
- API是否返回有效JSON
- 提供解决方案建议 