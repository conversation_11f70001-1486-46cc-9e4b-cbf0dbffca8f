-- 为已存在的face_analysis表添加usage_status字段
-- 执行时间：2025-01-01

-- 检查字段是否已存在，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'face_analysis' 
     AND column_name = 'usage_status' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "usage_status字段已存在" as message',
    'ALTER TABLE `face_analysis` ADD COLUMN `usage_status` enum(\'unused\',\'used\',\'expired\') NOT NULL DEFAULT \'unused\' COMMENT \'使用状态\' AFTER `amount`'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'face_analysis' 
     AND index_name = 'idx_usage_status' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "idx_usage_status索引已存在" as message',
    'ALTER TABLE `face_analysis` ADD KEY `idx_usage_status` (`usage_status`)'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新现有记录的使用状态
-- 如果分析结果不为空，说明已经使用过，标记为used
-- 如果分析结果为空但已支付，标记为unused
-- 如果未支付，保持unused状态
UPDATE `face_analysis` 
SET `usage_status` = CASE 
    WHEN `analysis_result` IS NOT NULL AND `analysis_result` != '' THEN 'used'
    WHEN `payment_status` = 'paid' AND (`analysis_result` IS NULL OR `analysis_result` = '') THEN 'unused'
    ELSE 'unused'
END
WHERE `usage_status` = 'unused';

SELECT 'face_analysis表usage_status字段更新完成' as message;
