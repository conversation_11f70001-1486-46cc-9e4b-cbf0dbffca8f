<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// Handle preflight request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => 'Method not allowed']);
    exit();
}

// Check if Authorization header is present
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => 'Missing authorization header']);
    exit();
}

// Get token from Authorization header
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// Verify token
$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => 'Invalid or expired token']);
    exit();
}

// Get request body
$data = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!isset($data['nickName']) || empty($data['nickName']) || 
    !isset($data['avatarUrl']) || empty($data['avatarUrl'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => 'Missing required fields: nickName, avatarUrl']);
    exit();
}

$userId = $payload['sub'];
$nickName = $data['nickName'];
$avatarUrl = $data['avatarUrl'];
$gender = isset($data['gender']) ? $data['gender'] : null;

// Update user profile
$db = new Database();
$result = $db->updateUserProfile($userId, $nickName, $avatarUrl, $gender);

if ($result) {
    echo json_encode(['error' => false, 'msg' => 'Profile updated successfully']);
} else {
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => 'Failed to update profile']);
} 