<?php
/**
 * 微信支付回调通知接口
 * 
 * 接收微信支付平台的支付结果通知
 * 处理用户的试衣次数更新
 */

require_once 'config.php';
require_once 'db.php';
require_once 'wx_pay_helper.php';

// 记录日志
function log_notify($message, $type = 'INFO') {
    $logFile = __DIR__ . '/logs/wx_pay_notify.log';
    $date = date('Y-m-d H:i:s');
    $logMessage = "[$date] [$type] $message" . PHP_EOL;
    
    // 确保日志目录存在
    $logDir = __DIR__ . '/logs';
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    // 写入日志
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// 开始记录访问
log_notify('收到微信支付回调 - ' . date('Y-m-d H:i:s'));

// 获取微信支付回调数据
$body = file_get_contents('php://input');
log_notify("接收到的数据: $body");

// 获取所有请求头
$headers = [];
foreach ($_SERVER as $key => $value) {
    if (substr($key, 0, 5) === 'HTTP_') {
        $headerKey = str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($key, 5)))));
        $headers[$headerKey] = $value;
    } else if (strpos($key, 'WECHATPAY') !== false) {
        // 直接从原始头信息获取
        $headerKey = str_replace('_', '-', $key);
        $headers[$headerKey] = $value;
    }
}

// 记录完整请求头
log_notify("接收到的完整请求头: " . json_encode($headers));

// 预处理请求头，转换为微信支付回调的标准头部
$wxPayHeaders = [];
foreach ($headers as $key => $value) {
    if (stripos($key, 'Wechatpay') === 0) {
        $wxPayHeaders[$key] = $value;
    }
}

// 如果找不到微信支付头部，尝试兼容性处理
if (empty($wxPayHeaders)) {
    log_notify("未找到标准Wechatpay头部，尝试从原始请求头提取");
    
    // 尝试不区分大小写查找
    foreach ($headers as $key => $value) {
        if (stripos($key, 'wechatpay') !== false) {
            $newKey = 'Wechatpay' . substr($key, stripos($key, 'wechatpay') + 9);
            $wxPayHeaders[$newKey] = $value;
        }
    }
    
    // 最后的备用处理
    if (empty($wxPayHeaders)) {
        log_notify("仍未找到微信支付头部，使用默认结构");
        $wxPayHeaders = [
            'Wechatpay-Signature' => isset($_SERVER['HTTP_WECHATPAY_SIGNATURE']) ? $_SERVER['HTTP_WECHATPAY_SIGNATURE'] : '',
            'Wechatpay-Timestamp' => isset($_SERVER['HTTP_WECHATPAY_TIMESTAMP']) ? $_SERVER['HTTP_WECHATPAY_TIMESTAMP'] : '',
            'Wechatpay-Nonce' => isset($_SERVER['HTTP_WECHATPAY_NONCE']) ? $_SERVER['HTTP_WECHATPAY_NONCE'] : '',
            'Wechatpay-Serial' => isset($_SERVER['HTTP_WECHATPAY_SERIAL']) ? $_SERVER['HTTP_WECHATPAY_SERIAL'] : ''
        ];
    }
}

log_notify("提取的微信支付请求头: " . json_encode($wxPayHeaders));

// 如果签名或关键信息丢失，尝试直接处理回调数据
if (empty($wxPayHeaders['Wechatpay-Signature']) || empty($wxPayHeaders['Wechatpay-Timestamp'])) {
    log_notify("签名信息丢失，尝试直接解析回调数据", 'WARNING');
    
    // 尝试解析回调数据
    $notifyData = json_decode($body, true);
    if (!$notifyData) {
        log_notify("无法解析回调数据JSON: " . json_last_error_msg(), 'ERROR');
        http_response_code(200); // 返回200让微信不再重试
        echo json_encode(['code' => 'SUCCESS', 'message' => 'OK']);
        exit;
    }
    
    // 检查回调数据
    if (!isset($notifyData['resource']) || !isset($notifyData['event_type'])) {
        log_notify("回调数据结构异常", 'ERROR');
        http_response_code(200);
        echo json_encode(['code' => 'SUCCESS', 'message' => 'OK']);
        exit;
    }
    
    log_notify("跳过验签，直接处理回调数据: " . json_encode($notifyData));
    
    // 处理支付结果
    if ($notifyData['event_type'] === 'TRANSACTION.SUCCESS') {
        // 解密回调数据
        $wxPayHelper = new WxPayHelper();
        $decryptResult = $wxPayHelper->decryptNotifyData(
            $notifyData['resource']['ciphertext'], 
            $notifyData['resource']['associated_data'] ?? '', 
            $notifyData['resource']['nonce']
        );
        
        if (isset($decryptResult['error']) && $decryptResult['error']) {
            log_notify("解密回调数据失败: " . $decryptResult['msg'], 'ERROR');
            http_response_code(200);
            echo json_encode(['code' => 'SUCCESS', 'message' => 'OK']);
            exit;
        }
        
        log_notify("解密后的回调数据: " . json_encode($decryptResult));
        
        // 处理解密后的数据
        if (isset($decryptResult['out_trade_no']) && isset($decryptResult['trade_state'])) {
            $outTradeNo = $decryptResult['out_trade_no'];
            $tradeState = $decryptResult['trade_state'];
            $transactionId = $decryptResult['transaction_id'] ?? '';
            
            log_notify("订单号: $outTradeNo, 交易状态: $tradeState, 微信支付交易号: $transactionId");
            
            if ($tradeState === 'SUCCESS') {
                try {
                    $db = new Database();
                    $conn = $db->getConnection();
                    
                    // 处理支付成功逻辑
                    processSuccessfulPayment($conn, $outTradeNo, $transactionId);
                } catch (Exception $e) {
                    log_notify("处理支付成功逻辑时出错: " . $e->getMessage(), 'ERROR');
                }
            }
        }
    }
    
    // 回复微信支付平台，表示成功接收回调
    http_response_code(200);
    echo json_encode(['code' => 'SUCCESS', 'message' => 'OK']);
    exit;
}

// 验证回调
$wxPayHelper = new WxPayHelper();
$verifyResult = $wxPayHelper->verifyPayNotify($wxPayHeaders, $body);

// 即使验证失败，我们仍然尝试处理回调数据
$notifyData = [];
$resource = [];

if (isset($verifyResult['error']) && $verifyResult['error']) {
    log_notify("验证失败: " . $verifyResult['msg'] . "，尝试直接解析回调数据", 'WARNING');
    
    // 尝试解析回调数据
    $notifyData = json_decode($body, true);
    if (!$notifyData) {
        log_notify("无法解析回调数据JSON: " . json_last_error_msg(), 'ERROR');
        // 仍然返回成功以避免微信重试
        http_response_code(200);
        echo json_encode(['code' => 'SUCCESS', 'message' => 'OK']);
        exit;
    }
    
    // 检查回调数据
    if (!isset($notifyData['resource']) || !isset($notifyData['event_type'])) {
        log_notify("回调数据结构异常", 'ERROR');
        http_response_code(200);
        echo json_encode(['code' => 'SUCCESS', 'message' => 'OK']);
        exit;
    }
    
    log_notify("开始处理未验证的回调数据: " . json_encode($notifyData));
    
    // 解密回调数据
    if (isset($notifyData['resource']['ciphertext'])) {
        $decryptResult = $wxPayHelper->decryptNotifyData(
            $notifyData['resource']['ciphertext'], 
            $notifyData['resource']['associated_data'] ?? '', 
            $notifyData['resource']['nonce']
        );
        
        if (isset($decryptResult['error']) && $decryptResult['error']) {
            log_notify("解密回调数据失败: " . $decryptResult['msg'], 'ERROR');
            http_response_code(200);
            echo json_encode(['code' => 'SUCCESS', 'message' => 'OK']);
            exit;
        }
        
        log_notify("解密后的回调数据: " . json_encode($decryptResult));
        $resource = $decryptResult;
    } else {
        log_notify("回调数据没有加密内容", 'ERROR');
        http_response_code(200);
        echo json_encode(['code' => 'SUCCESS', 'message' => 'OK']);
        exit;
    }
} else {
    log_notify("验证成功，处理回调数据");
    // 使用验证成功的数据
    $notifyData = $verifyResult['data'];
    $resource = isset($notifyData['resource']['plaintext']) ? $notifyData['resource']['plaintext'] : [];
}

// 检查支付状态
if (!isset($resource['trade_state'])) {
    log_notify("回调数据中缺少trade_state", 'ERROR');
    // 仍然返回成功，避免微信重试
    http_response_code(200);
    echo json_encode(['code' => 'SUCCESS', 'message' => 'OK']);
    exit;
}

// 提取关键信息
$tradeState = $resource['trade_state'];
$outTradeNo = $resource['out_trade_no'];
$transactionId = $resource['transaction_id'] ?? '';

log_notify("订单号: $outTradeNo, 交易状态: $tradeState, 微信支付交易号: $transactionId");

// 处理支付结果
if ($tradeState === 'SUCCESS') {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        // 处理支付成功
        $result = processSuccessfulPayment($conn, $outTradeNo, $transactionId);
        log_notify("支付处理结果: " . ($result ? "成功" : "失败"));
    } catch (Exception $e) {
        log_notify("处理支付回调时出错: " . $e->getMessage(), 'ERROR');
    }
} else {
    log_notify("交易状态非成功: $tradeState, 不处理支付结果");
}

// 始终返回成功，避免微信重试
http_response_code(200);
echo json_encode(['code' => 'SUCCESS', 'message' => 'OK']);

/**
 * 处理支付成功逻辑
 * 
 * @param PDO $conn 数据库连接
 * @param string $outTradeNo 订单号
 * @param string $transactionId 微信支付交易号
 * @return bool 处理结果
 */
function processSuccessfulPayment($conn, $outTradeNo, $transactionId) {
    log_notify("开始处理支付成功逻辑 - 订单号: $outTradeNo");
    
    // 开始事务
    $conn->beginTransaction();
    
    try {
        // 判断订单类型（根据前缀）
        $orderType = 'unknown';
        if (strpos($outTradeNo, 'CYYG') === 0) {
            $orderType = 'recharge'; // 充值订单
        } elseif (strpos($outTradeNo, 'DZSD') === 0) {
            $orderType = 'donation'; // 打赏订单
        } elseif (strpos($outTradeNo, 'FACE') === 0) {
            $orderType = 'face_analysis'; // 面容分析订单
        }
        
        log_notify("订单类型: $orderType");
        
        if ($orderType === 'recharge') {
            // 处理充值订单逻辑
        // 查询订单信息
        $stmt = $conn->prepare("
            SELECT * FROM recharge_records 
            WHERE order_id = :order_id 
            AND status = 'pending'
        ");
        $stmt->bindParam(':order_id', $outTradeNo);
        $stmt->execute();
        
        $order = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$order) {
                log_notify("未找到待支付充值订单: $outTradeNo 或订单已处理", 'WARNING');
            $conn->rollBack();
            return false;
        }
        
        $userId = $order['user_id'];
        $count = $order['count'];
        
            log_notify("找到充值订单: ID={$order['id']}, 用户ID=$userId, 次数=$count");
        
        // 更新订单状态
        $updateStmt = $conn->prepare("
            UPDATE recharge_records 
            SET 
                status = 'success', 
                transaction_id = :transaction_id, 
                paid_at = NOW() 
            WHERE id = :id
        ");
        $updateStmt->bindParam(':transaction_id', $transactionId);
        $updateStmt->bindParam(':id', $order['id'], PDO::PARAM_INT);
        $updateStmt->execute();
        
            log_notify("充值订单状态已更新为支付成功");
        
        // 更新用户试衣次数
        $tryOnCountMode = defined('TRY_ON_COUNT_MODE') ? TRY_ON_COUNT_MODE : 'daily';
        
        if ($tryOnCountMode === 'dual') {
            // 双层次数模式：增加付费次数
            $updateUserStmt = $conn->prepare("
                UPDATE users 
                SET 
                    paid_try_on_count = paid_try_on_count + :count 
                WHERE id = :user_id
            ");
            $updateUserStmt->bindParam(':count', $count, PDO::PARAM_INT);
            $updateUserStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $updateUserStmt->execute();
            
            log_notify("用户付费试衣次数已增加: +$count");
        } else if ($tryOnCountMode === 'database') {
            // 数据库模式：增加paid_try_on_count（原try_on_count）
            $updateUserStmt = $conn->prepare("
                UPDATE users 
                SET 
                    paid_try_on_count = paid_try_on_count + :count 
                WHERE id = :user_id
            ");
            $updateUserStmt->bindParam(':count', $count, PDO::PARAM_INT);
            $updateUserStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $updateUserStmt->execute();
            
            log_notify("用户试衣次数已增加: +$count");
        } else {
            // 每日一次模式：创建用户的paid_try_on_count字段并增加次数
            // 检查users表是否有paid_try_on_count字段
            $checkFieldStmt = $conn->query("SHOW COLUMNS FROM users LIKE 'paid_try_on_count'");
            
            if ($checkFieldStmt->rowCount() === 0) {
                // 字段不存在，添加字段
                $conn->exec("ALTER TABLE users ADD COLUMN paid_try_on_count INT NOT NULL DEFAULT 0 AFTER gender");
                log_notify("用户表添加paid_try_on_count字段");
            }
            
            // 增加付费次数
            $updateUserStmt = $conn->prepare("
                UPDATE users 
                SET 
                    paid_try_on_count = paid_try_on_count + :count 
                WHERE id = :user_id
            ");
            $updateUserStmt->bindParam(':count', $count, PDO::PARAM_INT);
            $updateUserStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $updateUserStmt->execute();
            
            log_notify("用户付费试衣次数已增加: +$count");
            }
        } elseif ($orderType === 'donation') {
            // 处理打赏订单逻辑
            // 查询打赏订单信息
            $stmt = $conn->prepare("
                SELECT * FROM donations 
                WHERE order_id = :order_id 
                AND status = 'pending'
            ");
            $stmt->bindParam(':order_id', $outTradeNo);
            $stmt->execute();
            
            $donation = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$donation) {
                log_notify("未找到待支付打赏订单: $outTradeNo 或订单已处理", 'WARNING');
                $conn->rollBack();
                return false;
            }
            
            $userId = $donation['user_id'];
            $amount = $donation['amount'];
            
            log_notify("找到打赏订单: ID={$donation['id']}, 用户ID=$userId, 金额=$amount");
            
            // 更新打赏订单状态
            $updateStmt = $conn->prepare("
                UPDATE donations 
                SET 
                    status = 'success', 
                    transaction_id = :transaction_id, 
                    paid_at = NOW() 
                WHERE id = :id
            ");
            $updateStmt->bindParam(':transaction_id', $transactionId);
            $updateStmt->bindParam(':id', $donation['id'], PDO::PARAM_INT);
            $updateStmt->execute();
            
            log_notify("打赏订单状态已更新为支付成功");

            // 这里可以添加其他打赏成功后的业务逻辑，如发送通知等
        } elseif ($orderType === 'face_analysis') {
            // 处理面容分析订单逻辑
            // 查询面容分析订单信息
            $stmt = $conn->prepare("
                SELECT * FROM face_analysis
                WHERE order_id = :order_id
                AND payment_status = 'unpaid'
            ");
            $stmt->bindParam(':order_id', $outTradeNo);
            $stmt->execute();

            $analysis = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$analysis) {
                log_notify("未找到待支付面容分析订单: $outTradeNo 或订单已处理", 'WARNING');
                $conn->rollBack();
                return false;
            }

            $userId = $analysis['user_id'];
            $amount = $analysis['amount'];

            log_notify("找到面容分析订单: ID={$analysis['id']}, 用户ID=$userId, 金额=$amount");

            // 更新面容分析订单状态
            $updateStmt = $conn->prepare("
                UPDATE face_analysis
                SET
                    payment_status = 'paid',
                    updated_at = NOW()
                WHERE id = :id
            ");
            $updateStmt->bindParam(':id', $analysis['id'], PDO::PARAM_INT);
            $updateStmt->execute();

            log_notify("面容分析订单状态已更新为支付成功");
        } else {
            // 未知订单类型，记录日志
            log_notify("未知订单类型: $orderType, 订单号: $outTradeNo", 'WARNING');
        }
        
        // 提交事务
        $conn->commit();
        log_notify("支付处理事务已提交");
        return true;
    } catch (Exception $e) {
        // 回滚事务
        $conn->rollBack();
        log_notify("支付处理失败，事务已回滚: " . $e->getMessage(), 'ERROR');
        return false;
    }
} 