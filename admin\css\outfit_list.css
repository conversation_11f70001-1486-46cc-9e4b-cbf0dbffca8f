/* 添加图片网格样式 */
.image-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 1px;
    width: auto;
    height: 120px;
    position: relative;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.grid-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    max-width: 140px;
    max-height: 140px;
}

.more-images {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 9px;
    font-weight: bold;
    position: absolute;
    right: 2px;
    bottom: 2px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
}

.thumbnail {
    height: 120px;
    width: auto;
    max-width: 100%;
    max-width: 180px;
    max-height: 120px;
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 表格样式 */
.outfit-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.outfit-table th, .outfit-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #e8e8e8;
    vertical-align: middle; /* 垂直居中 */
}

.outfit-table th {
    font-weight: 600;
    background-color: #fafafa;
}

.outfit-table td img {
    display: block;
    max-height: 120px;
    max-width: 180px;
    width: auto;
    margin: 0 auto;
    object-fit: contain;
}

/* 图片单元格固定宽度 */
.outfit-table td:nth-child(2) {
    width: 140px;
    text-align: center;
}

/* 操作按钮 */
.action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.view-btn {
    background-color: #1890ff;
    color: white;
}

.view-btn:hover {
    background-color: #40a9ff;
}

/* 空数据和错误提示 */
.no-data {
    padding: 20px;
    text-align: center;
    color: #999;
}

.error {
    color: #f5222d;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .image-grid {
        height: 200px;
    }
    
    .thumbnail {
        height: 200px;
        max-width: 160px;
        max-height: 200px;
    }
    
    .outfit-table td img {
        max-height: 200px;
        max-width: 160px;
    }
    
    .grid-image {
        max-width: 110px;
        max-height: 110px;
    }
}

@media (max-width: 992px) {
    .outfit-table th:nth-child(5),
    .outfit-table td:nth-child(5) {
        display: none; /* 隐藏分类列 */
    }
    
    .image-grid {
        height: 180px;
    }
    
    .thumbnail {
        height: 180px;
        max-width: 140px;
        max-height: 180px;
    }
    
    .outfit-table td img {
        max-height: 180px;
        max-width: 140px;
    }
    
    .grid-image {
        max-width: 100px;
        max-height: 100px;
    }
}

@media (max-width: 768px) {
    .outfit-table th:nth-child(4),
    .outfit-table td:nth-child(4),
    .outfit-table th:nth-child(6),
    .outfit-table td:nth-child(6) {
        display: none; /* 隐藏用户ID和衣物数量列 */
    }
    
    .image-grid {
        height: 150px;
    }
    
    .thumbnail {
        height: 150px;
        max-width: 120px;
        max-height: 150px;
    }
    
    .outfit-table td img {
        max-height: 150px;
        max-width: 120px;
    }
    
    .grid-image {
        max-width: 90px;
        max-height: 90px;
    }
}

@media (max-width: 576px) {
    .outfit-table th:nth-child(1),
    .outfit-table td:nth-child(1),
    .outfit-table th:nth-child(7),
    .outfit-table td:nth-child(7) {
        display: none; /* 隐藏ID和创建时间列 */
    }
    
    .image-grid {
        height: 120px;
    }
    
    .thumbnail {
        height: 120px;
        max-width: 100px;
        max-height: 120px;
    }
    
    .outfit-table td img {
        max-height: 120px;
        max-width: 100px;
    }
    
    .grid-image {
        max-width: 70px;
        max-height: 70px;
    }
}

/* 穿搭列表页样式 */
.outfits-container {
    padding: 20px;
}

.search-filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
}

.search-box {
    flex: 1;
    min-width: 200px;
    max-width: 400px;
}

.filter-box {
    min-width: 150px;
}

.outfits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.outfit-card {
    background-color: #fff;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    transition: all 0.25s ease;
    border: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
}

.outfit-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.outfit-image-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 2px;
    height: 120px;
    background-color: #f9f9f9;
    border-bottom: 1px solid #f0f0f0;
}

.outfit-image-container {
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f9f9f9;
}

.outfit-image {
    max-width: 85%;
    max-height: 85%;
    max-width: 120px;
    max-height: 120px;
    width: auto;
    height: auto;
    object-fit: contain;
    transition: all 0.2s ease-in-out;
}

.outfit-card:hover .outfit-image {
    max-width: 90%;
    max-height: 90%;
    max-width: 130px;
    max-height: 130px;
}

.outfit-info {
    padding: 12px 15px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.outfit-name {
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 6px;
    color: #333;
    /* 防止过长的文本 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.outfit-category {
    font-size: 14px;
    color: #8c8c8c;
    margin-bottom: 8px;
}

.outfit-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
    color: #999;
    margin-top: auto;
}

.outfit-meta .clothes-count {
    display: flex;
    align-items: center;
}

.outfit-meta .clothes-count i {
    margin-right: 4px;
}

.outfit-meta .date {
    color: #999;
}

.outfit-actions {
    padding: 10px 15px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

/* 加载状态和错误提示 */
.loading, .no-data, .error {
    padding: 30px;
    text-align: center;
}

.loading {
    color: #1890ff;
}

.no-data {
    color: #999;
}

.error {
    color: #ff4d4f;
}

/* 分页样式 */
.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

/* 响应式布局 */
@media (max-width: 1200px) {
    .outfits-grid {
        grid-template-columns: repeat(auto-fill, minmax(230px, 1fr));
    }
    
    .outfit-image-grid {
        height: 110px;
    }
    
    .outfit-image {
        max-width: 110px;
        max-height: 110px;
    }
    
    .outfit-card:hover .outfit-image {
        max-width: 120px;
        max-height: 120px;
    }
}

@media (max-width: 992px) {
    .outfits-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .outfit-image-grid {
        height: 100px;
    }
    
    .outfit-image {
        max-width: 100px;
        max-height: 100px;
    }
    
    .outfit-card:hover .outfit-image {
        max-width: 110px;
        max-height: 110px;
    }
}

@media (max-width: 768px) {
    .search-filter-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box, .filter-box {
        max-width: 100%;
    }
    
    .outfits-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 15px;
    }
    
    .outfit-image-grid {
        height: 90px;
    }
    
    .outfit-image {
        max-width: 90px;
        max-height: 90px;
    }
    
    .outfit-card:hover .outfit-image {
        max-width: 100px;
        max-height: 100px;
    }
}

@media (max-width: 576px) {
    .outfits-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }
    
    .outfit-image-grid {
        height: 80px;
    }
    
    .outfit-image {
        max-width: 80px;
        max-height: 80px;
    }
    
    .outfit-info {
        padding: 10px 12px;
    }
    
    .outfit-name {
        font-size: 14px;
    }
    
    .outfit-category, .outfit-meta {
        font-size: 12px;
    }
    
    .outfit-card:hover .outfit-image {
        max-width: 90px;
        max-height: 90px;
    }
} 