<?php
/**
 * 移动衣物API
 * 
 * 将衣物移动到指定衣橱
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * POST 参数:
 * - clothes_ids: 衣物ID数组，如 [1, 2, 3]（必填）
 * - target_wardrobe_id: 目标衣橱ID（必填）
 * 
 * 返回:
 * {
 *   "error": false,
 *   "msg": "衣物移动成功",
 *   "data": {
 *     "moved_count": 3,
 *     "target_wardrobe": {
 *       "id": 2,
 *       "name": "春季衣橱"
 *     }
 *   }
 * }
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 开启错误日志
error_log("===== 开始处理移动衣物请求 =====");

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => true, 'msg' => '仅支持POST请求']);
    exit();
}

// 检查Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode(['error' => true, 'msg' => '缺少认证信息']);
    exit();
}

// 获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取用户ID
$userId = $payload['sub'];

// 获取请求数据
$requestBody = file_get_contents('php://input');
$requestData = json_decode($requestBody, true);

// 如果请求体为空，则尝试从POST数据获取
if (empty($requestData)) {
    $requestData = $_POST;
}

error_log("移动衣物请求数据: " . print_r($requestData, true));

// 检查必要的参数
if (!isset($requestData['source_wardrobe_id']) || empty($requestData['source_wardrobe_id'])) {
    echo json_encode(['error' => true, 'msg' => '缺少源衣柜ID']);
    exit();
}

if (!isset($requestData['target_wardrobe_id']) || empty($requestData['target_wardrobe_id'])) {
    echo json_encode(['error' => true, 'msg' => '缺少目标衣柜ID']);
    exit();
}

if (!isset($requestData['clothes_ids']) || !is_array($requestData['clothes_ids']) || empty($requestData['clothes_ids'])) {
    echo json_encode(['error' => true, 'msg' => '缺少衣物ID列表']);
    exit();
}

$sourceWardrobeId = $requestData['source_wardrobe_id'];
$targetWardrobeId = $requestData['target_wardrobe_id'];
$clothesIds = $requestData['clothes_ids'];

// 可选的目标分类参数
$targetCategory = $requestData['target_category'] ?? null;

// 获取数据库连接
$db = new Database();
$conn = $db->getConnection();

try {
    // 开始事务
    $conn->beginTransaction();
    
    // 1. 验证源衣柜是否存在并且属于当前用户
    $checkSourceWardrobeStmt = $conn->prepare("
        SELECT * FROM wardrobes 
        WHERE id = :wardrobe_id AND user_id = :user_id
    ");
    $checkSourceWardrobeStmt->bindParam(':wardrobe_id', $sourceWardrobeId);
    $checkSourceWardrobeStmt->bindParam(':user_id', $userId);
    $checkSourceWardrobeStmt->execute();
    
    $sourceWardrobeExists = $checkSourceWardrobeStmt->rowCount() > 0;
    
    if (!$sourceWardrobeExists) {
        $conn->rollBack();
        echo json_encode(['error' => true, 'msg' => '源衣柜不存在或无权操作']);
        exit();
    }
    
    // 2. 验证目标衣柜是否存在并且属于当前用户
    $checkTargetWardrobeStmt = $conn->prepare("
        SELECT * FROM wardrobes 
        WHERE id = :wardrobe_id AND user_id = :user_id
    ");
    $checkTargetWardrobeStmt->bindParam(':wardrobe_id', $targetWardrobeId);
    $checkTargetWardrobeStmt->bindParam(':user_id', $userId);
    $checkTargetWardrobeStmt->execute();
    
    $targetWardrobeExists = $checkTargetWardrobeStmt->rowCount() > 0;
    
    if (!$targetWardrobeExists) {
        $conn->rollBack();
        echo json_encode(['error' => true, 'msg' => '目标衣柜不存在或无权操作']);
        exit();
    }
    
    // 3. 验证所有衣物是否存在并且属于源衣柜
    $invalidClothes = [];
    $validClothes = [];
    
    foreach ($clothesIds as $clothingId) {
        $checkClothingStmt = $conn->prepare("
            SELECT * FROM clothes 
            WHERE id = :clothing_id AND wardrobe_id = :wardrobe_id
        ");
        $checkClothingStmt->bindParam(':clothing_id', $clothingId);
        $checkClothingStmt->bindParam(':wardrobe_id', $sourceWardrobeId);
        $checkClothingStmt->execute();
        
        if ($checkClothingStmt->rowCount() === 0) {
            $invalidClothes[] = $clothingId;
        } else {
            $validClothes[] = $clothingId;
        }
    }
    
    // 如果有无效的衣物，返回错误
    if (!empty($invalidClothes)) {
        $conn->rollBack();
        echo json_encode([
            'error' => true, 
            'msg' => '部分衣物不存在或无权操作',
            'invalid_clothes' => $invalidClothes
        ]);
        exit();
    }
    
    // 4. 移动有效的衣物到目标衣柜
    $successCount = 0;
    $failCount = 0;
    
    foreach ($validClothes as $clothingId) {
        // 根据是否指定目标分类来构建不同的SQL
        if ($targetCategory) {
            $moveClothingStmt = $conn->prepare("
                UPDATE clothes 
                SET wardrobe_id = :target_wardrobe_id, category = :target_category
                WHERE id = :clothing_id AND wardrobe_id = :source_wardrobe_id
            ");
            $moveClothingStmt->bindParam(':target_category', $targetCategory);
        } else {
        $moveClothingStmt = $conn->prepare("
            UPDATE clothes 
            SET wardrobe_id = :target_wardrobe_id 
            WHERE id = :clothing_id AND wardrobe_id = :source_wardrobe_id
        ");
        }
        
        $moveClothingStmt->bindParam(':target_wardrobe_id', $targetWardrobeId);
        $moveClothingStmt->bindParam(':clothing_id', $clothingId);
        $moveClothingStmt->bindParam(':source_wardrobe_id', $sourceWardrobeId);
        $moveClothingStmt->execute();
        
        if ($moveClothingStmt->rowCount() > 0) {
            $successCount++;
        } else {
            $failCount++;
            $invalidClothes[] = $clothingId;
        }
    }
    
    // 如果所有衣物都移动失败，返回错误
    if ($successCount === 0 && $failCount > 0) {
        $conn->rollBack();
        echo json_encode([
            'error' => true, 
            'msg' => '所有衣物移动失败',
            'failed_clothes' => $invalidClothes
        ]);
        exit();
    }
    
    // 提交事务
    $conn->commit();
    
    // 返回成功响应
    $responseMessage = $targetCategory ? 
        '衣物移动并更新分类成功' : 
        '衣物移动成功';
    
    echo json_encode([
        'error' => false,
        'msg' => $responseMessage,
        'data' => [
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'category_updated' => $targetCategory ? true : false
        ]
    ]);
    
} catch (PDOException $e) {
    // 回滚事务
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    error_log('移动衣物错误: ' . $e->getMessage());
    echo json_encode(['error' => true, 'msg' => '移动衣物时发生错误: ' . $e->getMessage()]);
    exit();
}

// 记录操作完成
error_log("===== 衣物移动请求处理完成 =====");
?> 