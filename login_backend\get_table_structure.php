<?php
/**
 * 临时脚本：获取数据库中所有表的结构
 * 特别查找可能包含图片URL的列
 */

// 引入数据库配置
require_once 'config.php';
require_once 'db.php';

// 连接数据库
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 获取所有表名
    $tables = [];
    $stmt = $conn->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }
    
    echo "数据库中的表：\n";
    echo "=============\n\n";
    
    // 遍历每个表，获取结构
    foreach ($tables as $table) {
        echo "表名: {$table}\n";
        echo "-------------\n";
        
        // 获取表结构
        $stmt = $conn->query("DESCRIBE {$table}");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "列信息：\n";
        foreach ($columns as $column) {
            echo "  - {$column['Field']} ({$column['Type']})";
            
            // 标记可能包含URL的列
            $isUrlColumn = false;
            if (
                strpos(strtolower($column['Field']), 'url') !== false || 
                strpos(strtolower($column['Field']), 'image') !== false || 
                strpos(strtolower($column['Field']), 'photo') !== false || 
                strpos(strtolower($column['Field']), 'pic') !== false
            ) {
                echo " [可能包含URL]";
                $isUrlColumn = true;
            }
            
            echo "\n";
            
            // 如果是可能包含URL的列，检查样本数据
            if ($isUrlColumn) {
                try {
                    $sampleQuery = $conn->query("SELECT {$column['Field']} FROM {$table} WHERE {$column['Field']} LIKE '%oss-cn-shanghai.aliyuncs.com%' LIMIT 1");
                    $sample = $sampleQuery->fetch(PDO::FETCH_ASSOC);
                    
                    if ($sample && !empty($sample[$column['Field']])) {
                        echo "    样本值: {$sample[$column['Field']]}\n";
                    }
                } catch (PDOException $e) {
                    echo "    无法获取样本值: {$e->getMessage()}\n";
                }
            }
        }
        
        // 检查JSON列是否可能包含URL
        foreach ($columns as $column) {
            if (strpos(strtolower($column['Type']), 'json') !== false || 
                strpos(strtolower($column['Type']), 'text') !== false) {
                try {
                    $sampleQuery = $conn->query("SELECT {$column['Field']} FROM {$table} WHERE {$column['Field']} LIKE '%oss-cn-shanghai.aliyuncs.com%' LIMIT 1");
                    $sample = $sampleQuery->fetch(PDO::FETCH_ASSOC);
                    
                    if ($sample && !empty($sample[$column['Field']])) {
                        echo "  - {$column['Field']} 可能在JSON/TEXT中包含URL\n";
                        echo "    样本值: " . substr($sample[$column['Field']], 0, 150) . "...\n";
                    }
                } catch (PDOException $e) {
                    // 忽略错误
                }
            }
        }
        
        echo "\n\n";
    }
    
    echo "分析完成。\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
} 