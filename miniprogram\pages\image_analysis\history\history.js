const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    analysisRecords: [],
    statusText: {
      'pending': '待分析',
      'processing': '分析中',
      'completed': '已完成',
      'failed': '分析失败'
    },
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    isLoading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('==== 形象分析历史页面加载 ====');
    this.loadAnalysisHistory(1);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.log('==== 形象分析历史页面显示 ====');
    // 避免重复加载，只在第一次显示或数据为空时加载
    if (this.data.analysisRecords.length === 0) {
      this.loadAnalysisHistory(1);
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    console.log('==== 下拉刷新触发 ====');
    this.setData({
      currentPage: 1,
      hasMore: true,
      analysisRecords: [] // 清空现有数据
    });
    this.loadAnalysisHistory(1, false, () => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    console.log('!!!!! 触发上拉触底事件 !!!!!');
    console.log('当前状态:', {
      hasMore: this.data.hasMore,
      isLoading: this.data.isLoading,
      currentPage: this.data.currentPage,
      记录数: this.data.analysisRecords.length
    });
    
    if (this.data.hasMore && !this.data.isLoading) {
      const nextPage = this.data.currentPage + 1;
      console.log('准备加载第' + nextPage + '页数据');
      this.loadAnalysisHistory(nextPage, true);
    } else {
      console.log('不满足加载条件:', {
        hasMore: this.data.hasMore ? '有更多数据' : '没有更多数据',
        isLoading: this.data.isLoading ? '正在加载中' : '非加载状态'
      });
    }
  },

  /**
   * 加载分析历史
   * @param {number} page 页码
   * @param {boolean} isLoadMore 是否为加载更多
   * @param {Function} callback 回调函数
   */
  loadAnalysisHistory: function (page = 1, isLoadMore = false, callback) {
    // 检查是否登录
    if (!wx.getStorageSync('token')) {
      wx.showModal({
        title: '提示',
        content: '请先登录以查看您的形象分析历史',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          } else {
            wx.navigateBack();
          }
        }
      });
      return;
    }

    // 设置加载状态
    this.setData({
      isLoading: true
    });

    // 显示加载提示
    if (!isLoadMore) {
      wx.showLoading({
        title: '加载中...',
      });
    } else {
      wx.showToast({
        title: '加载更多...',
        icon: 'loading',
        duration: 500
      });
    }

    const token = wx.getStorageSync('token');
    const requestParams = {
      page: page,
      limit: this.data.pageSize
    };
    
    console.log('请求形象分析历史数据:', requestParams);

    wx.request({
      url: app.globalData.baseUrl + '/get_image_analysis_history.php',
      method: 'GET',
      header: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      data: requestParams,
      success: (res) => {
        wx.hideLoading();
        
        console.log('形象分析历史响应数据:', res.data);
        
        if (res.data && !res.data.error) {
          const newRecords = res.data.data.results || [];
          const total = parseInt(res.data.data.total) || 0; // 确保转换为数字
          const totalPages = parseInt(res.data.data.total_pages) || 1; // 确保转换为数字
          
          console.log('数据解析结果:', {
            记录数: newRecords.length,
            总数: total,
            总页数: totalPages,
            当前页: page,
            是否有更多: page < totalPages
          });
          
          // 检查是否有新数据
          if (newRecords.length === 0) {
            console.log('当前页没有数据');
            this.setData({
              hasMore: false
            });
            return;
          }
          
          // 更新数据
          this.setData({
            analysisRecords: isLoadMore ? [...this.data.analysisRecords, ...newRecords] : newRecords,
            currentPage: page, // 更新当前页码
            hasMore: page < totalPages // 更新是否有更多数据
          });
          
          console.log('更新后的状态:', {
            记录总数: this.data.analysisRecords.length,
            当前页码: this.data.currentPage,
            是否有更多: this.data.hasMore
          });
        } else {
          console.error('获取历史记录失败:', res.data);
          wx.showToast({
            title: '获取历史记录失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('请求失败:', error);
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({
          isLoading: false
        });
        
        if (typeof callback === 'function') {
          callback();
        }
      }
    });
  },

  /**
   * 查看分析详情
   */
  viewDetail: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/image_analysis/detail/detail?id=' + id
    });
  },

  /**
   * 前往形象分析页面
   */
  goToAnalysis: function () {
    wx.redirectTo({
      url: '/pages/image_analysis/index/index'
    });
  },
  
  /**
   * 手动加载更多
   * 添加此函数作为备选方案，可以在页面底部添加"加载更多"按钮调用
   */
  loadMore: function() {
    if (this.data.hasMore && !this.data.isLoading) {
      const nextPage = this.data.currentPage + 1;
      console.log('手动加载更多，页码:', nextPage);
      this.loadAnalysisHistory(nextPage, true);
    }
  },

  /**
   * 显示删除确认弹框
   */
  showDeleteConfirm: function(e) {
    const id = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条形象分析记录吗？删除后将无法恢复。',
      confirmText: '删除',
      confirmColor: '#FF0000',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.deleteAnalysisRecord(id);
        }
      }
    });
  },
  
  /**
   * 删除形象分析记录
   */
  deleteAnalysisRecord: function(id) {
    // 检查是否登录
    if (!wx.getStorageSync('token')) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    const token = wx.getStorageSync('token');
    
    // 显示加载提示
    wx.showLoading({
      title: '删除中...',
    });
    
    wx.request({
      url: app.globalData.baseUrl + '/delete_image_analysis.php',
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      data: {
        id: id
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.data && !res.data.error) {
          // 删除成功
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
          
          // 从列表中移除已删除的记录
          const updatedRecords = this.data.analysisRecords.filter(item => item.id !== id);
          
          this.setData({
            analysisRecords: updatedRecords
          });
          
          // 如果删除后列表为空，显示空状态
          if (updatedRecords.length === 0) {
            this.setData({
              hasMore: false
            });
          }
          
          console.log('删除记录后的状态:', {
            记录总数: this.data.analysisRecords.length
          });
        } else {
          // 删除失败
          wx.showToast({
            title: res.data.msg || '删除失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('删除请求失败:', error);
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 基于形象分析推荐穿搭
   */
  goToRecommendation: function () {
    // 检查是否有已完成的分析
    const completedAnalyses = this.data.analysisRecords.filter(analysis => analysis.status === 'completed');
    
    if (completedAnalyses.length === 0) {
      wx.showToast({
        title: '没有已完成的形象分析',
        icon: 'none'
      });
      return;
    }
    
    // 如果只有一个已完成的分析，直接跳转
    if (completedAnalyses.length === 1) {
      wx.navigateTo({
        url: `/pages/clothing_recommendation/clothing_recommendation?analysis_id=${completedAnalyses[0].id}&type=image_analysis`
      });
      return;
    }
    
    // 如果有多个已完成的分析，让用户选择一个
    wx.showActionSheet({
      itemList: completedAnalyses.map(analysis => `形象分析 #${analysis.id} (${analysis.created_at})`),
      success: (res) => {
        if (!res.cancel) {
          const selectedAnalysis = completedAnalyses[res.tapIndex];
          wx.navigateTo({
            url: `/pages/clothing_recommendation/clothing_recommendation?analysis_id=${selectedAnalysis.id}&type=image_analysis`
          });
        }
      }
    });
  }
}) 