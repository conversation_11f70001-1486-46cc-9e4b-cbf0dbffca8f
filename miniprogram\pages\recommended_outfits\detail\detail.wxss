.container {
  background-color: #f7f7f7;
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部操作栏预留空间 */
  padding-top: 10rpx; /* 顶部轻微间隔 */
}

/* 加载中样式 */
.loading-container {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-container {
  padding: 120rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.error-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  border-radius: 50%;
  background-color: #f56c6c;
  position: relative;
}

.error-icon::before,
.error-icon::after {
  content: '';
  position: absolute;
  width: 40rpx;
  height: 6rpx;
  background-color: #fff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
}

.error-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.error-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.error-button {
  padding: 12rpx 30rpx;
  background-color: #07c160;
  color: #fff;
  font-size: 26rpx;
  border-radius: 30rpx;
}

/* 主图区域 */
.outfit-image-container {
  width: 100%;
  height: 750rpx; /* 正方形大图 */
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-top: 20rpx; /* 顶部间隔 */
  border-radius: 12rpx; /* 添加圆角 */
}

.outfit-swiper {
  width: 100%;
  height: 100%;
}

.outfit-image {
  width: 95%;
  height: 95%;
  object-fit: contain; /* 确保图片不被裁剪 */
  display: block;
  margin: 0 auto;
}

/* 穿搭详情 */
.outfit-header {
  padding: 30rpx 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.outfit-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.outfit-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 26rpx;
  color: #999;
}

.outfit-category {
  background-color: #f2f2f2;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.outfit-content-section {
  padding: 30rpx 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.outfit-description {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  font-weight: 500;
  margin-bottom: 20rpx;
  white-space: pre-wrap; /* 保留换行和空格 */
}

.outfit-recommendation {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  position: relative;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  white-space: pre-wrap; /* 保留换行和空格 */
}

/* 衣物组合区域 */
.outfit-items-section {
  background-color: #fff;
  padding: 30rpx 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background-color: #000;
  border-radius: 4rpx;
}

.item-list {
  display: flex;
  flex-direction: column;
}

.item-card {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  align-items: center;
}

.item-card:last-child {
  border-bottom: none;
}

.item-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  background-color: #f9f9f9;
  object-fit: contain;
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.item-price {
  font-size: 32rpx;
  color: #f56c6c;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.item-buy-button {
  align-self: flex-end;
  padding: 8rpx 30rpx;
  background-color: #000;
  color: #fff;
  border-radius: 30rpx;
  font-size: 24rpx;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 0;
  box-shadow: 0 -2rpx 0rpx rgba(0, 0, 0, 0.05);
}

.share-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
  padding: 0;
  border: none;
}

.share-button::after {
  border: none;
}

.icon-share {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
  border-radius: 0;
}

/* 大图弹框样式 */
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #000000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  border-radius: 16rpx; /* 整体添加圆角 */
}

.image-modal.show {
  opacity: 1;
  visibility: visible;
}

/* 自定义指示点样式 - 仅适用于大图弹窗 */
.wx-swiper-dots {
  position: fixed;
  bottom: 120rpx !important;
}

.wx-swiper-dot {
  width: 16rpx !important;
  height: 16rpx !important;
  margin: 0 8rpx !important;
  background-color: rgba(255, 255, 255, 0.5) !important;
}

.wx-swiper-dot-active {
  background-color: #ffffff !important;
}

/* 自定义轮播指示点样式 */
.swiper-dots-container {
  padding: 20rpx 0;
  display: flex;
  justify-content: center;
}

.swiper-dots {
  display: flex;
  justify-content: center;
  align-items: center;
}

.swiper-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #cccccc;
  margin: 0 8rpx;
  transition: all 0.3s;
}

.swiper-dot.active {
  background-color: #000000;
  width: 16rpx;
  height: 16rpx;
}

/* 关闭按钮样式 */
.close-btn-container {
  position: fixed;
  top: 30rpx;
  right: 30rpx;
  width: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
  pointer-events: auto;
}

.close-icon {
  width: 26rpx;
  height: 26rpx;
  position: relative;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 12rpx;
}

.close-icon::before,
.close-icon::after {
  content: '';
  position: absolute;
  width: 70%;
  height: 1.5rpx;
  background-color: #ffffff;
  top: 50%;
  left: 15%;
}

.close-icon::before {
  transform: translateY(-50%) rotate(45deg);
}

.close-icon::after {
  transform: translateY(-50%) rotate(-45deg);
}

.modal-content {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: transparent;
  border-radius: 16rpx;
  padding: 0;
  touch-action: pan-x pan-y;
}

.modal-swiper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  touch-action: pan-x pan-y;
}

.large-image {
  width: 100%; 
  height: 90vh;
  object-fit: contain; /* 确保图片保持比例且完整显示 */
  display: flex;
  margin: auto;
  border-radius: 16rpx; /* 添加圆角 */
}

/* 已移除关闭按钮和保存按钮样式 */ 