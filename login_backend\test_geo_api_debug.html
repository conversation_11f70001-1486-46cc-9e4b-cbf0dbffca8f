<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>和风天气地理编码API调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1, h2, h3 {
            color: #333;
        }
        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .form-container {
            flex: 1;
            min-width: 300px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .result-container {
            flex: 2;
            min-width: 500px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
            max-height: 600px;
            overflow: auto;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, button {
            padding: 8px 12px;
            width: 100%;
            box-sizing: border-box;
            margin-bottom: 10px;
        }
        button {
            background-color: #4CAF50;
            border: none;
            color: white;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f1f1f1;
            padding: 10px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .note {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 10px;
            margin: 15px 0;
        }
        .tabs {
            display: flex;
            margin-bottom: 10px;
        }
        .tab {
            padding: 8px 16px;
            cursor: pointer;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-bottom: none;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #fff;
            border-bottom: 1px solid #fff;
        }
        .tab-content {
            display: none;
            border: 1px solid #ddd;
            padding: 15px;
        }
        .tab-content.active {
            display: block;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            color: #666;
        }
        .debug-info {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        .debug-info h3 {
            margin-top: 0;
        }
    </style>
</head>
<body>
    <h1>和风天气地理编码API调试工具</h1>
    
    <div class="note">
        <p>此工具用于测试和调试和风天气地理编码API的请求和响应。可以通过坐标或城市名称查询，并查看详细的响应信息。</p>
    </div>

    <div class="container">
        <div class="form-container">
            <h2>API请求参数</h2>
            
            <div class="form-group">
                <label for="queryType">查询类型:</label>
                <select id="queryType">
                    <option value="coordinates">使用经纬度查询</option>
                    <option value="city">使用城市名称查询</option>
                    <option value="cityId">使用城市ID查询</option>
                </select>
            </div>
            
            <div id="coordinatesForm" class="query-form">
                <div class="form-group">
                    <label for="latitude">纬度:</label>
                    <input type="text" id="latitude" value="30.4380786" placeholder="例如：30.4380786">
                </div>
                <div class="form-group">
                    <label for="longitude">经度:</label>
                    <input type="text" id="longitude" value="120" placeholder="例如：120">
                </div>
            </div>
            
            <div id="cityForm" class="query-form" style="display:none;">
                <div class="form-group">
                    <label for="cityName">城市名称:</label>
                    <input type="text" id="cityName" value="杭州" placeholder="例如：杭州">
                </div>
            </div>
            
            <div id="cityIdForm" class="query-form" style="display:none;">
                <div class="form-group">
                    <label for="cityId">城市ID:</label>
                    <input type="text" id="cityId" value="101210101" placeholder="例如：101210101">
                </div>
            </div>
            
            <div class="form-group">
                <label for="apiType">API类型:</label>
                <select id="apiType">
                    <option value="geo">城市查询API (get_city_by_location.php)</option>
                    <option value="test">测试脚本 (test_geo_api.php)</option>
                    <option value="weather">天气API (get_weather.php)</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>调试选项:</label>
                <div>
                    <input type="checkbox" id="debug" checked>
                    <label for="debug" style="display:inline;">启用调试模式</label>
                </div>
                <div>
                    <input type="checkbox" id="rawResponse">
                    <label for="rawResponse" style="display:inline;">显示原始响应</label>
                </div>
            </div>
            
            <button onclick="sendRequest()">发送请求</button>
            <button onclick="getCurrentLocation()" style="background-color: #007bff;">获取当前位置</button>
        </div>
        
        <div class="result-container">
            <h2>API响应结果</h2>
            
            <div class="tabs">
                <div class="tab active" onclick="showTab('formatted')">格式化结果</div>
                <div class="tab" onclick="showTab('raw')">原始响应</div>
                <div class="tab" onclick="showTab('headers')">响应头</div>
                <div class="tab" onclick="showTab('debug')">调试信息</div>
            </div>
            
            <div id="formatted" class="tab-content active">
                <div id="result">
                    <p>点击"发送请求"按钮开始测试</p>
                </div>
            </div>
            
            <div id="raw" class="tab-content">
                <pre id="rawResult">尚未发送请求</pre>
            </div>
            
            <div id="headers" class="tab-content">
                <pre id="headersResult">尚未发送请求</pre>
            </div>
            
            <div id="debug" class="tab-content">
                <div id="debugInfo">尚未发送请求</div>
            </div>
        </div>
    </div>

    <script>
        // 切换查询表单
        document.getElementById('queryType').addEventListener('change', function() {
            document.querySelectorAll('.query-form').forEach(form => {
                form.style.display = 'none';
            });
            
            const queryType = this.value;
            if (queryType === 'coordinates') {
                document.getElementById('coordinatesForm').style.display = 'block';
            } else if (queryType === 'city') {
                document.getElementById('cityForm').style.display = 'block';
            } else if (queryType === 'cityId') {
                document.getElementById('cityIdForm').style.display = 'block';
            }
        });
        
        // 切换标签页
        function showTab(tabId) {
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            document.querySelector(`.tab[onclick="showTab('${tabId}')"]`).classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }
        
        // 获取当前位置
        function getCurrentLocation() {
            document.getElementById('result').innerHTML = '<div class="loading">正在获取位置信息...</div>';
            
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        document.getElementById('latitude').value = position.coords.latitude;
                        document.getElementById('longitude').value = position.coords.longitude;
                        document.getElementById('result').innerHTML = 
                            `<p class="success">获取位置成功!</p>
                            <p>纬度: ${position.coords.latitude}</p>
                            <p>经度: ${position.coords.longitude}</p>`;
                    },
                    function(error) {
                        document.getElementById('result').innerHTML = 
                            `<p class="error">获取位置失败: ${error.message}</p>`;
                    }
                );
            } else {
                document.getElementById('result').innerHTML = 
                    '<p class="error">您的浏览器不支持地理位置功能</p>';
            }
        }
        
        // 发送API请求
        function sendRequest() {
            // 显示加载中
            document.getElementById('result').innerHTML = '<div class="loading">正在发送请求...</div>';
            document.getElementById('rawResult').textContent = '加载中...';
            document.getElementById('headersResult').textContent = '加载中...';
            document.getElementById('debugInfo').innerHTML = '加载中...';
            
            // 构建URL
            let url = '';
            let apiType = document.getElementById('apiType').value;
            const debug = document.getElementById('debug').checked;
            const rawResponse = document.getElementById('rawResponse').checked;
            const queryType = document.getElementById('queryType').value;
            
            if (apiType === 'geo') {
                url = 'get_city_by_location.php';
            } else if (apiType === 'test') {
                url = 'test_geo_api.php';
            } else if (apiType === 'weather') {
                url = 'get_weather.php';
            }
            
            // 添加查询参数
            let params = new URLSearchParams();
            
            if (queryType === 'coordinates') {
                const latitude = document.getElementById('latitude').value.trim();
                const longitude = document.getElementById('longitude').value.trim();
                
                if (!latitude || !longitude) {
                    document.getElementById('result').innerHTML = '<p class="error">请输入有效的经纬度</p>';
                    return;
                }
                
                params.append('latitude', latitude);
                params.append('longitude', longitude);
            } else if (queryType === 'city') {
                const cityName = document.getElementById('cityName').value.trim();
                
                if (!cityName) {
                    document.getElementById('result').innerHTML = '<p class="error">请输入城市名称</p>';
                    return;
                }
                
                params.append('city', cityName);
            } else if (queryType === 'cityId') {
                const cityId = document.getElementById('cityId').value.trim();
                
                if (!cityId) {
                    document.getElementById('result').innerHTML = '<p class="error">请输入城市ID</p>';
                    return;
                }
                
                if (apiType === 'weather') {
                    params.append('cityid', cityId);
                } else {
                    params.append('city', cityId);
                }
            }
            
            // 添加调试参数
            if (debug) {
                params.append('debug', 'true');
            }
            
            if (rawResponse) {
                params.append('raw', 'true');
            }
            
            // 添加随机数防止缓存
            params.append('_', Date.now());
            
            // 发送请求
            const fullUrl = `${url}?${params.toString()}`;
            console.log('Sending request to:', fullUrl);
            
            // 使用Fetch API发送请求
            fetch(fullUrl)
                .then(response => {
                    // 记录响应头
                    const headers = {};
                    response.headers.forEach((value, name) => {
                        headers[name] = value;
                    });
                    
                    document.getElementById('headersResult').textContent = 
                        JSON.stringify(headers, null, 2);
                    
                    // 保存原始响应用于调试
                    return response.text().then(text => {
                        return {
                            status: response.status,
                            statusText: response.statusText,
                            headers: headers,
                            text: text
                        };
                    });
                })
                .then(data => {
                    // 显示原始响应
                    document.getElementById('rawResult').textContent = data.text;
                    
                    // 如果是原始响应模式，直接显示并返回
                    if (rawResponse) {
                        document.getElementById('result').innerHTML = 
                            `<pre>${escapeHtml(data.text)}</pre>`;
                        return;
                    }
                    
                    // 尝试解析为JSON
                    try {
                        const jsonData = JSON.parse(data.text);
                        displayResult(jsonData, data);
                    } catch (error) {
                        // 无法解析为JSON
                        document.getElementById('result').innerHTML = 
                            `<p class="error">无法解析响应为JSON: ${error.message}</p>
                            <pre>${escapeHtml(data.text.substring(0, 1000))}</pre>`;
                        
                        document.getElementById('debugInfo').innerHTML = 
                            `<div class="debug-info">
                                <h3>解析错误</h3>
                                <p><strong>错误消息:</strong> ${error.message}</p>
                                <p><strong>响应长度:</strong> ${data.text.length} 字符</p>
                                <p><strong>响应前50个字符:</strong> ${escapeHtml(data.text.substring(0, 50))}</p>
                                <p><strong>响应字节值:</strong></p>
                                <pre>${getByteValues(data.text.substring(0, 50))}</pre>
                            </div>`;
                    }
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = 
                        `<p class="error">请求失败: ${error.message}</p>`;
                    
                    document.getElementById('debugInfo').innerHTML = 
                        `<div class="debug-info">
                            <h3>网络错误</h3>
                            <p><strong>错误消息:</strong> ${error.message}</p>
                            <p><strong>请求URL:</strong> ${fullUrl}</p>
                        </div>`;
                });
        }
        
        // 显示结果
        function displayResult(jsonData, rawData) {
            let resultHtml = '';
            let debugHtml = '';
            
            // 检查是否有错误
            if (!jsonData.success) {
                resultHtml = `<p class="error">请求失败: ${jsonData.message || '未知错误'}</p>`;
                
                if (jsonData.code) {
                    resultHtml += `<p>错误代码: ${jsonData.code}</p>`;
                }
            } else {
                resultHtml = `<p class="success">请求成功!</p>`;
                
                // 不同API类型显示不同结果
                const apiType = document.getElementById('apiType').value;
                
                if (apiType === 'geo' || apiType === 'test') {
                    // 城市查询API结果
                    if (jsonData.city) {
                        const city = jsonData.city;
                        resultHtml += `
                            <table style="width:100%; border-collapse: collapse; margin-top: 15px;">
                                <tr><th style="text-align:left; border:1px solid #ddd; padding:8px;">字段</th><th style="text-align:left; border:1px solid #ddd; padding:8px;">值</th></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">城市名称</td><td style="border:1px solid #ddd; padding:8px;">${city.name || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">完整名称</td><td style="border:1px solid #ddd; padding:8px;">${city.fullName || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">城市ID</td><td style="border:1px solid #ddd; padding:8px;">${city.id || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">经度</td><td style="border:1px solid #ddd; padding:8px;">${city.lon || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">纬度</td><td style="border:1px solid #ddd; padding:8px;">${city.lat || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">省份/行政区</td><td style="border:1px solid #ddd; padding:8px;">${city.adm1 || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">国家</td><td style="border:1px solid #ddd; padding:8px;">${city.country || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">时区</td><td style="border:1px solid #ddd; padding:8px;">${city.timezone || '-'}</td></tr>
                            </table>
                        `;
                    } else if (jsonData.data && jsonData.data.location && jsonData.data.location.length > 0) {
                        // 测试脚本结果 - 和风天气API原始数据
                        const location = jsonData.data.location[0];
                        resultHtml += `
                            <table style="width:100%; border-collapse: collapse; margin-top: 15px;">
                                <tr><th style="text-align:left; border:1px solid #ddd; padding:8px;">字段</th><th style="text-align:left; border:1px solid #ddd; padding:8px;">值</th></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">城市名称</td><td style="border:1px solid #ddd; padding:8px;">${location.name || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">城市ID</td><td style="border:1px solid #ddd; padding:8px;">${location.id || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">经度</td><td style="border:1px solid #ddd; padding:8px;">${location.lon || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">纬度</td><td style="border:1px solid #ddd; padding:8px;">${location.lat || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">上级行政区</td><td style="border:1px solid #ddd; padding:8px;">${location.adm1 || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">地区/城市</td><td style="border:1px solid #ddd; padding:8px;">${location.adm2 || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">国家</td><td style="border:1px solid #ddd; padding:8px;">${location.country || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">时区</td><td style="border:1px solid #ddd; padding:8px;">${location.tz || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">Rank值</td><td style="border:1px solid #ddd; padding:8px;">${location.rank || '-'}</td></tr>
                            </table>
                        `;
                    }
                } else if (apiType === 'weather') {
                    // 天气API结果
                    if (jsonData.data) {
                        const weather = jsonData.data;
                        resultHtml += `
                            <table style="width:100%; border-collapse: collapse; margin-top: 15px;">
                                <tr><th style="text-align:left; border:1px solid #ddd; padding:8px;">字段</th><th style="text-align:left; border:1px solid #ddd; padding:8px;">值</th></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">城市</td><td style="border:1px solid #ddd; padding:8px;">${weather.city || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">城市ID</td><td style="border:1px solid #ddd; padding:8px;">${weather.cityid || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">温度</td><td style="border:1px solid #ddd; padding:8px;">${weather.temp || '-'}°C</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">体感温度</td><td style="border:1px solid #ddd; padding:8px;">${weather.feelsLike || '-'}°C</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">天气</td><td style="border:1px solid #ddd; padding:8px;">${weather.text || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">风向</td><td style="border:1px solid #ddd; padding:8px;">${weather.windDir || '-'}</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">风力</td><td style="border:1px solid #ddd; padding:8px;">${weather.windScale || '-'}级</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">湿度</td><td style="border:1px solid #ddd; padding:8px;">${weather.humidity || '-'}%</td></tr>
                                <tr><td style="border:1px solid #ddd; padding:8px;">更新时间</td><td style="border:1px solid #ddd; padding:8px;">${weather.updateTime || '-'}</td></tr>
                            </table>
                        `;
                    }
                }
            }
            
            // 添加完整JSON
            resultHtml += `<p style="margin-top: 20px;">完整JSON响应:</p>
                          <pre>${JSON.stringify(jsonData, null, 2)}</pre>`;
            
            // 显示结果
            document.getElementById('result').innerHTML = resultHtml;
            
            // 构建调试信息
            debugHtml = `<div class="debug-info">
                <h3>请求信息</h3>
                <p><strong>URL:</strong> ${rawData.url || '-'}</p>
                <p><strong>HTTP状态:</strong> ${rawData.status} ${rawData.statusText}</p>
                <p><strong>响应长度:</strong> ${rawData.text.length} 字符</p>
            </div>`;
            
            // 添加调试数据
            if (jsonData.debug || jsonData.debug_info) {
                const debugData = jsonData.debug || jsonData.debug_info;
                debugHtml += `<div class="debug-info">
                    <h3>API调试信息</h3>
                    <pre>${JSON.stringify(debugData, null, 2)}</pre>
                </div>`;
            }
            
            document.getElementById('debugInfo').innerHTML = debugHtml;
        }
        
        // 辅助函数：转义HTML
        function escapeHtml(text) {
            return text
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }
        
        // 辅助函数：获取字符串字节值
        function getByteValues(text) {
            let result = [];
            for (let i = 0; i < text.length; i++) {
                let byte = text.charCodeAt(i);
                result.push(`位置 ${i}: ${byte} (0x${byte.toString(16)}) - '${text[i]}'`);
            }
            return result.join('\n');
        }
    </script>
</body>
</html> 