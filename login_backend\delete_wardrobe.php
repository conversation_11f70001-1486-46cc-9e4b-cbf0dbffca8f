<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入配置和辅助函数
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// 验证用户Token
$auth = new Auth();
$token = null;

// 从请求头或查询参数中获取token
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 移除可能存在的Bearer前缀
    $token = str_replace('Bearer ', '', $token);
} elseif (isset($_GET['token'])) {
    $token = $_GET['token'];
}

if (!$token) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'No token provided']);
    exit;
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Invalid or expired token']);
    exit;
}

$user_id = $payload['sub'];

// 获取POST数据
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// 检查必要参数
if (!isset($data['id']) || empty($data['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Wardrobe ID is required']);
    exit;
}

$wardrobe_id = (int)$data['id'];

try {
    // 获取数据库连接
    $db = new Database();
    $pdo = $db->getConnection();
    
    // 检查衣橱是否存在且属于当前用户
    $stmt = $pdo->prepare("SELECT * FROM wardrobes WHERE id = :id AND user_id = :user_id");
    $stmt->bindParam(':id', $wardrobe_id, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    
    $wardrobe = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$wardrobe) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'Wardrobe not found']);
        exit;
    }
    
    // 检查是否为默认衣橱
    if ($wardrobe['is_default'] == 1) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Cannot delete default wardrobe']);
        exit;
    }
    
    // 检查衣橱中是否有衣物
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM clothes WHERE wardrobe_id = :wardrobe_id AND user_id = :user_id");
    $stmt->bindParam(':wardrobe_id', $wardrobe_id, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    
    $clothes_count = (int)$stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($clothes_count > 0) {
        http_response_code(400);
        echo json_encode([
            'success' => false, 
            'error' => 'Wardrobe is not empty', 
            'message' => 'Please remove all clothes from the wardrobe before deleting it',
            'clothes_count' => $clothes_count
        ]);
        exit;
    }
    
    // 执行删除操作
    $stmt = $pdo->prepare("DELETE FROM wardrobes WHERE id = :id AND user_id = :user_id");
    $stmt->bindParam(':id', $wardrobe_id, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    
    // 记录日志
    error_log("User ID: $user_id deleted wardrobe ID: $wardrobe_id");
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => 'Wardrobe deleted successfully'
    ]);
    
} catch (PDOException $e) {
    error_log("Database error in delete_wardrobe.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database error', 'message' => 'An error occurred while deleting wardrobe']);
} catch (Exception $e) {
    error_log("General error in delete_wardrobe.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Server error', 'message' => 'An unexpected error occurred']);
} 