<?php
/**
 * 调试分类数据问题的脚本
 * 用于排查为什么"全部"数据源下没有共享分类
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

// 修复：从payload中正确获取用户ID
$userId = $payload['user_id'] ?? $payload['sub'] ?? null;

if (!$userId) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Token中缺少用户ID信息'
    ]);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $result = [];
    $result['user_id'] = $userId;
    
    // 1. 检查用户所在的圈子
    $stmt = $conn->prepare("
        SELECT cm.circle_id, c.name as circle_name, cm.status, cm.role
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $circles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['user_circles'] = $circles;
    
    // 2. 检查用户的个人分类
    $stmt = $conn->prepare("
        SELECT id, user_id, name, code, is_system, sort_order, created_at, circle_id
        FROM clothing_categories
        WHERE (is_system = 1 AND user_id = :user_id) OR (is_system = 0 AND user_id = :user_id AND circle_id IS NULL)
        ORDER BY sort_order ASC, is_system DESC, created_at ASC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $personalCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['personal_categories'] = $personalCategories;
    
    // 3. 检查圈子中的所有分类（如果用户在圈子中）
    if (!empty($circles)) {
        $circleIds = array_column($circles, 'circle_id');
        $placeholders = str_repeat('?,', count($circleIds) - 1) . '?';
        
        $stmt = $conn->prepare("
            SELECT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at, c.circle_id,
                   u.nickname as creator_nickname
            FROM clothing_categories c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE c.circle_id IN ($placeholders)
            ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC
        ");
        $stmt->execute($circleIds);
        $circleCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result['circle_categories'] = $circleCategories;
        
        // 4. 测试"全部"数据源的SQL查询
        $stmt = $conn->prepare("
            SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                           u.nickname as creator_nickname,
                           CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
            FROM clothing_categories c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE ((c.is_system = 1 AND c.user_id = :user_id) OR (c.is_system = 0 AND c.user_id = :user_id AND c.circle_id IS NULL)) OR
                  (c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'))
            ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $allCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result['all_categories_query'] = $allCategories;
        
        // 5. 分别测试个人和共享分类查询
        // 个人分类查询
        $stmt = $conn->prepare("
            SELECT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at, c.circle_id,
                   'personal' as data_source
            FROM clothing_categories c
            WHERE (c.is_system = 1 AND c.user_id = :user_id) OR (c.is_system = 0 AND c.user_id = :user_id AND c.circle_id IS NULL)
            ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $personalQuery = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result['personal_query'] = $personalQuery;
        
        // 共享分类查询
        $stmt = $conn->prepare("
            SELECT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at, c.circle_id,
                   u.nickname as creator_nickname,
                   'shared' as data_source
            FROM clothing_categories c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
            ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $sharedQuery = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result['shared_query'] = $sharedQuery;
        
    } else {
        $result['circle_categories'] = [];
        $result['all_categories_query'] = $personalCategories; // 如果不在圈子中，全部查询等于个人查询
        $result['shared_query'] = [];
    }
    
    // 6. 检查circle_members表中的数据
    $stmt = $conn->prepare("
        SELECT circle_id, status, role, joined_at
        FROM circle_members
        WHERE user_id = :user_id
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $membershipData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['membership_data'] = $membershipData;
    
    echo json_encode([
        'status' => 'success',
        'data' => $result
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '查询失败: ' . $e->getMessage()
    ]);
}
?>
