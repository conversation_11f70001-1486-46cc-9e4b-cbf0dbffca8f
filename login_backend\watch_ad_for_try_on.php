<?php
/**
 * 通过观看广告获取试衣次数API
 * 
 * 这个API允许用户通过观看激励视频广告获取一次免费试衣机会
 * 每个用户每天观看广告获取次数的次数有上限，默认为3次，通过AD_WATCH_DAILY_LIMIT配置
 * 
 * 请求参数:
 * - completed: 是否完整观看广告 (1=完成, 0=未完成)
 * 
 * 返回:
 * {
 *   "error": false,
 *   "msg": "成功获得1次试衣机会",
 *   "data": {
 *     "free_try_on_count": 1,
 *     "paid_try_on_count": 0
 *   }
 * }
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 只允许POST方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => '不支持的请求方法'
    ]);
    exit;
}

// 检查是否有Token
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => '缺少认证信息'
    ]);
    exit;
}

// 获取Token
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证Token
$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    echo json_encode([
        'error' => true,
        'msg' => '无效的认证信息'
    ]);
    exit;
}

// 获取用户ID
$userId = $payload['sub'];

// 获取请求参数
$data = json_decode(file_get_contents('php://input'), true);
if (json_last_error() !== JSON_ERROR_NONE) {
    $data = $_POST;
}

// 获取广告是否完成观看的标志
$completed = isset($data['completed']) ? (int)$data['completed'] : 0;

// 如果未完成观看广告，返回错误
if (!$completed) {
    echo json_encode([
        'error' => true,
        'msg' => '必须完整观看广告才能获得奖励'
    ]);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查用户当日广告观看次数是否已达上限
    $dailyLimit = defined('AD_WATCH_DAILY_LIMIT') ? AD_WATCH_DAILY_LIMIT : 3; // 默认上限为3次
    $todayStart = date('Y-m-d 00:00:00'); // 今天的开始时间

    // 查询用户今日已观看广告次数
    $checkStmt = $conn->prepare("
        SELECT COUNT(*) as watch_count 
        FROM ad_watch_log 
        WHERE user_id = :user_id 
        AND reward_type = 'free_try_on' 
        AND created_at >= :today_start
    ");
    
    $checkStmt->execute([
        'user_id' => $userId,
        'today_start' => $todayStart
    ]);
    
    $watchCount = (int)$checkStmt->fetchColumn();
    
    // 如果已达到每日上限，返回错误
    if ($watchCount >= $dailyLimit) {
        echo json_encode([
            'error' => true,
            'msg' => "您今日观看广告获取试衣次数的机会已用完（每日限制{$dailyLimit}次），请明天再来或购买试衣次数",
            'daily_limit_reached' => true,
            'daily_limit' => $dailyLimit,
            'watch_count' => $watchCount
        ]);
        exit;
    }
    
    // 获取用户当前的试衣次数
    $userStmt = $conn->prepare("SELECT free_try_on_count, paid_try_on_count FROM users WHERE id = :user_id");
    $userStmt->execute(['user_id' => $userId]);
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode([
            'error' => true,
            'msg' => '用户不存在'
        ]);
        exit;
    }
    
    // 增加免费试衣次数
    $currentFreeCount = (int)$user['free_try_on_count'];
    $newFreeCount = $currentFreeCount + 1;
    
    // 更新用户试衣次数
    $updateStmt = $conn->prepare("UPDATE users SET free_try_on_count = :free_try_on_count WHERE id = :user_id");
    $updateStmt->execute([
        'free_try_on_count' => $newFreeCount,
        'user_id' => $userId
    ]);
    
    // 记录广告观看日志
    $logStmt = $conn->prepare("
        INSERT INTO ad_watch_log (
            user_id,
            reward_type,
            reward_amount,
            created_at
        ) VALUES (
            :user_id,
            'free_try_on',
            1,
            NOW()
        )
    ");
    
    // 如果ad_watch_log表存在就记录日志
    try {
        $logStmt->execute([
            'user_id' => $userId
        ]);
    } catch (PDOException $e) {
        // 表可能不存在，尝试创建
        try {
            // 简单的建表语句，如果表不存在
            $conn->exec("
                CREATE TABLE IF NOT EXISTS ad_watch_log (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    reward_type VARCHAR(50) NOT NULL,
                    reward_amount INT NOT NULL DEFAULT 1,
                    created_at DATETIME NOT NULL,
                    INDEX idx_user_date (user_id, created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");
            // 重新尝试执行
            $logStmt->execute([
                'user_id' => $userId
            ]);
        } catch (PDOException $createError) {
            // 记录错误但不中断流程
            error_log("无法创建或写入广告观看日志: " . $createError->getMessage());
        }
    }
    
    // 返回成功响应，包含今日已使用次数和限制信息
    echo json_encode([
        'error' => false,
        'msg' => '成功获得1次试衣机会',
        'data' => [
            'free_try_on_count' => $newFreeCount,
            'paid_try_on_count' => (int)$user['paid_try_on_count'],
            'daily_ad_watch_count' => $watchCount + 1,
            'daily_ad_watch_limit' => $dailyLimit
        ]
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'error' => true,
        'msg' => '数据库操作失败: ' . $e->getMessage()
    ]);
    exit;
} 