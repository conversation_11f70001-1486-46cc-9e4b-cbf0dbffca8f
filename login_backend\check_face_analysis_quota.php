<?php
/**
 * 检查面容分析次数API
 * 
 * 检查用户是否有可用的面容分析次数
 * 
 * 请求方法：POST
 * 请求参数：无
 * 
 * 返回：
 * {
 *   "error": false,
 *   "data": {
 *     "has_quota": true/false,
 *     "analysis_id": "分析ID"（如果有可用次数）
 *   }
 * }
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => '只支持POST请求'
    ]);
    exit;
}

// 验证用户身份
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供授权Token'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
$auth = new Auth();
$verifyResult = $auth->verifyToken($token);

if ($verifyResult === false) {
    echo json_encode([
        'error' => true,
        'msg' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $verifyResult['sub']; // 从验证结果中获取用户ID

try {
    $db = new Database();
    $conn = $db->getConnection();

    // 先检查usage_status字段是否存在
    $checkColumnStmt = $conn->prepare("SHOW COLUMNS FROM face_analysis LIKE 'usage_status'");
    $checkColumnStmt->execute();
    $columnExists = $checkColumnStmt->rowCount() > 0;

    if (!$columnExists) {
        // 如果usage_status字段不存在，说明数据库还没有更新，直接返回没有次数
        echo json_encode([
            'error' => false,
            'data' => [
                'has_quota' => false
            ]
        ]);
        return;
    }

    // 检查是否有未使用的已支付分析记录
    // 只有同时满足：已支付、未使用、没有分析结果的记录才算可用
    $stmt = $conn->prepare("
        SELECT id
        FROM face_analysis
        WHERE user_id = :user_id
        AND payment_status = 'paid'
        AND usage_status = 'unused'
        AND (analysis_result IS NULL OR analysis_result = '')
        ORDER BY created_at DESC
        LIMIT 1
    ");

    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();

    $availableAnalysis = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($availableAnalysis) {
        // 有可用的分析次数
        echo json_encode([
            'error' => false,
            'data' => [
                'has_quota' => true,
                'analysis_id' => $availableAnalysis['id']
            ]
        ]);
    } else {
        // 没有可用的分析次数
        echo json_encode([
            'error' => false,
            'data' => [
                'has_quota' => false
            ]
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '检查分析次数失败: ' . $e->getMessage()
    ]);
}
?>
