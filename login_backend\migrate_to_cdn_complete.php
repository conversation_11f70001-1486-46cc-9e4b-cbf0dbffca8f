<?php
/**
 * 完整的图片URL迁移脚本
 * 将OSS域名替换为CDN域名
 * 
 * 使用方法：
 * php migrate_to_cdn_complete.php [用户ID] [--all] [--dry-run]
 * 
 * 参数:
 * 用户ID - 要处理的用户ID（默认为3）
 * --all - 处理所有用户的数据
 * --dry-run - 不实际更新数据库，只显示将更改什么
 */

// 设置执行时间，防止超时
set_time_limit(0);
ini_set('memory_limit', '512M');

// 引入配置和数据库连接
require_once 'config.php';
require_once 'db.php';

// 检查是否存在CDN配置
if (!defined('ALIYUN_CDN_DOMAIN')) {
    die("错误: 请先在config.php中添加ALIYUN_CDN_DOMAIN配置项。\n");
}

// 解析命令行参数
$userId = 3; // 默认用户ID
$dryRun = false; // 默认不是模拟运行
$debug = false; // 调试模式
$processAllUsers = false; // 是否处理所有用户

if ($argc > 1) {
    foreach (array_slice($argv, 1) as $arg) {
        if ($arg === '--dry-run') {
            $dryRun = true;
        } elseif ($arg === '--debug') {
            $debug = true;
        } elseif ($arg === '--all') {
            $processAllUsers = true;
        } elseif (is_numeric($arg)) {
            $userId = (int)$arg;
        }
    }
}

// 记录日志
function logMessage($message, $isDebugMessage = false) {
    global $debug;
    $timestamp = date('Y-m-d H:i:s');
    $logMsg = "[$timestamp] $message";
    echo $logMsg . "\n";
    error_log($logMsg);
    
    // 如果是调试消息且未开启调试模式，则不写入日志文件
    if ($isDebugMessage && !$debug) {
        return;
    }
    
    // 写入日志文件
    file_put_contents('cdn_migration.log', $logMsg . "\n", FILE_APPEND);
}

// 替换URL域名
function replaceDomain($url) {
    $oldDomain = 'https://' . ALIYUN_OSS_BUCKET_DOMAIN;
    $newDomain = 'https://' . ALIYUN_CDN_DOMAIN;
    
    return str_replace($oldDomain, $newDomain, $url);
}

// 检查表是否存在
function tableExists($conn, $tableName) {
    try {
        $result = $conn->query("SHOW TABLES LIKE '$tableName'");
        return $result->rowCount() > 0;
    } catch (Exception $e) {
        logMessage("检查表是否存在时出错: " . $e->getMessage(), true);
        return false;
    }
}

// 检查列是否存在
function columnExists($conn, $tableName, $columnName) {
    try {
        $result = $conn->query("SHOW COLUMNS FROM `$tableName` LIKE '$columnName'");
        return $result->rowCount() > 0;
    } catch (Exception $e) {
        logMessage("检查列是否存在时出错: " . $e->getMessage(), true);
        return false;
    }
}

// 更新表中的URL字段
function updateUrlColumn($conn, $tableName, $columnName, $userId, $dryRun = false, $processAllUsers = false) {
    global $stats;
    
    if (!tableExists($conn, $tableName)) {
        logMessage("表 '$tableName' 不存在，跳过处理");
        return 0;
    }
    
    if (!columnExists($conn, $tableName, $columnName)) {
        logMessage("表 '$tableName' 中不存在列 '$columnName'，跳过处理");
        return 0;
    }
    
    // 初始化表的统计信息（如果不存在）
    if (!isset($stats[$tableName])) {
        $stats[$tableName] = ['total' => 0, 'updated' => 0];
    }
    
    try {
        // 获取需要更新的记录数
        $sql = "SELECT COUNT(*) as count FROM `$tableName` WHERE `$columnName` LIKE :pattern";
        $params = [];
        $pattern = '%' . ALIYUN_OSS_BUCKET_DOMAIN . '%';
        $params[':pattern'] = $pattern;
        
        // 如果不是处理所有用户，添加用户ID筛选条件
        if (!$processAllUsers && columnExists($conn, $tableName, 'user_id')) {
            $sql .= " AND user_id = :user_id";
            $params[':user_id'] = $userId;
        }
        
        $countStmt = $conn->prepare($sql);
        $countStmt->execute($params);
        $totalToUpdate = $countStmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        $stats[$tableName]['total'] = $totalToUpdate;
        
        if ($totalToUpdate == 0) {
            logMessage("表 '$tableName' 中列 '$columnName' 没有需要更新的记录");
            return 0;
        }
        
        logMessage("表 '$tableName' 中列 '$columnName' 有 $totalToUpdate 条记录需要更新");
        
        // 获取需要更新的记录
        $sql = "SELECT id, `$columnName` FROM `$tableName` WHERE `$columnName` LIKE :pattern";
        
        // 如果不是处理所有用户，添加用户ID筛选条件
        if (!$processAllUsers && columnExists($conn, $tableName, 'user_id')) {
            $sql .= " AND user_id = :user_id";
        }
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        $updated = 0;
        
        while ($item = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $oldUrl = $item[$columnName];
            $newUrl = replaceDomain($oldUrl);
            
            if ($newUrl !== $oldUrl) {
                logMessage("$tableName ID={$item['id']}: $oldUrl => $newUrl");
                $updated++;
                
                if (!$dryRun) {
                    $updateStmt = $conn->prepare("
                        UPDATE `$tableName` 
                        SET `$columnName` = :new_url 
                        WHERE id = :id
                    ");
                    $updateStmt->bindParam(':new_url', $newUrl);
                    $updateStmt->bindParam(':id', $item['id']);
                    $updateStmt->execute();
                    
                    // 验证更新
                    $verifyStmt = $conn->prepare("
                        SELECT `$columnName` 
                        FROM `$tableName` 
                        WHERE id = :id
                    ");
                    $verifyStmt->bindParam(':id', $item['id']);
                    $verifyStmt->execute();
                    $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($verifyResult[$columnName] !== $newUrl) {
                        logMessage("警告: 记录 $tableName ID={$item['id']} 验证失败，URL未更新");
                    }
                }
            }
        }
        
        $stats[$tableName]['updated'] = $updated;
        return $updated;
        
    } catch (Exception $e) {
        logMessage("更新表 '$tableName' 列 '$columnName' 时出错: " . $e->getMessage());
        return 0;
    }
}

// 更新JSON字段中的URL
function updateJsonUrlField($conn, $tableName, $columnName, $userId, $jsonPath, $dryRun = false, $processAllUsers = false) {
    global $stats;
    
    if (!tableExists($conn, $tableName)) {
        logMessage("表 '$tableName' 不存在，跳过处理");
        return 0;
    }
    
    if (!columnExists($conn, $tableName, $columnName)) {
        logMessage("表 '$tableName' 中不存在列 '$columnName'，跳过处理");
        return 0;
    }
    
    // 初始化表的统计信息（如果不存在）
    $statKey = $tableName . '_json';
    if (!isset($stats[$statKey])) {
        $stats[$statKey] = ['total' => 0, 'updated' => 0];
    }
    
    try {
        // 获取含有OSS URL的JSON数据
        $sql = "SELECT id, `$columnName` FROM `$tableName` WHERE `$columnName` LIKE :pattern";
        $params = [];
        $pattern = '%' . ALIYUN_OSS_BUCKET_DOMAIN . '%';
        $params[':pattern'] = $pattern;
        
        // 如果不是处理所有用户，添加用户ID筛选条件
        if (!$processAllUsers && columnExists($conn, $tableName, 'user_id')) {
            $sql .= " AND user_id = :user_id";
            $params[':user_id'] = $userId;
        }
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        $totalProcessed = 0;
        $totalUpdated = 0;
        
        while ($item = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $totalProcessed++;
            $jsonData = json_decode($item[$columnName], true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                logMessage("警告: $tableName ID={$item['id']} 的JSON数据解析失败");
                continue;
            }
            
            $updated = false;
            
            // 根据JSON路径处理不同结构
            // outfit_data.items[].clothing_data.image_url 格式
            if ($jsonPath === 'items.clothing_data.image_url') {
                if (isset($jsonData['items']) && is_array($jsonData['items'])) {
                    foreach ($jsonData['items'] as &$outfitItem) {
                        if (isset($outfitItem['clothing_data']) && isset($outfitItem['clothing_data']['image_url'])) {
                            $oldUrl = $outfitItem['clothing_data']['image_url'];
                            $newUrl = replaceDomain($oldUrl);
                            
                            if ($newUrl !== $oldUrl) {
                                $outfitItem['clothing_data']['image_url'] = $newUrl;
                                $updated = true;
                                logMessage("$tableName ID={$item['id']} JSON中的URL: $oldUrl => $newUrl");
                            }
                        }
                    }
                }
            }
            // 其他JSON结构可以在这里添加更多的处理逻辑
            
            if ($updated) {
                $totalUpdated++;
                
                if (!$dryRun) {
                    $newJsonData = json_encode($jsonData);
                    $updateStmt = $conn->prepare("
                        UPDATE `$tableName` 
                        SET `$columnName` = :json_data 
                        WHERE id = :id
                    ");
                    $updateStmt->bindParam(':json_data', $newJsonData);
                    $updateStmt->bindParam(':id', $item['id']);
                    $updateStmt->execute();
                }
            }
        }
        
        $stats[$statKey]['total'] = $totalProcessed;
        $stats[$statKey]['updated'] = $totalUpdated;
        return $totalUpdated;
        
    } catch (Exception $e) {
        logMessage("更新表 '$tableName' 中的JSON数据时出错: " . $e->getMessage());
        return 0;
    }
}

// 初始化统计信息
$stats = [];

// 开始脚本
$userStr = $processAllUsers ? "所有用户" : "用户ID=$userId";
logMessage("开始为{$userStr}迁移图片URL从OSS到CDN" . ($dryRun ? "（模拟模式）" : ""));
logMessage("旧域名: https://" . ALIYUN_OSS_BUCKET_DOMAIN);
logMessage("新域名: https://" . ALIYUN_CDN_DOMAIN);

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();
    
    // 验证用户是否存在（如果不是处理所有用户）
    if (!$processAllUsers) {
        $stmt = $conn->prepare("SELECT id, nickname FROM users WHERE id = :user_id");
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            die("错误: 找不到ID为 $userId 的用户。\n");
        }
        
        logMessage("用户信息: ID={$user['id']}, 昵称={$user['nickname']}");
    } else {
        // 处理所有用户时，显示用户总数
        $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
        $userCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        logMessage("准备处理所有用户，数据库中共有 $userCount 个用户");
    }
    
    // 开始事务（如果不是模拟运行）
    if (!$dryRun) {
        $conn->beginTransaction();
    }
    
    // 处理已知表中的URL字段
    // 1. clothes表的image_url字段
    updateUrlColumn($conn, 'clothes', 'image_url', $userId, $dryRun, $processAllUsers);
    
    // 2. photos表的image_url字段
    updateUrlColumn($conn, 'photos', 'image_url', $userId, $dryRun, $processAllUsers);
    
    // 3. try_on_history表的result_image_url字段
    updateUrlColumn($conn, 'try_on_history', 'result_image_url', $userId, $dryRun, $processAllUsers);
    
    // 4. media_check_results表的media_url字段
    if (tableExists($conn, 'media_check_results')) {
        updateUrlColumn($conn, 'media_check_results', 'media_url', $userId, $dryRun, $processAllUsers);
    }
    
    // 5. users表的avatar_url字段（如果来自OSS）
    if (tableExists($conn, 'users')) {
        // 构建SQL查询来检查avatar_url是否包含OSS URL
        $sql = "SELECT COUNT(*) as count FROM users WHERE avatar_url LIKE :pattern";
        $params = [':pattern' => '%' . ALIYUN_OSS_BUCKET_DOMAIN . '%'];
        
        // 如果不是处理所有用户，添加用户ID筛选条件
        if (!$processAllUsers) {
            $sql .= " AND id = :user_id";
            $params[':user_id'] = $userId;
        }
        
        $checkStmt = $conn->prepare($sql);
        $checkStmt->execute($params);
        $hasOssUrl = $checkStmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;
        
        if ($hasOssUrl) {
            updateUrlColumn($conn, 'users', 'avatar_url', $userId, $dryRun, $processAllUsers);
        } else {
            $userStr = $processAllUsers ? "所有用户的" : "用户的";
            logMessage("{$userStr}头像URL不是OSS格式，跳过处理");
        }
    }
    
    // 6. wardrobes表的image_url字段（如果有且来自OSS）
    if (tableExists($conn, 'wardrobes') && columnExists($conn, 'wardrobes', 'image_url')) {
        updateUrlColumn($conn, 'wardrobes', 'image_url', $userId, $dryRun, $processAllUsers);
    }
    
    // 7. outfits表 - 如果存在
    if (tableExists($conn, 'outfits')) {
        // 处理缩略图URL
        updateUrlColumn($conn, 'outfits', 'thumbnail_url', $userId, $dryRun, $processAllUsers);
        
        // 处理JSON数据中的URL
        updateJsonUrlField($conn, 'outfits', 'outfit_data', $userId, 'items.clothing_data.image_url', $dryRun, $processAllUsers);
    }
    
    // 提交事务（如果不是模拟运行）
    if (!$dryRun) {
        $conn->commit();
        logMessage("事务已提交，所有更改已保存到数据库");
    } else {
        logMessage("模拟运行模式，未对数据库进行实际更改");
    }
    
    // 输出统计信息
    logMessage("\n迁移统计信息:");
    foreach ($stats as $table => $info) {
        logMessage("$table: 共{$info['total']}条记录，更新了{$info['updated']}条");
    }
    
    $totalUpdated = array_sum(array_column($stats, 'updated'));
    logMessage("总计更新URL: $totalUpdated 个");
    logMessage("迁移完成" . ($dryRun ? "（模拟模式，未实际更新数据库）" : ""));
    
} catch (Exception $e) {
    // 如果有事务且发生错误，回滚
    if (!$dryRun && isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
        logMessage("发生错误，事务已回滚");
    }
    
    logMessage("错误: " . $e->getMessage());
    logMessage("在 " . $e->getFile() . " 第 " . $e->getLine() . " 行");
} 