<?php
/**
 * AI试衣API
 * 
 * 调用阿里云OutfitAnyone API实现虚拟试衣效果
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * POST 参数:
 * - photo_id: 用户照片ID
 * - clothes_ids: 衣物ID数组
 * 
 * 返回:
 * {
 *   "error": false,
 *   "data": {
 *     "result_image_url": "https://example.com/result.jpg",
 *     "task_id": "xxxxxxx",
 *     "status": "success",
 *     "clothes": [...]
 *   }
 * }
 */

// 开启输出缓冲，防止意外输出
ob_start();

require_once 'config.php';
require_once 'auth.php'; // 替换verify_token.php为auth.php
require_once 'db.php';
require_once '../vendor/autoload.php'; // 引入阿里云OSS SDK
require_once 'oss_helper.php';

// 清除之前的输出
ob_end_clean();

/**
 * 自定义日志函数 - 将日志保存到try_on.log文件
 * 
 * @param string $message 日志消息
 * @return void
 */
function try_on_log($message) {
    $logFile = __DIR__ . '/try_on.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message" . PHP_EOL;
    
    // 追加到日志文件
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否存在Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// 验证token，直接使用Auth类，不通过verify_token.php
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid or expired token'
    ]);
    exit;
}

// 获取用户ID
$userId = $tokenData['sub'];

// 获取POST请求体
$requestBody = file_get_contents('php://input');
$data = json_decode($requestBody, true);

// 记录请求信息
try_on_log("试衣API请求参数: " . json_encode($data));

// 验证请求参数
if (!isset($data['photo_id']) || !isset($data['clothes_ids']) || empty($data['clothes_ids'])) {
    echo json_encode([
        'error' => true,
        'msg' => '请求参数不完整，需要photo_id和clothes_ids'
    ]);
    exit;
}

// 检查是否有AI分类的衣物类型
$aiClothingType = isset($data['clothing_type']) ? $data['clothing_type'] : null;
if ($aiClothingType) {
    try_on_log("收到AI分类的衣物类型: " . $aiClothingType);
}

// 检查是否是商家模式（访问商家衣物）
$merchantId = isset($data['merchant_id']) ? intval($data['merchant_id']) : null;
$isMerchantMode = $merchantId > 0;

try_on_log("试衣模式: " . ($isMerchantMode ? "商家模式，商家ID=$merchantId" : "普通模式"));

// 检查用户当天的试衣次数限制
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 记录请求信息以便追踪
    $originalRequest = json_encode($data);
    try_on_log("原始请求数据: " . $originalRequest);
    
    // 获取用户的次数信息
    $userTryOnCounts = getUserTryOnCounts($conn, $userId);
    $freeCount = $userTryOnCounts['free_count'];
    $paidCount = $userTryOnCounts['paid_count'];
    
    try_on_log("用户 {$userId} 当前试衣次数状态 - 免费次数: $freeCount, 付费次数: $paidCount");
    
    // 检查用户是否有足够的次数
    if ($freeCount <= 0 && $paidCount <= 0) {
        try_on_log("用户 {$userId} 次数不足，免费次数: $freeCount, 付费次数: $paidCount");
        
        // 检查用户当日广告观看次数是否已达上限
        $dailyLimit = defined('AD_WATCH_DAILY_LIMIT') ? AD_WATCH_DAILY_LIMIT : 3; // 默认上限为3次
        $todayStart = date('Y-m-d 00:00:00'); // 今天的开始时间
        $adWatchCount = 0;
        $adLimitReached = false;
        
        // 查询用户今日已观看广告次数
        try {
            $checkStmt = $conn->prepare("
                SELECT COUNT(*) as watch_count 
                FROM ad_watch_log 
                WHERE user_id = :user_id 
                AND reward_type = 'free_try_on' 
                AND created_at >= :today_start
            ");
            
            $checkStmt->execute([
                'user_id' => $userId,
                'today_start' => $todayStart
            ]);
            
            $adWatchCount = (int)$checkStmt->fetchColumn();
            $adLimitReached = ($adWatchCount >= $dailyLimit);
            try_on_log("用户 {$userId} 今日广告观看次数: {$adWatchCount}, 上限: {$dailyLimit}, 是否达到上限: " . ($adLimitReached ? "是" : "否"));
        } catch (PDOException $e) {
            // 如果表不存在或查询出错，假设用户未达到观看上限
            try_on_log("查询广告观看记录出错: " . $e->getMessage() . "，假设用户未达到观看上限");
            $adWatchCount = 0;
            $adLimitReached = false;
        }
        
        echo json_encode([
            'error' => true,
            'limit_exceeded' => true,
            'show_purchase_button' => true,
            'show_ad_button' => !$adLimitReached, // 只有在未达到广告观看上限时才显示广告按钮
            'ad_watch_limit' => $dailyLimit,
            'ad_watch_count' => $adWatchCount,
            'ad_limit_reached' => $adLimitReached,
            'msg' => $adLimitReached 
                ? "您的试衣次数已用完，今日观看广告获取次数的机会也已用完（每日限制{$dailyLimit}次），请明天再来或购买试衣次数"
                : "您的试衣次数已用完，如需更多试衣次数请购买试衣点数或者观看广告获取免费次数"
        ]);
        exit;
    }
    
    // 记录用户ID、照片ID和衣物ID，等待API调用成功后再扣除次数
    $pendingDeduction = [
        'user_id' => $userId,
        'free_count' => $userTryOnCounts['free_count'],
        'paid_count' => $userTryOnCounts['paid_count'],
        'merchant_id' => $merchantId,
        'use_paid' => ($freeCount <= 0) // 标记是否使用付费次数
    ];
    
    // 检查是否有强制使用用户积分的标记
    if (isset($data['force_use_user_credits']) && $data['force_use_user_credits']) {
        $pendingDeduction['force_use_user_credits'] = true;
        try_on_log("已设置强制使用用户积分标记");
    }
    
    // 将待扣除信息存入会话，这样在API调用失败时不会实际扣除
    try_on_log("暂存试衣次数扣除信息，等待API调用成功后执行");
    
} catch (PDOException $e) {
    try_on_log("检查试衣次数限制时出错: " . $e->getMessage());
    // 出错时不阻止试衣，只记录日志，继续执行
}

$photoId = $data['photo_id'];
$clothesIds = $data['clothes_ids'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 获取照片信息
    $photoStmt = $conn->prepare("SELECT * FROM photos WHERE id = :photo_id AND user_id = :user_id");
    $photoStmt->execute([
        'photo_id' => $photoId,
        'user_id' => $userId
    ]);
    $photo = $photoStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$photo) {
        try_on_log("试衣API错误: 找不到指定照片，photo_id=$photoId, user_id=$userId");
        echo json_encode([
            'error' => true,
            'msg' => '找不到指定照片'
        ]);
        exit;
    }
    
    // 记录照片信息
    try_on_log("试衣照片信息: " . json_encode($photo));
    
    // 获取衣物信息 - 区分普通模式和商家模式
    $placeholders = implode(',', array_fill(0, count($clothesIds), '?'));
    
    if ($isMerchantMode) {
        // 商家模式：获取指定商家的衣物
        try_on_log("商家模式查询衣物: 商家ID=$merchantId, 衣物IDs=" . implode(',', $clothesIds));
    $clothesStmt = $conn->prepare("SELECT * FROM clothes WHERE id IN ($placeholders) AND user_id = ?");
        $params = array_merge($clothesIds, [$merchantId]); // 使用商家ID作为用户ID
    } else {
        // 普通模式：获取用户自己的衣物
        try_on_log("普通模式查询衣物: 用户ID=$userId, 衣物IDs=" . implode(',', $clothesIds));
        $clothesStmt = $conn->prepare("SELECT * FROM clothes WHERE id IN ($placeholders) AND user_id = ?");
        $params = array_merge($clothesIds, [$userId]); // 使用当前用户ID
    }
    
    // 绑定参数
    $clothesStmt->execute($params);
    $clothes = $clothesStmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($clothes) === 0) {
        try_on_log("试衣API错误: 找不到指定衣物，clothes_ids=" . implode(',', $clothesIds) . 
                  "，用户ID=" . ($isMerchantMode ? $merchantId : $userId));
        echo json_encode([
            'error' => true,
            'msg' => '找不到指定衣物'
        ]);
        exit;
    }
    
    // 记录衣物信息
    try_on_log("试衣衣物信息: " . json_encode($clothes));
    
    // 分类衣物（上装和下装）
    $topGarment = null;
    $bottomGarment = null;
    $dressGarment = null;
    
    // 如果有AI识别的衣物类型，优先使用
    $apiGarmentType = 'upper_body'; // 默认值
    if ($aiClothingType) {
        $apiGarmentType = getApiTypeFromClothingType($aiClothingType);
        try_on_log("使用AI识别的衣物类型: $aiClothingType -> $apiGarmentType");
    }
    
    // 衣物类别到石衣API类型的映射
    $categoryToApiType = [
        // 上装类型
        'tops' => 'upper_body',
        'coats' => 'upper_body',
        'jackets' => 'upper_body',
        'sweaters' => 'upper_body',
        'shirts' => 'upper_body',
        't-shirts' => 'upper_body',
        'blouses' => 'upper_body',
        'hoodies' => 'upper_body',
        // 下装类型
        'pants' => 'lower_body',
        'skirts' => 'lower_body',
        'jeans' => 'lower_body',
        'shorts' => 'lower_body',
        'leggings' => 'lower_body',
        'trousers' => 'lower_body',
        // 连衣裙类型
        'dresses' => 'dresses',
        'dress' => 'dresses',
        'gowns' => 'dresses',
        'jumpsuits' => 'dresses',
        'rompers' => 'dresses',
        'suits' => 'dresses'
    ];
    
    foreach ($clothes as $item) {
        $category = strtolower($item['category']);
        $garmentApiType = $categoryToApiType[$category] ?? null;
        
        try_on_log("衣物ID={$item['id']}, 类别={$category}, 映射API类型={$garmentApiType}");
        
        if ($garmentApiType === 'upper_body') {
            $topGarment = $item;
            try_on_log("选择上衣: id=" . $item['id'] . ", category=" . $item['category']);
        } else if ($garmentApiType === 'lower_body') {
            $bottomGarment = $item;
            try_on_log("选择下装: id=" . $item['id'] . ", category=" . $item['category']);
        } else if ($garmentApiType === 'dresses') {
            $dressGarment = $item;
            try_on_log("选择连衣裙: id=" . $item['id'] . ", category=" . $item['category']);
        } else {
            // 未知类型，尝试自动推断
            if (preg_match('/(dress|suit|gown|jumpsuit|romper)/i', $item['name'])) {
                $dressGarment = $item;
                try_on_log("根据名称自动判定为连衣裙: id=" . $item['id'] . ", name=" . $item['name']);
            } else if (preg_match('/(jacket|coat|top|shirt|blouse|hoodie|sweater)/i', $item['name'])) {
                $topGarment = $item;
                try_on_log("根据名称自动判定为上衣: id=" . $item['id'] . ", name=" . $item['name']);
            } else if (preg_match('/(pant|trousers|jean|skirt|short|legging)/i', $item['name'])) {
                $bottomGarment = $item;
                try_on_log("根据名称自动判定为下装: id=" . $item['id'] . ", name=" . $item['name']);
            } else {
                // 默认作为上衣处理
                $topGarment = $item;
                try_on_log("无法确定类型，默认作为上衣: id=" . $item['id']);
            }
        }
    }
    
    // 预先下载所有需要的图片到本地临时目录
    // 优先级：连衣裙 > 上衣 + 下装 > 上衣 > 下装
    if ($dressGarment) {
        $preDownloadResult = preDownloadImages($photo['image_url'], $dressGarment['image_url'], null);
        try_on_log("使用连衣裙试穿模式");
    } else {
    $preDownloadResult = preDownloadImages($photo['image_url'], $topGarment['image_url'] ?? null, $bottomGarment['image_url'] ?? null);
        try_on_log("使用普通试穿模式: " . ($topGarment ? "有上衣" : "无上衣") . ", " . ($bottomGarment ? "有下装" : "无下装"));
    }
    
    if ($preDownloadResult['error']) {
        echo json_encode([
            'error' => true,
            'msg' => '预下载图片失败: ' . $preDownloadResult['msg']
        ]);
        exit;
    }
    
    // 获取用户信息用于试衣结果展示
    $userStmt = $conn->prepare("SELECT * FROM users WHERE id = :user_id");
    $userStmt->execute(['user_id' => $userId]);
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        try_on_log("警告: 找不到用户信息，user_id=$userId");
        $user = [
            'id' => $userId,
            'nickname' => '',
            'avatar_url' => '',
            'gender' => '0'
        ];
    }
    
    // 根据配置选择使用哪个试衣API
    $apiType = defined('TRY_ON_API_TYPE') ? TRY_ON_API_TYPE : 'aliyun';
    try_on_log("使用试衣API类型: $apiType");
    
    if ($apiType === 'shiyi') {
        // 确定石衣API的衣物类型
        $shiyiGarmentType = 'upper_body'; // 默认为上装
        
        // 优先级：连衣裙 > 上衣 > 下装
        if ($dressGarment) {
            $shiyiGarmentType = 'dresses';
            try_on_log("石衣API使用连衣裙模式");
            // 调用石衣试衣API - 连衣裙模式
            $result = callShiyiTryOnAPI(
                $preDownloadResult['person_image'], 
                $preDownloadResult['top_image'], // 传入连衣裙图片
                null, 
                $shiyiGarmentType
            );
        } else {
            if ($topGarment) {
                $shiyiGarmentType = 'upper_body';
                try_on_log("石衣API使用上衣模式");
            } else if ($bottomGarment) {
                $shiyiGarmentType = 'lower_body';
                try_on_log("石衣API使用下装模式");
            }
            // 调用石衣试衣API - 普通模式
            $result = callShiyiTryOnAPI(
                $preDownloadResult['person_image'], 
                $preDownloadResult['top_image'], 
                $preDownloadResult['bottom_image'], 
                $shiyiGarmentType
            );
        }
    } else {
        // 默认调用阿里云试衣API
    $result = callAliyunTryOnAPI($preDownloadResult['person_image'], $preDownloadResult['top_image'], $preDownloadResult['bottom_image']);
    }
    
    // 检查API调用结果
    if (isset($result['error']) && $result['error'] === true) {
        // API调用失败，获取用户当日试衣失败次数
        $failedCount = getUserDailyFailedCount($conn, $userId);
        try_on_log("用户 {$userId} 当日试衣失败次数: {$failedCount}");
        
        // 记录失败但不扣除次数，使用saveHistoryRecord函数
        $errorMsg = "API调用失败: " . ($result['msg'] ?? '未知错误');
        $saveResult = saveHistoryRecord($conn, $userId, '', $data['clothes_ids'], $data['photo_id'], 
                                       $result['task_id'] ?? '', 'failed', $errorMsg);
        
        if (!$saveResult) {
            try_on_log("警告: 记录试衣失败历史失败");
        }
        
        // 如果用户当日已有失败记录，第二次失败时扣除免费次数并修改返回信息
        if ($failedCount >= 1) {
            try_on_log("用户当日已有失败记录，本次失败将扣除免费次数");
            
            // 扣除免费次数
            $deductionResult = deductTryOnCount($conn, $pendingDeduction);
            
            try_on_log("免费次数扣除" . ($deductionResult['success'] ? "成功" : "失败"));
            
            // 修改返回信息，添加特定错误提示
            $result['limit_exceeded'] = true;
            $result['show_purchase_button'] = true;
            $result['show_ad_button'] = true;
            $result['msg'] = '您提交的照片可能不符合试衣要求，请确保模特照片为正面全身照，衣物为平铺拍摄。详情请查看"试衣须知"。';
        } else {
            try_on_log("用户第一次试衣失败，不扣除次数");
        }
        
        echo json_encode($result);
        exit;
    }
    
    // API调用成功，现在扣除次数
    if (isset($pendingDeduction)) {
        $deductionResult = deductTryOnCount($conn, $pendingDeduction);
        try_on_log("API调用成功，次数扣除" . ($deductionResult['success'] ? "成功" : "失败"));
        
        // 记录使用的试衣次数类型
        $usageType = isset($deductionResult['usage_type']) ? $deductionResult['usage_type'] : "未知次数类型";
        
        // 添加次数扣除结果信息到返回值中，以便前端显示
        if ($deductionResult['success']) {
            if (isset($deductionResult['merchant_deducted'])) {
                $result['merchant_deducted'] = $deductionResult['merchant_deducted'];
                $result['merchant_shared'] = true;
            } else if (isset($deductionResult['merchant_failed']) && $deductionResult['merchant_failed']) {
                $result['merchant_failed'] = true;
                $result['merchant_message'] = '商家试穿次数已用完，已扣除您自己的试穿次数';
            }
        }
        
        // 即使扣除失败也继续，因为API调用已经成功
    } else {
        $usageType = "系统试衣";  // 默认值，表示系统内部操作
    }
    
    // 获取任务ID
    $taskId = isset($result['task_id']) ? $result['task_id'] : '';
    
    // 如果是异步任务模式且有任务ID，立即返回任务ID而不等待任务完成
    if (!empty($taskId) && !isset($result['image_url'])) {
        // 先将任务记录到数据库，标记为处理中状态
        $saveResult = saveHistoryRecord($conn, $userId, '', $clothesIds, $photoId, $taskId, 'processing', $usageType);
        
        if (!$saveResult) {
            try_on_log("警告: 试穿历史记录保存失败");
        }
        
        // 立即返回任务ID，前端将使用此ID查询进度
        echo json_encode([
            'error' => false,
            'data' => [
                'task_id' => $taskId,
                'status' => 'processing',
                'message' => '任务已提交，正在处理中',
                'clothes' => $clothes
            ]
        ]);
        exit;
    }
    
    // 如果已经有结果，继续原来的流程
    // 保存试穿历史记录
    $saveResult = saveHistoryRecord($conn, $userId, $result['image_url'], $clothesIds, $photoId, $taskId, 'success', $usageType);
    
    if (!$saveResult) {
        try_on_log("警告: 试穿历史记录保存失败，但继续返回试穿结果");
    }
    
    // 返回成功响应（合并用户信息和试衣结果到单个响应中）
    echo json_encode([
        'error' => false,
        'data' => [
            'id' => $user['id'],
            'nickname' => $user['nickname'],
            'avatar_url' => $user['avatar_url'],
            'gender' => $user['gender'],
            'created_at' => $user['created_at'] ?? '',
            'result_image_url' => $result['image_url'],
            'task_id' => $result['task_id'],
            'status' => 'success',
            'clothes' => $clothes
        ]
    ]);
    
} catch (PDOException $e) {
    try_on_log("试衣API错误: " . $e->getMessage());
    
    echo json_encode([
        'error' => true,
        'msg' => '数据库操作失败: ' . $e->getMessage()
    ]);
}

/**
 * 预先下载所有需要的图片到本地临时目录
 * 
 * @param string $personImageUrl 人物图片URL
 * @param string|null $topImageUrl 上衣图片URL
 * @param string|null $bottomImageUrl 下装图片URL
 * @return array 返回本地图片路径或错误信息
 */
function preDownloadImages($personImageUrl, $topImageUrl = null, $bottomImageUrl = null) {
    try_on_log("开始预下载所有图片...");
    
    // 创建临时目录
    $tmpDir = __DIR__ . '/uploads/tmp_try_on/';
    if (!file_exists($tmpDir)) {
        if (!mkdir($tmpDir, 0755, true)) {
            try_on_log("错误: 无法创建临时目录");
            return ['error' => true, 'msg' => '无法创建临时目录'];
        }
    }
    
    // 定义返回结果
    $result = [
        'error' => false,
        'person_image' => null,
        'top_image' => null,
        'bottom_image' => null
    ];
    
    // 下载人物图片
    try_on_log("下载人物图片: " . $personImageUrl);
    $personLocalPath = $tmpDir . 'person_' . time() . '_' . substr(md5(rand()), 0, 8) . '.jpg';
    
    if (!downloadImageToPath($personImageUrl, $personLocalPath)) {
        try_on_log("错误: 无法下载人物图片");
        return ['error' => true, 'msg' => '无法下载人物图片，请确保图片URL可访问'];
    }
    $result['person_image'] = createAccessibleUrl($personLocalPath);
    
    // 下载上衣图片
    if ($topImageUrl) {
        try_on_log("下载上衣图片: " . $topImageUrl);
        $topLocalPath = $tmpDir . 'top_' . time() . '_' . substr(md5(rand()), 0, 8) . '.jpg';
        
        if (!downloadImageToPath($topImageUrl, $topLocalPath)) {
            try_on_log("错误: 无法下载上衣图片");
            return ['error' => true, 'msg' => '无法下载上衣图片，请确保图片URL可访问'];
        }
        $result['top_image'] = createAccessibleUrl($topLocalPath);
    }
    
    // 下载下装图片
    if ($bottomImageUrl) {
        try_on_log("下载下装图片: " . $bottomImageUrl);
        $bottomLocalPath = $tmpDir . 'bottom_' . time() . '_' . substr(md5(rand()), 0, 8) . '.jpg';
        
        if (!downloadImageToPath($bottomImageUrl, $bottomLocalPath)) {
            try_on_log("错误: 无法下载下装图片");
            return ['error' => true, 'msg' => '无法下载下装图片，请确保图片URL可访问'];
        }
        $result['bottom_image'] = createAccessibleUrl($bottomLocalPath);
    }
    
    try_on_log("所有图片预下载完成");
    return $result;
}

/**
 * 下载图片到指定路径
 * 
 * @param string $imageUrl 图片URL
 * @param string $localPath 本地保存路径
 * @return bool 是否成功
 */
function downloadImageToPath($imageUrl, $localPath) {
    // 设置cURL选项
    $ch = curl_init($imageUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    
    $imageContent = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($imageContent === false || $httpCode !== 200) {
        try_on_log("错误: 无法下载图片: " . $imageUrl . ", 状态码: " . $httpCode . ", 错误: " . $error);
        return false;
    }
    
    // 保存到本地
    if (file_put_contents($localPath, $imageContent) === false) {
        try_on_log("错误: 无法保存图片到本地: " . $localPath);
        return false;
    }
    
    return true;
}

/**
 * 创建可访问的URL
 * 
 * @param string $localPath 本地文件路径
 * @return string 可访问的URL
 */
function createAccessibleUrl($localPath) {
    $relativePath = str_replace(__DIR__, '', $localPath);
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    $host = $_SERVER['HTTP_HOST'];
    $scriptDir = dirname($_SERVER['PHP_SELF']);
    
    // 确保URL是正确格式
    $url = $protocol . $host . $scriptDir . $relativePath;
    try_on_log("为本地文件创建URL: " . $url);
    
    return $url;
}

/**
 * 检查URL是否可访问，并且内容是有效的图片
 * 
 * @param string $url 要检查的URL
 * @return bool 是否可访问
 */
function isUrlAccessible($url) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // 改为获取内容而不是HEAD请求
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'); // 添加用户代理
    
    $content = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $contentLength = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);
    curl_close($ch);
    
    try_on_log("URL访问检查: $url - 状态码: $httpCode, 内容类型: $contentType, 内容长度: $contentLength");
    
    // 检查HTTP状态码和内容类型
    if ($httpCode >= 200 && $httpCode < 300 && !empty($content)) {
        // 检查内容类型是否为图片
        if (strpos($contentType, 'image/') === 0) {
            return true;
        } else {
            try_on_log("URL内容类型不是图片: $contentType");
        }
    } else {
        try_on_log("URL无法访问或返回空内容: 状态码 $httpCode");
    }
    
    return false;
}

/**
 * 调用阿里云试衣API
 *
 * @param string $personImageUrl 人物图片URL
 * @param string|null $topGarmentUrl 上衣图片URL
 * @param string|null $bottomGarmentUrl 下装图片URL
 * @return array API响应结果
 */
function callAliyunTryOnAPI($personImageUrl, $topGarmentUrl = null, $bottomGarmentUrl = null) {
    // 阿里云API配置
    $apiKey = defined('ALIYUN_OUTFIT_API_KEY') ? ALIYUN_OUTFIT_API_KEY : ALIYUN_ACCESS_KEY_ID; // 使用专用密钥，如未定义则使用通用密钥
    
    // 获取用户ID，用于图片下载
    global $userId;
    
    // 修改API端点，使用与示例代码相同的URL结构
    $baseUrl = 'https://dashscope.aliyuncs.com/api/v1';
    $apiUrl = $baseUrl . '/services/aigc/image2image/image-synthesis';
    
    try_on_log("试衣API开始调用: 使用URL " . $apiUrl);
    
    // 确保图片URL是绝对路径且以http/https开头
    $personImageUrl = formatImageUrl($personImageUrl);
    try_on_log("人物图片URL(格式化后): " . $personImageUrl);
    
    // 验证URL是否可以被外部访问
    if (!checkUrlPublicAccessible($personImageUrl)) {
        return [
            'error' => true,
            'msg' => '人物图片URL无法从外部访问，请确保图片可公开访问'
        ];
    }
    
    // 构建请求体 - 使用示例代码中的参数结构
    $requestData = [
        'model' => defined('ALIYUN_TRY_ON_MODEL') ? ALIYUN_TRY_ON_MODEL : 'aitryon',
        'input' => [
            'person_image_url' => $personImageUrl
        ],
        'parameters' => [
            'resolution' => -1,
            'restore_face' => true
        ]
    ];
    
    // 处理上衣图片
    if ($topGarmentUrl) {
        $topGarmentUrl = formatImageUrl($topGarmentUrl);
        try_on_log("上衣图片URL(格式化后): " . $topGarmentUrl);
        
        if (!checkUrlPublicAccessible($topGarmentUrl)) {
            return [
                'error' => true,
                'msg' => '上衣图片URL无法从外部访问，请确保图片可公开访问'
            ];
        }
        
        $requestData['input']['top_garment_url'] = $topGarmentUrl;
    }
    
    // 处理下装图片
    if ($bottomGarmentUrl) {
        $bottomGarmentUrl = formatImageUrl($bottomGarmentUrl);
        try_on_log("下装图片URL(格式化后): " . $bottomGarmentUrl);
        
        if (!checkUrlPublicAccessible($bottomGarmentUrl)) {
            return [
                'error' => true,
                'msg' => '下装图片URL无法从外部访问，请确保图片可公开访问'
            ];
        }
        
        $requestData['input']['bottom_garment_url'] = $bottomGarmentUrl;
    }
    
    // 记录完整请求数据
    try_on_log("试衣API请求数据: " . json_encode($requestData));
    
    // 执行请求 - 包含重试机制
    try_on_log("试衣API发送请求...");
    $maxRetries = 2; // 最多重试2次
    $retryCount = 0;
    $success = false;
    $response = null;
    $httpCode = 0;
    $curlError = '';
    $data = null; // 初始化数据变量

    while (!$success && $retryCount <= $maxRetries) {
        if ($retryCount > 0) {
            try_on_log("第" . $retryCount . "次重试请求...");
            sleep(3); // 重试前等待3秒
        }
        
        $ch = curl_init($apiUrl);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 增加超时时间到30秒
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 增加连接超时设置
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证SSL证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 不验证主机名
        
        // 添加与示例代码相同的头部，包括异步标记
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey,
            'X-DashScope-Async: enable'  // 添加异步处理标记
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        try_on_log("试衣API响应状态码: " . $httpCode);
        try_on_log("试衣API响应内容: " . $response);
        
        // 判断是否为重试条件
        if ($curlError) {
            try_on_log("请求失败，错误信息: " . $curlError);
            $retryCount++;
            continue;
        }
        
        $data = json_decode($response, true);
        
        // 检查特定错误条件（超时或媒体下载失败）
        if ($httpCode == 400 && isset($data['code']) && 
            (strpos($data['message'], 'timed out') !== false || 
             strpos($data['code'], 'DataInspection') !== false)) {
            try_on_log("检测到超时或媒体下载错误，将尝试重试");
            $retryCount++;
            continue;
        }
        
        // 成功或其他错误条件不重试
        $success = true;
    }

    // 如果URL方式尝试失败，尝试使用base64编码方式
    if (!$success && $retryCount > $maxRetries) {
        try_on_log("常规URL方法失败，尝试使用base64编码...");
        
        // 获取图片内容并base64编码
        $personImage = file_get_contents($personImageUrl);
        if ($personImage === false) {
            try_on_log("错误: 无法读取人物图片内容");
            return [
                'error' => true,
                'msg' => '无法读取人物图片内容'
            ];
        }
        $personBase64 = 'data:image/jpeg;base64,' . base64_encode($personImage);
        
        // 准备新的请求数据
        $base64RequestData = [
            'model' => defined('ALIYUN_TRY_ON_MODEL') ? ALIYUN_TRY_ON_MODEL : 'aitryon',
            'input' => [
                'person_image_base64' => $personBase64
            ],
            'parameters' => [
                'resolution' => -1,
                'restore_face' => true
            ]
        ];
        
        // 处理上衣图片
        if (!empty($topGarmentUrl)) {
            $topImage = file_get_contents($topGarmentUrl);
            if ($topImage !== false) {
                $topBase64 = 'data:image/jpeg;base64,' . base64_encode($topImage);
                $base64RequestData['input']['top_garment_base64'] = $topBase64;
            } else {
                try_on_log("警告: 无法读取上衣图片内容，跳过上衣");
            }
        }
        
        // 处理下装图片
        if (!empty($bottomGarmentUrl)) {
            $bottomImage = file_get_contents($bottomGarmentUrl);
            if ($bottomImage !== false) {
                $bottomBase64 = 'data:image/jpeg;base64,' . base64_encode($bottomImage);
                $base64RequestData['input']['bottom_garment_base64'] = $bottomBase64;
            } else {
                try_on_log("警告: 无法读取下装图片内容，跳过下装");
            }
        }
        
        // 记录base64请求数据（省略实际base64内容以避免日志过大）
        try_on_log("使用base64编码发送请求...");
        
        // 重新发送请求
        $ch = curl_init($apiUrl);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($base64RequestData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 增加超时时间
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey,
            'X-DashScope-Async: enable'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        try_on_log("Base64请求响应状态码: " . $httpCode);
        
        if ($curlError) {
            try_on_log("Base64请求CURL错误: " . $curlError);
            return [
                'error' => true,
                'msg' => '网络请求错误: ' . $curlError
            ];
        }
        
        $data = json_decode($response, true);
        
        // 检查是否仍有错误
        if ($httpCode !== 200 || isset($data['code'])) {
            $errorMsg = isset($data['message']) ? $data['message'] : '未知错误';
            try_on_log("Base64请求返回错误: " . $errorMsg);
            return [
                'error' => true,
                'msg' => $errorMsg,
                'code' => $httpCode
            ];
        }
        
        // 如果base64请求成功，标记成功状态
        $success = true;
    }

    // 如果所有重试都失败了
    if (!$success || $curlError) {
        try_on_log("试衣API请求CURL错误: " . $curlError);
        return [
            'error' => true,
            'msg' => '网络请求错误: ' . $curlError
        ];
    }
    
    // 解析响应
    if (!$data) {
        try_on_log("无效的任务响应格式");
        return [
            'error' => true,
            'msg' => 'API响应错误或无效JSON'
        ];
    }
    
    // 检查是否返回错误
    if ($httpCode !== 200 || isset($data['code'])) {
        $errorMsg = isset($data['message']) ? $data['message'] : '未知错误';
        try_on_log("试衣API返回错误: " . $errorMsg . ", 状态码: " . $httpCode);
        
        return [
            'error' => true,
            'msg' => $errorMsg,
            'code' => $httpCode
        ];
    }

    // 从API响应中获取任务ID，注意，异步API返回数据结构与之前不同
    if (!isset($data['output']) || !isset($data['output']['task_id'])) {
        try_on_log("API响应错误或缺少task_id字段");
        return [
            'error' => true,
            'msg' => 'API响应错误或缺少task_id字段'
        ];
    }
    
    $taskId = $data['output']['task_id'];
    try_on_log("试衣API任务ID: " . $taskId);
    
    // 轮询获取任务结果
    try_on_log("开始轮询任务结果...");
    $result = pollTaskResult($taskId, $apiKey);
    
    // 如果任务成功，下载图片到本地
    if (!isset($result['error']) || !$result['error']) {
        $remoteImageUrl = $result['image_url'];
        
        // 确保从阿里云OSS下载图片
        if (strpos($remoteImageUrl, 'aliyuncs.com') !== false) {
            try_on_log("检测到阿里云OSS图片URL: " . $remoteImageUrl);
            
            // 验证阿里云OSS URL是否有效
            $isAccessible = false;
            $checkRetries = 2;
            
            for ($i = 0; $i <= $checkRetries; $i++) {
                if ($i > 0) {
                    try_on_log("第" . $i . "次尝试验证OSS URL...");
                    sleep(2);
                }
                
                $ch = curl_init($remoteImageUrl);
                curl_setopt($ch, CURLOPT_NOBODY, true); // 只检查头信息
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept: */*'
                ]);
                
                curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($httpCode >= 200 && $httpCode < 300) {
                    $isAccessible = true;
                    try_on_log("OSS URL验证成功，状态码: " . $httpCode);
                    break;
                } else {
                    try_on_log("OSS URL暂时无法访问，状态码: " . $httpCode . "，可能需要等待同步...");
                }
            }
            
            // 如果URL不可访问，可能需要等待一段时间
            if (!$isAccessible) {
                try_on_log("等待OSS URL同步，延迟3秒后再次尝试下载...");
                sleep(3);
            }
            
            // 执行下载
            $localImagePath = downloadRemoteImage($remoteImageUrl, $userId);
            
            if ($localImagePath) {
                $result['image_url'] = $localImagePath;
                try_on_log("图片已成功下载到本地: " . $localImagePath);
            } else {
                try_on_log("警告: 阿里云图片下载失败，使用原始URL: " . $remoteImageUrl);
                // 阿里云图片下载失败算作警告，但仍然返回原始URL，让前端处理
                try_on_log("由于下载失败，返回原始OSS URL并添加警告标志");
                $result['warning'] = true;
                $result['warning_msg'] = '无法将阿里云图片下载到本地，使用的是原始OSS链接，可能会导致后续访问问题';
            }
        } else {
            // 其他类型的图片URL也尝试下载
            $localImagePath = downloadRemoteImage($remoteImageUrl, $userId);
            if ($localImagePath) {
                $result['image_url'] = $localImagePath;
                try_on_log("图片已成功下载到本地: " . $localImagePath);
            } else {
                try_on_log("警告: 图片下载失败，使用原始URL: " . $remoteImageUrl);
            }
        }
    }
    
    return $result;
}

/**
 * 格式化图片URL，确保是完整的URL并且可被外部访问
 * 
 * @param string $url 原始URL
 * @return string 格式化后的URL
 */
function formatImageUrl($url) {
    try_on_log("格式化URL: $url");
    
    // 如果是相对路径，转换为绝对路径
    if (strpos($url, 'http') !== 0) {
        // 获取当前服务器域名和协议
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
        $host = $_SERVER['HTTP_HOST'];
        
        // 如果URL以/开头，直接拼接完整URL
        if (strpos($url, '/') === 0) {
            $url = $protocol . $host . $url;
        } else {
            // 否则视为相对于当前脚本的路径
            $url = $protocol . $host . dirname($_SERVER['PHP_SELF']) . '/' . $url;
        }
        
        try_on_log("URL转换为绝对路径: $url");
    }
    
    // 确保URL编码正确，把空格替换为%20
    $url = preg_replace('/\s+/', '%20', $url);
    
    // 示例代码使用了alidog.cn域名，检查当前URL是否已经使用该域名
    if (strpos($url, 'alidog.cn') === false && strpos($url, 'localhost') !== false) {
        try_on_log("警告: URL使用了localhost，可能无法从外部访问: $url");
    }
    
    return $url;
}

/**
 * 检查URL是否可从外部公开访问
 * 
 * @param string $url 要检查的URL
 * @return bool 是否可访问
 */
function checkUrlPublicAccessible($url) {
    try_on_log("检查URL是否可公开访问: " . $url);
    
    // 最基本的检查 - 确认URL是否以http或https开头
    if (strpos($url, 'http://') !== 0 && strpos($url, 'https://') !== 0) {
        try_on_log("警告: URL不是标准的http/https URL: " . $url);
        return false;
    }
    
    // 检查URL是否使用了localhost或内部IP地址
    $hostname = parse_url($url, PHP_URL_HOST);
    if ($hostname == 'localhost' || 
        strpos($hostname, '127.0.0.') === 0 || 
        strpos($hostname, '192.168.') === 0 ||
        strpos($hostname, '10.') === 0) {
        try_on_log("警告: URL使用了内部地址，无法从外部访问: " . $url);
        return false;
    }
    
    // 检查URL是否可以被当前服务器访问
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15); // 增加超时到15秒
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    
    curl_exec($ch);
    $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $isAccessible = $responseCode >= 200 && $responseCode < 300;
    try_on_log("URL访问检查: " . $url . " - 状态码: " . $responseCode . ", 可访问: " . ($isAccessible ? "是" : "否"));
    
    if (!$isAccessible) {
        try_on_log("警告: URL无法访问或返回非200状态码: " . $responseCode . ", 错误: " . $error);
    } else {
        try_on_log("URL可正常访问，但请确保阿里云API也能访问此URL");
    }
    
    // 如果无法访问，但域名是alidog.cn或cyyg.alidog.cn，尝试使用GET请求再次检查
    if (!$isAccessible && (strpos($hostname, 'alidog.cn') !== false)) {
        try_on_log("尝试使用GET请求再次检查alidog.cn域名URL");
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        $content = curl_exec($ch);
        $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        $isAccessible = $responseCode >= 200 && $responseCode < 300 && !empty($content);
        try_on_log("URL GET请求检查: " . $url . " - 状态码: " . $responseCode . ", 可访问: " . ($isAccessible ? "是" : "否"));
    }
    
    return $isAccessible;
}

/**
 * 轮询任务结果
 *
 * @param string $taskId 任务ID
 * @param string $apiKey API密钥
 * @param int $maxAttempts 最大尝试次数
 * @param int $interval 轮询间隔（秒）
 * @return array 任务结果
 */
function pollTaskResult($taskId, $apiKey, $maxAttempts = 30, $interval = 3) {
    $apiUrl = "https://dashscope.aliyuncs.com/api/v1/tasks/$taskId";
    try_on_log("开始轮询任务结果，API URL: " . $apiUrl);
    try_on_log("轮询设置: 最大尝试次数=" . $maxAttempts . ", 初始间隔=" . $interval . "秒");
    
    $start_time = time();
    $timeout = 600; // 10分钟超时，与示例代码一致
    $retry_count = 0;
    $dynamicInterval = $interval; // 动态间隔，初始为基本间隔
    
    while ((time() - $start_time) < $timeout && $retry_count < $maxAttempts) {
        // 等待间隔，使用动态增长的间隔时间
        if ($retry_count > 0) {
            try_on_log("轮询等待 " . $dynamicInterval . " 秒后继续...");
            sleep($dynamicInterval);
            
            // 逐渐增加等待时间，但最多不超过15秒
            $dynamicInterval = min($dynamicInterval + 2, 15);
        }
        
        try_on_log("轮询任务状态: 第" . ($retry_count+1) . "次尝试");
        
        // 查询任务状态
        $ch = curl_init($apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 增加超时时间
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5); // 连接超时
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $apiKey,
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36' // 添加User-Agent
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        
        if($curlError) {
            try_on_log("轮询任务CURL错误: " . $curlError);
            $retry_count++;
            curl_close($ch);
            continue;
        }
        
        curl_close($ch);
        
        try_on_log("轮询响应状态码: " . $httpCode);
        try_on_log("轮询响应内容: " . $response);
        
        if ($httpCode !== 200) {
            try_on_log("轮询请求失败，状态码: " . $httpCode);
            $retry_count++;
            continue;
        }
        
        $data = json_decode($response, true);
        if (!$data || !isset($data['output'])) {
            try_on_log("无效的任务响应格式");
            $retry_count++;
            continue;
        }
        
        // 检查任务状态
        $task_status = isset($data['output']['task_status']) ? $data['output']['task_status'] : "";
        try_on_log("任务 $taskId 状态: " . $task_status);
        
        // 任务成功完成
        if ($task_status === 'SUCCEEDED') {
            $image_url = "";
            // 检查不同的返回格式
            if (isset($data['output']['results']['image_url']) && $data['output']['results']['image_url']) {
                $image_url = $data['output']['results']['image_url'];
            } elseif (isset($data['output']['image_url']) && $data['output']['image_url']) {
                $image_url = $data['output']['image_url'];
            }
            
            if (!$image_url) {
                try_on_log("结果中缺少image_url字段");
                return [
                    'error' => true,
                    'msg' => '结果中缺少image_url字段',
                    'task_id' => $taskId
                ];
            }
            
            try_on_log("任务成功完成! 图片URL: " . $image_url);
            return [
                'error' => false,
                'image_url' => $image_url,
                'task_id' => $taskId
            ];
        }
        
        // 任务失败
        if ($task_status === 'FAILED' || $task_status === 'UNKNOWN') {
            $error_msg = isset($data['output']['message']) ? $data['output']['message'] : 
                        (isset($data['message']) ? $data['message'] : '任务处理失败');
            try_on_log("任务失败: " . $error_msg);
            
            return [
                'error' => true,
                'msg' => $error_msg,
                'task_id' => $taskId
            ];
        }
        
        // 任务仍在处理中，继续轮询
        try_on_log("任务 $taskId 状态: " . $task_status . "，继续等待...");
        $retry_count++;
    }
    
    // 达到最大尝试次数或超时，返回超时错误
    try_on_log("任务轮询超时，达到最大尝试次数: " . $maxAttempts . " 或超时时间: " . $timeout . "秒");
    
    return [
        'error' => true,
        'msg' => '任务处理超时',
        'task_id' => $taskId
    ];
}

/**
 * 下载远程图片到阿里云OSS
 * 
 * @param string $imageUrl 远程图片URL
 * @param int $userId 用户ID，用于创建唯一文件名
 * @return string|bool 成功返回OSS URL，失败返回false
 */
function downloadRemoteImage($imageUrl, $userId) {
    try_on_log("开始下载远程图片并上传到OSS: " . $imageUrl);
    
    // 初始化OSS辅助类
    $ossHelper = new OssHelper();
    
    // 如果已经是OSS URL，直接返回
    if ($ossHelper->isOssUrl($imageUrl)) {
        try_on_log("图片已经是OSS URL，不需要再次上传: " . $imageUrl);
        return $imageUrl;
    }
    
    // 生成唯一文件名
    $extension = pathinfo(parse_url($imageUrl, PHP_URL_PATH), PATHINFO_EXTENSION);
    if (empty($extension)) {
        $extension = 'jpg'; // 默认使用jpg
    }
    
    $timestamp = time();
    $randomString = substr(md5(rand()), 0, 8);
    $fileName = "try_on_{$userId}_{$timestamp}_{$randomString}.{$extension}";
    $ossKey = OSS_PATH_TRY_ON . $fileName;
    
    // 创建临时文件
    $tempFile = tempnam(sys_get_temp_dir(), 'oss_tryon_');
    
    // 下载图片，增强重试逻辑
    $maxRetries = 3;
    $retryCount = 0;
    $success = false;
    
    // 如果是阿里云OSS URL，先检查格式和过期时间
    $isAliyunOss = (strpos($imageUrl, 'aliyuncs.com') !== false);
    if ($isAliyunOss) {
        try_on_log("检测到阿里云OSS图片链接，进行特殊处理");
        
        // 检查URL是否包含过期时间参数
        if (strpos($imageUrl, 'Expires=') !== false) {
            $expiresPos = strpos($imageUrl, 'Expires=');
            $expiresEndPos = strpos($imageUrl, '&', $expiresPos);
            $expiresEndPos = ($expiresEndPos === false) ? strlen($imageUrl) : $expiresEndPos;
            $expiresValue = substr($imageUrl, $expiresPos + 8, $expiresEndPos - ($expiresPos + 8));
            
            if (is_numeric($expiresValue) && $expiresValue < time()) {
                try_on_log("警告: OSS链接可能已过期，过期时间戳: " . $expiresValue . ", 当前时间戳: " . time());
            }
        }
    }
    
    while (!$success && $retryCount <= $maxRetries) {
        if ($retryCount > 0) {
            try_on_log("第" . $retryCount . "次尝试下载图片...");
            sleep(2 + $retryCount); // 重试等待时间随重试次数增加
        }
        
        // 设置cURL选项以处理阿里云OSS链接
        $ch = curl_init($imageUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 45); // 增加超时到45秒
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15); // 增加连接超时
        
        // 设置更完整的用户代理和头信息，模拟浏览器行为
        $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
        curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
        
        // 为所有请求添加标准的头信息
        $headers = [
            'Accept: image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Accept-Encoding: gzip, deflate, br',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control: no-cache',
            'Pragma: no-cache'
        ];
        
        // 如果是阿里云OSS链接，添加特定的referer和其他头信息
        if ($isAliyunOss) {
            $headers[] = 'Referer: ' . $imageUrl;
            $headers[] = 'Origin: https://dashscope.aliyuncs.com';
            // 添加一个随机字符串作为请求ID，避免缓存
            $headers[] = 'X-Request-ID: ' . uniqid();
        }
        
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        
        // 对于阿里云OSS，尝试使用不同的方法
        if ($isAliyunOss && $retryCount > 0) {
            // 第二次尝试时使用GET请求并加入随机参数避免缓存
            $randomParam = '&nocache=' . uniqid();
            if (strpos($imageUrl, '?') === false) {
                $imageUrl .= '?' . ltrim($randomParam, '&');
            } else {
                $imageUrl .= $randomParam;
            }
            try_on_log("使用带随机参数的URL: " . $imageUrl);
        }
        
        $imageContent = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        curl_close($ch);
        
        // 检查是否下载成功
        if ($imageContent === false || $httpCode !== 200) {
            try_on_log("错误: 无法下载图片内容: " . $imageUrl . ", 状态码: " . $httpCode . ", 错误: " . $error);
            $retryCount++;
            continue;
        }
        
        // 验证内容是否为图片或相关类型
        $validTypes = ['image/', 'application/octet-stream', 'binary/octet-stream'];
        $isValidContent = false;
        
        foreach ($validTypes as $type) {
            if (strpos($contentType, $type) === 0) {
                $isValidContent = true;
                break;
            }
        }
        
        if (!$isValidContent) {
            try_on_log("错误: 下载的内容不是预期类型, 内容类型: " . $contentType);
            
            // 如果是阿里云OSS链接返回的是JSON错误，记录它
            if (strpos($contentType, 'application/json') === 0) {
                try_on_log("OSS错误响应: " . $imageContent);
            }
            
            $retryCount++;
            continue;
        }
        
        // 保存到临时文件
        if (file_put_contents($tempFile, $imageContent) === false) {
            try_on_log("错误: 无法保存图片到临时文件: " . $tempFile);
            $retryCount++;
            continue;
        }
        
        // 验证保存的文件是否有效
        if (!file_exists($tempFile) || filesize($tempFile) < 100) { // 小于100字节可能是无效图片
            try_on_log("错误: 保存的临时图片文件无效或过小: " . $tempFile . ", 大小: " . filesize($tempFile) . " 字节");
            $retryCount++;
            continue;
        }
        
        // 尝试上传到OSS
        $uploadResult = $ossHelper->uploadFile($tempFile, $ossKey);
        
        // 删除临时文件
        @unlink($tempFile);
        
        if (!$uploadResult['success']) {
            try_on_log("错误: 上传到OSS失败: " . $uploadResult['error']);
            $retryCount++;
            continue;
        }
        
        try_on_log("成功上传到OSS，URL: " . $uploadResult['url']);
        return $uploadResult['url'];
    }
    
    // 清理临时文件
    if (file_exists($tempFile)) {
        @unlink($tempFile);
    }
    
    try_on_log("错误: 经过多次重试后仍无法下载图片或上传到OSS");
    return false;
}

/**
 * 保存试穿历史记录到数据库
 * 
 * @param PDO $conn 数据库连接
 * @param int $userId 用户ID
 * @param string $resultImageUrl 结果图片URL
 * @param array $clothesIds 衣物ID数组
 * @param int $photoId 照片ID
 * @param string $taskId 任务ID
 * @param string $status 状态
 * @param string $usageType 使用的试衣次数类型（免费次数/付费次数/商家次数）
 * @param string $errorMessage 错误信息
 * @return bool 保存是否成功
 */
function saveHistoryRecord($conn, $userId, $resultImageUrl, $clothesIds, $photoId, $taskId, $status = 'success', $usageType = '', $errorMessage = '') {
    try {
        try_on_log("开始保存试穿历史记录: 用户ID={$userId}, 图片URL={$resultImageUrl}, 任务ID={$taskId}, 状态={$status}, 使用类型={$usageType}");
        
        // 如果提供了使用类型，将其添加到error_message字段
        // 这样可以避免修改数据库结构
        if (!empty($usageType)) {
            if (!empty($errorMessage)) {
                $errorMessage = $usageType . " | " . $errorMessage;
            } else {
                $errorMessage = $usageType;
            }
        }
        
        // 检查try_on_history表是否存在
        $tableExists = false;
        $tablesStmt = $conn->query("SHOW TABLES LIKE 'try_on_history'");
        if ($tablesStmt->rowCount() > 0) {
            $tableExists = true;
        }
        
        // 如果表不存在，尝试创建
        if (!$tableExists) {
            try_on_log("试穿历史表不存在，尝试创建...");
            $createTableSql = file_get_contents(__DIR__ . '/create_try_on_history_table.sql');
            if ($createTableSql) {
                $conn->exec($createTableSql);
                // 再次检查表是否成功创建
                $tablesStmt = $conn->query("SHOW TABLES LIKE 'try_on_history'");
                $tableExists = ($tablesStmt->rowCount() > 0);
            }
        }
        
        // 如果表仍然不存在，记录错误并返回
        if (!$tableExists) {
            try_on_log("错误: 无法创建试穿历史表");
            return false;
        }
        
        // 将衣物ID数组转换为字符串
        $clothesIdsStr = is_array($clothesIds) ? implode(',', $clothesIds) : $clothesIds;
        
        // 准备SQL语句，增加error_message字段
        $sql = "
            INSERT INTO try_on_history (
                user_id, 
                result_image_url, 
                clothes_ids, 
                photo_id, 
                task_id, 
                status, 
                error_message,
                created_at
            ) VALUES (
                :user_id, 
                :result_image_url, 
                :clothes_ids, 
                :photo_id, 
                :task_id, 
                :status, 
                :error_message,
                NOW()
            )
        ";
        
        $stmt = $conn->prepare($sql);
        
        // 绑定参数
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindParam(':result_image_url', $resultImageUrl);
        $stmt->bindParam(':clothes_ids', $clothesIdsStr);
        $stmt->bindParam(':photo_id', $photoId, PDO::PARAM_INT);
        $stmt->bindParam(':task_id', $taskId);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':error_message', $errorMessage);
        
        // 执行SQL
        $result = $stmt->execute();
        
        if ($result) {
            $historyId = $conn->lastInsertId();
            try_on_log("成功保存试穿历史记录，ID: {$historyId}");
            return true;
        } else {
            try_on_log("错误: 保存试穿历史记录失败");
            return false;
        }
    } catch (PDOException $e) {
        try_on_log("数据库错误: " . $e->getMessage());
        return false;
    } catch (Exception $e) {
        try_on_log("保存试穿历史时发生异常: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取用户试衣次数
 * 
 * @param PDO $conn 数据库连接
 * @param int $userId 用户ID
 * @return array 包含免费次数和付费次数的数组
 */
function getUserTryOnCounts($conn, $userId) {
    // 默认返回值
    $result = [
        'free_count' => 0,
        'paid_count' => 0
    ];
    
    try {
        // 根据配置选择使用哪种试衣次数检查方式
        if (defined('TRY_ON_COUNT_MODE') && TRY_ON_COUNT_MODE === 'dual') {
            // 双层次数模式：检查免费次数和付费次数
            $tryOnCountStmt = $conn->prepare("
                SELECT free_try_on_count, paid_try_on_count 
                FROM users 
                WHERE id = :user_id
            ");
            
            $tryOnCountStmt->execute([
                'user_id' => $userId
            ]);
            
            $tryOnData = $tryOnCountStmt->fetch(PDO::FETCH_ASSOC);
            if ($tryOnData) {
                $result['free_count'] = intval($tryOnData['free_try_on_count']);
                $result['paid_count'] = intval($tryOnData['paid_try_on_count']);
            }
            
            try_on_log("用户 {$userId} 双层次数模式，免费次数: {$result['free_count']}, 付费次数: {$result['paid_count']}");
        } else if (defined('TRY_ON_COUNT_MODE') && TRY_ON_COUNT_MODE === 'database') {
            // 数据库模式：从users表读取paid_try_on_count (原try_on_count)
            $tryOnCountStmt = $conn->prepare("
                SELECT paid_try_on_count as count 
                FROM users 
                WHERE id = :user_id
            ");
            
            $tryOnCountStmt->execute([
                'user_id' => $userId
            ]);
            
            $tryOnCount = $tryOnCountStmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
            $result['paid_count'] = intval($tryOnCount);
            try_on_log("用户 {$userId} 数据库次数模式，剩余试衣次数: {$result['paid_count']}");
        } else {
            // 每日一次模式（原有逻辑）
            // 获取当天0点的时间戳
            $todayStart = date('Y-m-d 00:00:00');
            
            // 查询用户当天的试衣次数
            $tryOnCountStmt = $conn->prepare("
                SELECT COUNT(*) as count 
                FROM try_on_history 
                WHERE user_id = :user_id 
                AND created_at >= :today_start 
                AND status = 'success'
            ");
            
            $tryOnCountStmt->execute([
                'user_id' => $userId,
                'today_start' => $todayStart
            ]);
            
            $tryOnCount = $tryOnCountStmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
            // 每日模式下，免费次数为设置的每日限制(默认1)减去已用次数
            $dailyLimit = defined('DEFAULT_FREE_TRY_ON_COUNT') ? DEFAULT_FREE_TRY_ON_COUNT : 1;
            $result['free_count'] = max(0, $dailyLimit - intval($tryOnCount));
            try_on_log("用户 {$userId} 每日一次模式，今日已试衣次数: $tryOnCount, 剩余免费次数: {$result['free_count']}");
        }
    } catch (Exception $e) {
        try_on_log("获取用户试衣次数时出错: " . $e->getMessage());
    }
    
    return $result;
}

// 在API调用成功后扣除次数的函数
function deductTryOnCount($conn, $pendingDeduction) {
    try_on_log("开始执行试穿点数扣除逻辑");
    $userId = $pendingDeduction['user_id'];
    $merchantId = $pendingDeduction['merchant_id'] ?? null; // 获取商家ID
    $forceUseUserCredits = $pendingDeduction['force_use_user_credits'] ?? false; // 获取强制使用用户积分的标记
    
    // 设置一个变量来记录使用的是哪种试衣次数
    $usageType = '';
    
    // 检查是否为商家衣物试穿且商家开启了共享试穿点数，且未强制使用用户积分
    if ($merchantId && !$forceUseUserCredits) {
        try_on_log("检测到商家衣物试穿，商家ID: " . $merchantId);
        
        // 获取商家信息以及试衣次数
        $merchantStmt = $conn->prepare("SELECT id, nickname, share_try_on_credits, free_try_on_count, paid_try_on_count FROM users WHERE id = :merchant_id AND merchant_status = 'yes'");
        $merchantStmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_INT);
        $merchantStmt->execute();
        $merchant = $merchantStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($merchantStmt->rowCount() > 0) {
            // 判断商家是否开启了共享试穿点数
            if ($merchant['share_try_on_credits'] == 1) {
                try_on_log("商家已开启共享试穿点数，尝试扣除商家次数");
                
                // 获取商家剩余次数
                $merchantFreeCount = intval($merchant['free_try_on_count']);
                $merchantPaidCount = intval($merchant['paid_try_on_count']);
                
                try_on_log("商家剩余次数 - 免费: {$merchantFreeCount}, 付费: {$merchantPaidCount}");
                
                // 首先尝试扣除商家免费次数
                if ($merchantFreeCount > 0) {
                    $stmt = $conn->prepare("UPDATE users SET free_try_on_count = free_try_on_count - 1, updated_at = NOW() WHERE id = :merchant_id AND free_try_on_count > 0");
                    $stmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_INT);
                    $stmt->execute();
                    
                    if ($stmt->rowCount() > 0) {
                        try_on_log("成功扣除商家 {$merchantId} 的免费试衣次数");
                        $usageType = "商家免费次数";
                        return [
                            'success' => true,
                            'message' => '商家共享试穿点数，本次试穿不消耗您的点数',
                            'free_count' => $pendingDeduction['free_count'],
                            'paid_count' => $pendingDeduction['paid_count'],
                            'merchant_shared' => true,
                            'merchant_deducted' => 'free',
                            'usage_type' => $usageType
                        ];
                    } else {
                        try_on_log("商家 {$merchantId} 免费次数扣除失败，尝试扣除付费次数");
                    }
                }
                
                // 如果商家免费次数不足，尝试扣除付费次数
                if ($merchantPaidCount > 0) {
                    $stmt = $conn->prepare("UPDATE users SET paid_try_on_count = paid_try_on_count - 1, updated_at = NOW() WHERE id = :merchant_id AND paid_try_on_count > 0");
                    $stmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_INT);
                    $stmt->execute();
                    
                    if ($stmt->rowCount() > 0) {
                        try_on_log("成功扣除商家 {$merchantId} 的付费试衣次数");
                        $usageType = "商家付费次数";
                        return [
                            'success' => true,
                            'message' => '商家共享试穿点数，本次试穿不消耗您的点数',
                            'free_count' => $pendingDeduction['free_count'],
                            'paid_count' => $pendingDeduction['paid_count'],
                            'merchant_shared' => true,
                            'merchant_deducted' => 'paid',
                            'usage_type' => $usageType
                        ];
                    } else {
                        try_on_log("商家 {$merchantId} 付费次数扣除失败");
                    }
                }
                
                // 如果商家所有次数都不足，提示用户并回退到扣除用户自己的次数
                try_on_log("商家 {$merchantId} 试穿次数已用完，将扣除用户自己的次数");
                
                // 返回特殊状态，提示用户确认是否继续
                return [
                    'success' => false,
                    'message' => '商家试衣次数已用完，如果继续将扣除您自己的试穿次数',
                    'merchant_credits_depleted' => true,
                    'user_free_count' => $pendingDeduction['free_count'],
                    'user_paid_count' => $pendingDeduction['paid_count']
                ];
            } else {
                try_on_log("商家未开启共享试穿点数，将扣除用户点数");
            }
        }
    }
    
    // 原有的扣点逻辑
    $freeCount = $pendingDeduction['free_count'];
    $paidCount = $pendingDeduction['paid_count'];
    $usePaid = $pendingDeduction['use_paid'] ?? ($freeCount <= 0);
    $message = isset($message) ? $message : ''; // 使用前面设置的消息，如果有的话
    $newFreeCount = $freeCount;
    $newPaidCount = $paidCount;
    
    try {
        // 判断是使用免费次数还是付费次数
        if (!$usePaid && $freeCount > 0) {
            // 使用免费次数
            $stmt = $conn->prepare("UPDATE users SET free_try_on_count = free_try_on_count - 1, updated_at = NOW() WHERE id = :user_id AND free_try_on_count > 0");
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $stmt->execute();
        
            // 检查影响的行数
            $affectedRows = $stmt->rowCount();
            if ($affectedRows > 0) {
                try_on_log("成功扣除用户 {$userId} 的免费试衣次数");
                $message .= $message ? '，' : '';
                $message .= '已使用1次免费试穿';
                $newFreeCount = $freeCount - 1;
                $usageType = "免费次数";
            } else {
                try_on_log("警告: 扣除用户 {$userId} 的免费试衣次数失败，可能次数不足");
                return [
                    'success' => false,
                    'message' => '免费试穿次数不足',
                    'merchant_shared' => false
                ];
            }
        } else if ($paidCount > 0) {
            // 使用付费次数
            $stmt = $conn->prepare("UPDATE users SET paid_try_on_count = paid_try_on_count - 1, updated_at = NOW() WHERE id = :user_id AND paid_try_on_count > 0");
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $stmt->execute();
            
            // 检查影响的行数
            $affectedRows = $stmt->rowCount();
            if ($affectedRows > 0) {
                try_on_log("成功扣除用户 {$userId} 的付费试衣次数");
                $message .= $message ? '，' : '';
                $message .= '已使用1次付费试穿';
                $newPaidCount = $paidCount - 1;
                $usageType = "付费次数";
            } else {
                try_on_log("警告: 扣除用户 {$userId} 的付费试衣次数失败，可能次数不足");
                return [
                    'success' => false,
                    'message' => '付费试穿次数不足',
                    'merchant_shared' => false
                ];
            }
        } else {
            try_on_log("用户 {$userId} 没有可用的试穿次数");
            return [
                'success' => false,
                'message' => '您的试穿次数已用完，请充值',
                'merchant_shared' => false
            ];
        }
        
        return [
            'success' => true,
            'message' => $message,
            'free_count' => $newFreeCount,
            'paid_count' => $newPaidCount,
            'merchant_shared' => false,
            'merchant_failed' => isset($message) && strpos($message, '商家试穿次数已用完') !== false,
            'usage_type' => $usageType
        ];
    } catch (Exception $e) {
        try_on_log("扣除试衣次数时出错: " . $e->getMessage());
        return [
            'success' => false,
            'message' => '扣除试穿次数时发生错误',
            'merchant_shared' => false
        ];
    }
}

/**
 * 获取用户当日试衣失败次数
 * 
 * @param PDO $conn 数据库连接
 * @param int $userId 用户ID
 * @return int 当日失败次数
 */
function getUserDailyFailedCount($conn, $userId) {
    try {
        // 获取当天0点的时间
        $today = date('Y-m-d 00:00:00');
        
        // 查询用户当天的试衣失败次数
        $stmt = $conn->prepare("
            SELECT COUNT(*) as count 
            FROM try_on_history 
            WHERE user_id = :user_id 
            AND status = 'failed' 
            AND created_at >= :today
        ");
        $stmt->execute([
            'user_id' => $userId,
            'today' => $today
        ]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)($result['count'] ?? 0);
    } catch (Exception $e) {
        try_on_log("获取用户当日失败次数时出错: " . $e->getMessage());
        return 0; // 出错时返回0，避免影响正常流程
    }
}

/**
 * 调用石衣试衣API
 *
 * @param string $personImageUrl 人物图片URL
 * @param string|null $topGarmentUrl 上衣图片URL
 * @param string|null $bottomGarmentUrl 下装图片URL
 * @param string $garmentType 衣物类型 (upper_body|lower_body|dresses)
 * @return array API响应结果
 */
function callShiyiTryOnAPI($personImageUrl, $topGarmentUrl = null, $bottomGarmentUrl = null, $garmentType = 'upper_body') {
    try_on_log("石衣API开始调用: " . SHIYI_API_URL);
    
    // 获取客户端传递的额外信息（如果有）
    global $data;
    $photoFormat = isset($data['photo_format']) ? $data['photo_format'] : '';
    $clothesFormat = isset($data['clothes_format']) ? $data['clothes_format'] : '';
    
    // 记录格式信息
    try_on_log("客户端传递的图片格式信息 - 人物: " . $photoFormat . ", 衣物: " . $clothesFormat);
    
    // 确定API密钥
    $apiKey = defined('SHIYI_API_KEY') ? SHIYI_API_KEY : WX_ENCODING_AES_KEY;
    
    // 生成唯一任务ID
    $taskId = 'shiyi_' . uniqid();
    
    // 准备文件数据
    $personFile = [
        'name' => 'person_image',
        'url' => $personImageUrl,
        'path' => $personImageUrl
    ];
    
    $garmentFile = [
        'name' => 'garment_image',
        'url' => $topGarmentUrl,
        'path' => $topGarmentUrl
    ];
    
    // 确定使用哪个衣物URL
    // 根据衣物类型，选择合适的衣物图片
    if ($garmentType === 'upper_body' && $topGarmentUrl) {
        $garmentFile['url'] = $topGarmentUrl;
        $garmentFile['path'] = $topGarmentUrl;
    } else if ($garmentType === 'lower_body' && $bottomGarmentUrl) {
        $garmentFile['url'] = $bottomGarmentUrl;
        $garmentFile['path'] = $bottomGarmentUrl;
    } else if ($garmentType === 'dresses' && $topGarmentUrl) {
        // 连衣裙模式下，topGarmentUrl应该包含连衣裙图片
        $garmentFile['url'] = $topGarmentUrl;
        $garmentFile['path'] = $topGarmentUrl;
    }
    
    try_on_log("石衣API请求参数: 人物=" . $personImageUrl . ", 衣物=" . $garmentFile['url'] . ", 类型=" . $garmentType);
    
    // 使用curl发送多部分表单请求
    $ch = curl_init();
    
    // 从URL中提取文件扩展名，确保有正确的扩展名
    $personExt = getExtensionFromUrl($personImageUrl);
    $garmentExt = getExtensionFromUrl($garmentFile['url']);
    
    // 如果客户端提供了格式信息，尝试使用它
    if (!empty($photoFormat) && in_array(strtolower($photoFormat), ['jpg', 'jpeg', 'png', 'webp'])) {
        $personExt = strtolower($photoFormat) === 'jpeg' ? 'jpg' : strtolower($photoFormat);
        try_on_log("使用客户端提供的人物图片格式: " . $personExt);
    }
    
    if (!empty($clothesFormat) && in_array(strtolower($clothesFormat), ['jpg', 'jpeg', 'png', 'webp'])) {
        $garmentExt = strtolower($clothesFormat) === 'jpeg' ? 'jpg' : strtolower($clothesFormat);
        try_on_log("使用客户端提供的衣物图片格式: " . $garmentExt);
    }
    
    // 如果无法从URL获取扩展名，默认使用jpg
    $personExt = $personExt ? $personExt : 'jpg';
    $garmentExt = $garmentExt ? $garmentExt : 'jpg';
    
    try_on_log("文件扩展名: 人物=" . $personExt . ", 衣物=" . $garmentExt);
    
    // 创建带正确扩展名的临时文件
    $personTemp = tempnam(sys_get_temp_dir(), 'person_') . '.' . $personExt;
    $garmentTemp = tempnam(sys_get_temp_dir(), 'garment_') . '.' . $garmentExt;
    
    // 如果临时文件已存在（因为tempnam创建了一个不带扩展名的文件），则删除它
    $basePersonTemp = str_replace('.' . $personExt, '', $personTemp);
    $baseGarmentTemp = str_replace('.' . $garmentExt, '', $garmentTemp);
    
    if (file_exists($basePersonTemp)) {
        unlink($basePersonTemp);
    }
    if (file_exists($baseGarmentTemp)) {
        unlink($baseGarmentTemp);
    }
    
    try {
        // 下载图片到临时文件
        $personImageContent = @file_get_contents($personImageUrl);
        if ($personImageContent === false) {
            try_on_log("错误: 无法下载人物图片: " . $personImageUrl);
            return [
                'error' => true,
                'msg' => '无法下载人物图片，请确保图片URL可访问',
                'task_id' => $taskId
            ];
        }
        
        $garmentImageContent = @file_get_contents($garmentFile['url']);
        if ($garmentImageContent === false) {
            try_on_log("错误: 无法下载衣物图片: " . $garmentFile['url']);
            return [
                'error' => true,
                'msg' => '无法下载衣物图片，请确保图片URL可访问',
                'task_id' => $taskId
            ];
        }
        
        // 验证图片内容是否有效
        if (!isValidImageContent($personImageContent, $personExt)) {
            try_on_log("错误: 人物图片内容无效或格式不匹配");
            return [
                'error' => true,
                'msg' => '人物图片内容无效或格式不支持，请确保使用JPG、PNG或WEBP格式',
                'task_id' => $taskId
            ];
        }
        
        if (!isValidImageContent($garmentImageContent, $garmentExt)) {
            try_on_log("错误: 衣物图片内容无效或格式不匹配");
            return [
                'error' => true,
                'msg' => '衣物图片内容无效或格式不支持，请确保使用JPG、PNG或WEBP格式',
                'task_id' => $taskId
            ];
        }
        
        // 如果图片非常小，尝试进行尺寸放大
        if (strlen($personImageContent) < 10240) { // 小于10KB
            try_on_log("警告: 人物图片非常小 (" . strlen($personImageContent) . " 字节)，尝试调整");
        }
        
        if (strlen($garmentImageContent) < 10240) { // 小于10KB
            try_on_log("警告: 衣物图片非常小 (" . strlen($garmentImageContent) . " 字节)，尝试调整");
        }
        
        // 保存图片内容到临时文件
        if (file_put_contents($personTemp, $personImageContent) === false) {
            try_on_log("错误: 无法保存人物图片到临时文件");
            return [
                'error' => true,
                'msg' => '无法保存人物图片',
                'task_id' => $taskId
            ];
        }
        
        if (file_put_contents($garmentTemp, $garmentImageContent) === false) {
            try_on_log("错误: 无法保存衣物图片到临时文件");
            return [
                'error' => true,
                'msg' => '无法保存衣物图片',
                'task_id' => $taskId
            ];
        }
        
        try_on_log("临时文件已创建: 人物=" . $personTemp . ", 衣物=" . $garmentTemp);
        try_on_log("图片大小: 人物=" . filesize($personTemp) . " 字节, 衣物=" . filesize($garmentTemp) . " 字节");
        
        // 创建CURLFile对象，确保文件名包含扩展名
        $curlPersonFile = new CURLFile(
            $personTemp, 
            'image/' . ($personExt == 'jpg' ? 'jpeg' : $personExt), 
            'person_image.' . $personExt
        );
        
        $curlGarmentFile = new CURLFile(
            $garmentTemp, 
            'image/' . ($garmentExt == 'jpg' ? 'jpeg' : $garmentExt), 
            'garment_image.' . $garmentExt
        );
        
        // 准备POST数据
        $postData = [
            'person_image' => $curlPersonFile,
            'garment_image' => $curlGarmentFile,
            'garment_type' => $garmentType
        ];
        
        // 设置curl选项
        curl_setopt_array($ch, [
            CURLOPT_URL => SHIYI_API_URL,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $postData,
            CURLOPT_HTTPHEADER => [
                'Authorization: ' . $apiKey
            ],
            CURLOPT_TIMEOUT => 60, // 增加超时时间到60秒
            CURLOPT_CONNECTTIMEOUT => 15, // 连接超时15秒
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
        ]);
        
        // 执行请求
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        // 记录调用结果
        try_on_log("石衣API响应状态码: " . $httpCode);
        try_on_log("石衣API响应内容长度: " . strlen($response) . " 字节");
        
        if ($error) {
            try_on_log("石衣API调用错误: " . $error);
            return [
                'error' => true,
                'msg' => '网络请求错误: ' . $error,
                'task_id' => $taskId
            ];
        }
        
        // 记录完整响应（用于调试）
        if ($httpCode !== 200) {
            try_on_log("石衣API响应错误，状态码: " . $httpCode);
            try_on_log("石衣API响应内容: " . $response);
            return [
                'error' => true,
                'msg' => '服务器返回错误状态码: ' . $httpCode,
                'task_id' => $taskId
            ];
        }
        
        // 解析响应JSON
        $data = json_decode($response, true);
        if (!$data) {
            try_on_log("石衣API响应无法解析为JSON: " . substr($response, 0, 500));
            return [
                'error' => true,
                'msg' => '无法解析服务器响应',
                'task_id' => $taskId
            ];
        }
        
        // 检查响应中是否存在错误
        if (isset($data['error']) && $data['error'] === true) {
            try_on_log("石衣API返回错误: " . ($data['msg'] ?? '未知错误'));
            return [
                'error' => true,
                'msg' => $data['msg'] ?? '服务器返回错误',
                'task_id' => $taskId
            ];
        }
        
        // 检查是否成功并包含结果图片URL
        if (isset($data['success']) && $data['success'] === true && isset($data['result_image'])) {
            $resultImageUrl = $data['result_image'];
            try_on_log("石衣API返回成功，结果图片URL: " . $resultImageUrl);
            
            // 下载结果图片到本地或OSS
            global $userId;
            $localImagePath = downloadRemoteImage($resultImageUrl, $userId);
            
            if ($localImagePath) {
                try_on_log("石衣API结果图片已下载到: " . $localImagePath);
                return [
                    'error' => false,
                    'image_url' => $localImagePath,
                    'task_id' => $taskId
                ];
            } else {
                try_on_log("石衣API结果图片下载失败，使用原始URL");
                return [
                    'error' => false,
                    'image_url' => $resultImageUrl,
                    'task_id' => $taskId,
                    'warning' => true,
                    'warning_msg' => '无法将结果图片下载到本地，使用的是原始链接'
                ];
            }
        }
        
        // 如果没有找到预期的成功响应格式，返回错误
        try_on_log("石衣API响应格式不符合预期: " . json_encode($data));
        return [
            'error' => true,
            'msg' => '服务器响应格式不符合预期',
            'task_id' => $taskId
        ];
    } finally {
        // 关闭curl句柄
        curl_close($ch);
        
        // 清理临时文件
        if (file_exists($personTemp)) {
            unlink($personTemp);
        }
        if (file_exists($garmentTemp)) {
            unlink($garmentTemp);
        }
    }
}

/**
 * 验证图片内容是否有效
 *
 * @param string $imageContent 图片内容
 * @param string $extension 预期的图片扩展名
 * @return bool 是否为有效图片
 */
function isValidImageContent($imageContent, $extension) {
    if (empty($imageContent)) {
        return false;
    }
    
    // 检查文件大小
    if (strlen($imageContent) < 100) { // 极小图片可能无效
        return false;
    }
    
    // 检查图片文件头部特征码
    $signatures = [
        'jpg' => ["\xFF\xD8\xFF"],
        'jpeg' => ["\xFF\xD8\xFF"],
        'png' => ["\x89\x50\x4E\x47\x0D\x0A\x1A\x0A"],
        'webp' => ["RIFF", "WEBP"]
    ];
    
    // 获取当前扩展名对应的特征码
    if (!isset($signatures[strtolower($extension)])) {
        // 如果扩展名未知，尝试检查所有已知格式
        foreach ($signatures as $sig_arr) {
            foreach ($sig_arr as $sig) {
                if (strncmp($imageContent, $sig, strlen($sig)) === 0) {
                    return true;
                }
            }
        }
        return false;
    }
    
    // 检查指定扩展名的特征码
    $validSignatures = $signatures[strtolower($extension)];
    foreach ($validSignatures as $sig) {
        if (strncmp($imageContent, $sig, strlen($sig)) === 0) {
            return true;
        }
    }
    
    return false;
}

/**
 * 从URL中提取文件扩展名
 *
 * @param string $url 图片URL
 * @return string|null 文件扩展名(不带点)，如果无法提取则返回null
 */
function getExtensionFromUrl($url) {
    // 移除URL中的查询参数
    $url = preg_replace('/\?.*/', '', $url);
    
    // 获取文件扩展名
    $extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));
    
    // 检查是否为允许的扩展名（只允许jpg、jpeg、png和webp）
    if (in_array($extension, ['jpg', 'jpeg', 'png', 'webp'])) {
        return $extension;
    }
    
    // 如果无法从URL获取有效扩展名，尝试根据内容类型推断
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_exec($ch);
    
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    curl_close($ch);
    
    // 根据内容类型确定扩展名
    if ($contentType) {
        if (strpos($contentType, 'image/jpeg') !== false) {
            return 'jpg';
        } elseif (strpos($contentType, 'image/png') !== false) {
            return 'png';
        } elseif (strpos($contentType, 'image/webp') !== false) {
            return 'webp';
        }
    }
    
    // 默认返回jpg
    return 'jpg';
}

/**
 * 从衣物类型获取API参数类型
 * 
 * @param string $clothingType 衣物类型('上装'/'Upper', '下装'/'Lower', '全身裙'/'Dress')
 * @return string API参数类型('upper_body', 'lower_body', 'dresses')
 */
function getApiTypeFromClothingType($clothingType) {
    switch ($clothingType) {
        case '上装':
        case 'Upper':
            return 'upper_body';
        case '下装':
        case 'Lower':
            return 'lower_body';
        case '全身裙':
        case 'Dress':
            return 'dresses';
        default:
            return 'upper_body'; // 默认为上装
    }
}

// 调用阿里云或石衣API获取试衣结果
if (TRY_ON_API_TYPE === 'aliyun') {
    try_on_log("使用阿里云试衣API");
    $apiResult = callAliyunTryOnAPI(
        $photo['image_url'],
        $topGarment ? $topGarment['image_url'] : null,
        $bottomGarment ? $bottomGarment['image_url'] : null
    );
} else {
    try_on_log("使用石衣试衣API");
    
    // 如果有AI识别的衣物类型，使用它；否则保持现有的逻辑
    if ($aiClothingType) {
        $apiGarmentType = getApiTypeFromClothingType($aiClothingType);
        try_on_log("使用AI识别的衣物类型调用石衣API: " . $apiGarmentType);
        $apiResult = callShiyiTryOnAPI(
            $photo['image_url'],
            $clothes[0]['image_url'],
            null,
            $apiGarmentType
        );
    } else {
        // 原有逻辑
        if ($dressGarment) {
            try_on_log("检测到连衣裙类型衣物，使用dresses模式调用石衣API");
            $apiResult = callShiyiTryOnAPI(
                $photo['image_url'],
                $dressGarment['image_url'],
                null,
                'dresses'
            );
        } elseif ($topGarment && $bottomGarment) {
            try_on_log("检测到上衣+下装搭配，但API不支持同时试上下装，将使用上衣");
            $apiResult = callShiyiTryOnAPI(
                $photo['image_url'],
                $topGarment['image_url'],
                null,
                'upper_body'
            );
        } elseif ($topGarment) {
            try_on_log("检测到上衣类型衣物，使用upper_body模式调用石衣API");
            $apiResult = callShiyiTryOnAPI(
                $photo['image_url'],
                $topGarment['image_url'],
                null,
                'upper_body'
            );
        } elseif ($bottomGarment) {
            try_on_log("检测到下装类型衣物，使用lower_body模式调用石衣API");
            $apiResult = callShiyiTryOnAPI(
                $photo['image_url'],
                $bottomGarment['image_url'],
                null,
                'lower_body'
            );
        } else {
            // 默认处理
            try_on_log("未检测到特定类型衣物，默认使用upper_body模式调用石衣API");
            $apiResult = callShiyiTryOnAPI(
                $photo['image_url'],
                $clothes[0]['image_url'],
                null,
                'upper_body'
            );
        }
    }
}
?> 