<?php
// 引入必要的文件
require_once 'auth.php';
require_once 'db.php';
require_once 'config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 处理POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => '不支持的请求方法'
    ]);
    exit;
}

// 获取请求体数据
$data = json_decode(file_get_contents('php://input'), true);

// 验证必要参数
if (!isset($data['outfit_id']) || !isset($data['action']) || !in_array($data['action'], ['like', 'unlike'])) {
    echo json_encode([
        'error' => true,
        'msg' => '缺少必要参数或参数无效'
    ]);
    exit;
}

$outfitId = intval($data['outfit_id']);
$action = $data['action'];

// 验证token获取用户ID
$auth = new Auth();

// 获取token
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 如果有Bearer前缀，去掉它
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
}

if (!$token) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供授权Token'
    ]);
    exit;
}

// 使用Auth类验证token
$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'error' => true,
        'msg' => '未授权，请先登录'
    ]);
    exit;
}

$userId = $payload['sub']; // 从payload中获取用户ID

// 对于体验账号，检查token是否以mock_开头或有模拟签名
$isMockUser = (strpos($token, 'mock_') === 0) || 
              (isset($payload['openid']) && $payload['openid'] === 'mock_openid');

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();
    
    // 开始事务
    $conn->beginTransaction();
    
    // 检查穿搭是否存在
    $checkStmt = $conn->prepare("SELECT id FROM outfits WHERE id = :outfit_id LIMIT 1");
    $checkStmt->bindParam(':outfit_id', $outfitId, PDO::PARAM_INT);
    $checkStmt->execute();
    
    if ($checkStmt->rowCount() === 0) {
        $conn->rollBack();
        echo json_encode([
            'error' => true,
            'msg' => '穿搭不存在'
        ]);
        exit;
    }
    
    // 对于体验账号，使用特定的查询方式
    if ($isMockUser) {
        // 使用session存储体验账号的点赞状态
        $sessionId = session_id();
        if (empty($sessionId)) {
            session_start();
            $sessionId = session_id();
        }
        
        // 体验账号的点赞状态存储在会话中
        if (!isset($_SESSION['mock_likes'])) {
            $_SESSION['mock_likes'] = [];
        }
        
        $mockLikeKey = "outfit_{$outfitId}";
        $isLiked = isset($_SESSION['mock_likes'][$mockLikeKey]) && $_SESSION['mock_likes'][$mockLikeKey] === true;
    } else {
        // 正常用户查询数据库
        $likeStmt = $conn->prepare("SELECT id FROM outfit_likes WHERE user_id = :user_id AND outfit_id = :outfit_id LIMIT 1");
        $likeStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $likeStmt->bindParam(':outfit_id', $outfitId, PDO::PARAM_INT);
        $likeStmt->execute();
        $isLiked = $likeStmt->rowCount() > 0;
    }
    
    // 根据操作执行不同的SQL
    if ($action === 'like' && !$isLiked) {
        if ($isMockUser) {
            // 体验账号：保存点赞状态到会话
            $_SESSION['mock_likes']["outfit_{$outfitId}"] = true;
        } else {
            // 添加点赞记录到数据库
            $addLikeStmt = $conn->prepare("INSERT INTO outfit_likes (user_id, outfit_id) VALUES (:user_id, :outfit_id)");
            $addLikeStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $addLikeStmt->bindParam(':outfit_id', $outfitId, PDO::PARAM_INT);
            $addLikeStmt->execute();
        }
        
        // 更新点赞数量
        $updateStmt = $conn->prepare("UPDATE outfits SET likes_count = likes_count + 1 WHERE id = :outfit_id");
        $updateStmt->bindParam(':outfit_id', $outfitId, PDO::PARAM_INT);
        $updateStmt->execute();
        
        $changed = true;
    } elseif ($action === 'unlike' && $isLiked) {
        if ($isMockUser) {
            // 体验账号：移除点赞状态
            if (isset($_SESSION['mock_likes']["outfit_{$outfitId}"])) {
                $_SESSION['mock_likes']["outfit_{$outfitId}"] = false;
            }
        } else {
            // 删除点赞记录
            $deleteLikeStmt = $conn->prepare("DELETE FROM outfit_likes WHERE user_id = :user_id AND outfit_id = :outfit_id");
            $deleteLikeStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $deleteLikeStmt->bindParam(':outfit_id', $outfitId, PDO::PARAM_INT);
            $deleteLikeStmt->execute();
        }
        
        // 更新点赞数量，确保不小于0
        $updateStmt = $conn->prepare("UPDATE outfits SET likes_count = GREATEST(0, likes_count - 1) WHERE id = :outfit_id");
        $updateStmt->bindParam(':outfit_id', $outfitId, PDO::PARAM_INT);
        $updateStmt->execute();
        
        $changed = true;
    } else {
        $changed = false;
    }
    
    // 获取最新点赞数
    $countStmt = $conn->prepare("SELECT likes_count FROM outfits WHERE id = :outfit_id");
    $countStmt->bindParam(':outfit_id', $outfitId, PDO::PARAM_INT);
    $countStmt->execute();
    $likesCount = $countStmt->fetchColumn();
    
    // 提交事务
    $conn->commit();
    
    // 返回结果
    echo json_encode([
        'success' => true,
        'action' => $action,
        'changed' => $changed,
        'likes_count' => $likesCount
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if (isset($conn)) {
        $conn->rollBack();
    }
    
    echo json_encode([
        'error' => true,
        'msg' => '点赞操作失败: ' . $e->getMessage()
    ]);
} 