/**
 * 照片详情脚本
 */
const PhotoDetails = {
    // 照片ID
    photoId: null,
    
    // 照片数据
    photoData: null,
    
    // DOM元素
    elements: {},
    
    // 内部图片查看器
    imageViewer: {
        // 查看器元素
        viewer: null,
        image: null,
        closeBtn: null,
        isInitialized: false,
        
        // 初始化查看器
        init: function() {
            if (this.isInitialized) return;
            
            // 创建查看器DOM
            this.viewer = document.createElement('div');
            this.viewer.className = 'image-viewer';
            this.viewer.style.cssText = 'position:fixed; top:0; left:0; width:100%; height:100%; ' +
                'background-color:rgba(0,0,0,0.9); z-index:9999; display:none; ' +
                'align-items:center; justify-content:center;';
            
            // 创建图片元素
            this.image = document.createElement('img');
            this.image.className = 'viewer-image';
            this.image.style.cssText = 'max-width:90%; max-height:90%; object-fit:contain;';
            this.viewer.appendChild(this.image);
            
            // 创建关闭按钮
            this.closeBtn = document.createElement('button');
            this.closeBtn.innerHTML = '&times;';
            this.closeBtn.style.cssText = 'position:absolute; top:15px; right:20px; ' +
                'background:none; border:none; color:white; font-size:30px; ' +
                'cursor:pointer; z-index:10000;';
            this.viewer.appendChild(this.closeBtn);
            
            // 添加到页面
            document.body.appendChild(this.viewer);
            
            // 绑定事件
            this.closeBtn.addEventListener('click', () => this.close());
            this.viewer.addEventListener('click', (e) => {
                if (e.target === this.viewer) this.close();
            });
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') this.close();
            });
            
            this.isInitialized = true;
            console.log('内部图片查看器初始化成功');
        },
        
        // 显示图片
        show: function(imageUrl) {
            if (!this.isInitialized) this.init();
            
            this.image.src = imageUrl;
            this.image.onload = () => {
                this.viewer.style.display = 'flex';
                document.body.style.overflow = 'hidden';
            };
            this.image.onerror = () => {
                console.error('图片加载失败:', imageUrl);
                alert('图片加载失败');
            };
        },
        
        // 关闭查看器
        close: function() {
            if (!this.isInitialized) return;
            this.viewer.style.display = 'none';
            document.body.style.overflow = '';
        },
        
        // 绑定图片点击事件
        bindImages: function(selector) {
            if (!this.isInitialized) this.init();
            
            const images = document.querySelectorAll(selector);
            console.log(`为${images.length}个元素绑定内部图片查看器`);
            
            images.forEach(img => {
                if (!img.dataset.hasInternalViewer) {
                    img.style.cursor = 'pointer';
                    
                    img.addEventListener('click', (event) => {
                        event.preventDefault();
                        event.stopPropagation();
                        
                        const imageUrl = img.getAttribute('data-origin') || img.src;
                        this.show(imageUrl);
                    });
                    
                    img.dataset.hasInternalViewer = 'true';
                }
            });
        }
    },
    
    /**
     * 初始化
     */
    init: function() {
        // 获取URL参数中的照片ID
        const urlParams = new URLSearchParams(window.location.search);
        this.photoId = urlParams.get('id');
        
        if (!this.photoId) {
            alert('缺少照片ID参数');
            window.location.href = 'photo_list.html';
            return;
        }
        
        // 初始化DOM元素引用
        this.elements = {
            photoDetailContainer: document.getElementById('photoDetailContainer'),
            userInfoContainer: document.getElementById('userInfoContainer')
        };
        
        // 初始化内部图片查看器
        this.imageViewer.init();
        
        // 加载照片详情
        this.loadPhotoDetails();
    },
    
    /**
     * 加载照片详情
     */
    loadPhotoDetails: function() {
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        fetch(`${Auth.apiBaseUrl}/get_admin_photo_details.php?id=${this.photoId}`, {
            method: 'GET',
            headers: {
                'Authorization': token
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '加载失败');
            }
            
            this.photoData = data.data;
            this.renderPhotoDetails(data.data.photo);
            this.renderUserInfo(data.data.user);
        })
        .catch(error => {
            console.error('获取照片详情失败:', error);
            this.elements.photoDetailContainer.innerHTML = `<div class="empty-text">加载失败: ${error.message}</div>`;
            this.elements.userInfoContainer.innerHTML = '<div class="empty-text">加载失败</div>';
        });
    },
    
    /**
     * 渲染照片基本信息
     * @param {Object} photo 照片数据
     */
    renderPhotoDetails: function(photo) {
        if (!photo) {
            this.elements.photoDetailContainer.innerHTML = '<div class="empty-text">照片数据不存在</div>';
            return;
        }
        
        const typeText = this.getTypeText(photo.type);
        
        // 默认图片
        const defaultImg = 'images/default-photo.png';
        
        // 检查图片URL并处理微信临时文件路径
        let imageUrl;
        if (!photo.image_url || photo.image_url.trim() === '') {
            imageUrl = defaultImg;
        } else if (photo.image_url.startsWith('wxfile://')) {
            // 不支持的微信临时文件URL，使用默认图片
            imageUrl = defaultImg;
        } else {
            imageUrl = photo.image_url;
        }
        
        const description = photo.description || '无描述';
        
        const html = `
            <div class="photo-details">
                <div class="photo-image-container">
                    <img src="${imageUrl}" class="photo-image" alt="照片" title="点击查看大图" onerror="this.src='${defaultImg}'" style="cursor: pointer;">
                </div>
                <div class="photo-info">
                    <div class="photo-description">${description}</div>
                    <div class="photo-id">ID: ${photo.id}</div>
                    <div class="photo-meta">
                        <div class="meta-item">
                            <span class="meta-label">类型:</span>
                            <span class="type-badge">${typeText}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">上传时间:</span>
                            <span>${this.formatDate(photo.created_at)}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">最后更新:</span>
                            <span>${photo.updated_at ? this.formatDate(photo.updated_at) : '无'}</span>
                        </div>
                    </div>
                    <div class="photo-actions">
                        <button class="photo-action-btn edit-btn" onclick="PhotoDetails.editPhoto()">编辑照片</button>
                        <button class="photo-action-btn delete-btn" onclick="PhotoDetails.deletePhoto()">删除照片</button>
                    </div>
                </div>
            </div>
        `;
        
        this.elements.photoDetailContainer.innerHTML = html;
        
        // 绑定照片大图预览
        try {
            console.log('使用内部图片查看器绑定图片预览');
            this.imageViewer.bindImages('.photo-image');
            console.log('成功绑定图片预览');
        } catch (error) {
            console.error('绑定图片预览失败:', error);
        }
    },
    
    /**
     * 渲染用户信息
     * @param {Object} user 用户数据
     */
    renderUserInfo: function(user) {
        if (!user) {
            this.elements.userInfoContainer.innerHTML = '<div class="empty-text">用户数据不存在</div>';
            return;
        }
        
        const gender = this.getGenderText(user.gender);
        
        // 本地默认头像
        const defaultAvatar = 'images/default-avatar.png';
        
        // 检查头像URL并处理微信临时文件路径
        let avatarUrl;
        if (!user.avatar_url || user.avatar_url.trim() === '') {
            avatarUrl = defaultAvatar;
        } else if (user.avatar_url.startsWith('wxfile://')) {
            // 不支持的微信临时文件URL，使用默认头像
            avatarUrl = defaultAvatar;
        } else {
            avatarUrl = user.avatar_url;
        }
        
        const html = `
            <h3 class="section-title">所属用户信息</h3>
            <div class="user-info-section">
                <div class="user-info-header">
                    <img src="${avatarUrl}" class="user-avatar" alt="用户头像" onerror="this.src='${defaultAvatar}'">
                    <div class="user-name">${user.nickname || '未设置昵称'}</div>
                </div>
                <div class="user-meta">
                    <div class="user-meta-item">
                        <span class="meta-label">用户ID:</span>
                        <span>${user.id}</span>
                    </div>
                    <div class="user-meta-item">
                        <span class="meta-label">性别:</span>
                        <span>${gender}</span>
                    </div>
                    <div class="user-meta-item">
                        <span class="meta-label">创建时间:</span>
                        <span>${this.formatDate(user.created_at)}</span>
                    </div>
                    <div class="user-meta-item">
                        <span class="meta-label">状态:</span>
                        <span>${user.status == 1 ? '正常' : '已禁用'}</span>
                    </div>
                </div>
                <div style="margin-top: 10px;">
                    <button class="photo-action-btn view-btn" onclick="window.location.href='user_details.html?id=${user.id}'">查看用户详情</button>
                </div>
            </div>
        `;
        
        this.elements.userInfoContainer.innerHTML = html;
    },
    
    /**
     * 编辑照片
     */
    editPhoto: function() {
        window.location.href = `photo_edit.html?id=${this.photoId}`;
    },
    
    /**
     * 删除照片
     */
    deletePhoto: function() {
        if (!confirm('确定要删除该照片吗？此操作不可恢复。')) {
            return;
        }
        
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        fetch(`${Auth.apiBaseUrl}/delete_admin_photo.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': token
            },
            body: JSON.stringify({
                id: this.photoId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '操作失败');
            }
            
            alert(data.msg || '删除成功');
            window.location.href = 'photo_list.html';
        })
        .catch(error => {
            console.error('删除照片失败:', error);
            alert(`操作失败: ${error.message}`);
        });
    },
    
    /**
     * 获取类型文本
     * @param {String} type 类型
     * @returns {String} 类型文本
     */
    getTypeText: function(type) {
        const typeMap = {
            'full': '全身照',
            'half': '半身照',
            'other': '其他'
        };
        
        return typeMap[type] || type;
    },
    
    /**
     * 获取性别文本
     * @param {Number} gender 性别码
     * @returns {String} 性别文本
     */
    getGenderText: function(gender) {
        switch(parseInt(gender)) {
            case 1: return '男';
            case 2: return '女';
            default: return '未知';
        }
    },
    
    /**
     * 格式化日期显示
     * @param {String} dateStr 日期字符串
     * @returns {String} 格式化的日期
     */
    formatDate: function(dateStr) {
        if (!dateStr) return '未知';
        
        const date = new Date(dateStr);
        return isNaN(date.getTime()) 
            ? dateStr 
            : date.getFullYear() + '-' + 
              String(date.getMonth() + 1).padStart(2, '0') + '-' + 
              String(date.getDate()).padStart(2, '0') + ' ' +
              String(date.getHours()).padStart(2, '0') + ':' +
              String(date.getMinutes()).padStart(2, '0');
    }
}; 