-- 模块4：数据共享基础模块 - 为现有表添加circle_id字段
-- 为现有的衣橱、衣物、穿搭相关表添加圈子关联字段

-- 1. 为wardrobes表添加circle_id字段
ALTER TABLE `wardrobes` 
ADD COLUMN `circle_id` int(11) DEFAULT NULL COMMENT '关联的圈子ID，NULL表示个人数据' AFTER `user_id`;

-- 添加外键约束
ALTER TABLE `wardrobes` 
ADD CONSTRAINT `fk_wardrobes_circle` 
FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE SET NULL;

-- 添加索引
CREATE INDEX `idx_wardrobes_circle_id` ON `wardrobes` (`circle_id`);
CREATE INDEX `idx_wardrobes_user_circle` ON `wardrobes` (`user_id`, `circle_id`);

-- 2. 为clothes表添加circle_id字段
ALTER TABLE `clothes` 
ADD COLUMN `circle_id` int(11) DEFAULT NULL COMMENT '关联的圈子ID，NULL表示个人数据' AFTER `user_id`;

-- 添加外键约束
ALTER TABLE `clothes` 
ADD CONSTRAINT `fk_clothes_circle` 
FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE SET NULL;

-- 添加索引
CREATE INDEX `idx_clothes_circle_id` ON `clothes` (`circle_id`);
CREATE INDEX `idx_clothes_user_circle` ON `clothes` (`user_id`, `circle_id`);
CREATE INDEX `idx_clothes_wardrobe_circle` ON `clothes` (`wardrobe_id`, `circle_id`);

-- 3. 为outfits表添加circle_id字段
ALTER TABLE `outfits` 
ADD COLUMN `circle_id` int(11) DEFAULT NULL COMMENT '关联的圈子ID，NULL表示个人数据' AFTER `user_id`;

-- 添加外键约束
ALTER TABLE `outfits` 
ADD CONSTRAINT `fk_outfits_circle` 
FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE SET NULL;

-- 添加索引
CREATE INDEX `idx_outfits_circle_id` ON `outfits` (`circle_id`);
CREATE INDEX `idx_outfits_user_circle` ON `outfits` (`user_id`, `circle_id`);

-- 4. 为clothing_categories表添加circle_id字段（如果存在）
-- 检查表是否存在，如果存在则添加字段
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_schema = DATABASE() AND table_name = 'clothing_categories');

SET @sql = IF(@table_exists > 0, 
    'ALTER TABLE `clothing_categories` ADD COLUMN `circle_id` int(11) DEFAULT NULL COMMENT \'关联的圈子ID，NULL表示个人数据\' AFTER `user_id`',
    'SELECT "clothing_categories table does not exist" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 如果表存在，添加外键约束和索引
SET @sql = IF(@table_exists > 0, 
    'ALTER TABLE `clothing_categories` ADD CONSTRAINT `fk_clothing_categories_circle` FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE SET NULL',
    'SELECT "clothing_categories table does not exist" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@table_exists > 0, 
    'CREATE INDEX `idx_clothing_categories_circle_id` ON `clothing_categories` (`circle_id`)',
    'SELECT "clothing_categories table does not exist" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 为outfit_categories表添加circle_id字段（如果存在）
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_schema = DATABASE() AND table_name = 'outfit_categories');

SET @sql = IF(@table_exists > 0, 
    'ALTER TABLE `outfit_categories` ADD COLUMN `circle_id` int(11) DEFAULT NULL COMMENT \'关联的圈子ID，NULL表示个人数据\' AFTER `user_id`',
    'SELECT "outfit_categories table does not exist" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 如果表存在，添加外键约束和索引
SET @sql = IF(@table_exists > 0, 
    'ALTER TABLE `outfit_categories` ADD CONSTRAINT `fk_outfit_categories_circle` FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE SET NULL',
    'SELECT "outfit_categories table does not exist" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@table_exists > 0, 
    'CREATE INDEX `idx_outfit_categories_circle_id` ON `outfit_categories` (`circle_id`)',
    'SELECT "outfit_categories table does not exist" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 创建数据同步日志表
CREATE TABLE `circle_data_sync_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `circle_id` int(11) NOT NULL COMMENT '圈子ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `sync_type` enum('initial','incremental','manual') NOT NULL DEFAULT 'incremental' COMMENT '同步类型：initial=初始同步，incremental=增量同步，manual=手动同步',
  `data_type` enum('wardrobes','clothes','outfits','categories','all') NOT NULL COMMENT '同步的数据类型',
  `sync_status` enum('pending','processing','completed','failed') NOT NULL DEFAULT 'pending' COMMENT '同步状态',
  `items_count` int(11) NOT NULL DEFAULT '0' COMMENT '同步的数据条数',
  `error_message` text COMMENT '错误信息',
  `started_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_circle_id` (`circle_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_data_type` (`data_type`),
  KEY `idx_started_at` (`started_at`),
  CONSTRAINT `fk_sync_logs_circle` FOREIGN KEY (`circle_id`) REFERENCES `outfit_circles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sync_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='圈子数据同步日志表';

-- 创建索引以优化查询性能
CREATE INDEX `idx_sync_logs_circle_user` ON `circle_data_sync_logs` (`circle_id`, `user_id`);
CREATE INDEX `idx_sync_logs_status_type` ON `circle_data_sync_logs` (`sync_status`, `data_type`);