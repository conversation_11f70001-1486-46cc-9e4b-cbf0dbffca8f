<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        .search-box {
            display: flex;
            margin-bottom: 20px;
        }
        .search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px 0 0 4px;
            outline: none;
        }
        .search-btn {
            padding: 8px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
        }
        .search-btn:hover {
            background-color: #40a9ff;
        }
        .user-table {
            width: 100%;
            border-collapse: collapse;
        }
        .user-table th,
        .user-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }
        .user-table th {
            background-color: #fafafa;
            font-weight: 500;
        }
        .user-table tr:hover {
            background-color: #f5f5f5;
        }
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
        }
        .status-active {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        .status-disabled {
            background-color: #fff1f0;
            color: #f5222d;
        }
        .action-btn {
            padding: 4px 10px;
            border-radius: 4px;
            border: 1px solid;
            background-color: transparent;
            cursor: pointer;
            margin-right: 5px;
            font-size: 13px;
        }
        .view-btn {
            color: #1890ff;
            border-color: #1890ff;
        }
        .view-btn:hover {
            background-color: #e6f7ff;
        }
        .disable-btn {
            color: #f5222d;
            border-color: #f5222d;
        }
        .disable-btn:hover {
            background-color: #fff1f0;
        }
        .enable-btn {
            color: #52c41a;
            border-color: #52c41a;
        }
        .enable-btn:hover {
            background-color: #f6ffed;
        }
        .pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        .pagination button {
            padding: 5px 10px;
            margin: 0 5px;
            border: 1px solid #d9d9d9;
            background-color: white;
            cursor: pointer;
            border-radius: 4px;
        }
        .pagination button:hover:not(:disabled) {
            color: #1890ff;
            border-color: #1890ff;
        }
        .pagination button:disabled {
            color: #d9d9d9;
            cursor: not-allowed;
        }
        .pagination .current-page {
            color: #1890ff;
            border-color: #1890ff;
        }
        .no-data {
            text-align: center;
            padding: 20px;
            color: #999;
        }
        
        /* 排序表头样式 */
        .sortable {
            cursor: pointer;
            position: relative;
            padding-right: 18px;
        }
        
        .sortable:after {
            content: '\2195'; /* 上下箭头 */
            position: absolute;
            right: 5px;
            color: #999;
        }
        
        .sortable.asc:after {
            content: '\2191'; /* 向上箭头 */
            color: #1890ff;
        }
        
        .sortable.desc:after {
            content: '\2193'; /* 向下箭头 */
            color: #1890ff;
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            overflow-y: auto;
        }
        
        .modal-content {
            background-color: white;
            margin: 50px auto;
            padding: 20px;
            border-radius: 5px;
            width: 80%;
            max-width: 800px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            position: relative;
        }
        
        .modal-close {
            position: absolute;
            right: 20px;
            top: 20px;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
        }
        
        .modal-close:hover {
            color: #666;
        }
        
        .modal-header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .modal-body {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .modal-loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .primary-btn {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .primary-btn:hover {
            background-color: #40a9ff;
        }
        
        /* 排行表格样式 */
        .ranking-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .ranking-table th,
        .ranking-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .ranking-table th {
            background-color: #fafafa;
            font-weight: 500;
        }
        
        .ranking-table tr:hover {
            background-color: #f5f5f5;
        }
        
        .ranking-table .rank {
            font-weight: bold;
            color: #1890ff;
        }
        
        .top-3 {
            color: #f5222d;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>用户管理</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="card">
                <div class="search-box">
                    <input type="text" id="searchInput" class="search-input" placeholder="搜索用户昵称或ID..." />
                    <button id="searchBtn" class="search-btn">搜索</button>
                    <select id="statusFilter" class="filter-select" style="margin-left: 10px; padding: 8px 10px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                        <option value="">所有状态</option>
                        <option value="1">正常</option>
                        <option value="0">已禁用</option>
                    </select>
                    <button id="showRankingBtn" class="primary-btn" style="margin-left: 10px;">查看全部用户衣物排行</button>
                </div>
                
                <table class="user-table">
                    <thead>
                        <tr>
                            <th class="sortable" data-sort="id">ID</th>
                            <th>头像</th>
                            <th class="sortable" data-sort="nickname">昵称</th>
                            <th>OpenID</th>
                            <th>性别</th>
                            <th>状态</th>
                            <th class="sortable" data-sort="clothes_count">衣物数量</th>
                            <th>试衣次数</th>
                            <th class="sortable" data-sort="created_at">创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="userTableBody">
                        <tr>
                            <td colspan="10" class="no-data">加载中...</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="pagination" id="pagination">
                    <button id="prevBtn" disabled>&lt; 上一页</button>
                    <span id="pageInfo">第 0/0 页</span>
                    <button id="nextBtn" disabled>下一页 &gt;</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 衣物排行模态框 -->
    <div id="rankingModal" class="modal">
        <div class="modal-content">
            <span class="modal-close">&times;</span>
            <div class="modal-header">
                <h3>用户衣物数量排行</h3>
                <p>按照衣物数量从高到低排序，显示所有用户</p>
            </div>
            <div class="modal-body" id="rankingModalBody">
                <div class="modal-loading">正在加载用户排行数据...</div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/user_list.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为user
            Sidebar.init('user');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化用户列表
            UserList.init();
        });
    </script>
</body>
</html> 