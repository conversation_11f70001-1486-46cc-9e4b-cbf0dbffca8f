<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查Authorization头是否存在
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证管理员令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '无效或已过期的令牌']);
    exit();
}

// 获取数据库连接
$db = new Database();
$conn = $db->getConnection();

// 获取分页参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$pageSize = isset($_GET['page_size']) ? intval($_GET['page_size']) : 10;
$page = max(1, $page); // 确保页码至少为1
$pageSize = min(50, max(5, $pageSize)); // 限制每页大小在5到50之间

// 计算偏移量
$offset = ($page - 1) * $pageSize;

// 获取筛选条件
$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : null;
$status = isset($_GET['status']) ? $_GET['status'] : null;

try {
    // 构建基本查询
    $countSql = "SELECT COUNT(*) as total FROM face_analysis";
    $querySql = "SELECT fa.*, u.nickname, u.avatar_url 
                FROM face_analysis fa 
                LEFT JOIN users u ON fa.user_id = u.id";

    // 构建WHERE子句
    $whereConditions = [];
    $params = [];

    if ($userId) {
        $whereConditions[] = "fa.user_id = :user_id";
        $params[':user_id'] = $userId;
    }

    if ($status && in_array($status, ['pending', 'processing', 'completed', 'failed'])) {
        $whereConditions[] = "fa.status = :status";
        $params[':status'] = $status;
    }

    // 添加WHERE子句（如果有筛选条件）
    if (!empty($whereConditions)) {
        $whereClause = " WHERE " . implode(" AND ", $whereConditions);
        $countSql .= $whereClause;
        $querySql .= $whereClause;
    }

    // 添加排序和分页
    $querySql .= " ORDER BY fa.created_at DESC LIMIT :offset, :limit";
    $params[':offset'] = $offset;
    $params[':limit'] = $pageSize;

    // 执行计数查询
    $countStmt = $conn->prepare($countSql);
    foreach ($params as $key => $value) {
        if ($key !== ':offset' && $key !== ':limit') {
            if ($key === ':user_id') {
                $countStmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $countStmt->bindValue($key, $value);
            }
        }
    }
    $countStmt->execute();
    $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

    // 执行主查询
    $stmt = $conn->prepare($querySql);
    foreach ($params as $key => $value) {
        if ($key === ':offset' || $key === ':limit' || $key === ':user_id') {
            $stmt->bindValue($key, $value, PDO::PARAM_INT);
        } else {
            $stmt->bindValue($key, $value);
        }
    }
    $stmt->execute();
    $analyses = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 处理数据，隐藏或格式化敏感字段
    foreach ($analyses as &$analysis) {
        // 确保CDN URL字段存在，即使为空
        if (!isset($analysis['cdn_front_photo_url'])) {
            $analysis['cdn_front_photo_url'] = '';
        }
        
        if (!isset($analysis['cdn_side_photo_url'])) {
            $analysis['cdn_side_photo_url'] = '';
        }
        
        // 使用CDN URL替换本地URL，如果CDN URL存在
        if (!empty($analysis['cdn_front_photo_url'])) {
            $analysis['display_front_photo_url'] = $analysis['cdn_front_photo_url'];
        } else {
            $analysis['display_front_photo_url'] = $analysis['front_photo_url'];
        }
        
        if (!empty($analysis['cdn_side_photo_url'])) {
            $analysis['display_side_photo_url'] = $analysis['cdn_side_photo_url'];
        } else {
            $analysis['display_side_photo_url'] = $analysis['side_photo_url'];
        }
        
        // 从analysis_result中提取关键信息或摘要
        if (isset($analysis['analysis_result']) && !empty($analysis['analysis_result'])) {
            $resultData = json_decode($analysis['analysis_result'], true);
            if ($resultData) {
                // 提取摘要或关键信息
                $analysis['result_summary'] = isset($resultData['data']['summary']) ? 
                    $resultData['data']['summary'] : 
                    '无可用摘要';
            } else {
                $analysis['result_summary'] = '解析结果失败';
            }
        } else {
            $analysis['result_summary'] = '无分析结果';
        }

        // 移除完整的分析结果，减少数据传输量
        unset($analysis['analysis_result']);

        // 格式化日期
        if (isset($analysis['created_at'])) {
            $analysis['created_at'] = date('Y-m-d H:i:s', strtotime($analysis['created_at']));
        }
        if (isset($analysis['updated_at'])) {
            $analysis['updated_at'] = date('Y-m-d H:i:s', strtotime($analysis['updated_at']));
        }
    }

    // 计算分页信息
    $totalPages = ceil($totalCount / $pageSize);

    // 返回结果
    echo json_encode([
        'status' => 'success',
        'data' => $analyses,
        'pagination' => [
            'total' => $totalCount,
            'page' => $page,
            'page_size' => $pageSize,
            'total_pages' => $totalPages
        ]
    ]);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error', 
        'message' => '数据库查询错误', 
        'error' => $e->getMessage()
    ]);
    exit();
} 