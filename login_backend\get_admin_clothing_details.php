<?php
/**
 * 获取衣物详情API
 * - 用于管理员界面显示衣物详情
 * - 需要管理员权限
 * - 返回衣物和所属用户信息
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] != 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证管理员身份
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取并检查衣物ID参数
if (!isset($_GET['id']) || empty($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少衣物ID参数']);
    exit();
}

$clothingId = (int)$_GET['id'];

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

try {
    // 查询衣物数据和用户信息
    $query = "SELECT c.*, u.id as user_id, u.nickname, u.gender, u.avatar_url, u.created_at as user_created_at
            FROM clothes c
            JOIN users u ON c.user_id = u.id
            WHERE c.id = :id";
    $stmt = $conn->prepare($query);
    $stmt->bindValue(':id', $clothingId, PDO::PARAM_INT);
    $stmt->execute();
    
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$result) {
        http_response_code(404);
        echo json_encode(['error' => true, 'msg' => '衣物不存在']);
        exit();
    }
    
    // 分离衣物和用户数据
    $clothingData = [
        'id' => $result['id'],
        'user_id' => $result['user_id'],
        'name' => $result['name'],
        'category' => $result['category'],
        'image_url' => $result['image_url'],
        'tags' => $result['tags'],
        'description' => $result['description'],
        'created_at' => $result['created_at'],
        'updated_at' => $result['updated_at']
    ];
    
    // 处理标签 - 将逗号分隔的标签转换为数组
    if (!empty($clothingData['tags'])) {
        $clothingData['tags_array'] = explode(',', $clothingData['tags']);
    } else {
        $clothingData['tags_array'] = [];
    }
    
    // 处理描述 - 如果是JSON字符串尝试解析为对象
    if (!empty($clothingData['description'])) {
        $decodedDescription = json_decode($clothingData['description'], true);
        if (json_last_error() === JSON_ERROR_NONE) {
            $clothingData['description_obj'] = $decodedDescription;
        }
    }
    
    $userData = [
        'id' => $result['user_id'],
        'nickname' => $result['nickname'],
        'gender' => $result['gender'],
        'avatar_url' => $result['avatar_url'],
        'created_at' => $result['user_created_at']
    ];
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'msg' => '获取衣物详情成功',
        'data' => [
            'clothing' => $clothingData,
            'user' => $userData
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '服务器错误: ' . $e->getMessage()
    ]);
} 