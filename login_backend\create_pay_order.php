<?php
/**
 * 创建支付订单API
 * 
 * 用于创建微信支付订单，购买试衣次数
 * 
 * 请求方法：POST
 * 请求参数：
 * - package_id: 套餐ID
 * 
 * 返回：
 * {
 *   "error": false,
 *   "data": {
 *     "order_id": "订单号",
 *     "amount": 金额,
 *     "count": 次数,
 *     "pay_params": {
 *       "timeStamp": "时间戳",
 *       "nonceStr": "随机字符串",
 *       "package": "prepay_id=xxx",
 *       "signType": "RSA",
 *       "paySign": "签名"
 *     }
 *   }
 * }
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';
require_once 'wx_pay_helper.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => '只支持POST请求'
    ]);
    exit;
}

// 验证用户身份
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供授权Token'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token包含Bearer前缀，去掉它
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$auth = new Auth();
$payload = $auth->verifyToken($token);

if (!$payload) {
    echo json_encode([
        'error' => true,
        'msg' => '无效的授权Token或Token已过期'
    ]);
    exit;
}

// 模拟用户不能购买
if (isset($payload['openid']) && $payload['openid'] === 'mock_openid') {
    echo json_encode([
        'error' => true,
        'msg' => '体验账号不支持购买试衣次数，请登录后再试'
    ]);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['package_id'])) {
    echo json_encode([
        'error' => true,
        'msg' => '请求参数不完整'
    ]);
    exit;
}

$packageId = intval($input['package_id']);
$userId = $payload['sub'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 获取用户信息
    $stmt = $conn->prepare("SELECT nickname FROM users WHERE id = :user_id");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $userNickname = $user ? $user['nickname'] : '';
    
    // 创建微信支付订单
    $wxPayHelper = new WxPayHelper();
    
    // 获取用户IP
    $userIp = $_SERVER['REMOTE_ADDR'];
    
    // 创建订单
    $orderResult = $wxPayHelper->createOrder($userId, $userNickname, $packageId, $userIp);
    
    if (isset($orderResult['error']) && $orderResult['error']) {
        echo json_encode([
            'error' => true,
            'msg' => $orderResult['msg']
        ]);
        exit;
    }
    
    // 保存订单记录到数据库
    $packageInfo = $wxPayHelper->getPackageInfo($packageId);
    $count = $packageInfo[0]; // 次数
    $amount = $packageInfo[1] / 100; // 金额（元）
    
    // 检查recharge_records表是否存在
    $checkTableStmt = $conn->query("SHOW TABLES LIKE 'recharge_records'");
    if ($checkTableStmt->rowCount() === 0) {
        // 表不存在，创建表
        $createTableSql = file_get_contents(__DIR__ . '/create_recharge_records_table.sql');
        $conn->exec($createTableSql);
    }
    
    // 插入订单记录
    $stmt = $conn->prepare("
        INSERT INTO recharge_records (
            user_id, 
            nickname, 
            order_id, 
            package_id, 
            amount, 
            count, 
            status, 
            created_at
        ) VALUES (
            :user_id,
            :nickname,
            :order_id,
            :package_id,
            :amount,
            :count,
            'pending',
            NOW()
        )
    ");
    
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':nickname', $userNickname);
    $stmt->bindParam(':order_id', $orderResult['order_id']);
    $stmt->bindParam(':package_id', $packageId, PDO::PARAM_INT);
    $stmt->bindParam(':amount', $amount);
    $stmt->bindParam(':count', $count, PDO::PARAM_INT);
    
    $stmt->execute();
    
    // 返回订单信息和支付参数
    echo json_encode([
        'error' => false,
        'data' => [
            'order_id' => $orderResult['order_id'],
            'amount' => $orderResult['amount'],
            'count' => $orderResult['count'],
            'pay_params' => $orderResult['pay_params']
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '创建订单失败: ' . $e->getMessage()
    ]);
} 