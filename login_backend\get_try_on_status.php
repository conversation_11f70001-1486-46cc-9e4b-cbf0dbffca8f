<?php
/**
 * 获取试穿任务状态API
 * 
 * 根据任务ID返回当前处理状态、进度和预计剩余时间
 * 
 * GET参数:
 * - task_id: 试穿任务ID
 * 
 * 返回:
 * {
 *   "error": false,
 *   "data": {
 *     "status": "processing",  // 可能的状态: queued, uploading, processing, finishing, completed, failed
 *     "progress": 65,          // 进度百分比 0-100
 *     "time_remaining": 10,    // 预计剩余时间（秒）
 *     "message": "合成中...",   // 状态描述
 *     "result_image_url": ""   // 如果完成，包含结果图片URL
 *   }
 * }
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否有Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

// 验证token
$token = $_SERVER['HTTP_AUTHORIZATION'];
$auth = new Auth();
$tokenData = $auth->verifyToken($token);

if (!$tokenData) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid or expired token'
    ]);
    exit;
}

// 获取用户ID
$userId = $tokenData['sub'];

// 获取任务ID
if (!isset($_GET['task_id']) || empty($_GET['task_id'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Missing task_id parameter'
    ]);
    exit;
}

$taskId = $_GET['task_id'];

// 从数据库获取任务状态
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查任务是否存在并属于当前用户
    $stmt = $conn->prepare("SELECT * FROM try_on_history WHERE task_id = :task_id AND user_id = :user_id");
    $stmt->execute([
        'task_id' => $taskId,
        'user_id' => $userId
    ]);
    
    $task = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$task) {
        // 如果数据库中没有记录，尝试向阿里云API查询任务状态
        $result = queryAliyunTaskStatus($taskId);
        
        if ($result['error']) {
            echo json_encode([
                'error' => true,
                'msg' => '任务不存在或不属于当前用户'
            ]);
            exit;
        }
        
        // 返回从阿里云API获取的状态
        echo json_encode([
            'error' => false,
            'data' => $result
        ]);
        exit;
    }
    
    // 根据数据库中的状态构建响应
    $status = strtolower($task['status']);
    $resultImageUrl = $task['result_image_url'];
    
    // 计算进度和剩余时间
    $progress = 0;
    $timeRemaining = 30; // 默认剩余时间30秒
    $message = '准备中...';
    
    // 根据状态计算进度和消息
    if ($status === 'success' || $status === 'completed') {
        $progress = 100;
        $timeRemaining = 0;
        $message = '完成';
        $status = 'completed';
    } else if ($status === 'failed') {
        $progress = 100;
        $timeRemaining = 0;
        $message = '失败';
    } else {
        // 计算大致进度
        $createdTime = strtotime($task['created_at']);
        $now = time();
        $elapsedSeconds = $now - $createdTime;
        
        // 预估总时间为30秒
        $estimatedTotalTime = 30;
        
        if ($elapsedSeconds <= 5) {
            // 前5秒是排队阶段
            $status = 'queued';
            $progress = min(20, ($elapsedSeconds / 5) * 20);
            $timeRemaining = $estimatedTotalTime - $elapsedSeconds;
            $message = '排队中...';
        } else if ($elapsedSeconds <= 10) {
            // 5-10秒是上传阶段
            $status = 'uploading';
            $progress = 20 + min(20, (($elapsedSeconds - 5) / 5) * 20);
            $timeRemaining = $estimatedTotalTime - $elapsedSeconds;
            $message = '提交数据...';
        } else if ($elapsedSeconds <= 25) {
            // 10-25秒是处理阶段
            $status = 'processing';
            $progress = 40 + min(40, (($elapsedSeconds - 10) / 15) * 40);
            $timeRemaining = $estimatedTotalTime - $elapsedSeconds;
            $message = '合成中...';
        } else {
            // 25秒以后是完成阶段
            $status = 'finishing';
            $progress = 80 + min(20, (($elapsedSeconds - 25) / 5) * 20);
            $timeRemaining = max(0, $estimatedTotalTime - $elapsedSeconds);
            $message = '即将完成...';
        }
    }
    
    // 返回状态信息
    echo json_encode([
        'error' => false,
        'data' => [
            'status' => $status,
            'progress' => round($progress),
            'time_remaining' => max(0, round($timeRemaining)),
            'message' => $message,
            'result_image_url' => $resultImageUrl
        ]
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'error' => true,
        'msg' => '数据库操作失败: ' . $e->getMessage()
    ]);
}

/**
 * 查询阿里云任务状态
 * 
 * @param string $taskId 任务ID
 * @return array 任务状态信息
 */
function queryAliyunTaskStatus($taskId) {
    // 阿里云API配置
    $apiKey = defined('ALIYUN_OUTFIT_API_KEY') ? ALIYUN_OUTFIT_API_KEY : ALIYUN_ACCESS_KEY_ID;
    $apiUrl = "https://dashscope.aliyuncs.com/api/v1/tasks/$taskId";
    
    // 查询任务状态
    $ch = curl_init($apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $apiKey,
        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    if ($curlError || $httpCode !== 200) {
        return [
            'error' => true,
            'msg' => '无法获取任务状态: ' . ($curlError ?: "HTTP错误 $httpCode")
        ];
    }
    
    $data = json_decode($response, true);
    if (!$data || !isset($data['output'])) {
        return [
            'error' => true,
            'msg' => '无效的任务响应格式'
        ];
    }
    
    // 解析任务状态
    $taskStatus = isset($data['output']['task_status']) ? $data['output']['task_status'] : "";
    
    // 根据阿里云返回的状态转换为我们的状态格式
    $status = 'processing';
    $progress = 50;
    $timeRemaining = 15;
    $message = '合成中...';
    $resultImageUrl = '';
    
    if ($taskStatus === 'SUCCEEDED') {
        $status = 'completed';
        $progress = 100;
        $timeRemaining = 0;
        $message = '完成';
        
        // 获取结果图片URL
        if (isset($data['output']['results']['image_url']) && $data['output']['results']['image_url']) {
            $resultImageUrl = $data['output']['results']['image_url'];
        } elseif (isset($data['output']['image_url']) && $data['output']['image_url']) {
            $resultImageUrl = $data['output']['image_url'];
        }
    } else if ($taskStatus === 'FAILED' || $taskStatus === 'UNKNOWN') {
        $status = 'failed';
        $progress = 100;
        $timeRemaining = 0;
        $message = '失败';
    } else if ($taskStatus === 'PENDING') {
        $status = 'queued';
        $progress = 10;
        $timeRemaining = 25;
        $message = '排队中...';
    } else if ($taskStatus === 'RUNNING') {
        $status = 'processing';
        $progress = 50;
        $timeRemaining = 15;
        $message = '合成中...';
    }
    
    return [
        'error' => false,
        'status' => $status,
        'progress' => $progress,
        'time_remaining' => $timeRemaining,
        'message' => $message,
        'result_image_url' => $resultImageUrl
    ];
} 