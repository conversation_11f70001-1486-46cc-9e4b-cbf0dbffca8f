/* pages/clothing-categories/add/add.wxss */

.container {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  font-size: 42rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 60rpx;
}

.form-container {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 40rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.required {
  color: #ff4444;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
  box-sizing: border-box;
  height: 88rpx;
  line-height: 40rpx;
  display: flex;
  align-items: center;
}

.form-input:focus {
  border-color: #333;
  background-color: #fff;
}

.character-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.form-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  line-height: 1.4;
}

.button-container {
  display: flex;
  gap: 30rpx;
}

.cancel-btn, .submit-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  margin: 0;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
}

.submit-btn {
  background-color: #333;
  color: #fff;
}

.submit-btn:disabled {
  background-color: #cccccc;
  color: #999;
} 