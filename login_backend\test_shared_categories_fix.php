<?php
/**
 * 测试共享分类修复效果
 * 验证系统分类和自定义分类是否都正确显示
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "=== 共享分类修复效果测试 ===\n";
    echo "当前用户ID: $userId\n\n";
    
    // 1. 获取用户所在的活跃圈子
    echo "1. 获取用户所在的活跃圈子:\n";
    $circleQuery = "SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'";
    $circleStmt = $conn->prepare($circleQuery);
    $circleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $circleStmt->execute();
    $userCircles = $circleStmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($userCircles)) {
        echo "用户不在任何活跃圈子中\n";
        exit;
    }
    
    echo "活跃圈子ID: " . implode(', ', $userCircles) . "\n\n";
    $circleIds = implode(',', $userCircles);
    
    // 2. 检查圈子中的自定义分类数据
    echo "2. 检查圈子中的自定义分类数据:\n";
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.code, c.user_id, c.circle_id, u.nickname as creator_nickname
        FROM clothing_categories c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.is_system = 0 AND c.circle_id IN ($circleIds)
        ORDER BY c.user_id, c.created_at DESC
    ");
    $stmt->execute();
    $allCustomCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "圈子中总自定义分类数: " . count($allCustomCategories) . "\n";
    $ownCount = 0;
    $othersCount = 0;
    
    foreach ($allCustomCategories as $cat) {
        if ($cat['user_id'] == $userId) {
            $ownCount++;
            echo "- [自己] {$cat['name']} (code: {$cat['code']}, ID: {$cat['id']})\n";
        } else {
            $othersCount++;
            echo "- [其他] {$cat['name']} (code: {$cat['code']}, 创建者: {$cat['creator_nickname']}, ID: {$cat['id']})\n";
        }
    }
    
    echo "统计: 自己的 $ownCount 个，其他用户的 $othersCount 个\n\n";
    
    // 3. 测试修复后的shared查询
    echo "3. 测试修复后的shared查询:\n";
    
    $sql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                   u.nickname as creator_nickname,
                   CASE
                       WHEN c.is_system = 1 THEN 'system'
                       WHEN c.circle_id IS NULL THEN 'personal'
                       ELSE 'shared'
                   END as data_source
            FROM clothing_categories c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE (
                -- 当前用户的系统分类（用于显示系统分类下的共享衣物）
                (c.is_system = 1 AND c.user_id = :user_id) OR
                -- 其他用户的自定义分类（已同步到圈子的）
                (c.is_system = 0 AND c.user_id != :user_id AND c.circle_id IN ($circleIds))
            )
            ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC";
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $sharedResult = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "shared查询结果数量: " . count($sharedResult) . "\n";
    $systemCount = 0;
    $customCount = 0;
    
    foreach ($sharedResult as $cat) {
        if ($cat['is_system']) {
            $systemCount++;
            echo "- [系统] {$cat['name']} (code: {$cat['code']}, data_source: {$cat['data_source']})\n";
        } else {
            $customCount++;
            echo "- [自定义] {$cat['name']} (code: {$cat['code']}, 创建者: {$cat['creator_nickname']}, data_source: {$cat['data_source']})\n";
        }
    }
    
    echo "统计: 系统分类 $systemCount 个，自定义分类 $customCount 个\n\n";
    
    // 4. 验证修复效果
    echo "4. 验证修复效果:\n";
    
    if ($systemCount > 0) {
        echo "✅ 系统分类显示正常 ($systemCount 个)\n";
    } else {
        echo "❌ 系统分类缺失\n";
    }
    
    if ($othersCount > 0 && $customCount == $othersCount) {
        echo "✅ 其他用户自定义分类显示正常 ($customCount/$othersCount)\n";
    } elseif ($othersCount > 0 && $customCount < $othersCount) {
        echo "⚠️ 其他用户自定义分类显示不完整 ($customCount/$othersCount)\n";
    } elseif ($othersCount == 0) {
        echo "⚠️ 圈子中没有其他用户的自定义分类\n";
    } else {
        echo "❌ 其他用户自定义分类显示异常\n";
    }
    
    // 5. 模拟API调用测试
    echo "\n5. 模拟API调用测试:\n";
    
    // 模拟前端调用分类API
    $includeCircleData = true;
    $dataSource = 'shared';
    
    // 执行与API相同的逻辑
    $circleQuery = "SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'";
    $circleStmt = $conn->prepare($circleQuery);
    $circleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $circleStmt->execute();
    $apiUserCircles = $circleStmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($apiUserCircles)) {
        $apiSql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                          u.nickname as creator_nickname, 'system' as data_source
                   FROM clothing_categories c
                   LEFT JOIN users u ON c.user_id = u.id
                   WHERE c.is_system = 1 AND c.user_id = :user_id
                   ORDER BY c.sort_order ASC, c.created_at ASC";
    } else {
        $apiCircleIds = implode(',', $apiUserCircles);
        $apiSql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                          u.nickname as creator_nickname,
                          CASE
                              WHEN c.is_system = 1 THEN 'system'
                              WHEN c.circle_id IS NULL THEN 'personal'
                              ELSE 'shared'
                          END as data_source
                   FROM clothing_categories c
                   LEFT JOIN users u ON c.user_id = u.id
                   WHERE (
                       (c.is_system = 1 AND c.user_id = :user_id) OR
                       (c.is_system = 0 AND c.user_id != :user_id AND c.circle_id IN ($apiCircleIds))
                   )
                   ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC";
    }
    
    $apiStmt = $conn->prepare($apiSql);
    $apiStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $apiStmt->execute();
    $apiResult = $apiStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "API模拟调用结果数量: " . count($apiResult) . "\n";
    $apiSystemCount = 0;
    $apiCustomCount = 0;
    
    foreach ($apiResult as $cat) {
        if ($cat['is_system']) {
            $apiSystemCount++;
        } else {
            $apiCustomCount++;
        }
    }
    
    echo "API结果统计: 系统分类 $apiSystemCount 个，自定义分类 $apiCustomCount 个\n";
    
    // 6. 总结
    echo "\n6. 总结:\n";
    
    if ($apiSystemCount > 0 && $apiCustomCount == $othersCount) {
        echo "🎉 修复成功！\n";
        echo "- 系统分类正确显示: $apiSystemCount 个\n";
        echo "- 其他用户自定义分类正确显示: $apiCustomCount 个\n";
        echo "- 前端现在应该能看到完整的分类列表\n";
    } elseif ($apiSystemCount == 0) {
        echo "❌ 系统分类仍然缺失\n";
    } elseif ($apiCustomCount < $othersCount) {
        echo "⚠️ 部分自定义分类仍然缺失\n";
        echo "- 期望: $othersCount 个，实际: $apiCustomCount 个\n";
    } else {
        echo "⚠️ 需要进一步检查\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
