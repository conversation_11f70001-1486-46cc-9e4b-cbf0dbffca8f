<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 验证小程序用户权限
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证用户token
$userData = $auth->verifyToken($token);
if (!$userData) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取数据库连接
$db = new Database();
$conn = $db->getConnection();

// 验证是否提供了推荐穿搭ID
if (!isset($_GET['id']) || empty($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少推荐穿搭ID']);
    exit();
}

$outfitId = intval($_GET['id']);

try {
    // 查询推荐穿搭基本信息
    $query = "SELECT ro.*, oc.name as category_name 
              FROM recommended_outfits ro 
              LEFT JOIN outfit_categories oc ON ro.category_id = oc.id 
              WHERE ro.id = ? AND ro.status = 1";
    
    $stmt = $conn->prepare($query);
    $stmt->bindValue(1, $outfitId, PDO::PARAM_INT);
    $stmt->execute();
    $outfit = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$outfit) {
        http_response_code(404);
        echo json_encode(['error' => true, 'msg' => '找不到指定的推荐穿搭或穿搭已禁用']);
        exit();
    }
    
    // 查询推荐穿搭商品列表
    $itemsQuery = "SELECT * FROM recommended_outfit_items 
                   WHERE outfit_id = ? 
                   ORDER BY sort_order ASC, id ASC";
    
    $itemsStmt = $conn->prepare($itemsQuery);
    $itemsStmt->bindValue(1, $outfitId, PDO::PARAM_INT);
    $itemsStmt->execute();
    $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 将商品列表添加到穿搭对象中
    $outfit['items'] = $items;
    
    // 增加查看次数
    incrementViewCount($conn, $outfitId);
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'data' => $outfit
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '数据库错误: ' . $e->getMessage()]);
}

// 增加查看次数的辅助函数
function incrementViewCount($conn, $outfitId) {
    try {
        // 检查是否存在统计记录
        $checkQuery = "SELECT id FROM recommended_outfit_stats WHERE outfit_id = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bindValue(1, $outfitId, PDO::PARAM_INT);
        $checkStmt->execute();
        $statsRecord = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($statsRecord) {
            // 更新现有记录
            $updateQuery = "UPDATE recommended_outfit_stats 
                           SET view_count = view_count + 1
                           WHERE outfit_id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bindValue(1, $outfitId, PDO::PARAM_INT);
            $updateStmt->execute();
        } else {
            // 创建新记录
            $insertQuery = "INSERT INTO recommended_outfit_stats 
                           (outfit_id, view_count, copy_link_count, created_at) 
                           VALUES (?, 1, 0, NOW())";
            $insertStmt = $conn->prepare($insertQuery);
            $insertStmt->bindValue(1, $outfitId, PDO::PARAM_INT);
            $insertStmt->execute();
        }
    } catch (Exception $e) {
        // 只记录错误，不影响主要响应
        error_log('增加查看次数失败: ' . $e->getMessage());
    }
}

// 记录复制链接次数的API
// 由于需要单独的请求才能准确统计复制操作，这里只是示例，需要前端调用
// 实际统计将在另一个API文件中实现: record_copy_link.php 