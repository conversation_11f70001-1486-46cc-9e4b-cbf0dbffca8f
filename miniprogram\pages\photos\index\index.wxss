.container {
  padding: 20rpx;
  background-color: #f9f9f9;
  min-height: 100vh;
  box-sizing: border-box;
}

.photos-grid {
  display: flex;
  justify-content: space-between;
  margin: 0 -10rpx;
}

.column {
  width: 50%;
  padding: 0 10rpx;
  box-sizing: border-box;
}

.photo-item {
  width: 100%;
  margin-bottom: 20rpx;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  font-size: 0;
  line-height: 0;
}

.add-photo {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border: 1rpx dashed #dcdcdc;
  height: 360rpx;
  font-size: 28rpx;
  line-height: normal;
}

.add-icon {
  font-size: 80rpx;
  color: #999;
  line-height: 1;
}

.add-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

.photo-image {
  width: 100%;
  display: block;
  margin: 0;
  padding: 0;
  vertical-align: top;
}

.empty-state {
  margin-top: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
  font-size: 28rpx;
  line-height: 2;
}

.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 弹框样式 */
.photo-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx;
}

.modal-content {
  width: 90%;
  max-width: 650rpx;
  height: auto;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  padding: 16rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  max-height: 90vh; /* 限制最大高度 */
}

.modal-photo-container {
  position: relative;
  width: 100%;
  background-color: #f9f9f9;
  overflow: hidden;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200rpx; /* 设置最小高度 */
  max-height: calc(80vh - 100rpx); /* 预留删除按钮的空间 */
}

.modal-photo-image {
  width: 100%;
  display: block;
  object-fit: contain;
  max-height: calc(80vh - 100rpx);
}

.photo-type {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background-color: rgba(59, 130, 246, 0.8);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  z-index: 10;
}

.photo-type.half {
  background-color: rgba(59, 130, 246, 0.8);
}

.modal-footer {
  padding: 20rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border-top: 1rpx solid #f1f1f1;
}

.modal-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100rpx;
  height: 100rpx;
}

.modal-action-icon {
  background-color: #ff3b30;
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.modal-action-text {
  font-size: 24rpx;
  color: #333;
  margin-top: 8rpx;
}

.delete-icon {
  display: block;
  width: 30rpx;
  height: 36rpx;
  position: relative;
  border: 2px solid #fff;
  border-radius: 2px;
}

.add-photo-button {
  position: fixed;
  bottom: 50rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: #000;
  color: #fff;
  font-size: 30rpx;
  padding: 18rpx 50rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-delete-button {
  width: 100%;
  padding: 24rpx 0;
  text-align: center;
  color: #ff3b30;
  font-size: 32rpx;
  margin-top: 20rpx;
  border-top: 1px solid #f0f0f0;
}

.photo-item.selected {
  border: 3rpx solid #000;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.select-mark {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #000;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.try-on-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding: 0 30rpx;
  z-index: 100;
}

.try-on-button {
  width: 48%;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  transition: all 0.3s;
}

.try-on-button.disabled {
  background-color: #ccc;
  color: #666;
}

.try-on-button.active {
  background-color: #000;
  color: #fff;
}

.try-on-button.add-photo {
  background-color: #f8f8f8;
  color: #333;
  border: 1px solid #ddd;
}
