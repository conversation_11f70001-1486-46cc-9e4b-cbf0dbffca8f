/**
 * 推荐穿搭分类编辑
 */
const CategoryEditor = {
    // 当前编辑的分类ID
    categoryId: null,
    
    // 是否是新增模式
    isNewMode: true,
    
    /**
     * 初始化编辑页面
     */
    init: function() {
        // 检查登录状态
        if (!checkAdminLogin()) {
            return;
        }
        
        // 解析URL参数，获取分类ID
        const urlParams = new URLSearchParams(window.location.search);
        this.categoryId = urlParams.get('id');
        this.isNewMode = !this.categoryId;
        
        // 根据模式设置页面标题
        $('#pageTitle').text(this.isNewMode ? '添加推荐穿搭分类' : '编辑推荐穿搭分类');
        
        // 如果是编辑模式，加载分类数据
        if (!this.isNewMode) {
            this.loadCategoryData();
        }
        
        // 绑定表单提交事件
        this.bindEvents();
    },
    
    /**
     * 绑定事件处理
     */
    bindEvents: function() {
        // 表单提交
        $('#categoryForm').on('submit', (e) => {
            e.preventDefault();
            this.saveCategory();
        });
    },
    
    /**
     * 加载分类数据
     */
    loadCategoryData: function() {
        // 显示加载中状态
        $('#saveBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 加载中...');
        
        // 请求分类数据
        fetch(`${config.apiBaseUrl}/admin_get_recommended_category.php?id=${this.categoryId}`, {
            method: 'GET',
            headers: {
                'Authorization': getAdminToken()
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应不正常');
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '获取分类数据失败');
            }
            
            // 填充表单
            this.fillCategoryForm(data.data);
        })
        .catch(error => {
            console.error('加载分类数据失败:', error);
            this.showError(`加载分类数据失败: ${error.message}`);
        })
        .finally(() => {
            // 恢复按钮状态
            $('#saveBtn').prop('disabled', false).html('<i class="fa fa-save"></i> 保存');
        });
    },
    
    /**
     * 填充表单数据
     * @param {Object} category 分类数据
     */
    fillCategoryForm: function(category) {
        $('#categoryId').val(category.id);
        $('#categoryName').val(category.name);
        $('#categoryDescription').val(category.description);
        $('#sortOrder').val(category.sort_order);
        $('#categoryStatus').val(category.status);
        
        // 显示穿搭数量
        if (category.outfit_count !== undefined) {
            $('#outfitCount').text(category.outfit_count);
            $('#outfitCountContainer').show();
        }
    },
    
    /**
     * 收集表单数据
     * @returns {Object} 表单数据对象
     */
    collectFormData: function() {
        return {
            id: this.isNewMode ? null : $('#categoryId').val(),
            name: $('#categoryName').val().trim(),
            description: $('#categoryDescription').val().trim(),
            sort_order: parseInt($('#sortOrder').val()) || 0,
            status: parseInt($('#categoryStatus').val())
        };
    },
    
    /**
     * 验证表单数据
     * @param {Object} data 表单数据
     * @returns {boolean} 验证结果
     */
    validateForm: function(data) {
        // 验证分类名称
        if (!data.name) {
            this.showError('分类名称不能为空');
            $('#categoryName').focus();
            return false;
        }
        
        return true;
    },
    
    /**
     * 保存分类
     */
    saveCategory: function() {
        // 收集并验证表单数据
        const formData = this.collectFormData();
        if (!this.validateForm(formData)) {
            return;
        }
        
        // 设置按钮为加载状态
        $('#saveBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 保存中...');
        
        // 构建API请求
        const apiUrl = this.isNewMode
            ? `${config.apiBaseUrl}/admin_add_recommended_category.php`
            : `${config.apiBaseUrl}/admin_update_recommended_category.php`;
        
        // 发送请求
        fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': getAdminToken()
            },
            body: JSON.stringify(formData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应不正常');
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '保存分类失败');
            }
            
            // 保存成功
            this.showSuccess(this.isNewMode ? '分类添加成功' : '分类更新成功');
            
            // 如果是新增模式，重置表单或跳转到编辑模式
            if (this.isNewMode) {
                // 可以选择清空表单继续添加，或跳转到编辑页面
                if (confirm('分类添加成功，是否继续添加？')) {
                    $('#categoryForm')[0].reset();
                } else {
                    // 跳转到编辑页面
                    window.location.href = `recommended_category_edit.html?id=${data.data.id}`;
                }
            } else {
                // 更新模式下，刷新数据
                this.fillCategoryForm(data.data);
            }
        })
        .catch(error => {
            console.error('保存分类失败:', error);
            this.showError(`保存失败: ${error.message}`);
        })
        .finally(() => {
            // 恢复按钮状态
            $('#saveBtn').prop('disabled', false).html('<i class="fa fa-save"></i> 保存');
        });
    },
    
    /**
     * 显示成功消息
     * @param {string} message 消息内容
     */
    showSuccess: function(message) {
        $('#successAlert').text(message).fadeIn().delay(3000).fadeOut();
    },
    
    /**
     * 显示错误消息
     * @param {string} message 错误信息
     */
    showError: function(message) {
        $('#errorAlert').text(message).fadeIn().delay(5000).fadeOut();
    }
}; 