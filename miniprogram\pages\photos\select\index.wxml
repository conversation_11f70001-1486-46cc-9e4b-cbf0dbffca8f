<scroll-view class="page-scroll" scroll-y enable-flex>
  <view class="container">
    <view class="header">
      <view class="title">选择照片</view>
      <view class="subtitle">选择一张用于试穿的照片</view>
    </view>
    
    <view class="photos-grid" wx:if="{{photos.length > 0}}">
      <view wx:for="{{photos}}" wx:key="id" 
            class="photo-item {{selectedPhotoId === item.id ? 'selected' : ''}}"
            bindtap="selectPhoto"
            data-id="{{item.id}}">
        <view class="photo-container">
          <image 
            class="photo-image" 
            src="{{item.image_url}}" 
            mode="aspectFit"
            data-url="{{item.image_url}}"
            data-index="{{index}}"
            bindload="imageLoad"
            binderror="imageLoadError"></image>
          <view class="image-loading" wx:if="{{item.isLoading}}">
            <view class="loading-spinner"></view>
          </view>
          <view class="image-error" wx:if="{{item.loadError}}">
            <text>图片加载失败</text>
          </view>
        </view>
        <view class="photo-info">
          <view class="photo-type">{{item.photoType}}</view>
          <view class="photo-date">{{item.created_at}}</view>
        </view>
        <view class="select-mark" wx:if="{{selectedPhotoId === item.id}}">
          <view class="check-icon-container">✓</view>
        </view>
      </view>
      
      <view class="photo-item add-photo" bindtap="uploadPhoto">
        <view class="add-icon">+</view>
        <view class="add-text">上传新照片</view>
      </view>
    </view>
    
    <view class="empty-state" wx:elif="{{!loading}}">
      <image class="empty-icon" src="/images/icons/empty.png"></image>
      <text class="empty-text">暂无照片</text>
      <view class="upload-btn" bindtap="uploadPhoto">上传照片</view>
    </view>
    
    <view class="loading" wx:if="{{loading}}">
      <image class="loading-icon" src="/images/icons/loading.gif"></image>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</scroll-view>

<!-- 底部按钮 -->
<view class="bottom-bar">
  <view class="bottom-actions">
    <view class="nav-back-btn" bindtap="navigateBack">返回</view>
    <view class="proceed-btn {{canProceed ? 'active' : 'disabled'}}" bindtap="proceedToTryOn">
      <text class="next-icon">✨</text> 开始试穿
    </view>
  </view>
</view> 