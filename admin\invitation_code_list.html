<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邀请码管理 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .data-table th {
            background-color: #f5f5f5;
            padding: 12px 15px;
            text-align: left;
            font-weight: 500;
            color: #333;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .data-table tr:hover {
            background-color: #f9f9f9;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-unused {
            background-color: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        
        .status-used {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .status-expired {
            background-color: #fff1f0;
            color: #f5222d;
            border: 1px solid #ffa39e;
        }
        
        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        .pagination-item {
            min-width: 32px;
            height: 32px;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .pagination-item:hover, .pagination-item.active {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .pagination-item.disabled {
            color: rgba(0, 0, 0, 0.25);
            cursor: not-allowed;
        }
        
        /* 操作按钮样式 */
        .action-btn {
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            margin-right: 5px;
            border: none;
            color: white;
            transition: opacity 0.3s;
        }
        
        .action-btn:hover {
            opacity: 0.8;
        }
        
        .view-btn {
            background-color: #1890ff;
        }
        
        .delete-btn {
            background-color: #ff4d4f;
        }
        
        .generate-btn {
            background-color: #1890ff;
            color: white;
            padding: 8px 16px;
            font-size: 14px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        
        .generate-btn:hover {
            background-color: #40a9ff;
        }
        
        /* 过滤控件样式 */
        .filter-bar {
            display: flex;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .filter-item {
            margin-right: 15px;
        }
        
        .filter-label {
            margin-bottom: 5px;
            display: block;
            font-size: 14px;
            color: #555;
        }
        
        .filter-select, .filter-input {
            min-width: 150px;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .filter-btn {
            padding: 8px 16px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            height: 36px;
            margin-top: 22px;
        }
        
        .filter-btn:hover {
            background-color: #40a9ff;
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .modal.active {
            display: flex;
        }
        
        .modal-content {
            background-color: white;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
            padding: 20px;
            position: relative;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 500;
            color: #333;
        }
        
        .modal-close {
            font-size: 22px;
            color: #999;
            cursor: pointer;
            background: none;
            border: none;
        }
        
        .modal-body {
            margin-bottom: 20px;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            padding-top: 10px;
            border-top: 1px solid #f0f0f0;
        }
        
        .modal-footer button {
            padding: 6px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .btn-cancel {
            background-color: #f5f5f5;
            border: 1px solid #d9d9d9;
            color: #555;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
            border: none;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: #555;
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-control:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .loading-indicator {
            text-align: center;
            padding: 20px;
            display: none;
        }
        
        .error-message {
            background-color: #fff1f0;
            border: 1px solid #ffa39e;
            color: #f5222d;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>邀请码管理</h2>
                <div class="user-info">
                    <span id="userName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div id="loadingIndicator" class="loading-indicator">正在加载数据...</div>
            <div id="errorMessage" class="error-message"></div>
            
            <button id="generateCodeBtn" class="generate-btn">生成新邀请码</button>
            
            <div class="filter-bar">
                <div class="filter-item">
                    <label for="statusFilter" class="filter-label">状态</label>
                    <select id="statusFilter" class="filter-select">
                        <option value="">全部状态</option>
                        <option value="unused">未使用</option>
                        <option value="used">已使用</option>
                        <option value="expired">已过期</option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label for="codeFilter" class="filter-label">邀请码</label>
                    <input type="text" id="codeFilter" class="filter-input" placeholder="搜索邀请码">
                </div>
                
                <button id="filterBtn" class="filter-btn">筛选</button>
            </div>
            
            <table class="data-table" id="invitationCodeTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>邀请码</th>
                        <th>状态</th>
                        <th>类型</th>
                        <th>创建时间</th>
                        <th>过期时间</th>
                        <th>使用时间</th>
                        <th>创建者</th>
                        <th>使用者</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="invitationCodeList">
                    <!-- 邀请码数据将在这里动态加载 -->
                </tbody>
            </table>
            
            <div class="pagination" id="pagination">
                <!-- 分页控件将在这里动态加载 -->
            </div>
            
            <!-- 生成邀请码模态框 -->
            <div id="generateModal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">生成邀请码</h3>
                        <button class="modal-close" id="closeGenerateModal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="generateForm">
                            <div class="form-group">
                                <label for="codeQuantity" class="form-label">生成数量</label>
                                <input type="number" id="codeQuantity" class="form-control" min="1" max="100" value="1">
                            </div>
                            <div class="form-group">
                                <label for="expireDate" class="form-label">过期日期 (可选)</label>
                                <input type="date" id="expireDate" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="codeType" class="form-label">邀请码类型</label>
                                <select id="codeType" class="form-control">
                                    <option value="image_analysis">形象分析</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn-cancel" id="cancelGenerateBtn">取消</button>
                        <button class="btn-primary" id="confirmGenerateBtn">确认生成</button>
                    </div>
                </div>
            </div>
            
            <!-- 查看邀请码详情模态框 -->
            <div id="viewCodeModal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">邀请码详情</h3>
                        <button class="modal-close" id="closeViewCodeModal">&times;</button>
                    </div>
                    <div class="modal-body" id="codeDetails">
                        <!-- 详情内容将在这里动态加载 -->
                    </div>
                    <div class="modal-footer">
                        <button class="btn-primary" id="closeDetailBtn">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/invitation_code.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为invitation_code
            Sidebar.init('invitation_code');
            
            // 显示用户信息
            const userName = document.getElementById('userName');
            const logoutBtn = document.getElementById('logoutBtn');
            
            const user = Auth.getCurrentUser();
            if (user) {
                userName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            logoutBtn.addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化邀请码管理
            InvitationCode.init();
        });
    </script>
</body>
</html> 