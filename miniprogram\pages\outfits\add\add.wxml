<view class="container">
  <view class="form-container">
    <view class="form-title">创建新穿搭</view>
    
    <!-- 穿搭表单 -->
    <view class="form-group">
      <view class="form-label">名称</view>
      <input 
        class="form-input" 
        placeholder="请输入穿搭名称" 
        maxlength="20" 
        bindinput="onNameInput"
        value="{{name}}"
      />
    </view>
    
    <!-- 分类选择表单项 -->
    <view class="form-group">
      <view class="form-label">所属分类</view>
      <view class="category-select-container">
        <picker 
          bindchange="onCategoryChange" 
          value="{{categoryIndex}}" 
          range="{{categories}}" 
          range-key="name" 
          disabled="{{categories.length === 0}}"
          class="category-picker"
        >
          <view class="picker-value {{categories.length === 0 ? 'disabled' : ''}}">
            {{categories.length > 0 ? categories[categoryIndex].name : '暂无分类'}}
          </view>
        </picker>
        <view class="add-category-btn" bindtap="goToAddCategory">
          <text class="add-icon">+</text>
          <text class="add-text">新增分类</text>
        </view>
      </view>
    </view>
    
    <view class="form-group">
      <view class="form-label">描述 (可选)</view>
      <textarea 
        class="form-textarea" 
        placeholder="添加描述，如场合、风格等" 
        maxlength="100" 
        bindinput="onDescriptionInput"
        value="{{description}}"
      />
    </view>
    
    <!-- 表单提示 -->
    <view class="form-tips">
      <text>创建后可以添加衣物并调整位置</text>
    </view>
  </view>
  
  <!-- 底部按钮 -->
  <view class="bottom-buttons">
    <button class="cancel-btn" bindtap="cancel">取消</button>
    <button class="submit-btn" bindtap="createOutfit" disabled="{{submitting}}">创建并编辑</button>
  </view>
</view> 