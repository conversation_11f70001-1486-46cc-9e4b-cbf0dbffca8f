<view class="container">
  <!-- 衣橱信息提示 -->
  <view class="wardrobe-info">
    <view class="wardrobe-name">{{wardrobeName}}</view>
    <view class="wardrobe-hint">该衣橱内共有 {{clothingList.length}} 件衣物</view>
  </view>
  
  <!-- 分类标签栏 -->
  <scroll-view scroll-x="true" class="category-tabs">
    <view class="tabs-container">
      <view 
        class="tab-item {{currentCategory === item.code ? 'active' : ''}}" 
        wx:for="{{tabCategoryList}}" 
        wx:key="code"
        data-category="{{item.code}}" 
        bindtap="switchCategory"
      >
        {{item.name}}
      </view>
    </view>
  </scroll-view>
  
  <!-- 衣物列表 -->
  <scroll-view scroll-y="true" class="clothes-container" wx:if="{{!loading}}">
    <view class="clothes-grid layout-mode-4">
      <view wx:for="{{clothingList}}" wx:key="id" class="clothes-item {{item.selected ? 'selected' : ''}}" data-id="{{item.id}}" bindtap="toggleSelect">
        <image src="{{item.image_url}}" mode="aspectFit" class="clothes-img"></image>
        <!-- 显示衣物名称 -->
        <view class="clothes-name" wx:if="{{showClothingName && item.name}}">{{item.name}}</view>
        <view class="select-icon">
          <text class="check-icon">✓</text>
        </view>
      </view>
    </view>
    
    <!-- 空状态提示 -->
    <view class="empty-state" wx:if="{{clothingList.length === 0}}">
      <view class="empty-icon">👕</view>
      <view class="empty-text">该衣橱中暂无衣物</view>
      <button class="add-btn" bindtap="navigateToAdd">添加衣物</button>
    </view>
  </scroll-view>
  
  <!-- 加载提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
  
  <!-- 编辑工具栏 -->
  <view class="edit-toolbar" wx:if="{{hasSelected}}">
    <view class="edit-btn" bindtap="editSelected">
      <text class="icon-edit">✎</text> 编辑
    </view>
    <view class="move-btn" bindtap="showWardrobePicker">
      <text class="icon-move">↗</text> 移动
    </view>
    <view class="delete-btn" bindtap="deleteSelected">
      <text class="icon-delete">🗑</text> 删除
    </view>
  </view>
  
  <!-- 衣橱选择弹窗 -->
  <view class="wardrobe-picker-modal" wx:if="{{showWardrobePicker}}" bindtap="hideWardrobePicker">
    <view class="wardrobe-picker-content" catchtap="stopPropagation">
      <view class="wardrobe-picker-header">
        选择目标衣橱
        <view class="wardrobe-picker-close" bindtap="hideWardrobePicker">
          <icon type="clear" size="20" color="#666666"></icon>
        </view>
      </view>
      <view class="wardrobe-picker-body">
        <block wx:if="{{loadingWardrobes}}">
          <view class="loading-wardrobes">
            <text class="iconfont icon-loading"></text>
            <text>加载中...</text>
          </view>
        </block>
        <block wx:elif="{{wardrobeList.length === 0}}">
          <view class="no-wardrobes">暂无其他衣橱可选</view>
        </block>
        <block wx:else>
          <view 
            class="wardrobe-item" 
            wx:for="{{wardrobeList}}" 
            wx:key="id"
            data-id="{{item.id}}"
            data-name="{{item.name}}"
            bindtap="selectWardrobe"
          >
            <text class="wardrobe-name">{{item.name}}</text>
            <text class="wardrobe-count">{{item.clothes_count}}件衣物</text>
          </view>
        </block>
      </view>
    </view>
  </view>
  
  <!-- 分类选择弹窗 -->
  <view class="category-picker-modal" wx:if="{{showCategoryPicker}}" bindtap="hideCategoryPicker">
    <view class="category-picker-content" catchtap="stopPropagation">
      <view class="category-picker-header">
        选择目标分类
        <view class="category-picker-close" bindtap="hideCategoryPicker">
          <icon type="clear" size="20" color="#666666"></icon>
        </view>
      </view>
      <view class="category-picker-body">
        <block wx:if="{{loadingCategories}}">
          <view class="loading-categories">
            <text class="iconfont icon-loading"></text>
            <text>加载中...</text>
          </view>
        </block>
        <block wx:elif="{{categoryList.length === 0}}">
          <view class="no-categories">暂无分类可选</view>
        </block>
        <block wx:else>
          <view 
            class="category-item" 
            wx:for="{{categoryList}}" 
            wx:key="id"
            data-code="{{item.code}}"
            data-name="{{item.name}}"
            bindtap="selectCategory"
          >
            <text class="category-name">{{item.name}}</text>
            <text class="category-tag" wx:if="{{item.is_system}}">系统</text>
            <text class="category-tag custom-tag" wx:else>自定义</text>
          </view>
        </block>
      </view>
    </view>
  </view>
  
  <!-- 底部操作按钮 -->
  <view class="bottom-btn-container" wx:if="{{!hasSelected}}">
    <button class="add-clothing-btn" bindtap="navigateToAdd">添加衣物到此衣橱</button>
  </view>
</view> 