<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';
require_once '../vendor/autoload.php'; // 引入阿里云OSS SDK
require_once 'oss_helper.php';

// 引入OSS命名空间
use OSS\OssClient;
use OSS\Core\OssException;

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证管理员身份
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 获取POST数据
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

// 检查必要参数
if (!isset($input['user_id']) || empty($input['user_id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少必要参数: user_id']);
    exit();
}

$userId = (int)$input['user_id'];

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

try {
    // 开始事务
    $conn->beginTransaction();
    
    // 初始化OSS辅助类
    $ossHelper = new OssHelper();
    
    // 首先获取用户所有衣物的图片URL
    $selectQuery = "SELECT id, image_url FROM clothes WHERE user_id = :user_id";
    $selectStmt = $conn->prepare($selectQuery);
    $selectStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
    $selectStmt->execute();
    
    $clothes = $selectStmt->fetchAll(PDO::FETCH_ASSOC);
    $deletedCount = 0;
    $ossDeletedCount = 0;
    
    // 尝试从OSS中删除图片
    foreach ($clothes as $clothing) {
        $imageUrl = $clothing['image_url'];
        
        // 检查是否为OSS URL，如果是则删除OSS对象
        if ($ossHelper->isOssUrl($imageUrl)) {
            $ossKey = $ossHelper->getKeyFromUrl($imageUrl);
            if ($ossKey) {
                $deleteResult = $ossHelper->deleteFile($ossKey);
                if ($deleteResult['success']) {
                    $ossDeletedCount++;
                    error_log("OSS文件已删除: $ossKey");
                } else {
                    error_log("OSS文件删除失败: " . $deleteResult['error']);
                }
            }
        }
    }
    
    // 删除数据库中的衣物记录
    $deleteQuery = "DELETE FROM clothes WHERE user_id = :user_id";
    $deleteStmt = $conn->prepare($deleteQuery);
    $deleteStmt->bindValue(':user_id', $userId, PDO::PARAM_INT);
    $deleteStmt->execute();
    
    $deletedCount = $deleteStmt->rowCount();
    
    // 提交事务
    $conn->commit();
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'msg' => "已成功删除用户 $userId 的所有衣物，共 $deletedCount 件，其中 $ossDeletedCount 张图片从OSS中删除",
        'deleted_count' => $deletedCount,
        'oss_deleted_count' => $ossDeletedCount
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    // 记录错误
    error_log("清空用户衣物失败: " . $e->getMessage());
    
    // 返回错误响应
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '操作失败: ' . $e->getMessage()
    ]);
} 