<?php
/**
 * 设置淘宝商品推荐状态接口
 * 
 * 该接口用于设置或取消淘宝联盟商品的推荐状态，推荐的商品可在前端特殊位置展示。
 * 只有管理员用户有权限调用此接口。
 * 
 * 请求方式：POST
 * 接口路径：/login_backend/set_product_recommend.php
 * 权限要求：必须是管理员用户
 * 
 * 请求参数（JSON格式）：
 * - product_id: 必填，商品ID，整数类型
 * - is_recommend: 必填，推荐状态，1表示推荐，0表示取消推荐
 * 
 * 请求头要求：
 * - Content-Type: application/json
 * - Authorization: Bearer {管理员令牌}
 * 
 * 返回数据：
 * - 成功时:
 *   {
 *     "error": false,
 *     "msg": "成功将商品设为推荐" 或 "已取消商品推荐",
 *     "product_id": 商品ID,
 *     "is_recommend": 推荐状态(1或0)
 *   }
 * - 失败时:
 *   {
 *     "error": true,
 *     "msg": "错误信息"
 *   }
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

require_once 'config.php';
require_once 'auth.php';

// 验证管理员Token
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION']) && !empty($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
    
    $auth = new Auth();
    $payload = $auth->verifyAdminToken($token);
    
    if (!$payload) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => '无效的管理员授权']);
        exit();
    }
}

// 接收POST数据
$input = json_decode(file_get_contents('php://input'), true);

// 验证必填参数
if (!isset($input['product_id']) || !isset($input['is_recommend'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少必要参数']);
    exit();
}

$productId = (int)$input['product_id'];
$isRecommend = (int)$input['is_recommend'];

// 验证参数有效性
if ($productId <= 0) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '无效的商品ID']);
    exit();
}

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查询商品是否存在
    $checkStmt = $conn->prepare("SELECT id, title FROM taobao_products WHERE id = :id");
    $checkStmt->bindParam(':id', $productId);
    $checkStmt->execute();
    
    $product = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        throw new Exception('商品不存在');
    }
    
    // 更新商品推荐状态
    $updateStmt = $conn->prepare("UPDATE taobao_products SET is_recommend = :is_recommend, updated_at = CURRENT_TIMESTAMP WHERE id = :id");
    $updateStmt->bindParam(':id', $productId);
    $updateStmt->bindParam(':is_recommend', $isRecommend);
    $updateStmt->execute();
    
    // 检查是否真的更新了
    if ($updateStmt->rowCount() === 0) {
        throw new Exception('更新推荐状态失败，可能是商品已被删除');
    }
    
    // 返回成功信息
    echo json_encode([
        'error' => false,
        'msg' => ($isRecommend === 1 ? '成功将商品设为推荐' : '已取消商品推荐') . ': ' . $product['title'],
        'product_id' => $productId,
        'is_recommend' => $isRecommend
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '设置推荐状态失败: ' . $e->getMessage()
    ]);
} 