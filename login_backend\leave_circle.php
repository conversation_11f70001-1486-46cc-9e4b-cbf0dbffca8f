<?php
// 退出圈子API
// 模块2：圈子成员管理模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查找用户所在的圈子
    $findCircleSql = "SELECT cm.id as member_id, cm.circle_id, cm.role, c.name as circle_name, c.creator_id
                      FROM circle_members cm 
                      JOIN outfit_circles c ON cm.circle_id = c.id 
                      WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
    $findCircleStmt = $conn->prepare($findCircleSql);
    $findCircleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $findCircleStmt->execute();
    
    $userCircle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$userCircle) {
        echo json_encode([
            'status' => 'error',
            'message' => '您当前未加入任何圈子'
        ]);
        exit;
    }
    
    // 检查是否是创建者
    if ($userCircle['role'] === 'creator') {
        echo json_encode([
            'status' => 'error',
            'message' => '创建者不能退出圈子，请先解散圈子或转让创建者权限'
        ]);
        exit;
    }
    
    // 开始事务
    $conn->beginTransaction();
    
    // 将成员状态设置为已移除
    $removeMemberSql = "UPDATE circle_members 
                        SET status = 'removed', removed_at = NOW(), removed_by = :user_id 
                        WHERE id = :member_id";
    $removeMemberStmt = $conn->prepare($removeMemberSql);
    $removeMemberStmt->bindParam(':member_id', $userCircle['member_id'], PDO::PARAM_INT);
    $removeMemberStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $removeMemberStmt->execute();
    
    // 更新圈子成员数量
    $updateCountSql = "UPDATE outfit_circles 
                       SET member_count = (
                           SELECT COUNT(*) FROM circle_members 
                           WHERE circle_id = :circle_id AND status = 'active'
                       ) 
                       WHERE id = :circle_id";
    $updateCountStmt = $conn->prepare($updateCountSql);
    $updateCountStmt->bindParam(':circle_id', $userCircle['circle_id'], PDO::PARAM_INT);
    $updateCountStmt->execute();
    
    // 提交事务
    $conn->commit();
    
    echo json_encode([
        'status' => 'success',
        'message' => '已成功退出圈子"' . $userCircle['circle_name'] . '"'
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if (isset($conn)) {
        $conn->rollBack();
    }
    
    echo json_encode([
        'status' => 'error',
        'message' => '退出圈子失败：' . $e->getMessage()
    ]);
}
?>
