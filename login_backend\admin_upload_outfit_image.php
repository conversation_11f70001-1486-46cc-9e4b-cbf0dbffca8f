<?php
/**
 * 管理后台图片上传API
 * 
 * 专门用于管理后台上传推荐穿搭图片
 */

// 关闭错误显示，记录到日志
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 创建日志函数
function writeLog($message) {
    $logDir = __DIR__ . '/logs';
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    $logFile = $logDir . '/admin_upload.log';
    file_put_contents($logFile, date('[Y-m-d H:i:s] ') . $message . "\n", FILE_APPEND);
    error_log($message);
}

writeLog("===== 开始处理管理后台图片上传请求 =====");

// 加载配置和验证文件
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '只允许POST请求']);
    exit;
}

// 验证Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '未提供授权令牌']);
    exit;
}

// 验证管理员令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
$auth = new Auth();
$adminData = $auth->verifyAdminToken($token);

if (!$adminData) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit;
}

// 验证是否上传了文件
if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '未上传图片或上传过程中出错']);
    exit;
}

// 验证文件类型
$allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
$fileType = $_FILES['image']['type'];

if (!in_array($fileType, $allowedTypes)) {
    http_response_code(400);
    echo json_encode([
        'error' => true,
        'msg' => '不支持的文件类型，仅支持JPEG、PNG、GIF和WEBP'
    ]);
    exit;
}

// 创建上传目录
$uploadDir = 'uploads/outfits/';
if (!file_exists($uploadDir)) {
    if (!mkdir($uploadDir, 0755, true)) {
        writeLog("错误: 无法创建上传目录: " . $uploadDir);
        http_response_code(500);
        echo json_encode(['error' => true, 'msg' => '服务器配置错误：无法创建上传目录']);
        exit;
    }
    writeLog("已创建上传目录: " . $uploadDir);
}

// 检查目录是否可写
if (!is_writable($uploadDir)) {
    writeLog("错误: 上传目录不可写: " . $uploadDir);
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '服务器配置错误：上传目录不可写']);
    exit;
}

// 生成唯一文件名
$extension = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
$filename = 'outfit_' . $adminData['admin_id'] . '_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
$targetPath = $uploadDir . $filename;

// 移动上传的文件到目标路径
if (move_uploaded_file($_FILES['image']['tmp_name'], $targetPath)) {
    writeLog("文件上传成功: " . $targetPath);
    
    // 尝试上传到OSS
    try {
        // 使用指定的SDK路径
        $autoloadPath = '/www/wwwroot/cyyg.alidog.cn/vendor/aliyuncs/oss-sdk-php/autoload.php';
        if (file_exists($autoloadPath)) {
            require_once $autoloadPath;
            writeLog("已加载OSS SDK: " . $autoloadPath);
        } else {
            writeLog("警告: 找不到OSS SDK，将使用本地URL");
            throw new Exception("OSS SDK not found");
        }
        
        require_once 'oss_helper.php';
        $ossHelper = new OssHelper();
        
        // 生成OSS路径
        $ossKey = OSS_PATH_CLOTHES . $filename;
        
        // 上传文件到OSS
        $uploadResult = $ossHelper->uploadFile($targetPath, $ossKey);
        
        if ($uploadResult['success']) {
            writeLog("文件已成功上传到OSS: " . $uploadResult['url']);
            
            // 返回成功响应，使用OSS URL
            echo json_encode([
                'error' => false,
                'data' => [
                    'image_url' => $uploadResult['url']
                ]
            ]);
            exit;
        } else {
            writeLog("上传到OSS失败，使用本地URL: " . $uploadResult['error']);
        }
    } catch (Exception $e) {
        writeLog("OSS上传异常: " . $e->getMessage() . "，使用本地URL");
    }
    
    // 如果OSS上传失败，使用本地URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];
    $baseUrl = $protocol . $host;
    $baseUrl = rtrim($baseUrl, '/') . '/';
    $imageUrl = $baseUrl . 'login_backend/' . $targetPath;
    
    writeLog("使用本地URL: " . $imageUrl);
    
    echo json_encode([
        'error' => false,
        'data' => [
            'image_url' => $imageUrl
        ]
    ]);
} else {
    writeLog("文件上传失败");
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '保存上传文件失败'
    ]);
}

writeLog("===== 图片上传处理完成 ====="); 