/* 容器样式 */
.container {
  padding: 0;
  width: 100%;
  min-height: 100vh;
  background-color: #f6f7fb;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 未登录状态样式 */
.empty-state {
  width: 100%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30px;
  box-sizing: border-box;
}

.empty-icon {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
}

.empty-image {
  width: 100%;
  height: 100%;
}

.empty-text {
  font-size: 16px;
  color: #333;
  text-align: center;
  margin-bottom: 10px;
}

.text-sm {
  font-size: 14px;
  color: #999;
}

.login-btn {
  margin-top: 30px;
  background-color: #1aad19;
  color: #fff;
  border-radius: 30px;
  font-size: 16px;
  padding: 8px 30px;
}

/* 头部样式 */
.header {
  width: 100%;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  margin-bottom: 10px;
}

.header-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.header-desc {
  font-size: 14px;
  color: #999;
}

/* 输入区域样式 */
.input-container {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.input-wrapper {
  width: 100%;
  position: relative;
  margin-bottom: 15px;
}

.preference-input {
  width: 100%;
  height: 50px;
  background-color: #fff;
  border-radius: 8px;
  padding: 0 40px 0 15px;
  font-size: 16px;
  box-sizing: border-box;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.clear-btn {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #999;
  z-index: 10;
}

/* 历史记录样式 */
.history-section {
  width: 100%;
  margin-bottom: 20px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.history-title {
  font-size: 14px;
  color: #666;
}

.history-arrow {
  font-size: 14px;
  color: #999;
}

.history-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

.history-item {
  background-color: #f0f0f0;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
  margin-right: 8px;
  margin-bottom: 8px;
}

/* 生成按钮样式 */
.generate-btn {
  width: 100%;
  height: 50px;
  background-color: #e0e0e0;
  color: #999;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin: 10px 0 20px;
  transition: all 0.3s;
}

.generate-btn.active {
  background-color: #000;
  color: #fff;
}

/* 提示信息样式 */
.tips-container {
  width: 100%;
  margin-top: 20px;
}

.tip-item {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
}

/* 偏好卡片样式 */
.preference-card {
  width: calc(100% - 30px);
  padding: 15px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin: 0 15px 10px 15px;
  border-radius: 12px;
  box-sizing: border-box;
}

.preference-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.preference-content {
  font-size: 18px;
  color: #333;
  font-weight: 500;
}

/* 穿搭卡片样式 */
.outfit-card {
  width: calc(100% - 30px);
  padding: 15px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin: 0 15px 15px 15px;
  border-radius: 12px;
  box-sizing: border-box;
}

.outfit-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

/* 穿搭项样式 */
.outfit-item {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.outfit-item:last-child {
  border-bottom: none;
}

.outfit-item-image-container {
  width: 80px;
  height: 80px;
  margin-right: 15px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f9f9f9;
}

.outfit-item-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.outfit-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.outfit-item-category {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
}

.outfit-item-name {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
  font-weight: 500;
}

.outfit-item-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.outfit-item-reason {
  font-size: 12px;
  color: #727272;
  line-height: 1.5;
}

/* 穿搭总结样式 */
.outfit-summary {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.summary-title {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  margin-bottom: 8px;
}

.summary-content {
  font-size: 14px;
  color: #333;
  line-height: 1.6;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
  margin-bottom: 15px;
}

.action-btn {
  flex: 1;
  height: 40px;
  font-size: 14px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 5px;
  background-color: #fff;
  border: 1px solid #ddd;
}

.refresh-btn {
  color: #333;
}

.save-btn {
  background-color: #000;
  color: #fff;
  border: none;
}

.refreshing {
  opacity: 0.7;
}

/* 返回按钮样式 */
.back-button {
  width: 100%;
  padding: 12px 0;
  text-align: center;
  font-size: 14px;
  color: #666;
  margin-top: 10px;
}

/* 穿搭推荐提示样式 */
.recommendation-tips {
  padding: 15px;
  font-size: 12px;
  color: #999;
  line-height: 1.6;
}

/* 加载遮罩样式 - 轻量级白色版本 */
.loading-overlay-light {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.loading-icon {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

.loading-text {
  font-size: 14px;
  color: #333;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 分享提示弹窗样式 */
.share-tip-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.share-tip-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.share-tip-content {
  position: relative;
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 50rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

.share-tip-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.share-tip-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.share-tip-message {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
  line-height: 1.6;
}

.share-tip-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.share-tip-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0 15rpx;
  padding: 0;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.share-btn {
  background-color: #000;
  color: #fff;
}

/* 剩余次数提示样式 */
.quota-tip {
  text-align: center;
  font-size: 26rpx;
  color: #999;
  margin-top: 20rpx;
  margin-bottom: 10rpx;
}

.quota-number {
  color: #f04142;
  font-weight: 600;
  margin: 0 4rpx;
} 