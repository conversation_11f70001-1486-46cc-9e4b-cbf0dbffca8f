/**
 * 穿搭详情页面的JavaScript逻辑
 * 用于加载单个穿搭的详细信息
 */

// 全局变量
let outfitId = null;
const outfitContainer = document.getElementById('outfitContainer');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 从URL获取穿搭ID
    const urlParams = new URLSearchParams(window.location.search);
    outfitId = urlParams.get('id');
    
    if (!outfitId) {
        showError('穿搭ID不能为空');
        return;
    }
    
    // 加载穿搭详情
    loadOutfitDetails();
});

/**
 * 加载穿搭详情
 */
function loadOutfitDetails() {
    // 显示加载中
    outfitContainer.innerHTML = '<div class="loading">加载中...</div>';
    
    // 构建API URL
    const apiUrl = `../login_backend/admin_get_outfits.php?id=${outfitId}`;
    
    // 发起请求
    fetch(apiUrl, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + localStorage.getItem('admin_token')
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络请求失败');
        }
        return response.json();
    })
    .then(data => {
        // 处理响应数据
        if (data.status === 'success' && data.data.outfits && data.data.outfits.length > 0) {
            // 渲染穿搭详情
            renderOutfitDetails(data.data.outfits[0]);
            
            // 加载穿搭中的衣物
            loadOutfitClothes(data.data.outfits[0].id);
        } else {
            showError(data.message || '穿搭不存在或已被删除');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('加载穿搭详情失败: ' + error.message);
    });
}

/**
 * 加载穿搭中的衣物
 * @param {string} outfitId 穿搭ID
 */
function loadOutfitClothes(outfitId) {
    // 构建API URL
    const apiUrl = `../login_backend/admin_get_outfit_clothes.php?outfit_id=${outfitId}`;
    
    // 发起请求
    fetch(apiUrl, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + localStorage.getItem('admin_token')
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络请求失败');
        }
        return response.json();
    })
    .then(data => {
        // 处理响应数据
        if (data.status === 'success') {
            // 渲染衣物列表
            renderOutfitClothes(data.data.clothes || []);
        } else {
            // 显示空衣物列表
            renderOutfitClothes([]);
            console.warn('加载穿搭衣物失败:', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // 显示空衣物列表，但在控制台记录错误
        renderOutfitClothes([]);
        console.error('加载穿搭衣物失败:', error);
    });
}

/**
 * 渲染穿搭详情
 * @param {Object} outfit 穿搭数据对象
 */
function renderOutfitDetails(outfit) {
    // 清空容器
    outfitContainer.innerHTML = '';
    
    // 创建图片轮播或网格
    let imagesHTML = '';
    if (outfit.image_urls && outfit.image_urls.length > 0) {
        imagesHTML = `
            <div class="outfit-images-slider">
                <div class="main-image-container">
                    <img src="${outfit.image_urls[0]}" alt="${escapeHtml(outfit.name)}" class="main-image outfit-main-img" id="mainImage">
                </div>
                
                <div class="thumbnails-container">
                    ${outfit.image_urls.map((url, index) => 
                        `<img src="${url}" alt="预览图${index+1}" class="thumbnail-image outfit-thumbnail ${index === 0 ? 'active' : ''}" 
                              onclick="changeMainImage('${url}', this)">`
                    ).join('')}
                </div>
            </div>
        `;
    } else {
        imagesHTML = `
            <div class="outfit-image-container">
                <img src="img/default-outfit.png" alt="${escapeHtml(outfit.name)}" class="outfit-image">
            </div>
        `;
    }
    
    // 创建穿搭详情HTML
    const outfitHTML = `
        <div class="outfit-main">
            ${imagesHTML}
            
            <div class="outfit-info">
                <h3>${escapeHtml(outfit.name || '未命名穿搭')}</h3>
                
                <div class="info-item">
                    <span class="info-label">穿搭ID：</span>
                    <span class="info-value">${outfit.id}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">用户ID：</span>
                    <span class="info-value">${outfit.user_id}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">分类：</span>
                    <span class="info-value">${escapeHtml(outfit.category_name || '未分类')}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">描述：</span>
                    <span class="info-value">${escapeHtml(outfit.description || '无描述')}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">创建时间：</span>
                    <span class="info-value">${formatDate(outfit.created_at)}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">衣物数量：</span>
                    <span class="info-value">${outfit.clothes_count || 0}</span>
                </div>
            </div>
        </div>
        
        <div class="clothes-container">
            <h3>穿搭包含的衣物</h3>
            <div id="clothesGrid" class="clothes-grid">
                <div class="loading">加载中...</div>
            </div>
        </div>
    `;
    
    // 设置HTML内容
    outfitContainer.innerHTML = outfitHTML;
    
    // 绑定图片查看器到主图和缩略图
    setTimeout(() => {
        if (typeof ImageViewer !== 'undefined') {
            ImageViewer.bindImages('.main-image, .thumbnail-image');
            console.log('已为穿搭详情主图和缩略图绑定查看器');
        }
    }, 300);
}

/**
 * 渲染穿搭中的衣物
 * @param {Array} clothes 衣物数据数组
 */
function renderOutfitClothes(clothes) {
    const clothesGrid = document.getElementById('clothesGrid');
    
    if (!clothesGrid) {
        console.error('找不到衣物网格容器');
        return;
    }
    
    if (!clothes || clothes.length === 0) {
        clothesGrid.innerHTML = '<div class="no-data">该穿搭暂无衣物</div>';
        return;
    }
    
    // 清空网格
    clothesGrid.innerHTML = '';
    
    // 渲染每个衣物项
    clothes.forEach(cloth => {
        const clothItem = document.createElement('div');
        clothItem.className = 'clothes-item';
        
        clothItem.innerHTML = `
            <div class="clothes-image">
                <img src="${cloth.image_url || 'img/default-clothing.png'}" alt="${escapeHtml(cloth.name)}" class="outfit-img">
            </div>
            <div class="clothes-info">
                <div class="clothes-name">${escapeHtml(cloth.name || '未命名衣物')}</div>
                <div class="clothes-type">${escapeHtml(cloth.type || '未知类型')}</div>
            </div>
        `;
        
        clothesGrid.appendChild(clothItem);
    });
    
    // 绑定图片查看器到衣物图片
    setTimeout(() => {
        if (typeof ImageViewer !== 'undefined') {
            ImageViewer.bindImages('.clothes-image img');
            console.log('已为穿搭衣物图片绑定查看器');
        }
    }, 300);
}

/**
 * 显示错误信息
 * @param {string} message 错误信息
 */
function showError(message) {
    outfitContainer.innerHTML = `<div class="error">${message}</div>`;
}

/**
 * 格式化日期
 * @param {string} dateString 日期字符串
 * @returns {string} 格式化后的日期
 */
function formatDate(dateString) {
    if (!dateString) return '未知';
    
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * HTML转义
 * @param {string} str 要转义的字符串
 * @returns {string} 转义后的字符串
 */
function escapeHtml(str) {
    if (!str) return '';
    return str
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
}

/**
 * 切换主图显示
 * @param {string} imageUrl 图片URL
 * @param {HTMLElement} thumbElement 缩略图元素
 */
window.changeMainImage = function(imageUrl, thumbElement) {
    // 更新主图
    const mainImage = document.getElementById('mainImage');
    if (mainImage) {
        mainImage.src = imageUrl;
    }
    
    // 更新缩略图激活状态
    const thumbs = document.querySelectorAll('.thumbnail-image');
    thumbs.forEach(thumb => thumb.classList.remove('active'));
    if (thumbElement) {
        thumbElement.classList.add('active');
    }
}; 