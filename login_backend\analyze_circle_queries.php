<?php
// 分析圈子查询问题的专用API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';
require_once 'Logger.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode(['status' => 'error', 'message' => '缺少授权头']);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode(['status' => 'error', 'message' => '无效或已过期的令牌']);
    exit;
}

$userId = $payload['sub'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 1. 获取用户圈子信息
    $findCircleSql = "SELECT cm.circle_id, cm.role, c.name as circle_name
                      FROM circle_members cm 
                      JOIN outfit_circles c ON cm.circle_id = c.id 
                      WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
    $findCircleStmt = $conn->prepare($findCircleSql);
    $findCircleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $findCircleStmt->execute();
    $userCircle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$userCircle) {
        echo json_encode(['status' => 'error', 'message' => '用户未加入圈子']);
        exit;
    }
    
    $circleId = $userCircle['circle_id'];
    
    // 2. 分析衣物查询问题
    $clothesAnalysis = [];
    
    // 2.1 统计查询（这个有数据）
    $categoryStatsSql = "SELECT c.category, COUNT(*) as count
                        FROM clothes c
                        JOIN users u ON c.user_id = u.id
                        WHERE (c.circle_id = :circle_id OR 
                               (c.user_id IN (SELECT user_id FROM circle_members 
                                             WHERE circle_id = :circle_id AND status = 'active') 
                                AND c.circle_id IS NULL))
                        GROUP BY c.category
                        ORDER BY count DESC";
    $categoryStatsStmt = $conn->prepare($categoryStatsSql);
    $categoryStatsStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $categoryStatsStmt->execute();
    $categoryStats = $categoryStatsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 2.2 主查询（这个没数据）
    $mainQuerySql = "SELECT c.id, c.name, c.category, c.user_id, c.circle_id,
                            u.nickname as creator_nickname
                     FROM clothes c
                     JOIN users u ON c.user_id = u.id
                     WHERE (c.circle_id = :circle_id OR 
                            (c.user_id IN (SELECT user_id FROM circle_members 
                                          WHERE circle_id = :circle_id AND status = 'active') 
                             AND c.circle_id IS NULL))
                     LIMIT 5";
    $mainQueryStmt = $conn->prepare($mainQuerySql);
    $mainQueryStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $mainQueryStmt->execute();
    $mainQueryResults = $mainQueryStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 2.3 分步测试查询
    $stepTests = [];
    
    // 测试1：只查询圈子共享数据
    $test1Sql = "SELECT COUNT(*) as count FROM clothes c JOIN users u ON c.user_id = u.id WHERE c.circle_id = :circle_id";
    $test1Stmt = $conn->prepare($test1Sql);
    $test1Stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $test1Stmt->execute();
    $stepTests['circle_shared_clothes'] = $test1Stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // 测试2：查询圈子成员
    $test2Sql = "SELECT user_id, status FROM circle_members WHERE circle_id = :circle_id";
    $test2Stmt = $conn->prepare($test2Sql);
    $test2Stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $test2Stmt->execute();
    $circleMembers = $test2Stmt->fetchAll(PDO::FETCH_ASSOC);
    $stepTests['circle_members'] = $circleMembers;
    
    // 测试3：查询圈子成员的个人衣物
    $memberIds = array_column($circleMembers, 'user_id');
    if (!empty($memberIds)) {
        $memberIdsStr = implode(',', $memberIds);
        $test3Sql = "SELECT COUNT(*) as count FROM clothes c JOIN users u ON c.user_id = u.id 
                     WHERE c.user_id IN ($memberIdsStr) AND c.circle_id IS NULL";
        $test3Stmt = $conn->prepare($test3Sql);
        $test3Stmt->execute();
        $stepTests['members_personal_clothes'] = $test3Stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // 测试4：查询每个成员的衣物数量
        $memberClothes = [];
        foreach ($memberIds as $memberId) {
            $test4Sql = "SELECT COUNT(*) as count FROM clothes c JOIN users u ON c.user_id = u.id 
                         WHERE c.user_id = :user_id";
            $test4Stmt = $conn->prepare($test4Sql);
            $test4Stmt->bindParam(':user_id', $memberId, PDO::PARAM_INT);
            $test4Stmt->execute();
            $memberClothes[$memberId] = $test4Stmt->fetch(PDO::FETCH_ASSOC)['count'];
        }
        $stepTests['member_clothes_detail'] = $memberClothes;
    }
    
    // 测试5：不使用JOIN的统计查询
    $test5Sql = "SELECT c.category, COUNT(*) as count
                FROM clothes c
                WHERE (c.circle_id = :circle_id OR 
                       (c.user_id IN (SELECT user_id FROM circle_members 
                                     WHERE circle_id = :circle_id AND status = 'active') 
                        AND c.circle_id IS NULL))
                GROUP BY c.category";
    $test5Stmt = $conn->prepare($test5Sql);
    $test5Stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $test5Stmt->execute();
    $noJoinStats = $test5Stmt->fetchAll(PDO::FETCH_ASSOC);
    $stepTests['no_join_category_stats'] = $noJoinStats;
    
    $clothesAnalysis = [
        'category_stats_with_join' => $categoryStats,
        'main_query_results' => $mainQueryResults,
        'main_query_count' => count($mainQueryResults),
        'step_tests' => $stepTests
    ];
    
    // 3. 分析穿搭查询问题（类似的分析）
    $outfitsAnalysis = [];
    
    // 穿搭统计查询
    $outfitCategoryStatsSql = "SELECT o.category, COUNT(*) as count
                              FROM outfits o
                              JOIN users u ON o.user_id = u.id
                              WHERE (o.circle_id = :circle_id OR 
                                     (o.user_id IN (SELECT user_id FROM circle_members 
                                                   WHERE circle_id = :circle_id AND status = 'active') 
                                      AND o.circle_id IS NULL))
                              GROUP BY o.category";
    $outfitCategoryStatsStmt = $conn->prepare($outfitCategoryStatsSql);
    $outfitCategoryStatsStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $outfitCategoryStatsStmt->execute();
    $outfitCategoryStats = $outfitCategoryStatsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 穿搭主查询
    $outfitMainQuerySql = "SELECT o.id, o.name, o.category, o.user_id, o.circle_id,
                                  u.nickname as creator_nickname
                           FROM outfits o
                           JOIN users u ON o.user_id = u.id
                           WHERE (o.circle_id = :circle_id OR 
                                  (o.user_id IN (SELECT user_id FROM circle_members 
                                                WHERE circle_id = :circle_id AND status = 'active') 
                                   AND o.circle_id IS NULL))
                           LIMIT 5";
    $outfitMainQueryStmt = $conn->prepare($outfitMainQuerySql);
    $outfitMainQueryStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $outfitMainQueryStmt->execute();
    $outfitMainQueryResults = $outfitMainQueryStmt->fetchAll(PDO::FETCH_ASSOC);
    
    $outfitsAnalysis = [
        'category_stats' => $outfitCategoryStats,
        'main_query_results' => $outfitMainQueryResults,
        'main_query_count' => count($outfitMainQueryResults)
    ];
    
    // 记录分析结果
    Logger::debug('圈子查询分析完成', [
        'user_id' => $userId,
        'circle_id' => $circleId,
        'clothes_analysis' => $clothesAnalysis,
        'outfits_analysis' => $outfitsAnalysis
    ], 'query_analysis');
    
    echo json_encode([
        'status' => 'success',
        'data' => [
            'user_info' => [
                'user_id' => $userId,
                'circle_id' => $circleId,
                'circle_name' => $userCircle['circle_name'],
                'user_role' => $userCircle['role']
            ],
            'clothes_analysis' => $clothesAnalysis,
            'outfits_analysis' => $outfitsAnalysis
        ]
    ]);
    
} catch (Exception $e) {
    Logger::error('查询分析失败', [
        'user_id' => $userId ?? null,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ], 'query_analysis');
    
    echo json_encode([
        'status' => 'error',
        'message' => '查询分析失败：' . $e->getMessage()
    ]);
}
?>
