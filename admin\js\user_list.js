/**
 * 用户管理列表脚本
 */
const UserList = {
    // 初始化参数
    page: 1,
    perPage: 10,
    search: '',
    sortBy: 'id',
    sortOrder: 'desc',
    status: '', // 添加状态筛选参数
    
    // DOM元素
    elements: {},
    
    // 用户衣物数量缓存
    clothesCountCache: {},
    
    // 当前用户数据
    currentUsers: [],
    
    // 所有用户数据（用于排行）
    allUsers: [],
    
    // 是否正在加载全部用户
    isLoadingAllUsers: false,
    
    /**
     * 初始化
     */
    init: function() {
        // 初始化DOM元素引用
        this.elements = {
            userTableBody: document.getElementById('userTableBody'),
            pagination: document.getElementById('pagination'),
            pageInfo: document.getElementById('pageInfo'),
            prevBtn: document.getElementById('prevBtn'),
            nextBtn: document.getElementById('nextBtn'),
            searchInput: document.getElementById('searchInput'),
            searchBtn: document.getElementById('searchBtn'),
            statusFilter: document.getElementById('statusFilter'), // 添加状态筛选器引用
            sortableHeaders: document.querySelectorAll('.sortable'), // 获取所有可排序的表头
            rankingModal: document.getElementById('rankingModal'),
            rankingModalBody: document.getElementById('rankingModalBody'),
            showRankingBtn: document.getElementById('showRankingBtn')
        };
        
        // 绑定排序表头点击事件
        this.bindSortEvents();
        
        // 初始化模态框
        this.initModal();
        
        // 加载用户数据
        this.loadUsers();
        
        // 绑定事件
        this.bindEvents();
    },
    
    /**
     * 绑定DOM事件
     */
    bindEvents: function() {
        // 上一页
        this.elements.prevBtn.addEventListener('click', () => {
            if (this.page > 1) {
                this.page--;
                this.loadUsers();
            }
        });
        
        // 下一页
        this.elements.nextBtn.addEventListener('click', () => {
            this.page++;
            this.loadUsers();
        });
        
        // 搜索按钮
        this.elements.searchBtn.addEventListener('click', () => {
            this.search = this.elements.searchInput.value.trim();
            this.page = 1; // 重置为第一页
            this.loadUsers();
        });
        
        // 回车搜索
        this.elements.searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.elements.searchBtn.click();
            }
        });
        
        // 状态筛选
        this.elements.statusFilter.addEventListener('change', () => {
            this.status = this.elements.statusFilter.value;
            this.page = 1; // 重置为第一页
            this.loadUsers();
        });
        
        // 显示排行按钮
        if (this.elements.showRankingBtn) {
            this.elements.showRankingBtn.addEventListener('click', () => {
                this.showRankingModal();
            });
        }
    },
    
    /**
     * 初始化模态框
     */
    initModal: function() {
        if (!this.elements.rankingModal) return;
        
        // 关闭模态框的X按钮
        const closeBtn = this.elements.rankingModal.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.elements.rankingModal.style.display = 'none';
            });
        }
        
        // 点击模态框外部关闭
        window.addEventListener('click', (e) => {
            if (e.target === this.elements.rankingModal) {
                this.elements.rankingModal.style.display = 'none';
            }
        });
    },
    
    /**
     * 显示排行模态框
     */
    showRankingModal: function() {
        if (!this.elements.rankingModal) return;
        
        // 显示模态框
        this.elements.rankingModal.style.display = 'block';
        
        // 显示加载中
        this.elements.rankingModalBody.innerHTML = '<div class="modal-loading">正在加载用户排行数据...</div>';
        
        // 加载所有用户数据
        this.loadAllUsers();
    },
    
    /**
     * 加载所有用户数据（用于排行）
     */
    loadAllUsers: function() {
        // 如果已经有所有用户数据且不是正在加载中，直接显示
        if (this.allUsers.length > 0 && !this.isLoadingAllUsers) {
            this.showUserRanking();
            return;
        }
        
        // 设置加载标志
        this.isLoadingAllUsers = true;
        
        // 获取管理员Token
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        // 构建查询参数 - 获取全部用户，每页100个
        const params = new URLSearchParams({
            per_page: 100,
            sort_by: 'id',
            sort_order: 'asc'
        });
        
        // 创建一个数组用于存储所有用户
        let allUsers = [];
        
        // 递归函数，用于获取所有页面的用户
        const fetchUsersPage = (page) => {
            params.set('page', page);
            
            return fetch(`${Auth.apiBaseUrl}/get_admin_users.php?${params.toString()}`, {
                method: 'GET',
                headers: {
                    'Authorization': token
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.msg || '加载失败');
                }
                
                // 将当前页的用户添加到数组
                if (data.data && data.data.length > 0) {
                    allUsers = allUsers.concat(data.data);
                }
                
                // 检查是否有更多页面
                if (data.pagination && data.pagination.current_page < data.pagination.total_pages) {
                    // 更新模态框中的加载信息
                    this.elements.rankingModalBody.innerHTML = `<div class="modal-loading">正在加载用户数据...${data.pagination.current_page}/${data.pagination.total_pages}页</div>`;
                    
                    // 递归获取下一页
                    return fetchUsersPage(page + 1);
                } else {
                    // 所有页面已加载完成
                    return allUsers;
                }
            });
        };
        
        // 开始获取第一页
        fetchUsersPage(1)
            .then(users => {
                // 保存所有用户数据
                this.allUsers = users;
                
                // 获取所有用户的衣物数量
                return this.fetchAllUsersClothesCount(users);
            })
            .then(() => {
                // 显示用户排行
                this.showUserRanking();
            })
            .catch(error => {
                console.error('加载所有用户数据失败:', error);
                this.elements.rankingModalBody.innerHTML = `<div class="modal-loading">加载失败: ${error.message}</div>`;
            })
            .finally(() => {
                this.isLoadingAllUsers = false;
            });
    },
    
    /**
     * 获取所有用户的衣物数量
     * @param {Array} users 用户数组
     * @returns {Promise} Promise对象
     */
    fetchAllUsersClothesCount: function(users) {
        if (!users || users.length === 0) {
            return Promise.resolve([]);
        }
        
        // 提取所有用户ID
        const userIds = users.map(user => user.id);
        
        // 获取管理员Token
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return Promise.reject(new Error('未登录'));
        }
        
        // 更新模态框中的加载信息
        this.elements.rankingModalBody.innerHTML = `<div class="modal-loading">正在获取用户衣物数量...0/${userIds.length}</div>`;
        
        // 分批处理用户ID，避免同时发送太多请求
        const batchSize = 10;
        const batches = [];
        
        for (let i = 0; i < userIds.length; i += batchSize) {
            batches.push(userIds.slice(i, i + batchSize));
        }
        
        let processedCount = 0;
        
        // 逐批处理用户ID
        return batches.reduce((promise, batch, batchIndex) => {
            return promise.then(() => {
                // 为当前批次的所有用户创建Promise
                const batchPromises = batch.map(userId => {
                    // 如果缓存中已有该用户的衣物数量，则直接使用缓存值
                    if (this.clothesCountCache[userId]) {
                        return Promise.resolve({ 
                            userId: userId, 
                            count: this.clothesCountCache[userId] 
                        });
                    }
                    
                    // 否则请求API获取该用户的衣物数量
                    return fetch(`${Auth.apiBaseUrl}/get_admin_clothes.php?user_id=${userId}&per_page=1`, {
                        method: 'GET',
                        headers: {
                            'Authorization': token
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        const count = data.pagination ? data.pagination.total : 0;
                        // 更新缓存
                        this.clothesCountCache[userId] = count;
                        return { userId: userId, count: count };
                    })
                    .catch(error => {
                        console.error(`获取用户 ${userId} 衣物数量失败:`, error);
                        return { userId: userId, count: 0 };
                    });
                });
                
                // 等待当前批次的所有请求完成
                return Promise.all(batchPromises).then(results => {
                    // 更新已处理数量
                    processedCount += results.length;
                    
                    // 更新模态框中的加载信息
                    this.elements.rankingModalBody.innerHTML = `<div class="modal-loading">正在获取用户衣物数量...${processedCount}/${userIds.length}</div>`;
                    
                    // 将衣物数量添加到用户数据中
                    results.forEach(result => {
                        const user = this.allUsers.find(u => u.id === result.userId);
                        if (user) {
                            user.clothesCount = result.count;
                        }
                    });
                });
            });
        }, Promise.resolve());
    },
    
    /**
     * 显示用户排行
     */
    showUserRanking: function() {
        if (!this.elements.rankingModalBody || this.allUsers.length === 0) {
            return;
        }
        
        // 按衣物数量排序（从高到低）
        const sortedUsers = [...this.allUsers].sort((a, b) => {
            const countA = a.clothesCount !== undefined ? a.clothesCount : 0;
            const countB = b.clothesCount !== undefined ? b.clothesCount : 0;
            return countB - countA; // 降序
        });
        
        // 构建排行表格HTML
        let html = `
            <table class="ranking-table">
                <thead>
                    <tr>
                        <th width="10%">排名</th>
                        <th width="15%">用户ID</th>
                        <th width="30%">昵称</th>
                        <th width="15%">衣物数量</th>
                        <th width="30%">操作</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        // 添加每个用户的排行信息
        sortedUsers.forEach((user, index) => {
            const rank = index + 1;
            const rankClass = rank <= 3 ? 'top-3' : '';
            
            html += `
                <tr>
                    <td><span class="rank ${rankClass}">${rank}</span></td>
                    <td>${user.id}</td>
                    <td>${user.nickname || '未设置昵称'}</td>
                    <td>${user.clothesCount !== undefined ? user.clothesCount : 0}</td>
                    <td>
                        <button class="action-btn view-btn" onclick="UserList.viewUser(${user.id})">查看</button>
                    </td>
                </tr>
            `;
        });
        
        html += `
                </tbody>
            </table>
        `;
        
        // 更新模态框内容
        this.elements.rankingModalBody.innerHTML = html;
    },
    
    /**
     * 绑定排序事件
     */
    bindSortEvents: function() {
        this.elements.sortableHeaders.forEach(header => {
            header.addEventListener('click', () => {
                const sortField = header.getAttribute('data-sort');
                
                // 更新排序状态
                if (this.sortBy === sortField) {
                    // 切换排序方向
                    this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
                } else {
                    // 新的排序字段，默认降序
                    this.sortBy = sortField;
                    this.sortOrder = 'desc';
                }
                
                // 更新UI
                this.updateSortIndicators();
                
                // 如果是衣物数量排序，本地排序
                if (sortField === 'clothes_count') {
                    this.sortUsersByClothesCount();
                } else {
                    // 其他字段重新加载用户
                    this.loadUsers();
                }
            });
        });
        
        // 初始化排序指示器
        this.updateSortIndicators();
    },
    
    /**
     * 更新排序指示器
     */
    updateSortIndicators: function() {
        // 移除所有表头的排序类
        this.elements.sortableHeaders.forEach(header => {
            header.classList.remove('asc', 'desc');
        });
        
        // 设置当前排序表头的类
        const currentHeader = Array.from(this.elements.sortableHeaders).find(
            header => header.getAttribute('data-sort') === this.sortBy
        );
        
        if (currentHeader) {
            currentHeader.classList.add(this.sortOrder);
        }
    },
    
    /**
     * 按衣物数量本地排序用户
     */
    sortUsersByClothesCount: function() {
        if (!this.currentUsers || this.currentUsers.length === 0) {
            return;
        }
        
        // 本地排序
        this.currentUsers.sort((a, b) => {
            const countA = a.clothesCount !== undefined ? a.clothesCount : 0;
            const countB = b.clothesCount !== undefined ? b.clothesCount : 0;
            
            // 升序或降序
            return this.sortOrder === 'asc' ? countA - countB : countB - countA;
        });
        
        // 重新渲染表格
        this.renderUsers(this.currentUsers, {
            current_page: this.page,
            total_pages: Math.ceil(this.currentUsers.length / this.perPage)
        });
    },
    
    /**
     * 加载用户数据
     */
    loadUsers: function() {
        // 显示加载状态
        this.elements.userTableBody.innerHTML = '<tr><td colspan="10" class="no-data">加载中...</td></tr>';
        
        // 构建查询参数
        const params = new URLSearchParams({
            page: this.page,
            per_page: this.perPage,
            sort_by: this.sortBy,
            sort_order: this.sortOrder
        });
        
        // 添加搜索参数
        if (this.search) {
            params.append('search', this.search);
        }
        
        // 添加状态筛选
        if (this.status !== '') {
            params.append('status', this.status);
        }
        
        // 获取管理员Token
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        // 请求API
        fetch(`${Auth.apiBaseUrl}/get_admin_users.php?${params.toString()}`, {
            method: 'GET',
            headers: {
                'Authorization': token
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '加载失败');
            }
            
            // 获取用户列表后，加载衣物数量
            this.fetchClothesCount(data.data, data.pagination);
        })
        .catch(error => {
            console.error('获取用户列表失败:', error);
            this.elements.userTableBody.innerHTML = `<tr><td colspan="10" class="no-data">加载失败: ${error.message}</td></tr>`;
        });
    },
    
    /**
     * 获取用户的衣物数量
     * @param {Array} users 用户数据
     * @param {Object} pagination 分页信息
     */
    fetchClothesCount: function(users, pagination) {
        if (!users || users.length === 0) {
            this.currentUsers = [];
            this.renderUsers(users, pagination);
            return;
        }
        
        // 提取所有用户ID
        const userIds = users.map(user => user.id);
        
        // 获取管理员Token
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        // 创建Promise数组，为每个用户获取衣物数量
        const promises = userIds.map(userId => {
            // 如果缓存中已有该用户的衣物数量，则直接使用缓存值
            if (this.clothesCountCache[userId]) {
                return Promise.resolve({ 
                    userId: userId, 
                    count: this.clothesCountCache[userId] 
                });
            }
            
            // 否则请求API获取该用户的衣物数量
            return fetch(`${Auth.apiBaseUrl}/get_admin_clothes.php?user_id=${userId}&per_page=1`, {
                method: 'GET',
                headers: {
                    'Authorization': token
                }
            })
            .then(response => response.json())
            .then(data => {
                const count = data.pagination ? data.pagination.total : 0;
                // 更新缓存
                this.clothesCountCache[userId] = count;
                return { userId: userId, count: count };
            })
            .catch(error => {
                console.error(`获取用户 ${userId} 衣物数量失败:`, error);
                return { userId: userId, count: '-' };
            });
        });
        
        // 等待所有请求完成
        Promise.all(promises)
            .then(results => {
                // 将衣物数量添加到用户数据中
                const clothesCountMap = results.reduce((map, item) => {
                    map[item.userId] = item.count;
                    return map;
                }, {});
                
                users.forEach(user => {
                    user.clothesCount = clothesCountMap[user.id] || 0;
                });
                
                // 保存当前用户数据
                this.currentUsers = [...users];
                
                // 如果当前是按衣物数量排序，则进行本地排序
                if (this.sortBy === 'clothes_count') {
                    this.sortUsersByClothesCount();
                } else {
                    // 渲染用户列表
                    this.renderUsers(users, pagination);
                }
            })
            .catch(error => {
                console.error('获取衣物数量失败:', error);
                // 仍然渲染用户列表，但不显示衣物数量
                this.renderUsers(users, pagination);
            });
    },
    
    /**
     * 渲染用户列表
     * @param {Array} users 用户数据
     * @param {Object} pagination 分页信息
     */
    renderUsers: function(users, pagination) {
        // 处理空数据
        if (!users || users.length === 0) {
            this.elements.userTableBody.innerHTML = '<tr><td colspan="10" class="no-data">暂无用户数据</td></tr>';
            this.updatePagination(pagination);
            return;
        }
        
        // 构建表格HTML
        let html = '';
        users.forEach(user => {
            const gender = this.getGenderText(user.gender);
            const statusClass = parseInt(user.status) === 1 ? 'status-active' : 'status-disabled';
            const statusText = parseInt(user.status) === 1 ? '正常' : '禁用';
            
            // 本地默认头像
            const defaultAvatar = 'images/default-avatar.png';
            
            // 检查头像URL并处理微信临时文件路径
            let avatarUrl;
            if (!user.avatar_url || user.avatar_url.trim() === '') {
                avatarUrl = defaultAvatar;
            } else if (user.avatar_url.startsWith('wxfile://')) {
                // 不支持的微信临时文件URL，使用默认头像
                avatarUrl = defaultAvatar;
            } else {
                avatarUrl = user.avatar_url;
            }
            
            // 试衣次数显示，修复默认值为1而不是0
            const freeCount = user.free_try_on_count !== undefined ? user.free_try_on_count : '1';
            const paidCount = user.paid_try_on_count !== undefined ? user.paid_try_on_count : '0';
            const tryOnCountDisplay = `${freeCount}/${paidCount}`;
            
            // 衣物数量显示
            const clothesCount = user.clothesCount !== undefined ? user.clothesCount : '-';
            
            html += `
                <tr>
                    <td>${user.id}</td>
                    <td><img src="${avatarUrl}" class="avatar" alt="用户头像" onerror="this.src='${defaultAvatar}'"></td>
                    <td>${user.nickname || '未设置昵称'}</td>
                    <td>${this.formatOpenid(user.openid)}</td>
                    <td>${gender}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td>${clothesCount}</td>
                    <td>${tryOnCountDisplay}</td>
                    <td>${this.formatDate(user.created_at)}</td>
                    <td>
                        <button class="action-btn view-btn" onclick="UserList.viewUser(${user.id})">查看</button>
                        ${parseInt(user.status) === 1 
                            ? `<button class="action-btn disable-btn" onclick="UserList.updateUserStatus(${user.id}, 0)">禁用</button>` 
                            : `<button class="action-btn enable-btn" onclick="UserList.updateUserStatus(${user.id}, 1)">启用</button>`
                        }
                    </td>
                </tr>
            `;
        });
        
        this.elements.userTableBody.innerHTML = html;
        this.updatePagination(pagination);
    },
    
    /**
     * 更新分页控件
     * @param {Object} pagination 分页信息
     */
    updatePagination: function(pagination) {
        if (!pagination) return;
        
        this.elements.pageInfo.textContent = `第 ${pagination.current_page}/${pagination.total_pages} 页`;
        this.elements.prevBtn.disabled = pagination.current_page <= 1;
        this.elements.nextBtn.disabled = pagination.current_page >= pagination.total_pages;
    },
    
    /**
     * 查看用户详情
     * @param {Number} userId 用户ID
     */
    viewUser: function(userId) {
        window.location.href = `user_details.html?id=${userId}`;
    },
    
    /**
     * 更新用户状态
     * @param {Number} userId 用户ID
     * @param {Number} status 状态值 (0: 禁用, 1: 启用)
     */
    updateUserStatus: function(userId, status) {
        if (!confirm(`确定要${status === 1 ? '启用' : '禁用'}该用户吗？`)) {
            return;
        }
        
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        fetch(`${Auth.apiBaseUrl}/update_admin_user_status.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': token
            },
            body: JSON.stringify({
                user_id: userId,
                status: status
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '操作失败');
            }
            
            alert(data.msg || '操作成功');
            this.loadUsers(); // 重新加载用户列表
        })
        .catch(error => {
            console.error('更新用户状态失败:', error);
            alert(`操作失败: ${error.message}`);
        });
    },
    
    /**
     * 格式化性别显示
     * @param {Number} gender 性别码 (0: 未知, 1: 男, 2: 女)
     * @returns {String} 性别文本
     */
    getGenderText: function(gender) {
        switch(parseInt(gender)) {
            case 1: return '男';
            case 2: return '女';
            default: return '未知';
        }
    },
    
    /**
     * 格式化OpenID显示
     * @param {String} openid OpenID
     * @returns {String} 格式化的OpenID
     */
    formatOpenid: function(openid) {
        if (!openid) return '未知';
        if (openid.length <= 10) return openid;
        
        // 显示前后各4位，中间用省略号
        return `${openid.substr(0, 4)}...${openid.substr(-4)}`;
    },
    
    /**
     * 格式化日期显示
     * @param {String} dateStr 日期字符串
     * @returns {String} 格式化的日期
     */
    formatDate: function(dateStr) {
        if (!dateStr) return '未知';
        
        const date = new Date(dateStr);
        return isNaN(date.getTime()) 
            ? dateStr 
            : date.getFullYear() + '-' + 
              String(date.getMonth() + 1).padStart(2, '0') + '-' + 
              String(date.getDate()).padStart(2, '0') + ' ' +
              String(date.getHours()).padStart(2, '0') + ':' +
              String(date.getMinutes()).padStart(2, '0');
    }
};

// 文档加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    UserList.init();
}); 