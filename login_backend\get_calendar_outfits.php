<?php
// 引入必要的文件
require_once 'auth.php';
require_once 'db.php';
require_once 'config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 验证token获取用户ID
$auth = new Auth();

// 获取token
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 如果有Bearer前缀，去掉它
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
}

if (!$token) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供授权Token'
    ]);
    exit;
}

// 使用Auth类验证token
$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'error' => true,
        'msg' => '未授权，请先登录'
    ]);
    exit;
}

$userId = $payload['sub']; // 从payload中获取用户ID

// 处理GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'error' => true,
        'msg' => '不支持的请求方法'
    ]);
    exit;
}

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();

    // 获取日期范围参数
    $startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01'); // 默认当月第一天
    $endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-t'); // 默认当月最后一天
    
    // 查询指定日期范围内的穿搭
    $stmt = $conn->prepare("
        SELECT o.id, o.name, o.description, o.thumbnail_url, o.outfit_data, 
               oc.calendar_date, o.category_id, c.name as category_name, 
               o.created_at, o.updated_at
        FROM outfit_calendar oc
        JOIN outfits o ON oc.outfit_id = o.id
        LEFT JOIN outfit_categories c ON o.category_id = c.id
        WHERE oc.user_id = :user_id 
        AND oc.calendar_date BETWEEN :start_date AND :end_date
        ORDER BY oc.calendar_date ASC
    ");
    
    $stmt->bindParam(':user_id', $userId);
    $stmt->bindParam(':start_date', $startDate);
    $stmt->bindParam(':end_date', $endDate);
    $stmt->execute();
    
    $outfits = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理查询结果
    $result = [];
    foreach ($outfits as $outfit) {
        // 解析JSON数据
        $outfitData = json_decode($outfit['outfit_data'], true);
        
        // 构建返回的穿搭对象
        $resultOutfit = [
            'id' => $outfit['id'],
            'name' => $outfit['name'],
            'description' => $outfit['description'],
            'thumbnail_url' => $outfit['thumbnail_url'],
            'calendar_date' => $outfit['calendar_date'],
            'category_id' => $outfit['category_id'],
            'category_name' => $outfit['category_name'],
            'created_at' => $outfit['created_at'],
            'updated_at' => $outfit['updated_at'],
            'items' => $outfitData['items'] ?? []
        ];
        
        $result[] = $resultOutfit;
    }
    
    // 返回结果
    echo json_encode([
        'error' => false,
        'data' => $result
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '获取穿搭日历数据失败: ' . $e->getMessage()
    ]);
} 