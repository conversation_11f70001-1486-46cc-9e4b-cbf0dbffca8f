/* 复用app.wxss的基础样式 */
@import '/app.wxss';

.container {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding: 20rpx;
}

/* 排行榜列表 */
.ranking-list {
  margin-top: 20rpx;
}

.ranking-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.ranking-item:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 排名数字 */
.rank-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.rank-number.rank-top {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
}

.rank-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.rank-top .rank-text {
  color: #000;
}

/* 衣物图片 */
.item-image-container {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
  background-color: #f8f8f8;
}

.item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 衣物信息 */
.item-info {
  flex: 1;
  margin-right: 24rpx;
}

.item-name {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.item-category {
  font-size: 26rpx;
  color: #666;
}

/* 穿搭次数 */
.wear-count {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.count-number {
  font-size: 36rpx;
  color: #333;
  line-height: 1;
}

.count-unit {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

/* 空状态 */
.empty-state {
  margin-top: 120rpx;
  padding: 60rpx 40rpx;
}

/* 加载状态 */
.loading-state {
  margin-top: 120rpx;
  padding: 60rpx 40rpx;
}

/* 加载更多 */
.load-more {
  margin-top: 40rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.load-more:active {
  background-color: #f8f8f8;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .ranking-item {
    padding: 20rpx;
  }
  
  .item-name {
    font-size: 30rpx;
  }
  
  .count-number {
    font-size: 32rpx;
  }
}
