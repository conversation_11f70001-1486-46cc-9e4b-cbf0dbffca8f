<?php
// 获取圈子共享衣橱API
// 模块4：数据共享基础模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

// 获取查询参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$perPage = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;
$offset = ($page - 1) * $perPage;

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查找用户所在的圈子
    $findCircleSql = "SELECT cm.circle_id, cm.role, c.name as circle_name
                      FROM circle_members cm 
                      JOIN outfit_circles c ON cm.circle_id = c.id 
                      WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
    $findCircleStmt = $conn->prepare($findCircleSql);
    $findCircleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $findCircleStmt->execute();
    
    $userCircle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$userCircle) {
        echo json_encode([
            'status' => 'error',
            'message' => '您当前未加入任何圈子'
        ]);
        exit;
    }
    
    $circleId = $userCircle['circle_id'];
    
    // 获取圈子中的所有衣橱（包括个人数据和圈子共享数据）
    $wardrobesSql = "SELECT w.id, w.name, w.description, w.sort_order, w.is_default, 
                            w.created_at, w.updated_at, w.user_id, w.circle_id,
                            u.nickname as creator_nickname,
                            COUNT(c.id) as clothes_count
                     FROM wardrobes w
                     JOIN users u ON w.user_id = u.id
                     LEFT JOIN clothes c ON w.id = c.wardrobe_id
                     WHERE (w.circle_id = :circle_id OR 
                            (w.user_id IN (SELECT user_id FROM circle_members 
                                          WHERE circle_id = :circle_id AND status = 'active') 
                             AND w.circle_id IS NULL))
                     GROUP BY w.id
                     ORDER BY w.is_default DESC, w.sort_order ASC, w.created_at DESC
                     LIMIT :offset, :per_page";
    
    $wardrobesStmt = $conn->prepare($wardrobesSql);
    $wardrobesStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $wardrobesStmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $wardrobesStmt->bindParam(':per_page', $perPage, PDO::PARAM_INT);
    $wardrobesStmt->execute();
    
    $wardrobes = [];
    while ($wardrobe = $wardrobesStmt->fetch(PDO::FETCH_ASSOC)) {
        $wardrobeData = [
            'id' => $wardrobe['id'],
            'name' => $wardrobe['name'],
            'description' => $wardrobe['description'],
            'sort_order' => intval($wardrobe['sort_order']),
            'is_default' => intval($wardrobe['is_default']),
            'created_at' => $wardrobe['created_at'],
            'updated_at' => $wardrobe['updated_at'],
            'user_id' => $wardrobe['user_id'],
            'circle_id' => $wardrobe['circle_id'],
            'creator_nickname' => $wardrobe['creator_nickname'],
            'clothes_count' => intval($wardrobe['clothes_count']),
            'is_shared' => !is_null($wardrobe['circle_id']),
            'is_own' => $wardrobe['user_id'] == $userId,
            'data_source' => is_null($wardrobe['circle_id']) ? 'personal' : 'shared'
        ];
        
        $wardrobes[] = $wardrobeData;
    }
    
    // 获取总数量
    $countSql = "SELECT COUNT(DISTINCT w.id) as total
                 FROM wardrobes w
                 WHERE (w.circle_id = :circle_id OR 
                        (w.user_id IN (SELECT user_id FROM circle_members 
                                      WHERE circle_id = :circle_id AND status = 'active') 
                         AND w.circle_id IS NULL))";
    $countStmt = $conn->prepare($countSql);
    $countStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $countStmt->execute();
    
    $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    $totalPages = ceil($totalCount / $perPage);
    
    // 获取圈子统计信息
    $statsSql = "SELECT 
                    COUNT(DISTINCT w.id) as total_wardrobes,
                    COUNT(DISTINCT c.id) as total_clothes,
                    COUNT(DISTINCT o.id) as total_outfits,
                    COUNT(DISTINCT cm.user_id) as total_members
                 FROM circle_members cm
                 LEFT JOIN wardrobes w ON (w.circle_id = :circle_id OR 
                                          (w.user_id = cm.user_id AND w.circle_id IS NULL))
                 LEFT JOIN clothes c ON (c.circle_id = :circle_id OR 
                                        (c.user_id = cm.user_id AND c.circle_id IS NULL))
                 LEFT JOIN outfits o ON (o.circle_id = :circle_id OR 
                                        (o.user_id = cm.user_id AND o.circle_id IS NULL))
                 WHERE cm.circle_id = :circle_id AND cm.status = 'active'";
    $statsStmt = $conn->prepare($statsSql);
    $statsStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $statsStmt->execute();
    
    $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'status' => 'success',
        'data' => [
            'circle_info' => [
                'circle_id' => $circleId,
                'circle_name' => $userCircle['circle_name'],
                'user_role' => $userCircle['role']
            ],
            'wardrobes' => $wardrobes,
            'pagination' => [
                'total' => intval($totalCount),
                'page' => $page,
                'per_page' => $perPage,
                'total_pages' => $totalPages
            ],
            'stats' => [
                'total_wardrobes' => intval($stats['total_wardrobes']),
                'total_clothes' => intval($stats['total_clothes']),
                'total_outfits' => intval($stats['total_outfits']),
                'total_members' => intval($stats['total_members'])
            ]
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '获取圈子衣橱失败：' . $e->getMessage()
    ]);
}
?>
