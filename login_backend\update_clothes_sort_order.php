<?php
/**
 * Update Clothes Sort Order API
 * 
 * Updates the sort order of clothes for a user
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * POST Parameters:
 * - clothes_order: Array of clothing IDs in the desired order
 * 
 * Response:
 * {
 *   "error": false,
 *   "message": "Sort order updated successfully"
 * }
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// Set response content type
header('Content-Type: application/json');

// Handle CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if Authorization header exists
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode(['error' => true, 'message' => 'Authorization header missing']);
    exit;
}

// Verify token and get user info
$token = $_SERVER['HTTP_AUTHORIZATION'];
$user = verifyToken($token);

if (!$user) {
    echo json_encode(['error' => true, 'message' => 'Invalid token']);
    exit;
}

// Only allow POST method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => true, 'message' => 'Only POST method allowed']);
    exit;
}

// Get POST data
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['clothes_order']) || !is_array($input['clothes_order'])) {
    echo json_encode(['error' => true, 'message' => 'clothes_order parameter is required and must be an array']);
    exit;
}

$clothes_order = $input['clothes_order'];
$user_id = $user['id'];

try {
    $pdo = getDbConnection();
    
    // Start transaction
    $pdo->beginTransaction();
    
    // Update sort order for each clothing item
    $stmt = $pdo->prepare("UPDATE clothes SET sort_order = ? WHERE id = ? AND user_id = ?");
    
    foreach ($clothes_order as $index => $clothing_id) {
        $sort_order = $index + 1; // Start from 1
        $stmt->execute([$sort_order, $clothing_id, $user_id]);
    }
    
    // Commit transaction
    $pdo->commit();
    
    echo json_encode([
        'error' => false,
        'message' => 'Sort order updated successfully'
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    
    echo json_encode([
        'error' => true,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
