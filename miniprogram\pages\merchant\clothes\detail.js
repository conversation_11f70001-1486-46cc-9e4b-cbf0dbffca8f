const app = getApp();

Page({
  data: {
    clothesId: null,
    merchantId: null,
    clothes: null,
    merchant: null,
    isShareCredits: false,
    loading: true
  },
  
  onLoad: function(options) {
    const clothesId = options.clothes_id;
    const merchantId = options.merchant_id;
    
    if (!clothesId || !merchantId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }
    
    this.setData({
      clothesId: parseInt(clothesId),
      merchantId: parseInt(merchantId)
    });
    
    this.loadClothesDetail();
  },
  
  // 加载衣物详情
  loadClothesDetail: function() {
    wx.getStorage({
      key: 'token',
      success: (res) => {
        wx.request({
          url: app.globalData.apiBaseUrl + '/get_merchant_clothes_detail.php',
          method: 'POST',
          data: {
            token: res.data,
            clothes_id: this.data.clothesId,
            merchant_id: this.data.merchantId
          },
          success: (response) => {
            if (response.data.code === 0) {
              const data = response.data.data;
              this.setData({
                clothes: data.clothes,
                merchant: data.merchant,
                isShareCredits: data.merchant.share_try_on_credits === 1,
                loading: false
              });
              wx.setNavigationBarTitle({
                title: data.clothes.name || '衣物详情'
              });
            } else {
              this.setData({
                loading: false
              });
              wx.showToast({
                title: response.data.message || '获取衣物详情失败',
                icon: 'none'
              });
            }
          },
          fail: () => {
            this.setData({
              loading: false
            });
            wx.showToast({
              title: '网络错误，请稍后再试',
              icon: 'none'
            });
          }
        });
      },
      fail: () => {
        this.setData({
          loading: false
        });
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
      }
    });
  },
  
  // 试穿衣物
  tryOnClothes: function() {
    wx.navigateTo({
      url: `/pages/photos/index/index?clothes_id=${this.data.clothesId}&merchant_id=${this.data.merchantId}&try_on=1`
    });
  },
  
  // 预览图片
  previewImage: function() {
    wx.previewImage({
      urls: [this.data.clothes.image_url],
      current: this.data.clothes.image_url
    });
  },
  
  // 返回上一页
  backToList: function() {
    wx.navigateBack();
  }
}); 