// pages/index/index.js
const app = getApp()

Page({
  data: {
    userInfo: {},
    hasUserInfo: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    canIUseGetUserProfile: false,
    canIUseOpenData: wx.canIUse('open-data.type.userAvatarUrl') && wx.canIUse('open-data.type.userNickName'),
    isUsingMockUser: false,
    canIUseCanvas: wx.canIUse('canvas'),
    // 衣橱ID
    selectedWardrobeId: null,
    selectedWardrobeName: '全部衣橱',
    wardrobeList: [],
    // 分类
    categories: [],
    currentCategory: 'all',
    clothes: [], // 衣物列表
    leftItems: [], // 左列衣物
    rightItems: [], // 右列衣物
    lastRefreshTime: 0, // 添加最后刷新时间戳
    isRefreshing: false, // 添加刷新状态标记
    // 显示选项
    showUploadOptions: false,
    showAnnouncement: false,
    showWardrobePopup: false,
    showSearchPopup: false,
    showPersonalAnalysisOptions: false,
    showOutfitOptions: false, // 添加智能穿搭选项显示控制
    announcement: {
      title: '',
      content: ''
    },
    hideImageAnalysisBanner: false, // 是否隐藏个人形象分析Banner
    // 搜索
    isSearching: false,
    searchKeyword: '',
    searchResults: [],
    // 布局
    layoutMode: 4, // 当前布局模式，默认4列
    showClothingName: false, // 是否显示衣物名称
    categoryCountMap: {}, // 各分类的衣物计数缓存
    isLoadingCategory: false, // 是否正在加载分类数据
    isLoadingClothes: true,
    loadError: false,

    // 新增：数据源相关
    dataSource: wx.getStorageSync('dataSource') || 'personal', // personal, shared, all
    showDataSourcePopup: false,
    dataSourceOptions: [
      { key: 'personal', name: '个人数据', icon: '👤' },
      { key: 'shared', name: '共享数据', icon: '👥' },
      { key: 'all', name: '全部数据', icon: '🌐' }
    ]
  },
  
  onLoad: function(options) {
    // 检查是否从分享链接进入
    if (options.source === 'outfit_share') {
      // 显示欢迎提示
      wx.showModal({
        title: '欢迎使用次元衣帽间',
        content: '这里是您的数字衣橱，点击"今日智能穿搭"可获取天气穿搭推荐',
        showCancel: false,
        confirmText: '我知道了'
      });
    }
    
    this.checkLoginStatus();
    
    // 设置默认的布局模式和显示名称设置
    this.setData({
      layoutMode: 4,
      showClothingName: false
    });
    
    // 存储默认设置到本地
    wx.setStorageSync('preferredLayoutMode', 4);
    wx.setStorageSync('showClothingName', false);
    
    // 初始化分类计数映射
    this.setData({
      categoryCountMap: {
        'all': 0  // 初始化全部分类的计数
      }
    });
    
    // 获取Banner显示设置
    const hideImageAnalysisBanner = wx.getStorageSync('hideImageAnalysisBanner') === true;
    this.setData({
      hideImageAnalysisBanner: hideImageAnalysisBanner
    });
    
    // 获取公告
    this.getAnnouncement();
    
    // 获取分类列表
    console.log('页面初始化时的数据源:', this.data.dataSource);
    this.getClothingCategories();
  },
  
  // 获取公告信息
  getAnnouncement: function() {
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_announcement.php`,
      method: 'GET',
      success: (res) => {
        console.log('获取公告响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          const announcement = res.data.data;
          
          // 检查是否应该显示公告
          if (this.shouldShowAnnouncement(announcement.id)) {
            this.setData({
              announcement: announcement,
              showAnnouncement: true
            });
          }
        }
      },
      fail: (err) => {
        console.error('获取公告失败:', err);
      }
    });
  },
  
  // 判断是否应该显示公告
  shouldShowAnnouncement: function(announcementId) {
    try {
      // 获取本地存储的已读公告信息
      const readAnnouncementsStr = wx.getStorageSync('readAnnouncements');
      const readAnnouncements = readAnnouncementsStr ? JSON.parse(readAnnouncementsStr) : {};
      
      // 获取当前公告的阅读记录
      const readInfo = readAnnouncements[announcementId];
      
      // 如果没有阅读记录，应该显示
      if (!readInfo) {
        return true;
      }
      
      // 获取当前日期和时间
      const now = new Date();
      const today = now.toISOString().split('T')[0]; // 格式: YYYY-MM-DD
      const currentHour = now.getHours();
      
      // 如果不是今天阅读的，或者是今天但已经过了8点，应该显示
      if (readInfo.date !== today || (readInfo.date === today && readInfo.shown === false && currentHour >= 8)) {
        return true;
      }
      
      // 其他情况不显示
      return false;
    } catch (e) {
      console.error('检查公告显示状态失败:', e);
      return true; // 出错时默认显示
    }
  },
  
  // 关闭公告弹窗
  closeAnnouncement: function() {
    try {
      // 标记公告为已读
      const announcementId = this.data.announcement.id;
      const readAnnouncementsStr = wx.getStorageSync('readAnnouncements');
      const readAnnouncements = readAnnouncementsStr ? JSON.parse(readAnnouncementsStr) : {};
      
      // 记录当前日期
      const today = new Date().toISOString().split('T')[0]; // 格式: YYYY-MM-DD
      
      // 更新已读信息
      readAnnouncements[announcementId] = {
        date: today,
        shown: true
      };
      
      // 保存到本地存储
      wx.setStorageSync('readAnnouncements', JSON.stringify(readAnnouncements));
      
      // 关闭弹窗
      this.setData({
        showAnnouncement: false
      });
    } catch (e) {
      console.error('保存公告阅读状态失败:', e);
      // 即使保存失败也关闭弹窗
      this.setData({
        showAnnouncement: false
      });
    }
  },
  
  // 添加下拉刷新处理函数
  onPullDownRefresh: function() {
    console.log("触发下拉刷新");
    // 设置刷新状态
    this.setData({
      isRefreshing: true
    });
    
    // 刷新分类列表
    this.getClothingCategories();
    
    // 先刷新衣橱列表，再刷新衣物数据
    this.loadWardrobeList(() => {
      this.loadClothesData();
    });
  },
  
  onShow: function() {
    // 获取全局刷新标志
    const needRefreshClothes = app.globalData.needRefreshClothes === true;
    const needRefreshWardrobes = app.globalData.needRefreshWardrobes === true;
    const needRefreshCategories = app.globalData.needRefreshCategories === true;
    
    // 从本地存储获取用户偏好设置
    try {
      const preferredLayoutMode = wx.getStorageSync('preferredLayoutMode');
      const showClothingName = wx.getStorageSync('showClothingName');
      
      // 只有当存储中有值时才应用设置
      this.setData({
        layoutMode: preferredLayoutMode || this.data.layoutMode,
        showClothingName: showClothingName !== undefined ? showClothingName : this.data.showClothingName
      });
    } catch (e) {
      console.error('读取用户偏好设置失败:', e);
    }
    
    // 记录之前的状态
    const prevIsUsingMockUser = this.data.isUsingMockUser;
    const prevHasUserInfo = this.data.hasUserInfo;
    
    // 检查登录状态
    if (app.globalData.token) {
      // 如果已登录，则设置用户信息
      if (app.globalData.userInfo) {
        // 检查是否使用体验账号
        const isUsingMockUser = app.globalData.useMockUser === true;
        const userInfo = {
          id: app.globalData.userInfo.id,
          nickName: app.globalData.userInfo.nickname,
          avatarUrl: app.globalData.userInfo.avatar_url,
          gender: app.globalData.userInfo.gender
        };
        
        // 检查是否从体验账号切换到登录状态或反之
        const loginStatusChanged = prevIsUsingMockUser !== isUsingMockUser || 
                                  prevHasUserInfo !== true;
        
        // 更新用户信息
        this.setData({
          userInfo: userInfo,
          hasUserInfo: true,
          isUsingMockUser: isUsingMockUser // 确保更新体验账号状态
        });
        
        // 如果需要刷新分类
        if (needRefreshCategories || this.data.categories.length === 0 || loginStatusChanged) {
          console.log("需要刷新分类列表");
          this.getClothingCategories();
          app.globalData.needRefreshCategories = false;
        }
        
        // 如果需要刷新衣橱列表
        if (needRefreshWardrobes || this.data.wardrobeList.length === 0) {
          console.log("需要刷新衣橱列表", needRefreshWardrobes ? "（有刷新标志）" : "（无数据需初始化）");
          this.loadWardrobeList(() => {
            // 如果同时需要刷新衣物，在加载衣橱后再加载衣物
            if (needRefreshClothes || this.data.clothes.length === 0 || loginStatusChanged) {
              this.loadClothesData();
            }
          });
          
          // 重置衣橱刷新标志
          app.globalData.needRefreshWardrobes = false;
        }
        // 如果只需要刷新衣物列表
        else if (needRefreshClothes || this.data.clothes.length === 0 || loginStatusChanged) {
          console.log("页面显示，刷新衣物列表", 
            needRefreshClothes ? "（有刷新标志）" : 
            loginStatusChanged ? "（登录状态变化）" : "（无数据需初始化）");
          
          // 如果是登录状态变化，添加短暂延迟确保数据库状态已更新
          if (loginStatusChanged) {
            wx.showLoading({
              title: '加载中...',
            });
            
            // 先加载衣橱列表
            this.loadWardrobeList(() => {
              setTimeout(() => {
                this.loadClothesData();
                wx.hideLoading();
              }, 500);
            });
          } else {
            // 如果wardrobeList为空，先加载衣橱列表
            if (this.data.wardrobeList.length === 0) {
              this.loadWardrobeList(() => {
                this.loadClothesData();
              });
            } else {
              this.loadClothesData();
            }
          }
          
          // 重置衣物刷新标志
          app.globalData.needRefreshClothes = false;
        }
      } else {
        this.checkLoginStatus();
      }
    } else {
      this.setData({
        hasUserInfo: false,
        isUsingMockUser: false
      });
    }
  },
  
  // Check if user is logged in
  checkLoginStatus: function() {
    console.log("index页面检查登录状态", app.globalData.token, app.globalData.userInfo);
    
    // If token doesn't exist, show not-logged-in UI
    if (!app.globalData.token) {
      this.setData({
        hasUserInfo: false
      });
      return;
    }
    
    // 如果已经有用户信息，直接使用
    if (app.globalData.userInfo) {
      this.updateUserDisplay();
    } else {
      // 等待token验证完成
      app.checkTokenValidity(app.globalData.token, (success, userInfo) => {
        if (success) {
          this.updateUserDisplay();
        } else {
          this.setData({
            hasUserInfo: false
          });
        }
      });
    }
  },
  
  // 更新用户显示信息
  updateUserDisplay: function() {
    // 处理字段名称差异 (后端用 nickname, avatar_url，前端用 nickName, avatarUrl)
    const userInfo = {
      id: app.globalData.userInfo.id,
      nickName: app.globalData.userInfo.nickname, // 转换为前端使用的驼峰命名
      avatarUrl: app.globalData.userInfo.avatar_url, // 转换为前端使用的驼峰命名
      gender: app.globalData.userInfo.gender
    };
    
    // 检查是否使用体验账号
    const isUsingMockUser = app.globalData.useMockUser === true;
    
    this.setData({
      userInfo: userInfo,
      hasUserInfo: true,
      isUsingMockUser: isUsingMockUser // 添加标志表示是否使用体验账号
    });
    
    // 显示提示
    if (isUsingMockUser) {
      wx.showToast({
        title: '您正在使用体验账号',
        icon: 'none',
        duration: 2000
      });
    }
    
    // 先加载衣橱列表，再加载衣物数据
    this.loadWardrobeList(() => {
      this.loadClothesData();
    });
  },
  
  // 加载衣橱列表
  loadWardrobeList: function(callback) {
    if (!app.globalData.token) {
      if (typeof callback === 'function') callback();
      return;
    }

    // 构建API URL，添加圈子数据支持
    let url = `${app.globalData.apiBaseUrl}/get_wardrobes.php`;
    if (this.data.dataSource !== 'personal') {
      url += `?include_circle_data=true&data_source=${this.data.dataSource}`;
    }

    wx.request({
      url: url,
      method: 'GET',
      header: {
        'content-type': 'application/json',
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log('获取衣橱列表成功:', res.data);
        if (res.statusCode === 200 && res.data.success) {
          // 添加 "全部衣物" 选项
          const wardrobeList = res.data.data || [];
          
          this.setData({
            wardrobeList: wardrobeList
          });
          
          // 更新当前选中衣橱的名称
          if (this.data.selectedWardrobeId !== null) {
            const selectedWardrobe = wardrobeList.find(item => item.id === this.data.selectedWardrobeId);
            if (selectedWardrobe) {
              this.setData({
                selectedWardrobeName: selectedWardrobe.name
              });
            }
          }
        } else {
          console.error('获取衣橱列表失败:', res);
        }
      },
      fail: (err) => {
        console.error('请求衣橱列表失败:', err);
      },
      complete: () => {
        if (typeof callback === 'function') callback();
      }
    });
  },
  
  // 切换衣橱选择弹出框显示状态
  toggleWardrobePopup: function() {
    this.setData({
      showWardrobePopup: !this.data.showWardrobePopup
    });
  },
  
  // 关闭衣橱选择弹出框
  closeWardrobePopup: function() {
    this.setData({
      showWardrobePopup: false
    });
  },
  
  // 切换衣橱
  switchWardrobe: function(e) {
    const wardrobeId = e.currentTarget.dataset.id;
    const wardrobeName = e.currentTarget.dataset.name || '全部衣橱';
    
    console.log('切换衣橱:', wardrobeId === null ? '全部衣物' : `衣橱ID=${wardrobeId}，名称=${wardrobeName}`);
    
    this.setData({
      selectedWardrobeId: wardrobeId,
      selectedWardrobeName: wardrobeName,
      showWardrobePopup: false, // 选择后关闭弹出框
      currentCategory: 'all', // 切换衣橱时重置到"全部"分类，确保重新计算分类计数
      categoryCountMap: { 'all': 0 } // 重置分类计数映射
    });

    // 如果当前数据源不是个人数据，需要重新加载分类数据以确保分类列表正确
    if (this.data.dataSource !== 'personal') {
      console.log('衣橱切换时重新加载分类数据，当前数据源:', this.data.dataSource);
      this.getClothingCategories(() => {
        // 分类数据加载完成后再加载衣物数据
        this.loadClothesData();
      });
    } else {
      // 个人数据源下直接加载衣物数据
      this.loadClothesData();
    }
  },
  
  // 加载衣物数据
  loadClothesData: function() {
    // 判断是否登录
    if (!app.globalData.token) {
      // 如果未登录且在下拉刷新，停止下拉刷新动画
      if (this.data.isRefreshing) {
        wx.stopPullDownRefresh();
        this.setData({
          isRefreshing: false
        });
      }
      return;
    }
    
    // 设置正在加载标志
    this.setData({
      isLoadingCategory: true
    });
    
    // 记录刷新时间
    this.setData({
      lastRefreshTime: Date.now()
    });
    
    // 显示加载中
    wx.showLoading({
      title: '加载中...',
    });
    
    // 使用实际API获取用户衣物数据
    let url = `${app.globalData.apiBaseUrl}/get_clothes.php`;
    let params = {};

    // 添加分类过滤参数
    if (this.data.currentCategory !== 'all') {
      params.category = this.data.currentCategory;
    }

    // 添加衣橱过滤参数
    if (this.data.selectedWardrobeId !== null) {
      params.wardrobe_id = this.data.selectedWardrobeId;
    }

    // 新增：添加圈子数据参数
    if (this.data.dataSource !== 'personal') {
      params.include_circle_data = 'true';
      params.data_source = this.data.dataSource;
    }
    
    console.log("开始获取衣物数据:");
    console.log("- 数据源:", this.data.dataSource);
    console.log("- 选中衣橱ID:", this.data.selectedWardrobeId);
    console.log("- 选中衣橱名称:", this.data.selectedWardrobeName);
    console.log("- 当前分类:", this.data.currentCategory);
    console.log("- URL:", url);
    console.log("- 参数:", params);
    
    wx.request({
      url: url,
      method: 'GET',
      data: params,
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        // 隐藏加载中
        wx.hideLoading();
        
        console.log("获取衣物数据响应:", res.data);
        console.log("请求参数详情:", {
          dataSource: this.data.dataSource,
          wardrobeId: this.data.selectedWardrobeId,
          category: this.data.currentCategory,
          includeCircleData: params.include_circle_data,
          dataSourceParam: params.data_source
        });

        if (res.statusCode === 200 && !res.data.error) {
          // 转换字段名称 - 后端使用下划线命名，前端使用驼峰命名
          const clothes = res.data.data.map(item => {
            return {
              id: item.id,
              name: item.name,
              category: item.category,
              imageUrl: item.image_url,
              tags: item.tags,
              description: item.description,
              createdAt: item.created_at
            };
          });
          
          console.log("处理后的衣物数据:", clothes);
          
          // 更新对应分类的计数缓存
          const currentCategory = this.data.currentCategory;
          let categoryCountMap = this.data.categoryCountMap;

          // 如果当前是"全部"分类，需要重新计算所有分类的计数
          if (currentCategory === 'all') {
            // 重置所有分类计数
            this.data.categories.forEach(cat => {
              categoryCountMap[cat.value] = 0;
            });

            // 统计每个分类的衣物数量
            clothes.forEach(item => {
              if (item.category) {
                if (categoryCountMap[item.category] !== undefined) {
                  categoryCountMap[item.category]++;
                }
              }
            });

            // 更新"全部"分类的计数
            categoryCountMap['all'] = clothes.length;
          } else {
            // 对于特定分类，只更新当前分类的计数
            categoryCountMap[currentCategory] = clothes.length;

            // 注意：由于衣橱切换时会重置到"全部"分类，这个分支通常不会在衣橱切换后执行
            // 如果执行到这里，说明用户在同一个衣橱内切换分类，只需要更新当前分类的计数即可
          }
          
          this.setData({
            clothes: clothes,
            categoryCountMap: categoryCountMap,
            isLoadingCategory: false
          }, () => {
            // 在衣物数据设置完成后，分配衣物到两列
            this.distributeClothes();
          });

          console.log("衣物数据已更新，当前衣物数量:", clothes.length);
          console.log("分类计数已更新:", categoryCountMap);
          console.log("当前分类:", currentCategory, "当前衣橱:", this.data.selectedWardrobeId);

          // 如果没有衣物数据，显示提示
          if (clothes.length === 0) {
            wx.showToast({
              title: '暂无衣物，快去添加吧',
              icon: 'none',
              duration: 2000
            });
          }
        } else {
          // 处理错误
          console.error("获取衣物失败:", res.data.msg);
          
          this.setData({
            isLoadingCategory: false
          });
          
          wx.showToast({
            title: res.data.msg || '获取衣物失败',
            icon: 'none'
          });
        }
        
        // 如果是下拉刷新触发的加载，停止下拉刷新动画
        if (this.data.isRefreshing) {
          wx.stopPullDownRefresh();
          this.setData({
            isRefreshing: false
          });
        }
      },
      fail: (err) => {
        // 隐藏加载中
        wx.hideLoading();
        
        console.error("请求失败:", err);
        
        this.setData({
          isLoadingCategory: false
        });
        
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        
        // 如果是下拉刷新触发的加载，停止下拉刷新动画
        if (this.data.isRefreshing) {
          wx.stopPullDownRefresh();
          this.setData({
            isRefreshing: false
          });
        }
      }
    });
  },
  
  // 分配衣物到两列，实现瀑布流效果
  distributeClothes: function() {
    // 如果使用CSS Grid布局，则不需要手动分配
    if (this.data.layoutMode >= 3) {
      console.log("使用Grid布局，无需手动分配衣物");
      return;
    }
    
    const clothes = this.data.clothes;
    const leftItems = [];
    const rightItems = [];
    
    // 从左到右、从上到下的顺序分配衣物
    clothes.forEach((item, index) => {
      // 按顺序填充：左1、右1、左2、右2...
      if (index % 2 === 0) {
        leftItems.push(item);
      } else {
        rightItems.push(item);
      }
    });
    
    this.setData({
      leftItems,
      rightItems
    });
  },
  
  // 切换衣物分类
  switchCategory: function(e) {
    const category = e.currentTarget.dataset.category;
    console.log("切换到分类:", category);
    
    // 如果点击的是当前分类，不做任何操作
    if (category === this.data.currentCategory) {
      return;
    }
    
    // 更新当前分类，并设置加载状态
    this.setData({
      currentCategory: category,
      isLoadingCategory: true
    });
    
    // 刷新衣物列表
    this.loadClothesData();
  },



  // 新增：数据源切换相关方法
  toggleDataSourcePopup: function() {
    this.setData({
      showDataSourcePopup: !this.data.showDataSourcePopup
    });
  },

  closeDataSourcePopup: function() {
    this.setData({
      showDataSourcePopup: false
    });
  },

  switchDataSource: function(e) {
    const dataSource = e.currentTarget.dataset.source;
    console.log("切换数据源到:", dataSource);

    // 保存数据源到本地存储
    wx.setStorageSync('dataSource', dataSource);

    this.setData({
      dataSource: dataSource,
      showDataSourcePopup: false,
      isLoadingClothes: true
      // 注释掉重置选择状态，保持用户的筛选选择
      // selectedWardrobeId: null,
      // selectedWardrobeName: '全部衣橱',
      // currentCategory: 'all'
    });

    // 重新加载所有相关数据，确保正确的时序
    this.getClothingCategories(() => { // 先加载分类数据
      this.loadWardrobeList(() => {    // 然后加载衣橱数据
        // 重置分类计数映射，强制重新计算
        this.setData({
          categoryCountMap: { 'all': 0 },
          currentCategory: 'all' // 切换数据源时重置到"全部"分类，确保重新计算所有分类计数
        });
        this.loadClothesData();        // 最后加载衣物数据
      });
    });
  },

  // 查看衣物详情
  viewClothingDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    console.log("查看衣物详情, ID:", id);

    // 跳转到衣物详情页
    wx.navigateTo({
      url: `/pages/clothing/detail/detail?id=${id}`
    });
  },
  
  // 显示上传选项
  showUploadOptions: function() {
    this.setData({
      showUploadOptions: true
    });
  },
  
  // 隐藏上传选项
  hideUploadOptions: function() {
    this.setData({
      showUploadOptions: false
    });
  },
  
  // 单件上传
  singleUpload: function() {
    this.hideUploadOptions();
    
    // 在跳转前记录当前状态
    getApp().globalData.needRefreshClothes = true;
    
    wx.navigateTo({
      url: '/pages/clothing/add/add',
      events: {
        // 监听添加成功事件
        addSuccess: () => {
          // 标记需要刷新
          getApp().globalData.needRefreshClothes = true;
        }
      }
    });
  },
  
  // 批量上传
  batchUpload: function() {
    this.hideUploadOptions();
    
    // 在跳转前记录当前状态
    getApp().globalData.needRefreshClothes = true;
    
    wx.navigateTo({
      url: '/pages/clothing/batch-upload/batch-upload',
      events: {
        // 监听添加成功事件
        addSuccess: () => {
          // 标记需要刷新
          getApp().globalData.needRefreshClothes = true;
        }
      }
    });
  },
  
  // 添加衣物 - 更新为调用弹框
  addClothing: function() {
    // 显示上传选项弹框
    this.showUploadOptions();
  },
  
  // 管理衣橱
  manageCloset: function() {
    wx.showToast({
      title: '衣橱管理功能开发中...',
      icon: 'none'
    });
    // 实际实现时应该跳转到管理页面
    // wx.navigateTo({
    //   url: '/pages/closet/manage'
    // });
  },
  
  // 前往登录页
  goToLogin: function() {
    // 如果是体验账号状态，先清除当前体验账号状态
    if (app.globalData.useMockUser) {
      // 清除体验账号的数据，但不自动生成新的模拟token
      wx.removeStorageSync('token');
      wx.removeStorageSync('userInfo');
      app.globalData.token = null;
      app.globalData.userInfo = null;
      app.globalData.useMockUser = false;
      
      console.log("已退出体验账号状态");
    }
    
    // 跳转到登录页
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },
  
  // 处理头像加载失败
  onAvatarError: function(e) {
    console.log("index页面头像加载失败，使用默认头像");
    this.setData({
      'userInfo.avatarUrl': '/images/default-avatar.png'
    });
  },
  
  // Navigate to different features
  navigateToFeature: function(e) {
    const feature = e.currentTarget.dataset.feature;
    wx.showToast({
      title: '功能开发中...',
      icon: 'none'
    });
  },
  
  // Create a new outfit
  createOutfit: function() {
    wx.showToast({
      title: '功能开发中...',
      icon: 'none'
    });
  },
  
  // 添加管理衣橱功能
  navigateToManage: function() {
    wx.navigateTo({
      url: '/pages/clothing/manage/manage'
    });
  },
  
  // 分享给好友
  onShareAppMessage: function() {
    return {
      title: '次元衣帽间 - 免费无限量衣橱轻松管理',
      path: '/pages/index/index',
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png' // 可自定义分享图片，如无可删除此行
    }
  },
  
  // 分享到朋友圈
  onShareTimeline: function() {
    return {
      title: '次元衣帽间 - 免费无限量衣橱轻松管理',
      query: 'source=timeline&page=index',
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png' // 可自定义分享图片，如无可删除此行
    }
  },
  
  // 跳转到智能穿搭推荐页面 - 修改为显示底部选项弹框
  goToSmartOutfit: function() {
    // 显示智能穿搭选项弹框
    this.setData({
      showOutfitOptions: true
    });
  },
  
  // 关闭智能穿搭选项弹框
  closeOutfitOptions: function() {
    this.setData({
      showOutfitOptions: false
    });
  },
  
  /**
   * 根据选择的类型导航到相应的推荐页面
   */
  navigateToOutfitType: function(e) {
    const type = e.currentTarget.dataset.type;
    console.log('选择的推荐类型:', type);
    this.closeOutfitOptions();
    
    if (type === 'weather') {
      // 导航到按天气推荐页面
      console.log('跳转到按天气推荐页面');
      wx.navigateTo({
        url: '/pages/smart_outfit/smart_outfit'
      });
    } else if (type === 'clothing') {
      // 导航到按衣物推荐页面
      console.log('跳转到按衣物推荐页面');
      wx.navigateTo({
        url: '/pages/clothing_recommendation/clothing_recommendation'
      });
    } else if (type === 'preference') {
      // 导航到按个人喜好推荐页面
      console.log('跳转到按个人喜好推荐页面');
      wx.navigateTo({
        url: '/pages/preference_recommendation/preference_recommendation'
      });
    } else if (type === 'analysis') {
      // 导航到按个人形象分析推荐页面
      console.log('跳转到形象分析历史页面');
      try {
        wx.navigateTo({
          url: '/pages/image_analysis/history/history',
          success: function() {
            console.log('成功跳转到形象分析历史页面');
          },
          fail: function(error) {
            console.error('跳转失败:', error);
            wx.showToast({
              title: '跳转失败: ' + JSON.stringify(error),
              icon: 'none',
              duration: 3000
            });
          }
        });
      } catch (error) {
        console.error('跳转出现异常:', error);
        wx.showToast({
          title: '跳转异常: ' + error.message,
          icon: 'none',
          duration: 3000
        });
      }
    } else {
      // 其他类型暂未开发
      console.log('其他类型功能开发中');
      wx.showToast({
        title: '该功能正在开发中',
        icon: 'none'
      });
    }
  },
  
  // 切换布局模式
  switchLayoutMode: function() {
    // 在2、3、4列布局间循环切换
    let newLayoutMode = this.data.layoutMode === 4 ? 2 : this.data.layoutMode === 2 ? 3 : 4;
    
    this.setData({
      layoutMode: newLayoutMode
    });
    
    // 保存偏好设置
    wx.setStorageSync('preferredLayoutMode', newLayoutMode);
    
    // 显示提示
    wx.showToast({
      title: `已切换为${newLayoutMode}列显示`,
      icon: 'none',
      duration: 1000
    });
  },
  
  // 获取分类列表
  getClothingCategories: function(callback) {
    // 只在用户登录时获取分类
    if (!app.globalData.token) {
      this.setDefaultCategories();
      return;
    }

    // 构建API URL，添加圈子数据支持
    let url = `${app.globalData.apiBaseUrl}/get_clothing_categories.php`;
    if (this.data.dataSource !== 'personal') {
      url += `?include_circle_data=true&data_source=${this.data.dataSource}`;
    }

    console.log('请求分类数据 - 数据源:', this.data.dataSource, 'URL:', url);
    console.log('分类API调用时的完整状态:', {
      dataSource: this.data.dataSource,
      selectedWardrobeId: this.data.selectedWardrobeId,
      selectedWardrobeName: this.data.selectedWardrobeName
    });

    wx.request({
      url: url,
      method: 'GET',
      header: {
        'content-type': 'application/json',
        'Authorization': `Bearer ${app.globalData.token}` // 统一使用Bearer格式
      },
      success: (res) => {
        console.log('获取分类列表响应:', res.data);
        console.log('分类API响应详情:', {
          statusCode: res.statusCode,
          error: res.data.error,
          dataLength: res.data.data ? res.data.data.length : 0,
          dataSource: this.data.dataSource
        });

        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          const categories = res.data.data || [];
          
          // 转换数据格式
          const formattedCategories = categories.map(cat => ({
            name: cat.name,
            value: cat.code,
            id: cat.id,
            is_system: cat.is_system,
            data_source: cat.data_source || 'personal',
            creator_nickname: cat.creator_nickname
          }));

          console.log('格式化后的分类数据:', formattedCategories);

          // 初始化分类计数映射
          let categoryCountMap = this.data.categoryCountMap;
          formattedCategories.forEach(cat => {
            // 如果该分类还没有计数，初始化为0
            if (typeof categoryCountMap[cat.value] === 'undefined') {
              categoryCountMap[cat.value] = 0;
            }
          });

          this.setData({
            categories: formattedCategories,
            categoryCountMap: categoryCountMap
          });

          console.log('分类数据已更新，当前分类列表:', this.data.categories);

          // 调用回调函数
          if (typeof callback === 'function') {
            callback();
          }
        } else {
          console.error('获取分类列表失败:', res);
          this.setDefaultCategories();

          // 即使失败也要调用回调函数
          if (typeof callback === 'function') {
            callback();
          }
        }
      },
      fail: (err) => {
        console.error('请求分类列表失败:', err);
        this.setDefaultCategories();

        // 即使失败也要调用回调函数
        if (typeof callback === 'function') {
          callback();
        }
      }
    });
  },
  
  // 设置默认分类（兼容性处理）
  setDefaultCategories: function() {
    const defaultCategories = [
      { name: '上衣', value: 'tops', is_system: true },
      { name: '裤子', value: 'pants', is_system: true },
      { name: '裙子', value: 'skirts', is_system: true },
      { name: '外套', value: 'coats', is_system: true },
      { name: '鞋子', value: 'shoes', is_system: true },
      { name: '包包', value: 'bags', is_system: true },
      { name: '配饰', value: 'accessories', is_system: true }
    ];
    
    // 初始化分类计数映射
    let categoryCountMap = this.data.categoryCountMap;
    defaultCategories.forEach(cat => {
      // 如果该分类还没有计数，初始化为0
      if (typeof categoryCountMap[cat.value] === 'undefined') {
        categoryCountMap[cat.value] = 0;
      }
    });
    
    this.setData({
      categories: defaultCategories,
      categoryCountMap: categoryCountMap
    });
  },
  
  // 添加跳转到个人形象分析页面的方法
  goToImageAnalysis: function() {
    // 先检查是否登录
    if (!app.globalData.token) {
      // 未登录，跳转到登录页
      wx.showModal({
        title: '提示',
        content: '需要登录才能使用个人形象分析功能',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    // 已登录，直接跳转
    wx.navigateTo({
      url: '/pages/image_analysis/index/index'
    });
  },
  
  // 关闭Banner
  closeBanner: function(e) {
    // 阻止冒泡，避免触发Banner点击事件
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    
    // 判断是否需要阻止默认的点击事件传递
    if (e && e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.preventTap) {
      // 这里不需要做额外处理，只是标记这个点击不应该传递到父元素
    }
    
    // 隐藏Banner
    this.setData({
      hideImageAnalysisBanner: true
    });
    
    // 保存设置到本地存储
    wx.setStorageSync('hideImageAnalysisBanner', true);
    
    // 显示提示
    wx.showToast({
      title: '已隐藏，可在"我的"页面重新查看',
      icon: 'none',
      duration: 2000
    });
  },
  
  // 切换衣物名称的显示状态
  toggleClothingName: function() {
    // 切换名称显示状态
    const newShowState = !this.data.showClothingName;
    
    this.setData({
      showClothingName: newShowState
    });
    
    // 同步到全局数据
    app.globalData.showClothingName = newShowState;
    
    // 保存设置到本地
    wx.setStorageSync('showClothingName', newShowState);
    
    // 显示提示
    wx.showToast({
      title: newShowState ? '已显示衣物名称' : '已隐藏衣物名称',
      icon: 'none',
      duration: 1000
    });
  },
  
  // 跳转到衣橱管理页面
  goToManageWardrobes() {
    wx.navigateTo({
      url: '/pages/wardrobes/index/index',
    });
    this.closeWardrobePopup();
  },

  // 跳转到添加衣橱页面
  goToAddWardrobe() {
    wx.navigateTo({
      url: '/pages/wardrobes/add/add',
    });
    this.closeWardrobePopup();
  },

  // 显示/隐藏搜索弹窗
  toggleSearchPopup() {
    this.setData({
      showSearchPopup: !this.data.showSearchPopup
    });
  },

  // 关闭搜索弹窗
  closeSearchPopup() {
    this.setData({
      showSearchPopup: false
    });
  },

  // 搜索输入变化
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });
    
    // 添加延时搜索，提高性能
    if (this._searchTimer) {
      clearTimeout(this._searchTimer);
    }
    
    this._searchTimer = setTimeout(() => {
      this.searchClothes();
    }, 300);
  },

  // 清空搜索
  clearSearch() {
    this.setData({
      searchKeyword: '',
      searchResults: []
    });
  },

  // 执行搜索
  searchClothes() {
    if (!this.data.searchKeyword.trim()) {
      // 清空搜索结果
      this.setData({
        searchResults: []
      });
      return;
    }

    const keyword = this.data.searchKeyword.trim().toLowerCase();
    console.log('执行搜索，关键词:', keyword);
    
    try {
      // 从现有衣物中搜索
      const searchResults = this.data.clothes.filter(item => {
        if (!item || !item.name) return false;
        return item.name.toLowerCase().includes(keyword);
      });
      
      console.log('搜索结果:', searchResults.length, '条');
      
      this.setData({
        searchResults
      });
    } catch (error) {
      console.error('搜索过程中发生错误:', error);
      wx.showToast({
        title: '搜索出错',
        icon: 'none'
      });
    }
  },

  // 从服务器搜索衣物（示例）
  searchClothesFromServer(keyword) {
    const token = wx.getStorageSync('token');
    if (!token) {
      return;
    }

    wx.request({
      url: app.globalData.baseUrl + '/api/clothes/search',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + token
      },
      data: {
        keyword: keyword,
      },
      success: res => {
        if (res.data.code === 0) {
          this.setData({
            searchResults: res.data.data
          });
        }
      }
    });
  },
  
  // 跳转到穿搭打分页面
  goToOutfitRating: function() {
    if (!app.globalData.token) {
      // 未登录，跳转到登录页
      wx.showModal({
        title: '提示',
        content: '需要登录才能使用穿搭打分功能',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    // 已登录，直接跳转
    wx.navigateTo({
      url: '/pages/outfit_rating/index/index'
    });
  },
  
  // 跳转到面容分析页面
  goToFaceAnalysis: function() {
    if (!app.globalData.token) {
      // 未登录，跳转到登录页
      wx.showModal({
        title: '提示',
        content: '需要登录才能使用面容分析功能',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    // 已登录，直接跳转
    wx.navigateTo({
      url: '/pages/face_analysis/index/index'
    });
  },

  // 页面导航功能
  goToImageAnalysis: function() {
    wx.navigateTo({
      url: '/pages/image_analysis/index/index',
    })
  },

  goToSmartOutfit: function() {
    wx.navigateTo({
      url: '/pages/smart_outfit/index',
    })
  },

  goToOutfitRating: function() {
    wx.navigateTo({
      url: '/pages/outfit_rating/index/index',
    })
  },

  goToFaceAnalysis: function() {
    wx.navigateTo({
      url: '/pages/face_analysis/index/index',
    })
  },

  // 新增导航方法别名，兼容新修改的WXML
  navigateToImageAnalysis: function() {
    this.togglePersonalAnalysisOptions();
  },

  navigateToOutfitRating: function() {
    this.togglePersonalAnalysisOptions();
  },

  navigateToFaceAnalysis: function() {
    this.togglePersonalAnalysisOptions();
  },

  // 显示个人形象分析选项弹窗
  togglePersonalAnalysisOptions: function() {
    this.setData({
      showPersonalAnalysisOptions: !this.data.showPersonalAnalysisOptions
    });
  },
  
  // 关闭个人形象分析选项弹窗
  closePersonalAnalysisOptions: function() {
    this.setData({
      showPersonalAnalysisOptions: false
    });
  },
  
  // 显示智能穿搭选项弹窗
  toggleOutfitOptions: function() {
    this.setData({
      showOutfitOptions: !this.data.showOutfitOptions
    });
  },

  // 关闭智能穿搭选项弹窗
  closeOutfitOptions: function() {
    this.setData({
      showOutfitOptions: false
    });
  },

  // 导航到不同的穿搭功能
  navigateToOutfitType: function(e) {
    const type = e.currentTarget.dataset.type;
    console.log('选择的穿搭类型:', type);
    this.closeOutfitOptions();
    
    // 先检查是否登录
    if (!app.globalData.token) {
      // 未登录，跳转到登录页
      wx.showModal({
        title: '提示',
        content: '需要登录才能使用智能穿搭功能',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    // 根据类型导航到不同页面
    if (type === 'weather') {
      // 天气穿搭
      wx.navigateTo({
        url: '/pages/smart_outfit/smart_outfit'
      });
    } else if (type === 'clothing') {
      // 按衣物推荐
      wx.navigateTo({
        url: '/pages/clothing_recommendation/clothing_recommendation'
      });
    } else if (type === 'preference') {
      // 个人喜好
      wx.navigateTo({
        url: '/pages/preference_recommendation/preference_recommendation'
      });
    } else if (type === 'analysis') {
      // 形象分析推荐
      wx.navigateTo({
        url: '/pages/image_analysis/history/history'
      });
    }
  },
  
  // 导航到不同的分析功能
  navigateToAnalysisType: function(e) {
    const type = e.currentTarget.dataset.type;
    console.log('选择的分析类型:', type);
    this.closePersonalAnalysisOptions();
    
    // 先检查是否登录
    if (!app.globalData.token) {
      // 未登录，跳转到登录页
      wx.showModal({
        title: '提示',
        content: '需要登录才能使用个人形象分析功能',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    // 根据类型导航到不同页面
    if (type === 'image') {
      // 图片分析
      wx.navigateTo({
        url: '/pages/image_analysis/index/index'
      });
    } else if (type === 'outfit') {
      // 穿搭评分
      wx.navigateTo({
        url: '/pages/outfit_rating/index/index'
      });
    } else if (type === 'face') {
      // 面容分析
      wx.navigateTo({
        url: '/pages/face_analysis/index/index'
      });
    } else if (type === 'history') {
      // 历史分析
      wx.navigateTo({
        url: '/pages/image_analysis/history/history'
      });
    }
  },
}) 