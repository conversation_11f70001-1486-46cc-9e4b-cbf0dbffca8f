-- 创建推荐穿搭表
CREATE TABLE IF NOT EXISTS `recommended_outfits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '穿搭名称',
  `image_url` text NOT NULL COMMENT '穿搭图片URL',
  `description` text COMMENT '穿搭描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态: 1=启用, 0=禁用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序，值越小越靠前',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_recommended_outfit_category` (`category_id`),
  KEY `idx_recommended_outfit_status` (`status`),
  KEY `idx_recommended_outfit_sort` (`sort_order`),
  CONSTRAINT `fk_recommended_outfit_category` FOREIGN KEY (`category_id`) REFERENCES `outfit_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建推荐穿搭商品表
CREATE TABLE IF NOT EXISTS `recommended_outfit_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `outfit_id` int(11) NOT NULL COMMENT '关联的推荐穿搭ID',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `image_url` text NOT NULL COMMENT '商品图片URL',
  `price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `purchase_url` text NOT NULL COMMENT '购买链接(淘宝客链接)',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序，值越小越靠前',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_recommended_outfit_item_outfit` (`outfit_id`),
  KEY `idx_recommended_outfit_item_sort` (`sort_order`),
  CONSTRAINT `fk_recommended_outfit_item_outfit` FOREIGN KEY (`outfit_id`) REFERENCES `recommended_outfits` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建推荐穿搭统计表
CREATE TABLE IF NOT EXISTS `recommended_outfit_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `outfit_id` int(11) NOT NULL COMMENT '关联的推荐穿搭ID',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '查看次数',
  `copy_link_count` int(11) NOT NULL DEFAULT '0' COMMENT '复制链接次数',
  `last_viewed_at` datetime DEFAULT NULL COMMENT '最后查看时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `outfit_id` (`outfit_id`),
  CONSTRAINT `fk_recommended_outfit_stats_outfit` FOREIGN KEY (`outfit_id`) REFERENCES `recommended_outfits` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4; 

-- 为推荐穿搭表添加推荐理由字段
ALTER TABLE `recommended_outfits` 
ADD COLUMN `recommendation_reason` text COMMENT '穿搭推荐理由' AFTER `description`;