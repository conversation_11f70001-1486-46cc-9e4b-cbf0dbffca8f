<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入配置和辅助函数
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

// 验证管理员权限
$auth = new Auth();

// 检查是否存在Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证管理员token
$adminData = $auth->verifyAdminToken($token);
if (!$adminData) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的管理员令牌']);
    exit();
}

try {
    // 获取数据库连接
    $db = new Database();
    $pdo = $db->getConnection();
    
    // 获取分页参数
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $per_page = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 20;
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    $status = isset($_GET['status']) ? (int)$_GET['status'] : null;
    $offset = ($page - 1) * $per_page;
    
    // 构建基础查询
    $baseSql = "FROM recommended_outfit_categories WHERE 1=1";
    $params = [];
    
    // 添加搜索条件
    if (!empty($search)) {
        $baseSql .= " AND (name LIKE ? OR description LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    // 添加状态过滤
    if ($status !== null) {
        $baseSql .= " AND status = ?";
        $params[] = $status;
    }
    
    // 获取总记录数
    $countSql = "SELECT COUNT(*) as total " . $baseSql;
    $stmt = $pdo->prepare($countSql);
    
    for ($i = 0; $i < count($params); $i++) {
        $stmt->bindValue($i + 1, $params[$i]);
    }
    
    $stmt->execute();
    $total = (int)$stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 构建获取分类的SQL
    $sql = "SELECT id, name, description, sort_order, status, created_at, updated_at " . $baseSql . " ORDER BY sort_order ASC, name ASC LIMIT ? OFFSET ?";
    $stmt = $pdo->prepare($sql);
    
    // 绑定参数
    for ($i = 0; $i < count($params); $i++) {
        $stmt->bindValue($i + 1, $params[$i]);
    }
    
    $stmt->bindValue(count($params) + 1, $per_page, PDO::PARAM_INT);
    $stmt->bindValue(count($params) + 2, $offset, PDO::PARAM_INT);
    
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 计算总页数
    $total_pages = ceil($total / $per_page);
    
    // 获取每个分类的穿搭数量
    foreach ($categories as &$category) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as outfit_count 
            FROM recommended_outfits
            WHERE category_id = ?
        ");
        $stmt->bindParam(1, $category['id'], PDO::PARAM_INT);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $category['outfit_count'] = (int)$result['outfit_count'];
    }
    
    // 返回成功响应
    http_response_code(200);
    echo json_encode([
        'error' => false,
        'data' => $categories,
        'pagination' => [
            'total' => $total,
            'per_page' => $per_page,
            'current_page' => $page,
            'total_pages' => $total_pages,
            'has_more' => $page < $total_pages
        ]
    ]);
    
} catch (PDOException $e) {
    error_log("Database error in admin_get_recommended_categories.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '数据库错误: ' . $e->getMessage()]);
} catch (Exception $e) {
    error_log("General error in admin_get_recommended_categories.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => '服务器错误: ' . $e->getMessage()]);
} 