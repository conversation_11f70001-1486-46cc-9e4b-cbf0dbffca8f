<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天气推荐穿搭API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="number"],
        textarea {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .form-inline {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .form-inline .form-group {
            flex: 1;
            min-width: 200px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            overflow-x: auto;
            border-radius: 4px;
            border: 1px solid #ddd;
            max-height: 400px;
        }
        .response-container {
            margin-top: 20px;
        }
        .loading {
            text-align: center;
            margin: 20px 0;
            display: none;
        }
        .loading:after {
            content: " .";
            animation: dots 1s steps(5, end) infinite;
        }
        @keyframes dots {
            0%, 20% { content: " ."; }
            40% { content: " .."; }
            60% { content: " ..."; }
            80%, 100% { content: " ...."; }
        }
        .token-input {
            font-family: monospace;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid #ddd;
            border-bottom: none;
            background-color: #f9f9f9;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #fff;
            border-bottom: 2px solid #fff;
            font-weight: bold;
        }
        .tab-content {
            display: none;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 0 4px 4px 4px;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>天气推荐穿搭API测试</h1>
    
    <div class="form-group">
        <label for="auth-token">授权令牌 (Authorization Token):</label>
        <input type="text" id="auth-token" class="token-input" placeholder="Bearer your_token_here" value="Bearer mock_token_123">
    </div>
    
    <div class="tabs">
        <div class="tab active" data-tab="by-coords">按坐标获取</div>
        <div class="tab" data-tab="by-city">按城市ID获取</div>
    </div>
    
    <div id="tab-by-coords" class="tab-content active">
        <div class="form-inline">
            <div class="form-group">
                <label for="latitude">纬度:</label>
                <input type="text" id="latitude" value="30.287458" placeholder="例如: 30.287458">
            </div>
            
            <div class="form-group">
                <label for="longitude">经度:</label>
                <input type="text" id="longitude" value="120.153580" placeholder="例如: 120.153580">
            </div>
            
            <div class="form-group">
                <label for="refresh-coords">换一批:</label>
                <input type="number" id="refresh-coords" value="0" min="0" max="1" step="1">
            </div>
        </div>
        
        <button id="send-coords-request">发送请求</button>
    </div>
    
    <div id="tab-by-city" class="tab-content">
        <div class="form-inline">
            <div class="form-group">
                <label for="city-id">城市ID:</label>
                <input type="text" id="city-id" value="101210101" placeholder="例如: 101210101 (杭州)">
            </div>
            
            <div class="form-group">
                <label for="refresh-city">换一批:</label>
                <input type="number" id="refresh-city" value="0" min="0" max="1" step="1">
            </div>
        </div>
        
        <button id="send-city-request">发送请求</button>
    </div>
    
    <div class="loading" id="loading">处理中</div>
    
    <div class="response-container">
        <h3>响应结果：</h3>
        <pre id="response-output">// 响应将显示在这里</pre>
    </div>
    
    <script>
        // 标签页切换
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有标签和内容的活动状态
                document.querySelectorAll('.tab, .tab-content').forEach(el => {
                    el.classList.remove('active');
                });
                
                // 激活当前标签和对应内容
                this.classList.add('active');
                document.getElementById('tab-' + this.getAttribute('data-tab')).classList.add('active');
            });
        });
        
        // 按坐标发送请求
        document.getElementById('send-coords-request').addEventListener('click', function() {
            sendRequest({
                latitude: document.getElementById('latitude').value,
                longitude: document.getElementById('longitude').value,
                refresh: document.getElementById('refresh-coords').value
            });
        });
        
        // 按城市ID发送请求
        document.getElementById('send-city-request').addEventListener('click', function() {
            sendRequest({
                city_id: document.getElementById('city-id').value,
                refresh: document.getElementById('refresh-city').value
            });
        });
        
        // 发送API请求
        async function sendRequest(params) {
            const responseOutput = document.getElementById('response-output');
            const loadingIndicator = document.getElementById('loading');
            const authToken = document.getElementById('auth-token').value;
            
            try {
                // 显示加载指示器
                loadingIndicator.style.display = 'block';
                responseOutput.textContent = '正在发送请求...';
                
                // 构建查询字符串
                const queryString = Object.entries(params)
                    .filter(([_, value]) => value !== '')
                    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
                    .join('&');
                
                // 发送请求到API
                const response = await fetch(`get_weather_recommendation.php?${queryString}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': authToken
                    }
                });
                
                // 解析响应
                const responseData = await response.json();
                
                // 格式化并显示响应
                responseOutput.textContent = JSON.stringify(responseData, null, 2);
            } catch (error) {
                // 显示错误
                responseOutput.textContent = `错误: ${error.message}`;
                console.error('API调用错误:', error);
            } finally {
                // 隐藏加载指示器
                loadingIndicator.style.display = 'none';
            }
        }
    </script>
</body>
</html> 