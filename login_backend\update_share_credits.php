<?php
header("Content-Type: application/json");
require_once './db.php';
require_once './auth.php';
require_once './config.php';

// 初始化响应数组
$response = [
    'code' => 0,
    'message' => 'success',
    'data' => null
];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['code'] = 405;
    $response['message'] = 'Method Not Allowed';
    echo json_encode($response);
    exit;
}

// 获取POST参数
$postData = json_decode(file_get_contents("php://input"), true);
$token = isset($postData['token']) ? $postData['token'] : '';
$shareStatus = isset($postData['share_status']) ? (int)$postData['share_status'] : null;

if (empty($token) || is_null($shareStatus) || !in_array($shareStatus, [0, 1])) {
    $response['code'] = 400;
    $response['message'] = 'Missing required parameters or invalid share status';
    echo json_encode($response);
    exit;
}

// 验证用户token
$auth = new Auth();
$verifyResult = $auth->verifyToken($token);

if ($verifyResult === false) {
    $response['code'] = 401;
    $response['message'] = '无效或已过期的令牌';
    echo json_encode($response);
    exit;
}

$userId = $verifyResult['sub']; // 从验证结果中获取用户ID
$db = new Database();
$conn = $db->getConnection();

try {
    // 首先检查用户是否为商家
    $checkStmt = $conn->prepare("SELECT merchant_status FROM users WHERE id = :user_id");
    $checkStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkStmt->execute();
    $user = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user || $user['merchant_status'] !== 'yes') {
        $response['code'] = 403;
        $response['message'] = '只有商家才能设置共享试穿点数';
        echo json_encode($response);
        exit;
    }
    
    // 更新共享试穿点数状态
    $stmt = $conn->prepare("UPDATE users SET share_try_on_credits = :share_status, updated_at = NOW() WHERE id = :user_id");
    $stmt->bindParam(':share_status', $shareStatus, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    
    if ($stmt->execute()) {
        $statusText = $shareStatus ? '开启' : '关闭';
        $response['data'] = [
            'share_try_on_credits' => $shareStatus
        ];
        $response['message'] = "成功{$statusText}共享试穿点数";
    } else {
        $response['code'] = 500;
        $response['message'] = '更新共享状态失败';
    }
} catch (Exception $e) {
    $response['code'] = 500;
    $response['message'] = '处理请求时发生错误: ' . $e->getMessage();
} finally {
    // PDO connections are closed automatically when the variable is unset
    $conn = null;
}

echo json_encode($response); 