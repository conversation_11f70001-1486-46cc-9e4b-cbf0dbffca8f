const app = getApp();

Page({
  data: {
    outfits: [],
    loading: true,
    isEmpty: false,
    currentPage: 1,
    hasMoreData: true,
    loadingMore: false,
    searchKeyword: '',  // 搜索关键词
    originalOutfits: [], // 保存原始数据用于搜索恢复
    isSearchMode: false  // 是否处于搜索模式
  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '穿搭广场'
    });
    
    // 加载公开的穿搭
    this.loadPublicOutfits();
    
    // 记录页面第一次加载时间
    this.lastLoadTime = Date.now();
  },
  
  // 搜索框输入
  onSearchInput: function(e) {
    const keyword = e.detail.value.trim();
    this.setData({
      searchKeyword: keyword
    });
    
    // 实时搜索，每次输入都过滤结果
    this.filterOutfitsByKeyword(keyword);
  },
  
  // 搜索确认
  onSearchConfirm: function(e) {
    const keyword = e.detail.value.trim();
    if (keyword) {
      wx.showToast({
        title: '搜索: ' + keyword,
        icon: 'none',
        duration: 1000
      });
      this.filterOutfitsByKeyword(keyword);
    }
  },
  
  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchKeyword: '',
      isSearchMode: false,
      outfits: this.data.originalOutfits,
      isEmpty: this.data.originalOutfits.length === 0
    });
  },
  
  // 根据关键词过滤穿搭
  filterOutfitsByKeyword: function(keyword) {
    // 如果关键词为空，恢复原始数据
    if (!keyword) {
      this.setData({
        isSearchMode: false,
        outfits: this.data.originalOutfits,
        isEmpty: this.data.originalOutfits.length === 0
      });
      return;
    }
    
    // 如果尚未保存原始数据，先保存
    if (!this.data.isSearchMode) {
      this.setData({
        originalOutfits: this.data.outfits,
        isSearchMode: true
      });
    }
    
    // 根据关键词过滤数据
    const filteredOutfits = this.data.originalOutfits.filter(outfit => {
      return outfit.name.toLowerCase().includes(keyword.toLowerCase());
    });
    
    this.setData({
      outfits: filteredOutfits,
      isEmpty: filteredOutfits.length === 0
    });
  },
  
  onShow: function() {
    const currentTime = Date.now();
    const timeElapsed = currentTime - (this.lastLoadTime || 0);
    
    // 如果距上次加载超过5分钟，刷新数据
    if (timeElapsed > 5 * 60 * 1000 || 
        (this.data.outfits.length === 0 && !this.data.loading)) {
      
      console.log('刷新穿搭广场', timeElapsed > 5 * 60 * 1000 ? '超过5分钟' : '空数据');
      
      this.setData({
        currentPage: 1,
        hasMoreData: true
      });
      
      this.loadPublicOutfits();
      this.lastLoadTime = currentTime;
    }
  },
  
  // 加载公开穿搭列表
  loadPublicOutfits: function(isLoadMore = false) {
    console.log('开始加载公开穿搭列表', isLoadMore ? '加载更多' : '刷新');
    
    // 如果是刷新，重置页码
    if (!isLoadMore) {
      this.setData({ 
        loading: true,
        currentPage: 1,
        hasMoreData: true
      });
    } else {
      // 加载更多，设置loadingMore状态
      this.setData({ loadingMore: true });
    }
    
    const page = this.data.currentPage;
    
    // 请求服务器获取公开穿搭列表
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_public_outfits.php`,
      method: 'GET',
      data: {
        page: page,
        per_page: 20
      },
      success: (res) => {
        console.log('获取公开穿搭列表结果:', res.data);
        
        if (res.statusCode === 200 && res.data.success) {
          let publicOutfits = res.data.data || [];
          
          // 处理数据
          this.processOutfitsData(publicOutfits);
          
          // 如果是加载更多，则追加数据，否则替换数据
          let newOutfits = publicOutfits;
          if (isLoadMore && this.data.outfits.length > 0) {
            // 为防止重复，根据id过滤出新数据
            const existingIds = this.data.outfits.map(item => item.id);
            const uniqueNewOutfits = publicOutfits.filter(item => !existingIds.includes(item.id));
            newOutfits = [...this.data.outfits, ...uniqueNewOutfits];
          }
          
          // 如果正在搜索中，需要保存原始数据并重新过滤
          if (this.data.isSearchMode) {
            this.setData({
              originalOutfits: newOutfits
            });
            this.filterOutfitsByKeyword(this.data.searchKeyword);
          } else {
            this.setData({
              outfits: newOutfits,
              originalOutfits: newOutfits
            });
          }
          
          this.setData({
            loading: false,
            isEmpty: this.data.outfits.length === 0,
            loadingMore: false,
            hasMoreData: publicOutfits.length === 20 // 如果返回了满页数据，认为有更多数据
          });
          
          // 如果当前页有数据，页码+1为下次做准备
          if (publicOutfits.length > 0) {
            this.setData({
              currentPage: this.data.currentPage + 1
            });
          }
        } else {
          this.setData({
            loading: false,
            loadingMore: false,
            isEmpty: this.data.outfits.length === 0,
            hasMoreData: false
          });
          
          wx.showToast({
            title: res.data.error || '获取穿搭失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('获取公开穿搭列表失败:', err);
        this.setData({
          loading: false,
          loadingMore: false,
          isEmpty: this.data.outfits.length === 0,
          hasMoreData: false
        });
        
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      }
    });
    
    // 设置超时处理
    setTimeout(() => {
      if (this.data.loading || this.data.loadingMore) {
        console.log('请求超时，取消加载状态');
        this.setData({
          loading: false,
          loadingMore: false
        });
      }
    }, 10000); // 10秒超时
  },
  
  // 处理穿搭数据，确保衣物位置信息正确
  processOutfitsData: function(outfits) {
    if (!outfits || !outfits.length) return;
    
    outfits.forEach(outfit => {
      if (outfit.items && outfit.items.length > 0) {
        // 计算所有衣物的边界框
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
        
        // 首先确保每个衣物项有正确的位置信息，并计算边界
        outfit.items.forEach(item => {
          // 防止位置信息缺失导致显示异常
          if (!item.position) {
            item.position = { x: 50, y: 50 };
          }
          if (!item.size) {
            item.size = { width: 100, height: 120 };
          }
          if (item.rotation === undefined) {
            item.rotation = 0;
          }
          if (item.z_index === undefined) {
            item.z_index = 1;
          }
          
          // 计算衣物的边界
          const left = item.position.x;
          const top = item.position.y;
          const right = left + item.size.width;
          const bottom = top + item.size.height;
          
          // 更新整体边界
          minX = Math.min(minX, left);
          minY = Math.min(minY, top);
          maxX = Math.max(maxX, right);
          maxY = Math.max(maxY, bottom);
        });
        
        // 计算边界框的宽度和高度
        const boundingWidth = maxX - minX;
        const boundingHeight = maxY - minY;
        
        // 计算边界框的中心点
        const centerX = minX + boundingWidth / 2;
        const centerY = minY + boundingHeight / 2;
        
        // 为每个衣物计算预览列表中的居中坐标
        // 假设预览容器的中心点是 (90, 90)，因为outfit-preview大约是180px高宽
        const previewCenterX = 90;
        const previewCenterY = 90;
        
        // 计算最合适的缩放比例，确保整个穿搭都在预览框内
        const maxDimension = Math.max(boundingWidth, boundingHeight);
        const maxAllowedSize = 160; // 预留一些边距
        const scale = maxDimension > 0 ? Math.min(maxAllowedSize / maxDimension, 0.5) : 0.5;
        
        // 为每个衣物添加预览坐标，用于在列表页显示
        outfit.items.forEach(item => {
          item.previewPosition = {
            x: previewCenterX + (item.position.x - centerX) * scale,
            y: previewCenterY + (item.position.y - centerY) * scale,
            scale: scale
          };
        });
      }
      
      // 格式化日期显示
      if (outfit.created_at) {
        try {
          // 处理iOS不兼容的日期格式
          let dateStr = outfit.created_at;
          if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateStr)) {
            dateStr = dateStr.replace(' ', 'T');
          }
          
          const date = new Date(dateStr);
          if (!isNaN(date.getTime())) {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            outfit.formatted_date = `${year}/${month}/${day}`;
          } else {
            outfit.formatted_date = outfit.created_at.split(' ')[0].replace(/-/g, '/');
          }
        } catch (err) {
          console.error('日期格式化错误:', err);
          outfit.formatted_date = outfit.created_at;
        }
      }
    });
    
    return outfits;
  },
  
  // 查看穿搭详情
  viewOutfit: function(e) {
    const outfitId = e.currentTarget.dataset.id;
    const userId = e.currentTarget.dataset.userId;
    
    console.log('查看穿搭详情:', outfitId, '创建者ID:', userId);
    
    wx.navigateTo({
      url: `/pages/outfits/detail/detail?id=${outfitId}&userId=${userId}&fromSquare=true`
    });
  },
  
  // 下拉刷新
  onPullDownRefresh: function() {
    this.setData({
      currentPage: 1,
      hasMoreData: true,
      searchKeyword: '',
      isSearchMode: false
    });
    
    this.loadPublicOutfits();
    wx.stopPullDownRefresh();
  },
  
  // 触底加载更多
  onReachBottom: function() {
    console.log('滚动到底部，是否有更多数据:', this.data.hasMoreData);
    
    // 如果搜索模式下，不加载更多
    if (this.data.isSearchMode) {
      return;
    }
    
    // 如果没有更多数据或者正在加载，不处理
    if (!this.data.hasMoreData || this.data.loading || this.data.loadingMore) {
      console.log('无需加载更多');
      return;
    }
    
    this.loadPublicOutfits(true); // 传入true表示加载更多
  },
  
  // 手动加载更多
  loadMore: function() {
    // 如果搜索模式下，不加载更多
    if (this.data.isSearchMode) {
      return;
    }
    
    // 如果没有更多数据或者正在加载，不处理
    if (!this.data.hasMoreData || this.data.loading || this.data.loadingMore) {
      return;
    }
    
    wx.showToast({
      title: '加载更多...',
      icon: 'loading',
      duration: 500
    });
    
    this.loadPublicOutfits(true);
  },
  
  // 分享功能
  onShareAppMessage: function() {
    return {
      title: "在穿搭广场发现更多精彩搭配！",
      path: "/pages/outfit_square/index/index"
    };
  }
}); 