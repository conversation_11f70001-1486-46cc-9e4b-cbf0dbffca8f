<?php
/**
 * 和风天气API验证工具
 * 
 * 用于验证API配置和连接是否正确
 */

header('Content-Type: text/html; charset=utf-8');
require_once 'config.php';

// 关闭错误显示到浏览器
ini_set('display_errors', 0);

echo <<<HTML
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>和风天气API验证工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow: auto; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .code { font-family: monospace; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>和风天气API验证工具</h1>
HTML;

// 显示当前配置信息
echo "<h2>当前配置</h2>";
echo "<table>";
echo "<tr><th>配置项</th><th>值</th></tr>";
echo "<tr><td>API Key</td><td>" . substr(WEATHER_API_KEY, 0, 5) . "..." . substr(WEATHER_API_KEY, -5) . "</td></tr>";
echo "<tr><td>API Host</td><td>" . WEATHER_API_HOST . "</td></tr>";
echo "<tr><td>API Path</td><td>" . WEATHER_API_PATH . "</td></tr>";
echo "</table>";

// 测试参数
$longitude = '120.21'; // 经度
$latitude = '30.22';   // 纬度

echo "<h2>测试参数</h2>";
echo "<p>经度: $longitude, 纬度: $latitude (注意：和风天气API要求location参数格式为\"经度,纬度\")</p>";

// 测试1: 直接用API KEY作为URL参数
function testApiKeyInUrl($host, $path, $longitude, $latitude, $key) {
    echo "<h2>测试1: API KEY 作为URL参数</h2>";
    
    // 正确的经纬度格式: 经度,纬度
    $locationParam = "$longitude,$latitude";
    
    $queryParams = [
        'location' => $locationParam,
        'key' => $key,
        'lang' => 'zh',
        'unit' => 'm'
    ];
    
    $queryString = http_build_query($queryParams);
    $url = "https://$host$path?$queryString";
    
    echo "<p>请求URL: <code>$url</code></p>";
    
    $headers = [
        'Accept: application/json',
        'User-Agent: Weather API Validator'
    ];
    
    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => 0,
        CURLOPT_HTTPHEADER => $headers
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p>HTTP状态码: <span class=\"" . ($httpCode == 200 ? 'success' : 'error') . "\">$httpCode</span></p>";
    
    if ($error) {
        echo "<p class=\"error\">cURL错误: $error</p>";
    }
    
    if ($response) {
        $result = json_decode($response, true);
        if ($result) {
            echo "<p>响应JSON:</p>";
            echo "<pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
            
            if ($httpCode == 200 && isset($result['now'])) {
                echo "<p class=\"success\">✓ API调用成功!</p>";
                echo "<table>";
                echo "<tr><th>天气信息</th><th>值</th></tr>";
                echo "<tr><td>温度</td><td>" . $result['now']['temp'] . "°C</td></tr>";
                echo "<tr><td>天气状况</td><td>" . $result['now']['text'] . "</td></tr>";
                echo "<tr><td>体感温度</td><td>" . $result['now']['feelsLike'] . "°C</td></tr>";
                echo "<tr><td>风向</td><td>" . $result['now']['windDir'] . " (" . $result['now']['wind360'] . "°)</td></tr>";
                echo "<tr><td>风力等级</td><td>" . $result['now']['windScale'] . "</td></tr>";
                echo "<tr><td>风速</td><td>" . $result['now']['windSpeed'] . " km/h</td></tr>";
                echo "<tr><td>相对湿度</td><td>" . $result['now']['humidity'] . "%</td></tr>";
                echo "<tr><td>降水量</td><td>" . $result['now']['precip'] . " mm</td></tr>";
                echo "<tr><td>气压</td><td>" . $result['now']['pressure'] . " hPa</td></tr>";
                echo "<tr><td>能见度</td><td>" . $result['now']['vis'] . " km</td></tr>";
                echo "<tr><td>云量</td><td>" . $result['now']['cloud'] . "%</td></tr>";
                echo "<tr><td>露点温度</td><td>" . $result['now']['dew'] . "°C</td></tr>";
                echo "</table>";
                
                if (isset($result['updateTime'])) {
                    echo "<p>数据更新时间: " . $result['updateTime'] . "</p>";
                }
            } else {
                echo "<p class=\"error\">✗ API调用失败! 错误信息: " . 
                     (isset($result['error']) ? $result['error']['detail'] : '未知错误') . "</p>";
            }
        } else {
            echo "<p class=\"error\">✗ 无法解析JSON响应</p>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
        }
    } else {
        echo "<p class=\"error\">✗ 没有收到响应</p>";
    }
}

// 测试2: 测试API KEY放在请求头中
function testApiKeyInHeader($host, $path, $longitude, $latitude, $key) {
    echo "<h2>测试2: API KEY 作为请求头</h2>";
    
    $locationParam = "$longitude,$latitude";
    
    $queryParams = [
        'location' => $locationParam,
        'lang' => 'zh',
        'unit' => 'm'
    ];
    
    $queryString = http_build_query($queryParams);
    $url = "https://$host$path?$queryString";
    
    echo "<p>请求URL: <code>$url</code></p>";
    
    $headers = [
        'Accept: application/json',
        'User-Agent: Weather API Validator',
        'X-QW-Api-Key: ' . $key
    ];
    
    echo "<p>使用请求头: <code>X-QW-Api-Key: " . substr($key, 0, 5) . "..." . substr($key, -5) . "</code></p>";
    
    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => 0,
        CURLOPT_HTTPHEADER => $headers
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p>HTTP状态码: <span class=\"" . ($httpCode == 200 ? 'success' : 'error') . "\">$httpCode</span></p>";
    
    if ($error) {
        echo "<p class=\"error\">cURL错误: $error</p>";
    }
    
    if ($response) {
        $result = json_decode($response, true);
        if ($result) {
            echo "<p>响应JSON:</p>";
            echo "<pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
            
            if ($httpCode == 200 && isset($result['now'])) {
                echo "<p class=\"success\">✓ API调用成功!</p>";
                echo "<p>温度: " . $result['now']['temp'] . "°C, 天气: " . $result['now']['text'] . "</p>";
            } else {
                echo "<p class=\"error\">✗ API调用失败!</p>";
            }
        } else {
            echo "<p class=\"error\">✗ 无法解析JSON响应</p>";
        }
    } else {
        echo "<p class=\"error\">✗ 没有收到响应</p>";
    }
}

// 测试3: 测试地理编码API
function testGeoApi($host, $longitude, $latitude, $key) {
    echo "<h2>测试3: 地理编码API</h2>";
    
    // 为专属API构建正确的地理编码API域名
    if (strpos($host, 're.qweatherapi.com') !== false) {
        $prefix = substr($host, 0, strpos($host, '.'));
        $geoHost = 'geo-' . $prefix . '.re.qweatherapi.com';
    } else {
        $geoHost = "geoapi.qweather.com";
    }
    
    echo "<p>使用地理编码API Host: <code>$geoHost</code></p>";
    
    $locationParam = "$longitude,$latitude";
    $queryParams = [
        'location' => $locationParam,
        'key' => $key,
        'lang' => 'zh'
    ];
    
    $queryString = http_build_query($queryParams);
    $url = "https://$geoHost/v2/city/lookup?$queryString";
    
    echo "<p>请求URL: <code>$url</code></p>";
    
    $headers = [
        'Accept: application/json',
        'User-Agent: Weather API Validator'
    ];
    
    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => 0,
        CURLOPT_HTTPHEADER => $headers
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p>HTTP状态码: <span class=\"" . ($httpCode == 200 ? 'success' : 'error') . "\">$httpCode</span></p>";
    
    if ($error) {
        echo "<p class=\"error\">cURL错误: $error</p>";
    }
    
    if ($response) {
        $result = json_decode($response, true);
        if ($result) {
            echo "<p>响应JSON:</p>";
            echo "<pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
            
            if ($httpCode == 200 && isset($result['location']) && !empty($result['location'])) {
                echo "<p class=\"success\">✓ 地理编码API调用成功!</p>";
                echo "<table>";
                echo "<tr><th>地理位置信息</th><th>值</th></tr>";
                echo "<tr><td>城市名</td><td>" . $result['location'][0]['name'] . "</td></tr>";
                echo "<tr><td>ID</td><td>" . $result['location'][0]['id'] . "</td></tr>";
                echo "<tr><td>经度</td><td>" . $result['location'][0]['lon'] . "</td></tr>";
                echo "<tr><td>纬度</td><td>" . $result['location'][0]['lat'] . "</td></tr>";
                echo "<tr><td>行政区</td><td>" . $result['location'][0]['adm2'] . "</td></tr>";
                echo "<tr><td>省/州</td><td>" . $result['location'][0]['adm1'] . "</td></tr>";
                echo "<tr><td>国家</td><td>" . $result['location'][0]['country'] . "</td></tr>";
                echo "</table>";
            } else {
                echo "<p class=\"error\">✗ 地理编码API调用失败!</p>";
            }
        } else {
            echo "<p class=\"error\">✗ 无法解析JSON响应</p>";
        }
    } else {
        echo "<p class=\"error\">✗ 没有收到响应</p>";
    }
}

// 测试4: 测试本地get_weather.php接口
function testLocalApi($longitude, $latitude) {
    echo "<h2>测试4: 本地get_weather.php接口</h2>";
    
    $url = API_DOMAIN . "/login_backend/get_weather.php?longitude=$longitude&latitude=$latitude&_nocache=" . time();
    
    echo "<p>请求URL: <code>$url</code></p>";
    
    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => 0
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p>HTTP状态码: <span class=\"" . ($httpCode == 200 ? 'success' : 'error') . "\">$httpCode</span></p>";
    
    if ($error) {
        echo "<p class=\"error\">cURL错误: $error</p>";
    }
    
    if ($response) {
        $result = json_decode($response, true);
        if ($result) {
            echo "<p>响应JSON:</p>";
            echo "<pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
            
            if (isset($result['success']) && $result['success'] && isset($result['data'])) {
                echo "<p class=\"success\">✓ 本地API调用成功!</p>";
                echo "<p>温度: " . $result['data']['temp'] . "°C, 天气: " . $result['data']['text'] . ", 城市: " . $result['data']['city'] . "</p>";
                
                echo "<p>检查是否使用2025年的静态数据:</p>";
                if (isset($result['data']['obsTime'])) {
                    $obsTime = $result['data']['obsTime'];
                    if (strpos($obsTime, '2025') !== false) {
                        echo "<p class=\"error\">✗ 使用的是2025年的静态数据: $obsTime</p>";
                    } else {
                        echo "<p class=\"success\">✓ 使用的是当前日期的数据: $obsTime</p>";
                    }
                }
            } else {
                echo "<p class=\"error\">✗ 本地API调用失败!</p>";
            }
        } else {
            echo "<p class=\"error\">✗ 无法解析JSON响应</p>";
        }
    } else {
        echo "<p class=\"error\">✗ 没有收到响应</p>";
    }
}

// 执行测试
testApiKeyInUrl(WEATHER_API_HOST, WEATHER_API_PATH, $longitude, $latitude, WEATHER_API_KEY);
testApiKeyInHeader(WEATHER_API_HOST, WEATHER_API_PATH, $longitude, $latitude, WEATHER_API_KEY);
testGeoApi(WEATHER_API_HOST, $longitude, $latitude, WEATHER_API_KEY);
testLocalApi($longitude, $latitude);

echo "<h2>建议修复</h2>";
echo "<ol>";
echo "<li>确保location参数格式正确: <code>经度,纬度</code>，而不是<code>纬度,经度</code></li>";
echo "<li>使用专属API Host: <code>" . WEATHER_API_HOST . "</code>，而不是<code>devapi.qweather.com</code></li>";
echo "<li>如果需要使用地理编码API，正确的Host应该是: <code>geo-" . substr(WEATHER_API_HOST, 0, strpos(WEATHER_API_HOST, '.')) . ".re.qweatherapi.com</code></li>";
echo "<li>确认API KEY使用正确: <code>" . substr(WEATHER_API_KEY, 0, 5) . "..." . substr(WEATHER_API_KEY, -5) . "</code></li>";
echo "</ol>";

echo <<<HTML
</body>
</html>
HTML;
?> 