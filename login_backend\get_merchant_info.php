<?php
header("Content-Type: application/json");
require_once './db.php';
require_once './auth.php';
require_once './config.php';

// 初始化响应数组
$response = [
    'code' => 0,
    'message' => 'success',
    'data' => null
];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['code'] = 405;
    $response['message'] = 'Method Not Allowed';
    echo json_encode($response);
    exit;
}

// 获取POST参数
$postData = json_decode(file_get_contents("php://input"), true);
$token = isset($postData['token']) ? $postData['token'] : '';
$merchantId = isset($postData['merchant_id']) ? intval($postData['merchant_id']) : 0;

// 处理 demo_mode 参数
$demoMode = false;
if (isset($postData['demo_mode'])) {
    // 支持各种可能的true值: true, "true", "1", 1
    if ($postData['demo_mode'] === true || 
        $postData['demo_mode'] === "true" || 
        $postData['demo_mode'] === "1" || 
        $postData['demo_mode'] === 1 ||
        $postData['demo_mode'] == true) {
        $demoMode = true;
    }
}

if (empty($token) && !$demoMode) {
    $response['code'] = 400;
    $response['message'] = 'Missing required parameters';
    echo json_encode($response);
    exit;
}

if ($merchantId <= 0) {
    $response['code'] = 400;
    $response['message'] = 'Missing required parameters';
    echo json_encode($response);
    exit;
}

// 验证用户token，除非是体验模式
$userId = null;
if (!$demoMode) {
    $auth = new Auth();
    $verifyResult = $auth->verifyToken($token);

    if (!$verifyResult) {
        $response['code'] = 401;
        $response['message'] = '无效或已过期的令牌';
        echo json_encode($response);
        exit;
    }
    
    $userId = $verifyResult['sub'];
} else {
    // 在体验模式下，使用默认用户ID
    $userId = 1;
}

$db = new Database();
$conn = $db->getConnection();

try {
    // 获取商家信息
    $stmt = $conn->prepare("
        SELECT id, openid, nickname, avatar_url, gender, merchant_status, share_try_on_credits, paid_try_on_count
        FROM users 
        WHERE id = :merchant_id AND merchant_status = 'yes'
    ");
    $stmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_INT);
    $stmt->execute();
    
    $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($merchant) {
        // 移除敏感信息
        unset($merchant['openid']);
        
        $response['data'] = $merchant;
    } else {
        $response['code'] = 404;
        $response['message'] = '商家不存在或未入驻';
    }
} catch (Exception $e) {
    $response['code'] = 500;
    $response['message'] = '处理请求时发生错误: ' . $e->getMessage();
} finally {
    // PDO不需要手动关闭连接
    $conn = null;
}

echo json_encode($response); 