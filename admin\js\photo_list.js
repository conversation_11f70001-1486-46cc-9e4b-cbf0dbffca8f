/**
 * 照片管理列表脚本
 */
const PhotoList = {
    // 初始化参数
    page: 1,
    perPage: 10,
    search: '',
    sortBy: 'id',
    sortOrder: 'desc',
    type: '',
    userId: '',
    
    // DOM元素
    elements: {},
    
    // 内部图片查看器
    imageViewer: {
        // 查看器元素
        viewer: null,
        image: null,
        closeBtn: null,
        isInitialized: false,
        
        // 初始化查看器
        init: function() {
            if (this.isInitialized) return;
            
            // 创建查看器DOM
            this.viewer = document.createElement('div');
            this.viewer.className = 'image-viewer';
            this.viewer.style.cssText = 'position:fixed; top:0; left:0; width:100%; height:100%; ' +
                'background-color:rgba(0,0,0,0.9); z-index:9999; display:none; ' +
                'align-items:center; justify-content:center;';
            
            // 创建图片元素
            this.image = document.createElement('img');
            this.image.className = 'viewer-image';
            this.image.style.cssText = 'max-width:90%; max-height:90%; object-fit:contain;';
            this.viewer.appendChild(this.image);
            
            // 创建关闭按钮
            this.closeBtn = document.createElement('button');
            this.closeBtn.innerHTML = '&times;';
            this.closeBtn.style.cssText = 'position:absolute; top:15px; right:20px; ' +
                'background:none; border:none; color:white; font-size:30px; ' +
                'cursor:pointer; z-index:10000;';
            this.viewer.appendChild(this.closeBtn);
            
            // 添加到页面
            document.body.appendChild(this.viewer);
            
            // 绑定事件
            this.closeBtn.addEventListener('click', () => this.close());
            this.viewer.addEventListener('click', (e) => {
                if (e.target === this.viewer) this.close();
            });
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') this.close();
            });
            
            this.isInitialized = true;
            console.log('内部图片查看器初始化成功');
        },
        
        // 显示图片
        show: function(imageUrl) {
            if (!this.isInitialized) this.init();
            
            this.image.src = imageUrl;
            this.image.onload = () => {
                this.viewer.style.display = 'flex';
                document.body.style.overflow = 'hidden';
            };
            this.image.onerror = () => {
                console.error('图片加载失败:', imageUrl);
                alert('图片加载失败');
            };
        },
        
        // 关闭查看器
        close: function() {
            if (!this.isInitialized) return;
            this.viewer.style.display = 'none';
            document.body.style.overflow = '';
        },
        
        // 绑定图片点击事件
        bindImages: function(selector) {
            if (!this.isInitialized) this.init();
            
            const images = document.querySelectorAll(selector);
            console.log(`为${images.length}个元素绑定内部图片查看器`);
            
            images.forEach(img => {
                if (!img.dataset.hasInternalViewer) {
                    img.style.cursor = 'pointer';
                    
                    img.addEventListener('click', (event) => {
                        event.preventDefault();
                        event.stopPropagation();
                        
                        const imageUrl = img.getAttribute('data-origin') || img.src;
                        this.show(imageUrl);
                    });
                    
                    img.dataset.hasInternalViewer = 'true';
                }
            });
        }
    },
    
    /**
     * 初始化
     */
    init: function() {
        // 初始化DOM元素引用
        this.elements = {
            photoTableBody: document.getElementById('photoTableBody'),
            pagination: document.getElementById('pagination'),
            pageInfo: document.getElementById('pageInfo'),
            prevBtn: document.getElementById('prevBtn'),
            nextBtn: document.getElementById('nextBtn'),
            searchInput: document.getElementById('searchInput'),
            searchBtn: document.getElementById('searchBtn'),
            typeFilter: document.getElementById('typeFilter'),
            userFilter: document.getElementById('userFilter')
        };
        
        // 从URL获取用户ID
        const urlParams = new URLSearchParams(window.location.search);
        const userIdFromUrl = urlParams.get('user_id');
        if (userIdFromUrl) {
            this.userId = userIdFromUrl;
        }
        
        // 初始化内部图片查看器
        this.imageViewer.init();
        
        // 加载用户列表
        this.loadUsers();
        
        // 加载照片数据
        this.loadPhotos();
        
        // 绑定事件
        this.bindEvents();
    },
    
    /**
     * 绑定DOM事件
     */
    bindEvents: function() {
        // 上一页
        this.elements.prevBtn.addEventListener('click', () => {
            if (this.page > 1) {
                this.page--;
                this.loadPhotos();
            }
        });
        
        // 下一页
        this.elements.nextBtn.addEventListener('click', () => {
            this.page++;
            this.loadPhotos();
        });
        
        // 搜索按钮
        this.elements.searchBtn.addEventListener('click', () => {
            this.search = this.elements.searchInput.value.trim();
            this.page = 1; // 重置为第一页
            this.loadPhotos();
        });
        
        // 回车搜索
        this.elements.searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.elements.searchBtn.click();
            }
        });
        
        // 类型筛选
        this.elements.typeFilter.addEventListener('change', () => {
            this.type = this.elements.typeFilter.value;
            this.page = 1; // 重置为第一页
            this.loadPhotos();
        });
        
        // 用户筛选
        this.elements.userFilter.addEventListener('change', () => {
            this.userId = this.elements.userFilter.value;
            this.page = 1; // 重置为第一页
            this.loadPhotos();
        });
    },
    
    /**
     * 加载用户列表
     */
    loadUsers: function() {
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        fetch(`${Auth.apiBaseUrl}/get_admin_users.php`, {
            method: 'GET',
            headers: {
                'Authorization': token
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '加载用户失败');
            }
            
            // 更新用户选择器
            this.updateUserFilter(data.data);
        })
        .catch(error => {
            console.error('获取用户列表失败:', error);
        });
    },
    
    /**
     * 更新用户筛选下拉框
     * @param {Array} users 用户数据
     */
    updateUserFilter: function(users) {
        if (!users || !users.length) return;
        
        let html = '<option value="">所有用户</option>';
        users.forEach(user => {
            const selected = user.id.toString() === this.userId ? 'selected' : '';
            html += `<option value="${user.id}" ${selected}>${user.nickname || '用户' + user.id}</option>`;
        });
        
        this.elements.userFilter.innerHTML = html;
    },
    
    /**
     * 加载照片数据
     */
    loadPhotos: function() {
        // 显示加载状态
        this.elements.photoTableBody.innerHTML = '<tr><td colspan="7" class="no-data">加载中...</td></tr>';
        
        // 构建查询参数
        const params = new URLSearchParams({
            page: this.page,
            per_page: this.perPage,
            sort_by: this.sortBy,
            sort_order: this.sortOrder
        });
        
        // 添加搜索参数
        if (this.search) {
            params.append('search', this.search);
        }
        
        // 添加类型筛选
        if (this.type) {
            params.append('type', this.type);
        }
        
        // 添加用户筛选
        if (this.userId) {
            params.append('user_id', this.userId);
        }
        
        // 获取管理员Token
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        // 请求API
        fetch(`${Auth.apiBaseUrl}/get_admin_photos.php?${params.toString()}`, {
            method: 'GET',
            headers: {
                'Authorization': token
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '加载失败');
            }
            
            this.renderPhotos(data.data, data.pagination);
        })
        .catch(error => {
            console.error('获取照片列表失败:', error);
            this.elements.photoTableBody.innerHTML = `<tr><td colspan="7" class="no-data">加载失败: ${error.message}</td></tr>`;
        });
    },
    
    /**
     * 渲染照片列表
     * @param {Array} photos 照片数据
     * @param {Object} pagination 分页信息
     */
    renderPhotos: function(photos, pagination) {
        // 处理空数据
        if (!photos || photos.length === 0) {
            this.elements.photoTableBody.innerHTML = '<tr><td colspan="7" class="no-data">暂无照片数据</td></tr>';
            this.updatePagination(pagination);
            return;
        }
        
        // 构建表格HTML
        let html = '';
        photos.forEach(photo => {
            const typeText = this.getTypeText(photo.type);
            const description = photo.description || '无描述';
            
            // 默认图片
            const defaultImg = 'images/default-photo.png';
            
            // 检查图片URL并处理微信临时文件路径
            let imageUrl;
            if (!photo.image_url || photo.image_url.trim() === '') {
                imageUrl = defaultImg;
            } else if (photo.image_url.startsWith('wxfile://')) {
                // 不支持的微信临时文件URL，使用默认图片
                imageUrl = defaultImg;
            } else {
                imageUrl = photo.image_url;
            }
            
            // 用户头像
            const defaultAvatar = 'images/default-avatar.png';
            
            // 检查头像URL并处理微信临时文件路径
            let userAvatar;
            if (!photo.user_avatar || photo.user_avatar.trim() === '') {
                userAvatar = defaultAvatar;
            } else if (photo.user_avatar.startsWith('wxfile://')) {
                // 不支持的微信临时文件URL，使用默认头像
                userAvatar = defaultAvatar;
            } else {
                userAvatar = photo.user_avatar;
            }
            
            html += `
                <tr>
                    <td>${photo.id}</td>
                    <td><img src="${imageUrl}" class="photo-image" alt="照片" title="点击查看大图" onerror="this.src='${defaultImg}'" style="cursor: pointer;"></td>
                    <td><span class="type-badge">${typeText}</span></td>
                    <td>${description}</td>
                    <td>
                        <div class="user-info">
                            <img src="${userAvatar}" class="user-avatar" alt="用户头像" onerror="this.src='${defaultAvatar}'">
                            <span>${photo.user_nickname || '用户' + photo.user_id}</span>
                        </div>
                    </td>
                    <td>${this.formatDate(photo.created_at)}</td>
                    <td>
                        <button class="view-btn" onclick="PhotoList.viewPhoto(${photo.id})">查看</button>
                        <button class="edit-btn" onclick="PhotoList.editPhoto(${photo.id})">编辑</button>
                        <button class="delete-btn" onclick="PhotoList.deletePhoto(${photo.id})">删除</button>
                    </td>
                </tr>
            `;
        });
        
        this.elements.photoTableBody.innerHTML = html;
        this.updatePagination(pagination);
        
        // 绑定照片大图预览
        try {
            console.log('使用内部图片查看器绑定图片预览');
            this.imageViewer.bindImages('.photo-image');
            console.log('成功绑定图片预览');
        } catch (error) {
            console.error('绑定图片预览失败:', error);
        }
    },
    
    /**
     * 更新分页控件
     * @param {Object} pagination 分页信息
     */
    updatePagination: function(pagination) {
        if (!pagination) return;
        
        this.elements.pageInfo.textContent = `第 ${pagination.current_page}/${pagination.total_pages} 页`;
        this.elements.prevBtn.disabled = pagination.current_page <= 1;
        this.elements.nextBtn.disabled = pagination.current_page >= pagination.total_pages;
    },
    
    /**
     * 添加照片
     */
    addPhoto: function() {
        window.location.href = 'photo_edit.html';
    },
    
    /**
     * 查看照片详情
     * @param {Number} photoId 照片ID
     */
    viewPhoto: function(photoId) {
        window.location.href = `photo_details.html?id=${photoId}`;
    },
    
    /**
     * 编辑照片
     * @param {Number} photoId 照片ID
     */
    editPhoto: function(photoId) {
        window.location.href = `photo_edit.html?id=${photoId}`;
    },
    
    /**
     * 删除照片
     * @param {Number} photoId 照片ID
     */
    deletePhoto: function(photoId) {
        if (!confirm('确定要删除该照片吗？此操作不可恢复。')) {
            return;
        }
        
        const token = Auth.getToken();
        if (!token) {
            Auth.logout();
            return;
        }
        
        fetch(`${Auth.apiBaseUrl}/delete_admin_photo.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': token
            },
            body: JSON.stringify({
                id: photoId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.msg || '操作失败');
            }
            
            alert(data.msg || '删除成功');
            this.loadPhotos(); // 重新加载照片列表
        })
        .catch(error => {
            console.error('删除照片失败:', error);
            alert(`操作失败: ${error.message}`);
        });
    },
    
    /**
     * 获取类型文本
     * @param {String} type 类型
     * @returns {String} 类型文本
     */
    getTypeText: function(type) {
        const typeMap = {
            'full': '全身照',
            'half': '半身照',
            'other': '其他'
        };
        
        return typeMap[type] || type;
    },
    
    /**
     * 格式化日期显示
     * @param {String} dateStr 日期字符串
     * @returns {String} 格式化的日期
     */
    formatDate: function(dateStr) {
        if (!dateStr) return '未知';
        
        const date = new Date(dateStr);
        return isNaN(date.getTime()) 
            ? dateStr 
            : date.getFullYear() + '-' + 
              String(date.getMonth() + 1).padStart(2, '0') + '-' + 
              String(date.getDate()).padStart(2, '0') + ' ' +
              String(date.getHours()).padStart(2, '0') + ':' +
              String(date.getMinutes()).padStart(2, '0');
    }
};

// 文档加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    PhotoList.init();
}); 