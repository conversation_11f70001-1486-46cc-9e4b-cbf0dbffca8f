.container {
  padding: 0;
  background-color: #f7f7f7;
  min-height: 100vh;
  position: relative;
  padding-bottom: 90px; /* 为底部固定按钮留出空间 */
}

.header {
  background: linear-gradient(to right, #111111, #444444);
  color: #fff;
  padding: 20px;
  text-align: center;
}

.title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
}

.subtitle {
  font-size: 14px;
  opacity: 0.9;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  padding: 15px 20px 10px;
  color: #333;
}

.donation-list {
  background-color: #fff;
  margin: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
  padding-bottom: 10px;
}

.donation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f1f1f1;
}

.donation-item:last-child {
  border-bottom: none;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
  background-color: #f0f0f0;
}

.info-content {
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 15px;
  color: #333;
  margin-bottom: 3px;
}

.time {
  font-size: 12px;
  color: #999;
}

.amount {
  font-size: 16px;
  font-weight: bold;
  color: #ff6b6b;
}

.load-more {
  text-align: center;
  padding: 15px 0;
}

.load-text {
  font-size: 14px;
  color: #333;
}

.no-more {
  text-align: center;
  padding: 15px 0;
}

.no-more-text {
  font-size: 14px;
  color: #999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 20px;
  margin-top: 20px;
}

.empty-image {
  width: 150px;
  height: 150px;
  margin-bottom: 20px;
}

.empty-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4286f4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #666;
}

/* 底部打赏按钮 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 20px;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
}

.donation-btn {
  width: 100%;
  height: 46px;
  background: linear-gradient(to right, #111111, #444444);
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  border-radius: 23px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 打赏弹窗 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
}

.popup-content {
  position: fixed;
  bottom: -100%;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 20px;
  z-index: 1001;
  transition: all 0.3s ease;
  max-height: 80vh;
  overflow-y: auto;
}

.popup-content.show {
  bottom: 0;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.popup-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.popup-close {
  font-size: 24px;
  color: #999;
  padding: 5px;
}

.amount-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 20px;
}

.amount-option {
  width: 48%;
  height: 70px;
  border: 1px solid #e1e1e1;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
}

.amount-option.selected {
  border-color: #333;
  background-color: rgba(0,0,0,0.05);
}

.amount-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.amount-desc {
  font-size: 12px;
  color: #999;
}

.custom-amount {
  margin-bottom: 20px;
}

.amount-input {
  width: 100%;
  height: 46px;
  border: 1px solid #e1e1e1;
  border-radius: 8px;
  padding: 0 15px;
  font-size: 16px;
}

.donation-action-btn {
  height: 46px;
  background: linear-gradient(to right, #111111, #444444);
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  border-radius: 23px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.donation-action-btn.disabled {
  background: #ccc;
  color: #fff;
} 