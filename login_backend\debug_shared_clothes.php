<?php
/**
 * 调试共享衣物查询问题
 * 检查为什么共享衣物无法正确加载
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'] ?? $payload['sub'] ?? null;

if (!$userId) {
    echo "错误：无法获取用户ID\n";
    exit;
}

try {
    echo "=== 共享衣物查询调试 ===\n";
    echo "当前用户ID: $userId\n";
    echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";
    
    $db = new Database();
    $conn = $db->getConnection();
    
    // 1. 检查用户圈子状态
    echo "1. 用户圈子状态检查:\n";
    $stmt = $conn->prepare("
        SELECT cm.circle_id, cm.role, cm.status, c.name as circle_name
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $circles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($circles)) {
        echo "❌ 用户不在任何圈子中\n";
    } else {
        foreach ($circles as $circle) {
            $status = $circle['status'] === 'active' ? '✅' : '❌';
            echo "- 圈子: {$circle['circle_name']} (ID: {$circle['circle_id']}) - 角色: {$circle['role']} - 状态: {$circle['status']} $status\n";
        }
    }
    
    $activeCircles = array_filter($circles, function($c) { return $c['status'] === 'active'; });
    $activeCircleIds = array_column($activeCircles, 'circle_id');
    
    // 2. 检查共享衣物数据
    echo "\n2. 共享衣物数据检查:\n";
    
    if (empty($activeCircleIds)) {
        echo "❌ 没有活跃的圈子，无法查询共享衣物\n";
    } else {
        $circleIdList = implode(',', $activeCircleIds);
        
        // 查询圈子中的所有衣物
        $stmt = $conn->prepare("
            SELECT c.id, c.name, c.user_id, c.circle_id, u.nickname as creator_nickname,
                   CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
            FROM clothes c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE c.circle_id IN ($circleIdList)
            ORDER BY c.created_at DESC
            LIMIT 10
        ");
        $stmt->execute();
        $sharedClothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "圈子中的衣物 (前10个):\n";
        foreach ($sharedClothes as $item) {
            $isOwn = $item['user_id'] == $userId ? '[自己的]' : '[他人的]';
            echo "- ID: {$item['id']}, 名称: {$item['name']}, 创建者: {$item['creator_nickname']} $isOwn\n";
        }
        
        // 统计
        $stmt = $conn->prepare("
            SELECT COUNT(*) as total,
                   COUNT(CASE WHEN user_id = :user_id THEN 1 END) as own,
                   COUNT(CASE WHEN user_id != :user_id THEN 1 END) as others
            FROM clothes 
            WHERE circle_id IN ($circleIdList)
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "\n统计: 总数 {$stats['total']}, 自己的 {$stats['own']}, 他人的 {$stats['others']}\n";
    }
    
    // 3. 测试不同数据源的查询
    echo "\n3. 测试不同数据源查询:\n";
    
    $testCases = [
        ['include_circle_data' => false, 'data_source' => 'personal', 'label' => '个人数据（兼容模式）'],
        ['include_circle_data' => true, 'data_source' => 'personal', 'label' => '个人数据'],
        ['include_circle_data' => true, 'data_source' => 'shared', 'label' => '共享数据'],
        ['include_circle_data' => true, 'data_source' => 'all', 'label' => '全部数据']
    ];
    
    foreach ($testCases as $testCase) {
        echo "\n测试: {$testCase['label']}\n";
        
        $includeCircleData = $testCase['include_circle_data'];
        $dataSource = $testCase['data_source'];
        
        if ($includeCircleData) {
            // 新功能：包含圈子数据的查询
            $sql = "SELECT c.id, c.name, c.category, c.image_url, c.tags, c.description, c.created_at, c.wardrobe_id,
                           c.circle_id, u.nickname as creator_nickname,
                           CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                    FROM clothes c
                    LEFT JOIN users u ON c.user_id = u.id
                    WHERE ";

            // 根据数据源参数构建WHERE条件
            if ($dataSource === 'personal') {
                $sql .= "c.user_id = :user_id AND c.circle_id IS NULL";
                $params = ['user_id' => $userId];
            } elseif ($dataSource === 'shared') {
                // 查询用户所在圈子的共享数据（排除用户自己的数据）
                $sql .= "c.circle_id IS NOT NULL AND c.user_id != :user_id AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')";
                $params = ['user_id' => $userId];
            } else { // $dataSource === 'all'
                // 查询个人数据 + 圈子共享数据
                $sql .= "((c.user_id = :user_id AND c.circle_id IS NULL) OR
                         (c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')))";
                $params = ['user_id' => $userId];
            }
        } else {
            // 原有逻辑：只查询个人数据
            $sql = "SELECT id, name, category, image_url, tags, description, created_at, wardrobe_id FROM clothes WHERE user_id = :user_id";
            $params = ['user_id' => $userId];
        }
        
        $sql .= " ORDER BY c.created_at DESC LIMIT 5";
        
        echo "SQL: $sql\n";
        echo "参数: " . json_encode($params) . "\n";
        
        try {
            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "结果数量: " . count($results) . "\n";
            foreach ($results as $item) {
                $dataSourceLabel = isset($item['data_source']) ? "[{$item['data_source']}]" : '[unknown]';
                echo "- $dataSourceLabel {$item['name']} (ID: {$item['id']})\n";
            }
        } catch (Exception $e) {
            echo "❌ 查询失败: " . $e->getMessage() . "\n";
        }
    }
    
    // 4. 测试特定ID查询
    if (!empty($sharedClothes)) {
        $testClothes = $sharedClothes[0];
        echo "\n4. 测试特定ID查询 (ID: {$testClothes['id']}):\n";
        
        // 模拟API调用
        $apiUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/get_clothes.php";
        
        $testParams = [
            'id' => $testClothes['id'],
            'include_circle_data' => 'true',
            'data_source' => 'all'
        ];
        
        $queryString = http_build_query($testParams);
        $fullUrl = "$apiUrl?$queryString";
        
        echo "API URL: $fullUrl\n";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $fullUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: ' . $_SERVER['HTTP_AUTHORIZATION']
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            $data = json_decode($response, true);
            if ($data && !$data['error']) {
                echo "✅ API调用成功\n";
                echo "返回数据数量: " . count($data['data']) . "\n";
                echo "Meta信息: " . json_encode($data['meta']) . "\n";
                
                if (count($data['data']) > 0) {
                    $item = $data['data'][0];
                    echo "衣物信息: {$item['name']} (数据源: " . ($item['data_source'] ?? 'unknown') . ")\n";
                } else {
                    echo "❌ 没有返回衣物数据\n";
                }
            } else {
                echo "❌ API返回错误: " . ($data['message'] ?? '未知错误') . "\n";
            }
        } else {
            echo "❌ API调用失败，HTTP状态码: $httpCode\n";
        }
    }
    
    echo "\n=== 调试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
?>
