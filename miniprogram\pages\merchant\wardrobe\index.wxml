<view class="container">
  <!-- 衣橱列表 -->
  <view class="wardrobe-switcher" wx:if="{{wardrobes && wardrobes.length > 0}}">
    <scroll-view scroll-x="true" class="wardrobe-scroll" enhanced show-scrollbar="{{false}}">
      <view 
        class="wardrobe-option {{currentWardrobe && currentWardrobe.id === item.id ? 'active' : ''}}" 
        wx:for="{{wardrobes}}" 
        wx:key="id"
        bindtap="selectWardrobe"
        data-id="{{item.id}}">
        {{item.name}}
      </view>
    </scroll-view>
  </view>

  <!-- 分类选项卡 -->
  <view class="category-tabs-container">
    <view class="category-tabs">
      <view 
        class="tab-item {{selectedCategoryId === null ? 'active' : ''}}" 
        bindtap="clearCategory">
        全部
      </view>
      <view 
        class="tab-item {{selectedCategoryId === item.id ? 'active' : ''}}" 
        wx:for="{{categories}}" 
        wx:key="id"
        bindtap="selectCategory"
        data-id="{{item.id}}">
        {{item.name}}
      </view>
    </view>
  </view>

  <!-- 衣物列表 -->
  <view class="clothes-container">
    <view class="clothes-grid layout-mode-4">
      <block wx:if="{{clothes && clothes.length > 0}}">
        <view 
          class="clothes-item" 
          wx:for="{{clothes}}" 
          wx:key="id"
          bindtap="navigateToClothesDetail"
          data-id="{{item.id}}"
          style="position: relative;">
          <image class="clothing-image" src="{{item.image_url}}" mode="aspectFit"></image>
          
          <!-- 试衣按钮 
          <view class="try-on-btn" catchtap="tryOnClothing" data-id="{{item.id}}">
            <text>试穿</text>
          </view>-->
        </view>
      </block>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!clothes || clothes.length === 0}}">
    <image class="empty-icon" src="/images/icons/empty.png"></image>
    <text class="empty-text">暂无衣物</text>
  </view>

  <!-- 加载中 -->
  <view class="loading" wx:if="{{loading}}">
    <image class="loading-icon" src="/images/icons/loading.gif"></image>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 加载更多提示 -->
  <view class="loading-more" wx:if="{{loadingMore}}">
    <view class="loading-more-content">
      <image class="loading-more-icon" src="/images/icons/loading.gif"></image>
      <text class="loading-more-text">加载更多...</text>
    </view>
  </view>

  <!-- 没有更多数据提示 -->
  <view class="no-more-data" wx:if="{{!loadingMore && !loading && !hasMoreData && clothes && clothes.length > 0}}">
    <text class="no-more-text">没有更多衣物了</text>
  </view>
</view> 