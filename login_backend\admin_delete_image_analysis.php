<?php
/**
 * 管理员删除形象分析记录API
 */

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit;
}

// 检查是否有Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '需要授权头']);
    exit;
}

// 验证管理员token
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或过期的令牌']);
    exit;
}

// 获取请求数据
$data = json_decode(file_get_contents('php://input'), true);

// 检查是否提供了分析ID
if (!isset($data['id']) || empty($data['id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少分析ID']);
    exit;
}

$analysisId = intval($data['id']);

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

try {
    // 开始事务
    $conn->beginTransaction();
    
    // 查询记录是否存在
    $checkStmt = $conn->prepare("SELECT id FROM user_image_analysis WHERE id = :id");
    $checkStmt->bindParam(':id', $analysisId, PDO::PARAM_INT);
    $checkStmt->execute();
    
    if ($checkStmt->rowCount() === 0) {
        // 记录不存在
        $conn->rollBack();
        http_response_code(404);
        echo json_encode(['error' => true, 'msg' => '未找到形象分析记录']);
        exit;
    }
    
    // 删除记录
    $deleteStmt = $conn->prepare("DELETE FROM user_image_analysis WHERE id = :id");
    $deleteStmt->bindParam(':id', $analysisId, PDO::PARAM_INT);
    $deleteStmt->execute();
    
    // 提交事务
    $conn->commit();
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'msg' => '形象分析记录已成功删除'
    ]);
    
} catch (PDOException $e) {
    // 回滚事务
    $conn->rollBack();
    
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '删除形象分析记录失败: ' . $e->getMessage()
    ]);
} 