<?php
// 验证圈子邀请码API
// 模块1：圈子基础管理模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

// 验证必需参数
if (!isset($input['invitation_code']) || empty(trim($input['invitation_code']))) {
    echo json_encode([
        'status' => 'error',
        'message' => '邀请码不能为空'
    ]);
    exit;
}

$invitationCode = trim(strtoupper($input['invitation_code']));

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查找圈子
    $findCircleSql = "SELECT c.id, c.name, c.description, c.creator_id, c.member_count, c.created_at,
                             u.nickname as creator_nickname, u.avatar_url as creator_avatar
                      FROM outfit_circles c
                      JOIN users u ON c.creator_id = u.id
                      WHERE c.invitation_code = :invitation_code AND c.status = 'active'";
    $findCircleStmt = $conn->prepare($findCircleSql);
    $findCircleStmt->bindParam(':invitation_code', $invitationCode);
    $findCircleStmt->execute();
    
    $circle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$circle) {
        echo json_encode([
            'status' => 'error',
            'message' => '邀请码无效或圈子不存在'
        ]);
        exit;
    }
    
    // 检查用户是否已经在其他圈子中
    $checkUserSql = "SELECT c.id, c.name, cm.role 
                     FROM circle_members cm 
                     JOIN outfit_circles c ON cm.circle_id = c.id 
                     WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
    $checkUserStmt = $conn->prepare($checkUserSql);
    $checkUserStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkUserStmt->execute();
    
    $existingCircle = $checkUserStmt->fetch(PDO::FETCH_ASSOC);
    
    // 检查用户是否已经是该圈子的成员
    $checkMemberSql = "SELECT id, status FROM circle_members 
                       WHERE circle_id = :circle_id AND user_id = :user_id";
    $checkMemberStmt = $conn->prepare($checkMemberSql);
    $checkMemberStmt->bindParam(':circle_id', $circle['id'], PDO::PARAM_INT);
    $checkMemberStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $checkMemberStmt->execute();
    
    $existingMember = $checkMemberStmt->fetch(PDO::FETCH_ASSOC);
    
    // 确定用户状态
    $userStatus = 'can_join'; // 默认可以加入
    $statusMessage = '';
    
    if ($existingCircle) {
        if ($existingCircle['id'] == $circle['id']) {
            $userStatus = 'already_member';
            $statusMessage = '您已经是该圈子的成员';
        } else {
            $userStatus = 'in_other_circle';
            $statusMessage = '您已经在圈子"' . $existingCircle['name'] . '"中，请先退出后再加入其他圈子';
        }
    } elseif ($existingMember && $existingMember['status'] === 'active') {
        $userStatus = 'already_member';
        $statusMessage = '您已经是该圈子的成员';
    } elseif ($existingMember && $existingMember['status'] === 'removed') {
        $userStatus = 'can_rejoin';
        $statusMessage = '您之前曾是该圈子的成员，可以重新加入';
    }
    
    echo json_encode([
        'status' => 'success',
        'data' => [
            'valid' => true,
            'user_status' => $userStatus,
            'status_message' => $statusMessage,
            'circle' => [
                'id' => $circle['id'],
                'name' => $circle['name'],
                'description' => $circle['description'],
                'member_count' => $circle['member_count'],
                'created_at' => $circle['created_at'],
                'creator' => [
                    'id' => $circle['creator_id'],
                    'nickname' => $circle['creator_nickname'] ?? '未知用户',
                    'avatar_url' => $circle['creator_avatar']
                ]
            ]
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '验证邀请码失败：' . $e->getMessage()
    ]);
}
?>
