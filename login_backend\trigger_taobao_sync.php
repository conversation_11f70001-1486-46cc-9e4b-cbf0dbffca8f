<?php
/**
 * 触发淘宝商品数据同步接口
 * 
 * 该接口用于手动触发淘宝联盟商品数据同步任务。
 * 触发后，系统会在后台执行update_taobao_products.php脚本，获取最新商品数据。
 * 本接口仅用于启动同步任务，不会等待同步完成，适用于手动更新数据场景。
 * 
 * 请求方式：POST
 * 接口路径：/login_backend/trigger_taobao_sync.php
 * 权限要求：必须是管理员用户
 * 
 * 请求参数：无需参数
 * 请求头要求：Authorization: Bearer {管理员令牌}
 * 
 * 返回数据：
 * - 成功时:
 *   {
 *     "error": false,
 *     "msg": "同步任务已在后台启动",
 *     "start_time": "开始时间"
 *   }
 * - 失败时:
 *   {
 *     "error": true,
 *     "msg": "错误信息"
 *   }
 * 
 * 注意：同一时间只允许一个同步任务在执行，如果已有同步任务正在运行，接口会返回错误信息
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

require_once 'config.php';
require_once 'auth.php';

// 验证管理员Token
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION']) && !empty($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
    
    $auth = new Auth();
    $payload = $auth->verifyAdminToken($token);
    
    if (!$payload) {
        http_response_code(401);
        echo json_encode(['error' => true, 'msg' => '无效的管理员授权']);
        exit();
    }
}

// 确保日志目录存在
$logDir = __DIR__ . '/logs';
if (!file_exists($logDir)) {
    mkdir($logDir, 0755, true);
}

// 检查后台任务是否正在执行
$lockFile = $logDir . '/taobao_sync.lock';
if (file_exists($lockFile)) {
    $lastRunTime = file_get_contents($lockFile);
    $lastRunTimestamp = (int)$lastRunTime;
    
    // 如果锁文件存在但超过30分钟，则认为上次同步已经失败或卡住
    if (time() - $lastRunTimestamp < 1800) { // 30分钟 = 1800秒
        echo json_encode([
            'error' => true,
            'msg' => '另一个同步任务正在执行中，请稍后再试',
            'last_run' => date('Y-m-d H:i:s', $lastRunTimestamp)
        ]);
        exit();
    }
}

try {
    // 创建锁文件
    file_put_contents($lockFile, time());
    
    // 构建更新脚本的路径
    $scriptPath = __DIR__ . '/update_taobao_products.php';
    
    // 确保脚本文件存在
    if (!file_exists($scriptPath)) {
        throw new Exception('更新脚本不存在');
    }
    
    // 设置日志文件路径
    $logFilePath = $logDir . '/taobao_sync_' . date('Ymd_His') . '.log';
    
    // 准备响应数据
    $responseData = [
        'error' => false,
        'msg' => '同步任务已在后台启动',
        'start_time' => date('Y-m-d H:i:s')
    ];
    
    // 发送响应并关闭连接，使脚本可以在后台继续运行
    ob_start();
    echo json_encode($responseData);
    header('Connection: close');
    header('Content-Length: ' . ob_get_length());
    ob_end_flush();
    flush();
    
    // 确保所有数据都已发送给客户端
    if (function_exists('fastcgi_finish_request')) {
        fastcgi_finish_request();
    }
    
    // 重定向错误输出到日志文件
    $oldErrorLog = ini_get('error_log');
    ini_set('error_log', $logFilePath);
    
    // 记录开始执行的信息
    error_log('[' . date('Y-m-d H:i:s') . '] 开始执行淘宝商品数据同步...');
    
    // 直接包含并执行更新脚本
    try {
        include $scriptPath;
        error_log('[' . date('Y-m-d H:i:s') . '] 同步任务执行完成');
    } catch (Exception $execException) {
        error_log('[' . date('Y-m-d H:i:s') . '] 执行脚本时出错: ' . $execException->getMessage());
    }
    
    // 恢复错误日志设置
    ini_set('error_log', $oldErrorLog);
    
    // 删除锁文件
    if (file_exists($lockFile)) {
        unlink($lockFile);
    }
    
} catch (Exception $e) {
    // 删除锁文件
    if (file_exists($lockFile)) {
        unlink($lockFile);
    }
    
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '启动同步任务失败: ' . $e->getMessage()
    ]);
} 