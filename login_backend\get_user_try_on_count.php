<?php
/**
 * 获取用户试衣次数API
 * 
 * 返回当前认证用户的剩余试衣次数
 * 
 * 返回:
 * {
 *   "error": false,
 *   "data": {
 *     "free_try_on_count": 1,   // 免费试衣次数
 *     "paid_try_on_count": 5,   // 付费试衣次数
 *     "count_mode": "dual"      // "daily", "database" 或 "dual"
 *   }
 * }
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 检查是否有Authorization头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

// 验证token
$token = $_SERVER['HTTP_AUTHORIZATION'];
$auth = new Auth();
$tokenData = $auth->verifyToken($token);

if (!$tokenData) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid or expired token'
    ]);
    exit;
}

// 获取用户ID
$userId = $tokenData['sub'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 获取当前使用的计数模式
    $countMode = defined('TRY_ON_COUNT_MODE') ? TRY_ON_COUNT_MODE : 'daily';
    
    if ($countMode === 'dual') {
        // 双层次数模式：返回免费次数和付费次数
        $stmt = $conn->prepare("SELECT free_try_on_count, paid_try_on_count FROM users WHERE id = :user_id");
        $stmt->execute(['user_id' => $userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $freeCount = isset($result['free_try_on_count']) ? (int)$result['free_try_on_count'] : 0;
        $paidCount = isset($result['paid_try_on_count']) ? (int)$result['paid_try_on_count'] : 0;
        
        echo json_encode([
            'error' => false,
            'data' => [
                'free_try_on_count' => $freeCount,
                'paid_try_on_count' => $paidCount,
                'count_mode' => $countMode
            ]
        ]);
    } else if ($countMode === 'database') {
        // 数据库模式：从users表获取paid_try_on_count (原try_on_count)
        $stmt = $conn->prepare("SELECT paid_try_on_count FROM users WHERE id = :user_id");
        $stmt->execute(['user_id' => $userId]);
        $tryOnCount = $stmt->fetch(PDO::FETCH_ASSOC)['paid_try_on_count'] ?? 0;
        
        echo json_encode([
            'error' => false,
            'data' => [
                'try_on_count' => (int)$tryOnCount,
                'count_mode' => $countMode
            ]
        ]);
    } else {
        // 每日一次模式
        $todayStart = date('Y-m-d 00:00:00');
        $stmt = $conn->prepare("
            SELECT COUNT(*) as used_count 
            FROM try_on_history 
            WHERE user_id = :user_id 
            AND created_at >= :today_start 
            AND status = 'success'
        ");
        
        $stmt->execute([
            'user_id' => $userId,
            'today_start' => $todayStart
        ]);
        
        $usedCount = $stmt->fetch(PDO::FETCH_ASSOC)['used_count'] ?? 0;
        $tryOnCount = $usedCount < 1 ? 1 : 0; // 每日只有1次
        
        echo json_encode([
            'error' => false,
            'data' => [
                'try_on_count' => (int)$tryOnCount,
                'count_mode' => $countMode
            ]
        ]);
    }
    
} catch (PDOException $e) {
    echo json_encode([
        'error' => true,
        'msg' => '数据库操作失败: ' . $e->getMessage()
    ]);
    exit;
} 