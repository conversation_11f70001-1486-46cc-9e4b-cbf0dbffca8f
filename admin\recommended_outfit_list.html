<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>次元衣柜 - 推荐穿搭管理</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <style>
        /* 列表样式 */
        .table-container {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .data-table th {
            background-color: #f9f9f9;
            font-weight: 500;
            color: #333;
        }
        
        .data-table tr:hover {
            background-color: #f5f5f5;
        }
        
        /* 穿搭图片缩略图 */
        .outfit-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-active {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        
        .status-inactive {
            background-color: #fff7e6;
            color: #fa8c16;
        }
        
        /* 操作按钮 */
        .action-btn {
            padding: 4px 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            margin-right: 5px;
            font-size: 13px;
        }
        
        .edit-btn {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        
        .edit-btn:hover {
            background-color: #bae7ff;
        }
        
        .delete-btn {
            background-color: #fff1f0;
            color: #f5222d;
        }
        
        .delete-btn:hover {
            background-color: #ffccc7;
        }
        
        .toggle-btn {
            background-color: #f9f9f9;
            color: #666;
        }
        
        .toggle-btn:hover {
            background-color: #f0f0f0;
        }
        
        /* 头部工具栏 */
        .toolbar {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .toolbar-right {
            display: flex;
            gap: 10px;
        }
        
        .add-btn {
            background-color: #1890ff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .add-btn:hover {
            background-color: #40a9ff;
        }
        
        .filter-select {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #d9d9d9;
            min-width: 120px;
        }
        
        .search-input {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #d9d9d9;
            min-width: 200px;
        }
        
        /* 分页 */
        .pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
            gap: 5px;
        }
        
        .page-item {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            background-color: white;
        }
        
        .page-item.active {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        
        .page-item:hover:not(.active) {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        /* 加载和错误状态 */
        .loading {
            text-align: center;
            padding: 40px;
            color: #999;
        }
        
        .error-message {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #f5222d;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        /* 统计数据卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-gap: 15px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 1024px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 640px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .stats-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            padding: 15px;
            display: flex;
            flex-direction: column;
        }
        
        .stats-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .stats-value {
            font-size: 24px;
            font-weight: 500;
            color: #1890ff;
            margin-bottom: 5px;
        }
        
        .stats-description {
            font-size: 12px;
            color: #999;
        }
        
        /* 确认删除模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            width: 400px;
            max-width: 90%;
        }
        
        .modal-header {
            margin-bottom: 15px;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .modal-body {
            margin-bottom: 20px;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .modal-btn {
            padding: 8px 16px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
        }
        
        .modal-cancel {
            background-color: #f0f0f0;
            color: #666;
        }
        
        .modal-confirm {
            background-color: #f5222d;
            color: white;
        }
        
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>推荐穿搭管理</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stats-card">
                    <div class="stats-title">推荐穿搭总数</div>
                    <div class="stats-value" id="totalOutfits">0</div>
                    <div class="stats-description">系统中全部推荐穿搭数量</div>
                </div>
                
                <div class="stats-card">
                    <div class="stats-title">已启用穿搭</div>
                    <div class="stats-value" id="activeOutfits">0</div>
                    <div class="stats-description">当前可见的推荐穿搭数量</div>
                </div>
                
                <div class="stats-card">
                    <div class="stats-title">用户浏览量</div>
                    <div class="stats-value" id="totalViews">0</div>
                    <div class="stats-description">所有推荐穿搭的浏览总次数</div>
                </div>
                
                <div class="stats-card">
                    <div class="stats-title">链接点击量</div>
                    <div class="stats-value" id="totalClicks">0</div>
                    <div class="stats-description">用户点击购买链接的总次数</div>
                </div>
            </div>
            
            <div id="errorMessage" class="error-message" style="display:none;"></div>
            
            <!-- 工具栏 -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <select id="categoryFilter" class="filter-select">
                        <option value="">全部分类</option>
                        <!-- 分类选项将通过JavaScript动态添加 -->
                    </select>
                    
                    <select id="statusFilter" class="filter-select">
                        <option value="">全部状态</option>
                        <option value="1">已启用</option>
                        <option value="0">已禁用</option>
                    </select>
                    
                    <input type="text" id="searchInput" class="search-input" placeholder="搜索穿搭名称...">
                </div>
                
                <div class="toolbar-right">
                    <button id="addOutfitBtn" class="add-btn">新增推荐穿搭</button>
                </div>
            </div>
            
            <!-- 数据表格 -->
            <div class="card">
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>缩略图</th>
                                <th>名称</th>
                                <th>分类</th>
                                <th>商品数</th>
                                <th>浏览量</th>
                                <th>点击量</th>
                                <th>排序</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="outfitTableBody">
                            <!-- 数据行将由JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
                
                <div id="loadingIndicator" class="loading">正在加载数据...</div>
                
                <!-- 分页控件 -->
                <div class="pagination" id="pagination">
                    <!-- 分页按钮将由JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- 确认删除模态框 -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">确认删除</h3>
            </div>
            <div class="modal-body">
                <p>确定要删除该推荐穿搭吗？此操作无法撤销。</p>
                <p id="deleteOutfitName" style="font-weight:bold;"></p>
            </div>
            <div class="modal-footer">
                <button id="cancelDelete" class="modal-btn modal-cancel">取消</button>
                <button id="confirmDelete" class="modal-btn modal-confirm">确认删除</button>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/recommended_outfits.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为recommended_outfit
            Sidebar.init('recommended_outfit');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 确保userName元素存在
            if (!document.getElementById('userName')) {
                const userName = document.createElement('span');
                userName.id = 'userName';
                userName.style.display = 'none';
                document.body.appendChild(userName);
            }
            
            // 初始化handlers对象，用于管理推荐穿搭列表
            window.handlers = {
                init: function() {
                    // 使用现有的api和UI处理逻辑
                    this.loadOutfits();
                    this.loadStats();
                    this.loadCategories();
                    this.initEvents();
                },
                
                // 使用推荐穿搭列表的API和UI处理逻辑
                ...window.api,
                ...window.state,
                
                // 加载穿搭列表
                loadOutfits: async function(page = 1) {
                    try {
                        elements.loadingIndicator.style.display = 'block';
                        elements.outfitTableBody.innerHTML = '';
                        elements.errorMessage.style.display = 'none';
                        
                        const data = await api.getOutfits(page, state.filters);
                        state.outfits = data.data || [];
                        state.pagination = data.pagination || {
                            current_page: 1,
                            total_pages: 1,
                            total_items: 0
                        };
                        
                        this.renderTable();
                        this.renderPagination();
                        
                        elements.loadingIndicator.style.display = 'none';
                    } catch (error) {
                        console.error('加载穿搭列表失败:', error);
                        elements.loadingIndicator.style.display = 'none';
                        elements.errorMessage.style.display = 'block';
                        elements.errorMessage.textContent = error.message || '加载穿搭列表失败';
                    }
                },
                
                // 加载统计数据
                loadStats: async function() {
                    try {
                        const data = await api.getStats();
                        if (data.data) {
                            state.stats = {
                                total: data.data.total || 0,
                                active: data.data.active || 0,
                                views: data.data.views || 0,
                                clicks: data.data.clicks || 0
                            };
                            
                            elements.totalOutfits.textContent = state.stats.total;
                            elements.activeOutfits.textContent = state.stats.active;
                            elements.totalViews.textContent = state.stats.views;
                            elements.totalClicks.textContent = state.stats.clicks;
                        }
                    } catch (error) {
                        console.error('加载统计数据失败:', error);
                    }
                },
                
                // 加载分类列表
                loadCategories: async function() {
                    try {
                        const data = await api.getCategories();
                        if (data.data && Array.isArray(data.data)) {
                            state.categories = data.data;
                            
                            // 更新分类筛选下拉框
                            elements.categoryFilter.innerHTML = '<option value="">全部分类</option>';
                            state.categories.forEach(category => {
                                const option = document.createElement('option');
                                option.value = category.id;
                                option.textContent = category.name;
                                elements.categoryFilter.appendChild(option);
                            });
                        }
                    } catch (error) {
                        console.error('加载分类列表失败:', error);
                    }
                },
                
                // 渲染表格
                renderTable: function() {
                    elements.outfitTableBody.innerHTML = '';
                    
                    if (state.outfits.length === 0) {
                        const emptyRow = document.createElement('tr');
                        emptyRow.innerHTML = `<td colspan="11" style="text-align: center; padding: 20px;">暂无数据</td>`;
                        elements.outfitTableBody.appendChild(emptyRow);
                        return;
                    }
                    
                    state.outfits.forEach(outfit => {
                        const row = document.createElement('tr');
                        
                        // 格式化日期
                        const createdDate = new Date(outfit.created_at);
                        const formattedDate = `${createdDate.getFullYear()}-${String(createdDate.getMonth() + 1).padStart(2, '0')}-${String(createdDate.getDate()).padStart(2, '0')} ${String(createdDate.getHours()).padStart(2, '0')}:${String(createdDate.getMinutes()).padStart(2, '0')}`;
                        
                        row.innerHTML = `
                            <td>${outfit.id}</td>
                            <td><img src="${outfit.image_url}" alt="${outfit.name}" class="outfit-image"></td>
                            <td>${outfit.name}</td>
                            <td>${outfit.category_name || '未分类'}</td>
                            <td>${outfit.product_count || 0}</td>
                            <td>${outfit.view_count || 0}</td>
                            <td>${outfit.click_count || 0}</td>
                            <td>${outfit.sort_order || 0}</td>
                            <td>
                                <span class="status-badge ${outfit.status == 1 ? 'status-active' : 'status-inactive'}">
                                    ${outfit.status == 1 ? '已启用' : '已禁用'}
                                </span>
                            </td>
                            <td>${formattedDate}</td>
                            <td>
                                <a href="recommended_outfit_edit.html?id=${outfit.id}" class="action-btn edit-btn">编辑</a>
                                <button class="action-btn toggle-btn" data-id="${outfit.id}" data-status="${outfit.status == 1 ? 0 : 1}">
                                    ${outfit.status == 1 ? '禁用' : '启用'}
                                </button>
                                <button class="action-btn delete-btn" data-id="${outfit.id}" data-name="${outfit.name}">删除</button>
                            </td>
                        `;
                        
                        elements.outfitTableBody.appendChild(row);
                    });
                    
                    // 绑定操作按钮事件
                    this.bindTableEvents();
                },
                
                // 绑定表格中的事件
                bindTableEvents: function() {
                    // 删除按钮
                    document.querySelectorAll('.delete-btn').forEach(btn => {
                        btn.addEventListener('click', () => {
                            const id = btn.dataset.id;
                            const name = btn.dataset.name;
                            this.showDeleteConfirm(id, name);
                        });
                    });
                    
                    // 启用/禁用按钮
                    document.querySelectorAll('.toggle-btn').forEach(btn => {
                        btn.addEventListener('click', () => {
                            const id = btn.dataset.id;
                            const status = parseInt(btn.dataset.status);
                            this.toggleOutfitStatus(id, status);
                        });
                    });
                },
                
                // 初始化事件
                initEvents: function() {
                    // 添加按钮
                    elements.addOutfitBtn.addEventListener('click', () => {
                        window.location.href = 'recommended_outfit_edit.html';
                    });
                    
                    // 筛选和搜索
                    elements.categoryFilter.addEventListener('change', () => {
                        state.filters.categoryId = elements.categoryFilter.value;
                        state.pagination.currentPage = 1;
                        this.loadOutfits(1);
                    });
                    
                    elements.statusFilter.addEventListener('change', () => {
                        state.filters.status = elements.statusFilter.value;
                        state.pagination.currentPage = 1;
                        this.loadOutfits(1);
                    });
                    
                    elements.searchInput.addEventListener('keyup', event => {
                        if (event.key === 'Enter') {
                            state.filters.search = elements.searchInput.value.trim();
                            state.pagination.currentPage = 1;
                            this.loadOutfits(1);
                        }
                    });
                    
                    // 删除模态框
                    elements.cancelDelete.addEventListener('click', () => {
                        this.hideDeleteConfirm();
                    });
                    
                    elements.confirmDelete.addEventListener('click', () => {
                        this.deleteOutfit();
                    });
                }
            };
            
            // 直接调用handlers.init()方法初始化页面
            if (typeof handlers !== 'undefined') {
                handlers.init();
            } else {
                console.error('推荐穿搭列表功能未找到');
                document.getElementById('loadingIndicator').textContent = '加载失败：推荐穿搭列表功能模块缺失';
            }
        });
    </script>
</body>
</html> 