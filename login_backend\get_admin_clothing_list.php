<?php
/**
 * 获取衣物列表API
 * - 用于管理员界面显示衣物列表
 * - 需要管理员权限
 * - 支持分页、搜索和分类过滤
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'error' => true,
        'msg' => '请求方法不允许'
    ]);
    exit;
}

// 检查授权头
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode([
        'error' => true,
        'msg' => '未授权访问'
    ]);
    exit;
}

require_once 'jwt_helper.php';
$token = $_SERVER['HTTP_AUTHORIZATION'];

try {
    // 验证令牌并获取用户数据
    $payload = JWTHelper::validateToken($token);
    
    // 检查是否为管理员
    if ($payload->role !== 'admin') {
        http_response_code(403);
        echo json_encode([
            'error' => true,
            'msg' => '权限不足'
        ]);
        exit;
    }
    
    // 获取请求参数
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $perPage = isset($_GET['per_page']) ? intval($_GET['per_page']) : 10;
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $category = isset($_GET['category']) ? trim($_GET['category']) : '';
    $sortBy = isset($_GET['sort_by']) ? trim($_GET['sort_by']) : 'id';
    $sortOrder = isset($_GET['sort_order']) ? trim($_GET['sort_order']) : 'desc';
    
    // 验证并调整分页参数
    if ($page < 1) $page = 1;
    if ($perPage < 1) $perPage = 10;
    if ($perPage > 100) $perPage = 100;
    
    // 验证排序参数
    $allowedSortFields = ['id', 'name', 'category', 'created_at'];
    if (!in_array($sortBy, $allowedSortFields)) {
        $sortBy = 'id';
    }
    
    $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';
    
    // 计算偏移量
    $offset = ($page - 1) * $perPage;
    
    // 连接数据库
    require_once 'db_config.php';
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 构建查询条件
    $conditions = [];
    $params = [];
    
    if ($search !== '') {
        $conditions[] = "(c.name LIKE :search OR c.tags LIKE :search)";
        $params[':search'] = "%$search%";
    }
    
    if ($category !== '') {
        $conditions[] = "c.category = :category";
        $params[':category'] = $category;
    }
    
    $whereClause = count($conditions) > 0 ? "WHERE " . implode(" AND ", $conditions) : "";
    
    // 查询总数
    $countQuery = "
        SELECT COUNT(*) as total
        FROM clothes c
        $whereClause
    ";
    
    $stmt = $pdo->prepare($countQuery);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $totalResult = $stmt->fetch(PDO::FETCH_ASSOC);
    $totalItems = $totalResult['total'];
    
    // 如果没有记录，直接返回空结果
    if ($totalItems === 0) {
        echo json_encode([
            'error' => false,
            'msg' => '获取衣物列表成功',
            'data' => [
                'clothes' => [],
                'pagination' => [
                    'total' => 0,
                    'per_page' => $perPage,
                    'current_page' => $page,
                    'last_page' => 0,
                    'from' => 0,
                    'to' => 0
                ]
            ]
        ]);
        exit;
    }
    
    // 计算总页数
    $totalPages = ceil($totalItems / $perPage);
    
    // 如果请求页数超出范围，返回最后一页
    if ($page > $totalPages) {
        $page = $totalPages;
        $offset = ($page - 1) * $perPage;
    }
    
    // 查询衣物数据
    $query = "
        SELECT c.*, u.nickname, u.username, u.avatar_url
        FROM clothes c
        LEFT JOIN users u ON c.user_id = u.id
        $whereClause
        ORDER BY c.$sortBy $sortOrder
        LIMIT :offset, :limit
    ";
    
    $stmt = $pdo->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':limit', $perPage, PDO::PARAM_INT);
    $stmt->execute();
    
    $clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理数据
    $result = [];
    foreach ($clothes as $item) {
        // 处理标签
        $tags = !empty($item['tags']) ? explode(',', $item['tags']) : [];
        
        // 处理用户名
        $userName = !empty($item['nickname']) ? $item['nickname'] : 
                   (!empty($item['username']) ? $item['username'] : "用户{$item['user_id']}");
        
        $result[] = [
            'id' => $item['id'],
            'user_id' => $item['user_id'],
            'user_name' => $userName,
            'user_avatar' => $item['avatar_url'],
            'name' => $item['name'],
            'category' => $item['category'],
            'image_url' => $item['image_url'],
            'tags' => $item['tags'],
            'tags_array' => $tags,
            'created_at' => $item['created_at'],
            'updated_at' => $item['updated_at']
        ];
    }
    
    // 构建响应数据
    $response = [
        'error' => false,
        'msg' => '获取衣物列表成功',
        'data' => [
            'clothes' => $result,
            'pagination' => [
                'total' => $totalItems,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => $totalPages,
                'from' => $offset + 1,
                'to' => min($offset + $perPage, $totalItems)
            ]
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'msg' => '服务器错误: ' . $e->getMessage()
    ]);
} 