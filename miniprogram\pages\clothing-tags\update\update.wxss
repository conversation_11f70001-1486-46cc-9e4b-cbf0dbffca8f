.container {
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  min-height: 100vh;
  box-sizing: border-box;
  background-color: #f8f8f8;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.back-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
}

.title-container {
  flex: 1;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #888;
}

/* 初始界面 */
.initial-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0 60rpx;
}

.intro-image {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 40rpx;
}

.intro-text {
  text-align: center;
  margin-bottom: 60rpx;
}

.intro-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.intro-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  padding: 0 40rpx;
}

.features {
  width: 100%;
  margin-bottom: 80rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  background-color: #fff;
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.feature-icon {
  width: 64rpx;
  height: 64rpx;
  margin-right: 24rpx;
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-text {
  font-size: 28rpx;
  color: #333;
}

/* 重写按钮基础样式，解决居中问题 */
.update-button, .return-button {
  width: 100%;
  height: 88rpx;
  background-color: #000000;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.2);
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  box-sizing: border-box !important;
}

/* 覆盖微信按钮的默认样式 */
button.update-button::after, 
button.return-button::after {
  border: none !important;
}

/* 确保按钮内容居中 */
.button-text {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  width: 100% !important;
  height: 100% !important;
}

/* 更新进度 */
.update-progress {
  padding: 40rpx 0;
}

.progress-info {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-text {
  font-size: 30rpx;
  color: #333;
  text-align: center;
}

.progress-percent {
  font-size: 32rpx;
  font-weight: bold;
  color: #000000;
}

.progress-bar-container {
  width: 100%;
  height: 16rpx;
  background-color: #eee;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #000000, #333333);
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.tips {
  margin-top: 40rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tip-item {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 30rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-item:before {
  content: '';
  position: absolute;
  left: 0;
  top: 14rpx;
  width: 12rpx;
  height: 12rpx;
  background-color: #000000;
  border-radius: 6rpx;
}

/* 更新结果 */
.update-result {
  padding: 40rpx 0;
}

.result-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.result-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  font-size: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.result-stats {
  display: flex;
  justify-content: space-around;
  padding: 30rpx 0;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.stat-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.stat-value.success {
  color: #07C160;
}

.stat-value.failed {
  color: #FF4D4F;
}

.stat-value.cleanup {
  color: #722ED1; /* 紫色，表示清理操作 */
}

.result-details {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f5f5f5;
}

.details-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.toggle-icon {
  display: flex;
  align-items: center;
}

.toggle-text {
  font-size: 26rpx;
  color: #888;
  margin-right: 10rpx;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  transition: transform 0.3s ease;
}

.details-list {
  padding: 20rpx 30rpx;
}

.detail-item {
  padding: 20rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.detail-item:last-child {
  border-bottom: none;
}

.item-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.item-tags {
  display: flex;
  flex-wrap: wrap;
}

.item-tag {
  font-size: 24rpx;
  color: #000000;
  background-color: #EFF3F9;
  border-radius: 20rpx;
  padding: 6rpx 16rpx;
  margin-right: 12rpx;
  margin-bottom: 12rpx;
}

.no-update-needed {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.check-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
  font-size: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.check-tip {
  font-size: 28rpx;
  color: #888;
}

/* 删除标签示例样式 */
.deleted-tags-sample {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 10rpx 0 30rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.deleted-tags-header {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.deleted-tags-list {
  display: flex;
  flex-wrap: wrap;
}

.deleted-tag {
  font-size: 24rpx;
  color: #ff4d4f;
  background-color: #fff1f0;
  border: 1px solid #ffccc7;
  border-radius: 20rpx;
  padding: 6rpx 16rpx;
  margin-right: 12rpx;
  margin-bottom: 12rpx;
}

.more-indicator {
  font-size: 24rpx;
  color: #888;
  padding: 6rpx 0;
} 