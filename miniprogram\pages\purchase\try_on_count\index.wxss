.container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin: 40rpx 0;
}

.title {
  font-size: 38rpx;
  font-weight: bold;
  color: #333;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-top: 10rpx;
}

.package-list {
  width: 100%;
}

.package-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid transparent;
}

.package-item.selected {
  border-color: #333;
  background-color: #f8f8f8;
}

.package-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.package-count {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.package-price {
  font-size: 34rpx;
  font-weight: bold;
  color: #ff6b81;
}

.package-desc {
  font-size: 26rpx;
  color: #888;
}

.check-icon {
  position: absolute;
  right: 30rpx;
  bottom: 20rpx;
  transform: none;
}

.payment-btn {
  margin: 40rpx auto;
  width: 80%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: #333;
  color: white;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
}

.payment-btn.disabled {
  background: #ccc;
  box-shadow: none;
}

.tips {
  margin-top: 40rpx;
  padding: 0 30rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 