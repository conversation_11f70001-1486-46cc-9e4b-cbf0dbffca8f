.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.content {
  flex: 1;
  padding: 30rpx;
}

.analysis-list {
  margin-bottom: 30rpx;
}

.analysis-item {
  margin-bottom: 20rpx;
}

.analysis-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  position: relative;
}

.analysis-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.analysis-date {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.analysis-status-container {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.analysis-status {
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.analysis-status.pending {
  background-color: #f0f0f0;
  color: #999;
}

.analysis-status.processing {
  background-color: #e3f2fd;
  color: #1976d2;
}

.analysis-status.completed {
  background-color: #e8f5e9;
  color: #388e3c;
}

.analysis-status.failed {
  background-color: #ffebee;
  color: #d32f2f;
}

.analysis-divider {
  height: 1px;
  background-color: #f0f0f0;
  margin-bottom: 20rpx;
}

.analysis-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-text {
  font-size: 28rpx;
  color: #000;
}

.action-icon {
  font-size: 24rpx;
  color: #000;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
  color: #ccc;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.start-btn {
  background-color: #000;
  color: white;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: bold;
  padding: 0 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.start-btn:after {
  border: none;
}

/* 加载更多和没有更多数据的样式 */
.loading-more, .no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
  width: 100%;
}

.loading-text, .no-more-text {
  font-size: 24rpx;
  color: #999;
}

/* 加载更多按钮样式 */
.load-more-btn-container {
  display: flex;
  justify-content: center;
  padding: 20rpx 0 40rpx;
  width: 100%;
}

.load-more-btn {
  background-color: #f8f8f8;
  color: #666;
  font-size: 28rpx;
  padding: 10rpx 40rpx;
  border: 1px solid #ddd;
  border-radius: 30rpx;
}

.load-more-btn:after {
  border: none;
}

/* 删除按钮样式 */
.delete-btn {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}

.delete-btn:active {
  background-color: #e0e0e0;
}

.delete-icon {
  font-size: 32rpx;
  color: #999;
  font-weight: bold;
} 