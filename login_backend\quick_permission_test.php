<?php
/**
 * 快速权限测试
 * 验证权限检查API修复是否成功
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

echo "=== 快速权限测试 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

// 检查授权头
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "❌ 缺少Authorization头\n";
    echo "请在请求头中添加: Authorization: Bearer your_token\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证token
require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

try {
    $auth = new Auth();
    $payload = $auth->verifyToken($token);
    
    if (!$payload) {
        echo "❌ Token验证失败\n";
        exit;
    }
    
    // 测试用户ID获取
    $userId = $payload['user_id'] ?? $payload['sub'] ?? null;
    
    if (!$userId) {
        echo "❌ 无法从Token中获取用户ID\n";
        echo "Payload内容: " . json_encode($payload) . "\n";
        exit;
    }
    
    echo "✅ Token验证成功，用户ID: $userId\n\n";
    
    // 获取测试数据
    $db = new Database();
    $conn = $db->getConnection();
    
    // 获取一个衣物进行测试
    $stmt = $conn->prepare("SELECT id, name FROM clothes LIMIT 1");
    $stmt->execute();
    $testClothes = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$testClothes) {
        echo "❌ 数据库中没有衣物数据进行测试\n";
        exit;
    }
    
    echo "测试衣物: {$testClothes['name']} (ID: {$testClothes['id']})\n\n";
    
    // 测试权限检查API
    $apiUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/check_circle_permission.php";
    
    $testParams = [
        'data_type' => 'clothes',
        'data_id' => $testClothes['id'],
        'operation' => 'edit'
    ];
    
    $queryString = http_build_query($testParams);
    $fullUrl = "$apiUrl?$queryString";
    
    echo "测试权限检查API:\n";
    echo "URL: $fullUrl\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $fullUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: ' . $_SERVER['HTTP_AUTHORIZATION']
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ CURL错误: $error\n";
    } elseif ($httpCode !== 200) {
        echo "❌ HTTP错误: $httpCode\n";
        echo "响应: $response\n";
    } else {
        // 检查响应是否包含PHP错误
        if (strpos($response, '<b>Notice</b>') !== false || 
            strpos($response, '<b>Warning</b>') !== false ||
            strpos($response, 'Undefined index') !== false) {
            echo "❌ 权限检查API仍有PHP错误:\n";
            echo "$response\n";
        } else {
            echo "✅ 权限检查API调用成功，无PHP错误\n";
            
            $data = json_decode($response, true);
            if ($data && $data['status'] === 'success') {
                $permission = $data['data'];
                $status = $permission['allowed'] ? '✅ 允许' : '❌ 拒绝';
                echo "权限结果: $status - {$permission['reason']}\n";
                
                if (isset($data['debug']['user_id'])) {
                    echo "API获取的用户ID: " . $data['debug']['user_id'] . "\n";
                    
                    if ($data['debug']['user_id'] == $userId) {
                        echo "✅ 用户ID传递正确\n";
                    } else {
                        echo "❌ 用户ID传递错误\n";
                    }
                }
            } else {
                echo "❌ API返回错误: " . ($data['message'] ?? '未知错误') . "\n";
            }
        }
    }
    
    echo "\n=== 测试结论 ===\n";
    
    if (strpos($response, 'Undefined index') === false && 
        strpos($response, '<b>Notice</b>') === false) {
        echo "🎉 权限检查API修复成功！\n";
        echo "前端权限功能现在应该可以正常工作了。\n";
    } else {
        echo "❌ 权限检查API仍有问题，需要进一步调试。\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}
?>
