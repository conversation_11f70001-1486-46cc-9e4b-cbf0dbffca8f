<?php
/**
 * 直接测试分类API调用
 * 模拟前端的完整请求流程
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    echo "=== 直接测试分类API ===\n";
    echo "当前用户ID: $userId\n\n";
    
    // 测试不同的数据源
    $dataSources = ['personal', 'shared', 'all'];
    
    foreach ($dataSources as $dataSource) {
        echo "=== 测试数据源: $dataSource ===\n";
        
        // 构建API URL
        $apiUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/get_clothing_categories.php";
        if ($dataSource !== 'personal') {
            $apiUrl .= "?include_circle_data=true&data_source=$dataSource";
        }
        
        echo "API URL: $apiUrl\n";
        
        // 准备请求头
        $headers = [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json'
        ];
        
        // 发起API请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "HTTP状态码: $httpCode\n";
        
        if ($error) {
            echo "CURL错误: $error\n\n";
            continue;
        }
        
        if ($httpCode !== 200) {
            echo "HTTP错误，响应内容:\n$response\n\n";
            continue;
        }
        
        // 解析响应
        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "JSON解析错误: " . json_last_error_msg() . "\n";
            echo "原始响应:\n$response\n\n";
            continue;
        }
        
        echo "API响应解析成功\n";
        echo "错误状态: " . ($data['error'] ? 'true' : 'false') . "\n";
        echo "消息: " . ($data['msg'] ?? 'N/A') . "\n";
        
        if (isset($data['data'])) {
            $categories = $data['data'];
            echo "分类数量: " . count($categories) . "\n";
            
            $systemCount = 0;
            $customCount = 0;
            
            foreach ($categories as $cat) {
                if ($cat['is_system']) {
                    $systemCount++;
                    echo "- [系统] {$cat['name']} (code: {$cat['code']}";
                    if (isset($cat['data_source'])) {
                        echo ", data_source: {$cat['data_source']}";
                    }
                    echo ")\n";
                } else {
                    $customCount++;
                    echo "- [自定义] {$cat['name']} (code: {$cat['code']}";
                    if (isset($cat['creator_nickname'])) {
                        echo ", 创建者: {$cat['creator_nickname']}";
                    }
                    if (isset($cat['data_source'])) {
                        echo ", data_source: {$cat['data_source']}";
                    }
                    echo ")\n";
                }
            }
            
            echo "统计: 系统分类 $systemCount 个，自定义分类 $customCount 个\n";
            
            // 检查meta信息
            if (isset($data['meta'])) {
                echo "Meta信息:\n";
                foreach ($data['meta'] as $key => $value) {
                    echo "- $key: $value\n";
                }
            }
            
        } else {
            echo "响应中没有data字段\n";
        }
        
        echo "\n";
    }
    
    // 额外测试：直接执行数据库查询
    echo "=== 额外测试：直接数据库查询 ===\n";
    
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查用户圈子
    $stmt = $conn->prepare("SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $userCircles = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "用户活跃圈子: " . json_encode($userCircles) . "\n";
    
    if (!empty($userCircles)) {
        $circleIds = implode(',', $userCircles);
        
        // 检查圈子中的自定义分类
        $stmt = $conn->prepare("
            SELECT c.id, c.name, c.code, c.user_id, c.circle_id, u.nickname as creator
            FROM clothing_categories c
            LEFT JOIN users u ON c.user_id = u.id
            WHERE c.is_system = 0 AND c.circle_id IN ($circleIds)
            ORDER BY c.user_id, c.created_at DESC
        ");
        $stmt->execute();
        $dbCustomCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "数据库中圈子的自定义分类数量: " . count($dbCustomCategories) . "\n";
        
        $ownCount = 0;
        $othersCount = 0;
        
        foreach ($dbCustomCategories as $cat) {
            if ($cat['user_id'] == $userId) {
                $ownCount++;
                echo "- [自己] {$cat['name']} (code: {$cat['code']}, ID: {$cat['id']})\n";
            } else {
                $othersCount++;
                echo "- [其他] {$cat['name']} (code: {$cat['code']}, 创建者: {$cat['creator']}, ID: {$cat['id']})\n";
            }
        }
        
        echo "统计: 自己的 $ownCount 个，其他用户的 $othersCount 个\n";
        
        // 测试shared查询的具体SQL
        echo "\n测试shared查询的具体SQL:\n";
        $sharedSql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                             u.nickname as creator_nickname,
                             CASE
                                 WHEN c.is_system = 1 THEN 'system'
                                 WHEN c.circle_id IS NULL THEN 'personal'
                                 ELSE 'shared'
                             END as data_source
                      FROM clothing_categories c
                      LEFT JOIN users u ON c.user_id = u.id
                      WHERE (
                          (c.is_system = 1 AND c.user_id = :user_id) OR
                          (c.is_system = 0 AND c.user_id != :user_id AND c.circle_id IN ($circleIds))
                      )
                      ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC";
        
        echo "SQL: $sharedSql\n";
        echo "参数: user_id = $userId\n";
        
        $stmt = $conn->prepare($sharedSql);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $sharedResult = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "SQL查询结果数量: " . count($sharedResult) . "\n";
        
        $sqlSystemCount = 0;
        $sqlCustomCount = 0;
        
        foreach ($sharedResult as $cat) {
            if ($cat['is_system']) {
                $sqlSystemCount++;
            } else {
                $sqlCustomCount++;
                echo "- [SQL自定义] {$cat['name']} (code: {$cat['code']}, 创建者: {$cat['creator_nickname']})\n";
            }
        }
        
        echo "SQL统计: 系统分类 $sqlSystemCount 个，自定义分类 $sqlCustomCount 个\n";
        
        // 对比分析
        echo "\n对比分析:\n";
        echo "- 数据库中其他用户的自定义分类: $othersCount 个\n";
        echo "- SQL查询返回的自定义分类: $sqlCustomCount 个\n";
        
        if ($othersCount > 0 && $sqlCustomCount == 0) {
            echo "❌ 问题确认：数据库中有数据，但SQL查询返回空\n";
        } elseif ($othersCount == $sqlCustomCount && $sqlCustomCount > 0) {
            echo "✅ SQL查询正常\n";
        } elseif ($othersCount == 0) {
            echo "⚠️ 数据库中没有其他用户的自定义分类\n";
        }
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
?>
