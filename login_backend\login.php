<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config.php';
require_once 'auth.php';

// Handle preflight request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => 'Method not allowed']);
    exit();
}

// Get request body
$data = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!isset($data['code']) || empty($data['code'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => 'Missing required field: code']);
    exit();
}

$code = $data['code'];

// Get WeChat session
$auth = new Auth();
$wxSessionResult = $auth->getWxSession($code);

if ($wxSessionResult['error']) {
    http_response_code(400);
    echo json_encode([
        'error' => true, 
        'msg' => 'Failed to get WeChat session: ' . $wxSessionResult['msg'],
        'code' => isset($wxSessionResult['code']) ? $wxSessionResult['code'] : null
    ]);
    exit();
}

// Login or register user with the session data
$loginResult = $auth->loginOrRegisterUser($wxSessionResult['data']);

// Return the result
echo json_encode([
    'error' => false,
    'data' => $loginResult
]); 