<view class="container">
  <!-- 标题栏 -->
  <view class="header">
    <view class="title">穿搭分类</view>
    <view class="add-btn" bindtap="goToAddCategory">
      <image src="/images/add.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 分类列表 -->
  <view class="category-list" wx:if="{{categories.length > 0}}">
    <view class="category-item" wx:for="{{categories}}" wx:key="id">
      <view class="category-info" bindtap="viewOutfits" data-id="{{item.id}}" data-name="{{item.name}}">
        <view class="category-name">
          {{item.name}}
          <view class="default-tag" wx:if="{{item.is_default == 1}}">默认</view>
        </view>
        <view class="category-desc" wx:if="{{item.description}}">{{item.description}}</view>
        <view class="category-count">穿搭数量: {{item.outfit_count}}</view>
      </view>
      <view class="category-actions">
        <view class="edit-btn" catchtap="editCategory" data-category="{{item}}">
          <image src="/images/edit.png" mode="aspectFit"></image>
        </view>
        <view class="delete-btn" catchtap="deleteCategory" data-id="{{item.id}}" data-is-default="{{item.is_default == 1}}">
          <image src="/images/delete.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <image class="empty-image" src="/images/empty-outfit.png" mode="aspectFit"></image>
    <view class="empty-text">您还没有创建穿搭分类</view>
    <view class="create-btn" bindtap="goToAddCategory">创建分类</view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMoreData}}">
    <view bindtap="loadMore" wx:if="{{!isLoading}}">加载更多</view>
    <view wx:else>加载中...</view>
  </view>
</view> 