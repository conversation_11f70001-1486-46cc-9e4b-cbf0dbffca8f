<?php
/**
 * 智能穿搭推荐API
 * 
 * 根据天气和用户衣物，生成智能穿搭推荐
 * 
 * 请求参数:
 * - refresh: 可选，值为1时表示重新生成推荐，忽略缓存
 * 
 * 响应:
 * {
 *   "success": true,
 *   "data": {
 *     "weather": {天气信息},
 *     "outfit": {
 *       "top": {上衣信息},
 *       "bottom": {下装信息},
 *       "outerwear": {外套信息，可选},
 *       "shoes": {鞋子信息},
 *       "accessories": {配饰信息，可选}
 *     }
 *   }
 * }
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 设置返回内容类型
header('Content-Type: application/json');

// 处理CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 对于OPTIONS请求，直接返回
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'success' => false,
        'msg' => '仅支持GET请求'
    ]);
    exit;
}

// 获取并验证token
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'success' => false,
        'msg' => '缺少授权信息'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
$auth = new Auth();
$tokenData = $auth->verifyToken($token);

if (!$tokenData) {
    echo json_encode([
        'success' => false,
        'msg' => '无效或已过期的授权信息'
    ]);
    exit;
}

// 获取用户ID
$userId = $tokenData['sub'];

// 检查是否需要刷新推荐
$refresh = isset($_GET['refresh']) && $_GET['refresh'] == '1';

// 初始化数据库连接
$db = new Database();
$conn = $db->getConnection();

// 获取今天的日期
$today = date('Y-m-d');

// 获取位置参数
$latitude = isset($_GET['latitude']) ? $_GET['latitude'] : null;
$longitude = isset($_GET['longitude']) ? $_GET['longitude'] : null;
$cityName = isset($_GET['city']) ? $_GET['city'] : null;

// 如果有位置信息，强制刷新推荐
if ($latitude !== null && $longitude !== null) {
    $refresh = true;
    // 删除当天的缓存数据
    try {
        $stmt = $conn->prepare("
            DELETE FROM outfit_recommendations 
            WHERE user_id = ? AND DATE(created_at) = ?
        ");
        $stmt->execute([$userId, $today]);
    } catch (Exception $e) {
        error_log("删除缓存推荐失败: " . $e->getMessage());
    }
}

// 修改城市信息处理部分，使用城市ID查询天气信息
if ($latitude !== null && $longitude !== null) {
    error_log("推荐API - 使用位置信息: 纬度=$latitude, 经度=$longitude");
    
    // 确保经纬度是有效值
    if (!is_numeric($latitude) || !is_numeric($longitude)) {
        error_log("推荐API - 警告: 非法的经纬度格式，纬度=$latitude, 经度=$longitude");
        echo json_encode([
            'success' => false,
            'msg' => '非法的位置信息格式'
        ]);
        exit;
    }
    
    // 使用经纬度查询天气
    $weatherParams = "latitude=$latitude&longitude=$longitude&_nocache=" . time();
    error_log("推荐API - 使用经纬度查询天气: $latitude, $longitude");
} elseif (isset($cityName) && !empty($cityName)) {
    // 使用城市名查询
    $weatherParams = "city=" . urlencode($cityName) . "&_nocache=" . time();
    error_log("推荐API - 使用城市名查询天气: $cityName");
} else {
    // 使用默认城市(杭州)
    $weatherParams = "cityid=" . WEATHER_DEFAULT_LOCATION . "&_nocache=" . time();
    error_log("推荐API - 没有位置信息，使用默认位置 (杭州: " . WEATHER_DEFAULT_LOCATION . ")");
}

// 获取天气数据
$weatherUrl = API_DOMAIN . "/login_backend/get_weather.php?$weatherParams";
error_log("推荐API - 天气请求URL: $weatherUrl");

// 添加日志函数
/**
 * 写入日志到文件
 * 
 * @param string $type 日志类型
 * @param string $message 日志信息
 * @param array|null $data 额外的数据
 */
function writeLog($type, $message, $data = null) {
    // 确保日志目录存在
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        if (!mkdir($logDir, 0755, true)) {
            error_log("无法创建日志目录: $logDir");
            return;
        }
    }
    
    // 按日期和类型创建日志文件
    $date = date('Y-m-d');
    $logFile = $logDir . "/{$type}_{$date}.log";
    
    // 构建日志内容
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[{$timestamp}] {$message}";
    
    // 如果有额外数据，添加到日志中
    if ($data !== null) {
        $logEntry .= "\nData: " . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    
    $logEntry .= "\n--------------------------------------------------\n";
    
    // 写入日志文件
    file_put_contents($logFile, $logEntry, FILE_APPEND);
    
    // 同时写入PHP错误日志，便于调试
    error_log("[{$type}] {$message}");
}

// 在获取天气数据前记录日志
writeLog('weather', "开始获取天气数据, 参数: $weatherParams", [
    'latitude' => $latitude,
    'longitude' => $longitude,
    'cityName' => $cityName,
    'weatherUrl' => $weatherUrl
]);

// 发送请求获取天气数据
$ch = curl_init($weatherUrl);
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_SSL_VERIFYPEER => true
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

// 记录天气API响应日志
writeLog('weather', "天气API响应状态: $httpCode", [
    'error' => $error,
    'response' => substr($response, 0, 1000) // 限制长度
]);

// 检查响应
if ($httpCode !== 200 || $error) {
    error_log("推荐API - 天气API请求失败: " . ($error ?: "HTTP状态码: $httpCode"));
    
    // 尝试使用备用的天气数据获取方法
    $weatherData = getFallbackWeatherData();
    
    if (!$weatherData['success']) {
        echo json_encode([
            'success' => false,
            'msg' => '获取天气数据失败，请稍后重试',
            'debug_info' => $weatherData['msg']
        ]);
        exit;
    }
} else {
    // 过滤可能包含的PHP错误通知并去除前导空格
    $cleanResponse = trim($response);
    
    // 检查是否包含PHP错误信息（通常以<br>、Notice:等开头）
    if (strpos($cleanResponse, '<br') !== false || strpos($cleanResponse, 'Notice:') !== false) {
        writeLog('weather', "检测到API响应中包含PHP错误信息，尝试提取有效JSON");
        // 查找JSON的开始位置（通常是{）
        $jsonStart = strpos($cleanResponse, '{');
        if ($jsonStart !== false) {
            $cleanResponse = substr($cleanResponse, $jsonStart);
            writeLog('weather', "提取后的JSON响应: " . substr($cleanResponse, 0, 100) . "...");
        }
    }
    
    // 记录响应前后的对比，帮助诊断问题
    if ($cleanResponse !== $response) {
        writeLog('weather', "响应已清理", [
            'original_length' => strlen($response),
            'cleaned_length' => strlen($cleanResponse),
            'diff_start' => substr($response, 0, min(20, strlen($response)))
        ]);
    }
    
    // 解析响应
    $weatherData = json_decode($cleanResponse, true);
    $jsonError = json_last_error();
    
    if ($jsonError !== JSON_ERROR_NONE) {
        // 记录JSON解析错误
        writeLog('weather', "JSON解析错误: " . json_last_error_msg(), [
            'error_code' => $jsonError,
            'raw_response_start' => substr($cleanResponse, 0, 50)
        ]);
    }
    
    if (!$weatherData || !isset($weatherData['success']) || !$weatherData['success']) {
        error_log("推荐API - 天气API响应格式无效: " . substr($cleanResponse, 0, 200));
        
        // 记录详细调试信息
        writeLog('weather', "天气API响应解析失败", [
            'raw_response' => substr($response, 0, 500),
            'cleaned_response' => substr($cleanResponse, 0, 500),
            'json_error' => json_last_error_msg(),
            'json_error_code' => $jsonError
        ]);
        
        // 尝试使用备用的天气数据获取方法
        $weatherData = getFallbackWeatherData();
        
        if (!$weatherData['success']) {
            echo json_encode([
                'success' => false,
                'msg' => '解析天气数据失败，请稍后重试',
                'debug_info' => $response
            ]);
            exit;
        }
    } else {
        // 检查是否包含预期的数据结构
        if (!isset($weatherData['data']) || empty($weatherData['data'])) {
            writeLog('weather', "天气API响应缺少data字段", [
                'response_keys' => $weatherData ? array_keys($weatherData) : 'null'
            ]);
            
            // 使用备用数据
            $weatherData = getFallbackWeatherData();
        } else {
            // 记录成功获取天气数据
            writeLog('weather', "成功获取天气数据", [
                'city' => isset($weatherData['data']['city']) ? $weatherData['data']['city'] : '未知',
                'temp' => isset($weatherData['data']['temp']) ? $weatherData['data']['temp'] : '未知',
                'weather' => isset($weatherData['data']['text']) ? $weatherData['data']['text'] : '未知',
                'source' => isset($weatherData['data']['_fallback']) ? 'fallback' : 'api'
            ]);
        }
    }
}

error_log("推荐API - 获取天气成功，城市: " . (isset($weatherData['data']['city']) ? $weatherData['data']['city'] : '未知'));

$weather = $weatherData['data'];

// 处理城市名称，确保没有重复
if (isset($weather['city'])) {
    $weather['city'] = formatCityDisplay($weather['city']);
}

// 确保天气数据中的关键字段都有默认值，避免未定义索引错误
ensureWeatherDataFields($weather);

// 尝试从缓存获取今天的推荐
if (!$refresh) {
    try {
        $stmt = $conn->prepare("
            SELECT recommendation_data 
            FROM outfit_recommendations 
            WHERE user_id = ? AND DATE(created_at) = ? 
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$userId, $today]);
        $recommendationRow = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($recommendationRow) {
            $recommendationData = json_decode($recommendationRow['recommendation_data'], true);
            
            // 确保数据结构正确，且包含天气信息
            if (isset($recommendationData['outfit']) && isset($recommendationData['weather'])) {
                echo json_encode([
                    'success' => true,
                    'data' => $recommendationData,
                    'source' => 'cache'
                ]);
                exit;
            }
        }
    } catch (Exception $e) {
        error_log("获取缓存推荐失败: " . $e->getMessage());
        // 继续生成新推荐
    }
}

// 如果没有缓存或需要刷新，生成新推荐
$outfit = generateOutfitRecommendation($userId, $weather, $db);

if (!$outfit) {
    echo json_encode([
        'success' => false,
        'msg' => '无法生成穿搭推荐，用户衣物可能不足'
    ]);
    exit;
}

// 保存推荐到数据库
try {
    $recommendationData = [
        'weather' => $weather,
        'outfit' => $outfit
    ];
    
    $stmt = $conn->prepare("
        INSERT INTO outfit_recommendations (user_id, recommendation_data, created_at) 
        VALUES (?, ?, NOW())
    ");
    $stmt->execute([$userId, json_encode($recommendationData)]);
} catch (Exception $e) {
    error_log("保存推荐失败: " . $e->getMessage());
    // 即使保存失败，也返回推荐结果
}

// 返回推荐结果
echo json_encode([
    'success' => true,
    'data' => [
        'weather' => $weather,
        'outfit' => $outfit
    ],
    'source' => 'new'
]);

/**
 * 获取图标信息
 * @param string $iconCode 图标代码
 * @return array 图标信息，包含URL或字体类名
 */
function getWeatherIconInfo($iconCode) {
    if (empty($iconCode)) {
        return [
            'url' => '',
            'font_class' => '',
            'use_font' => false
        ];
    }
    
    // 确保图标代码是完整的
    if (strlen($iconCode) < 2) {
        $iconCode = str_pad($iconCode, 2, '0', STR_PAD_LEFT);
    }
    
    // 返回图标信息
    return [
        'url' => WEATHER_ICON_URL . $iconCode . '.png',
        'font_class' => 'qi-' . $iconCode,
        'use_font' => WEATHER_ICON_USE_FONT,
        'css_url' => WEATHER_ICON_CSS_URL
    ];
}

/**
 * 获取天气数据
 * @param float|null $latitude 纬度
 * @param float|null $longitude 经度
 * @return array 天气数据
 */
function getWeatherData($latitude = null, $longitude = null) {
    global $isTestMode;
    
    // 记录函数调用
    writeLog('weather', "getWeatherData函数调用, latitude=$latitude, longitude=$longitude");
    
    // 测试模式下直接返回备用天气数据
    if ($isTestMode) {
        writeLog('weather', "处于测试模式，使用备用天气数据");
        return getFallbackWeatherData();
    }
    
    // 尝试直接从API获取真实天气数据
    try {
        $weatherData = getDirectWeatherData($latitude, $longitude);
        
        // 检查是否为2025年的静态测试数据
        if (isset($weatherData['obsTime']) && strpos($weatherData['obsTime'], '2025') !== false) {
            writeLog('weather', "发现静态测试数据(2025年)，尝试重新请求实时数据");
            
            // 尝试带缓存刷新参数再请求一次
            $weatherData = getDirectWeatherData($latitude, $longitude, true);
            
            // 如果仍然是2025年的数据，使用当前日期的备用数据
            if (isset($weatherData['obsTime']) && strpos($weatherData['obsTime'], '2025') !== false) {
                writeLog('weather', "仍然获得静态测试数据，使用当前日期的备用天气数据");
                $weatherData = getFallbackWeatherData();
            }
        }
        
        // 确保所有必需的字段都存在
        ensureWeatherDataFields($weatherData);
        return $weatherData;
    } catch (Exception $e) {
        writeLog('weather', "获取天气数据出错: " . $e->getMessage() . "，使用备用天气数据");
        
        // 出错时使用备用天气数据
        $weatherData = getFallbackWeatherData();
        ensureWeatherDataFields($weatherData);
        return $weatherData;
    }
}

/**
 * 生成穿搭推荐的总结
 */
function generateOutfitSummary($outfit, $weather, $season) {
    writeLog('outfit', "开始生成穿搭总结", [
        'outfit_items' => array_keys($outfit),
        'weather_temp' => $weather['temp'],
        'season' => $season
    ]);
    
    // 分析当前穿搭组合的特点
    $hasOuterwear = isset($outfit['outerwear']);
    $hasFullDress = isset($outfit['bottom']) && !empty($outfit['bottom']['is_full_dress']) && $outfit['bottom']['is_full_dress'] === true;
    $topBottomOutfit = isset($outfit['top']) && isset($outfit['bottom']);
    
    // 确定衣物类型，支持自定义分类
    $hasSkirt = false;
    $hasPants = false;
    
    if (isset($outfit['bottom'])) {
        $bottomCategory = $outfit['bottom']['category'] ?? '';
        // 检查分类代码或名称中是否包含与裙类相关的关键词
        $hasSkirt = stripos($bottomCategory, 'skirt') !== false || 
                   stripos($bottomCategory, 'dress') !== false || 
                   (isset($outfit['bottom']['name']) && (
                       stripos($outfit['bottom']['name'], '裙') !== false || 
                       stripos($outfit['bottom']['name'], '裙子') !== false || 
                       stripos($outfit['bottom']['name'], '连衣') !== false
                   ));
        
        // 检查分类代码或名称中是否包含与裤类相关的关键词
        $hasPants = stripos($bottomCategory, 'pant') !== false || 
                   (isset($outfit['bottom']['name']) && (
                       stripos($outfit['bottom']['name'], '裤') !== false || 
                       stripos($outfit['bottom']['name'], '裤子') !== false
                   ));
        
        // 如果无法确定，根据是否为连衣裙决定
        if (!$hasSkirt && !$hasPants) {
            $hasSkirt = $hasFullDress;
            $hasPants = !$hasFullDress;
        }
    }
    
    writeLog('outfit', "穿搭类型分析", [
        'has_outerwear' => $hasOuterwear,
        'has_full_dress' => $hasFullDress,
        'top_bottom_outfit' => $topBottomOutfit,
        'has_skirt' => $hasSkirt,
        'has_pants' => $hasPants
    ]);
    
    // 获取主要颜色
    $colors = [];
    foreach ($outfit as $key => $item) {
        if (isset($item['description']) && is_array($item['description']) && !empty($item['description']['颜色'])) {
            $colors[] = $item['description']['颜色'];
        }
    }
    $colors = array_unique($colors);
    $colorDesc = '';
    if (count($colors) == 1) {
        $colorDesc = $colors[0] . '为主色调';
    } elseif (count($colors) > 1) {
        $colorDesc = implode('、', array_slice($colors, 0, 3)) . '等颜色组合';
    }
    
    // 根据温度和天气生成建议
    $tempDesc = '';
    if ($weather['temp'] >= 30) {
        $tempDesc = '炎热天气';
    } elseif ($weather['temp'] >= 25) {
        $tempDesc = '偏热天气';
    } elseif ($weather['temp'] >= 20) {
        $tempDesc = '温暖天气';
    } elseif ($weather['temp'] >= 15) {
        $tempDesc = '舒适天气';
    } elseif ($weather['temp'] >= 10) {
        $tempDesc = '凉爽天气';
    } elseif ($weather['temp'] >= 5) {
        $tempDesc = '偏冷天气';
    } else {
        $tempDesc = '寒冷天气';
    }
    
    // 天气状况描述
    $weatherCondition = $weather['text'] ?? '';
    $weatherEffect = '';
    if (strpos($weatherCondition, '雨') !== false) {
        $weatherEffect = '，建议随身携带雨具';
    } elseif (strpos($weatherCondition, '雪') !== false) {
        $weatherEffect = '，注意防滑保暖';
    } elseif (strpos($weatherCondition, '风') !== false && $weather['windScale'] > 3) {
        $weatherEffect = '，注意防风保暖';
    } elseif (strpos($weatherCondition, '晴') !== false && $weather['temp'] > 25) {
        $weatherEffect = '，注意防晒补水';
    }
    
    // 构建穿搭总结模板
    $summaries = [];
    
    // 基于连衣裙的搭配
    if ($hasFullDress || ($hasSkirt && !$topBottomOutfit)) {
        $summaries[] = "这套以连衣裙为主的搭配，适合{$tempDesc}穿着，" . (!empty($colorDesc) ? "以{$colorDesc}，" : "") . "整体风格简约大方{$weatherEffect}。";
        $summaries[] = "连衣裙单品搭配方案，不仅方便穿着，也很适合{$season}季{$tempDesc}，" . (!empty($colorDesc) ? "{$colorDesc}，" : "") . "给人清新优雅的感觉{$weatherEffect}。";
        $summaries[] = "今日{$tempDesc}{$weatherCondition}，这身连衣裙搭配既轻松又不失时尚感，" . (!empty($colorDesc) ? "以{$colorDesc}，" : "") . "展现优雅气质{$weatherEffect}。";
    }
    // 上衣+下装的搭配
    elseif ($topBottomOutfit) {
        $bottomType = $hasSkirt ? '裙装' : '裤装';
        $summaries[] = "今日气温{$weather['temp']}°C，{$weatherCondition}，这套上衣搭配{$bottomType}的组合，" . (!empty($colorDesc) ? "以{$colorDesc}，" : "") . "整体协调又兼具个性{$weatherEffect}。";
        $summaries[] = "适合{$tempDesc}的上下身分体穿搭，" . (!empty($colorDesc) ? "{$colorDesc}，" : "") . "搭配合理，色彩和谐，展现{$season}季节感{$weatherEffect}。";
        $summaries[] = "这套{$bottomType}搭配，适合今日{$tempDesc}{$weatherCondition}，" . (!empty($colorDesc) ? "采用{$colorDesc}，" : "") . "时尚又实用{$weatherEffect}。";
    }
    // 其他组合
    else {
        $summaries[] = "基于今日{$tempDesc}{$weatherCondition}，推荐这套" . ($hasOuterwear ? '含外套的' : '') . "搭配组合，" . (!empty($colorDesc) ? "以{$colorDesc}，" : "") . "整体和谐统一{$weatherEffect}。";
        $summaries[] = "适合{$season}季节{$tempDesc}穿着的搭配方案，" . (!empty($colorDesc) ? "{$colorDesc}，" : "") . "单品之间相互呼应，展现个人风格{$weatherEffect}。";
        $summaries[] = "今日气温{$weather['temp']}°C，推荐这套" . ($hasOuterwear ? '带外套的' : '') . "穿搭组合，" . (!empty($colorDesc) ? "运用{$colorDesc}，" : "") . "舒适实用又不失时尚感{$weatherEffect}。";
    }
    
    // 随机选择一条总结
    $summary = $summaries[array_rand($summaries)];
    
    writeLog('outfit', "生成的穿搭总结", [
        'summary' => $summary
    ]);
    
    return $summary;
}

/**
 * 根据天气和用户衣物生成穿搭推荐
 */
function generateOutfitRecommendation($userId, $weather, $db) {
    writeLog('outfit', "开始生成穿搭推荐", [
        'user_id' => $userId,
        'weather_temp' => $weather['temp'],
        'weather_text' => $weather['text'],
        'weather_city' => $weather['city']
    ]);
    
    // 确保天气数据字段完整
    $weather = ensureWeatherDataFields($weather);
    
    // 根据温度判断季节
    writeLog('outfit', "季节判断前的天气数据", [
            'temp' => $weather['temp'],
        'text' => $weather['text'],
        'weather' => $weather['weather'],
        'city' => $weather['city']
    ]);
    
    $season = getSeasonByTemp($weather['temp']);
        
    // 根据当前温度选择衣物
    $tops = getUserClothingByCategory($userId, 'tops', $season, $db);
    $pants = getUserClothingByCategory($userId, 'pants', $season, $db);
    $skirts = getUserClothingByCategory($userId, 'skirts', $season, $db);
    $outerwears = getUserClothingByCategory($userId, 'outerwears', $season, $db);
    $shoes = getUserClothingByCategory($userId, 'shoes', $season, $db);
    $accessories = getUserClothingByCategory($userId, 'accessories', $season, $db);
    $bags = getUserClothingByCategory($userId, 'bags', $season, $db);
    
    // 确保所有变量都是数组
    if (!is_array($tops)) $tops = [];
    if (!is_array($pants)) $pants = [];
    if (!is_array($skirts)) $skirts = [];
    if (!is_array($outerwears)) $outerwears = [];
    if (!is_array($shoes)) $shoes = [];
    if (!is_array($accessories)) $accessories = [];
    if (!is_array($bags)) $bags = [];
        
    // 添加详细的衣物分类诊断信息
    // 记录按分类代码统计的衣物数量，帮助诊断自定义分类问题
    $clothingByCategory = [];
    $categoryCodes = [];
    
    // 处理所有衣物类别
    // 确保所有参数都是数组，防止array_merge错误
    $tops = is_array($tops) ? $tops : [];
    $pants = is_array($pants) ? $pants : [];
    $skirts = is_array($skirts) ? $skirts : [];
    $outerwears = is_array($outerwears) ? $outerwears : [];
    $shoes = is_array($shoes) ? $shoes : [];
    $accessories = is_array($accessories) ? $accessories : [];
    $bags = is_array($bags) ? $bags : [];
    
    $allClothes = array_merge($tops, $pants, $skirts, $outerwears, $shoes, $accessories, $bags);
    
    // 统计每个分类的衣物数量
    foreach ($allClothes as $item) {
        $categoryCode = $item['category'];
        if (!isset($clothingByCategory[$categoryCode])) {
            $clothingByCategory[$categoryCode] = 0;
            $categoryCodes[] = $categoryCode;
        }
        $clothingByCategory[$categoryCode]++;
    }
    
    // 记录衣物统计信息
        writeLog('outfit', "衣物详细统计", [
            'user_id' => $userId,
            'tops' => count($tops),
            'pants' => count($pants),
            'skirts' => count($skirts),
            'outerwears' => count($outerwears),
            'shoes' => count($shoes),
            'accessories' => count($accessories),
            'bags' => count($bags),
        'empty_items' => getEmptyClothingTypes($tops, $pants, $skirts, $outerwears, $shoes, $accessories, $bags),
            'season' => $season
        ]);
        
    // 记录分类代码统计
    writeLog('outfit', "分类代码统计", [
        'category_codes' => $categoryCodes,
        'counts_by_category' => $clothingByCategory
        ]);
        
    // 优先尝试使用AI生成穿搭推荐
    $useAI = true; // 默认使用AI推荐
    
    if ($useAI) {
        // 尝试使用AI生成穿搭
        $aiOutfit = tryGenerateAIOutfit($tops, $pants, $skirts, $outerwears, $shoes, $accessories, $bags, $weather, $season);
        
        if ($aiOutfit) {
            // 处理AI返回的推荐结果
            $processedOutfit = processAIRecommendation($aiOutfit, $tops, $pants, $skirts, $outerwears, $shoes, $accessories, $bags);
            
            if ($processedOutfit) {
                writeLog('outfit', "使用AI生成的穿搭推荐");
            
                // 如果没有穿搭总结，生成一个
                if (!isset($processedOutfit['outfit_summary'])) {
                    $processedOutfit['outfit_summary'] = generateOutfitSummary($processedOutfit, $weather, $season);
                }
                
                return $processedOutfit;
            }
        }
        
        writeLog('outfit', "AI穿搭推荐失败，回退到规则推荐");
    }
    
    // 如果AI推荐失败或未启用，使用规则生成穿搭
    $outfit = [];
    
    // 合并裤子和裙子作为底装选择
    $bottoms = array_merge($pants, $skirts);
        
            if (empty($tops) || empty($bottoms)) {
                error_log("无法生成穿搭推荐: 没有足够的上衣或底装");
                return null;
            }
            
            // 随机选择上衣
            $top = $tops[array_rand($tops)];
            $top['reason'] = generateRecommendationReason('top', $top, $weather, $season);
            $outfit['top'] = $top;
            
            // 随机选择底装(仅普通裙子或裤子)
            $bottom = $bottoms[array_rand($bottoms)];
            // 明确标记这不是连衣裙
            $bottom['is_full_dress'] = false;
            $bottom['reason'] = generateRecommendationReason('bottom', $bottom, $weather, $season);
            $outfit['bottom'] = $bottom;
            
            error_log("选择搭配: 上衣={$top['name']}(ID={$top['id']}), 底装={$bottom['name']}(ID={$bottom['id']})");
        
        // 根据温度决定是否添加外套
    if ($weather['temp'] < 20 && !empty($outerwears) && is_array($outerwears)) {
            $outerwear = $outerwears[array_rand($outerwears)];
            $outerwear['reason'] = generateRecommendationReason('outerwear', $outerwear, $weather, $season);
            $outfit['outerwear'] = $outerwear;
        }
        
    // 添加鞋子
    if (!empty($shoes)) {
        $shoe = $shoes[array_rand($shoes)];
        $shoe['reason'] = generateRecommendationReason('shoes', $shoe, $weather, $season);
        $outfit['shoes'] = $shoe;
    }
    
    // 添加配饰(可选)
    if (!empty($accessories) && rand(0, 1) == 1) {
            $accessory = $accessories[array_rand($accessories)];
        $accessory['reason'] = generateRecommendationReason('accessories', $accessory, $weather, $season);
            $outfit['accessories'] = $accessory;
        }
        
    // 添加包包(可选)
    if (!empty($bags) && rand(0, 1) == 1) {
            $bag = $bags[array_rand($bags)];
            $bag['reason'] = generateRecommendationReason('bag', $bag, $weather, $season);
            $outfit['bag'] = $bag;
        }
        
    // 生成穿搭总结
        $outfit['outfit_summary'] = generateOutfitSummary($outfit, $weather, $season);
        
        return $outfit;
}

/**
 * 使用AI分析裙子是否为全身裙
 */
function analyzeSkirtWithAI($skirt) {
    writeLog('ai_analysis', "开始分析裙子类型", [
        'skirt_id' => $skirt['id'],
        'skirt_name' => $skirt['name']
    ]);
    
    // 首先使用基本的关键词判断，如果名字中明确包含关键词，可以避免不必要的AI调用
    $fullDressKeywords = ['连衣裙', '全身裙', '连身裙', '长裙', 'dress'];
    $halfSkirtKeywords = ['半身裙', '短裙', 'a字裙', '百褶裙', '迷你裙', 'skirt'];
    
    $name = strtolower($skirt['name']);
    
    // 如果名称中明确包含全身裙关键词
    foreach ($fullDressKeywords as $keyword) {
        if (strpos($name, strtolower($keyword)) !== false) {
            writeLog('ai_analysis', "关键词匹配: {$skirt['name']} 包含关键词 {$keyword}，判定为连衣裙");
            return true;
        }
    }
    
    // 如果名称中明确包含半身裙关键词
    foreach ($halfSkirtKeywords as $keyword) {
        if (strpos($name, strtolower($keyword)) !== false) {
            writeLog('ai_analysis', "关键词匹配: {$skirt['name']} 包含关键词 {$keyword}，判定为半身裙");
            return false;
        }
    }
    
    // 如果有图片，尝试使用AI进行分析
    if (!empty($skirt['image_url'])) {
        writeLog('ai_analysis', "通过图片分析裙子类型", [
            'image_url' => $skirt['image_url']
        ]);
        
        try {
            // 构建AI分析请求
            $aiResult = callClothingAnalysisAPI($skirt['image_url'], "分析这件裙子是连衣裙(全身裙)还是半身裙。连衣裙覆盖上半身和下半身，半身裙只覆盖下半身。");
            
            if ($aiResult) {
                // 检查AI返回的结果
                writeLog('ai_analysis', "AI分析结果", [
                    'result' => $aiResult
                ]);
                
                // 检查AI返回的分类结果
                if (isset($aiResult['衣物类别'])) {
                    $category = strtolower($aiResult['衣物类别']);
                    
                    // 如果AI明确识别为连衣裙/裙装
                    if (strpos($category, '连衣裙') !== false || strpos($category, '裙装') !== false) {
                        writeLog('ai_analysis', "AI判定: {$skirt['name']} 被AI识别为连衣裙");
                        return true;
                    }
                    
                    // 如果AI明确识别为半身裙/裙子
                    if (strpos($category, '半身裙') !== false || strpos($category, '裙子') !== false) {
                        writeLog('ai_analysis', "AI判定: {$skirt['name']} 被AI识别为半身裙");
                        return false;
                    }
                }
                
                // 如果AI返回了标签，检查标签
                if (isset($aiResult['衣物标签']) && is_array($aiResult['衣物标签'])) {
                    foreach ($aiResult['衣物标签'] as $tag) {
                        $tag = strtolower($tag);
                        if (strpos($tag, '连衣') !== false || strpos($tag, '全身') !== false) {
                            writeLog('ai_analysis', "AI标签判定: {$skirt['name']} 的标签包含'连衣'或'全身'，判定为连衣裙");
                            return true;
                        }
                        if (strpos($tag, '半身') !== false) {
                            writeLog('ai_analysis', "AI标签判定: {$skirt['name']} 的标签包含'半身'，判定为半身裙");
                            return false;
                        }
                    }
                }
            }
        } catch (Exception $e) {
            writeLog('ai_analysis', "AI分析裙子失败", [
                'error' => $e->getMessage()
            ]);
            // AI分析失败，使用默认判断逻辑
        }
    }
    
    // 如果上述方法都未能确定，使用保守策略：
    // 未知类型的裙子默认作为普通半身裙处理，这样更安全
    writeLog('ai_analysis', "无法确定 {$skirt['name']} 的类型，默认判定为半身裙");
    return false;
}

/**
 * 尝试使用AI生成完整的穿搭推荐
 */
function tryGenerateAIOutfit($tops, $pants, $skirts, $outerwears, $shoes, $accessories, $bags, $weather, $season) {
    writeLog('ai_outfit', "开始准备AI穿搭推荐请求", [
        'clothes_count' => [
            'tops' => count($tops),
            'pants' => count($pants),
            'skirts' => count($skirts),
            'outerwears' => is_array($outerwears) ? count($outerwears) : 0,
            'shoes' => count($shoes)
        ],
        'weather' => [
            'text' => $weather['text'],
            'temp' => $weather['temp'],
            'humidity' => $weather['humidity'],
            'windDir' => $weather['windDir'],
            'windScale' => $weather['windScale']
        ],
        'season' => $season
    ]);
    
    // 如果衣物数量不足，无法进行推荐
    if ((count($tops) < 1 && count($skirts) < 1)) {
        writeLog('ai_outfit', "衣物数量不足，无法推荐", [
            'tops' => count($tops),
            'pants' => count($pants),
            'skirts' => count($skirts)
        ]);
        return null;
    }
    
    // 准备API请求数据
    $clothesData = [
        'tops' => [],
        'pants' => [],
        'skirts' => [],
        'outerwears' => [],
        'shoes' => [],
        'accessories' => [],
        'bags' => []
    ];
    
    // 添加上衣数据
    foreach ($tops as $item) {
        $clothesData['tops'][] = [
            'id' => $item['id'],
            'name' => $item['name'],
            'category' => $item['category'], // 保留category字段
            'tags' => isset($item['tags']) ? $item['tags'] : '',
            'description' => isset($item['description']) ? $item['description'] : ''
        ];
    }
    
    // 添加裤子数据
    foreach ($pants as $item) {
        $clothesData['pants'][] = [
            'id' => $item['id'],
            'name' => $item['name'],
            'category' => $item['category'], // 保留category字段
            'tags' => isset($item['tags']) ? $item['tags'] : '',
            'description' => isset($item['description']) ? $item['description'] : ''
        ];
    }
    
    // 添加裙子数据
    foreach ($skirts as $item) {
        $clothesData['skirts'][] = [
            'id' => $item['id'],
            'name' => $item['name'],
            'category' => $item['category'], // 保留category字段
            'tags' => isset($item['tags']) ? $item['tags'] : '',
            'description' => isset($item['description']) ? $item['description'] : ''
        ];
    }
    
    if (is_array($outerwears)) {
    foreach ($outerwears as $item) {
        $clothesData['outerwears'][] = [
            'id' => $item['id'],
            'name' => $item['name'],
            'category' => $item['category'], // 保留category字段
            'tags' => isset($item['tags']) ? $item['tags'] : '',
            'description' => isset($item['description']) ? $item['description'] : ''
        ];
        }
    }
    
    // 添加鞋子数据
    foreach ($shoes as $item) {
        $clothesData['shoes'][] = [
            'id' => $item['id'],
            'name' => $item['name'],
            'category' => $item['category'], // 保留category字段
            'tags' => isset($item['tags']) ? $item['tags'] : '',
            'description' => isset($item['description']) ? $item['description'] : ''
        ];
    }
    
    // 添加配饰数据
    foreach ($accessories as $item) {
        $clothesData['accessories'][] = [
            'id' => $item['id'],
            'name' => $item['name'],
            'category' => $item['category'], // 保留category字段
            'tags' => isset($item['tags']) ? $item['tags'] : '',
            'description' => isset($item['description']) ? $item['description'] : ''
        ];
    }
    
    // 添加包包数据
    foreach ($bags as $item) {
        $clothesData['bags'][] = [
            'id' => $item['id'],
            'name' => $item['name'],
            'category' => $item['category'], // 保留category字段
            'tags' => isset($item['tags']) ? $item['tags'] : '',
            'description' => isset($item['description']) ? $item['description'] : ''
        ];
    }
    
    // 统计每个分类的衣物数量
    $clothingStats = [
        'tops' => count($clothesData['tops']),
        'pants' => count($clothesData['pants']),
        'skirts' => count($clothesData['skirts']),
        'outerwears' => count($clothesData['outerwears']),
        'shoes' => count($clothesData['shoes']),
        'accessories' => count($clothesData['accessories']),
        'bags' => count($clothesData['bags'])
    ];
    
    // 确保天气数据包含城市名称
    if (!isset($weather['city']) && isset($weather['cityName'])) {
        $weather['city'] = $weather['cityName'];
    }
    
    // 尝试使用新的基于天气的AI穿搭推荐API
    $useNewApi = true;
    $apiUrl = "https://www.furrywoo.com/gemini/jiyutianqituijian.php";
    
    writeLog('ai_outfit', "使用" . ($useNewApi ? "天气穿搭推荐API" : "本地测试API"));
    
    // 准备API请求数据
    $postData = [
        'clothes_data' => json_encode($clothesData),
        'weather' => json_encode($weather),
        'season' => $season,
        'clothing_stats' => json_encode($clothingStats)
    ];
    
    // 记录请求数据大小
    writeLog('ai_outfit', "准备调用AI穿搭推荐API", [
        'api_url' => $apiUrl,
        'post_data_length' => strlen(json_encode($postData)),
        'total_items' => [
            'tops' => count($clothesData['tops']),
            'pants' => count($clothesData['pants']),
            'skirts' => count($clothesData['skirts']),
            'outerwears' => count($clothesData['outerwears']),
            'shoes' => count($clothesData['shoes']),
            'accessories' => count($clothesData['accessories']),
            'bags' => count($clothesData['bags']),
            'total' => count($clothesData['tops']) + count($clothesData['pants']) + 
                      count($clothesData['skirts']) + count($clothesData['outerwears']) + 
                      count($clothesData['shoes']) + count($clothesData['accessories']) + 
                      count($clothesData['bags'])
        ]
    ]);
    
    // 发送API请求
        $ch = curl_init($apiUrl);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($postData),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json'
        ],
        CURLOPT_TIMEOUT => 30
    ]);
    
        $response = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);
        
    // 检查是否有错误
        if ($error) {
            writeLog('ai_outfit', "调用AI穿搭推荐API失败", [
                'error' => $error
            ]);
        
        // 如果新API失败，尝试使用本地备选API
        if ($useNewApi) {
            writeLog('ai_outfit', "尝试使用本地备选API");
            $localApiUrl = "./gemini_outfit_api.php";
            
            $ch = curl_init($localApiUrl);
            curl_setopt_array($ch, [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode($postData),
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json'
                ],
                CURLOPT_TIMEOUT => 15
            ]);
            
            $response = curl_exec($ch);
            $error = curl_error($ch);
            curl_close($ch);
        
            if ($error) {
                writeLog('ai_outfit', "本地备选API也失败", [
                    'error' => $error
            ]);
            return null;
        }
        } else {
            return null;
        }
    }
        
    // 解析API响应
    $result = json_decode($response, true);
    
    // 检查响应是否有效
    if (!$result || (isset($result['error']) && !isset($result['data'])) || 
        (isset($result['success']) && $result['success'] === false)) {
        writeLog('ai_outfit', "API返回无效响应", [
            'response_preview' => substr($response, 0, 500),
            'error' => isset($result['error']) ? $result['error'] : '未知错误',
            'recommendation_hint' => isset($result['recommendation_hint']) ? $result['recommendation_hint'] : null,
            'debug_info' => isset($result['debug_info']) ? $result['debug_info'] : null
        ]);
        
        // 如果API返回了具体的错误提示，记录并返回更详细的信息
        if (isset($result['error']) && isset($result['recommendation_hint'])) {
            return [
                'error' => true,
                'error_message' => $result['error'],
                'recommendation_hint' => $result['recommendation_hint'],
                'outfit_summary' => "无法生成穿搭推荐：" . $result['error']
            ];
        }
        
            return null;
        }
        
    // 提取推荐结果
    $recommendation = isset($result['data']) ? $result['data'] : $result;
    
    writeLog('ai_outfit', "成功获取AI穿搭推荐", [
        'has_outfit' => isset($recommendation['outfit']),
        'has_reasons' => isset($recommendation['reasons']),
        'has_summary' => isset($recommendation['outfit_summary'])
        ]);
    
    return $recommendation;
}

// 处理AI推荐结果
function processAIRecommendation($recommendation, $tops, $pants, $skirts, $outerwears, $shoes, $accessories, $bags) {
    // 检查推荐数据是否有效
    if (!$recommendation || !isset($recommendation['outfit'])) {
        writeLog('ai_outfit', "处理AI推荐失败：无效的推荐数据");
        return null;
    }
    
    // 创建结果数组
    $outfit = [];
    
    // 添加总结信息
    if (isset($recommendation['outfit_summary'])) {
        $outfit['outfit_summary'] = $recommendation['outfit_summary'];
    }
    
    // 获取推荐原因
    $reasons = isset($recommendation['reasons']) ? $recommendation['reasons'] : [];
    
    // 处理上衣推荐
    if (isset($recommendation['outfit']['top'])) {
        $topId = $recommendation['outfit']['top'];
        foreach ($tops as $top) {
            if ($top['id'] == $topId) {
                $outfit['top'] = $top;
                $outfit['top']['reason'] = isset($reasons['top']) ? $reasons['top'] : '';
                
                // 记录分类信息
                writeLog('ai_outfit', "选择上衣", [
                    'id' => $top['id'],
                    'name' => $top['name'],
                    'category' => $top['category'] // 记录分类代码，方便调试
                ]);
                break;
            }
        }
    }
    
    // 处理下装推荐（裤子或裙子）
    if (isset($recommendation['outfit']['bottom'])) {
        $bottomId = $recommendation['outfit']['bottom'];
        $bottomFound = false;
        
        // 先检查裤子
        foreach ($pants as $pant) {
            if ($pant['id'] == $bottomId) {
                $outfit['bottom'] = $pant;
                $outfit['bottom']['reason'] = isset($reasons['bottom']) ? $reasons['bottom'] : '';
                $outfit['bottom']['is_full_dress'] = false; // 标记不是连衣裙
                $bottomFound = true;
                
                // 记录分类信息
                writeLog('ai_outfit', "选择裤子", [
                    'id' => $pant['id'],
                    'name' => $pant['name'],
                    'category' => $pant['category'] // 记录分类代码，方便调试
                ]);
                break;
            }
        }
        
        // 如果在裤子中没找到，检查裙子
        if (!$bottomFound) {
            foreach ($skirts as $skirt) {
                if ($skirt['id'] == $bottomId) {
                    $outfit['bottom'] = $skirt;
                    $outfit['bottom']['reason'] = isset($reasons['bottom']) ? $reasons['bottom'] : '';
                    $outfit['bottom']['is_full_dress'] = false; // 标记不是连衣裙
                    
                    // 记录分类信息
                    writeLog('ai_outfit', "选择裙子", [
                        'id' => $skirt['id'],
                        'name' => $skirt['name'],
                        'category' => $skirt['category'] // 记录分类代码，方便调试
                    ]);
                    break;
                }
            }
        }
    }
    
    // 处理外套推荐（可选）
    if (isset($recommendation['outfit']['outerwear']) && is_array($outerwears)) {
        $outerwearId = $recommendation['outfit']['outerwear'];
        foreach ($outerwears as $outerwear) {
            if ($outerwear['id'] == $outerwearId) {
                $outfit['outerwear'] = $outerwear;
                $outfit['outerwear']['reason'] = isset($reasons['outerwear']) ? $reasons['outerwear'] : '';
                
                // 记录分类信息
                writeLog('ai_outfit', "选择外套", [
                    'id' => $outerwear['id'],
                    'name' => $outerwear['name'],
                    'category' => $outerwear['category'] // 记录分类代码，方便调试
                ]);
                break;
            }
        }
    }
    
    // 处理鞋子推荐
    if (isset($recommendation['outfit']['shoes'])) {
        $shoeId = $recommendation['outfit']['shoes'];
        foreach ($shoes as $shoe) {
            if ($shoe['id'] == $shoeId) {
                $outfit['shoes'] = $shoe;
                $outfit['shoes']['reason'] = isset($reasons['shoes']) ? $reasons['shoes'] : '';
                
                // 记录分类信息
                writeLog('ai_outfit', "选择鞋子", [
                    'id' => $shoe['id'],
                    'name' => $shoe['name'],
                    'category' => $shoe['category'] // 记录分类代码，方便调试
                ]);
                break;
            }
        }
    }
    
    // 处理配饰推荐（可选）
    if (isset($recommendation['outfit']['accessories']) && !empty($accessories)) {
        $accessoryId = $recommendation['outfit']['accessories'];
        foreach ($accessories as $accessory) {
            if ($accessory['id'] == $accessoryId) {
                $outfit['accessories'] = $accessory;
                $outfit['accessories']['reason'] = isset($reasons['accessories']) ? $reasons['accessories'] : '';
                
                // 记录分类信息
                writeLog('ai_outfit', "选择配饰", [
                    'id' => $accessory['id'],
                    'name' => $accessory['name'],
                    'category' => $accessory['category'] // 记录分类代码，方便调试
                ]);
                break;
            }
        }
    }
    
    // 处理包包推荐（可选）
    if (isset($recommendation['outfit']['bag']) && !empty($bags)) {
        $bagId = $recommendation['outfit']['bag'];
        foreach ($bags as $bag) {
            if ($bag['id'] == $bagId) {
                $outfit['bag'] = $bag;
                $outfit['bag']['reason'] = isset($reasons['bag']) ? $reasons['bag'] : '';
                
                // 记录分类信息
                writeLog('ai_outfit', "选择包包", [
                    'id' => $bag['id'],
                    'name' => $bag['name'],
                    'category' => $bag['category'] // 记录分类代码，方便调试
                ]);
                break;
            }
        }
    }
    
    // 检查是否至少有上衣或下装
    if (!isset($outfit['top']) && !isset($outfit['bottom'])) {
        writeLog('ai_outfit', "处理AI推荐失败：没有找到匹配的上衣或下装");
        return null;
    }
    
    writeLog('ai_outfit', "AI穿搭推荐处理完成", [
        'items' => array_keys($outfit)
    ]);
    
    return $outfit;
}

/**
 * 调用衣物分析API
 */
function callClothingAnalysisAPI($imageUrl, $prompt = null) {
    $apiUrl = 'https://www.furrywoo.com/gemini/api.php';
    
    // 如果没有提供特定提示词，使用默认分析提示词
    if (!$prompt) {
        $prompt = "分析这件衣物的类别、风格和特征，按照JSON格式返回结果";
    }
    
    writeLog('api_call', "调用衣物分析API", [
        'image_url' => $imageUrl,
        'prompt' => $prompt,
        'api_url' => $apiUrl
    ]);
    
    try {
        // 获取图片数据
        $imageData = file_get_contents($imageUrl);
        if (!$imageData) {
            writeLog('api_call', "无法获取图片数据", [
                'image_url' => $imageUrl
            ]);
            return null;
        }
        
        // 准备API请求
        $imageBase64 = base64_encode($imageData);
        $fullBase64 = 'data:image/jpeg;base64,' . substr($imageBase64, 0, 100) . '...'; // 日志中只记录部分base64
        
        $postData = [
            'prompt' => $prompt,
            'image_base64' => $fullBase64 // 日志中只记录部分，实际请求使用完整的
        ];
        
        writeLog('api_call', "准备衣物分析API请求", [
            'base64_length' => strlen($imageBase64)
        ]);
        
        // 初始化cURL请求
        $ch = curl_init($apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, [
            'prompt' => $prompt,
            'image_base64' => 'data:image/jpeg;base64,' . $imageBase64
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 30秒超时
        
        // 执行请求
        writeLog('api_call', "执行衣物分析API请求");
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        // 检查请求是否成功
        if ($error) {
            writeLog('api_call', "调用衣物分析API失败", [
                'error' => $error
            ]);
            return null;
        }
        
        if ($httpCode !== 200) {
            writeLog('api_call', "衣物分析API返回非200状态码", [
                'http_code' => $httpCode,
                'response' => substr($response, 0, 500)
            ]);
            return null;
        }
        
        writeLog('api_call', "衣物分析API响应成功", [
            'response_length' => strlen($response)
        ]);
        
        // 解析响应
        $result = json_decode($response, true);
        
        // 从Gemini API的响应中提取内容
        if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            $textResponse = $result['candidates'][0]['content']['parts'][0]['text'];
            
            writeLog('api_call', "成功从衣物分析API提取文本响应", [
                'text_length' => strlen($textResponse),
                'text_preview' => substr($textResponse, 0, 200)
            ]);
            
            // 尝试从文本中提取JSON
            $extractedJson = extractJsonFromText($textResponse);
            
            if ($extractedJson) {
                writeLog('api_call', "成功从文本中提取JSON", [
                    'json_keys' => array_keys($extractedJson)
                ]);
            } else {
                writeLog('api_call', "无法从文本中提取JSON");
            }
            
            return $extractedJson;
        }
        
        writeLog('api_call', "衣物分析API响应格式不符合预期");
        return null;
        
    } catch (Exception $e) {
        writeLog('api_call', "衣物分析API调用异常", [
            'error' => $e->getMessage(),
            'stack_trace' => $e->getTraceAsString()
        ]);
        return null;
    }
}

/**
 * 调用文本AI API
 */
function callTextAIAPI($prompt) {
    $apiUrl = 'https://www.furrywoo.com/gemini/api.php';
    
    writeLog('api_call', "调用文本AI API", [
        'prompt_length' => strlen($prompt),
        'prompt_preview' => substr($prompt, 0, 200) . (strlen($prompt) > 200 ? '...' : ''),
        'api_url' => $apiUrl
    ]);
    
    try {
        // 准备API请求
        $postData = [
            'prompt' => $prompt
        ];
        
        // 初始化cURL请求
        $ch = curl_init($apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 30秒超时
        
        // 执行请求
        writeLog('api_call', "执行文本AI API请求");
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        // 检查请求是否成功
        if ($error) {
            writeLog('api_call', "调用文本AI API失败", [
                'error' => $error
            ]);
            return null;
        }
        
        if ($httpCode !== 200) {
            writeLog('api_call', "文本AI API返回非200状态码", [
                'http_code' => $httpCode,
                'response' => substr($response, 0, 500)
            ]);
            return null;
        }
        
        writeLog('api_call', "文本AI API响应成功", [
            'response_length' => strlen($response)
        ]);
        
        // 解析响应
        $result = json_decode($response, true);
        
        // 从Gemini API的响应中提取内容
        if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            $textResponse = $result['candidates'][0]['content']['parts'][0]['text'];
            
            writeLog('api_call', "成功从文本AI API提取响应", [
                'text_length' => strlen($textResponse),
                'text_preview' => substr($textResponse, 0, 200) . (strlen($textResponse) > 200 ? '...' : '')
            ]);
            
            return $textResponse;
        }
        
        writeLog('api_call', "文本AI API响应格式不符合预期");
        return null;
        
    } catch (Exception $e) {
        writeLog('api_call', "文本AI API调用异常", [
            'error' => $e->getMessage(),
            'stack_trace' => $e->getTraceAsString()
        ]);
        return null;
    }
}

/**
 * 从文本中提取JSON对象
 */
function extractJsonFromText($text) {
    writeLog('json_extract', "尝试从文本中提取JSON", [
        'text_length' => strlen($text),
        'text_preview' => substr($text, 0, 100) . (strlen($text) > 100 ? '...' : '')
    ]);
    
    // 尝试找到JSON部分
    if (preg_match('/\{.*\}/s', $text, $matches)) {
        $jsonStr = $matches[0];
        
        writeLog('json_extract', "通过正则表达式找到JSON字符串", [
            'json_length' => strlen($jsonStr),
            'json_preview' => substr($jsonStr, 0, 100) . (strlen($jsonStr) > 100 ? '...' : '')
        ]);
        
        // 尝试解析JSON
        $json = json_decode($jsonStr, true);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            writeLog('json_extract', "成功解析JSON字符串", [
                'json_keys' => array_keys($json)
            ]);
            return $json;
        } else {
            writeLog('json_extract', "JSON解析失败", [
                'error' => json_last_error_msg()
            ]);
        }
    } else {
        writeLog('json_extract', "未找到匹配的JSON部分");
    }
    
    // 尝试直接解析整个文本
    $json = json_decode($text, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        writeLog('json_extract', "直接解析文本为JSON成功", [
            'json_keys' => array_keys($json)
        ]);
        return $json;
    } else {
        writeLog('json_extract', "直接解析文本为JSON失败", [
            'error' => json_last_error_msg()
        ]);
    }
    
    writeLog('json_extract', "无法从文本中提取JSON");
    return null;
}

/**
 * 获取中文季节名称
 */
function getChineseSeason($season) {
    $seasonMap = [
        'spring' => '春季',
        'summer' => '夏季',
        'autumn' => '秋季',
        'winter' => '冬季'
    ];
    
    return $seasonMap[$season] ?? $season;
}

/**
 * 确保推荐表存在
 */
function ensureRecommendationTableExists($conn) {
    try {
        // 检查表是否存在
        $stmt = $conn->query("SHOW TABLES LIKE 'outfit_recommendations'");
        if ($stmt->rowCount() == 0) {
            // 表不存在，创建表
            $conn->exec("
                CREATE TABLE `outfit_recommendations` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `user_id` int(11) NOT NULL,
                  `recommendation_data` text NOT NULL,
                  `created_at` datetime NOT NULL,
                  PRIMARY KEY (`id`),
                  KEY `idx_user_created` (`user_id`,`created_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");
        }
    } catch (Exception $e) {
        error_log("检查或创建推荐表失败: " . $e->getMessage());
        // 继续执行，如果表已存在或有权限问题，可能会在后续操作中失败
    }
}

/**
 * 根据温度确定季节
 * @param mixed $temp 温度
 * @return string 季节（spring|summer|autumn|winter）
 */
function getSeasonByTemp($temp) {
    // 确保温度是数值
    if ($temp === null || !is_numeric($temp)) {
        writeLog('outfit', "温度为null或非数值，使用默认值22℃");
        $temp = 22;
    }
    
    // 将温度转换为数值
    $temp = floatval($temp);
    
    if ($temp >= 30) {
        return 'summer';
    } elseif ($temp >= 20) {
        return 'summer';
    } elseif ($temp >= 15) {
        return 'spring';
    } elseif ($temp >= 10) {
        return 'autumn';
    } else {
        return 'winter';
    }
}

/**
 * 获取用户指定类别的衣物
 * @param int $userId 用户ID
 * @param string $category 分类类型
 * @param string $season 季节
 * @param mixed $db 数据库对象或连接
 * @return array 衣物列表
 */
function getUserClothingByCategory($userId, $category, $season, $db) {
    try {
        // 确保我们有一个PDO连接
        $conn = null;
        if (is_object($db) && method_exists($db, 'getConnection')) {
            // 如果是Database对象，获取连接
            $conn = $db->getConnection();
            writeLog('outfit', "获取到数据库连接: " . get_class($conn));
        } elseif (is_object($db) && $db instanceof PDO) {
            // 如果已经是PDO连接，直接使用
            $conn = $db;
            writeLog('outfit', "已传入PDO连接");
        } else {
            // 无法确定连接类型
            writeLog('outfit', "无法获取有效的数据库连接", [
                'db_type' => is_object($db) ? get_class($db) : gettype($db)
            ]);
            return [];
        }
        
        // 获取该基础类型下的所有分类代码（系统分类+用户自定义分类）
        $categoryCodes = getCategoryCodesByType($userId, $category, $conn);
        
        if (empty($categoryCodes)) {
            // 如果没有找到任何分类代码，返回空数组
            writeLog('outfit', "未找到任何分类代码，category_type={$category}");
            return [];
        }
        
        // 记录找到的分类代码
        writeLog('outfit', "找到的分类代码 - category_type={$category}", [
            'category_codes' => $categoryCodes
        ]);
        
        // 构建SQL查询，支持多个分类代码
        $placeholders = str_repeat('?,', count($categoryCodes) - 1) . '?';
        $sql = "
            SELECT id, name, category, image_url, description, tags
            FROM clothes
            WHERE user_id = ? AND category IN ($placeholders)
        ";
        
        // 准备参数数组
        $params = [$userId];
        $params = array_merge($params, $categoryCodes);
        
        // 记录查询详情
        writeLog('outfit', "执行衣物查询", [
            'sql' => $sql,
            'params' => $params,
            'category_type' => $category,
            'category_codes' => $categoryCodes
        ]);
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        $clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 记录查询结果
        writeLog('outfit', "衣物查询结果", [
            'category_type' => $category,
            'found_clothes' => count($clothes),
            'query_returned_items' => min(3, count($clothes)) > 0 ? array_slice(array_map(function($item) {
                return ['id' => $item['id'], 'name' => $item['name']];
            }, $clothes), 0, 3) : []
        ]);
        
        // 如果在标准分类代码下没有找到衣物，尝试使用更宽泛的查询
        if (empty($clothes)) {
            writeLog('outfit', "标准分类代码下未找到衣物，尝试使用更宽泛的模糊匹配", [
                'category_type' => $category
            ]);
            
            // 尝试通过名称或标签模糊匹配找到相关衣物
            $keywordMap = [
                'tops' => ['上衣', '上装', '衬衫', 't恤', '毛衣', '卫衣', '背心'],
                'pants' => ['裤', '牛仔裤', '运动裤', '休闲裤', '西裤', '短裤'],
                'skirts' => ['裙', '连衣裙', '短裙', '长裙', '百褶裙', '半身裙'],
                'outerwear' => ['外套', '夹克', '大衣', '风衣', '羽绒服', '马甲'],
                'shoes' => ['鞋', '靴', '运动鞋', '皮鞋', '高跟鞋', '拖鞋', '凉鞋'],
                'bags' => ['包', '背包', '手包', '手提包', '钱包', '单肩包', '双肩包'],
                'accessories' => ['配饰', '饰品', '帽', '围巾', '手表', '项链', '手链', '手镯', '耳环', '发饰', '太阳镜']
            ];
            
            if (isset($keywordMap[$category])) {
                $keywords = $keywordMap[$category];
                $likeClauses = [];
                $likeParams = [$userId];
                
                foreach ($keywords as $keyword) {
                    $likeClauses[] = "name LIKE ?";
                    $likeClauses[] = "tags LIKE ?";
                    $likeParams[] = "%{$keyword}%";
                    $likeParams[] = "%{$keyword}%";
                }
                
                $likeSQL = "
                    SELECT id, name, category, image_url, description, tags
                    FROM clothes
                    WHERE user_id = ? AND (" . implode(" OR ", $likeClauses) . ")
                ";
                
                $likeStmt = $conn->prepare($likeSQL);
                $likeStmt->execute($likeParams);
                $clothes = $likeStmt->fetchAll(PDO::FETCH_ASSOC);
                
                writeLog('outfit', "通过名称和标签模糊匹配找到衣物", [
                    'category_type' => $category,
                    'keywords' => $keywords,
                    'found_clothes' => count($clothes)
                ]);
            }
        }
        
        // 过滤适合当前季节的衣物
        $seasonClothes = [];
        foreach ($clothes as $cloth) {
            $tags = !empty($cloth['tags']) ? explode(',', $cloth['tags']) : [];
            $description = !empty($cloth['description']) ? json_decode($cloth['description'], true) : [];
            
            // 从标签和描述中提取季节信息
            $clothSeason = '';
            if (!empty($description['季节'])) {
                $clothSeason = $description['季节'];
            } else {
                foreach ($tags as $tag) {
                    if (in_array(trim($tag), ['春季', '夏季', '秋季', '冬季'])) {
                        $clothSeason = trim($tag);
                        break;
                    }
                }
            }
            
            // 季节匹配或没有指定季节
            if (empty($clothSeason) || 
                ($season == 'spring' && $clothSeason == '春季') ||
                ($season == 'summer' && $clothSeason == '夏季') ||
                ($season == 'autumn' && $clothSeason == '秋季') ||
                ($season == 'winter' && $clothSeason == '冬季')) {
                    
                // 解析JSON格式的description
                if (!empty($cloth['description'])) {
                    $cloth['description'] = json_decode($cloth['description'], true);
                }
                
                $seasonClothes[] = $cloth;
            }
        }
        
        writeLog('outfit', "季节筛选后的衣物", [
            'category_type' => $category,
            'original_count' => count($clothes),
            'filtered_count' => count($seasonClothes),
            'season' => $season
        ]);
        
        return $seasonClothes;
        
    } catch (Exception $e) {
        error_log("获取用户衣物失败: " . $e->getMessage());
        writeLog('outfit', "获取用户衣物时发生错误", [
            'error' => $e->getMessage(),
            'category' => $category,
            'user_id' => $userId
        ]);
        return [];
    }
}

/**
 * 根据基础类型获取所有相关的分类代码
 * 包括系统分类和用户自定义分类
 * @param int $userId 用户ID
 * @param string $categoryType 分类类型
 * @param mixed $db 数据库对象或连接
 * @return array 分类代码列表
 */
function getCategoryCodesByType($userId, $categoryType, $db) {
    try {
        writeLog('outfit_debug', "开始获取分类代码 - category_type={$categoryType}, user_id={$userId}");
        
        // 标准化categoryType，处理单复数形式不一致的问题
        $normalizedCategoryType = $categoryType;
        if ($categoryType === 'outerwears') {
            $normalizedCategoryType = 'outerwear';
            writeLog('outfit_debug', "将categoryType从'outerwears'标准化为'outerwear'");
        }
        
        // 确保我们有一个PDO连接
        $conn = null;
        if (is_object($db) && method_exists($db, 'getConnection')) {
            // 如果是Database对象，获取连接
            $conn = $db->getConnection();
            writeLog('outfit_debug', "获取到数据库连接: " . get_class($conn));
        } elseif (is_object($db) && $db instanceof PDO) {
            // 如果已经是PDO连接，直接使用
            $conn = $db;
            writeLog('outfit_debug', "已传入PDO连接");
        } else {
            // 无法确定连接类型
            writeLog('outfit_debug', "无法获取有效的数据库连接", [
                'db_type' => is_object($db) ? get_class($db) : gettype($db)
            ]);
            
            // 返回默认系统分类
            $systemDefaults = [
                'tops' => ['tops'],
                'pants' => ['pants'], 
                'skirts' => ['skirts'],
                'outerwear' => ['outerwear'],  // 单数形式
                'outerwears' => ['outerwear'],  // 添加复数形式支持
                'shoes' => ['shoes'],
                'bags' => ['bags'],
                'accessories' => ['accessories']
            ];
            
            return $systemDefaults[$normalizedCategoryType] ?? [];
        }
        
        // 定义基础类型到系统分类的映射
        $systemCategoryMap = [
            'tops' => ['tops'],
            'pants' => ['pants'], 
            'skirts' => ['skirts'],
            'outerwear' => ['outerwear'], 
            'outerwears' => ['outerwear'], // 确保同时支持单数和复数形式
            'shoes' => ['shoes'],
            'bags' => ['bags'],
            'accessories' => ['accessories']
        ];
        
        // 获取系统分类代码
        $categoryCodes = $systemCategoryMap[$normalizedCategoryType] ?? [];
        
        writeLog('outfit_debug', "开始获取自定义分类", [
            'category_type' => $normalizedCategoryType,
            'user_id' => $userId,
            'initial_system_codes' => $categoryCodes
        ]);
        
        // 获取用户所有分类，包括系统分类副本和自定义分类
        $allCategoriesSql = "
            SELECT id, code, name, is_system 
            FROM clothing_categories 
            WHERE user_id = ?
        ";
        
        $stmt = $conn->prepare($allCategoriesSql);
        $stmt->execute([$userId]);
        $allCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        writeLog('outfit_debug', "获取到用户所有分类", [
            'count' => count($allCategories),
            'categories' => $allCategories
        ]);
        
        // 定义更多的关键词映射，提高分类识别准确性
        $keywordMap = [
            'tops' => ['上衣', '上装', 'top', '衬衫', 't恤', '毛衣', '卫衣', '背心', '汗衫', 'shirt', 'sweater', 'hoodie', 'vest', 'blouse', '上', '短袖', '长袖'],
            'pants' => ['裤', 'pant', '牛仔裤', '运动裤', '休闲裤', '西裤', '短裤', 'trouser', 'jean', 'shorts', '裤子', '长裤'],
            'skirts' => ['裙', 'skirt', 'dress', '连衣裙', '短裙', '长裙', '百褶裙', '半身裙', '裙子', '裙装'],
            'outerwear' => ['外套', 'coat', '夹克', '大衣', '风衣', '羽绒服', '马甲', 'jacket', 'outerwear', 'overcoat', '风', '外'],
            'outerwears' => ['外套', 'coat', '夹克', '大衣', '风衣', '羽绒服', '马甲', 'jacket', 'outerwear', 'overcoat', '风', '外'], // 复数形式支持
            'shoes' => ['鞋', 'shoe', '靴', '运动鞋', '皮鞋', '高跟鞋', '拖鞋', '凉鞋', 'boot', 'sneaker', 'slipper', 'sandal', '鞋子', '足'],
            'bags' => ['包', 'bag', '背包', '手包', '手提包', '钱包', '单肩包', '双肩包', 'purse', 'wallet', 'backpack', 'handbag', '包包', '袋'],
            'accessories' => ['配饰', '饰品', '帽', '围巾', '手表', '项链', '手链', '手镯', '耳环', '发饰', '太阳镜', 'accessory', 'hat', 'scarf', 'watch', 'necklace', 'bracelet', 'earring', 'glasses', '饰', '首饰']
        ];
        
        // 遍历用户所有分类
        foreach ($allCategories as $category) {
            $categoryName = strtolower($category['name']);
            $categoryCode = $category['code'];
            $matched = false;
            
            // 如果是对应的系统分类，直接加入
            if ($category['is_system'] == 1 && in_array($categoryCode, $systemCategoryMap[$normalizedCategoryType] ?? [])) {
                if (!in_array($categoryCode, $categoryCodes)) {
                    $categoryCodes[] = $categoryCode;
                    $matched = true;
                    writeLog('outfit_debug', "匹配系统分类", [
                        'category' => $category,
                        'matched_type' => $normalizedCategoryType
                    ]);
                }
                continue;
            }
            
            // 对于自定义分类，使用扩展的关键词匹配 - 先检查分类名称
            if (!$matched && isset($keywordMap[$normalizedCategoryType])) {
                foreach ($keywordMap[$normalizedCategoryType] as $keyword) {
                    if (stripos($categoryName, $keyword) !== false) {
                        if (!in_array($categoryCode, $categoryCodes)) {
                            $categoryCodes[] = $categoryCode;
                            $matched = true;
                            writeLog('outfit_debug', "通过名称关键词匹配自定义分类", [
                                'category' => $category,
                                'keyword' => $keyword,
                                'matched_type' => $normalizedCategoryType
                            ]);
                        }
                        break;
                    }
                }
            }
            
            // 如果名称没匹配上，检查分类编码
            if (!$matched && $category['is_system'] == 0) {
                // 如果编码中包含类型关键词
                foreach ($keywordMap[$normalizedCategoryType] as $keyword) {
                    if (stripos($categoryCode, $keyword) !== false) {
                        if (!in_array($categoryCode, $categoryCodes)) {
                            $categoryCodes[] = $categoryCode;
                            $matched = true;
                            writeLog('outfit_debug', "通过编码匹配自定义分类", [
                                'category' => $category,
                                'keyword' => $keyword,
                                'matched_type' => $normalizedCategoryType
                            ]);
                        }
                        break;
                    }
                }
            }
        }
        
        // 如果没有找到任何分类，尝试查找该用户的所有自定义分类
        if (empty($categoryCodes)) {
            writeLog('outfit_debug', "未找到匹配的系统或自定义分类，尝试获取所有自定义分类", [
                'category_type' => $normalizedCategoryType
            ]);
            
            // 获取所有自定义分类
            $customCategorySql = "
                SELECT code FROM clothing_categories 
                WHERE user_id = ? AND is_system = 0
            ";
            
            $stmt = $conn->prepare($customCategorySql);
            $stmt->execute([$userId]);
            $customCodes = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (!empty($customCodes)) {
                // 如果有自定义分类，添加所有这些分类代码
                $categoryCodes = array_merge($categoryCodes, $customCodes);
                writeLog('outfit_debug', "添加所有自定义分类（兜底方案）", [
                    'category_type' => $normalizedCategoryType,
                    'custom_codes' => $customCodes,
                    'all_codes' => $categoryCodes
                ]);
            }
        }
        
        // 确保至少有一个分类代码 - 添加特定类型查询
        if (empty($categoryCodes) && $normalizedCategoryType != 'outerwear') {
            writeLog('outfit_debug', "尝试获取未分类但可能相关的衣物，使用预定义的分类类型编码", [
                'category_type' => $normalizedCategoryType
            ]);
            
            // 如果还是没有分类代码，添加一个特定类型编码，用于后续模糊匹配
            switch($normalizedCategoryType) {
                case 'tops':
                    $categoryCodes[] = 'tops';
                    break;
                case 'pants':
                    $categoryCodes[] = 'pants';
                    break;
                case 'skirts':
                    $categoryCodes[] = 'skirts';
                    break;
                case 'shoes':
                    $categoryCodes[] = 'shoes';
                    break;
                case 'bags':
                    $categoryCodes[] = 'bags';
                    break;
                case 'accessories':
                    $categoryCodes[] = 'accessories';
                    break;
            }
        }
        
        writeLog('outfit', "获取分类代码映射完成", [
            'category_type' => $normalizedCategoryType,
            'user_id' => $userId,
            'system_codes' => $systemCategoryMap[$normalizedCategoryType] ?? [],
            'custom_codes' => array_diff($categoryCodes, $systemCategoryMap[$normalizedCategoryType] ?? []),
            'total_codes' => $categoryCodes
        ]);
        
        return array_unique($categoryCodes);
        
    } catch (Exception $e) {
        error_log("获取分类代码映射失败: " . $e->getMessage());
        writeLog('outfit', "获取分类代码映射时发生错误", [
            'error' => $e->getMessage(),
            'category_type' => $normalizedCategoryType,
            'user_id' => $userId
        ]);
        
        // 出错时返回系统默认分类
        $systemDefaults = [
            'tops' => ['tops'],
            'pants' => ['pants'], 
            'skirts' => ['skirts'],
            'outerwear' => ['outerwear'],  // 修正为outerwear
            'shoes' => ['shoes'],
            'bags' => ['bags'],
            'accessories' => ['accessories']
        ];
        
        return $systemDefaults[$normalizedCategoryType] ?? [];
    }
}

/**
 * 根据衣物类型、特征和天气生成推荐理由
 */
function generateRecommendationReason($type, $item, $weather, $season) {
    // 获取衣物颜色
    $color = '';
    if (!empty($item['description']) && is_array($item['description']) && isset($item['description']['颜色'])) {
        $color = $item['description']['颜色'];
    } elseif (!empty($item['description']) && is_string($item['description'])) {
        $desc = json_decode($item['description'], true);
        if (isset($desc['颜色'])) {
            $color = $desc['颜色'];
        }
    }
    
    // 获取材质
    $material = '';
    if (!empty($item['description']) && is_array($item['description']) && isset($item['description']['材质'])) {
        $material = $item['description']['材质'];
    } elseif (!empty($item['description']) && is_string($item['description'])) {
        $desc = json_decode($item['description'], true);
        if (isset($desc['材质'])) {
            $material = $desc['材质'];
        }
    }
    
    // 天气相关描述
    $weatherDesc = '';
    if ($weather['temp'] >= 30) {
        $weatherDesc = '炎热';
    } elseif ($weather['temp'] >= 25) {
        $weatherDesc = '偏热';
    } elseif ($weather['temp'] >= 20) {
        $weatherDesc = '温暖';
    } elseif ($weather['temp'] >= 15) {
        $weatherDesc = '舒适';
    } elseif ($weather['temp'] >= 10) {
        $weatherDesc = '凉爽';
    } elseif ($weather['temp'] >= 5) {
        $weatherDesc = '偏冷';
    } else {
        $weatherDesc = '寒冷';
    }
    
    // 天气状况描述
    $weatherCondition = $weather['text'] ?? '';
    
    // 根据不同衣物类型生成推荐理由
    $reasons = [];
    
    switch ($type) {
        case 'top':
            $reasons = [
                "当前气温{$weather['temp']}°C，天气{$weatherDesc}，这件" . ($color ? $color : '') . "上衣非常适合今天穿着。",
                "今天{$weatherCondition}天气，这件上衣搭配起来很和谐。",
                ($color ? "这件{$color}上衣" : "这件上衣") . "与今天{$weatherDesc}的气温很匹配。",
                ($material ? "这件{$material}材质的上衣" : "这件上衣") . "适合{$season}季节穿着。"
            ];
            break;
            
        case 'bottom':
            $category = ($item['category'] == 'pants') ? '裤子' : '裙子';
            $reasons = [
                "搭配这条" . ($color ? $color : '') . $category . "，整体风格更协调。",
                "这条{$category}与上衣的风格很搭，适合今天{$weatherDesc}的天气。",
                ($color ? "这条{$color}{$category}" : "这条{$category}") . "是{$season}季节的理想选择。",
                "考虑到今天{$weatherCondition}的天气，这条{$category}会让你感觉舒适。"
            ];
            break;
            
        case 'full_dress':
            $reasons = [
                "这件" . ($color ? $color : '') . "连衣裙适合{$weatherDesc}天气，一件穿搭即可完成造型。",
                "今天{$weatherCondition}天气，这件连衣裙是完美的选择，无需额外搭配上衣。",
                ($color ? "这件{$color}连衣裙" : "这件连衣裙") . "既美观又舒适，适合{$season}季节穿着。",
                "作为一体式设计的连衣裙，它非常适合今天{$weather['temp']}°C的气温。"
            ];
            break;
            
        case 'outerwear':
            $reasons = [
                "今天气温{$weather['temp']}°C，建议加一件" . ($color ? $color : '') . "外套保暖。",
                "这件外套适合{$weatherDesc}的天气，既保暖又不会太热。",
                ($color ? "这件{$color}外套" : "这件外套") . "非常适合{$season}季节的{$weatherCondition}天气。",
                ($material ? "这件{$material}材质的外套" : "这件外套") . "在{$weatherDesc}天气中提供适当的保暖。"
            ];
            break;
            
        case 'shoe':
            $reasons = [
                "这双" . ($color ? $color : '') . "鞋子与整体穿搭相得益彰。",
                "考虑到今天{$weatherCondition}的天气，这双鞋子是理想选择。",
                "这双鞋子的款式适合{$season}季节，并且与其他衣物搭配和谐。",
                ($color ? "这双{$color}鞋子" : "这双鞋子") . "能为整体造型增添亮点。"
            ];
            break;
            
        case 'accessory':
            $reasons = [
                "这款" . ($color ? $color : '') . "配饰能为整体造型增添亮点。",
                "这件配饰与今天的穿搭风格相符，增添细节美感。",
                "在{$weatherDesc}的天气中，这款配饰既实用又美观。",
                "这款配饰能为{$season}季节的穿搭锦上添花。"
            ];
            break;
            
        case 'bag':
            $reasons = [
                "这款" . ($color ? $color : '') . "包包与整体穿搭风格协调。",
                "这个包包适合{$weatherCondition}天气使用，既实用又美观。",
                ($color ? "这款{$color}包包" : "这款包包") . "是{$season}季节的理想选择。",
                "这款包包的风格与今天的着装相得益彰。"
            ];
            break;
            
        default:
            return "适合今天的天气和场合。";
    }
    
    // 随机选择一条理由
    return $reasons[array_rand($reasons)];
}

/**
 * 直接从天气API获取天气数据
 * @param float|null $latitude 纬度
 * @param float|null $longitude 经度
 * @param bool $forceRefresh 是否强制刷新
 * @return array 天气数据
 * @throws Exception 如果API调用失败
 */
function getDirectWeatherData($latitude = null, $longitude = null, $forceRefresh = false) {
    // 构建请求URL
    $apiUrl = API_DOMAIN . "/login_backend/get_weather.php";
    
    // 参数
    $params = [];
    if ($latitude !== null && $longitude !== null) {
        $params['latitude'] = $latitude;
        $params['longitude'] = $longitude;
    }
    
    // 添加强制刷新参数
    if ($forceRefresh) {
        $params['_nocache'] = time();
    }
    
    // 构建完整URL
    if (!empty($params)) {
        $apiUrl .= '?' . http_build_query($params);
    }
    
    writeLog('weather', "请求天气API: $apiUrl");
    
    // 发送请求
    $ch = curl_init($apiUrl);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false, // 禁用SSL验证以解决证书问题
        CURLOPT_SSL_VERIFYHOST => 0,     // 不验证主机名
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTPHEADER => [
            'Referer: https://cyyg.alidog.cn'  // 添加Referer头，解决域名限制问题
        ],
        CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4 // 强制使用IPv4，解决CDN问题
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error || $httpCode !== 200) {
        $errorMsg = $error ? $error : "HTTP错误码: $httpCode";
        writeLog('weather', "API请求失败: $errorMsg");
        throw new Exception("天气API请求失败: $errorMsg");
    }
    
    // 清理响应，去除前导和尾随空格
    $response = trim($response);
    
    // 记录响应前几个字符，用于调试
    writeLog('weather', "API响应前20个字符: " . substr($response, 0, 20));
    
    // 解析响应
    $result = json_decode($response, true);
    if (!$result || !isset($result['success']) || !$result['success']) {
        $errorMsg = isset($result['message']) ? $result['message'] : "无效的API响应";
        writeLog('weather', "API响应解析失败: $errorMsg", [
            'response_preview' => substr($response, 0, 100),
            'json_error' => json_last_error_msg(),
            'json_error_code' => json_last_error()
        ]);
        throw new Exception("天气API响应无效: $errorMsg");
    }
    
    writeLog('weather', "成功获取天气数据: " . json_encode(array_slice($result['data'], 0, 3)));
    
    return $result['data'];
}

/**
 * 获取备用天气数据（当API不可用时使用）
 * @return array 备用天气数据
 */
function getFallbackWeatherData() {
    writeLog('weather', "使用备用天气数据，所有API调用失败");
    
    // 获取当前日期和时间
    $now = time();
    $currentDate = date('Y-m-d', $now);
    $currentTime = date('H:i', $now);
    
    // 生成观测时间，使用当前日期而不是2025年
    $obsTime = $currentDate . 'T' . $currentTime . '+08:00';
    $updateTime = date('Y-m-d\TH:i:sP', $now + 300); // 更新时间比观测时间晚5分钟
    
    // 获取当前季节和根据季节设置合适的温度范围
    $month = (int)date('n', $now);
    $tempRanges = [
        'spring' => [12, 24], // 春季温度范围
        'summer' => [22, 35], // 夏季温度范围
        'autumn' => [10, 25], // 秋季温度范围
        'winter' => [-5, 15]  // 冬季温度范围
    ];
    
    // 确定季节
    if ($month >= 3 && $month <= 5) {
        $season = 'spring'; // 春季：3-5月
    } elseif ($month >= 6 && $month <= 8) {
        $season = 'summer'; // 夏季：6-8月
    } elseif ($month >= 9 && $month <= 11) {
        $season = 'autumn'; // 秋季：9-11月
    } else {
        $season = 'winter'; // 冬季：12-2月
    }
    
    // 根据季节生成合理的温度
    $tempRange = $tempRanges[$season];
    $temp = rand($tempRange[0], $tempRange[1]);
    
    // 根据季节和温度确定合适的天气现象
    $weatherConditions = [
        'spring' => ['晴', '多云', '阴', '小雨'],
        'summer' => ['晴', '多云', '雷阵雨', '大雨', '阵雨'],
        'autumn' => ['晴', '多云', '阴', '小雨', '雾'],
        'winter' => ['晴', '多云', '阴', '小雪', '霜冻']
    ];
    
    $condition = $weatherConditions[$season][array_rand($weatherConditions[$season])];
    
    // 根据天气现象确定图标
    $icons = [
        '晴' => '100',
        '多云' => '101',
        '阴' => '104',
        '小雨' => '305',
        '大雨' => '306',
        '雷阵雨' => '302',
        '阵雨' => '300',
        '小雪' => '400',
        '霜冻' => '500',
        '雾' => '501'
    ];
    $icon = $icons[$condition] ?? '999';
    
    // 创建备用天气数据
    $weatherData = [
        'obsTime' => $obsTime,
        'temp' => (string)$temp,
        'feelsLike' => (string)($temp + rand(-3, 3)),
        'icon' => $icon,
        'text' => $condition,
        'wind360' => (string)rand(0, 359),
        'windDir' => ['东风', '南风', '西风', '北风', '东北风', '东南风', '西南风', '西北风'][array_rand([0, 1, 2, 3, 4, 5, 6, 7])],
        'windScale' => (string)rand(1, 5),
        'windSpeed' => (string)rand(1, 30),
        'humidity' => (string)rand(30, 95),
        'precip' => (string)(rand(0, 20) / 10),
        'pressure' => (string)rand(990, 1020),
        'vis' => (string)rand(5, 30),
        'cloud' => (string)rand(0, 100),
        'dew' => (string)rand($temp - 10, $temp),
        'updateTime' => $updateTime,
        'fxLink' => 'https://www.qweather.com/weather/hangzhou-101210101.html',
        'city' => '杭州市',
        'cityid' => '101210101',
        '_realtime' => false,
        '_timestamp' => $now,
        '_fallback' => true, // 标记这是备用数据
    ];
    
    // 记录生成的备用天气数据
    writeLog('weather', "生成的备用天气数据", [
        'weather_data' => [
            'temp' => $weatherData['temp'],
            'text' => $weatherData['text'],
            'icon' => $weatherData['icon'],
            'icon_url' => WEATHER_ICON_URL . $weatherData['icon'] . '.png',
            'icon_font_class' => 'qi-' . $weatherData['icon'],
            'icon_css_url' => WEATHER_ICON_CSS_URL,
            'icon_use_font' => WEATHER_ICON_USE_FONT,
            'windDir' => $weatherData['windDir'],
            'windScale' => $weatherData['windScale'],
            'humidity' => $weatherData['humidity'],
            'updateTime' => $weatherData['updateTime'],
            'city' => $weatherData['city']
        ]
    ]);
    
    // 返回格式与API一致
    return [
        'success' => true,
        'data' => $weatherData,
        'msg' => '使用备用天气数据'
    ];
}

/**
 * 处理城市显示，消除重复信息，并将拼音转换为中文
 * 
 * @param string $cityText 原始城市信息文本
 * @return string 格式化后的城市文本
 */
function formatCityDisplay($cityText) {
    // 如果城市名为空，返回未知城市
    if (empty($cityText)) {
        writeLog('city', "城市名为空，返回'未知城市'");
        return '未知城市';
    }
    
    writeLog('city', "开始格式化城市名称", [
        'original_city' => $cityText
    ]);
    
    // 处理 "CN Hangzhou" 格式，这是和风天气API常见的格式
    if (preg_match('/^CN\s+(.+)$/i', $cityText, $matches)) {
        $cityName = $matches[1];
        writeLog('city', "检测到CN前缀格式，提取城市名: $cityName");
        
        // 常见城市英文名直接映射
        $enToCnMap = [
            'Beijing' => '北京市',
            'Shanghai' => '上海市',
            'Guangzhou' => '广州市',
            'Shenzhen' => '深圳市',
            'Hangzhou' => '杭州市',
            'Nanjing' => '南京市',
            'Chongqing' => '重庆市',
            'Wuhan' => '武汉市',
            'Tianjin' => '天津市',
            'Chengdu' => '成都市',
            'Suzhou' => '苏州市',
            'Xian' => "西安市",
            'Xi\'an' => '西安市'
        ];
        
        if (isset($enToCnMap[$cityName])) {
            $formattedCity = $enToCnMap[$cityName];
            writeLog('city', "直接映射英文城市名到中文: $cityName -> $formattedCity");
            return $formattedCity;
        }
        
        // 尝试转换为中文
        $cityText = $cityName;
    }
    
    // 处理"Zhejiang 浙江省,Hangzhou Hangzhou"这样的重复
    $parts = explode(',', $cityText);
    
    // 如果有多个部分，逐个处理
    if (count($parts) > 1) {
        $processedParts = [];
        foreach ($parts as $part) {
            // 处理内部空格分隔的部分
            $innerParts = explode(' ', trim($part));
            
            // 如果有重复的部分，只保留一个
            $uniqueParts = array_unique($innerParts);
            
            $processedParts[] = implode(' ', $uniqueParts);
    }
    
        $cityText = implode(', ', $processedParts);
    }
    
    // 尝试转换拼音为中文
    $chineseCity = convertPinyinToChinese($cityText);
    
    // 如果转换成功（与原始文本不同），则使用转换后的结果
    if ($chineseCity !== $cityText) {
        writeLog('city', "城市名已转换为中文", [
        'original' => $cityText,
            'converted' => $chineseCity
    ]);
        return $chineseCity;
    }
    
    // 如果无法转换，返回原始文本
    return $cityText;
}

/**
 * 将拼音城市名转换为中文城市名
 * 
 * @param string $pinyinCity 拼音城市名
 * @return string 中文城市名，如果找不到则返回原始拼音
 */
function convertPinyinToChinese($pinyinCity) {
    // 标准化输入 - 移除多余空格和逗号
    $pinyinCity = trim(preg_replace('/\s+/', ' ', $pinyinCity));
    
    // 分解省市
    $parts = explode(' ', $pinyinCity);
    
    // 常见省份拼音与中文对照
    $provinceMap = [
        'beijing' => '北京市',
        'tianjin' => '天津市',
        'hebei' => '河北省',
        'shanxi' => '山西省',
        'neimenggu' => '内蒙古自治区',
        'liaoning' => '辽宁省',
        'jilin' => '吉林省',
        'heilongjiang' => '黑龙江省',
        'shanghai' => '上海市',
        'jiangsu' => '江苏省',
        'zhejiang' => '浙江省',
        'anhui' => '安徽省',
        'fujian' => '福建省',
        'jiangxi' => '江西省',
        'shandong' => '山东省',
        'henan' => '河南省',
        'hubei' => '湖北省',
        'hunan' => '湖南省',
        'guangdong' => '广东省',
        'guangxi' => '广西壮族自治区',
        'hainan' => '海南省',
        'chongqing' => '重庆市',
        'sichuan' => '四川省',
        'guizhou' => '贵州省',
        'yunnan' => '云南省',
        'xizang' => '西藏自治区',
        'shaanxi' => '陕西省',
        'gansu' => '甘肃省',
        'qinghai' => '青海省',
        'ningxia' => '宁夏回族自治区',
        'xinjiang' => '新疆维吾尔自治区',
        'taiwan' => '台湾省',
        'xianggang' => '香港特别行政区',
        'aomen' => '澳门特别行政区'
    ];
    
    // 常见城市拼音与中文对照
    $cityMap = [
        'beijing' => '北京市',
        'shanghai' => '上海市',
        'guangzhou' => '广州市',
        'shenzhen' => '深圳市',
        'hangzhou' => '杭州市',
        'nanjing' => '南京市',
        'chongqing' => '重庆市',
        'wuhan' => '武汉市',
        'tianjin' => '天津市',
        'chengdu' => '成都市',
        'suzhou' => '苏州市',
        'xian' => '西安市',
        'zhengzhou' => '郑州市',
        'qingdao' => '青岛市',
        'changsha' => '长沙市',
        'fuzhou' => '福州市',
        'jinan' => '济南市',
        'xiamen' => '厦门市',
        'kunming' => '昆明市',
        'nanchang' => '南昌市',
        'hefei' => '合肥市',
        'changchun' => '长春市',
        'harbin' => '哈尔滨市',
        'dalian' => '大连市',
        'dongguan' => '东莞市',
        'foshan' => '佛山市',
    ];
    
    // 尝试从CSV文件加载城市数据
    $cityData = loadCityDataFromCsv();
    
    // 如果有CSV数据，优先使用它
    if (!empty($cityData)) {
        // 尝试查找匹配的城市
        foreach ($parts as $part) {
            $partLower = strtolower(trim($part));
            if (isset($cityData[$partLower])) {
                // 发现匹配，保存中文城市名
                $cityMap[$partLower] = $cityData[$partLower];
            }
        }
    }
    
    // 构建最终结果
    $result = [];
    foreach ($parts as $part) {
        $partLower = strtolower(trim($part));
        // 先查找省份
        if (isset($provinceMap[$partLower])) {
            $result[] = $provinceMap[$partLower];
        } 
        // 再查找城市
        else if (isset($cityMap[$partLower])) {
            $result[] = $cityMap[$partLower];
        }
        // 如果都找不到，保留原始部分
        else {
            $result[] = ucfirst($part);
        }
    }
    
    // 返回格式化后的中文城市名
    return implode(' ', $result);
}

/**
 * 从CSV文件加载城市数据
 * 
 * @return array 拼音=>中文的对照表
 */
function loadCityDataFromCsv() {
    static $cityData = null;
    
    // 如果已经加载过，直接返回
    if ($cityData !== null) {
        return $cityData;
    }
    
    $csvFile = __DIR__ . '/json/China-City-List-latest.csv';
    
    // 检查文件是否存在
    if (!file_exists($csvFile)) {
        error_log("城市CSV文件不存在: $csvFile");
        return [];
    }
    
    // 读取CSV文件
    $content = file_get_contents($csvFile);
    if ($content === false) {
        error_log("无法读取城市CSV文件");
        return [];
    }
    
    // 将内容拆分为行
    $lines = explode("\n", $content);
    
    // 移除第一行（标题行）
    array_shift($lines);
    
    $cityData = [];
    
    // 处理每一行
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        $fields = str_getcsv($line);
        if (count($fields) < 3) {
            continue; // 跳过格式不正确的行
        }
        
        // 预期CSV格式: ID,中文名,英文名/拼音,...
        if (!empty($fields[1]) && !empty($fields[2])) {
            $chineseName = trim($fields[1]);
            $pinyinName = strtolower(trim($fields[2]));
            
            // 添加拼音=>中文映射
            $cityData[$pinyinName] = $chineseName;
        }
    }
    
    error_log("从CSV加载了 " . count($cityData) . " 个城市映射");
    return $cityData;
}

/**
 * 确保天气数据包含所有必要的字段
 * @param array $weather 天气数据
 * @return array 处理后的天气数据
 */
function ensureWeatherDataFields(&$weather) {
    if (!is_array($weather)) {
        writeLog('weather', "天气数据非数组，初始化为空数组");
        $weather = [];
    }
    
    // 确保关键字段存在
    $defaultValues = [
        'temp' => '22', // 默认温度22℃
        'text' => '晴', // 默认天气状态
        'weather' => '晴', // 兼容字段
        'icon' => '100', // 默认图标
        'city' => '未知城市',
        'humidity' => '50', // 默认湿度
        'windDir' => '无风',
        'windScale' => '0'
    ];
    
    foreach ($defaultValues as $key => $defaultValue) {
        // 检查字段是否存在且非null
        if (!isset($weather[$key]) || $weather[$key] === null) {
            $weather[$key] = $defaultValue;
            writeLog('weather', "天气数据缺少{$key}字段，使用默认值: {$defaultValue}");
        }
        }
        
    // 如果weather字段缺失但text字段存在，或相反
    if (empty($weather['weather']) && !empty($weather['text'])) {
        $weather['weather'] = $weather['text'];
    } else if (empty($weather['text']) && !empty($weather['weather'])) {
        $weather['text'] = $weather['weather'];
        }
        
    // 记录处理后的天气数据
    writeLog('weather', "天气数据字段已确保完整", [
        'temp' => $weather['temp'],
        'text/weather' => $weather['text'],
        'city' => $weather['city']
    ]);
    
    return $weather;
    }
    
/**
 * 获取为空的衣物类型列表
 * @param array $tops 上衣列表
 * @param array $pants 裤子列表
 * @param array $skirts 裙子列表
 * @param array $outerwears 外套列表
 * @param array $shoes 鞋子列表
 * @param array $accessories 配饰列表
 * @param array $bags 包包列表
 * @return array 为空的衣物类型名称列表
 */
function getEmptyClothingTypes($tops, $pants, $skirts, $outerwears, $shoes, $accessories, $bags) {
    $emptyItems = [];
    if (empty($tops)) $emptyItems[] = '上衣';
    if (empty($pants)) $emptyItems[] = '裤子';
    if (empty($skirts)) $emptyItems[] = '裙子';
    if (empty($outerwears)) $emptyItems[] = '外套';
    if (empty($accessories)) $emptyItems[] = '配饰';
    if (empty($bags)) $emptyItems[] = '包包';
    if (empty($shoes)) $emptyItems[] = '鞋子';
    
    return $emptyItems;
} 