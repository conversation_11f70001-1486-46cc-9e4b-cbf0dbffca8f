<view class="container">
  <view class="header">
    <view class="title">衣物标签</view>
    <view class="subtitle">管理您的衣物标签，助力智能穿搭</view>
  </view>
  
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <image class="loading-icon" src="/images/loading.gif"></image>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
  
  <!-- 加载失败 -->
  <view class="error-container" wx:elif="{{loadFailed}}">
    <icon type="warn" size="60" color="#FF4D4F"></icon>
    <text class="error-text">加载失败，请重试</text>
    <button class="retry-button" bindtap="loadTagData">重新加载</button>
  </view>
  
  <!-- 标签分组显示 -->
  <view class="tag-groups-container" wx:else>
    <block wx:if="{{tagGroups.length > 0}}">
      <view class="tag-group" wx:for="{{tagGroups}}" wx:key="name" wx:for-item="group">
        <view class="group-title">{{group.name}}</view>
        <view class="group-tags">
          <view class="tag-item" 
                wx:for="{{group.tags}}" 
                wx:key="name" 
                bindtap="onTagTap" 
                data-tag="{{item.name}}">
            <text class="tag-name">{{item.name}}</text>
            <text class="tag-count">{{item.count}}</text>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 没有标签的提示 -->
    <view class="empty-container" wx:elif="{{tags.length === 0}}">
      <image class="empty-icon" src="/images/empty-box.png"></image>
      <text class="empty-text">暂无标签数据</text>
      <text class="empty-tip">您可以点击下方按钮，自动为衣物生成标签</text>
    </view>
  </view>
  
  <!-- 底部按钮 -->
  <view class="bottom-button-container">
    <button class="update-button" bindtap="goToUpdateTags">
      <text class="button-text">更新衣物标签</text>
    </button>
  </view>
</view> 