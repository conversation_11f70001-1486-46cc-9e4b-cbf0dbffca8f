<?php
// 启用输出缓冲，确保只返回JSON数据
ob_start();

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 全局错误处理，将PHP错误转为JSON响应
function handleError($errno, $errstr, $errfile, $errline) {
    $error_message = "PHP Error [$errno]: $errstr in $errfile on line $errline";
    logDebug("捕获到PHP错误", ['error' => $error_message]);
    
    // 清除之前的输出
    ob_clean();
    
    // 返回JSON格式的错误
    header('Content-Type: application/json');
    echo json_encode(['status' => 'error', 'message' => '服务器错误，请稍后重试']);
    exit;
}
set_error_handler('handleError');

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 日志函数
function logDebug($message, $data = null) {
    $log_file = __DIR__ . '/weather_recommendation_debug.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] {$message}";
    
    if ($data !== null) {
        $log_message .= ': ' . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    
    file_put_contents($log_file, $log_message . PHP_EOL, FILE_APPEND);
}

// 注册shutdown函数，确保在脚本结束时清除输出缓冲并返回JSON
function shutdownHandler() {
    $error = error_get_last();
    if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        // 清除之前的输出
        ob_clean();
        
        // 记录致命错误
        logDebug("捕获到致命错误", ['error' => $error]);
        
        // 返回JSON格式的错误
        header('Content-Type: application/json');
        echo json_encode(['status' => 'error', 'message' => '服务器发生错误，请稍后重试']);
    }
}
register_shutdown_function('shutdownHandler');

// 初始化数据库连接
$db = new Database();
$pdo = $db->getConnection();

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 验证用户令牌
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '缺少授权头']);
    exit();
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证用户token
$userData = $auth->verifyToken($token);
if (!$userData) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '无效或已过期的令牌']);
    exit();
}

// 获取用户ID
$user_id = $userData['sub'];
logDebug("处理用户请求", ['user_id' => $user_id]);

// 获取请求参数
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $refresh = isset($_GET['refresh']) ? intval($_GET['refresh']) : 0;
    $latitude = isset($_GET['latitude']) ? $_GET['latitude'] : '';
    $longitude = isset($_GET['longitude']) ? $_GET['longitude'] : '';
    $city_id = isset($_GET['city_id']) ? $_GET['city_id'] : '';
} else {
    $refresh = isset($_POST['refresh']) ? intval($_POST['refresh']) : 0;
    $latitude = isset($_POST['latitude']) ? $_POST['latitude'] : '';
    $longitude = isset($_POST['longitude']) ? $_POST['longitude'] : '';
    $city_id = isset($_POST['city_id']) ? $_POST['city_id'] : '';
}

logDebug("请求参数", [
    'refresh' => $refresh,
    'latitude' => $latitude,
    'longitude' => $longitude,
    'city_id' => $city_id
]);

// 检查必要参数
if (empty($latitude) && empty($longitude) && empty($city_id)) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少位置信息，请提供经纬度或城市ID'
    ]);
    logDebug("错误: 缺少位置信息");
    exit;
}

try {
    // 获取天气数据
    $weather_data = fetchWeatherData($latitude, $longitude, $city_id);
    if (isset($weather_data['error'])) {
        echo json_encode([
            'status' => 'error',
            'message' => '获取天气数据失败: ' . $weather_data['message']
        ]);
        logDebug("获取天气数据失败", $weather_data);
        exit;
    }
    
    // 强化验证：检查天气数据的完整性
    if (!isset($weather_data['location']) || !isset($weather_data['now']) ||
        !isset($weather_data['location']['name']) || !isset($weather_data['now']['temp']) || !isset($weather_data['now']['text']) ||
        $weather_data['location']['name'] === null || $weather_data['now']['temp'] === null || $weather_data['now']['text'] === null) {
        
        logDebug("天气数据不完整或关键字段为空", [
            'location' => isset($weather_data['location']) ? (isset($weather_data['location']['name']) ? $weather_data['location']['name'] : 'missing name') : 'missing location',
            'temp' => isset($weather_data['now']) ? (isset($weather_data['now']['temp']) ? $weather_data['now']['temp'] : 'missing temp') : 'missing now',
            'text' => isset($weather_data['now']) ? (isset($weather_data['now']['text']) ? $weather_data['now']['text'] : 'missing text') : 'missing now'
        ]);
        
        echo json_encode([
            'status' => 'error',
            'message' => '获取到的天气数据不完整，请稍后重试'
        ]);
        exit;
    }
    
    // 记录位置数据来源
    $location_source = 'unknown';
    if (!empty($latitude) && !empty($longitude)) {
        $location_source = 'coordinates';
    } else if (!empty($city_id)) {
        $location_source = 'city_id';
    } else if (isset($weather_data['is_default_location']) && $weather_data['is_default_location']) {
        $location_source = 'default';
    }
    
    // 在城市名称中标记默认位置
    if ($location_source === 'default' && isset($weather_data['location']['name'])) {
        // 在天气数据中添加位置来源标记
        $weather_data['location_source'] = $location_source;
    }
    
    logDebug("获取到的天气数据", [
        'location' => $weather_data['location']['name'],
        'temp' => $weather_data['now']['temp'],
        'text' => $weather_data['now']['text'],
        'source' => $location_source
    ]);
    
    // 构建天气键，用于数据库缓存
    $weather_key = '';
    if (!empty($weather_data['location']['id'])) {
        $weather_key = $weather_data['location']['id'];
    } else if (!empty($weather_data['location']['name'])) {
        $weather_key = $weather_data['location']['name'];
    } else {
        // 如果没有有效的location标识，使用请求参数作为备用
        $weather_key = !empty($city_id) ? 'city_' . $city_id : 'loc_' . $latitude . '_' . $longitude;
    }
    
    if (!empty($weather_data['now']['text']) && !empty($weather_data['now']['temp'])) {
        $weather_key .= '_' . $weather_data['now']['text'] . '_' . round($weather_data['now']['temp']);
    } else {
        $weather_key .= '_unknown_' . time(); // 添加时间戳确保唯一性
    }
    $weather_key = strtolower($weather_key); // 转小写以确保一致性
    
    logDebug("生成的天气键", ['weather_key' => $weather_key]);
    
    // 如果是刷新请求或者没有缓存的推荐结果，则生成新的推荐
    $should_generate_new = $refresh == 1;
    $cached_recommendation = null;
    
    // 获取最近的推荐结果，无论是否刷新都需要获取
    $stmt = $pdo->prepare("SELECT * FROM weather_based_recommendations 
                           WHERE user_id = :user_id AND weather_key = :weather_key
                           ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([
        ':user_id' => $user_id,
        ':weather_key' => $weather_key
    ]);
    $cached_recommendation = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$should_generate_new && $cached_recommendation) {
        // 检查缓存是否在近期（比如3小时内）
        $cached_time = strtotime($cached_recommendation['created_at']);
        $current_time = time();
        $time_diff = $current_time - $cached_time;
        
        // 如果缓存时间超过3小时，则生成新的推荐
        if ($time_diff > 10800) { // 10800秒=3小时
            $should_generate_new = true;
            logDebug("缓存已过期，需要生成新推荐", ['缓存时间' => $cached_recommendation['created_at'], '过期时间差(秒)' => $time_diff]);
        } else {
            logDebug("使用缓存的推荐结果", ['缓存时间' => $cached_recommendation['created_at']]);
        }
    } else if (!$should_generate_new) {
        $should_generate_new = true;
        logDebug("没有找到缓存的推荐结果，需要生成新推荐");
    } else {
        logDebug("请求强制刷新推荐结果");
    }
    
    if ($should_generate_new) {
        // 获取用户所有衣物
        $stmt = $pdo->prepare("SELECT * FROM clothes WHERE user_id = :user_id");
        $stmt->execute([
            ':user_id' => $user_id
        ]);
        $all_clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        logDebug("获取的用户衣物数量", ['count' => count($all_clothes)]);
        
        if (empty($all_clothes)) {
            echo json_encode([
                'status' => 'error',
                'message' => '您的衣橱中没有衣物，请先添加衣物后再使用该功能'
            ]);
            logDebug("错误: 用户衣橱为空");
            exit;
        }
        
        // 处理衣物数据，将description解析为JSON
        foreach ($all_clothes as $key => $clothing) {
            if (!empty($clothing['description'])) {
                $all_clothes[$key]['description'] = json_decode($clothing['description'], true);
            } else {
                $all_clothes[$key]['description'] = [];
            }
        }
        
        // 获取上一次的推荐结果（用于"换一批"功能）
        $previous_outfit = null;
        if ($refresh == 1 && $cached_recommendation) {
            $previous_outfit = json_decode($cached_recommendation['recommendation_data'], true);
            logDebug("获取上一次的推荐结果用于换一批功能", ['has_previous' => !empty($previous_outfit)]);
        }
        
        // 准备发送到中转API的数据
        $api_data = [
            'user_clothes' => $all_clothes,
            'weather_data' => $weather_data,
            'refresh' => $refresh
        ];
        
        // 如果是换一批请求且有上一次的推荐结果，添加到请求中
        if ($refresh == 1 && !empty($previous_outfit)) {
            $api_data['previous_outfit'] = $previous_outfit;
        }
        
        // 添加随机因子，确保每次请求都不同
        $api_data['random_factor'] = time() . '_' . mt_rand(1000, 9999);
        
        logDebug("准备发送到API的数据结构", [
            'user_clothes_count' => count($all_clothes),
            'weather_data' => [
                'location' => $weather_data['location']['name'],
                'temp' => $weather_data['now']['temp'],
                'text' => $weather_data['now']['text']
            ],
            'refresh' => $refresh,
            'has_previous_outfit' => isset($api_data['previous_outfit']),
            'random_factor' => $api_data['random_factor']
        ]);
        
        // 调用中转API获取推荐结果
        $ch = curl_init('https://www.furrywoo.com/gemini/tianqituijian.php');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($api_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 设置超时时间为60秒
        
        logDebug("正在调用中转API");
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            logDebug("API调用失败", ['curl_error' => $error]);
            echo json_encode(['status' => 'error', 'message' => '调用推荐服务失败: ' . $error]);
            exit;
        }
        curl_close($ch);
        logDebug("中转API调用完成");
        
        // 解析响应
        $recommendation_result = json_decode($response, true);
        if (!$recommendation_result || isset($recommendation_result['error'])) {
            $error_message = isset($recommendation_result['error']) ? $recommendation_result['error'] : '解析推荐结果失败';
            logDebug("解析API响应失败", ['error' => $error_message, 'raw_response' => substr($response, 0, 1000)]);
            echo json_encode([
                'status' => 'error', 
                'message' => $error_message
            ]);
            exit;
        }
        logDebug("API返回的推荐结果结构", array_keys($recommendation_result));
        
        // 检查并修复推荐结果中的图片URL
        logDebug("开始修复图片URL");
        $recommendation_result = fixClothingImageUrls($recommendation_result, $pdo, $user_id);
        
        // 确保weather_based_recommendations表存在
        ensureWeatherRecommendationTableExists($pdo);
        
        // 存储推荐结果到数据库
        $stmt = $pdo->prepare("INSERT INTO weather_based_recommendations 
                              (user_id, weather_key, weather_data, recommendation_data, created_at) 
                              VALUES (:user_id, :weather_key, :weather_data, :recommendation_data, NOW())");
        $stmt->execute([
            ':user_id' => $user_id,
            ':weather_key' => $weather_key,
            ':weather_data' => json_encode($weather_data),
            ':recommendation_data' => json_encode($recommendation_result)
        ]);
        logDebug("已保存推荐结果到数据库");
        
        // 添加天气数据到响应
        $recommendation_result['weather_data'] = [
            'city' => $weather_data['location']['name'],
            'temp' => $weather_data['now']['temp'],
            'text' => $weather_data['now']['text'],
            'windDir' => $weather_data['now']['windDir'] ?? '未知',
            'windScale' => $weather_data['now']['windScale'] ?? '--',
            'humidity' => $weather_data['now']['humidity'] ?? '--',
            'feelsLike' => $weather_data['now']['feelsLike'] ?? '--'
        ];
        
        // 返回推荐结果
        echo json_encode([
            'status' => 'success',
            'recommendation' => $recommendation_result
        ]);
        logDebug("已向客户端返回新的推荐结果");
    } else {
        // 获取缓存的推荐结果
        $cached_data = json_decode($cached_recommendation['recommendation_data'], true);
        logDebug("从缓存获取的推荐结果结构", array_keys($cached_data));
        
        // 检查缓存的推荐结果中的图片URL是否需要更新
        logDebug("开始检查缓存结果的图片URL");
        $cached_data = fixClothingImageUrls($cached_data, $pdo, $user_id);
        
        // 添加天气数据到响应
        $cached_data['weather_data'] = [
            'city' => $weather_data['location']['name'],
            'temp' => $weather_data['now']['temp'],
            'text' => $weather_data['now']['text'],
            'windDir' => $weather_data['now']['windDir'] ?? '未知',
            'windScale' => $weather_data['now']['windScale'] ?? '--',
            'humidity' => $weather_data['now']['humidity'] ?? '--',
            'feelsLike' => $weather_data['now']['feelsLike'] ?? '--'
        ];
        
        // 返回缓存的推荐结果
        echo json_encode([
            'status' => 'success',
            'recommendation' => $cached_data,
            'cached' => true
        ]);
        logDebug("已向客户端返回缓存的推荐结果");
    }
} catch (Exception $e) {
    logDebug("处理过程中发生异常", ['exception' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
    
    // 清除之前的输出
    ob_clean();
    
    // 设置状态码并返回JSON错误信息
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => '服务器处理异常: ' . $e->getMessage()]);
} catch (Throwable $t) {
    logDebug("处理过程中发生严重错误", ['error' => $t->getMessage(), 'trace' => $t->getTraceAsString()]);
    
    // 清除之前的输出
    ob_clean();
    
    // 设置状态码并返回JSON错误信息
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => '服务器处理错误，请稍后重试']);
}

// 确保所有输出都经过输出缓冲
ob_end_flush();

/**
 * 获取天气数据
 * @param string $latitude 纬度
 * @param string $longitude 经度
 * @param string $city_id 城市ID
 * @return array 天气数据或错误信息
 */
function fetchWeatherData($latitude, $longitude, $city_id) {
    logDebug("开始获取天气数据", [
        'latitude' => $latitude,
        'longitude' => $longitude,
        'city_id' => $city_id,
        'from_request' => [
            'lat' => $_GET['lat'] ?? null,
            'lon' => $_GET['lon'] ?? null,
            'latitude' => $_GET['latitude'] ?? null,
            'longitude' => $_GET['longitude'] ?? null,
            'location' => $_GET['location'] ?? null,
            'cityid' => $_GET['cityid'] ?? null,
            'city' => $_GET['city'] ?? null
        ]
    ]);
    
    // 检查是否通过location参数传递了经纬度
    $location = $_GET['location'] ?? null;
    if (empty($latitude) && empty($longitude) && !empty($location)) {
        // 尝试解析location参数（格式：经度,纬度）
        $locationParts = explode(',', $location);
        if (count($locationParts) === 2) {
            $longitude = trim($locationParts[0]);
            $latitude = trim($locationParts[1]);
            logDebug("从location参数解析经纬度", [
                'location' => $location,
                'parsed_longitude' => $longitude,
                'parsed_latitude' => $latitude
            ]);
        }
    }
    
    // 用于保存中文城市名称
    $chinese_city_name = null;
    
    // 如果有经纬度但没有城市ID，先查询城市信息
    if (!empty($latitude) && !empty($longitude) && empty($city_id)) {
        // 调用城市查询API
        $city_api_url = API_DOMAIN . '/login_backend/get_city_by_location.php?latitude=' . urlencode($latitude) . '&longitude=' . urlencode($longitude);
        
        logDebug("尝试获取城市信息", ['url' => $city_api_url]);
        
        $ch = curl_init($city_api_url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 5,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => 0,
            CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            $result = json_decode($response, true);
            if ($result && isset($result['success']) && $result['success'] && isset($result['city']['id'])) {
                // 更新城市ID
                $city_id = $result['city']['id'];
                
                // 保存中文城市名称，优先使用fullName，其次使用name
                $chinese_city_name = $result['city']['fullName'] ?? $result['city']['name'];
                
                logDebug("成功获取城市信息", [
                    'city_id' => $city_id,
                    'name' => $result['city']['name'],
                    'chinese_name' => $chinese_city_name,
                    'fullName' => $result['city']['fullName'] ?? $result['city']['name']
                ]);
            } else {
                logDebug("获取城市信息失败", ['response' => $response]);
            }
        } else {
            logDebug("调用城市API失败", ['http_code' => $httpCode]);
        }
    }
    
    $params = [];
    
    // 根据提供的参数构建请求
    if (!empty($city_id)) {
        $params['location'] = $city_id;
    } elseif (!empty($latitude) && !empty($longitude)) {
        $params['location'] = $longitude . ',' . $latitude;
    } else {
        return ['error' => true, 'message' => '缺少位置信息参数'];
    }
    
    // 添加强制刷新和时间戳参数
    $params['_nocache'] = '1';
    $params['t'] = time();
    
    // 构建API URL
    $query = http_build_query($params);
    $api_url = API_DOMAIN . '/login_backend/get_weather.php?' . $query;
    
    logDebug("调用天气API", ['url' => $api_url]);
    
    // 发起请求
    $ch = curl_init($api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 设置超时时间
    
    $response = curl_exec($ch);
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        logDebug("天气API调用失败", ['curl_error' => $error]);
        return ['error' => true, 'message' => '获取天气数据失败: ' . $error];
    }
    
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code != 200) {
        logDebug("天气API返回非200状态码", ['http_code' => $http_code]);
        return ['error' => true, 'message' => '天气服务返回错误状态码: ' . $http_code];
    }
    
    // 记录原始响应
    logDebug("天气API原始响应", ['raw_response' => substr($response, 0, 500)]);
    
    // 检查响应是否为空
    if (empty($response)) {
        logDebug("天气API返回空响应");
        return ['error' => true, 'message' => '天气服务返回空响应'];
    }
    
    // 检查响应是否包含PHP错误信息
    if (strpos($response, 'Fatal error') !== false || strpos($response, 'Parse error') !== false) {
        logDebug("天气API返回PHP错误", ['error_response' => substr($response, 0, 500)]);
        return ['error' => true, 'message' => '天气服务返回PHP错误'];
    }
    
    // 尝试解析响应
    $weather_data = @json_decode($response, true);
    if (!$weather_data) {
        // 检查响应是否是HTML而非JSON
        if (preg_match('/<html|<!DOCTYPE html|<body/i', $response)) {
            logDebug("天气API返回HTML而非JSON", ['response_excerpt' => substr($response, 0, 200)]);
            return ['error' => true, 'message' => '天气服务返回了非JSON格式的响应'];
        }
        
        logDebug("解析天气API响应失败", ['raw_response' => substr($response, 0, 500), 'json_error' => json_last_error_msg()]);
        return ['error' => true, 'message' => '解析天气数据失败: ' . json_last_error_msg()];
    }
    
    // 检查并处理success/data格式的响应
    if (isset($weather_data['success']) && $weather_data['success'] === true && isset($weather_data['data'])) {
        logDebug("检测到success/data格式的天气数据，进行格式转换");
        
        $data = $weather_data['data'];
        $standardized_data = [];
        
        // 常用城市英文名到中文名的映射
        $cityNameMapping = [
            'hangzhou' => '杭州',
            'shanghai' => '上海',
            'beijing' => '北京',
            'guangzhou' => '广州',
            'shenzhen' => '深圳',
            'suzhou' => '苏州',
            'nanjing' => '南京',
            'wuhan' => '武汉',
            'chengdu' => '成都',
            'chongqing' => '重庆',
            'tianjin' => '天津',
            'xiamen' => '厦门',
            'xian' => '西安',
            'qingdao' => '青岛',
            'dalian' => '大连',
            'ningbo' => '宁波',
            'shenyang' => '沈阳',
            'changsha' => '长沙',
            'kunming' => '昆明',
            'zhengzhou' => '郑州',
            'fuzhou' => '福州',
            'jinan' => '济南',
            'hefei' => '合肥',
            'changchun' => '长春',
            'linping' => '临平', // 添加临平的映射
            'yuhang' => '余杭区'
        ];
        
        // 检查是否在数据中已经有中文城市名
        if (isset($data['city']) && $chinese_city_name === null) {
            // 检查API返回的城市名是否为中文
            if (preg_match('/[\x{4e00}-\x{9fa5}]/u', $data['city'])) {
                $chinese_city_name = $data['city'];
                logDebug("从API返回数据中获取中文城市名", ['city' => $chinese_city_name]);
            }
        }
        
        // 获取城市名称，优先使用已获取的中文名称
        if ($chinese_city_name !== null) {
            $cityName = $chinese_city_name;
            logDebug("使用中文城市名称", ['chinese_city_name' => $chinese_city_name]);
        } else {
            // 尝试从API响应获取城市名
            $cityName = $data['city'] ?? (!empty($city_id) ? $city_id : "未知城市");
            
            // 检查是否有对应的中文映射
            if (isset($cityNameMapping[strtolower($cityName)])) {
                $cityName = $cityNameMapping[strtolower($cityName)];
                logDebug("使用映射表转换城市名称", ['original' => $data['city'], 'converted' => $cityName]);
            }
        }
        
        // 构建location部分
        $standardized_data['location'] = [
            'name' => $cityName,
            'id' => $data['cityid'] ?? '',
        ];
        
        // 构建now部分，确保包含前端所需的所有字段
        $standardized_data['now'] = [
            'temp' => $data['temp'] ?? '--',              // 温度
            'text' => $data['text'] ?? '未知',            // 天气状况
            'feelsLike' => $data['feelsLike'] ?? '--',    // 体感温度
            'windDir' => $data['windDir'] ?? '未知',      // 风向
            'windScale' => $data['windScale'] ?? '--',    // 风力等级
            'humidity' => $data['humidity'] ?? '--',      // 湿度
            'pressure' => $data['pressure'] ?? '--',      // 气压
            'vis' => $data['vis'] ?? '--',                // 能见度
            'precip' => $data['precip'] ?? '0',           // 降水量
        ];
        
        // 保存原始数据用于调试
        $standardized_data['_original_data'] = $data;
        
        // 替换为标准格式
        $weather_data = $standardized_data;
        
        logDebug("转换后的天气数据格式", [
            'location' => $weather_data['location'],
            'now' => array_intersect_key($weather_data['now'], ['temp' => 1, 'text' => 1, 'windDir' => 1, 'windScale' => 1, 'humidity' => 1])
        ]);
    }
    
    if (isset($weather_data['code']) && $weather_data['code'] != '200') {
        logDebug("天气API返回错误码", ['code' => $weather_data['code'], 'msg' => $weather_data['msg'] ?? '未知错误']);
        return ['error' => true, 'message' => '天气服务返回错误: ' . ($weather_data['msg'] ?? '未知错误')];
    }
    
    // 验证关键字段是否存在
    if (!isset($weather_data['location']) || !isset($weather_data['now'])) {
        logDebug("天气数据缺少关键部分", ['has_location' => isset($weather_data['location']), 'has_now' => isset($weather_data['now'])]);
        return ['error' => true, 'message' => '天气数据格式不完整'];
    }
    
    // 确保location和now对象中有必要的字段，如果没有则设置默认值
    if (!isset($weather_data['location']['name']) || $weather_data['location']['name'] === null) {
        logDebug("天气数据缺少城市名称");
        $weather_data['location']['name'] = !empty($city_id) ? $city_id : "未知城市";
    }
    
    if (!isset($weather_data['now']['temp']) || $weather_data['now']['temp'] === null) {
        logDebug("天气数据缺少温度");
        $weather_data['now']['temp'] = "--";
    }
    
    if (!isset($weather_data['now']['text']) || $weather_data['now']['text'] === null) {
        logDebug("天气数据缺少天气状况");
        $weather_data['now']['text'] = "未知";
    }
    
    logDebug("成功获取天气数据");
    return $weather_data;
}

/**
 * 检查并修复推荐结果中的图片URL
 * @param array $recommendation_result 推荐结果
 * @param PDO $pdo 数据库连接
 * @param int $user_id 用户ID
 * @return array 修复后的推荐结果
 */
function fixClothingImageUrls($recommendation_result, $pdo, $user_id) {
    logDebug("进入fixClothingImageUrls函数", ['result_type' => gettype($recommendation_result)]);
    
    if (!is_array($recommendation_result)) {
        logDebug("推荐结果不是数组，无法处理", ['actual_type' => gettype($recommendation_result)]);
        return $recommendation_result;
    }
    
    // 记录推荐结果的原始结构
    logDebug("推荐结果原始结构", array_keys($recommendation_result));
    
    // 定义各类别的默认图片URL
    $default_image_urls = [
        'top' => 'https://images.alidog.cn/default/top_default.jpg',
        'bottom' => 'https://images.alidog.cn/default/bottom_default.jpg',
        'outerwear' => 'https://images.alidog.cn/default/outerwear_default.jpg',
        'shoes' => 'https://images.alidog.cn/default/shoes_default.jpg',
        'accessories' => 'https://images.alidog.cn/default/accessories_default.jpg',
        'bag' => 'https://images.alidog.cn/default/bag_default.jpg'
    ];
    
    // 收集需要查询的所有衣物ID
    $clothing_ids = [];
    $categories = ['top', 'bottom', 'outerwear', 'shoes', 'accessories', 'bag'];
    
    foreach ($categories as $category) {
        if (isset($recommendation_result[$category]) && 
            isset($recommendation_result[$category]['id']) && 
            is_numeric($recommendation_result[$category]['id'])) {
            $clothing_ids[] = $recommendation_result[$category]['id'];
            logDebug("找到衣物ID", ['category' => $category, 'id' => $recommendation_result[$category]['id']]);
        } else {
            if (isset($recommendation_result[$category])) {
                logDebug("无法从该类别获取有效ID", [
                    'category' => $category, 
                    'has_id_property' => isset($recommendation_result[$category]['id']),
                    'id_value' => isset($recommendation_result[$category]['id']) ? $recommendation_result[$category]['id'] : 'undefined',
                    'is_numeric' => isset($recommendation_result[$category]['id']) ? is_numeric($recommendation_result[$category]['id']) : false
                ]);
            } else {
                logDebug("推荐结果中缺少类别", ['missing_category' => $category]);
            }
        }
    }
    
    logDebug("收集到的衣物ID", ['ids' => $clothing_ids, 'count' => count($clothing_ids)]);
    
    // 创建ID到图片URL的映射
    $id_to_image_url = [];
    
    if (!empty($clothing_ids)) {
        // 准备IN查询的占位符
        $placeholders = implode(',', array_fill(0, count($clothing_ids), '?'));
        
        // 查询所有相关衣物的信息
        $query = "SELECT id, image_url, category FROM clothes WHERE id IN ($placeholders) AND user_id = ?";
        logDebug("准备执行数据库查询", ['query' => $query, 'param_count' => count($clothing_ids) + 1]);
        
        $stmt = $pdo->prepare($query);
        
        // 绑定所有参数
        $params = array_merge($clothing_ids, [$user_id]);
        $stmt->execute($params);
        
        // 获取所有查询结果
        $clothes_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        logDebug("数据库查询结果", ['found_items' => count($clothes_data), 'items' => $clothes_data]);
        
        // 创建ID到图片URL的映射
        foreach ($clothes_data as $clothing) {
            $id_to_image_url[$clothing['id']] = $clothing['image_url'];
            logDebug("映射衣物ID到图片URL", ['id' => $clothing['id'], 'image_url' => $clothing['image_url'], 'category' => $clothing['category']]);
        }
    }
    
    // 更新推荐结果中的image_url
    foreach ($categories as $category) {
        if (isset($recommendation_result[$category])) {
            // 确保每个类别都有image_url字段
            if (!isset($recommendation_result[$category]['image_url']) || 
                $recommendation_result[$category]['image_url'] === null || 
                $recommendation_result[$category]['image_url'] === 'null') {
                
                // 尝试从ID映射中获取图片URL
                if (isset($recommendation_result[$category]['id']) && 
                    isset($id_to_image_url[$recommendation_result[$category]['id']])) {
                    
                    $clothing_id = $recommendation_result[$category]['id'];
                    $recommendation_result[$category]['image_url'] = $id_to_image_url[$clothing_id];
                    logDebug("已更新图片URL", [
                        'category' => $category,
                        'id' => $clothing_id,
                        'new_image_url' => $id_to_image_url[$clothing_id]
                    ]);
                } else {
                    // 使用默认图片URL
                    if (isset($default_image_urls[$category])) {
                        $recommendation_result[$category]['image_url'] = $default_image_urls[$category];
                        logDebug("使用默认图片URL", [
                            'category' => $category,
                            'default_url' => $default_image_urls[$category]
                        ]);
                    } else {
                        // 如果没有该类别的默认图片，使用通用默认图片
                        $recommendation_result[$category]['image_url'] = 'https://images.alidog.cn/default/general_default.jpg';
                        logDebug("使用通用默认图片URL", [
                            'category' => $category,
                            'default_url' => 'https://images.alidog.cn/default/general_default.jpg'
                        ]);
                    }
                }
            } else {
                logDebug("保留现有图片URL", [
                    'category' => $category,
                    'existing_url' => $recommendation_result[$category]['image_url']
                ]);
            }
            
            // 记录当前类别的图片URL状态
            logDebug("类别图片URL最终状态", [
                'category' => $category,
                'id' => isset($recommendation_result[$category]['id']) ? $recommendation_result[$category]['id'] : 'undefined',
                'image_url' => isset($recommendation_result[$category]['image_url']) ? $recommendation_result[$category]['image_url'] : null
            ]);
        }
    }
    
    // 记录修复后的结构
    logDebug("修复后的推荐结果", ['top_keys' => array_keys($recommendation_result)]);
    
    return $recommendation_result;
}

/**
 * 确保weather_based_recommendations表存在
 */
function ensureWeatherRecommendationTableExists($conn) {
    try {
        // 检查表是否存在
        $stmt = $conn->query("SHOW TABLES LIKE 'weather_based_recommendations'");
        if ($stmt->rowCount() == 0) {
            // 表不存在，创建表
            $conn->exec("CREATE TABLE weather_based_recommendations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                weather_key VARCHAR(100) NOT NULL COMMENT '天气键(城市_天气状况_温度)',
                weather_data TEXT NOT NULL COMMENT '天气数据JSON',
                recommendation_data TEXT NOT NULL COMMENT '推荐数据JSON',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_weather_key (user_id, weather_key),
                INDEX idx_created_at (created_at),
                CONSTRAINT fk_weather_rec_user FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='基于天气的穿搭推荐记录'");
            logDebug("创建weather_based_recommendations表成功");
        }
    } catch (PDOException $e) {
        logDebug("检查或创建weather_based_recommendations表失败", ['error' => $e->getMessage()]);
        throw $e;
    }
}
?> 