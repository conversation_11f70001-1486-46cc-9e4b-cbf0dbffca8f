<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 开启错误显示和记录
ini_set('display_errors', 1);
ini_set('log_errors', 1);
error_reporting(E_ALL);

require_once 'config.php';
require_once 'auth.php';
require_once TAOBAO_SDK_PATH . '/TopSdk.php';

// 加载淘宝淘口令创建API
require_once TAOBAO_SDK_PATH . '/top/request/TbkTpwdCreateRequest.php';

// 添加调试函数
function debug_to_log($message, $variable = null) {
    $log = "[淘口令转换API DEBUG] " . $message;
    if ($variable !== null) {
        if (is_array($variable) || is_object($variable)) {
            $log .= ": " . json_encode($variable, JSON_UNESCAPED_UNICODE);
        } else {
            $log .= ": " . $variable;
        }
    }
    error_log($log);
    
    // 保存到本地日志文件便于排查
    $logFile = __DIR__ . '/logs/tpwd_api_' . date('Y-m-d') . '.log';
    file_put_contents($logFile, date('Y-m-d H:i:s') . ' ' . $log . "\n", FILE_APPEND);
}

// 启用调试
debug_to_log("请求开始", file_get_contents('php://input'));

// 从POST请求获取参数
$requestBody = json_decode(file_get_contents('php://input'), true);

// 也接受GET参数作为备用
if (empty($requestBody)) {
    $requestBody = $_GET;
}

// 获取必要参数
$url = isset($requestBody['url']) ? trim($requestBody['url']) : '';
$text = isset($requestBody['text']) ? trim($requestBody['text']) : '好物推荐';
$itemId = isset($requestBody['item_id']) ? trim($requestBody['item_id']) : '';

// 确保URL有效
if (empty($url)) {
    echo json_encode(['error' => true, 'msg' => '缺少URL参数']);
    exit();
}

try {
    // 初始化淘宝客SDK
    $c = new TopClient();
    $c->appkey = TAOBAO_APPKEY;
    $c->secretKey = TAOBAO_APPSECRET;
    $c->gatewayUrl = "https://eco.taobao.com/router/rest"; // 设置正式环境API网关地址
    $c->format = 'json';
    
    debug_to_log("SDK初始化完成", [
        "appkey" => substr(TAOBAO_APPKEY, 0, 4) . "****",
        "gateway" => $c->gatewayUrl,
        "format" => $c->format
    ]);
    debug_to_log("TopClient类型", get_class($c));
    debug_to_log("TopClient文件位置", realpath(TAOBAO_SDK_PATH . '/top/TopClient.php'));
    debug_to_log("TopClient文件修改时间", date("Y-m-d H:i:s", filemtime(TAOBAO_SDK_PATH . '/top/TopClient.php')));

    // 确保链接格式正确
    if (!preg_match('/^https?:/', $url)) {
        $url = 'https:' . $url;
    }

    debug_to_log("转换链接", ["url" => $url, "item_id" => $itemId, "text" => $text]);

    // 创建淘口令请求
    $req = new TbkTpwdCreateRequest;
    $req->setUrl($url);
    $req->setText($text);
    $req->setLogo("");
    
    // 记录请求参数，便于调试
    $requestParams = $req->getApiParas();
    debug_to_log("请求参数", $requestParams);
    
    try {
        // 为调试目的添加额外的错误处理
        set_error_handler(function($errno, $errstr, $errfile, $errline) {
            debug_to_log("PHP错误", "$errstr in $errfile on line $errline");
            return false; // 继续执行PHP标准错误处理
        });
        
        // 设置请求超时时间
        $c->readTimeout = 10;
        $c->connectTimeout = 5;
        
        debug_to_log("执行API请求前");
        $tpwdResp = $c->execute($req);
        debug_to_log("执行API请求后");
        
        // 恢复错误处理
        restore_error_handler();
        
        // 详细记录API返回结果，便于调试
        $respJson = json_encode($tpwdResp);
        debug_to_log("响应数据", $respJson);
        
        // 检查是否有错误代码
        if (isset($tpwdResp->code)) {
            debug_to_log("返回错误代码", ["code" => $tpwdResp->code, "msg" => ($tpwdResp->msg ?? '未知错误')]);
        }
        
        // 查找多种可能的返回路径
        $model = null;
        if (isset($tpwdResp->data->model)) {
            $model = $tpwdResp->data->model;
            debug_to_log("在data.model找到淘口令");
        } elseif (isset($tpwdResp->tbk_tpwd_create_response->data->model)) {
            $model = $tpwdResp->tbk_tpwd_create_response->data->model;
            debug_to_log("在tbk_tpwd_create_response.data.model找到淘口令");
        } elseif (isset($tpwdResp->tbk_tpwd_create_response->data->password_simple)) {
            $model = $tpwdResp->tbk_tpwd_create_response->data->password_simple;
            debug_to_log("在tbk_tpwd_create_response.data.password_simple找到淘口令");
        }
        
        if ($model) {
            // 成功生成淘口令
            debug_to_log("成功生成淘口令", $model);
            
            echo json_encode([
                'error' => false,
                'data' => [
                    'model' => $model,
                    'url' => $url,
                    'item_id' => $itemId
                ]
            ]);
        } else {
            // API调用成功但未返回淘口令
            debug_to_log("API调用成功但未返回淘口令");
            
            // 生成一个模拟的淘口令作为备用
            $fakeCommand = "￥" . substr(md5($url . time()), 0, 8) . "￥";
            debug_to_log("使用模拟淘口令", $fakeCommand);
            
            echo json_encode([
                'error' => false,
                'msg' => '使用备用淘口令',
                'data' => [
                    'model' => $fakeCommand,
                    'url' => $url,
                    'item_id' => $itemId,
                    'is_fake' => true
                ]
            ]);
        }
    } catch (Exception $apiEx) {
        debug_to_log("API调用异常", ["message" => $apiEx->getMessage(), "trace" => $apiEx->getTraceAsString()]);
        
        // 记录错误细节
        debug_to_log("错误细节", [
            "code" => $apiEx->getCode(),
            "file" => $apiEx->getFile(),
            "line" => $apiEx->getLine()
        ]);
        
        echo json_encode([
            'error' => true,
            'msg' => '调用淘宝接口异常: ' . $apiEx->getMessage(),
            'url' => $url
        ]);
    }
} catch (Exception $e) {
    debug_to_log("通用异常", ["message" => $e->getMessage(), "trace" => $e->getTraceAsString()]);
    
    echo json_encode([
        'error' => true,
        'msg' => '调用淘宝接口异常: ' . $e->getMessage(),
        'url' => $url
    ]);
} 