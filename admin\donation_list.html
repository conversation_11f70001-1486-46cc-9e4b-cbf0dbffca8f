<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>次元衣柜 - 打赏记录管理</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .data-table th, .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .data-table th {
            background-color: #fafafa;
            font-weight: 500;
        }
        
        .data-table tbody tr:hover {
            background-color: #f5f5f5;
        }
        
        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .pagination-item {
            margin: 0 5px;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            cursor: pointer;
        }
        
        .pagination-item:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .pagination-item.active {
            background-color: #1890ff;
            border-color: #1890ff;
            color: white;
        }
        
        /* 统计卡片样式 */
        .summary-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .summary-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            flex: 1;
            min-width: 200px;
            text-align: center;
        }
        
        .summary-card .number {
            font-size: 24px;
            font-weight: 600;
            margin: 10px 0;
            color: #1890ff;
        }
        
        .summary-card .title {
            font-size: 14px;
            color: #666;
        }
        
        /* 搜索表单 */
        .search-form {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            background-color: #fafafa;
            padding: 15px;
            border-radius: 4px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            flex: 1;
            min-width: 200px;
        }
        
        .form-group label {
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .form-control {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        
        .btn-search {
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0 20px;
            cursor: pointer;
            align-self: flex-end;
        }
        
        .btn-search:hover {
            background-color: #40a9ff;
        }
        
        /* 金额列样式 */
        .amount {
            font-weight: bold;
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>打赏记录管理</h2>
                <div class="user-info">
                    <span id="userName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div id="donationLoadingIndicator" class="loading-indicator">加载中...</div>
            <div id="donationErrorMessage" class="error-message"></div>
            
            <!-- 统计卡片 -->
            <div class="summary-cards">
                <div class="summary-card">
                    <div class="title">总打赏次数</div>
                    <div class="number" id="totalDonations">0</div>
                </div>
                <div class="summary-card">
                    <div class="title">打赏总金额</div>
                    <div class="number" id="totalAmount">¥0.00</div>
                </div>
                <div class="summary-card">
                    <div class="title">今日打赏</div>
                    <div class="number" id="todayDonations">0</div>
                </div>
                <div class="summary-card">
                    <div class="title">今日金额</div>
                    <div class="number" id="todayAmount">¥0.00</div>
                </div>
            </div>
            
            <!-- 搜索表单 -->
            <div class="search-form">
                <div class="form-group">
                    <label for="searchUser">用户昵称/ID</label>
                    <input type="text" id="searchUser" class="form-control" placeholder="输入用户昵称或ID">
                </div>
                <div class="form-group">
                    <label for="searchDateStart">开始日期</label>
                    <input type="date" id="searchDateStart" class="form-control">
                </div>
                <div class="form-group">
                    <label for="searchDateEnd">结束日期</label>
                    <input type="date" id="searchDateEnd" class="form-control">
                </div>
                <button id="btnSearch" class="btn-search">搜索</button>
            </div>
            
            <!-- 数据表格 -->
            <table class="data-table" id="donationTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户信息</th>
                        <th>打赏金额</th>
                        <th>订单号</th>
                        <th>微信交易号</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>支付时间</th>
                    </tr>
                </thead>
                <tbody id="donationTableBody">
                    <!-- 数据将通过JavaScript加载 -->
                </tbody>
            </table>
            
            <!-- 分页器 -->
            <div class="pagination" id="donationPagination">
                <!-- 分页将通过JavaScript生成 -->
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/donation_list.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为donation
            Sidebar.init('donation');
            
            // 获取DOM元素
            const userName = document.getElementById('userName');
            const logoutBtn = document.getElementById('logoutBtn');
            
            // 显示用户信息
            const user = Auth.getCurrentUser();
            if (user) {
                userName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            logoutBtn.addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化打赏列表
            if (typeof DonationList !== 'undefined') {
                DonationList.init();
            }
        });
    </script>
</body>
</html> 