const app = getApp();

Page({
  data: {
    merchantId: null,
    merchant: null,
    wardrobes: [],
    currentWardrobe: null,
    categories: [],
    clothes: [],
    loading: true,
    currentPage: 1,
    totalPages: 1,
    pageSize: 20,
    keyword: '',
    selectedCategoryId: null,
    isShareCredits: false,
    showFilter: false,
    allCategories: [],
    hasMoreData: true,
    loadingMore: false,
    demoMode: false
  },

  onLoad: function(options) {
    const merchantId = options.merchant_id;
    if (!merchantId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    this.setData({
      merchantId: parseInt(merchantId)
    });

    // 检查是否有登录状态
    wx.getStorage({
      key: 'token',
      success: (res) => {
        // 已登录，使用真实token
        this.loadMerchantInfo();
        this.loadWardrobes();
      },
      fail: () => {
        console.log('未找到token，使用体验模式');
        // 未登录，设置为体验模式
        this.setData({
          demoMode: true
        });
        this.loadMerchantInfoDemo();
        this.loadWardrobesDemo();
      }
    });
  },

  // 加载商家信息
  loadMerchantInfo: function() {
    wx.getStorage({
      key: 'token',
      success: (res) => {
        wx.request({
          url: app.globalData.apiBaseUrl + '/get_merchant_info.php',
          method: 'POST',
          data: {
            token: res.data,
            merchant_id: this.data.merchantId
          },
          success: (response) => {
            if (response.data.code === 0) {
              this.setData({
                merchant: response.data.data,
                isShareCredits: response.data.data.share_try_on_credits === 1
              });
              wx.setNavigationBarTitle({
                title: response.data.data.nickname || '商家衣橱'
              });
            } else {
              wx.showToast({
                title: response.data.message || '获取商家信息失败',
                icon: 'none'
              });
            }
          },
          fail: () => {
            wx.showToast({
              title: '网络错误，请稍后再试',
              icon: 'none'
            });
          }
        });
      }
    });
  },

  // 体验模式下加载商家信息
  loadMerchantInfoDemo: function() {
    wx.request({
      url: app.globalData.apiBaseUrl + '/get_merchant_info.php',
      method: 'POST',
      data: {
        demo_mode: true,
        merchant_id: this.data.merchantId
      },
      success: (response) => {
        if (response.data.code === 0) {
          this.setData({
            merchant: response.data.data,
            isShareCredits: response.data.data.share_try_on_credits === 1
          });
          wx.setNavigationBarTitle({
            title: response.data.data.nickname || '商家衣橱'
          });
        } else {
          wx.showToast({
            title: response.data.message || '获取商家信息失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      }
    });
  },

  // 加载商家衣橱
  loadWardrobes: function() {
    wx.getStorage({
      key: 'token',
      success: (res) => {
        wx.request({
          url: app.globalData.apiBaseUrl + '/get_merchant_wardrobes.php',
          method: 'POST',
          data: {
            token: res.data,
            merchant_id: this.data.merchantId
          },
          success: (response) => {
            if (response.data.code === 0) {
              const wardrobes = response.data.data;
              this.setData({
                wardrobes: wardrobes,
                loading: false
              });
              
              // 如果有衣橱，默认选中第一个
              if (wardrobes && wardrobes.length > 0) {
                this.selectWardrobe(wardrobes[0].id);
              }
            } else {
              this.setData({
                loading: false
              });
              wx.showToast({
                title: response.data.message || '获取衣橱列表失败',
                icon: 'none'
              });
            }
          },
          fail: () => {
            this.setData({
              loading: false
            });
            wx.showToast({
              title: '网络错误，请稍后再试',
              icon: 'none'
            });
          }
        });
      },
      fail: () => {
        this.setData({
          loading: false
        });
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
      }
    });
  },

  // 体验模式下加载商家衣橱
  loadWardrobesDemo: function() {
    wx.request({
      url: app.globalData.apiBaseUrl + '/get_merchant_wardrobes.php',
      method: 'POST',
      data: {
        demo_mode: true,
        merchant_id: this.data.merchantId
      },
      success: (response) => {
        if (response.data.code === 0) {
          const wardrobes = response.data.data;
          this.setData({
            wardrobes: wardrobes,
            loading: false
          });
          
          // 如果有衣橱，默认选中第一个
          if (wardrobes && wardrobes.length > 0) {
            this.selectWardrobeDemo(wardrobes[0].id);
          }
        } else {
          this.setData({
            loading: false
          });
          wx.showToast({
            title: response.data.message || '获取衣橱列表失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        this.setData({
          loading: false
        });
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      }
    });
  },

  // 选择衣橱
  selectWardrobe: function(wardrobeId) {
    const id = typeof wardrobeId === 'object' ? 
      wardrobeId.currentTarget.dataset.id : wardrobeId;
    
    const wardrobe = this.data.wardrobes.find(item => item.id === id);
    if (wardrobe) {
      this.setData({
        currentWardrobe: wardrobe,
        selectedCategoryId: null
      });
      
      // 先加载分类，在分类加载完成的回调中再加载衣物
      this.loadCategories(id);
    }
  },

  // 体验模式下选择衣橱
  selectWardrobeDemo: function(wardrobeId) {
    const id = typeof wardrobeId === 'object' ? 
      wardrobeId.currentTarget.dataset.id : wardrobeId;
    
    const wardrobe = this.data.wardrobes.find(item => item.id === id);
    if (wardrobe) {
      this.setData({
        currentWardrobe: wardrobe,
        selectedCategoryId: null
      });
      
      // 先加载分类，在分类加载完成的回调中再加载衣物
      this.loadCategoriesDemo(id);
    }
  },

  // 加载衣物分类
  loadCategories: function(wardrobeId) {
    wx.getStorage({
      key: 'token',
      success: (res) => {
        wx.request({
          url: app.globalData.apiBaseUrl + '/get_merchant_categories.php',
          method: 'POST',
          data: {
            token: res.data,
            merchant_id: this.data.merchantId,
            wardrobe_id: wardrobeId
          },
          success: (response) => {
            console.log("获取分类返回数据:", response.data);
            if (response.data.code === 0) {
              // 过滤掉系统分类，只保留商户自定义的分类
              let categories = response.data.data.filter(category => !category.is_system);
              
              // 保存所有分类信息，包括系统分类(用于后续过滤衣物)
              let allCategories = response.data.data.map(category => {
                // 确保is_system是布尔值
                return {
                  ...category,
                  is_system: Boolean(category.is_system)
                };
              });

              // 确保分类有clothes_count字段
              categories = categories.map(category => {
                if (typeof category.clothes_count === 'undefined') {
                  category.clothes_count = 0;
                }
                return category;
              });
              
              this.setData({
                categories: categories,
                allCategories: allCategories
              });
              console.log("设置分类数据:", categories);
              console.log("所有分类数据(含系统分类):", allCategories);
              
              // 输出系统分类的ID以便调试
              const systemCategoryIds = allCategories
                .filter(category => category.is_system === true)
                .map(category => parseInt(category.id));
              console.log("系统分类ID列表:", systemCategoryIds);
              
              // 在分类数据加载完成后，再加载衣物
              this.loadClothes(wardrobeId);
            } else {
              wx.showToast({
                title: response.data.message || '获取分类失败',
                icon: 'none'
              });
            }
          },
          fail: () => {
            wx.showToast({
              title: '获取分类失败，请稍后再试',
              icon: 'none'
            });
          }
        });
      }
    });
  },

  // 体验模式下加载衣物分类
  loadCategoriesDemo: function(wardrobeId) {
    wx.request({
      url: app.globalData.apiBaseUrl + '/get_merchant_categories.php',
      method: 'POST',
      data: {
        demo_mode: true,
        merchant_id: this.data.merchantId,
        wardrobe_id: wardrobeId
      },
      success: (response) => {
        console.log("获取分类返回数据:", response.data);
        if (response.data.code === 0) {
          // 过滤掉系统分类，只保留商户自定义的分类
          let categories = response.data.data.filter(category => !category.is_system);
          
          // 保存所有分类信息，包括系统分类(用于后续过滤衣物)
          let allCategories = response.data.data.map(category => {
            // 确保is_system是布尔值
            return {
              ...category,
              is_system: Boolean(category.is_system)
            };
          });

          // 确保分类有clothes_count字段
          categories = categories.map(category => {
            if (typeof category.clothes_count === 'undefined') {
              category.clothes_count = 0;
            }
            return category;
          });
          
          this.setData({
            categories: categories,
            allCategories: allCategories
          });
          console.log("设置分类数据:", categories);
          console.log("所有分类数据(含系统分类):", allCategories);
          
          // 输出系统分类的ID以便调试
          const systemCategoryIds = allCategories
            .filter(category => category.is_system === true)
            .map(category => parseInt(category.id));
          console.log("系统分类ID列表:", systemCategoryIds);
          
          // 在分类数据加载完成后，再加载衣物
          this.loadClothesDemo(wardrobeId);
        } else {
          wx.showToast({
            title: response.data.message || '获取分类失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '获取分类失败，请稍后再试',
          icon: 'none'
        });
      }
    });
  },

  // 加载衣物
  loadClothes: function(wardrobeId, page = 1, categoryId = null) {
    this.setData({
      loading: true,
      hasMoreData: true
    });
    
    console.log('加载衣物请求参数:', {
      wardrobe_id: wardrobeId,
      page: page,
      category_id: categoryId,
      type_of_category_id: typeof categoryId
    });
    
    wx.getStorage({
      key: 'token',
      success: (res) => {
        const requestData = {
          token: res.data,
          merchant_id: this.data.merchantId,
          wardrobe_id: wardrobeId,
          keyword: this.data.keyword,
          page: page,
          limit: this.data.pageSize
        };
        
        // 如果是选择特定分类，则直接按分类筛选
        if (categoryId !== null) {
          requestData.category_id = categoryId;
        }
        
        console.log('发送加载衣物请求:', requestData);
        
        wx.request({
          url: app.globalData.apiBaseUrl + '/get_merchant_clothes.php',
          method: 'POST',
          data: requestData,
          success: (response) => {
            console.log('获取衣物响应:', response.data);
            
            if (response.data.code === 0) {
              let clothes = response.data.data.list;
              console.log('获取到的衣物数量:', clothes.length);
              
              // 如果是"全部"分类，则过滤掉系统分类下的衣物
              if (categoryId === null && this.data.allCategories && this.data.allCategories.length > 0) {
                // 获取系统分类的ID列表
                const systemCategoryIds = [];
                // 获取自定义分类前缀列表
                const customCategoryPrefixes = [];
                
                // 遍历所有分类，找出系统分类和自定义分类前缀
                this.data.allCategories.forEach(category => {
                  // 将is_system显式转换为数字后再判断
                  if (Number(category.is_system) === 1) {
                    systemCategoryIds.push(parseInt(category.id));
                  } else {
                    // 提取自定义分类前缀，例如 "custom_1748011847_3" 中的 "custom_"
                    const categoryCode = category.code || '';
                    if (categoryCode.startsWith('custom_')) {
                      const prefix = 'custom_';
                      if (!customCategoryPrefixes.includes(prefix)) {
                        customCategoryPrefixes.push(prefix);
                      }
                    }
                  }
                });
                
                console.log('系统分类ID列表:', systemCategoryIds);
                console.log('自定义分类前缀列表:', customCategoryPrefixes);
                
                // 如果找到了系统分类，则过滤
                if (systemCategoryIds.length > 0) {
                  clothes = clothes.filter(item => {
                    // 检查衣物是否属于自定义分类
                    const clothesCategory = item.category || '';
                    const belongsToCustomCategory = customCategoryPrefixes.some(prefix => 
                      clothesCategory && clothesCategory.startsWith(prefix)
                    );
                    
                    // 如果衣物属于自定义分类，则保留
                    if (belongsToCustomCategory) {
                      return true;
                    }
                    
                    // 检查衣物是否属于系统分类
                    const clothesCategoryId = item.category_id !== null && item.category_id !== undefined ? 
                      parseInt(item.category_id) : null;
                    
                    // 如果没有category_id或不是系统分类ID，则保留
                    return clothesCategoryId === null || !systemCategoryIds.includes(clothesCategoryId);
                  });
                  
                  console.log('过滤后的衣物数量:', clothes.length);
                }
              }
              
              // 修复分页问题：使用过滤后的实际衣物数量计算总页数
              const originalTotal = response.data.data.total;
              let adjustedTotal = originalTotal;
              
              if (page === 1 && categoryId === null && 
                  clothes.length < response.data.data.list.length) {
                const filteredOutCount = response.data.data.list.length - clothes.length;
                // 调整为按比例估算总数
                const filterRatio = filteredOutCount / response.data.data.list.length;
                const estimatedFilteredTotal = Math.max(0, Math.round(originalTotal * (1 - filterRatio)));
                adjustedTotal = estimatedFilteredTotal;
                console.log('估算调整后的总数:', adjustedTotal, '原始总数:', originalTotal, '过滤比率:', filterRatio);
              }
              
              // 如果调整后的总数为0但有衣物，至少设置为衣物数量
              if (adjustedTotal === 0 && clothes.length > 0) {
                adjustedTotal = clothes.length;
              }
              
              const maxPage = Math.max(1, Math.ceil(adjustedTotal / this.data.pageSize));
              const hasMore = page < maxPage;
              
              this.setData({
                clothes: clothes,
                currentPage: page,
                totalPages: maxPage,
                loading: false,
                hasMoreData: hasMore
              });
              console.log('设置衣物数据成功,数量:', clothes.length, '总页数:', maxPage, '还有更多数据:', hasMore);
            } else {
              this.setData({
                loading: false
              });
              wx.showToast({
                title: response.data.message || '获取衣物失败',
                icon: 'none'
              });
            }
          },
          fail: () => {
            this.setData({
              loading: false
            });
            wx.showToast({
              title: '网络错误，请稍后再试',
              icon: 'none'
            });
          }
        });
      },
      fail: () => {
        this.setData({
          loading: false
        });
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
      }
    });
  },

  // 体验模式下加载衣物
  loadClothesDemo: function(wardrobeId, page = 1, categoryId = null) {
    this.setData({
      loading: true,
      hasMoreData: true
    });
    
    console.log('加载衣物请求参数:', {
      wardrobe_id: wardrobeId,
      page: page,
      category_id: categoryId,
      type_of_category_id: typeof categoryId,
      demo_mode: true
    });
    
    const requestData = {
      demo_mode: true,
      merchant_id: this.data.merchantId,
      wardrobe_id: wardrobeId,
      keyword: this.data.keyword,
      page: page,
      limit: this.data.pageSize
    };
    
    // 如果是选择特定分类，则直接按分类筛选
    if (categoryId !== null) {
      requestData.category_id = categoryId;
    }
    
    console.log('发送加载衣物请求:', requestData);
    
    wx.request({
      url: app.globalData.apiBaseUrl + '/get_merchant_clothes.php',
      method: 'POST',
      data: requestData,
      success: (response) => {
        console.log('获取衣物响应:', response.data);
        
        if (response.data.code === 0) {
          let clothes = response.data.data.list;
          console.log('获取到的衣物数量:', clothes.length);
          
          // 如果是"全部"分类，则过滤掉系统分类下的衣物
          if (categoryId === null && this.data.allCategories && this.data.allCategories.length > 0) {
            // 获取系统分类的ID列表
            const systemCategoryIds = [];
            // 获取自定义分类前缀列表
            const customCategoryPrefixes = [];
            
            // 遍历所有分类，找出系统分类和自定义分类前缀
            this.data.allCategories.forEach(category => {
              // 将is_system显式转换为数字后再判断
              if (Number(category.is_system) === 1) {
                systemCategoryIds.push(parseInt(category.id));
              } else {
                // 提取自定义分类前缀，例如 "custom_1748011847_3" 中的 "custom_"
                const categoryCode = category.code || '';
                if (categoryCode.startsWith('custom_')) {
                  const prefix = 'custom_';
                  if (!customCategoryPrefixes.includes(prefix)) {
                    customCategoryPrefixes.push(prefix);
                  }
                }
              }
            });
            
            console.log('系统分类ID列表:', systemCategoryIds);
            console.log('自定义分类前缀列表:', customCategoryPrefixes);
            
            // 如果找到了系统分类，则过滤
            if (systemCategoryIds.length > 0) {
              clothes = clothes.filter(item => {
                // 检查衣物是否属于自定义分类
                const clothesCategory = item.category || '';
                const belongsToCustomCategory = customCategoryPrefixes.some(prefix => 
                  clothesCategory && clothesCategory.startsWith(prefix)
                );
                
                // 如果衣物属于自定义分类，则保留
                if (belongsToCustomCategory) {
                  return true;
                }
                
                // 检查衣物是否属于系统分类
                const clothesCategoryId = item.category_id !== null && item.category_id !== undefined ? 
                  parseInt(item.category_id) : null;
                
                // 如果没有category_id或不是系统分类ID，则保留
                return clothesCategoryId === null || !systemCategoryIds.includes(clothesCategoryId);
              });
              
              console.log('过滤后的衣物数量:', clothes.length);
            }
          }
          
          // 修复分页问题：使用过滤后的实际衣物数量计算总页数
          const originalTotal = response.data.data.total;
          let adjustedTotal = originalTotal;
          
          if (page === 1 && categoryId === null && 
              clothes.length < response.data.data.list.length) {
            const filteredOutCount = response.data.data.list.length - clothes.length;
            // 调整为按比例估算总数
            const filterRatio = filteredOutCount / response.data.data.list.length;
            const estimatedFilteredTotal = Math.max(0, Math.round(originalTotal * (1 - filterRatio)));
            adjustedTotal = estimatedFilteredTotal;
            console.log('估算调整后的总数:', adjustedTotal, '原始总数:', originalTotal, '过滤比率:', filterRatio);
          }
          
          // 如果调整后的总数为0但有衣物，至少设置为衣物数量
          if (adjustedTotal === 0 && clothes.length > 0) {
            adjustedTotal = clothes.length;
          }
          
          const maxPage = Math.max(1, Math.ceil(adjustedTotal / this.data.pageSize));
          const hasMore = page < maxPage;
          
          this.setData({
            clothes: clothes,
            currentPage: page,
            totalPages: maxPage,
            loading: false,
            hasMoreData: hasMore
          });
          console.log('设置衣物数据成功,数量:', clothes.length, '总页数:', maxPage, '还有更多数据:', hasMore);
        } else {
          this.setData({
            loading: false
          });
          wx.showToast({
            title: response.data.message || '获取衣物失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        this.setData({
          loading: false
        });
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      }
    });
  },

  // 选择分类
  selectCategory: function(e) {
    // 确保categoryId是整数类型
    const categoryId = parseInt(e.currentTarget.dataset.id);
    console.log('选择分类，ID:', categoryId, '类型:', typeof categoryId);
    
    this.setData({
      selectedCategoryId: categoryId,
      currentPage: 1
    });
    
    if (this.data.demoMode) {
      this.loadClothesDemo(this.data.currentWardrobe.id, 1, categoryId);
    } else {
      this.loadClothes(this.data.currentWardrobe.id, 1, categoryId);
    }
  },

  // 清除分类筛选
  clearCategory: function() {
    this.setData({
      selectedCategoryId: null,
      currentPage: 1
    });
    
    if (this.data.demoMode) {
      this.loadClothesDemo(this.data.currentWardrobe.id, 1);
    } else {
      this.loadClothes(this.data.currentWardrobe.id, 1);
    }
  },

  // 搜索衣物
  searchClothes: function() {
    if (this.data.demoMode) {
      this.loadClothesDemo(
        this.data.currentWardrobe.id, 
        1, 
        this.data.selectedCategoryId
      );
    } else {
      this.loadClothes(
        this.data.currentWardrobe.id, 
        1, 
        this.data.selectedCategoryId
      );
    }
  },

  // 输入关键词
  onKeywordInput: function(e) {
    this.setData({
      keyword: e.detail.value
    });
  },

  // 滚动到底部时触发加载更多
  onReachBottom: function() {
    console.log('触发滚动到底部事件，当前页:', this.data.currentPage, '总页数:', this.data.totalPages);
    if (this.data.hasMoreData && !this.data.loading && !this.data.loadingMore) {
      this.loadMoreClothes();
    }
  },

  // 加载更多衣物
  loadMoreClothes: function() {
    if (this.data.currentPage >= this.data.totalPages) {
      this.setData({
        hasMoreData: false
      });
      return;
    }

    const nextPage = this.data.currentPage + 1;
    
    this.setData({
      loadingMore: true
    });
    
    console.log('加载更多衣物，页码:', nextPage);
    
    if (this.data.demoMode) {
      this.loadClothesPageDemo(nextPage);
    } else {
      this.loadClothesPage(nextPage);
    }
  },

  // 加载指定页的衣物数据，用于体验模式下拉加载更多
  loadClothesPageDemo: function(page) {
    console.log('加载衣物页面，参数:', {
      wardrobe_id: this.data.currentWardrobe.id,
      page: page,
      category_id: this.data.selectedCategoryId,
      demo_mode: true
    });
    
    const requestData = {
      demo_mode: true,
      merchant_id: this.data.merchantId,
      wardrobe_id: this.data.currentWardrobe.id,
      keyword: this.data.keyword,
      page: page,
      limit: this.data.pageSize
    };
    
    if (this.data.selectedCategoryId !== null) {
      requestData.category_id = this.data.selectedCategoryId;
    }
    
    console.log('发送加载更多衣物请求:', requestData);
    
    wx.request({
      url: app.globalData.apiBaseUrl + '/get_merchant_clothes.php',
      method: 'POST',
      data: requestData,
      success: (response) => {
        console.log('加载更多响应:', response.data);
        
        if (response.data.code === 0) {
          let newClothes = response.data.data.list;
          
          // 过滤系统分类的衣物
          if (this.data.selectedCategoryId === null && this.data.allCategories && this.data.allCategories.length > 0) {
            const systemCategoryIds = [];
            const customCategoryPrefixes = [];
            
            this.data.allCategories.forEach(category => {
              if (Number(category.is_system) === 1) {
                systemCategoryIds.push(parseInt(category.id));
              } else {
                // 提取自定义分类前缀
                const categoryCode = category.code || '';
                if (categoryCode.startsWith('custom_')) {
                  const prefix = 'custom_';
                  if (!customCategoryPrefixes.includes(prefix)) {
                    customCategoryPrefixes.push(prefix);
                  }
                }
              }
            });
            
            if (systemCategoryIds.length > 0) {
              newClothes = newClothes.filter(item => {
                // 检查衣物是否属于自定义分类
                const clothesCategory = item.category || '';
                const belongsToCustomCategory = customCategoryPrefixes.some(prefix => 
                  clothesCategory && clothesCategory.startsWith(prefix)
                );
                
                // 如果衣物属于自定义分类，则保留
                if (belongsToCustomCategory) {
                  return true;
                }
                
                // 检查衣物是否属于系统分类
                const clothesCategoryId = item.category_id !== null && item.category_id !== undefined ? 
                  parseInt(item.category_id) : null;
                
                // 如果没有category_id或不是系统分类ID，则保留
                return clothesCategoryId === null || !systemCategoryIds.includes(clothesCategoryId);
              });
            }
            
            console.log('过滤后的新加载衣物数量:', newClothes.length);
          }
          
          // 如果加载到的新衣物为空，设置没有更多数据了
          if (newClothes.length === 0) {
            this.setData({
              hasMoreData: false,
              loadingMore: false
            });
            return;
          }
          
          // 将新衣物合并到现有衣物
          const updatedClothes = [...this.data.clothes, ...newClothes];
          
          this.setData({
            clothes: updatedClothes,
            currentPage: page,
            loadingMore: false
          });
          
          console.log('加载更多成功，当前衣物总数:', updatedClothes.length);
        } else {
          this.setData({
            loadingMore: false
          });
          wx.showToast({
            title: response.data.message || '加载更多失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        this.setData({
          loadingMore: false
        });
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      }
    });
  },

  // 切换筛选面板
  toggleFilter: function() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 进入衣物详情
  navigateToClothesDetail: function(e) {
    const clothesId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/clothing/detail/detail?id=${clothesId}&merchant_id=${this.data.merchantId}`
    });
  },
  
  // 试穿衣物
  tryOnClothing: function(e) {
    const clothesId = e.currentTarget.dataset.id;
    // 跳转到照片选择页面，并传递衣物ID和商家ID
    wx.navigateTo({
      url: `/pages/photos/select/index?select_mode=true&clothes_id=${clothesId}&merchant_id=${this.data.merchantId}`
    });
  },
  
  // 返回商家列表
  backToMerchants: function() {
    wx.navigateBack();
  },

  // 加载指定页的衣物数据，用于下拉加载更多
  loadClothesPage: function(page) {
    console.log('加载衣物页面，参数:', {
      wardrobe_id: this.data.currentWardrobe.id,
      page: page,
      category_id: this.data.selectedCategoryId
    });
    
    wx.getStorage({
      key: 'token',
      success: (res) => {
        const requestData = {
          token: res.data,
          merchant_id: this.data.merchantId,
          wardrobe_id: this.data.currentWardrobe.id,
          keyword: this.data.keyword,
          page: page,
          limit: this.data.pageSize
        };
        
        if (this.data.selectedCategoryId !== null) {
          requestData.category_id = this.data.selectedCategoryId;
        }
        
        console.log('发送加载更多衣物请求:', requestData);
        
        wx.request({
          url: app.globalData.apiBaseUrl + '/get_merchant_clothes.php',
          method: 'POST',
          data: requestData,
          success: (response) => {
            console.log('加载更多响应:', response.data);
            
            if (response.data.code === 0) {
              let newClothes = response.data.data.list;
              
              // 过滤系统分类的衣物
              if (this.data.selectedCategoryId === null && this.data.allCategories && this.data.allCategories.length > 0) {
                const systemCategoryIds = [];
                const customCategoryPrefixes = [];
                
                this.data.allCategories.forEach(category => {
                  if (Number(category.is_system) === 1) {
                    systemCategoryIds.push(parseInt(category.id));
                  } else {
                    // 提取自定义分类前缀
                    const categoryCode = category.code || '';
                    if (categoryCode.startsWith('custom_')) {
                      const prefix = 'custom_';
                      if (!customCategoryPrefixes.includes(prefix)) {
                        customCategoryPrefixes.push(prefix);
                      }
                    }
                  }
                });
                
                if (systemCategoryIds.length > 0) {
                  newClothes = newClothes.filter(item => {
                    // 检查衣物是否属于自定义分类
                    const clothesCategory = item.category || '';
                    const belongsToCustomCategory = customCategoryPrefixes.some(prefix => 
                      clothesCategory && clothesCategory.startsWith(prefix)
                    );
                    
                    // 如果衣物属于自定义分类，则保留
                    if (belongsToCustomCategory) {
                      return true;
                    }
                    
                    // 检查衣物是否属于系统分类
                    const clothesCategoryId = item.category_id !== null && item.category_id !== undefined ? 
                      parseInt(item.category_id) : null;
                    
                    // 如果没有category_id或不是系统分类ID，则保留
                    return clothesCategoryId === null || !systemCategoryIds.includes(clothesCategoryId);
                  });
                }
                
                console.log('过滤后的新加载衣物数量:', newClothes.length);
              }
              
              // 如果加载到的新衣物为空，设置没有更多数据了
              if (newClothes.length === 0) {
                this.setData({
                  hasMoreData: false,
                  loadingMore: false
                });
                return;
              }
              
              // 将新衣物合并到现有衣物
              const updatedClothes = [...this.data.clothes, ...newClothes];
              
              this.setData({
                clothes: updatedClothes,
                currentPage: page,
                loadingMore: false
              });
              
              console.log('加载更多成功，当前衣物总数:', updatedClothes.length);
            } else {
              this.setData({
                loadingMore: false
              });
              wx.showToast({
                title: response.data.message || '加载更多失败',
                icon: 'none'
              });
            }
          },
          fail: () => {
            this.setData({
              loadingMore: false
            });
            wx.showToast({
              title: '网络错误，请稍后再试',
              icon: 'none'
            });
          }
        });
      },
      fail: () => {
        this.setData({
          loadingMore: false
        });
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      }
    });
  }
}); 