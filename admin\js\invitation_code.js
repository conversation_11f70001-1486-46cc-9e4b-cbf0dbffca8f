/**
 * 邀请码管理模块
 */
const InvitationCode = {
    // API基础URL
    apiBaseUrl: '../login_backend',
    
    // 分页参数
    pagination: {
        page: 1,
        pageSize: 10,
        total: 0
    },
    
    // 筛选参数
    filters: {
        status: '',
        code: ''
    },
    
    /**
     * 初始化邀请码管理
     */
    init: function() {
        // 绑定事件
        this.bindEvents();
        
        // 加载邀请码列表
        this.loadInvitationCodes();
    },
    
    /**
     * 绑定事件处理
     */
    bindEvents: function() {
        // 生成邀请码按钮
        const generateCodeBtn = document.getElementById('generateCodeBtn');
        if (generateCodeBtn) {
            generateCodeBtn.addEventListener('click', () => this.showGenerateModal());
        }
        
        // 筛选按钮
        const filterBtn = document.getElementById('filterBtn');
        if (filterBtn) {
            filterBtn.addEventListener('click', () => {
                this.pagination.page = 1;
                this.loadInvitationCodes();
            });
        }
        
        // 生成模态框关闭按钮
        const closeGenerateModal = document.getElementById('closeGenerateModal');
        const cancelGenerateBtn = document.getElementById('cancelGenerateBtn');
        if (closeGenerateModal) {
            closeGenerateModal.addEventListener('click', () => this.hideGenerateModal());
        }
        if (cancelGenerateBtn) {
            cancelGenerateBtn.addEventListener('click', () => this.hideGenerateModal());
        }
        
        // 确认生成按钮
        const confirmGenerateBtn = document.getElementById('confirmGenerateBtn');
        if (confirmGenerateBtn) {
            confirmGenerateBtn.addEventListener('click', () => this.generateInvitationCodes());
        }
        
        // 详情模态框关闭按钮
        const closeViewCodeModal = document.getElementById('closeViewCodeModal');
        const closeDetailBtn = document.getElementById('closeDetailBtn');
        if (closeViewCodeModal) {
            closeViewCodeModal.addEventListener('click', () => this.hideViewCodeModal());
        }
        if (closeDetailBtn) {
            closeDetailBtn.addEventListener('click', () => this.hideViewCodeModal());
        }
    },
    
    /**
     * 加载邀请码列表
     */
    loadInvitationCodes: function() {
        const loadingIndicator = document.getElementById('loadingIndicator');
        const errorMessage = document.getElementById('errorMessage');
        
        if (loadingIndicator) {
            loadingIndicator.style.display = 'block';
        }
        if (errorMessage) {
            errorMessage.style.display = 'none';
        }
        
        // 获取筛选条件
        const statusFilter = document.getElementById('statusFilter');
        const codeFilter = document.getElementById('codeFilter');
        
        if (statusFilter) {
            this.filters.status = statusFilter.value;
        }
        if (codeFilter) {
            this.filters.code = codeFilter.value;
        }
        
        // 构建URL和参数
        const url = new URL(`${this.apiBaseUrl}/get_invitation_codes.php`, window.location.origin);
        
        // 添加分页和筛选参数
        url.searchParams.append('page', this.pagination.page);
        url.searchParams.append('page_size', this.pagination.pageSize);
        
        if (this.filters.status) {
            url.searchParams.append('status', this.filters.status);
        }
        
        if (this.filters.code) {
            url.searchParams.append('code', this.filters.code);
        }
        
        // 发起请求
        fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('获取邀请码列表失败');
            }
            return response.json();
        })
        .then(data => {
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
            
            if (data.error) {
                if (errorMessage) {
                    errorMessage.textContent = data.msg || '获取数据失败';
                    errorMessage.style.display = 'block';
                }
                return;
            }
            
            // 更新分页信息
            this.pagination.total = data.total || 0;
            
            // 渲染邀请码列表
            this.renderInvitationCodes(data.data || []);
            
            // 渲染分页控件
            this.renderPagination();
        })
        .catch(error => {
            console.error('加载邀请码列表失败:', error);
            
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
            
            if (errorMessage) {
                errorMessage.textContent = error.message || '加载数据失败，请检查网络连接';
                errorMessage.style.display = 'block';
            }
        });
    },
    
    /**
     * 渲染邀请码列表
     * @param {Array} codes 邀请码数据列表
     */
    renderInvitationCodes: function(codes) {
        const tableBody = document.getElementById('invitationCodeList');
        
        if (!tableBody) {
            return;
        }
        
        // 清空现有内容
        tableBody.innerHTML = '';
        
        if (!codes.length) {
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = '<td colspan="10" style="text-align: center;">暂无邀请码数据</td>';
            tableBody.appendChild(emptyRow);
            return;
        }
        
        // 状态文本映射
        const statusMap = {
            'unused': '未使用',
            'used': '已使用',
            'expired': '已过期'
        };
        
        // 类型文本映射
        const typeMap = {
            'image_analysis': '形象分析'
        };
        
        // 渲染每一行数据
        codes.forEach(code => {
            const row = document.createElement('tr');
            
            // 格式化日期
            const createdAt = code.created_at ? new Date(code.created_at).toLocaleString() : '-';
            const expiredAt = code.expired_at ? new Date(code.expired_at).toLocaleString() : '-';
            const usedAt = code.used_at ? new Date(code.used_at).toLocaleString() : '-';
            
            // 获取状态文本和样式类名
            const statusText = statusMap[code.status] || code.status;
            const statusClass = `status-${code.status}`;
            
            // 获取类型文本
            const typeText = typeMap[code.type] || code.type;
            
            // 设置行内容
            row.innerHTML = `
                <td>${code.id}</td>
                <td>${code.code}</td>
                <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                <td>${typeText}</td>
                <td>${createdAt}</td>
                <td>${expiredAt}</td>
                <td>${usedAt}</td>
                <td>${code.creator_name || '-'}</td>
                <td>${code.user_name || '-'}</td>
                <td>
                    <button class="action-btn view-btn" data-id="${code.id}">查看</button>
                    ${code.status === 'unused' ? `<button class="action-btn delete-btn" data-id="${code.id}">删除</button>` : ''}
                </td>
            `;
            
            // 绑定操作按钮事件
            const viewBtn = row.querySelector('.view-btn');
            if (viewBtn) {
                viewBtn.addEventListener('click', () => this.viewCodeDetail(code.id));
            }
            
            const deleteBtn = row.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', () => this.deleteInvitationCode(code.id));
            }
            
            tableBody.appendChild(row);
        });
    },
    
    /**
     * 渲染分页控件
     */
    renderPagination: function() {
        const paginationContainer = document.getElementById('pagination');
        
        if (!paginationContainer) {
            return;
        }
        
        // 清空现有内容
        paginationContainer.innerHTML = '';
        
        // 计算总页数
        const totalPages = Math.ceil(this.pagination.total / this.pagination.pageSize) || 1;
        
        // 上一页按钮
        const prevBtn = document.createElement('div');
        prevBtn.className = `pagination-item ${this.pagination.page <= 1 ? 'disabled' : ''}`;
        prevBtn.textContent = '上一页';
        if (this.pagination.page > 1) {
            prevBtn.addEventListener('click', () => {
                this.pagination.page--;
                this.loadInvitationCodes();
            });
        }
        paginationContainer.appendChild(prevBtn);
        
        // 页码按钮
        const maxPageButtons = 5;
        let startPage = Math.max(1, this.pagination.page - Math.floor(maxPageButtons / 2));
        const endPage = Math.min(totalPages, startPage + maxPageButtons - 1);
        
        if (endPage - startPage + 1 < maxPageButtons) {
            startPage = Math.max(1, endPage - maxPageButtons + 1);
        }
        
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('div');
            pageBtn.className = `pagination-item ${i === this.pagination.page ? 'active' : ''}`;
            pageBtn.textContent = i;
            
            if (i !== this.pagination.page) {
                pageBtn.addEventListener('click', () => {
                    this.pagination.page = i;
                    this.loadInvitationCodes();
                });
            }
            
            paginationContainer.appendChild(pageBtn);
        }
        
        // 下一页按钮
        const nextBtn = document.createElement('div');
        nextBtn.className = `pagination-item ${this.pagination.page >= totalPages ? 'disabled' : ''}`;
        nextBtn.textContent = '下一页';
        if (this.pagination.page < totalPages) {
            nextBtn.addEventListener('click', () => {
                this.pagination.page++;
                this.loadInvitationCodes();
            });
        }
        paginationContainer.appendChild(nextBtn);
    },
    
    /**
     * 显示生成邀请码模态框
     */
    showGenerateModal: function() {
        const modal = document.getElementById('generateModal');
        if (modal) {
            modal.classList.add('active');
        }
    },
    
    /**
     * 隐藏生成邀请码模态框
     */
    hideGenerateModal: function() {
        const modal = document.getElementById('generateModal');
        if (modal) {
            modal.classList.remove('active');
        }
    },
    
    /**
     * 显示邀请码详情模态框
     */
    showViewCodeModal: function() {
        const modal = document.getElementById('viewCodeModal');
        if (modal) {
            modal.classList.add('active');
        }
    },
    
    /**
     * 隐藏邀请码详情模态框
     */
    hideViewCodeModal: function() {
        const modal = document.getElementById('viewCodeModal');
        if (modal) {
            modal.classList.remove('active');
        }
    },
    
    /**
     * 生成邀请码
     */
    generateInvitationCodes: function() {
        const quantityInput = document.getElementById('codeQuantity');
        const expireDateInput = document.getElementById('expireDate');
        const codeTypeSelect = document.getElementById('codeType');
        
        const quantity = parseInt(quantityInput?.value) || 1;
        const expireDate = expireDateInput?.value || null;
        const codeType = codeTypeSelect?.value || 'image_analysis';
        
        // 检查数量是否有效
        if (quantity < 1 || quantity > 100) {
            alert('请输入有效的生成数量 (1-100)');
            return;
        }
        
        // 发起请求
        fetch(`${this.apiBaseUrl}/generate_invitation_codes.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${Auth.getToken()}`
            },
            body: JSON.stringify({
                quantity: quantity,
                expire_date: expireDate,
                type: codeType
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('生成邀请码失败');
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                alert(data.msg || '生成邀请码失败');
                return;
            }
            
            // 隐藏模态框
            this.hideGenerateModal();
            
            // 显示成功消息
            alert(`成功生成 ${data.data.generated || 0} 个邀请码`);
            
            // 重新加载列表
            this.loadInvitationCodes();
        })
        .catch(error => {
            console.error('生成邀请码失败:', error);
            alert(error.message || '生成邀请码失败，请检查网络连接');
        });
    },
    
    /**
     * 查看邀请码详情
     * @param {number} id 邀请码ID
     */
    viewCodeDetail: function(id) {
        // 显示加载指示器
        const codeDetails = document.getElementById('codeDetails');
        if (codeDetails) {
            codeDetails.innerHTML = '<p style="text-align: center;">正在加载...</p>';
        }
        
        // 显示模态框
        this.showViewCodeModal();
        
        // 发起请求
        fetch(`${this.apiBaseUrl}/get_invitation_code_detail.php?id=${id}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('获取邀请码详情失败');
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                if (codeDetails) {
                    codeDetails.innerHTML = `<p style="color: #f5222d;">${data.msg || '获取详情失败'}</p>`;
                }
                return;
            }
            
            // 渲染详情
            const code = data.data;
            if (!code) {
                if (codeDetails) {
                    codeDetails.innerHTML = '<p style="color: #f5222d;">未找到邀请码数据</p>';
                }
                return;
            }
            
            // 状态文本映射
            const statusMap = {
                'unused': '未使用',
                'used': '已使用',
                'expired': '已过期'
            };
            
            // 类型文本映射
            const typeMap = {
                'image_analysis': '形象分析'
            };
            
            // 格式化日期
            const createdAt = code.created_at ? new Date(code.created_at).toLocaleString() : '-';
            const expiredAt = code.expired_at ? new Date(code.expired_at).toLocaleString() : '-';
            const usedAt = code.used_at ? new Date(code.used_at).toLocaleString() : '-';
            
            // 生成详情HTML
            const detailsHtml = `
                <div class="code-detail-item">
                    <label>ID:</label>
                    <span>${code.id}</span>
                </div>
                <div class="code-detail-item">
                    <label>邀请码:</label>
                    <span>${code.code}</span>
                </div>
                <div class="code-detail-item">
                    <label>状态:</label>
                    <span class="status-badge status-${code.status}">${statusMap[code.status] || code.status}</span>
                </div>
                <div class="code-detail-item">
                    <label>类型:</label>
                    <span>${typeMap[code.type] || code.type}</span>
                </div>
                <div class="code-detail-item">
                    <label>创建时间:</label>
                    <span>${createdAt}</span>
                </div>
                <div class="code-detail-item">
                    <label>过期时间:</label>
                    <span>${expiredAt}</span>
                </div>
                <div class="code-detail-item">
                    <label>使用时间:</label>
                    <span>${usedAt}</span>
                </div>
                <div class="code-detail-item">
                    <label>创建者:</label>
                    <span>${code.creator_name || '-'}</span>
                </div>
                <div class="code-detail-item">
                    <label>使用者:</label>
                    <span>${code.user_name || '-'}</span>
                </div>
            `;
            
            if (codeDetails) {
                codeDetails.innerHTML = detailsHtml;
            }
        })
        .catch(error => {
            console.error('获取邀请码详情失败:', error);
            
            if (codeDetails) {
                codeDetails.innerHTML = `<p style="color: #f5222d;">加载失败: ${error.message || '请检查网络连接'}</p>`;
            }
        });
    },
    
    /**
     * 删除邀请码
     * @param {number} id 邀请码ID
     */
    deleteInvitationCode: function(id) {
        if (!confirm('确定要删除这个邀请码吗？此操作不可撤销。')) {
            return;
        }
        
        // 发起请求
        fetch(`${this.apiBaseUrl}/delete_invitation_code.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${Auth.getToken()}`
            },
            body: JSON.stringify({
                id: id
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('删除邀请码失败');
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                alert(data.msg || '删除邀请码失败');
                return;
            }
            
            // 显示成功消息
            alert('邀请码删除成功');
            
            // 重新加载列表
            this.loadInvitationCodes();
        })
        .catch(error => {
            console.error('删除邀请码失败:', error);
            alert(error.message || '删除邀请码失败，请检查网络连接');
        });
    }
}; 