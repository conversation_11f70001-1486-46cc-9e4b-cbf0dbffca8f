<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 日志函数
function logDebug($message, $data = null) {
    $log_file = __DIR__ . '/image_analysis_recommendation_api_debug.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] {$message}";
    
    if ($data !== null) {
        $log_message .= ': ' . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    
    file_put_contents($log_file, $log_message . PHP_EOL, FILE_APPEND);
}

logDebug("接收到新的个人形象分析推荐API请求", ['method' => $_SERVER['REQUEST_METHOD']]);

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    logDebug("OPTIONS预检请求，直接返回");
    exit;
}

// 检查请求数据
$json = file_get_contents('php://input');
if (empty($json)) {
    logDebug("错误: 缺少请求数据");
    echo json_encode(['error' => '缺少请求数据']);
    exit;
}

$data = json_decode($json, true);
if (!$data) {
    logDebug("错误: 无效的JSON数据", ['raw_input' => substr($json, 0, 1000)]);
    echo json_encode(['error' => '无效的JSON数据']);
    exit;
}

// 验证必要参数
if (!isset($data['user_clothes']) || !isset($data['analysis_data'])) {
    logDebug("错误: 缺少必要参数", ['keys' => array_keys($data)]);
    echo json_encode(['error' => '缺少必要参数']);
    exit;
}

// 检查是否为"换一批"请求
$refresh = isset($data['refresh']) ? (int)$data['refresh'] : 0;
// 获取上次推荐的衣物ID列表（如果有）
$previous_outfit = isset($data['previous_outfit']) ? $data['previous_outfit'] : [];
// 获取随机因子
$random_factor = isset($data['random_factor']) ? $data['random_factor'] : time() . '_' . mt_rand(1000, 9999);
// 获取形象分析数据
$analysis_data = $data['analysis_data'];

logDebug("请求参数", [
    'refresh' => $refresh, 
    'has_previous_outfit' => !empty($previous_outfit),
    'random_factor' => $random_factor,
    'analysis_id' => isset($analysis_data['id']) ? $analysis_data['id'] : 'unknown'
]);

if ($refresh && !empty($previous_outfit)) {
    logDebug("上次推荐的衣物", $previous_outfit);
}

// 提取用户衣物信息
$user_clothes = $data['user_clothes'];
logDebug("接收到的用户衣物数量", ['count' => count($user_clothes)]);

// 配置Gemini API密钥
// 注意：实际部署时，应使用环境变量或安全配置存储API密钥
$apiKey = 'AIzaSyD1-g64EwoKNcvs0LeAn9hbyHJRuKj0Slg'; // 实际API密钥
$model = 'gemini-1.5-flash'; // 使用的模型名称

// 创建衣物ID映射表，按分类整理衣物
$clothes_by_category = [];
$available_clothes_by_category = [];

// 添加用户衣物到映射表
foreach ($user_clothes as $clothing) {
    if (!isset($clothes_by_category[$clothing['category']])) {
        $clothes_by_category[$clothing['category']] = [];
        $available_clothes_by_category[$clothing['category']] = [];
    }
    $clothes_by_category[$clothing['category']][] = $clothing;
    $available_clothes_by_category[$clothing['category']][$clothing['id']] = [
        'id' => $clothing['id'],
        'name' => $clothing['name'],
        'image_url' => isset($clothing['image_url']) ? $clothing['image_url'] : null
    ];
}

logDebug("按分类整理的衣物数量", array_map('count', $clothes_by_category));
logDebug("可用衣物ID映射表", array_keys($available_clothes_by_category));

// 构建提示词前，确定有效的衣物分类
$valid_categories = [];
foreach ($clothes_by_category as $category => $clothes) {
    if (count($clothes) > 0) {
        $valid_categories[$category] = count($clothes);
    }
}

logDebug("有效的衣物分类", $valid_categories);

// 提取形象分析关键信息
$analysis_result = isset($analysis_data['analysis_result']) ? $analysis_data['analysis_result'] : [];
$user_data = isset($analysis_data['user_data']) ? $analysis_data['user_data'] : [];

// 提取关键信息
$body_shape = '';
$body_proportion = '';
$advantages = [];
$color_tone = '';
$suitable_colors = [];
$suitable_styles = [];
$unsuitable_styles = [];
$clothing_suggestions = [];
$styling_tips = [];

// 身形分析
if (isset($analysis_result['身形分析'])) {
    if (isset($analysis_result['身形分析']['体型分类'])) {
        $body_shape = $analysis_result['身形分析']['体型分类'];
    }
    if (isset($analysis_result['身形分析']['身材比例'])) {
        $body_proportion = $analysis_result['身形分析']['身材比例'];
    }
    if (isset($analysis_result['身形分析']['优势特点']) && is_array($analysis_result['身形分析']['优势特点'])) {
        $advantages = $analysis_result['身形分析']['优势特点'];
    }
}

// 肤色分析
if (isset($analysis_result['肤色分析'])) {
    if (isset($analysis_result['肤色分析']['色调'])) {
        $color_tone = $analysis_result['肤色分析']['色调'];
    }
    if (isset($analysis_result['肤色分析']['适合色系']) && is_array($analysis_result['肤色分析']['适合色系'])) {
        $suitable_colors = $analysis_result['肤色分析']['适合色系'];
    }
}

// 风格定位
if (isset($analysis_result['风格定位'])) {
    if (isset($analysis_result['风格定位']['适合风格']) && is_array($analysis_result['风格定位']['适合风格'])) {
        $suitable_styles = $analysis_result['风格定位']['适合风格'];
    }
    if (isset($analysis_result['风格定位']['不适合风格']) && is_array($analysis_result['风格定位']['不适合风格'])) {
        $unsuitable_styles = $analysis_result['风格定位']['不适合风格'];
    }
}

// 穿搭建议
if (isset($analysis_result['穿搭建议'])) {
    foreach ($analysis_result['穿搭建议'] as $category => $suggestions) {
        if (is_array($suggestions)) {
            foreach ($suggestions as $suggestion) {
                $clothing_suggestions[] = "{$category}: {$suggestion}";
            }
        }
    }
}

// 搭配技巧
if (isset($analysis_result['搭配技巧'])) {
    if (isset($analysis_result['搭配技巧']['视觉重点'])) {
        $styling_tips[] = "视觉重点: " . $analysis_result['搭配技巧']['视觉重点'];
    }
    if (isset($analysis_result['搭配技巧']['比例处理'])) {
        $styling_tips[] = "比例处理: " . $analysis_result['搭配技巧']['比例处理'];
    }
    if (isset($analysis_result['搭配技巧']['层次感'])) {
        $styling_tips[] = "层次感: " . $analysis_result['搭配技巧']['层次感'];
    }
    if (isset($analysis_result['搭配技巧']['实用技巧']) && is_array($analysis_result['搭配技巧']['实用技巧'])) {
        foreach ($analysis_result['搭配技巧']['实用技巧'] as $tip) {
            $styling_tips[] = "技巧: " . $tip;
        }
    }
}

// 构建提示词
$prompt = "你是一位专业的穿搭顾问，我想请你根据我的个人形象分析结果和衣橱内容创建一套完整的穿搭搭配。\n\n";

// 添加个人形象分析信息
$prompt .= "【我的个人形象分析结果】\n";

if (!empty($body_shape)) {
    $prompt .= "体型分类: {$body_shape}\n";
}
if (!empty($body_proportion)) {
    $prompt .= "身材比例: {$body_proportion}\n";
}
if (!empty($advantages)) {
    $prompt .= "优势特点: " . implode("、", $advantages) . "\n";
}
if (!empty($color_tone)) {
    $prompt .= "肤色色调: {$color_tone}\n";
}
if (!empty($suitable_colors)) {
    $prompt .= "适合色系: " . implode("、", $suitable_colors) . "\n";
}
if (!empty($suitable_styles)) {
    $prompt .= "适合风格: " . implode("、", $suitable_styles) . "\n";
}
if (!empty($unsuitable_styles)) {
    $prompt .= "不适合风格: " . implode("、", $unsuitable_styles) . "\n";
}

// 添加穿搭建议和搭配技巧
if (!empty($clothing_suggestions)) {
    $prompt .= "\n【穿搭建议】\n" . implode("\n", $clothing_suggestions) . "\n";
}
if (!empty($styling_tips)) {
    $prompt .= "\n【搭配技巧】\n" . implode("\n", $styling_tips) . "\n";
}

// 如果是换一批请求，添加特殊指令
if ($refresh) {
    $prompt .= "\n【重要：这是一个'换一批'请求，请为同样的个人形象分析结果生成一套全新的、与之前不同的穿搭组合。请确保选择不同的衣物和搭配风格。】\n";
    // 添加时间戳作为随机因子
    $prompt .= "当前时间戳: " . time() . "（请基于此生成不同的搭配）\n";
    $prompt .= "【非常重要】请务必选择与之前不同的衣物ID，这是用户明确要求的。每个类别都必须选择不同的衣物，不要重复之前的选择。\n\n";
    
    // 如果有上次的推荐结果，添加排除列表
    if (!empty($previous_outfit)) {
        $prompt .= "【排除列表】请不要选择以下衣物ID，这些是上次已经推荐过的：\n";
        foreach ($previous_outfit as $category => $item) {
            if (isset($item['id']) && $category != 'outfit_summary' && $category != 'is_refresh') {
                $prompt .= "- " . $category . ": ID " . $item['id'] . " (" . $item['name'] . ")\n";
            }
        }
        $prompt .= "\n请确保为每个类别选择与上面列表中不同的衣物ID。这是必须遵守的规则。\n\n";
    }
}

$prompt .= "\n我的衣橱中有以下分类的衣物，请仅从这些衣物中选择搭配：\n";

// 添加衣物信息，包含ID，只包含有效分类
foreach ($valid_categories as $category => $count) {
    $prompt .= "\n" . translateCategory($category) . "（共" . $count . "件）：\n";
    
    // 列出该分类的所有衣物，包含ID
    foreach ($clothes_by_category[$category] as $cloth) {
        $prompt .= "- ID:" . $cloth['id'] . " " . $cloth['name'];
        
        if (isset($cloth['description'])) {
            if (is_string($cloth['description'])) {
                $description = json_decode($cloth['description'], true);
                if ($description && isset($description['color'])) {
                    $prompt .= "（颜色：" . $description['color'] . "）";
                } else if ($description && isset($description['颜色'])) {
                    $prompt .= "（颜色：" . $description['颜色'] . "）";
                }
            } else if (isset($cloth['description']['颜色'])) {
                $prompt .= "（颜色：" . $cloth['description']['颜色'] . "）";
            } else if (isset($cloth['description']['color'])) {
                $prompt .= "（颜色：" . $cloth['description']['color'] . "）";
            }
        }
        
        if (!empty($cloth['tags'])) {
            $prompt .= "（标签：" . $cloth['tags'] . "）";
        }
        
        $prompt .= "\n";
    }
}

$prompt .= "\n请根据我的个人形象分析结果，从我衣橱中搭配一套完整的穿搭。请确保搭配合适且时尚，并考虑颜色的协调性、季节适宜性和场合适宜性。";
$prompt .= "\n\n重要提示：";
$prompt .= "\n1. 请只推荐我实际拥有的衣物分类。";
$prompt .= "\n2. 请考虑季节因素，例如：夏季不需要推荐外套，冬季可能不需要凉鞋等。";
$prompt .= "\n3. 如果某个分类的衣物不适合当前搭配（比如季节不合适、风格不搭配等），请完全省略该分类，不要在JSON中包含该分类。";
$prompt .= "\n4. 不要返回空的或不需要的类别，即使是带有解释的也不要返回。例如，不要返回\"外套: {id: null, name: null, reason: '夏天不需要外套'}\"，而是完全省略外套类别。";
$prompt .= "\n5. 每次请生成一套全新的、创意的搭配组合，避免重复之前可能生成过的搭配。";
$prompt .= "\n6. 请尽量选择符合我个人形象分析结果的衣物，特别是适合我的色系、风格和身形特点的衣物。";
if ($refresh) {
    $prompt .= "\n7. 【重要】这是'换一批'请求，必须选择与之前不同的衣物ID。";
    $prompt .= "\n8. 【强制要求】每个类别都必须选择不同的衣物ID，不允许重复之前的推荐。";
}

$prompt .= "\n\n请以JSON格式返回结果，格式如下：";
$prompt .= "\n{";

// 动态生成JSON格式示例，只包含有效分类
$category_mapping = [
    'tops' => 'top',
    'pants' => 'bottom',
    'skirts' => 'bottom',
    'coats' => 'outerwear',
    'shoes' => 'shoes',
    'accessories' => 'accessories',
    'bags' => 'bag'
];

$included_categories = [];

// 添加有效分类
foreach ($valid_categories as $category => $count) {
    if (isset($category_mapping[$category])) {
        $outfit_category = $category_mapping[$category];
        if (!in_array($outfit_category, $included_categories)) {
            $prompt .= "\n  \"" . $outfit_category . "\": {\"id\": \"具体衣物ID\", \"name\": \"名称\", \"reason\": \"推荐理由\"},";
            $included_categories[] = $outfit_category;
        }
    }
}

$prompt .= "\n  \"outfit_summary\": \"整体穿搭风格描述和建议\"";
$prompt .= "\n}";

// 添加示例说明
$prompt .= "\n\n示例：";
$prompt .= "\n如果是夏季搭配，可能不需要外套，那么正确的返回应该是：";
$prompt .= "\n{";
$prompt .= "\n  \"top\": {\"id\": \"123\", \"name\": \"白色T恤\", \"reason\": \"清爽舒适，适合夏季\"},";
$prompt .= "\n  \"bottom\": {\"id\": \"456\", \"name\": \"牛仔短裤\", \"reason\": \"与T恤搭配清新自然\"},";
$prompt .= "\n  \"shoes\": {\"id\": \"789\", \"name\": \"帆布鞋\", \"reason\": \"休闲百搭\"},";
$prompt .= "\n  \"outfit_summary\": \"清新夏日休闲风格\"";
$prompt .= "\n}";
$prompt .= "\n注意上面的例子中完全省略了外套(outerwear)、配饰(accessories)和包包(bag)类别，因为它们不适合这个夏季搭配。";

$prompt .= "\n\n请只从我提供的衣物列表中进行选择，不要编造不存在的衣物或分类。";
$prompt .= "\n请确保返回的每个衣物都包含正确的ID，这是非常重要的。ID必须是我提供的衣物列表中的实际ID。";
$prompt .= "\n不要在返回结果中包含image_url字段，这会在后端处理。";

logDebug("构建的提示词", ['prompt_length' => strlen($prompt), 'prompt_excerpt' => substr($prompt, 0, 500)]);

// 调用Gemini API
$geminiUrl = "https://generativelanguage.googleapis.com/v1beta/models/" . $model . ":generateContent?key=" . $apiKey;

// 根据是否为"换一批"请求调整参数
$temperature = $refresh ? (0.9 + (mt_rand(0, 20) / 100)) : 0.7; // 换一批时增加随机性并添加小的随机波动
$topP = $refresh ? (0.95 + (mt_rand(0, 5) / 100)) : 0.8;
$topK = $refresh ? (40 + mt_rand(0, 20)) : 40;

// 使用随机因子来确保每次请求都不同
$seed = mt_rand(1, 2000000);
if (!empty($random_factor)) {
    // 从随机因子中提取数字部分作为种子的一部分
    preg_match('/(\d+)_(\d+)/', $random_factor, $matches);
    if (!empty($matches)) {
        $seed = $seed + intval($matches[1]) % 10000 + intval($matches[2]);
    } else {
        $seed = $seed + time() % 10000;
    }
}
logDebug("生成的随机种子", ['seed' => $seed]);

$request = [
    "contents" => [
        [
            "parts" => [
                [
                    "text" => $prompt
                ]
            ]
        ]
    ],
    "generationConfig" => [
        "temperature" => $temperature,
        "topP" => $topP,
        "topK" => $topK,
        "maxOutputTokens" => 1024,
        "stopSequences" => [],
        "candidateCount" => 1,
        // 添加随机种子，确保每次生成不同的结果
        "seed" => $seed
    ]
];

logDebug("准备调用Gemini API", [
    'url' => $geminiUrl, 
    'temperature' => $temperature, 
    'topP' => $topP, 
    'topK' => $topK,
    'refresh' => $refresh
]);

// 发送请求到Gemini API
$ch = curl_init($geminiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

$response = curl_exec($ch);
if (curl_errno($ch)) {
    $error = curl_error($ch);
    logDebug("调用Gemini API失败", ['curl_error' => $error]);
    echo json_encode(['error' => '调用Gemini API失败: ' . $error]);
    exit;
}
curl_close($ch);
logDebug("Gemini API调用完成", ['response_length' => strlen($response)]);

// 解析API响应
$api_response = json_decode($response, true);

// 检查API响应是否有效
if (!$api_response || !isset($api_response['candidates']) || empty($api_response['candidates'])) {
    logDebug("无效的API响应", ['response' => substr($response, 0, 1000)]);
    echo json_encode(['error' => '无效的API响应']);
    exit;
}

// 提取生成的文本
$generated_text = '';
if (isset($api_response['candidates'][0]['content']['parts'][0]['text'])) {
    $generated_text = $api_response['candidates'][0]['content']['parts'][0]['text'];
}

if (empty($generated_text)) {
    logDebug("API返回的文本为空");
    echo json_encode(['error' => 'API返回的文本为空']);
    exit;
}

logDebug("API返回的原始文本", ['text_length' => strlen($generated_text), 'text_excerpt' => substr($generated_text, 0, 500)]);

// 从生成的文本中提取JSON部分
$outfit_json = extractJsonFromText($generated_text);
if (!$outfit_json) {
    logDebug("无法从API响应中提取有效的JSON", ['text' => $generated_text]);
    echo json_encode(['error' => '无法从API响应中提取有效的JSON']);
    exit;
}

logDebug("提取的JSON数据", ['json_data' => $outfit_json]);

// 验证并处理提取的JSON数据
$outfit_data = json_decode($outfit_json, true);
if (!$outfit_data) {
    logDebug("无法解析提取的JSON", ['json_string' => $outfit_json]);
    echo json_encode(['error' => '无法解析提取的JSON']);
    exit;
}

// 添加刷新标记
if ($refresh) {
    $outfit_data['is_refresh'] = true;
}

// 检查并修复衣物ID，确保它们是整数而不是字符串
foreach ($outfit_data as $category => &$item) {
    if ($category !== 'outfit_summary' && $category !== 'is_refresh' && isset($item['id'])) {
        // 确保ID是整数
        if (is_string($item['id']) && is_numeric($item['id'])) {
            $item['id'] = intval($item['id']);
        }
    }
}

// 返回处理后的推荐结果
echo json_encode($outfit_data);
logDebug("成功返回推荐结果", ['categories' => array_keys($outfit_data)]);

/**
 * 从文本中提取JSON部分
 * @param string $text 包含JSON的文本
 * @return string|null 提取的JSON字符串，如果没有找到则返回null
 */
function extractJsonFromText($text) {
    // 尝试匹配花括号包围的JSON部分
    if (preg_match('/\{[\s\S]*\}/m', $text, $matches)) {
        return $matches[0];
    }
    return null;
}

/**
 * 将衣物分类转换为中文显示名称
 * @param string $category 衣物分类
 * @return string 中文显示名称
 */
function translateCategory($category) {
    $translations = [
        'tops' => '上衣',
        'pants' => '裤子',
        'skirts' => '裙子',
        'coats' => '外套',
        'shoes' => '鞋子',
        'accessories' => '配饰',
        'bags' => '包包'
    ];
    
    return isset($translations[$category]) ? $translations[$category] : $category;
}
?> 