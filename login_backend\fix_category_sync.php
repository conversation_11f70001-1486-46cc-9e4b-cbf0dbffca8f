<?php
/**
 * 修复分类同步问题
 * 检查并修复圈子成员的自定义分类同步状态
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];
$action = isset($_GET['action']) ? $_GET['action'] : 'check';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "=== 分类同步修复工具 ===\n";
    echo "当前用户ID: $userId\n";
    echo "操作类型: $action\n\n";
    
    // 1. 获取用户所在的圈子
    echo "1. 获取用户所在的圈子:\n";
    $stmt = $conn->prepare("
        SELECT cm.circle_id, cm.role, c.name as circle_name
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id AND cm.status = 'active'
        ORDER BY cm.joined_at DESC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $userCircles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($userCircles)) {
        echo "用户不在任何活跃圈子中\n";
        exit;
    }
    
    foreach ($userCircles as $circle) {
        echo "- 圈子ID: {$circle['circle_id']}, 名称: {$circle['circle_name']}, 角色: {$circle['role']}\n";
    }
    echo "\n";
    
    $circleId = $userCircles[0]['circle_id'];
    
    // 2. 检查圈子中所有成员的分类同步状态
    echo "2. 检查圈子成员的分类同步状态:\n";
    $stmt = $conn->prepare("
        SELECT cm.user_id, cm.role, u.nickname
        FROM circle_members cm
        LEFT JOIN users u ON cm.user_id = u.id
        WHERE cm.circle_id = :circle_id AND cm.status = 'active'
        ORDER BY cm.role DESC, cm.joined_at ASC
    ");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $needsSync = [];
    
    foreach ($members as $member) {
        $memberId = $member['user_id'];
        $memberName = $member['nickname'];
        $isCurrent = $memberId == $userId;
        
        echo "2.{$memberId} 用户: $memberName" . ($isCurrent ? ' (当前用户)' : '') . "\n";
        
        // 检查该用户的自定义分类
        $stmt = $conn->prepare("
            SELECT COUNT(*) as total_count,
                   COUNT(CASE WHEN circle_id IS NULL THEN 1 END) as unsynced_count,
                   COUNT(CASE WHEN circle_id = :circle_id THEN 1 END) as synced_count
            FROM clothing_categories
            WHERE user_id = :user_id AND is_system = 0
        ");
        $stmt->bindParam(':user_id', $memberId, PDO::PARAM_INT);
        $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
        $stmt->execute();
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "  - 总自定义分类: {$stats['total_count']} 个\n";
        echo "  - 已同步到圈子: {$stats['synced_count']} 个\n";
        echo "  - 未同步: {$stats['unsynced_count']} 个\n";
        
        if ($stats['unsynced_count'] > 0) {
            $needsSync[] = [
                'user_id' => $memberId,
                'nickname' => $memberName,
                'unsynced_count' => $stats['unsynced_count']
            ];
            echo "  ⚠️ 需要同步\n";
        } else {
            echo "  ✅ 同步状态正常\n";
        }
        echo "\n";
    }
    
    // 3. 如果是修复模式，执行同步
    if ($action === 'fix' && !empty($needsSync)) {
        echo "3. 执行分类同步修复:\n";
        
        $conn->beginTransaction();
        try {
            $totalFixed = 0;
            
            foreach ($needsSync as $member) {
                $memberId = $member['user_id'];
                $memberName = $member['nickname'];
                
                echo "修复用户: $memberName (ID: $memberId)\n";
                
                // 同步该用户的未同步自定义分类
                $stmt = $conn->prepare("
                    UPDATE clothing_categories 
                    SET circle_id = :circle_id 
                    WHERE user_id = :user_id AND is_system = 0 AND circle_id IS NULL
                ");
                $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
                $stmt->bindParam(':user_id', $memberId, PDO::PARAM_INT);
                $stmt->execute();
                
                $fixedCount = $stmt->rowCount();
                $totalFixed += $fixedCount;
                
                echo "- 已同步 $fixedCount 个自定义分类\n";
            }
            
            $conn->commit();
            echo "\n✅ 同步修复完成，总共修复了 $totalFixed 个分类\n";
            
        } catch (Exception $e) {
            $conn->rollBack();
            echo "\n❌ 同步修复失败: " . $e->getMessage() . "\n";
        }
        
    } elseif ($action === 'fix' && empty($needsSync)) {
        echo "3. 所有成员的分类同步状态都正常，无需修复\n";
    }
    
    // 4. 验证修复后的效果
    if ($action === 'fix' || $action === 'check') {
        echo "\n4. 验证当前状态:\n";
        
        // 检查圈子中的自定义分类总数
        $stmt = $conn->prepare("
            SELECT COUNT(*) as total_count,
                   COUNT(CASE WHEN user_id = :user_id THEN 1 END) as own_count,
                   COUNT(CASE WHEN user_id != :user_id THEN 1 END) as others_count
            FROM clothing_categories
            WHERE is_system = 0 AND circle_id = :circle_id
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
        $stmt->execute();
        $circleStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "圈子中的自定义分类统计:\n";
        echo "- 总数: {$circleStats['total_count']} 个\n";
        echo "- 自己的: {$circleStats['own_count']} 个\n";
        echo "- 其他用户的: {$circleStats['others_count']} 个\n\n";
        
        // 测试shared查询
        $circleIds = implode(',', array_column($userCircles, 'circle_id'));
        $stmt = $conn->prepare("
            SELECT COUNT(*) as total_count,
                   COUNT(CASE WHEN is_system = 1 THEN 1 END) as system_count,
                   COUNT(CASE WHEN is_system = 0 THEN 1 END) as custom_count
            FROM clothing_categories c
            WHERE (
                (c.is_system = 1 AND c.user_id = :user_id) OR
                (c.is_system = 0 AND c.user_id != :user_id AND c.circle_id IN ($circleIds))
            )
        ");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $sharedStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "shared数据源查询结果:\n";
        echo "- 总分类数: {$sharedStats['total_count']} 个\n";
        echo "- 系统分类: {$sharedStats['system_count']} 个\n";
        echo "- 其他用户自定义分类: {$sharedStats['custom_count']} 个\n\n";
        
        if ($sharedStats['system_count'] > 0 && $sharedStats['custom_count'] == $circleStats['others_count']) {
            echo "🎉 分类显示状态正常！\n";
        } elseif ($sharedStats['system_count'] == 0) {
            echo "⚠️ 系统分类缺失，可能需要检查用户系统分类初始化\n";
        } elseif ($sharedStats['custom_count'] < $circleStats['others_count']) {
            echo "⚠️ 部分自定义分类仍未正确显示\n";
        }
    }
    
    echo "\n=== 工具完成 ===\n";
    
    if ($action === 'check' && !empty($needsSync)) {
        echo "\n💡 建议：运行 ?action=fix 来修复分类同步问题\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
