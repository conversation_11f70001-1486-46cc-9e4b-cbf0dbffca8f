const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    analysisId: '',
    orderId: '',
    amount: 0,
    payParams: null,
    hasShared: false  // 添加标记用户是否已分享
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (options.analysisId) {
      this.setData({
        analysisId: options.analysisId,
        orderId: options.orderId || '',
        amount: '1.00' // 固定价格为1元
      });
      
      // 如果有payParams，尝试解析
      if (options.payParams) {
        try {
          const payParams = JSON.parse(decodeURIComponent(options.payParams));
          this.setData({
            payParams: payParams
          });
        } catch (e) {
          console.error('解析支付参数失败:', e);
          // 不显示错误提示，因为我们会使用模拟支付
        }
      }
    } else {
      wx.showToast({
        title: '缺少分析ID',
        icon: 'none'
      });
    }
    
    // 检查是否已经通过分享获得免费机会
    this.checkFreeChance();
  },
  
  /**
   * 检查用户是否有免费分析机会
   */
  checkFreeChance: function() {
    const freeAnalysisFlag = wx.getStorageSync('freeFaceAnalysisChance');
    if (freeAnalysisFlag) {
      wx.showToast({
        title: '您已获得免费分析机会！',
        icon: 'success',
        duration: 2000
      });
    }
  },
  
  /**
   * 用户点击右上角或按钮分享
   */
  onShareAppMessage: function() {
    // 记录用户已触发分享
    this.setData({
      hasShared: true
    });
    
    // 分享成功后给予奖励（微信限制无法确认是否实际分享，只能确认触发分享事件）
    setTimeout(() => {
      this.rewardAfterShare();
    }, 2000);
    
    // 分享内容
    return {
      title: '专业面容分析！1元获得个性化妆容建议，限时免费机会！',
      path: '/pages/face_analysis/index/index',
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png', // 替换为实际的分享图片
      success: function(res) {
        // 分享成功的回调，但微信限制实际上无法确认是否成功分享
        console.log('分享成功', res);
      }
    }
  },
  
  /**
   * 分享后奖励用户
   */
  rewardAfterShare: function() {
    // 为用户提供免费分析机会
    wx.setStorageSync('freeFaceAnalysisChance', true);
    
    // 显示获得奖励提示
    wx.showModal({
      title: '恭喜获得免费分析机会',
      content: '感谢您的分享！您已获得一次免费面容分析机会，现在可以免费使用此服务',
      confirmText: '立即使用',
      success: (res) => {
        if (res.confirm) {
          // 用户点击立即使用，标记为免费并处理
          this.processFreeAnalysis();
        }
      }
    });
  },
  
  /**
   * 处理免费分析
   */
  processFreeAnalysis: function() {
    wx.showLoading({
      title: '正在处理...',
    });

    // 模拟支付成功，更新支付状态
    this.updatePaymentStatus();

    // 使用后清除免费机会
    wx.removeStorageSync('freeFaceAnalysisChance');
  },

  /**
   * 发起支付
   */
  payNow: function () {
    // 检查是否有免费机会
    if (wx.getStorageSync('freeFaceAnalysisChance')) {
      wx.showModal({
        title: '使用免费机会',
        content: '您有一次免费面容分析机会，是否立即使用？',
        confirmText: '立即使用',
        cancelText: '暂不使用',
        success: (res) => {
          if (res.confirm) {
            // 使用免费机会
            this.processFreeAnalysis();
          }
        }
      });
      return;
    }
    
    // 如果没有payParams，提示创建订单失败
    if (!this.data.payParams) {
      wx.showToast({
        title: '支付参数缺失，请重新创建订单',
        icon: 'none'
      });
      return;
    }

    // 发起微信支付
    wx.requestPayment({
      ...this.data.payParams,
      success: (res) => {
        console.log('支付成功:', res);
        
        // 支付成功后，手动更新支付状态
        this.updatePaymentStatus();
      },
      fail: (err) => {
        console.error('支付失败:', err);
        
        if (err.errMsg.indexOf('cancel') > -1) {
          wx.showToast({
            title: '支付已取消',
            icon: 'none'
          });
        } else {
          wx.showToast({
            title: '支付失败',
            icon: 'none'
          });
        }
      }
    });
  },
  
  /**
   * 更新支付状态
   */
  updatePaymentStatus: function() {
    wx.showLoading({
      title: '正在分析...',
    });
    
    const token = wx.getStorageSync('token');
    
    console.log('开始更新支付状态', {
      analysisId: this.data.analysisId,
      url: app.globalData.baseUrl + '/update_face_analysis_payment.php'
    });
    
    wx.request({
      url: app.globalData.baseUrl + '/update_face_analysis_payment.php',
      method: 'POST',
      data: {
        analysis_id: this.data.analysisId
      },
      header: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      success: (res) => {
        console.log('支付状态更新响应:', res.data);
        
        if (!res.data.error) {
          // 支付状态更新成功，跳转到上传页面
          wx.hideLoading();
          wx.showToast({
            title: '支付成功',
            icon: 'success',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                wx.redirectTo({
                  url: `/pages/face_analysis/upload/upload?analysisId=${this.data.analysisId}`
                });
              }, 1500);
            }
          });
        } else {
          wx.hideLoading();
          // 如果更新失败但支付成功，仍然跳转，让后台异步处理
          console.error('更新支付状态失败:', res.data.msg);
          
          // 显示更详细的错误信息
          wx.showModal({
            title: '支付状态更新提示',
            content: '支付已成功，但状态更新失败: ' + res.data.msg + '。将继续跳转到上传页面。',
            showCancel: false,
            success: () => {
              wx.redirectTo({
                url: `/pages/face_analysis/upload/upload?analysisId=${this.data.analysisId}`
              });
            }
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        // 网络请求失败，但支付已成功，仍然跳转
        console.error('请求更新支付状态失败', err);
        
        // 显示更详细的错误信息
        wx.showModal({
          title: '网络请求失败',
          content: '支付已成功，但网络请求失败: ' + (err.errMsg || JSON.stringify(err)) + '。将继续跳转到上传页面。',
          showCancel: false,
          success: () => {
            wx.redirectTo({
              url: `/pages/face_analysis/upload/upload?analysisId=${this.data.analysisId}`
            });
          }
        });
      }
    });
  }
})
