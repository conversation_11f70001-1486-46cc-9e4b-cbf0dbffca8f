<?php
require_once 'config.php';
require_once 'db.php';

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    // Get WeChat session using code
    public function getWxSession($code) {
        $url = "https://api.weixin.qq.com/sns/jscode2session?appid=" . WX_APPID . 
               "&secret=" . WX_SECRET . 
               "&js_code=" . $code . 
               "&grant_type=authorization_code";
        
        $response = file_get_contents($url);
        if ($response === false) {
            return ['error' => true, 'msg' => 'Failed to connect to WeChat API'];
        }
        
        $data = json_decode($response, true);
        
        if (isset($data['errcode']) && $data['errcode'] != 0) {
            return ['error' => true, 'msg' => $data['errmsg'], 'code' => $data['errcode']];
        }
        
        return ['error' => false, 'data' => $data];
    }
    
    // Login or register user
    public function loginOrRegisterUser($wxSessionData) {
        $openid = $wxSessionData['openid'];
        $sessionKey = $wxSessionData['session_key'];
        $unionid = isset($wxSessionData['unionid']) ? $wxSessionData['unionid'] : null;
        
        // Check if user exists
        $user = $this->db->findUserByOpenid($openid);
        
        // If user doesn't exist, create new user
        if (!$user) {
            $userId = $this->db->createUser($openid, $sessionKey, $unionid);
            $user = ['id' => $userId, 'openid' => $openid];
        } else {
            // Update session key
            $this->db->updateSessionKey($user['id'], $sessionKey);
        }
        
        // Generate token for client
        $token = $this->generateToken($user['id'], $openid);
        
        return [
            'token' => $token,
            'user_id' => $user['id'],
            'is_new_user' => !isset($user['nickname']) || empty($user['nickname'])
        ];
    }
    
    // Generate JWT-like token
    private function generateToken($userId, $openid) {
        // Simple token generation for demo purposes
        // In production, use a proper JWT library
        $header = base64_encode(json_encode(['alg' => 'HS256', 'typ' => 'JWT']));
        $payload = base64_encode(json_encode([
            'sub' => $userId,
            'openid' => $openid,
            'iat' => time(),
            'exp' => time() + (7 * 24 * 60 * 60) // 7 days
        ]));
        
        $signature = hash_hmac('sha256', "$header.$payload", WX_SECRET);
        return "$header.$payload.$signature";
    }
    
    // 生成管理员JWT令牌
    public function generateAdminToken($adminId, $username) {
        $header = base64_encode(json_encode(['alg' => 'HS256', 'typ' => 'JWT']));
        $payload = base64_encode(json_encode([
            'admin_id' => $adminId,
            'username' => $username,
            'iat' => time(),
            'exp' => time() + (24 * 60 * 60), // 1天过期
            'type' => 'admin'
        ]));
        
        $signature = hash_hmac('sha256', "$header.$payload", ADMIN_SECRET_KEY);
        return "$header.$payload.$signature";
    }
    
    // 验证管理员令牌
    public function verifyAdminToken($token) {
        // 检查token是否包含两个点（分隔三部分）
        if(substr_count($token, '.') != 2) {
            return false;
        }
        
        // 安全拆分token
        $parts = explode('.', $token);
        if(count($parts) != 3) {
            return false;
        }
        
        list($header, $payload, $signature) = $parts;
        
        // 验证签名
        $calculatedSignature = hash_hmac('sha256', "$header.$payload", ADMIN_SECRET_KEY);
        
        if ($calculatedSignature !== $signature) {
            return false;
        }
        
        $payload = json_decode(base64_decode($payload), true);
        
        // 检查payload是否解析成功
        if(!$payload) {
            return false;
        }
        
        // 检查是否为管理员令牌
        if(!isset($payload['type']) || $payload['type'] !== 'admin') {
            return false;
        }
        
        // 检查是否包含过期时间
        if(!isset($payload['exp'])) {
            return false;
        }
        
        // 检查token是否已过期
        if ($payload['exp'] < time()) {
            return false;
        }
        
        return $payload;
    }
    
    // Verify token
    public function verifyToken($token) {
        // 特殊处理模拟token（微信小程序前端生成的临时token）
        if (strpos($token, 'mock_') === 0) {
            // 处理app.js生成的mock_token_时间戳格式
            // 确保模拟用户存在
            $this->ensureMockUserExists();
            
            return [
                'sub' => 1, // 默认用户ID
                'openid' => 'mock_openid',
                'iat' => time(),
                'exp' => time() + (7 * 24 * 60 * 60) // 7天后过期
            ];
        }
        
        // 检查token是否包含两个点（分隔三部分）
        if(substr_count($token, '.') != 2) {
            return false;
        }
        
        // 安全拆分token
        $parts = explode('.', $token);
        if(count($parts) != 3) {
            return false;
        }
        
        list($header, $payload, $signature) = $parts;
        
        // 特殊处理前端生成的JWT格式模拟token
        if($signature === 'mocksignature') {
            // 确保模拟用户存在
            $this->ensureMockUserExists();
            
            try {
                // 尝试解析payload, 但即使失败也返回有效token数据
                $decodedPayload = json_decode(base64_decode($payload), true);
                if($decodedPayload && isset($decodedPayload['sub'])) {
                    return $decodedPayload;
                }
            } catch(Exception $e) {
                // 无需处理异常，使用默认值
            }
            
            // 使用默认值
            return [
                'sub' => 1, // 默认用户ID
                'openid' => 'mock_openid',
                'iat' => time(),
                'exp' => time() + (7 * 24 * 60 * 60) // 7天后过期
            ];
        }
        
        // 正常JWT验证流程
        $calculatedSignature = hash_hmac('sha256', "$header.$payload", WX_SECRET);
        
        if ($calculatedSignature !== $signature) {
            return false;
        }
        
        $payload = json_decode(base64_decode($payload), true);
        
        // 检查payload是否解析成功
        if(!$payload) {
            return false;
        }
        
        // 检查是否包含过期时间
        if(!isset($payload['exp'])) {
            return false;
        }
        
        // 检查token是否已过期
        if ($payload['exp'] < time()) {
            return false;
        }
        
        return $payload;
    }
    
    // 确保模拟用户存在于数据库中
    private function ensureMockUserExists() {
        try {
            // 尝试直接通过ID=1查找用户
            $conn = $this->db->getConnection();
            $idStmt = $conn->prepare("SELECT * FROM users WHERE id = 1");
            $idStmt->execute();
            $userWithId1 = $idStmt->fetch(PDO::FETCH_ASSOC);
            
            // 检查ID=1的用户是否存在
            if ($userWithId1) {
                // 已存在ID=1的用户
                if ($userWithId1['openid'] === 'mock_openid') {
                    // ID=1已经是模拟用户，什么都不做
                    error_log('体验账号已存在，ID: 1');
                    return;
                } else {
                    // ID=1是其他用户，修改该用户成为模拟用户
                    error_log('ID=1已被其他用户占用，将其转换为体验账号');
                    $updateStmt = $conn->prepare("UPDATE users SET openid = 'mock_openid', session_key = 'mock_session_key', unionid = 'mock_unionid', nickname = '体验账号', avatar_url = '/images/default-avatar.png', gender = 0, updated_at = NOW() WHERE id = 1");
                    $updateStmt->execute();
                    return;
                }
            }
            
            // 检查模拟用户是否已存在（通过openid）
            $mockUser = $this->db->findUserByOpenid('mock_openid');
            
            if ($mockUser) {
                error_log('模拟用户已存在，ID: ' . $mockUser['id']);
                
                // 如果存在但ID不是1，则尝试将其ID修改为1
                if ($mockUser['id'] != 1) {
                    error_log('尝试将体验账号ID修改为1');
                    
                    // 先尝试将现有ID=1的记录（如果有）移动到其他ID
                    $conn->beginTransaction();
                    try {
                        // 检查是否需要修改AUTO_INCREMENT
                        $tableStatusStmt = $conn->query("SHOW TABLE STATUS LIKE 'users'");
                        $tableStatus = $tableStatusStmt->fetch(PDO::FETCH_ASSOC);
                        
                        if ($tableStatus['Auto_increment'] <= 1) {
                            // 如果自增值小于等于1，先将其设为2
                            $conn->exec("ALTER TABLE users AUTO_INCREMENT = 2");
                        }
                        
                        // 尝试删除并重建体验账号
                        $conn->exec("DELETE FROM users WHERE openid = 'mock_openid'");
                        
                        // 插入ID=1的体验账号
                        $insertStmt = $conn->prepare("INSERT INTO users (id, openid, session_key, unionid, nickname, avatar_url, gender, created_at, updated_at) VALUES (1, 'mock_openid', 'mock_session_key', 'mock_unionid', '体验账号', '/images/default-avatar.png', 0, NOW(), NOW())");
                        $insertStmt->execute();
                        
                        $conn->commit();
                        error_log('成功将体验账号ID设置为1');
                    } catch (Exception $e) {
                        $conn->rollBack();
                        error_log('修改体验账号ID失败: ' . $e->getMessage());
                    }
                }
            } else {
                error_log('体验账号不存在，尝试创建ID=1的体验账号');
                
                // 不存在，创建一个ID=1的体验账号
                $conn->beginTransaction();
                try {
                    // 检查是否需要修改AUTO_INCREMENT
                    $tableStatusStmt = $conn->query("SHOW TABLE STATUS LIKE 'users'");
                    $tableStatus = $tableStatusStmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($tableStatus['Auto_increment'] <= 1) {
                        // 如果自增值小于等于1，先将其设为2
                        $conn->exec("ALTER TABLE users AUTO_INCREMENT = 2");
                    }
                    
                    // 插入ID=1的体验账号
                    $insertStmt = $conn->prepare("INSERT INTO users (id, openid, session_key, unionid, nickname, avatar_url, gender, created_at, updated_at) VALUES (1, 'mock_openid', 'mock_session_key', 'mock_unionid', '体验账号', '/images/default-avatar.png', 0, NOW(), NOW())");
                    $insertStmt->execute();
                    
                    $conn->commit();
                    error_log('成功创建ID=1的体验账号');
                } catch (Exception $e) {
                    $conn->rollBack();
                    error_log('创建ID=1体验账号失败: ' . $e->getMessage());
                    
                    // 回退到普通方式创建模拟用户
                    error_log('使用常规方法创建体验账号');
                    $userId = $this->db->createUser(
                        'mock_openid', 
                        'mock_session_key', 
                        'mock_unionid'
                    );
                    
                    error_log("体验账号创建成功，ID: $userId");
                    
                    // 更新用户资料
                    $this->db->updateUserProfile(
                        $userId, 
                        '体验账号', 
                        '/images/default-avatar.png', 
                        0
                    );
                    
                    error_log("体验账号资料更新成功");
                }
            }
        } catch (Exception $e) {
            error_log('创建体验账号时出错: ' . $e->getMessage());
        }
    }
} 