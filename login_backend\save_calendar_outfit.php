<?php
// 引入必要的文件
require_once 'auth.php';
require_once 'db.php';
require_once 'config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 验证token获取用户ID
$auth = new Auth();

// 获取token
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 如果有Bearer前缀，去掉它
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
}

if (!$token) {
    echo json_encode([
        'error' => true,
        'msg' => '未提供授权Token'
    ]);
    exit;
}

// 使用Auth类验证token
$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'error' => true,
        'msg' => '未授权，请先登录'
    ]);
    exit;
}

$userId = $payload['sub']; // 从payload中获取用户ID

// 处理POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => '不支持的请求方法'
    ]);
    exit;
}

// 获取请求数据
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

// 验证必要参数
if (!isset($input['outfit_id']) && $input['action'] !== 'remove') {
    echo json_encode([
        'error' => true,
        'msg' => '缺少必要参数: outfit_id'
    ]);
    exit;
}

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();

    // 验证穿搭是否存在且属于当前用户（如果提供了outfit_id）
    if (isset($input['outfit_id'])) {
        $stmt = $conn->prepare("SELECT id FROM outfits WHERE id = :outfit_id AND user_id = :user_id");
        $stmt->bindParam(':outfit_id', $input['outfit_id']);
        $stmt->bindParam(':user_id', $userId);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            echo json_encode([
                'error' => true,
                'msg' => '穿搭不存在或无权操作'
            ]);
            exit;
        }
    }

    // 根据操作类型处理
    $action = isset($input['action']) ? $input['action'] : 'add';
    
    if ($action === 'add') {
        // 添加或更新日历日期
        if (!isset($input['calendar_date'])) {
            echo json_encode([
                'error' => true,
                'msg' => '缺少必要参数: calendar_date'
            ]);
            exit;
        }
        
        // 验证日期格式
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $input['calendar_date'])) {
            echo json_encode([
                'error' => true,
                'msg' => '日期格式不正确，应为YYYY-MM-DD'
            ]);
            exit;
        }
        
        // 首先检查该日期是否已有穿搭安排
        $checkStmt = $conn->prepare("
            SELECT id FROM outfit_calendar 
            WHERE user_id = :user_id AND calendar_date = :calendar_date
        ");
        $checkStmt->bindParam(':user_id', $userId);
        $checkStmt->bindParam(':calendar_date', $input['calendar_date']);
        $checkStmt->execute();
        
        if ($checkStmt->rowCount() > 0) {
            // 如果该日期已有穿搭，更新为新的穿搭
            $calendarEntryId = $checkStmt->fetchColumn();
            $updateStmt = $conn->prepare("
                UPDATE outfit_calendar 
                SET outfit_id = :outfit_id, updated_at = NOW()
                WHERE id = :id
            ");
            $updateStmt->bindParam(':outfit_id', $input['outfit_id']);
            $updateStmt->bindParam(':id', $calendarEntryId);
            $updateStmt->execute();
            
            if ($updateStmt->rowCount() === 0) {
                echo json_encode([
                    'error' => true,
                    'msg' => '更新穿搭日期失败'
                ]);
                exit;
            }
        } else {
            // 如果该日期没有穿搭，创建新记录
            $insertStmt = $conn->prepare("
                INSERT INTO outfit_calendar (outfit_id, user_id, calendar_date, created_at)
                VALUES (:outfit_id, :user_id, :calendar_date, NOW())
            ");
            $insertStmt->bindParam(':outfit_id', $input['outfit_id']);
            $insertStmt->bindParam(':user_id', $userId);
            $insertStmt->bindParam(':calendar_date', $input['calendar_date']);
            $insertStmt->execute();
            
            if ($insertStmt->rowCount() === 0) {
                echo json_encode([
                    'error' => true,
                    'msg' => '添加穿搭日期失败'
                ]);
                exit;
            }
        }
        
        // 返回成功
        echo json_encode([
            'error' => false,
            'msg' => '穿搭日期已更新',
            'data' => [
                'outfit_id' => $input['outfit_id'],
                'calendar_date' => $input['calendar_date']
            ]
        ]);
        
    } elseif ($action === 'remove') {
        // 移除日历日期
        if (!isset($input['calendar_date'])) {
            echo json_encode([
                'error' => true,
                'msg' => '缺少必要参数: calendar_date'
            ]);
            exit;
        }
        
        $deleteStmt = $conn->prepare("
            DELETE FROM outfit_calendar
            WHERE user_id = :user_id AND calendar_date = :calendar_date
        ");
        $deleteStmt->bindParam(':user_id', $userId);
        $deleteStmt->bindParam(':calendar_date', $input['calendar_date']);
        $deleteStmt->execute();
        
        // 返回成功，无论是否实际删除了记录
        echo json_encode([
            'error' => false,
            'msg' => '穿搭日期已移除',
            'data' => [
                'calendar_date' => $input['calendar_date']
            ]
        ]);
        
    } else {
        echo json_encode([
            'error' => true,
            'msg' => '不支持的操作类型'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '保存穿搭日期失败: ' . $e->getMessage()
    ]);
} 