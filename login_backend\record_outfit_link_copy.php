<?php
/**
 * Record Outfit Link Copy API
 * 
 * Records when a user copies a purchase link from a recommended outfit
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * POST Parameters (JSON):
 * - outfit_id: ID of the recommended outfit
 * - item_id: ID of the outfit item being copied
 * 
 * Response:
 * {
 *   "error": false,
 *   "msg": "Successfully recorded link copy"
 * }
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => 'Method not allowed']);
    exit;
}

// Check if Authorization header is present
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => 'Authorization header is required']);
    exit;
}

// Get token from Authorization header
$token = $_SERVER['HTTP_AUTHORIZATION'];

// Verify token
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => 'Invalid or expired token']);
    exit;
}

// Get user ID from token data
$userId = $tokenData['sub'];

// Get request data
$jsonData = file_get_contents('php://input');
$data = json_decode($jsonData, true);

// Validate request data
if (!$data || !isset($data['outfit_id']) || !isset($data['item_id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => 'Missing required parameters']);
    exit;
}

$outfitId = $data['outfit_id'];
$itemId = $data['item_id'];

// Validate outfit_id and item_id
if (!is_numeric($outfitId) || !is_numeric($itemId)) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => 'Invalid parameters']);
    exit;
}

// Connect to database
$db = new Database();
$conn = $db->getConnection();

// Begin transaction
$conn->beginTransaction();

try {
    // Update outfit copy_link_count
    $stmt = $conn->prepare("UPDATE recommended_outfits SET copy_link_count = copy_link_count + 1 WHERE id = :outfit_id");
    $stmt->bindParam(':outfit_id', $outfitId);
    $stmt->execute();
    
    // Update item copy_link_count (if such field exists)
    $stmt = $conn->prepare("UPDATE recommended_outfit_items SET copy_link_count = copy_link_count + 1 WHERE id = :item_id");
    $stmt->bindParam(':item_id', $itemId);
    $stmt->execute();
    
    // Log this action in a dedicated table if needed
    $stmt = $conn->prepare("INSERT INTO outfit_link_copies (user_id, outfit_id, item_id, created_at) VALUES (:user_id, :outfit_id, :item_id, NOW())");
    $stmt->bindParam(':user_id', $userId);
    $stmt->bindParam(':outfit_id', $outfitId);
    $stmt->bindParam(':item_id', $itemId);
    
    // Ignore if the table doesn't exist yet
    try {
        $stmt->execute();
    } catch (PDOException $e) {
        // Silently fail if table doesn't exist
    }
    
    // Commit transaction
    $conn->commit();
    
    // Return success response
    echo json_encode([
        'error' => false,
        'msg' => 'Successfully recorded link copy'
    ]);
} catch (PDOException $e) {
    // Rollback transaction on error
    $conn->rollBack();
    
    http_response_code(500);
    echo json_encode(['error' => true, 'msg' => 'Database error: ' . $e->getMessage()]);
}
?> 