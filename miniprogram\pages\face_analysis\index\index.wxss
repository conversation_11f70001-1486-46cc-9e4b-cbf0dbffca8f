.container {
  background-color: #fff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header {
  position: relative;
  height: 220rpx;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #333333 0%, #666666 100%);
}

.header-content {
  position: relative;
  z-index: 1;
  padding: 40rpx 40rpx;
  color: #fff;
}

.title {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 30rpx;
  padding-bottom: 120rpx; /* 添加底部内边距，避免内容被固定按钮遮挡 */
}

.section {
  margin-bottom: 50rpx;
}

.section-title {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  color: #333;
}

/* 特点列表 */
.feature-list {
  background: #f8f8f8;
  border-radius: 16rpx;
  overflow: hidden;
}

.feature-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.feature-info {
  flex: 1;
}

.feature-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  color: #333;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 步骤列表 */
.step-list {
  background: #f8f8f8;
  border-radius: 16rpx;
  overflow: hidden;
}

.step-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.step-item:last-child {
  border-bottom: none;
}

.step-number {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background-color: #333333;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.step-info {
  flex: 1;
}

.step-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  color: #333;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 按钮区域 */
.btn-area {
  margin-top: 50rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 固定底部按钮区域 */
.fixed-btn-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.primary-btn {
  background-color: #000;
  color: #fff;
  border-radius: 8rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
  text-align: center;
  border: none;
}

.secondary-btn {
  background-color: #f0f0f0;
  color: #333;
  border-radius: 8rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
  text-align: center;
  border: none;
} 