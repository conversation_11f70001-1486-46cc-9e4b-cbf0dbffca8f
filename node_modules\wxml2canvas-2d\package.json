{"name": "wxml2canvas-2d", "version": "1.3.5", "description": "基于微信小程序 2D Canvas 的画布组件，根据给定 WXML 结构以及 CSS 样式快速转换成 Canvas 元素", "main": "miniprogram_dist/index.js", "miniprogram": "miniprogram_dist", "scripts": {"dev": "gulp dev --dev", "clean-dev": "gulp clean --dev", "clean": "gulp clean", "build": "gulp"}, "keywords": ["wxml2canvas", "canvas", "miniprogram"], "files": ["miniprogram_dist"], "author": "chris<PERSON>", "license": "MIT", "repository": "https://github.com/ChrisChan13/wxml2canvas-2d.git", "devDependencies": {"eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-import": "^2.24.2", "gulp": "^4.0.2", "gulp-clean": "^0.4.0"}}