<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>衣物详情 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* 衣物详情页面样式 */
        .back-link {
            display: inline-flex;
            align-items: center;
            color: #1890ff;
            margin-bottom: 15px;
            text-decoration: none;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 10px;
        }
        
        .clothing-details {
            display: flex;
            margin-bottom: 20px;
        }
        
        .clothing-image-container {
            width: 200px;
            height: 200px;
            overflow: hidden;
            border-radius: 4px;
            margin-right: 20px;
            border: 1px solid #f0f0f0;
        }
        
        .clothing-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .clothing-info {
            flex: 1;
        }
        
        .clothing-name {
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        
        .clothing-id {
            color: #999;
            margin-bottom: 15px;
        }
        
        .clothing-meta {
            margin-bottom: 15px;
        }
        
        .meta-item {
            margin-bottom: 8px;
        }
        
        .meta-label {
            display: inline-block;
            width: 100px;
            color: #666;
        }
        
        .tag-list {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 15px;
        }
        
        .tag {
            background-color: #f5f5f5;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
            color: #666;
        }
        
        .category-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            background-color: #e6f7ff;
            color: #1890ff;
        }
        
        .clothing-actions {
            margin-top: 20px;
        }
        
        .clothing-action-btn {
            padding: 6px 15px;
            border-radius: 4px;
            border: none;
            margin-right: 10px;
            cursor: pointer;
            color: white;
            font-size: 14px;
        }
        
        .edit-btn {
            background-color: #faad14;
        }
        
        .edit-btn:hover {
            background-color: #ffc53d;
        }
        
        .delete-btn {
            background-color: #f5222d;
        }
        
        .delete-btn:hover {
            background-color: #ff4d4f;
        }
        
        .description-container {
            margin-bottom: 20px;
        }
        
        .description-content {
            background-color: #fafafa;
            padding: 15px;
            border-radius: 4px;
        }
        
        .description-item {
            margin-bottom: 8px;
        }
        
        .description-label {
            display: inline-block;
            width: 120px;
            color: #666;
            font-weight: 500;
        }
        
        .user-info-section {
            background-color: #fafafa;
            padding: 15px;
            border-radius: 4px;
        }
        
        .user-info-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
            object-fit: cover;
        }
        
        .user-name {
            font-size: 16px;
            font-weight: 500;
        }
        
        .user-meta {
            margin-bottom: 15px;
        }
        
        .user-meta-item {
            margin-bottom: 5px;
        }
        
        .view-btn {
            background-color: #1890ff;
        }
        
        .view-btn:hover {
            background-color: #40a9ff;
        }
        
        .empty-text {
            color: #999;
            text-align: center;
            padding: 20px 0;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>衣物详情</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="card">
                <a href="clothing_list.html" class="back-link">
                    &lt; 返回衣物列表
                </a>
                
                <div id="clothingDetailContainer">
                    <div class="empty-text">加载中...</div>
                </div>
            </div>
            
            <div class="card" id="descriptionContainer">
                <h3 class="section-title">详细属性</h3>
                <div class="description-content">
                    <div class="empty-text">加载中...</div>
                </div>
            </div>
            
            <div class="card" id="userInfoContainer">
                <div class="empty-text">加载中...</div>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/clothing_details.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为clothing
            Sidebar.init('clothing');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化衣物详情
            ClothingDetails.init();
        });
    </script>
</body>
</html> 