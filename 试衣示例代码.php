<?php
session_start();
header('Content-Type: application/json');
date_default_timezone_set('Asia/Shanghai');

// 检查是否已加载配置文件
if (!function_exists('checkUserTries')) {
    // 如果配置文件不存在，创建一个简单的默认配置
    if (!file_exists('config.php')) {
        // 定义默认的广告图片地址
        define('AD_IMAGE_URL', 'https://www.alidog.cn/images/cyhy.png');
        // 创建一个简单的文件存储方式来替代数据库
        function checkUserTries() {
            $ip = $_SERVER['REMOTE_ADDR'];
            $userAgent = md5($_SERVER['HTTP_USER_AGENT']);
            $today = date('Y-m-d');
            $userKey = md5($ip . $userAgent . $today);
            
            $logFile = 'user_tries.log';
            if (!file_exists($logFile)) {
                return true;
            }
            
            // 使用文件锁确保并发安全
            $fp = fopen($logFile, 'r');
            if (!$fp) {
                throw new Exception("无法读取用户记录文件");
            }
            
            if (flock($fp, LOCK_SH)) {  // 获取共享锁
                $logs = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                flock($fp, LOCK_UN);    // 释放锁
                fclose($fp);
                
                foreach ($logs as $log) {
                    $parts = explode('|', $log);
                    if (count($parts) !== 3) continue;
                    
                    list($key, $count, $date) = $parts;
                    if ($key === $userKey && $date === $today && $count >= 1) {
                        return false;
                    }
                }
            } else {
                fclose($fp);
                throw new Exception("无法锁定用户记录文件");
            }
            return true;
        }
        
        function recordUserTry() {
            $ip = $_SERVER['REMOTE_ADDR'];
            $userAgent = md5($_SERVER['HTTP_USER_AGENT']);
            $today = date('Y-m-d');
            $userKey = md5($ip . $userAgent . $today);
            
            $logFile = 'user_tries.log';
            $found = false;
            $logs = [];
            
            // 使用文件锁确保并发安全
            $fp = fopen($logFile, 'c+');
            if (!$fp) {
                throw new Exception("无法打开用户记录文件");
            }
            
            if (flock($fp, LOCK_EX)) {  // 获取排他锁
                $logs = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES) ?: [];
                
                foreach ($logs as $i => $log) {
                    $parts = explode('|', $log);
                    if (count($parts) !== 3) continue;
                    
                    list($key, $count, $date) = $parts;
                    if ($key === $userKey && $date === $today) {
                        $logs[$i] = $key . '|' . ($count + 1) . '|' . $date;
                        $found = true;
                        break;
                    }
                }
                
                if (!$found) {
                    $logs[] = $userKey . '|1|' . $today;
                }
                
                // 清理旧记录（保留最近7天的记录）
                $logs = array_filter($logs, function($log) {
                    $parts = explode('|', $log);
                    if (count($parts) !== 3) return false;
                    $date = $parts[2];
                    return strtotime($date) >= strtotime('-7 days');
                });
                
                // 截断文件并写入新内容
                ftruncate($fp, 0);
                rewind($fp);
                fwrite($fp, implode(PHP_EOL, $logs) . PHP_EOL);
                flock($fp, LOCK_UN);    // 释放锁
            } else {
                fclose($fp);
                throw new Exception("无法锁定用户记录文件");
            }
            fclose($fp);
        }
    } else {
        require_once 'config.php';
    }
}

// 检查用户是否可以试穿
if (function_exists('checkUserTries')) {
    if (!checkUserTries()) {
        echo json_encode([
            "error" => "limit_exceeded",
            "message" => "您今日的试穿次数已用完，请明天再来！",
            "ad_image" => defined('AD_IMAGE_URL') ? AD_IMAGE_URL : "https://www.alidog.cn/imgges/cyhy.jpg"
        ]);
        exit;
    }

    // 记录这次试穿
    if (function_exists('recordUserTry')) {
        recordUserTry();
    }
}

// 定义存储路径
$img_dir = 'img/';
$newimg_dir = 'newimg/';
if (!is_dir($img_dir)) {
    mkdir($img_dir, 0755, true);
}
if (!is_dir($newimg_dir)) {
    mkdir($newimg_dir, 0755, true);
}

// 后端存储敏感数据（API 密钥等）
$API_KEY = "sk-6dcb9653ba6d48d9905223257fff4c3a";
$base_url = "https://dashscope.aliyuncs.com/api/v1";

// 上传文件函数
function uploadFile($file, $dir) {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception("文件上传错误: " . ($file['name'] ?? '未知文件'));
    }
    
    // 检查文件类型
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
    if (!in_array($file['type'], $allowed_types)) {
        throw new Exception("不支持的文件类型: " . $file['type']);
    }
    
    // 检查文件大小（限制为5MB）
    if ($file['size'] > 5 * 1024 * 1024) {
        throw new Exception("文件大小超过限制");
    }
    
    $tmp_name = $file["tmp_name"];
    $filename = uniqid() . '_' . preg_replace('/[^a-zA-Z0-9.]/', '_', basename($file["name"]));
    $destination = $dir . $filename;
    
    if (!is_dir($dir)) {
        if (!mkdir($dir, 0755, true)) {
            throw new Exception("无法创建目录: " . $dir);
        }
    }
    
    if (!is_writable($dir)) {
        throw new Exception("目录无写入权限: " . $dir);
    }
    
    if (!move_uploaded_file($tmp_name, $destination)) {
        throw new Exception("文件上传失败: " . $file["name"]);
    }
    
    // 返回公开可访问的 URL
    return "http://www.alidog.cn/" . $destination;
}

// 处理上传
try {
    $person_url = uploadFile($_FILES["person_image"], $img_dir);
    $top_url = uploadFile($_FILES["top_image"], $img_dir);
    $bottom_url = "";
    if(isset($_FILES["bottom_image"]) && $_FILES["bottom_image"]["error"] === UPLOAD_ERR_OK) {
        $bottom_url = uploadFile($_FILES["bottom_image"], $img_dir);
    }
} catch (Exception $e) {
    echo json_encode(["error" => $e->getMessage()]);
    exit;
}

// 提交换衣任务函数
function submit_task($base_url, $api_key, $person_url, $top_url, $bottom_url = "", $resolution = -1) {
    $endpoint = $base_url . "/services/aigc/image2image/image-synthesis";
    $payload = array(
        "model" => "aitryon",
        "input" => array(
            "person_image_url" => $person_url,
            "top_garment_url" => $top_url,
            "bottom_garment_url" => $bottom_url
        ),
        "parameters" => array(
            "resolution" => $resolution,
            "restore_face" => true
        )
    );
    $json_payload = json_encode($payload);
    $ch = curl_init($endpoint);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($ch, CURLOPT_POSTFIELDS, $json_payload);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    $headers = array(
        "Authorization: Bearer " . $api_key,
        "Content-Type: application/json",
        "X-DashScope-Async: enable"
    );
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    if(curl_errno($ch)){
        throw new Exception("API请求失败: " . curl_error($ch));
    }
    curl_close($ch);
    $response_data = json_decode($response, true);
    if(!$response_data || !isset($response_data["output"]) || !isset($response_data["output"]["task_id"])) {
        throw new Exception("API响应错误或缺少task_id字段");
    }
    return $response_data["output"]["task_id"];
}

// 轮询获取任务结果函数
function get_task_result($base_url, $api_key, $task_id, $timeout = 600, $retry_interval = 5, $max_retries = 3) {
    $task_endpoint = $base_url . "/tasks/" . $task_id;
    $start_time = time();
    $retry_count = 0;
    while ((time() - $start_time) < $timeout && $retry_count < $max_retries) {
        $ch = curl_init($task_endpoint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        $headers = array(
            "Authorization: Bearer " . $api_key
        );
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $response = curl_exec($ch);
        if(curl_errno($ch)){
            $retry_count++;
            curl_close($ch);
            sleep($retry_interval);
            continue;
        }
        curl_close($ch);
        $response_data = json_decode($response, true);
        if(!$response_data || !isset($response_data["output"])) {
            throw new Exception("无效的任务响应格式");
        }
        $task_status = isset($response_data["output"]["task_status"]) ? $response_data["output"]["task_status"] : "";
        if($task_status === "SUCCEEDED") {
            $image_url = "";
            if(isset($response_data["output"]["results"]["image_url"]) && $response_data["output"]["results"]["image_url"]) {
                $image_url = $response_data["output"]["results"]["image_url"];
            } elseif(isset($response_data["output"]["image_url"]) && $response_data["output"]["image_url"]) {
                $image_url = $response_data["output"]["image_url"];
            }
            if(!$image_url) {
                throw new Exception("结果中缺少image_url字段");
            }
            return $image_url;
        }
        if($task_status === "FAILED" || $task_status === "UNKNOWN") {
            $error_msg = isset($response_data["message"]) ? $response_data["message"] : "未知错误";
            throw new Exception("任务失败: " . $error_msg);
        }
        sleep($retry_interval);
    }
    throw new Exception("任务处理超时");
}

// 调用接口提交任务并轮询获取结果
try {
    $task_id = submit_task($base_url, $API_KEY, $person_url, $top_url, $bottom_url);
    $result_image_url = get_task_result($base_url, $API_KEY, $task_id);
} catch (Exception $e) {
    echo json_encode(["error" => $e->getMessage()]);
    exit;
}

// 下载返回的图片到 newimg 目录
function downloadImage($url, $dir) {
    $img_data = file_get_contents($url);
    if($img_data === false) {
        throw new Exception("下载图片失败");
    }
    $filename = uniqid() . '.jpg';
    $filepath = $dir . $filename;
    if(file_put_contents($filepath, $img_data) === false) {
        throw new Exception("保存图片失败");
    }
    return $filepath;
}

try {
    $downloaded_image_path = downloadImage($result_image_url, $newimg_dir);
} catch (Exception $e) {
    echo json_encode(["error" => $e->getMessage()]);
    exit;
}

// 将结果图片路径存入 Session，供结果页使用
$_SESSION['result_image'] = $downloaded_image_path;
// 返回跳转 URL
echo json_encode(["redirectUrl" => "result.php"]);
