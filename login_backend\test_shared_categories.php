<?php
/**
 * 测试共享数据源下的分类显示
 * 验证系统分类和自定义分类的显示逻辑
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "=== 共享数据源分类显示测试 ===\n";
    echo "当前用户ID: $userId\n\n";
    
    // 1. 检查用户所在的圈子
    echo "1. 用户所在的圈子:\n";
    $stmt = $conn->prepare("
        SELECT cm.circle_id, cm.status, cm.role, c.name as circle_name
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id AND cm.status = 'active'
        ORDER BY cm.joined_at DESC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $userCircles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($userCircles)) {
        echo "用户不在任何活跃圈子中\n";
        exit;
    }
    
    foreach ($userCircles as $circle) {
        echo "- 圈子ID: {$circle['circle_id']}, 名称: {$circle['circle_name']}, 角色: {$circle['role']}\n";
    }
    echo "\n";
    
    $circleId = $userCircles[0]['circle_id'];
    
    // 2. 检查当前用户的系统分类
    echo "2. 当前用户的系统分类:\n";
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.code, c.sort_order
        FROM clothing_categories c
        WHERE c.user_id = :user_id AND c.is_system = 1
        ORDER BY c.sort_order ASC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $userSystemCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "系统分类数量: " . count($userSystemCategories) . "\n";
    foreach ($userSystemCategories as $cat) {
        echo "- {$cat['name']} (code: {$cat['code']}, ID: {$cat['id']})\n";
    }
    echo "\n";
    
    // 3. 检查圈子中其他用户的自定义分类
    echo "3. 圈子中其他用户的自定义分类:\n";
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.code, c.user_id, c.circle_id, u.nickname as creator_nickname
        FROM clothing_categories c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.is_system = 0 AND c.user_id != :user_id AND c.circle_id = :circle_id
        ORDER BY c.sort_order ASC, c.created_at ASC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->execute();
    $othersCustomCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "其他用户自定义分类数量: " . count($othersCustomCategories) . "\n";
    foreach ($othersCustomCategories as $cat) {
        echo "- {$cat['name']} (code: {$cat['code']}, 创建者: {$cat['creator_nickname']}, ID: {$cat['id']})\n";
    }
    echo "\n";
    
    // 4. 测试shared数据源的分类API查询
    echo "4. 测试shared数据源分类API查询:\n";
    $stmt = $conn->prepare("
        SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
               u.nickname as creator_nickname,
               CASE 
                   WHEN c.is_system = 1 THEN 'system'
                   WHEN c.circle_id IS NULL THEN 'personal' 
                   ELSE 'shared' 
               END as data_source
        FROM clothing_categories c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE (
            -- 当前用户的系统分类（用于显示系统分类下的共享衣物）
            (c.is_system = 1 AND c.user_id = :user_id) OR
            -- 其他用户的自定义分类（已同步到圈子的）
            (c.is_system = 0 AND c.user_id != :user_id AND c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'))
        )
        ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $sharedCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "shared数据源查询结果数量: " . count($sharedCategories) . "\n";
    $systemCount = 0;
    $customCount = 0;
    
    foreach ($sharedCategories as $cat) {
        if ($cat['is_system']) {
            $systemCount++;
            echo "- [系统] {$cat['name']} (code: {$cat['code']}, data_source: {$cat['data_source']})\n";
        } else {
            $customCount++;
            echo "- [自定义] {$cat['name']} (code: {$cat['code']}, 创建者: {$cat['creator_nickname']}, data_source: {$cat['data_source']})\n";
        }
    }
    
    echo "统计: 系统分类 $systemCount 个，自定义分类 $customCount 个\n\n";
    
    // 5. 检查每个系统分类下的衣物数量
    echo "5. 检查系统分类下的共享衣物数量:\n";
    foreach ($userSystemCategories as $cat) {
        // 统计该系统分类下的共享衣物数量
        $stmt = $conn->prepare("
            SELECT COUNT(*) as count
            FROM clothes cl
            WHERE cl.category = :category_code 
              AND cl.circle_id IS NOT NULL 
              AND cl.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
        ");
        $stmt->bindParam(':category_code', $cat['code'], PDO::PARAM_STR);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $clothesCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        echo "- {$cat['name']} (code: {$cat['code']}): $clothesCount 件共享衣物\n";
    }
    echo "\n";
    
    // 6. 检查自定义分类下的衣物数量
    echo "6. 检查自定义分类下的共享衣物数量:\n";
    foreach ($othersCustomCategories as $cat) {
        // 统计该自定义分类下的衣物数量
        $stmt = $conn->prepare("
            SELECT COUNT(*) as count
            FROM clothes cl
            WHERE cl.category = :category_code 
              AND cl.circle_id = :circle_id
        ");
        $stmt->bindParam(':category_code', $cat['code'], PDO::PARAM_STR);
        $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
        $stmt->execute();
        $clothesCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        echo "- {$cat['name']} (code: {$cat['code']}, 创建者: {$cat['creator_nickname']}): $clothesCount 件衣物\n";
    }
    echo "\n";
    
    // 7. 对比测试 - all数据源
    echo "7. 对比测试 - all数据源分类查询:\n";
    $stmt = $conn->prepare("
        SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
               u.nickname as creator_nickname,
               CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
        FROM clothing_categories c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE (
            -- 当前用户的系统分类（每个用户都有自己的系统分类副本）
            (c.is_system = 1 AND c.user_id = :user_id) OR
            -- 所有自定义分类：个人的 + 圈子共享的
            (c.is_system = 0 AND ((c.user_id = :user_id AND c.circle_id IS NULL) OR
             (c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'))))
        )
        ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $allCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "all数据源查询结果数量: " . count($allCategories) . "\n";
    $allSystemCount = 0;
    $allPersonalCount = 0;
    $allSharedCount = 0;
    
    foreach ($allCategories as $cat) {
        if ($cat['is_system']) {
            $allSystemCount++;
        } elseif ($cat['data_source'] === 'personal') {
            $allPersonalCount++;
        } else {
            $allSharedCount++;
        }
    }
    
    echo "统计: 系统分类 $allSystemCount 个，个人自定义分类 $allPersonalCount 个，共享自定义分类 $allSharedCount 个\n\n";
    
    // 8. 总结
    echo "8. 测试总结:\n";
    echo "✅ shared数据源分类显示验证:\n";
    echo "- 系统分类: $systemCount 个 " . ($systemCount > 0 ? "✅ 正确显示" : "❌ 缺失") . "\n";
    echo "- 其他用户自定义分类: $customCount 个 " . ($customCount >= 0 ? "✅ 正确显示" : "❌ 异常") . "\n";
    echo "\n";
    echo "✅ all数据源对比验证:\n";
    echo "- 系统分类: $allSystemCount 个\n";
    echo "- 个人自定义分类: $allPersonalCount 个\n";
    echo "- 共享自定义分类: $allSharedCount 个\n";
    echo "\n";
    
    if ($systemCount > 0) {
        echo "🎉 修复成功！shared数据源正确显示了系统分类和其他用户的自定义分类\n";
    } else {
        echo "⚠️ 系统分类缺失，可能需要检查用户的系统分类初始化\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
