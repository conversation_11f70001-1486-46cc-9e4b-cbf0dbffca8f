// pages/outfits/categories/edit.js
const app = getApp();

Page({

    /**
     * 页面的初始数据
     */
    data: {
        id: null,
        name: '',
        description: '',
        isDefault: false,
        nameLength: 0,
        descriptionLength: 0,
        submitting: false
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        const categoryId = options.id;
        
        // 如果有分类ID，加载分类数据
        if (categoryId) {
            this.loadCategoryData(categoryId);
        }
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    },

    // 加载分类数据
    loadCategoryData: function(categoryId) {
        wx.showLoading({
            title: '加载中...',
        });
        
        wx.request({
            url: `${app.globalData.apiBaseUrl}/get_outfit_categories.php`,
            method: 'GET',
            header: {
                'Authorization': app.globalData.token
            },
            data: {
                category_id: categoryId
            },
            success: (res) => {
                wx.hideLoading();
                
                if (res.statusCode === 200 && res.data.success && res.data.data && res.data.data.length > 0) {
                    const category = res.data.data[0];
                    
                    this.setData({
                        id: category.id,
                        name: category.name || '',
                        description: category.description || '',
                        isDefault: category.is_default == 1,
                        nameLength: (category.name || '').length,
                        descriptionLength: (category.description || '').length
                    });
                } else {
                    wx.showToast({
                        title: '未找到分类数据',
                        icon: 'none'
                    });
                    
                    // 延迟返回
                    setTimeout(() => {
                        wx.navigateBack();
                    }, 1500);
                }
            },
            fail: (err) => {
                wx.hideLoading();
                wx.showToast({
                    title: '加载失败',
                    icon: 'none'
                });
                console.error('加载分类数据失败:', err);
            }
        });
    },
    
    // 输入分类名称
    onNameInput: function(e) {
        const name = e.detail.value;
        this.setData({
            name: name,
            nameLength: name.length
        });
    },
    
    // 输入分类描述
    onDescriptionInput: function(e) {
        const description = e.detail.value;
        this.setData({
            description: description,
            descriptionLength: description.length
        });
    },
    
    // 切换是否设为默认
    toggleDefault: function() {
        this.setData({
            isDefault: !this.data.isDefault
        });
    },
    
    // 保存分类
    saveCategory: function() {
        // 验证名称是否为空
        if (!this.data.name.trim()) {
            wx.showToast({
                title: '请输入分类名称',
                icon: 'none'
            });
            return;
        }
        
        // 设置提交状态，防止重复提交
        this.setData({ submitting: true });
        
        // 构建分类数据
        const categoryData = {
            name: this.data.name.trim(),
            description: this.data.description.trim(),
            is_default: this.data.isDefault ? 1 : 0
        };
        
        // 如果是编辑模式，添加ID
        if (this.data.id) {
            categoryData.id = this.data.id;
        }
        
        // 发送请求保存分类
        wx.request({
            url: `${app.globalData.apiBaseUrl}/save_outfit_category.php`,
            method: 'POST',
            header: {
                'Authorization': app.globalData.token,
                'Content-Type': 'application/json'
            },
            data: categoryData,
            success: (res) => {
                if (res.statusCode === 200 && res.data.success) {
                    // 设置全局刷新标记
                    app.globalData.needRefreshOutfitCategories = true;
                    
                    wx.showToast({
                        title: this.data.id ? '更新成功' : '创建成功',
                        icon: 'success'
                    });
                    
                    // 延迟返回
                    setTimeout(() => {
                        wx.navigateBack();
                    }, 1500);
                } else {
                    wx.showToast({
                        title: res.data.msg || '保存失败',
                        icon: 'none'
                    });
                    this.setData({ submitting: false });
                }
            },
            fail: (err) => {
                console.error('保存分类失败:', err);
                wx.showToast({
                    title: '网络错误',
                    icon: 'none'
                });
                this.setData({ submitting: false });
            }
        });
    },
    
    // 取消编辑
    cancel: function() {
        wx.navigateBack();
    }
})