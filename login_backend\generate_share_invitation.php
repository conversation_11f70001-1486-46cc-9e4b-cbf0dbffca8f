<?php
// 生成分享邀请信息API
// 模块3：邀请分享模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查找用户所在的圈子
    $findCircleSql = "SELECT c.id, c.name, c.description, c.invitation_code, c.creator_id, 
                             c.member_count, c.created_at, cm.role
                      FROM circle_members cm 
                      JOIN outfit_circles c ON cm.circle_id = c.id 
                      WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
    $findCircleStmt = $conn->prepare($findCircleSql);
    $findCircleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $findCircleStmt->execute();
    
    $userCircle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$userCircle) {
        echo json_encode([
            'status' => 'error',
            'message' => '您还没有加入任何圈子'
        ]);
        exit;
    }
    
    // 只有创建者可以邀请好友
    if ($userCircle['role'] !== 'creator') {
        echo json_encode([
            'status' => 'error',
            'message' => '只有圈子创建者可以邀请好友'
        ]);
        exit;
    }
    
    // 获取用户信息
    $getUserSql = "SELECT nickname, avatar_url FROM users WHERE id = :user_id";
    $getUserStmt = $conn->prepare($getUserSql);
    $getUserStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $getUserStmt->execute();
    $userInfo = $getUserStmt->fetch(PDO::FETCH_ASSOC);
    
    // 获取圈子统计信息
    $getStatsSql = "SELECT 
                        COUNT(DISTINCT cm.user_id) as member_count,
                        COUNT(DISTINCT w.id) as wardrobe_count,
                        COUNT(DISTINCT cl.id) as clothes_count,
                        COUNT(DISTINCT o.id) as outfit_count
                    FROM circle_members cm
                    LEFT JOIN wardrobes w ON cm.circle_id = w.circle_id
                    LEFT JOIN clothes cl ON cm.circle_id = cl.circle_id  
                    LEFT JOIN outfits o ON cm.circle_id = o.circle_id
                    WHERE cm.circle_id = :circle_id AND cm.status = 'active'";
    $getStatsStmt = $conn->prepare($getStatsSql);
    $getStatsStmt->bindParam(':circle_id', $userCircle['id'], PDO::PARAM_INT);
    $getStatsStmt->execute();
    $stats = $getStatsStmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'status' => 'success',
        'data' => [
            'circle' => [
                'id' => $userCircle['id'],
                'name' => $userCircle['name'],
                'description' => $userCircle['description'],
                'invitation_code' => $userCircle['invitation_code'],
                'member_count' => $userCircle['member_count'],
                'created_at' => $userCircle['created_at'],
                'stats' => [
                    'member_count' => $stats['member_count'] ?? 0,
                    'wardrobe_count' => $stats['wardrobe_count'] ?? 0,
                    'clothes_count' => $stats['clothes_count'] ?? 0,
                    'outfit_count' => $stats['outfit_count'] ?? 0
                ]
            ],
            'inviter' => [
                'id' => $userId,
                'nickname' => $userInfo['nickname'] ?? '未知用户',
                'avatar_url' => $userInfo['avatar_url']
            ],
            'share_params' => [
                'path' => "/pages/outfit_circle/invitation/invitation?code={$userCircle['invitation_code']}&inviter={$userId}",
                'title' => ($userInfo['nickname'] ?? '朋友') . "邀请您加入「{$userCircle['name']}」穿搭圈子",
                'imageUrl' => 'https://images.alidog.cn/logo/xxfx.png'
            ]
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '生成分享邀请失败：' . $e->getMessage()
    ]);
}
?>
