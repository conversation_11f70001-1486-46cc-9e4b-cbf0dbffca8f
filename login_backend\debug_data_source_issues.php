<?php
/**
 * 调试数据源问题的脚本
 * 用于排查衣橱筛选和分类加载问题
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['user_id'];

// 获取测试参数
$dataSource = isset($_GET['data_source']) ? $_GET['data_source'] : 'all';
$wardrobeId = isset($_GET['wardrobe_id']) ? intval($_GET['wardrobe_id']) : null;

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    $result = [];
    $result['test_params'] = [
        'user_id' => $userId,
        'data_source' => $dataSource,
        'wardrobe_id' => $wardrobeId
    ];
    
    // 1. 检查用户所在的圈子
    $stmt = $conn->prepare("
        SELECT cm.circle_id, c.name as circle_name, cm.status, cm.role
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $circles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['user_circles'] = $circles;
    
    // 2. 测试分类查询
    if ($dataSource === 'shared') {
        $sql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                       u.nickname as creator_nickname,
                       CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                FROM clothing_categories c
                LEFT JOIN users u ON c.user_id = u.id
                WHERE c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
                ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC";
    } elseif ($dataSource === 'all') {
        $sql = "SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
                       u.nickname as creator_nickname,
                       CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                FROM clothing_categories c
                LEFT JOIN users u ON c.user_id = u.id
                WHERE ((c.is_system = 1 AND c.user_id = :user_id) OR (c.is_system = 0 AND c.user_id = :user_id AND c.circle_id IS NULL)) OR
                      (c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'))
                ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC";
    } else {
        $sql = "SELECT id, user_id, name, code, is_system, sort_order, created_at 
                FROM clothing_categories 
                WHERE (is_system = 1 AND user_id = :user_id) OR (is_system = 0 AND user_id = :user_id)
                ORDER BY sort_order ASC, is_system DESC, created_at ASC";
    }
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['categories'] = $categories;
    $result['categories_count'] = count($categories);
    
    // 3. 测试衣橱查询
    if ($dataSource === 'shared') {
        $sql = "SELECT DISTINCT w.id, w.name, w.description, w.sort_order, w.is_default, w.created_at, w.updated_at,
                       u.nickname as creator_nickname,
                       CASE WHEN w.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                FROM wardrobes w
                LEFT JOIN users u ON w.user_id = u.id
                WHERE w.circle_id IS NOT NULL AND w.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
                ORDER BY w.is_default DESC, w.sort_order ASC, w.name ASC";
    } elseif ($dataSource === 'all') {
        $sql = "SELECT DISTINCT w.id, w.name, w.description, w.sort_order, w.is_default, w.created_at, w.updated_at,
                       u.nickname as creator_nickname,
                       CASE WHEN w.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                FROM wardrobes w
                LEFT JOIN users u ON w.user_id = u.id
                WHERE (w.user_id = :user_id AND w.circle_id IS NULL) OR 
                      (w.circle_id IS NOT NULL AND w.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'))
                ORDER BY w.is_default DESC, w.sort_order ASC, w.name ASC";
    } else {
        $sql = "SELECT w.id, w.name, w.description, w.sort_order, w.is_default, w.created_at, w.updated_at
                FROM wardrobes w
                WHERE w.user_id = :user_id
                ORDER BY w.is_default DESC, w.sort_order ASC, w.name ASC";
    }
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $wardrobes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['wardrobes'] = $wardrobes;
    $result['wardrobes_count'] = count($wardrobes);
    
    // 4. 测试衣物查询（如果指定了衣橱ID）
    if ($wardrobeId) {
        if ($dataSource === 'shared') {
            $sql = "SELECT c.id, c.name, c.category, c.user_id, c.wardrobe_id, c.circle_id,
                           u.nickname as creator_nickname,
                           CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                    FROM clothes c 
                    LEFT JOIN users u ON c.user_id = u.id 
                    WHERE c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
                          AND c.wardrobe_id = :wardrobe_id
                    ORDER BY c.created_at DESC
                    LIMIT 10";
        } elseif ($dataSource === 'all') {
            $sql = "SELECT c.id, c.name, c.category, c.user_id, c.wardrobe_id, c.circle_id,
                           u.nickname as creator_nickname,
                           CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                    FROM clothes c 
                    LEFT JOIN users u ON c.user_id = u.id 
                    WHERE ((c.user_id = :user_id AND c.circle_id IS NULL) OR 
                           (c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')))
                          AND c.wardrobe_id = :wardrobe_id
                    ORDER BY c.created_at DESC
                    LIMIT 10";
        } else {
            $sql = "SELECT id, name, category, user_id, wardrobe_id, circle_id
                    FROM clothes 
                    WHERE user_id = :user_id AND wardrobe_id = :wardrobe_id
                    ORDER BY created_at DESC
                    LIMIT 10";
        }
        
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindParam(':wardrobe_id', $wardrobeId, PDO::PARAM_INT);
        $stmt->execute();
        $clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result['clothes_in_wardrobe'] = $clothes;
        $result['clothes_count'] = count($clothes);
    }
    
    // 5. 测试所有衣物查询（不限制衣橱）
    if ($dataSource === 'shared') {
        $sql = "SELECT c.id, c.name, c.category, c.user_id, c.wardrobe_id, c.circle_id,
                       u.nickname as creator_nickname,
                       CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                FROM clothes c 
                LEFT JOIN users u ON c.user_id = u.id 
                WHERE c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')
                ORDER BY c.created_at DESC
                LIMIT 5";
    } elseif ($dataSource === 'all') {
        $sql = "SELECT c.id, c.name, c.category, c.user_id, c.wardrobe_id, c.circle_id,
                       u.nickname as creator_nickname,
                       CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                FROM clothes c 
                LEFT JOIN users u ON c.user_id = u.id 
                WHERE (c.user_id = :user_id AND c.circle_id IS NULL) OR 
                      (c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'))
                ORDER BY c.created_at DESC
                LIMIT 5";
    } else {
        $sql = "SELECT id, name, category, user_id, wardrobe_id, circle_id
                FROM clothes 
                WHERE user_id = :user_id
                ORDER BY created_at DESC
                LIMIT 5";
    }
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $allClothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['all_clothes_sample'] = $allClothes;
    $result['all_clothes_count'] = count($allClothes);
    
    echo json_encode([
        'status' => 'success',
        'data' => $result
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '查询失败: ' . $e->getMessage()
    ]);
}
?>
