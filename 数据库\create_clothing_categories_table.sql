-- 创建衣物分类表
CREATE TABLE `clothing_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID，NULL表示系统默认分类',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `code` varchar(50) NOT NULL COMMENT '分类代码，用于兼容现有数据',
  `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为系统默认分类',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_code` (`code`),
  KEY `idx_is_system` (`is_system`),
  CONSTRAINT `clothing_categories_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='衣物分类表';

-- 插入系统默认分类
INSERT INTO `clothing_categories` (`user_id`, `name`, `code`, `is_system`, `sort_order`) VALUES
(NULL, '上装', 'tops', 1, 1),
(NULL, '下装', 'pants', 1, 2),
(NULL, '裙装', 'skirts', 1, 3),
(NULL, '外套', 'coats', 1, 4),
(NULL, '鞋子', 'shoes', 1, 5),
(NULL, '包包', 'bags', 1, 6),
(NULL, '配饰', 'accessories', 1, 7); 