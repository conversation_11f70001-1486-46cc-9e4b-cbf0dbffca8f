<?php
/**
 * 更新用户试衣次数API
 * 
 * 这个API允许管理员设置指定用户的试衣次数
 * 
 * 请求参数:
 * - user_id: 用户ID
 * - free_try_on_count: 设置的免费试衣次数 (可选)
 * - paid_try_on_count: 设置的付费试衣次数 (可选)
 * - admin_token: 管理员令牌
 * 
 * 注意: 如果某个计数参数未提供，则不会更新该字段
 * 
 * 返回:
 * {
 *   "error": false,
 *   "msg": "试衣次数更新成功",
 *   "data": {
 *     "user_id": 123,
 *     "free_try_on_count": 1,
 *     "paid_try_on_count": 5,
 *     "nickname": "用户昵称"
 *   }
 * }
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 只允许POST方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => '不支持的请求方法'
    ]);
    exit;
}

// 获取管理员Token
$adminToken = null;
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $adminToken = $_SERVER['HTTP_AUTHORIZATION'];
    // 如果有Bearer前缀，去掉它
    if (strpos($adminToken, 'Bearer ') === 0) {
        $adminToken = substr($adminToken, 7);
    }
}

// 如果没有管理员Token，也从POST数据中尝试获取
if (!$adminToken && isset($_POST['admin_token'])) {
    $adminToken = $_POST['admin_token'];
}

// 验证管理员Token
$auth = new Auth();
$adminData = $auth->verifyAdminToken($adminToken);

if (!$adminData) {
    echo json_encode([
        'error' => true,
        'msg' => '无效的管理员令牌'
    ]);
    exit;
}

// 获取请求参数
$userId = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
$freeTryOnCount = isset($_POST['free_try_on_count']) ? (int)$_POST['free_try_on_count'] : null;
$paidTryOnCount = isset($_POST['paid_try_on_count']) ? (int)$_POST['paid_try_on_count'] : null;

// 向后兼容: 如果使用旧参数try_on_count，则视为paid_try_on_count
if (isset($_POST['try_on_count']) && $paidTryOnCount === null) {
    $paidTryOnCount = (int)$_POST['try_on_count'];
}

// 验证参数
if ($userId <= 0) {
    echo json_encode([
        'error' => true,
        'msg' => '无效的用户ID'
    ]);
    exit;
}

// 至少要更新一种次数
if ($freeTryOnCount === null && $paidTryOnCount === null) {
    echo json_encode([
        'error' => true,
        'msg' => '请至少提供一种要更新的试衣次数'
    ]);
    exit;
}

// 验证次数是否为负
if (($freeTryOnCount !== null && $freeTryOnCount < 0) || ($paidTryOnCount !== null && $paidTryOnCount < 0)) {
    echo json_encode([
        'error' => true,
        'msg' => '试衣次数不能为负数'
    ]);
    exit;
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查用户是否存在，并获取当前的次数
    $userStmt = $conn->prepare("SELECT id, nickname, free_try_on_count, paid_try_on_count FROM users WHERE id = :user_id");
    $userStmt->execute(['user_id' => $userId]);
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode([
            'error' => true,
            'msg' => '用户不存在'
        ]);
        exit;
    }
    
    // 存储更新前的值，用于日志
    $oldFreeTryOnCount = $user['free_try_on_count'];
    $oldPaidTryOnCount = $user['paid_try_on_count'];
    
    // 准备SQL更新语句，根据提供的参数动态构建
    $sql = "UPDATE users SET ";
    $params = [];
    
    if ($freeTryOnCount !== null) {
        $sql .= "free_try_on_count = :free_try_on_count";
        $params['free_try_on_count'] = $freeTryOnCount;
        // 如果同时更新付费次数，添加逗号
        if ($paidTryOnCount !== null) {
            $sql .= ", ";
        }
    }
    
    if ($paidTryOnCount !== null) {
        $sql .= "paid_try_on_count = :paid_try_on_count";
        $params['paid_try_on_count'] = $paidTryOnCount;
    }
    
    $sql .= " WHERE id = :user_id";
    $params['user_id'] = $userId;
    
    // 更新用户试衣次数
    $updateStmt = $conn->prepare($sql);
    $updateStmt->execute($params);
    
    // 如果参数为null，使用原值
    if ($freeTryOnCount === null) {
        $freeTryOnCount = $oldFreeTryOnCount;
    }
    if ($paidTryOnCount === null) {
        $paidTryOnCount = $oldPaidTryOnCount;
    }
    
    // 记录操作日志
    $logStmt = $conn->prepare("
        INSERT INTO admin_operation_log (
            admin_id, 
            operation_type, 
            target_table, 
            target_id, 
            details, 
            created_at
        ) VALUES (
            :admin_id,
            'update_try_on_count',
            'users',
            :user_id,
            :details,
            NOW()
        )
    ");
    
    // 如果admin_operation_log表存在就记录日志
    try {
        $logStmt->execute([
            'admin_id' => $adminData['admin_id'],
            'user_id' => $userId,
            'details' => json_encode([
                'old_free_count' => $oldFreeTryOnCount,
                'new_free_count' => $freeTryOnCount,
                'old_paid_count' => $oldPaidTryOnCount,
                'new_paid_count' => $paidTryOnCount
            ])
        ]);
    } catch (PDOException $e) {
        // 日志表可能不存在，忽略错误
    }
    
    // 返回成功响应
    echo json_encode([
        'error' => false,
        'msg' => '试衣次数更新成功',
        'data' => [
            'user_id' => $userId,
            'free_try_on_count' => $freeTryOnCount,
            'paid_try_on_count' => $paidTryOnCount,
            'nickname' => $user['nickname']
        ]
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'error' => true,
        'msg' => '数据库操作失败: ' . $e->getMessage()
    ]);
    exit;
} 