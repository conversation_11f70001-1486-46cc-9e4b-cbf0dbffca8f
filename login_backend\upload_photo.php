<?php
/**
 * Upload Photo API
 * 
 * Uploads a photo for the user
 * 
 * Headers:
 * - Authorization: <token>
 * 
 * POST Parameters:
 * - image: The image file to upload (multipart/form-data)
 * - type: Photo type (optional, one of: full, half, other)
 * - description: Photo description (optional)
 * 
 * Response:
 * {
 *   "error": false,
 *   "data": {
 *     "id": 1,
 *     "image_url": "https://example.com/photo.jpg",
 *     "type": "full",
 *     "description": "Beach day",
 *     "created_at": "2023-03-31 12:00:00"
 *   }
 * }
 */

require_once 'config.php';
// 不要直接引入verify_token.php，因为它会直接输出JSON
// require_once 'verify_token.php'; 
require_once 'auth.php';
require_once 'db.php';
require_once '../vendor/autoload.php'; // 引入阿里云OSS SDK
require_once 'oss_helper.php';

// 引入OSS命名空间
use OSS\OssClient;
use OSS\Core\OssException;

// Set response content type
header('Content-Type: application/json');

// Handle CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if Authorization header exists
if (!isset($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'error' => true,
        'msg' => 'Authorization header is required'
    ]);
    exit;
}

// Check if the request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'error' => true,
        'msg' => 'Only POST method is allowed'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];

// Verify token
$auth = new Auth();
$tokenData = $auth->verifyToken($token);
if (!$tokenData) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid or expired token'
    ]);
    exit;
}

// Get user ID from token data
$userId = $tokenData['sub'];

// Check if file was uploaded (support both 'image' and 'photo' field names)
$fileField = isset($_FILES['image']) ? 'image' : (isset($_FILES['photo']) ? 'photo' : null);

if (!$fileField || $_FILES[$fileField]['error'] !== UPLOAD_ERR_OK) {
    echo json_encode([
        'error' => true,
        'msg' => 'No image file uploaded or upload error'
    ]);
    exit;
}

// Validate file type
$allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
$fileType = $_FILES[$fileField]['type'];

if (!in_array($fileType, $allowedTypes)) {
    echo json_encode([
        'error' => true,
        'msg' => 'Invalid file type. Only JPEG, PNG, GIF, and WEBP are allowed.'
    ]);
    exit;
}

// Get optional parameters
$type = isset($_POST['type']) ? $_POST['type'] : 'other';
$description = isset($_POST['description']) ? $_POST['description'] : '';

// Validate type
$validTypes = ['full', 'half', 'other'];
if (!in_array($type, $validTypes)) {
    $type = 'other';
}

// 生成文件扩展名
$extension = pathinfo($_FILES[$fileField]['name'], PATHINFO_EXTENSION);
if (empty($extension)) {
    // 根据MIME类型确定扩展名
    switch ($fileType) {
        case 'image/jpeg':
            $extension = 'jpg';
            break;
        case 'image/png':
            $extension = 'png';
            break;
        case 'image/gif':
            $extension = 'gif';
            break;
        case 'image/webp':
            $extension = 'webp';
            break;
        default:
            $extension = 'jpg';
    }
}

// 生成唯一的OSS文件名
$ossFilename = 'photo_' . $userId . '_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
$ossKey = OSS_PATH_PHOTOS . $ossFilename;

try {
    // 初始化OSS辅助类
    $ossHelper = new OssHelper();
    
    // 上传文件到OSS
    $uploadResult = $ossHelper->uploadFile($_FILES[$fileField]['tmp_name'], $ossKey);
    
    if (!$uploadResult['success']) {
        echo json_encode([
            'error' => true,
            'msg' => '上传到OSS失败: ' . $uploadResult['error']
        ]);
        exit;
    }
    
    // 获取OSS图片URL
    $imageUrl = $uploadResult['url'];
    
    // 数据库操作
    $db = new Database();
    $conn = $db->getConnection();

    // Get current timestamp
    $now = date('Y-m-d H:i:s');

    // Insert photo record
    $sql = "INSERT INTO photos (user_id, image_url, type, description, created_at, updated_at) 
            VALUES (:user_id, :image_url, :type, :description, :created_at, :updated_at)";
    
    $stmt = $conn->prepare($sql);
    
    $params = [
        'user_id' => $userId,
        'image_url' => $imageUrl,
        'type' => $type,
        'description' => $description,
        'created_at' => $now,
        'updated_at' => $now
    ];
    
    // 记录SQL和参数
    error_log("SQL: $sql");
    error_log("参数: " . json_encode($params));
    
    $stmt->execute($params);

    // Get the new photo ID
    $photoId = $conn->lastInsertId();
    error_log("成功插入照片数据，新ID: $photoId");

    // Return the newly created photo data
    echo json_encode([
        'error' => false,
        'data' => [
            'id' => $photoId,
            'image_url' => $imageUrl,
            'type' => $type,
            'description' => $description,
            'created_at' => $now
        ]
    ]);
} catch (PDOException $e) {
    // 详细记录错误信息
    $errorMessage = $e->getMessage();
    $errorCode = $e->getCode();
    error_log("数据库错误码: $errorCode");
    error_log("数据库错误信息: $errorMessage");
    
    // 返回通用错误，但带有更多信息
    echo json_encode([
        'error' => true,
        'msg' => "数据库错误: $errorCode - " . substr($errorMessage, 0, 100)
    ]);
} catch (Exception $e) {
    // 其他错误
    error_log("上传照片失败: " . $e->getMessage());
    echo json_encode([
        'error' => true,
        'msg' => '上传照片失败: ' . $e->getMessage()
    ]);
}
?>
