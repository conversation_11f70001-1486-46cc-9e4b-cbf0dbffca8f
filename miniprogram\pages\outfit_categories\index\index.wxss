.container {
  padding: 0 30rpx;
}

/* 标题栏 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
}

.add-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.add-btn image {
  width: 40rpx;
  height: 40rpx;
}

/* 分类列表 */
.category-list {
  margin-bottom: 30rpx;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.category-info {
  flex: 1;
  margin-right: 10rpx;
}

.category-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.default-tag {
  font-size: 20rpx;
  color: #fff;
  background-color: #333;
  padding: 4rpx 10rpx;
  border-radius: 8rpx;
  margin-left: 16rpx;
}

.category-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.category-count {
  font-size: 24rpx;
  color: #999;
}

.category-actions {
  display: flex;
  align-items: center;
  min-width: 120rpx;
  justify-content: flex-end;
}

.edit-btn, .delete-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10rpx;
}

.edit-btn image, .delete-btn image {
  width: 36rpx;
  height: 36rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.create-btn {
  padding: 20rpx 60rpx;
  background-color: #333;
  color: #fff;
  border-radius: 30rpx;
  font-size: 28rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 28rpx;
} 