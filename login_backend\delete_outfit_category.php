<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入配置和辅助函数
require_once 'config.php';
require_once 'db.php';
require_once 'auth.php';

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// 验证用户Token
$auth = new Auth();
$token = null;

// 从请求头或查询参数中获取token
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 移除可能存在的Bearer前缀
    $token = str_replace('Bearer ', '', $token);
} elseif (isset($_GET['token'])) {
    $token = $_GET['token'];
}

if (!$token) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'No token provided']);
    exit;
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Invalid or expired token']);
    exit;
}

$user_id = $payload['sub'];

// 获取POST数据
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// 检查必要参数
if (!isset($data['id']) || empty($data['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Category ID is required']);
    exit;
}

$category_id = (int)$data['id'];

try {
    // 获取数据库连接
    $db = new Database();
    $pdo = $db->getConnection();
    
    // 检查分类是否存在且属于当前用户
    $stmt = $pdo->prepare("SELECT * FROM outfit_categories WHERE id = :id AND user_id = :user_id");
    $stmt->bindParam(':id', $category_id, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    
    $category = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$category) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'Category not found']);
        exit;
    }
    
    // 检查是否为默认分类
    if ($category['is_default'] == 1) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Cannot delete default category']);
        exit;
    }
    
    // 获取默认分类ID
    $stmt = $pdo->prepare("SELECT id FROM outfit_categories WHERE user_id = :user_id AND is_default = 1");
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    $default_category = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 如果没有默认分类，创建一个
    $default_category_id = null;
    if (!$default_category) {
        $stmt = $pdo->prepare("
            INSERT INTO outfit_categories (user_id, name, description, is_default, created_at)
            VALUES (:user_id, '默认分类', '自动创建的默认分类', 1, NOW())
        ");
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        $default_category_id = $pdo->lastInsertId();
    } else {
        $default_category_id = $default_category['id'];
    }
    
    // 开始事务
    $pdo->beginTransaction();
    
    // 将该分类下的所有穿搭移动到默认分类
    $stmt = $pdo->prepare("
        UPDATE outfits
        SET category_id = :default_category_id
        WHERE user_id = :user_id AND category_id = :category_id
    ");
    $stmt->bindParam(':default_category_id', $default_category_id, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->bindParam(':category_id', $category_id, PDO::PARAM_INT);
    $stmt->execute();
    
    // 删除分类
    $stmt = $pdo->prepare("DELETE FROM outfit_categories WHERE id = :id AND user_id = :user_id");
    $stmt->bindParam(':id', $category_id, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    
    // 提交事务
    $pdo->commit();
    
    // 记录日志
    error_log("User ID: $user_id deleted outfit category ID: $category_id");
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => 'Outfit category deleted successfully'
    ]);
    
} catch (PDOException $e) {
    // 回滚事务
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Database error in delete_outfit_category.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database error', 'message' => 'An error occurred while deleting outfit category']);
} catch (Exception $e) {
    // 回滚事务
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("General error in delete_outfit_category.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Server error', 'message' => 'An unexpected error occurred']);
} 