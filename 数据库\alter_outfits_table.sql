-- Add category_id field to outfits table
ALTER TABLE outfits ADD COLUMN category_id INT NULL AFTER user_id;

-- Add foreign key constraint to link outfits to outfit_categories
ALTER TABLE outfits ADD CONSTRAINT fk_outfit_category 
FOREIGN KEY (category_id) REFERENCES outfit_categories(id) 
ON DELETE SET NULL;

-- Create index on category_id for faster lookups
CREATE INDEX idx_outfit_category_id ON outfits(category_id); 