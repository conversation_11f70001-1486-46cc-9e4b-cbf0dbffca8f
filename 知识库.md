连接MySQL并排查问题

登录MySQL:
mysql -u root -p  59b93187ca17e677

选择数据库
USE cyyg;

查看现有索引：
SHOW INDEX FROM clothes;
SHOW INDEX FROM clothing_categories;

firewall-cmd --permanent --zone=public --add-rich-rule='rule family="ipv4" source address="*************/32" port protocol="tcp" port="3306" accept'

执行MySQL命令
SHOW PROCESSLIST;

如果MySQL负载过高，也可以通过系统命令查看:
top
free -m
iostat -xm 5

检查慢查询日志配置
SHOW VARIABLES LIKE '%slow%';
SHOW VARIABLES LIKE 'long_query_time';

检查连接数和缓存使用情况
SHOW GLOBAL STATUS LIKE 'Threads_%';
SHOW GLOBAL STATUS LIKE 'Max_used_connections';
SHOW VARIABLES LIKE 'max_connections';
SHOW GLOBAL STATUS LIKE 'Innodb_buffer_pool%';

检查表状态
SHOW TABLE STATUS;

检查内存使用情况
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
SHOW VARIABLES LIKE 'key_buffer_size';

退出MySQL后检查系统资源
exit;

top
free -m
iostat -x

检查慢查询日志内容：
cat /www/server/data/mysql-slow.log | tail -n 100

选择您的业务数据库并检查表状态：
USE 您的数据库名;
SHOW TABLE STATUS;

检查磁盘I/O状态：
iostat -xm 5

增加InnoDB缓冲池大小：
SET GLOBAL innodb_buffer_pool_size = 536870912;  # 增加到512MB

mysqldump -uroot -p59b93187ca17e677 cyyg > cyyg_backup_$(date +%Y%m%d).sql

实际处理特定用户的穿搭：php migrate_outfit_images_to_cdn.php --user=1545  测试 php migrate_outfit_images_to_cdn.php --dry-run --user=1545
实际处理所有用户数据：php migrate_outfit_images_to_cdn.php --all  测试 php migrate_outfit_images_to_cdn.php --dry-run --all