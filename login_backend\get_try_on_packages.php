<?php
/**
 * 获取试衣套餐列表API
 * 
 * 返回可购买的试衣次数套餐列表
 * 
 * 请求方法：GET
 * 
 * 返回：
 * {
 *   "error": false,
 *   "data": [
 *     {
 *       "id": 1,
 *       "count": 1,
 *       "price": 1,
 *       "description": "1次试衣"
 *     },
 *     ...
 *   ]
 * }
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'wx_pay_helper.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 验证用户身份
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    // 如果token包含Bearer前缀，去掉它
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
    
    $auth = new Auth();
    $payload = $auth->verifyToken($token);
    
    // 模拟用户不能购买
    if ($payload && isset($payload['openid']) && $payload['openid'] === 'mock_openid') {
        echo json_encode([
            'error' => true,
            'msg' => '体验账号不支持购买试衣次数，请登录后再试'
        ]);
        exit;
    }
}

try {
    // 获取套餐列表
    $wxPayHelper = new WxPayHelper();
    $packages = $wxPayHelper->getAllPackages();
    
    // 返回套餐列表
    echo json_encode([
        'error' => false,
        'data' => $packages
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'msg' => '获取套餐列表失败: ' . $e->getMessage()
    ]);
} 