<?php
// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => '不支持的请求方法']);
    exit;
}

// 验证管理员身份
$authHeader = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
if (empty($authHeader) || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '未提供授权Token']);
    exit;
}

$token = $matches[1];
if (empty($token)) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => '无效的Token']);
    exit;
}

// 引入配置文件
$configFile = __DIR__ . '/config.php';

// 检查配置文件是否存在
if (!file_exists($configFile)) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error', 
        'message' => '系统配置文件不存在',
        'debug' => '配置文件路径: ' . $configFile
    ]);
    exit;
}

require_once $configFile;
require_once 'auth.php';

// 检查配置文件中是否定义了必要的常量
$requiredConstants = [
    'DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASS',
    'WX_APPID', 'WX_SECRET', 'API_DOMAIN',
    'ADMIN_SECRET_KEY'
];

$missingConstants = [];
foreach ($requiredConstants as $constant) {
    if (!defined($constant)) {
        $missingConstants[] = $constant;
    }
}

if (!empty($missingConstants)) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error', 
        'message' => '系统配置文件不完整，缺少必要的常量',
        'debug' => '缺少的常量: ' . implode(', ', $missingConstants)
    ]);
    exit;
}

// 验证Token
try {
    $auth = new Auth();
    $payload = $auth->verifyAdminToken($token);
    if (!$payload) {
        throw new Exception('管理员身份验证失败');
    }
} catch (Exception $e) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
    exit;
}

/**
 * 部分隐藏敏感信息
 * @param string $value 敏感值
 * @param int $visibleChars 可见字符数
 * @return string 处理后的值
 */
function maskSensitiveData($value, $visibleChars = 3) {
    // 对于空值不处理
    if (empty($value)) {
        return '';
    }
    
    // 短字符串处理
    $length = strlen($value);
    if ($length <= $visibleChars) {
        return str_repeat('*', $length);
    }
    
    // 普通字符串部分隐藏
    if ($length < 12) {
        $prefix = substr($value, 0, $visibleChars);
        return $prefix . str_repeat('*', $length - $visibleChars);
    }
    
    // 长字符串处理
    $prefix = substr($value, 0, $visibleChars);
    $suffix = substr($value, -2);
    return $prefix . str_repeat('*', $length - $visibleChars - 2) . $suffix;
}

// 构建要返回的配置数据，部分隐藏敏感信息
$configData = [
    'database' => [
        'DB_HOST' => DB_HOST,
        'DB_NAME' => DB_NAME,
        'DB_USER' => DB_USER,
        'DB_PASS' => maskSensitiveData(DB_PASS, 2)
    ],
    'wechat' => [
        'WX_APPID' => WX_APPID,
        'WX_SECRET' => maskSensitiveData(WX_SECRET, 3),
        'API_DOMAIN' => API_DOMAIN,
        'WX_TOKEN' => maskSensitiveData(WX_TOKEN, 3),
        'WX_ENCODING_AES_KEY' => maskSensitiveData(WX_ENCODING_AES_KEY, 6)
    ],
    'admin' => [
        'ADMIN_SECRET_KEY' => maskSensitiveData(ADMIN_SECRET_KEY, 4)
    ],
    'aliyun' => [
        'ALIYUN_ACCESS_KEY_ID' => maskSensitiveData(ALIYUN_ACCESS_KEY_ID, 4),
        'ALIYUN_ACCESS_KEY_SECRET' => maskSensitiveData(ALIYUN_ACCESS_KEY_SECRET, 4),
        'ALIYUN_OUTFIT_API_KEY' => maskSensitiveData(ALIYUN_OUTFIT_API_KEY, 4),
        'ALIYUN_OSS_ENDPOINT' => ALIYUN_OSS_ENDPOINT,
        'ALIYUN_OSS_BUCKET' => ALIYUN_OSS_BUCKET,
        'ALIYUN_OSS_BUCKET_DOMAIN' => ALIYUN_OSS_BUCKET_DOMAIN
    ],
    'storage_paths' => [
        'OSS_PATH_CLOTHES' => OSS_PATH_CLOTHES,
        'OSS_PATH_PHOTOS' => OSS_PATH_PHOTOS,
        'OSS_PATH_TRY_ON' => OSS_PATH_TRY_ON
    ]
];

// 返回配置数据
echo json_encode([
    'status' => 'success',
    'message' => '获取系统设置成功',
    'data' => $configData
]); 