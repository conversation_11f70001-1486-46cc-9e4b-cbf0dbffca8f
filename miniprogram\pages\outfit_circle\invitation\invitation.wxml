<!-- 圈子邀请页面 -->
<!-- 模块3：邀请分享模块 -->

<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 邀请内容 -->
  <view class="invitation-container" wx:if="{{!loading && circle}}">
    <!-- 邀请头部 -->
    <view class="invitation-header">
      <view class="invitation-icon">
        <image class="circle-icon" src="/images/outfit.png" mode="aspectFit"></image>
      </view>
      
      <view class="invitation-title">
        <text class="title-text">邀请您加入穿搭圈子</text>
        <text class="circle-name">{{circle.name}}</text>
      </view>
      
      <!-- 邀请者信息 -->
      <view class="inviter-info" wx:if="{{inviter}}">
        <image class="inviter-avatar" src="{{inviter.avatar_url || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        <text class="inviter-name">{{inviter.nickname}}</text>
        <text class="inviter-label">邀请您</text>
      </view>
    </view>

    <!-- 圈子信息卡片 -->
    <view class="circle-card">
      <view class="circle-info">
        <view class="circle-name-section">
          <text class="circle-name-large">{{circle.name}}</text>
          <view class="circle-code-section">
            <text class="code-label">邀请码：</text>
            <text class="code-value">{{circle.invitation_code}}</text>
            <view class="copy-btn" bindtap="copyInvitationCode">
              <image class="copy-icon" src="/images/share.png" mode="aspectFit"></image>
            </view>
          </view>
        </view>
        
        <text class="circle-desc" wx:if="{{circle.description}}">{{circle.description}}</text>
        
        <!-- 圈子统计 -->
        <view class="circle-stats">
          <view class="stat-item">
            <text class="stat-number">{{circle.stats.member_count}}</text>
            <text class="stat-label">成员</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{circle.stats.wardrobe_count}}</text>
            <text class="stat-label">衣橱</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{circle.stats.clothes_count}}</text>
            <text class="stat-label">衣物</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{circle.stats.outfit_count}}</text>
            <text class="stat-label">穿搭</text>
          </view>
        </view>
      </view>
      
      <!-- 创建者信息 -->
      <view class="creator-info">
        <image class="creator-avatar" src="{{circle.creator.avatar_url || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="creator-details">
          <text class="creator-name">{{circle.creator.nickname}}</text>
          <text class="creator-role">圈子创建者</text>
        </view>
        <text class="create-time">{{circle.created_at}}</text>
      </view>
    </view>

    <!-- 功能说明 -->
    <view class="features-section">
      <text class="features-title">加入圈子后您将与成员共享：</text>
      <view class="features-list">
        <view class="feature-item">
          <text class="feature-icon">👗</text>
          <text class="feature-text">衣橱分类和衣物信息</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">✨</text>
          <text class="feature-text">穿搭分类和穿搭信息</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">🏷️</text>
          <text class="feature-text">衣物标签和搭配记录</text>
        </view>
      </view>
    </view>

    <!-- 用户状态提示 -->
    <view class="status-section" wx:if="{{statusMessage}}">
      <view class="status-message status-{{userStatus}}">
        <text class="status-text">{{statusMessage}}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <!-- 可以加入的状态 -->
      <view class="action-buttons" wx:if="{{userStatus === 'can_join' || userStatus === 'can_rejoin'}}">
        <button class="refuse-btn" bindtap="refuseToJoin">拒绝加入</button>
        <button class="agree-btn" bindtap="agreeToJoin" loading="{{submitting}}">
          {{submitting ? '加入中...' : '同意加入'}}
        </button>
      </view>
      
      <!-- 需要登录的状态 -->
      <view class="action-buttons" wx:if="{{userStatus === 'unknown'}}">
        <button class="refuse-btn" bindtap="refuseToJoin">暂不加入</button>
        <button class="agree-btn" bindtap="showLogin">登录并加入</button>
      </view>
      
      <!-- 已经是成员的状态 -->
      <view class="action-buttons" wx:if="{{userStatus === 'already_member'}}">
        <button class="single-btn" bindtap="refuseToJoin">返回圈子</button>
      </view>
      
      <!-- 在其他圈子的状态 -->
      <view class="action-buttons" wx:if="{{userStatus === 'in_other_circle'}}">
        <button class="single-btn" bindtap="refuseToJoin">我知道了</button>
      </view>
    </view>
  </view>
</view>

<!-- 登录提示弹框 -->
<view class="modal-mask" wx:if="{{showLoginModal}}" bindtap="hideLoginModal">
  <view class="modal-content" catchtap="stopModalClose">
    <view class="modal-header">
      <text class="modal-title">需要登录</text>
      <view class="modal-close" bindtap="hideLoginModal">×</view>
    </view>
    
    <view class="modal-body">
      <text class="modal-desc">加入圈子需要先登录您的账号</text>
    </view>
    
    <view class="modal-footer">
      <button class="modal-cancel-btn" bindtap="hideLoginModal">取消</button>
      <button class="modal-confirm-btn" bindtap="goToLogin">去登录</button>
    </view>
  </view>
</view>
