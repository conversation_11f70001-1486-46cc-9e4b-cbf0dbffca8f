<view class="container">
  <view class="header">
    <view class="title">打赏作者</view>
    <view class="subtitle">您的鼓励是我们前进的动力</view>
  </view>
  
  <!-- 打赏记录列表 -->
  <view class="donation-list" wx:if="{{donations.length > 0}}">
    <view class="section-title">打赏榜单</view>
    
    <view class="donation-item" wx:for="{{donations}}" wx:key="id">
      <view class="user-info">
        <image class="avatar" src="{{item.avatar_url || '/images/default-avatar.png'}}" binderror="onAvatarError" mode="aspectFill"></image>
        <view class="info-content">
          <view class="nickname">{{item.nickname || '微信用户'}}</view>
          <view class="time">{{item.paid_at || item.created_at}}</view>
        </view>
      </view>
      <view class="amount">¥{{item.amount}}</view>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}" bindtap="loadMoreDonations">
      <view class="load-text">加载更多</view>
    </view>
    
    <view class="no-more" wx:if="{{!hasMore && donations.length > 0}}">
      <view class="no-more-text">没有更多了</view>
    </view>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && donations.length === 0}}">
    <image class="empty-image" src="/images/empty-donation.png" mode="aspectFit"></image>
    <view class="empty-text">暂无打赏记录</view>
    <view class="empty-subtext">成为第一个打赏的人吧~</view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 底部打赏按钮 -->
  <view class="bottom-bar">
    <view class="donation-btn" bindtap="showDonationPopup">打赏支持</view>
  </view>
  
  <!-- 打赏弹窗 -->
  <view class="popup-mask" wx:if="{{showPopup}}" bindtap="hideDonationPopup"></view>
  <view class="popup-content {{showPopup ? 'show' : ''}}">
    <view class="popup-header">
      <view class="popup-title">选择打赏金额</view>
      <view class="popup-close" bindtap="hideDonationPopup">×</view>
    </view>
    
    <view class="amount-options">
      <view class="amount-option {{selectedAmount === 5 ? 'selected' : ''}}" data-amount="5" bindtap="selectAmount">
        <view class="amount-value">¥5</view>
        <view class="amount-desc">一杯奶茶</view>
      </view>
      <view class="amount-option {{selectedAmount === 10 ? 'selected' : ''}}" data-amount="10" bindtap="selectAmount">
        <view class="amount-value">¥10</view>
        <view class="amount-desc">一顿早餐</view>
      </view>
      <view class="amount-option {{selectedAmount === 20 ? 'selected' : ''}}" data-amount="20" bindtap="selectAmount">
        <view class="amount-value">¥20</view>
        <view class="amount-desc">一次午餐</view>
      </view>
      <view class="amount-option {{selectedAmount === 50 ? 'selected' : ''}}" data-amount="50" bindtap="selectAmount">
        <view class="amount-value">¥50</view>
        <view class="amount-desc">一顿晚餐</view>
      </view>
      <view class="amount-option {{isCustomAmount ? 'selected' : ''}}" bindtap="showCustomAmount">
        <view class="amount-value">自定义</view>
        <view class="amount-desc">自由支持</view>
      </view>
    </view>
    
    <!-- 自定义金额输入框 -->
    <view class="custom-amount" wx:if="{{isCustomAmount}}">
      <input class="amount-input" type="digit" placeholder="输入打赏金额(¥)" value="{{customAmountValue}}" bindinput="onCustomAmountInput" />
    </view>
    
    <view class="donation-action-btn {{(selectedAmount > 0 || (isCustomAmount && customAmountValue > 0)) ? '' : 'disabled'}}" bindtap="createDonation">确认打赏</view>
  </view>
</view> 