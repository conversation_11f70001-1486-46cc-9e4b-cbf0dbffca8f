.container {
  position: relative;
  width: 100%;
  height: 100vh;
  background-color: #f8f8f8;
  overflow: hidden;
  overflow-y: auto; /* 允许垂直滚动 */
  padding-bottom: 30px;
  box-sizing: border-box;
}

/* 隐藏默认滚动条 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
  display: none;
}

.month-selector {
  width: 100%;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.month-btn {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.arrow {
  font-size: 18px;
  color: #333;
}

.month-text {
  font-size: 16px;
  font-weight: 500;
  margin: 0 20px;
}

.today-btn {
  position: absolute;
  right: 15px;
  padding: 4px 12px;
  background-color: #f2f2f2;
  border-radius: 15px;
  font-size: 14px;
}

.weekday-header {
  display: flex;
  width: 100%;
  background-color: #ffffff;
  padding: 10px 0;
}

.weekday {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #666;
}

.calendar-grid {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  background-color: #ffffff;
  padding-bottom: 20px;
}

.day-cell {
  width: 14.28%;
  height: 0;
  padding-bottom: 14.28%;
  position: relative;
  box-sizing: border-box;
  border: 0.5px solid #f0f0f0;
}

.current-month {
  background-color: #ffffff;
}

.other-month {
  background-color: #f9f9f9;
}

.today {
  background-color: #fff9e6;
}

.has-outfit {
  background-color: #f0f8ff;
  position: relative;
  overflow: hidden;
}

.has-outfit::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #007aff;
  border-radius: 50%;
  top: 5px;
  right: 5px;
}

.selected {
  background-color: rgba(0, 0, 0, 0.05);
  border: 2px solid #007aff;
}

.day-number {
  position: absolute;
  top: 5px;
  left: 5px;
  font-size: 14px;
  color: #333;
}

.outfit-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  top: 25px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.outfit-thumbnail {
  width: 80%;
  height: 65%;
  border-radius: 4px;
  object-fit: cover;
}

/* 穿搭名称标签 */
.outfit-name-tag {
  font-size: 8px;
  color: #333;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 1px 3px;
  border-radius: 2px;
  margin-bottom: 2px;
  max-width: 90%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}

.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.loading {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 新增：添加穿搭按钮容器 */
.add-outfit-container {
  margin: 15px auto;
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 15px;
  width: 90%;
  max-width: 500px;
}

.selected-date-info {
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 15px;
}

.selected-date-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-right: 10px;
}

.no-outfit-text {
  font-size: 14px;
  color: #999;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.action-btn {
  flex: 1;
  margin: 0 5px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  font-size: 14px;
  font-weight: normal;
  padding: 0;
}

.add-btn {
  background-color: #f5f5f5;
  color: #333;
}

.create-btn {
  background-color: #333;
  color: #fff;
}

/* 修改圆形删除图标样式，缩小并调整位置 */
.delete-icon-container {
  padding: 5px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.delete-icon {
  position: relative;
  width: 20px;
  height: 20px;
  background-color: #888; /* 改为灰色 */
  border-radius: 50%;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  opacity: 0.9; /* 稍微提高不透明度 */
}

.delete-icon-line {
  font-size: 16px;
  color: #fff;
  font-weight: 300;
  line-height: 1;
  margin: 0;
  padding: 0;
  text-align: center;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 移除文字说明 */
.delete-text {
  display: none;
}

/* 穿搭详情容器 - 替代原来的selected-date-outfit */
.outfit-detail-container {
  width: 100%;
  background-color: #fff;
  position: relative;
  overflow: hidden;
  box-shadow: none;
  border-radius: 0;
  margin: 0;
}

/* 隐藏不需要的画布样式 */
.outfit-canvas,
.outfit-view,
.outfit-item,
.empty-canvas {
  display: none;
}

/* 日期头部样式 */
.date-header {
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
}

.header-left {
  flex: 1;
}

.date-text {
  font-size: 14px;
  color: #666;
  margin-right: 10px;
}

.outfit-name {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 4px;
  color: #333;
}

/* 穿搭信息样式 - 优化高度和间距 */
.outfit-info {
  padding: 15px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.outfit-items {
  margin-bottom: 5px;
}

.items-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #333;
}

.items-scroll {
  width: 100%;
  white-space: nowrap;
  overflow-x: auto;
}

.items-container {
  display: inline-flex;
  padding: 5px 0;
}

.item-preview {
  width: 60px;
  height: 80px;
  margin-right: 10px;
  background-color: #f5f5f5;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #eee;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 穿搭选择弹出层样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.popup-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  z-index: 1001;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 60vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 0;
}

.popup-show {
  transform: translateY(0);
}

.popup-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.popup-title {
  font-size: 16px;
  font-weight: 500;
}

.popup-close {
  font-size: 22px;
  color: #999;
  line-height: 1;
}

/* 分类滚动列表样式 */
.categories-scroll-container {
  padding: 0 10px;
  border-bottom: 1px solid #f0f0f0;
}

.categories-scroll {
  height: 44px;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.category-item {
  display: inline-block;
  padding: 6px 15px;
  margin: 0 5px;
  font-size: 14px;
  color: #666;
  position: relative;
  border-radius: 16px;
  background-color: #f5f5f5;
}

.category-active {
  color: #fff;
  background-color: #333;
  font-weight: 500;
}

.popup-content {
  flex: 1;
  overflow: hidden;
}

.popup-hint {
  padding: 30px 0;
  text-align: center;
  color: #999;
  font-size: 14px;
}

.popup-loading {
  padding: 30px 0;
  text-align: center;
  color: #999;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
}

.outfits-scroll-view {
  max-height: calc(60vh - 120px);
  padding-bottom: 20px;
}

.outfit-items-container {
  padding-bottom: 10px;
}

.outfit-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.outfit-preview {
  width: 70px;
  height: 70px;
  background-color: #f9f9f9;
  border-radius: 6px;
  overflow: hidden;
  margin-right: 12px;
  position: relative;
}

/* 在弹出层中不隐藏outfit-canvas */
.popup-container .outfit-canvas {
  display: block;
  width: 100%;
  height: 100%;
  position: relative;
}

.clothing-item {
  position: absolute;
  overflow: hidden;
}

.outfit-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.outfit-date {
  font-size: 12px;
  color: #999;
}

/* 穿搭占位图样式 */
.outfit-placeholder {
  width: 100%;
  height: 100%;
  object-fit: contain;
  opacity: 0.7;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 10px;
  width: 100%;
} 