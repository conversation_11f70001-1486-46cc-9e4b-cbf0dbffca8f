const app = getApp();

Page({
  data: {
    activeTab: 'deals',
    activeCategory: '',
    categories: [],
    outfits: [],
    loading: false,
    loadingMore: false,
    isRefreshing: false,
    error: false,
    errorMsg: '',
    page: 1,
    perPage: 10,
    hasMore: true,
    products: [],
    productsLoading: false,
    isProductsRefreshing: false,
    hasMoreProducts: true,
    productsPage: 1,
    searchKeyword: '',
    taobaoCategories: [],
    activeTaobaoCategory: '86589',
  },

  onLoad: function (options) {
    this.loadOutfitCategories();
    this.loadTaobaoCategories();
    this.loadProducts();
  },

  onShow: function () {
    if (this.data.activeTab === 'deals') {
      if (this.data.products.length === 0) {
        this.loadProducts();
      }
    } else {
      this.loadOutfits();
    }
  },

  onTabChange: function (e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab === this.data.activeTab) return;

    this.setData({ activeTab: tab });

    if (tab === 'deals') {
      if (this.data.products.length === 0) {
        this.loadProducts();
      }
    } else {
      if (this.data.outfits.length === 0) {
        this.loadOutfits();
      }
    }
  },

  loadTaobaoCategories: function () {
    const apiUrl = app.globalData.apiBaseUrl || 'https://cyyg.alidog.cn/login_backend';
    wx.request({
      url: `${apiUrl}/get_taobao_material_categories.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        if (res.statusCode === 200 && !res.data.error) {
          this.setData({
            taobaoCategories: res.data.data || []
          });
          console.log('成功获取淘宝物料分类数据:', res.data.data);
        } else {
          console.error('获取淘宝物料分类失败', res.data);
        }
      },
      fail: (err) => {
        console.error('获取淘宝物料分类失败', err);
      }
    });
  },

  onTaobaoCategoryChange: function (e) {
    const categoryId = e.currentTarget.dataset.id;
    
    console.log('选择淘宝分类:', categoryId);
    
    this.setData({
      activeTaobaoCategory: categoryId,
      products: [],
      productsPage: 1,
      hasMoreProducts: true
    }, () => {
      this.loadProducts(true);
    });
  },

  loadOutfitCategories: function () {
    const apiUrl = app.globalData.apiBaseUrl || 'https://cyyg.alidog.cn/login_backend';
    wx.request({
      url: `${apiUrl}/get_recommended_categories.php`,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        if (res.statusCode === 200 && !res.data.error) {
          this.setData({
            categories: res.data.data || []
          });
          console.log('成功获取推荐穿搭分类数据:', res.data.data);
        } else {
          console.error('获取分类失败', res.data);
        }
      },
      fail: (err) => {
        console.error('获取分类失败', err);
      }
    });
  },

  loadOutfits: function (refresh = false) {
    const apiUrl = app.globalData.apiBaseUrl || 'https://cyyg.alidog.cn/login_backend';
    
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        isRefreshing: true
      });
    }
    
    if (this.data.loading && !refresh) return;
    
    if (!this.data.hasMore && !refresh) return;
    
    this.setData({ loading: true });
    
    const page = this.data.page;
    const categoryId = this.data.activeCategory;
    
    return new Promise((resolve, reject) => {
      let url = `${apiUrl}/get_recommended_outfits.php`;
      let data = { page: page };
      
      if (categoryId && categoryId !== '0' && categoryId !== '') {
        data.category_id = categoryId;
      }

      console.log('发送推荐穿搭列表请求参数:', data);

      wx.request({
        url: url,
        method: 'GET',
        data: data,
        header: {
          'Authorization': app.globalData.token
        },
        success: (res) => {
          if (res.statusCode === 200 && !res.data.error) {
            const newOutfits = this.data.page === 1 ? 
              res.data.data || [] : 
              [...this.data.outfits, ...(res.data.data || [])];
            
            const pagination = res.data.pagination || {};
            const currentPage = pagination.current_page || page;
            const totalPages = pagination.total_pages || 1;
            const hasMore = currentPage < totalPages;
            
            console.log('推荐穿搭数据响应:', res.data);
            console.log('获取到的穿搭数量:', (res.data.data || []).length);
            
            this.setData({
              outfits: newOutfits,
              hasMore: hasMore,
              page: page + 1
            });
            resolve();
          } else {
            this.setData({
              error: true,
              errorMsg: res.data?.msg || '获取推荐穿搭列表失败',
              loading: false
            });
            console.error('获取推荐穿搭列表失败:', res.data);
            reject(new Error('获取推荐穿搭列表失败'));
          }
        },
        fail: (err) => {
          this.setData({
            error: true,
            errorMsg: '网络错误，请重试',
            loading: false
          });
          console.error('获取推荐穿搭列表失败', err);
          reject(err);
        },
        complete: () => {
          this.setData({ 
            loading: false,
            isRefreshing: false
          });
          
          wx.stopPullDownRefresh();
        }
      });
    });
  },

  normalizeProductData: function(product) {
    if (!product) return null;
    
    const normalizeImageUrl = (url) => {
      if (!url) return '';
      if (typeof url === 'object') {
        if (url[0]) return this.ensureHttpPrefix(url[0]);
        return '';
      }
      return this.ensureHttpPrefix(url);
    };
    
    const extractValue = (field) => {
      if (field === undefined || field === null) return '';
      if (typeof field === 'object') {
        if (Array.isArray(field) && field.length > 0) return field[0];
        if (field[0] !== undefined) return field[0];
        const values = Object.values(field);
        if (values.length > 0) return values[0];
      }
      return field;
    };
    
    const normalizePrice = (price) => {
      if (!price) return 0;
      const numPrice = Number(price);
      return isNaN(numPrice) ? 0 : numPrice;
    };
    
    return {
      id: extractValue(product.id) || '',
      title: extractValue(product.title) || '商品标题',
      image_url: normalizeImageUrl(product.image_url) || '',
      original_price: normalizePrice(product.original_price || product.zk_final_price),
      final_price: normalizePrice(product.final_price || product.zk_final_price),
      coupon_amount: normalizePrice(product.coupon_amount),
      coupon_click_url: this.ensureHttpPrefix(product.coupon_click_url || ''),
      item_url: this.ensureHttpPrefix(product.item_url || ''),
      shop_title: extractValue(product.shop_title) || '淘宝店铺',
      volume: extractValue(product.volume) || "0",
      small_images: this.normalizeSmallImages(product.small_images)
    };
  },
  
  normalizeSmallImages: function(smallImages) {
    const result = [];
    
    if (!smallImages) return result;
    
    console.log('处理small_images，原始数据类型:', typeof smallImages);
    
    try {
      if (Array.isArray(smallImages)) {
        smallImages.forEach(url => {
          if (url && typeof url === 'string') {
            result.push(this.ensureHttpPrefix(url));
          }
        });
      } else if (typeof smallImages === 'object') {
        if (smallImages.string && Array.isArray(smallImages.string)) {
          smallImages.string.forEach(url => {
            if (url && typeof url === 'string') {
              result.push(this.ensureHttpPrefix(url));
            }
          });
        } else {
          Object.values(smallImages).forEach(item => {
            if (typeof item === 'string') {
              result.push(this.ensureHttpPrefix(item));
            } else if (Array.isArray(item)) {
              item.forEach(subItem => {
                if (typeof subItem === 'string') {
                  result.push(this.ensureHttpPrefix(subItem));
                }
              });
            }
          });
        }
      }
    } catch (error) {
      console.error('处理small_images时出错:', error);
    }
    
    console.log('处理后的small_images数组:', result, '长度:', result.length);
    return result;
  },
  
  ensureHttpPrefix: function(url) {
    if (!url) return '';
    if (typeof url !== 'string') return '';
    
    if (url.startsWith('//')) {
      return 'https:' + url;
    } else if (!url.startsWith('http')) {
      return 'https://' + url;
    }
    return url;
  },

  loadProducts: function(refresh = false) {
    const apiUrl = app.globalData.apiBaseUrl || 'https://cyyg.alidog.cn/login_backend';
    
    if (refresh) {
      this.setData({
        productsPage: 1,
        hasMoreProducts: true,
        isProductsRefreshing: true
      });
    }
    
    if (this.data.productsLoading && !refresh) return;
    
    if (!this.data.hasMoreProducts && !refresh) return;
    
    this.setData({ productsLoading: true });
    
    const page = this.data.productsPage;
    const keyword = this.data.searchKeyword;
    const materialId = this.data.activeTaobaoCategory;
    
    return new Promise((resolve, reject) => {
      let url = `${apiUrl}/get_taobao_products.php`;
      let data = {
        page: page,
        page_size: 10,
        keyword: keyword
      };
      
      if (materialId) {
        data.material_id = materialId;
      }

      console.log('发送淘宝商品列表请求参数:', data);

      wx.request({
        url: url,
        method: 'GET',
        data: data,
        header: {
          'Authorization': app.globalData.token
        },
        success: (res) => {
          console.log('淘宝商品API响应:', res.data);
          
          if (res.statusCode === 200 && !res.data.error) {
            let newProducts = res.data.data || [];
            
            newProducts = newProducts.map(product => this.normalizeProductData(product));
            
            newProducts = newProducts.filter(product => product && product.id && product.title);
            
            console.log('处理后的商品数据:', newProducts);
            
            const pagination = res.data.pagination || {};
            const currentPage = pagination.current_page || page;
            const totalPages = pagination.total_pages || 0;
            
            let hasMore = newProducts.length > 0;
            
            if (totalPages > 0) {
              hasMore = currentPage < totalPages;
            }
            
            console.log('分页信息:', pagination);
            console.log('当前页:', currentPage, '总页数:', totalPages);
            console.log('新获取商品数量:', newProducts.length);
            console.log('是否还有更多:', hasMore);
            
            this.setData({
              products: refresh ? newProducts : [...this.data.products, ...newProducts],
              hasMoreProducts: hasMore,
              productsPage: page + 1
            });
            resolve();
          } else {
            wx.showToast({
              title: res.data.msg || '获取商品失败',
              icon: 'none'
            });
            reject(new Error('获取商品列表失败'));
          }
        },
        fail: (err) => {
          console.error('请求商品API失败:', err);
          wx.showToast({
            title: '网络错误，请稍后重试',
            icon: 'none'
          });
          reject(err);
        },
        complete: () => {
          this.setData({ 
            productsLoading: false,
            isProductsRefreshing: false
          });
          
          wx.stopPullDownRefresh();
        }
      });
    });
  },

  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  onSearchConfirm: function() {
    this.loadProducts(true);
  },

  onCategoryChange: function (e) {
    const categoryId = e.currentTarget.dataset.name || '';
    
    console.log('选择分类:', categoryId);
    
    this.setData({
      activeCategory: categoryId
    }, () => {
      this.loadOutfits(true);
    });
  },

  goToDetail: function (e) {
    const id = e.currentTarget.dataset.id;
    if (id) {
      wx.navigateTo({
        url: `/pages/recommended_outfits/detail/detail?id=${id}`
      });
    }
  },

  goToProductDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    
    try {
      console.log('准备获取商品链接，ID:', id);
      
      const index = e.currentTarget.dataset.index;
      const product = this.data.products[index];
      
      if (!product) {
        wx.showToast({
          title: '商品信息不存在',
          icon: 'none'
        });
        return;
      }
      
      console.log('找到商品信息，准备获取链接');
      
      this.getProductLink(product.id);
      
      wx.setStorageSync('currentProduct', product);
    } catch (error) {
      console.error('处理过程发生错误:', error);
      wx.showToast({
        title: '系统错误，请稍后重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  getProductLink: function(itemId) {
    const app = getApp();
    
    wx.showLoading({
      title: '获取淘口令中...',
    });
    
    const actualItemId = typeof itemId === 'object' && itemId[0] ? itemId[0] : itemId;
    
    const product = this.data.products.find(p => p.id[0] === actualItemId || p.id === actualItemId);
    
    if (product) {
      console.log('从本地商品列表找到商品信息');
      
      let itemUrl = '';
      if (product.coupon_click_url) {
        itemUrl = product.coupon_click_url;
        if (!itemUrl.startsWith('http')) {
          itemUrl = 'https:' + itemUrl;
        }
        console.log('使用优惠券链接');
      } else if (product.item_url) {
        itemUrl = product.item_url;
        if (!itemUrl.startsWith('http')) {
          itemUrl = 'https:' + itemUrl;
        }
        console.log('使用普通商品链接');
      }
      
      if (itemUrl) {
        this.convertLinkToTpwd(itemUrl, actualItemId);
        return;
      }
    }
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_taobao_products.php`,
      method: 'GET',
      data: {
        item_id: actualItemId,
        id_type: 'item_id'
      },
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        console.log('获取淘宝产品数据:', res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data && res.data.data.length > 0) {
          const item = res.data.data[0];
          const validUrl = item.coupon_click_url || item.url || item.item_url;
          
          if (validUrl) {
            this.convertLinkToTpwd(validUrl, actualItemId);
          } else {
            wx.showToast({
              title: '未找到有效商品链接',
              icon: 'none',
              duration: 2000
            });
          }
        } else {
          this.getOriginalLink(actualItemId);
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('获取淘宝商品失败:', err);
        this.getOriginalLink(actualItemId);
      }
    });
  },
  
  getOriginalLink: function(itemId) {
    const app = getApp();
    
    wx.showLoading({
      title: '重新获取链接...',
    });
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_taobao_link.php`,
      method: 'GET',
      data: {
        item_id: itemId,
        need_tpwd: 1
      },
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          const link = res.data.data.tpwd || res.data.data.model || res.data.data.promotion_url;
          if (link) {
            this.copyUrlToClipboard(link);
          } else {
            wx.showToast({
              title: '获取商品链接失败',
              icon: 'none',
              duration: 2000
            });
          }
        } else {
          wx.showToast({
            title: '获取商品链接失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      }
    });
  },
  
  convertLinkToTpwd: function(url, itemId) {
    const app = getApp();
    
    console.log('开始转换链接为淘口令:', url);
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/convert_to_tpwd.php`,
      method: 'POST',
      data: {
        url: url,
        item_id: itemId,
        text: '好物推荐'
      },
      header: {
        'Content-Type': 'application/json',
        'Authorization': app.globalData.token
      },
      success: (res) => {
        wx.hideLoading();
        console.log('淘口令转换响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          const tpwd = res.data.data.model;
          
          if (tpwd && (tpwd.includes('￥') || tpwd.includes('¥'))) {
            console.log('成功获取到淘口令');
            this.copyUrlToClipboard(tpwd, true);
          } else {
            console.log('未获取到淘口令，使用普通链接');
            this.copyUrlToClipboard(url);
          }
        } else {
          console.error('转换淘口令失败:', res.data);
          this.getOriginalLink(itemId);
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('转换淘口令请求失败:', err);
        this.getOriginalLink(itemId);
      }
    });
  },
  
  copyUrlToClipboard: function(url, isTpwd = false) {
    wx.setClipboardData({
      data: url,
      success: () => {
        let toastMsg = '链接已复制，请打开淘宝APP';
        
        if (isTpwd || url.includes('￥') || url.includes('¥')) {
          toastMsg = '淘口令已复制，请打开淘宝APP粘贴';
        }
        
        wx.showToast({
          title: toastMsg,
          icon: 'none',
          duration: 2500
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制链接失败',
          icon: 'none'
        });
      }
    });
  },

  copyProductLink: function(itemId, couponUrl) {
    const app = getApp();
    
    wx.showLoading({
      title: '获取链接中...',
    });
    
    const actualItemId = typeof itemId === 'object' && itemId[0] ? itemId[0] : itemId;
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_taobao_link.php`,
      method: 'GET',
      data: {
        item_id: actualItemId,
        coupon_url: couponUrl
      },
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        if (res.statusCode === 200 && !res.data.error) {
          const link = res.data.data.promotion_url;
          wx.setClipboardData({
            data: link,
            success: () => {
              wx.hideLoading();
              wx.showToast({
                title: '优惠链接已复制，请前往淘宝打开',
                icon: 'none',
                duration: 2000
              });
            }
          });
        } else {
          wx.setClipboardData({
            data: couponUrl,
            success: () => {
              wx.hideLoading();
              wx.showToast({
                title: '链接已复制，请前往淘宝打开',
                icon: 'none',
                duration: 2000
              });
            }
          });
        }
      },
      fail: () => {
        wx.setClipboardData({
          data: couponUrl,
          success: () => {
            wx.hideLoading();
            wx.showToast({
              title: '链接已复制，请前往淘宝打开',
              icon: 'none',
              duration: 2000
            });
          }
        });
      }
    });
  },

  onPullDownRefresh: function () {
    if (this.data.activeTab === 'deals') {
      this.loadProducts(true);
    } else {
      this.loadOutfits(true);
    }
  },

  onReachBottom: function () {
    console.log('触发页面触底事件');
    console.log('当前标签：', this.data.activeTab);
    console.log('商品加载状态：', this.data.productsLoading);
    console.log('是否有更多商品：', this.data.hasMoreProducts);
    console.log('当前商品数量：', this.data.products.length);
    
    if (this.data.activeTab === 'deals') {
      console.log('准备加载更多商品数据');
      
      if (!this.data.productsLoading) {
        console.log('开始加载更多商品数据，当前页码：', this.data.productsPage);
        
        this.setData({
          hasMoreProducts: true
        }, () => {
          this.loadProducts();
        });
      } else {
        console.log('正在加载中，跳过加载');
      }
    } else {
      console.log('准备加载更多穿搭数据');
      if (this.data.hasMore && !this.data.loading) {
        console.log('开始加载更多穿搭数据，当前页码：', this.data.page);
        this.loadOutfits();
      } else {
        console.log('不满足加载条件，跳过加载');
      }
    }
  },

  onShareAppMessage: function () {
    return {
      title: this.data.activeTab === 'deals' ? '优惠好物推荐' : '精选穿搭推荐',
      path: '/pages/recommended_outfits/index/index'
    };
  },

  goToTaobaoDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;
    const product = this.data.products[index];
    
    if (product) {
      wx.setStorageSync('currentProduct', product);
      
      wx.navigateTo({
        url: `/pages/taobao/detail/index?id=${id}`
      });
    } else {
      wx.showToast({
        title: '商品信息不存在',
        icon: 'none'
      });
    }
  },

  goToTaobaoList: function() {
    wx.navigateTo({
      url: '/pages/taobao/list/index'
    });
  }
}); 