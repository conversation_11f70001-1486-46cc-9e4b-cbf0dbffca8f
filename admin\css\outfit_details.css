/* 穿搭详情页样式 */
.outfit-container {
    padding: 20px;
}

.outfit-main {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30px;
    gap: 30px;
}

.outfit-images-slider {
    flex: 0 0 auto;
    max-width: 400px;
}

.main-image-container {
    width: 100%;
    height: 320px;
    margin-bottom: 10px;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    background-color: #f9f9f9;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #eee;
    transition: all 0.2s ease-in-out;
}

.main-image-container:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.main-image {
    max-height: 90%;
    max-width: 90%;
    object-fit: contain;
    max-width: 300px;
    max-height: 300px;
    width: auto;
    height: auto;
}

.thumbnails-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    max-width: 100%;
    margin-top: 10px;
}

.thumbnail-image {
    width: auto;
    height: 40px;
    max-width: 60px;
    max-height: 40px;
    object-fit: contain;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0.7;
    border: 1px solid #e8e8e8;
    background-color: #f9f9f9;
    padding: 2px;
}

.thumbnail-image:hover {
    opacity: 0.9;
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.thumbnail-image.active {
    opacity: 1;
    border-color: #1890ff;
    box-shadow: 0 1px 4px rgba(24, 144, 255, 0.3);
}

.outfit-image-container {
    width: 100%;
    max-width: 400px;
    height: 320px;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #eee;
    background-color: #f9f9f9;
    transition: all 0.2s ease-in-out;
}

.outfit-image-container:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.outfit-image {
    max-height: 90%;
    max-width: 90%;
    object-fit: contain;
    max-width: 300px;
    max-height: 300px;
    width: auto;
    height: auto;
}

.outfit-info {
    flex: 1;
    min-width: 300px;
}

.outfit-info h3 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 24px;
    color: #333;
}

.info-item {
    margin-bottom: 12px;
    font-size: 16px;
}

.info-label {
    font-weight: 600;
    color: #666;
    display: inline-block;
    width: 100px;
}

.info-value {
    color: #333;
}

/* 衣物列表样式 */
.clothes-container {
    margin-top: 20px;
}

.clothes-container h3 {
    margin-bottom: 15px;
    font-size: 20px;
    color: #333;
}

.clothes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 15px;
}

.clothes-item {
    background-color: #fff;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    transition: all 0.25s ease;
    border: 1px solid #f0f0f0;
}

.clothes-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.clothes-image {
    width: 100%;
    height: 140px;
    background-color: #f9f9f9;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease-in-out;
}

.clothes-image:hover {
    background-color: #f2f2f2;
}

.clothes-image img {
    max-width: 85%;
    max-height: 85%;
    object-fit: contain;
    transition: all 0.2s ease-in-out;
    max-width: 120px;
    max-height: 120px;
    width: auto;
    height: auto;
}

.clothes-item:hover .clothes-image img {
    max-width: 90%;
    max-height: 90%;
    max-width: 130px;
    max-height: 130px;
}

.clothes-info {
    padding: 10px 12px;
}

.clothes-name {
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 4px;
    color: #333;
    /* 防止过长的文本 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.clothes-type {
    font-size: 12px;
    color: #8c8c8c;
}

/* 加载状态和错误提示 */
.loading, .no-data, .error {
    padding: 20px;
    text-align: center;
}

.loading {
    color: #1890ff;
}

.no-data {
    color: #999;
}

.error {
    color: #ff4d4f;
}

/* 响应式布局 */
@media (max-width: 1200px) {
    .main-image-container, .outfit-image-container {
        height: 300px;
    }
    
    .clothes-image {
        height: 130px;
    }
    
    .clothes-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .main-image, .outfit-image {
        max-height: 90%;
        max-width: 90%;
        max-width: 280px;
        max-height: 280px;
    }
    
    .clothes-image img {
        max-width: 110px;
        max-height: 110px;
    }
    
    .clothes-item:hover .clothes-image img {
        max-width: 120px;
        max-height: 120px;
    }
}

@media (max-width: 992px) {
    .main-image-container, .outfit-image-container {
        height: 260px;
    }
    
    .clothes-image {
        height: 120px;
    }
    
    .clothes-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }
    
    .main-image, .outfit-image {
        max-width: 240px;
        max-height: 240px;
    }
    
    .clothes-image img {
        max-width: 100px;
        max-height: 100px;
    }
    
    .clothes-item:hover .clothes-image img {
        max-width: 110px;
        max-height: 110px;
    }
}

@media (max-width: 768px) {
    .outfit-main {
        flex-direction: column;
    }
    
    .outfit-images-slider {
        margin-bottom: 20px;
        margin-right: 0;
        max-width: 100%;
    }
    
    .main-image-container, .outfit-image-container {
        height: 240px;
    }
    
    .thumbnail-image {
        height: 35px;
        max-width: 50px;
        max-height: 35px;
    }
    
    .clothes-image {
        height: 110px;
    }
    
    .clothes-grid {
        grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    }
    
    .main-image, .outfit-image {
        max-width: 220px;
        max-height: 220px;
    }
    
    .clothes-image img {
        max-width: 90px;
        max-height: 90px;
    }
    
    .clothes-item:hover .clothes-image img {
        max-width: 100px;
        max-height: 100px;
    }
}

@media (max-width: 576px) {
    .main-image-container, .outfit-image-container {
        height: 200px;
    }
    
    .thumbnail-image {
        height: 30px;
        max-width: 45px;
        max-height: 30px;
    }
    
    .clothes-image {
        height: 100px;
    }
    
    .clothes-grid {
        grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
        gap: 10px;
    }
    
    .main-image, .outfit-image {
        max-width: 180px;
        max-height: 180px;
    }
    
    .clothes-image img {
        max-width: 80px;
        max-height: 80px;
    }
    
    .clothes-item:hover .clothes-image img {
        max-width: 90px;
        max-height: 90px;
    }
} 