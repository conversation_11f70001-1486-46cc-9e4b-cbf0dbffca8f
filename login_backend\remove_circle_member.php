<?php
// 踢出圈子成员API
// 模块2：圈子成员管理模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$operatorId = $payload['sub'];

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

// 验证必需参数
if (!isset($input['user_id']) || empty($input['user_id'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少用户ID参数'
    ]);
    exit;
}

$targetUserId = intval($input['user_id']);

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查找操作者所在的圈子和角色
    $findOperatorSql = "SELECT cm.circle_id, cm.role, c.name as circle_name
                        FROM circle_members cm 
                        JOIN outfit_circles c ON cm.circle_id = c.id 
                        WHERE cm.user_id = :operator_id AND cm.status = 'active' AND c.status = 'active'";
    $findOperatorStmt = $conn->prepare($findOperatorSql);
    $findOperatorStmt->bindParam(':operator_id', $operatorId, PDO::PARAM_INT);
    $findOperatorStmt->execute();
    
    $operatorCircle = $findOperatorStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$operatorCircle) {
        echo json_encode([
            'status' => 'error',
            'message' => '您当前未加入任何圈子'
        ]);
        exit;
    }
    
    // 检查操作者是否是创建者
    if ($operatorCircle['role'] !== 'creator') {
        echo json_encode([
            'status' => 'error',
            'message' => '只有创建者可以移除成员'
        ]);
        exit;
    }
    
    // 查找目标用户在同一圈子中的信息
    $findTargetSql = "SELECT cm.id as member_id, cm.role, u.nickname
                      FROM circle_members cm 
                      JOIN users u ON cm.user_id = u.id
                      WHERE cm.user_id = :target_user_id AND cm.circle_id = :circle_id AND cm.status = 'active'";
    $findTargetStmt = $conn->prepare($findTargetSql);
    $findTargetStmt->bindParam(':target_user_id', $targetUserId, PDO::PARAM_INT);
    $findTargetStmt->bindParam(':circle_id', $operatorCircle['circle_id'], PDO::PARAM_INT);
    $findTargetStmt->execute();
    
    $targetMember = $findTargetStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$targetMember) {
        echo json_encode([
            'status' => 'error',
            'message' => '目标用户不在您的圈子中'
        ]);
        exit;
    }
    
    // 不能移除创建者（自己）
    if ($targetMember['role'] === 'creator') {
        echo json_encode([
            'status' => 'error',
            'message' => '不能移除创建者'
        ]);
        exit;
    }
    
    // 开始事务
    $conn->beginTransaction();
    
    // 将目标成员状态设置为已移除
    $removeMemberSql = "UPDATE circle_members 
                        SET status = 'removed', removed_at = NOW(), removed_by = :operator_id 
                        WHERE id = :member_id";
    $removeMemberStmt = $conn->prepare($removeMemberSql);
    $removeMemberStmt->bindParam(':member_id', $targetMember['member_id'], PDO::PARAM_INT);
    $removeMemberStmt->bindParam(':operator_id', $operatorId, PDO::PARAM_INT);
    $removeMemberStmt->execute();
    
    // 更新圈子成员数量
    $updateCountSql = "UPDATE outfit_circles 
                       SET member_count = (
                           SELECT COUNT(*) FROM circle_members 
                           WHERE circle_id = :circle_id AND status = 'active'
                       ) 
                       WHERE id = :circle_id";
    $updateCountStmt = $conn->prepare($updateCountSql);
    $updateCountStmt->bindParam(':circle_id', $operatorCircle['circle_id'], PDO::PARAM_INT);
    $updateCountStmt->execute();
    
    // 提交事务
    $conn->commit();
    
    echo json_encode([
        'status' => 'success',
        'message' => '已成功移除成员"' . $targetMember['nickname'] . '"'
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if (isset($conn)) {
        $conn->rollBack();
    }
    
    echo json_encode([
        'status' => 'error',
        'message' => '移除成员失败：' . $e->getMessage()
    ]);
}
?>
