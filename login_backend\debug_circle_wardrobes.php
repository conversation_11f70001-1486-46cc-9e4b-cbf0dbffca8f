<?php
/**
 * 调试圈子衣橱数据的脚本
 * 用于排查衣橱筛选问题
 */

require_once 'config.php';
require_once 'db.php';

// 测试用户ID（请替换为实际的测试用户ID）
$testUserId = 3;
$testWardrobeId = 1; // 测试衣橱ID

echo "=== 圈子衣橱数据调试 ===\n\n";

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 1. 检查用户所在的圈子
    echo "1. 检查用户 $testUserId 所在的圈子:\n";
    $stmt = $conn->prepare("
        SELECT cm.circle_id, c.name as circle_name, cm.status, cm.role
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id
    ");
    $stmt->bindParam(':user_id', $testUserId, PDO::PARAM_INT);
    $stmt->execute();
    $circles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($circles)) {
        echo "用户不在任何圈子中\n\n";
    } else {
        foreach ($circles as $circle) {
            echo "- 圈子ID: {$circle['circle_id']}, 名称: {$circle['circle_name']}, 状态: {$circle['status']}, 角色: {$circle['role']}\n";
        }
        echo "\n";
    }
    
    // 2. 检查个人衣橱
    echo "2. 检查用户个人衣橱:\n";
    $stmt = $conn->prepare("
        SELECT id, name, description, user_id, circle_id, is_default
        FROM wardrobes
        WHERE user_id = :user_id
        ORDER BY is_default DESC, sort_order ASC
    ");
    $stmt->bindParam(':user_id', $testUserId, PDO::PARAM_INT);
    $stmt->execute();
    $personalWardrobes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "个人衣橱数量: " . count($personalWardrobes) . "\n";
    foreach ($personalWardrobes as $wardrobe) {
        echo "- ID: {$wardrobe['id']}, 名称: {$wardrobe['name']}, 用户ID: {$wardrobe['user_id']}, 圈子ID: {$wardrobe['circle_id']}, 默认: {$wardrobe['is_default']}\n";
    }
    echo "\n";
    
    // 3. 检查圈子共享衣橱
    echo "3. 检查圈子共享衣橱:\n";
    if (!empty($circles)) {
        $circleIds = array_column($circles, 'circle_id');
        $placeholders = str_repeat('?,', count($circleIds) - 1) . '?';
        
        $stmt = $conn->prepare("
            SELECT w.id, w.name, w.description, w.user_id, w.circle_id, w.is_default, u.nickname as creator_nickname
            FROM wardrobes w
            LEFT JOIN users u ON w.user_id = u.id
            WHERE w.circle_id IN ($placeholders)
            ORDER BY w.is_default DESC, w.sort_order ASC
        ");
        $stmt->execute($circleIds);
        $sharedWardrobes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "圈子共享衣橱数量: " . count($sharedWardrobes) . "\n";
        foreach ($sharedWardrobes as $wardrobe) {
            echo "- ID: {$wardrobe['id']}, 名称: {$wardrobe['name']}, 圈子ID: {$wardrobe['circle_id']}, 创建者: {$wardrobe['creator_nickname']}\n";
        }
    } else {
        echo "用户不在任何圈子中，无共享衣橱\n";
    }
    echo "\n";
    
    // 4. 检查指定衣橱中的衣物
    echo "4. 检查衣橱 $testWardrobeId 中的衣物:\n";
    
    // 个人衣物
    echo "个人衣物:\n";
    $stmt = $conn->prepare("
        SELECT id, name, category, user_id, wardrobe_id, circle_id
        FROM clothes
        WHERE wardrobe_id = :wardrobe_id AND user_id = :user_id
        LIMIT 5
    ");
    $stmt->bindParam(':wardrobe_id', $testWardrobeId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $testUserId, PDO::PARAM_INT);
    $stmt->execute();
    $personalClothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "个人衣物数量: " . count($personalClothes) . "\n";
    foreach ($personalClothes as $item) {
        echo "- ID: {$item['id']}, 名称: {$item['name']}, 分类: {$item['category']}, 衣橱ID: {$item['wardrobe_id']}, 圈子ID: {$item['circle_id']}\n";
    }
    
    // 圈子衣物
    echo "圈子衣物:\n";
    $stmt = $conn->prepare("
        SELECT id, name, category, user_id, wardrobe_id, circle_id
        FROM clothes
        WHERE wardrobe_id = :wardrobe_id AND circle_id IS NOT NULL
        LIMIT 5
    ");
    $stmt->bindParam(':wardrobe_id', $testWardrobeId, PDO::PARAM_INT);
    $stmt->execute();
    $circleClothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "圈子衣物数量: " . count($circleClothes) . "\n";
    foreach ($circleClothes as $item) {
        echo "- ID: {$item['id']}, 名称: {$item['name']}, 分类: {$item['category']}, 用户ID: {$item['user_id']}, 衣橱ID: {$item['wardrobe_id']}, 圈子ID: {$item['circle_id']}\n";
    }
    echo "\n";
    
    // 5. 测试API查询逻辑
    echo "5. 测试API查询逻辑:\n";
    
    // 测试全部数据源 + 衣橱筛选
    echo "全部数据源 + 衣橱筛选:\n";
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.category, c.user_id, c.wardrobe_id, c.circle_id,
               u.nickname as creator_nickname,
               CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
        FROM clothes c 
        LEFT JOIN users u ON c.user_id = u.id 
        WHERE ((c.user_id = :user_id AND c.circle_id IS NULL) OR 
               c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'))
               AND c.wardrobe_id = :wardrobe_id
        ORDER BY c.created_at DESC
        LIMIT 10
    ");
    $stmt->bindParam(':user_id', $testUserId, PDO::PARAM_INT);
    $stmt->bindParam(':wardrobe_id', $testWardrobeId, PDO::PARAM_INT);
    $stmt->execute();
    $allClothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "结果数量: " . count($allClothes) . "\n";
    foreach ($allClothes as $item) {
        echo "- {$item['name']} (ID: {$item['id']}) - 数据源: {$item['data_source']}, 衣橱ID: {$item['wardrobe_id']}\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 调试完成 ===\n";
?>
