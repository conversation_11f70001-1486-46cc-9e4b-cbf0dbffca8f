<?php
/**
 * 面容分析API
 * 
 * 功能：
 * 1. 接收用户上传的面容照片和风格偏好
 * 2. 将数据传输到中转API进行分析
 * 3. 存储和返回分析结果
 */

// 引入必要的配置和库
require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';
require_once '../vendor/autoload.php'; // 引入阿里云OSS SDK
require_once 'oss_helper.php'; // 引入OSS助手类

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');

// 设置日志文件
$logFile = __DIR__ . '/logs/face_analysis.log';
$responseLogFile = __DIR__ . '/logs/face_analysis.php响应日志.log';
if (!file_exists(dirname($logFile))) {
    mkdir(dirname($logFile), 0755, true);
}

/**
 * 记录日志函数
 */
function logDebug($message, $data = null) {
    global $logFile;
    $timestamp = date('[Y-m-d H:i:s]');
    $logMessage = $timestamp . ' ' . $message;
    
    if ($data !== null) {
        if (is_array($data) || is_object($data)) {
            $logMessage .= ' ' . json_encode($data, JSON_UNESCAPED_UNICODE);
        } else {
            $logMessage .= ' ' . $data;
        }
    }
    
    file_put_contents($logFile, $logMessage . PHP_EOL, FILE_APPEND);
    error_log($logMessage);
}

/**
 * 记录响应日志函数
 */
function logResponse($data) {
    global $responseLogFile;
    file_put_contents($responseLogFile, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE), LOCK_EX);
}

// 处理OPTIONS请求（CORS预检）
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

// 初始化响应数组
$response = [
    'error' => false,
    'msg' => '',
    'data' => null
];

// 验证用户Token
$token = null;

// 1. 尝试从Authorization头获取token
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = $_SERVER['HTTP_AUTHORIZATION'];
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
    logDebug("从请求头获取到token");
}

// 2. 如果请求头中没有token，尝试从POST参数获取
if (!$token && isset($_POST['token']) && !empty($_POST['token'])) {
    $token = $_POST['token'];
    logDebug("从POST参数获取到token");
}

// 3. 如果POST中没有token，尝试从GET参数获取
if (!$token && isset($_GET['token']) && !empty($_GET['token'])) {
    $token = $_GET['token'];
    logDebug("从GET参数获取到token");
}

// 4. 如果JSON数据中有token，也可以获取
if (!$token && isset($jsonData['token']) && !empty($jsonData['token'])) {
    $token = $jsonData['token'];
    logDebug("从JSON数据获取到token");
}

// 如果所有方式都没有找到token，返回错误
if (!$token) {
    $response['error'] = true;
    $response['msg'] = '未提供授权信息';
    echo json_encode($response);
    exit;
}

$auth = new Auth();
$tokenData = $auth->verifyToken($token);

if (!$tokenData) {
    $response['error'] = true;
    $response['msg'] = '无效的授权信息';
    echo json_encode($response);
    exit;
}

// 获取用户ID
$userId = $tokenData['sub'];
logDebug("用户认证成功，用户ID: $userId");

// 获取数据库连接
$db = new Database();
$conn = $db->getConnection();

// 检查是否有JSON格式的请求数据
$jsonData = null;
$contentType = isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : '';
if (strpos($contentType, 'application/json') !== false) {
    $jsonInput = file_get_contents('php://input');
    $jsonData = json_decode($jsonInput, true);
    logDebug("接收到JSON数据请求", $jsonData);
}

// 处理不同的请求方法
try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // 处理JSON格式的分析请求
        if ($jsonData !== null && isset($jsonData['front_photo_url'])) {
            logDebug("处理JSON格式的面容分析请求");
            handleAnalysisRequest($userId, $conn, $jsonData);
        }
        // 处理表单格式的分析请求
        else if (isset($_POST['action']) && $_POST['action'] === 'analyze') {
            logDebug("处理表单格式的面容分析请求");
            handleAnalysisRequest($userId, $conn);
        }
        // 处理更新已有分析记录的请求
        else if (isset($_POST['action']) && $_POST['action'] === 'update_analysis') {
            logDebug("处理更新分析记录请求");
            handleUpdateAnalysisRequest($userId, $conn);
        }
        // 处理分析结果查询
        else if (isset($_POST['action']) && $_POST['action'] === 'get_result') {
            logDebug("处理POST分析结果查询请求");
            handleGetResultRequest($userId, $conn);
        }
        // 处理删除分析记录请求
        else if (isset($_POST['action']) && $_POST['action'] === 'delete') {
            logDebug("处理删除分析记录请求");
            handleDeleteRequest($userId, $conn);
        }
        // 未知操作
        else {
            logDebug("未知操作请求", ['POST' => $_POST, 'JSON' => $jsonData, 'CONTENT_TYPE' => $contentType]);
            $response['error'] = true;
            $response['msg'] = '未知操作';
            echo json_encode($response);
        }
    }
    // 获取用户分析历史或分析结果
    else if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // 如果提供了analysis_id，则获取特定分析结果
        if (isset($_GET['analysis_id'])) {
            logDebug("处理GET分析结果查询请求");
            handleGetResultRequest($userId, $conn);
        }
        // 否则获取用户分析历史
        else {
            logDebug("处理分析历史查询请求");
            handleGetHistoryRequest($userId, $conn);
        }
    }
    // 不支持的请求方法
    else {
        $response['error'] = true;
        $response['msg'] = '不支持的请求方法';
        echo json_encode($response);
    }
} catch (Exception $e) {
    logDebug("处理请求异常: " . $e->getMessage());
    $response['error'] = true;
    $response['msg'] = '服务器错误: ' . $e->getMessage();
    echo json_encode($response);
}

/**
 * 处理分析请求
 */
function handleAnalysisRequest($userId, $conn, $jsonData = null) {
    global $response;
    logDebug("处理面容分析请求");
    
    // 根据请求类型获取参数
    $isJsonRequest = ($jsonData !== null);
    
    if ($isJsonRequest) {
        // 处理JSON格式请求
        if (!isset($jsonData['front_photo_url']) || empty($jsonData['front_photo_url'])) {
            $response['error'] = true;
            $response['msg'] = '缺少正面照片';
            echo json_encode($response);
            return;
        }
        
        $frontPhotoUrl = $jsonData['front_photo_url'];
        $sidePhotoUrl = isset($jsonData['side_photo_url']) ? $jsonData['side_photo_url'] : '';
        $preferredStyle = isset($jsonData['preferred_style']) ? $jsonData['preferred_style'] : '';
        
        // 新增获取CDN URL（如果前端提供）
        $cdnFrontPhotoUrl = isset($jsonData['cdn_front_photo_url']) ? $jsonData['cdn_front_photo_url'] : '';
        $cdnSidePhotoUrl = isset($jsonData['cdn_side_photo_url']) ? $jsonData['cdn_side_photo_url'] : '';
        
        logDebug("处理JSON请求数据，正面照片URL: $frontPhotoUrl, 风格偏好: $preferredStyle");
        
        // 将分析任务保存到数据库
        $stmt = $conn->prepare("INSERT INTO face_analysis (user_id, front_photo_url, side_photo_url, cdn_front_photo_url, cdn_side_photo_url, preferred_style, status, created_at) 
                              VALUES (:user_id, :front_photo_url, :side_photo_url, :cdn_front_photo_url, :cdn_side_photo_url, :preferred_style, 'pending', NOW())");
        
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':front_photo_url', $frontPhotoUrl);
        $stmt->bindParam(':side_photo_url', $sidePhotoUrl);
        $stmt->bindParam(':cdn_front_photo_url', $cdnFrontPhotoUrl);
        $stmt->bindParam(':cdn_side_photo_url', $cdnSidePhotoUrl);
        $stmt->bindParam(':preferred_style', $preferredStyle);
        
        if (!$stmt->execute()) {
            logDebug("数据库插入失败: " . json_encode($stmt->errorInfo()));
            $response['error'] = true;
            $response['msg'] = '保存分析请求失败';
            echo json_encode($response);
            return;
        }
        
        $analysisId = $conn->lastInsertId();
        logDebug("分析任务已创建，ID: $analysisId");
        
        // 调用中转API进行分析
        $analysisResult = callTransitApi($frontPhotoUrl, $sidePhotoUrl, $preferredStyle);
        
    } else {
        // 处理表单格式请求
        if (!isset($_POST['front_photo']) || empty($_POST['front_photo'])) {
            $response['error'] = true;
            $response['msg'] = '缺少正面照片';
            echo json_encode($response);
            return;
        }

        // 获取请求参数
        $frontPhotoBase64 = $_POST['front_photo'];
        $sidePhotoBase64 = isset($_POST['side_photo']) ? $_POST['side_photo'] : '';
        $preferredStyle = isset($_POST['preferred_style']) ? $_POST['preferred_style'] : '';

        logDebug("接收到面容分析请求，风格偏好: $preferredStyle");

        // 保存照片到服务器（用于备份和显示）
        $frontPhotoUrl = saveBase64Image($frontPhotoBase64, 'face_front_' . $userId . '_' . time());
        $sidePhotoUrl = $sidePhotoBase64 ? saveBase64Image($sidePhotoBase64, 'face_side_' . $userId . '_' . time()) : '';

        // 清理Base64数据，移除data:image前缀（如果存在）
        $cleanFrontPhotoBase64 = cleanBase64ForTransmission($frontPhotoBase64);
        $cleanSidePhotoBase64 = $sidePhotoBase64 ? cleanBase64ForTransmission($sidePhotoBase64) : '';
        
        // 初始化OSS助手
        $ossHelper = new OssHelper();
        $cdnFrontPhotoUrl = '';
        $cdnSidePhotoUrl = '';
        
        // 上传正面照片到OSS
        if ($frontPhotoUrl) {
            logDebug("尝试上传正面照片到OSS");
            
            // 解析URL，获取本地文件路径
            $parsedUrl = parse_url($frontPhotoUrl);
            $relativePath = isset($parsedUrl['path']) ? $parsedUrl['path'] : '';
            
            // 移除URL中的域名部分，只保留相对路径
            $relativePath = preg_replace('/^\/login_backend\//', '', $relativePath);
            
            // 构建绝对路径
            $frontPhotoLocalPath = __DIR__ . '/' . $relativePath;
            
            logDebug("解析后的本地文件路径: $frontPhotoLocalPath");
            
            // 检查文件是否存在
            if (!file_exists($frontPhotoLocalPath)) {
                logDebug("错误：本地文件不存在: $frontPhotoLocalPath");
                
                // 尝试直接使用相对路径
                $alternativePath = $relativePath;
                logDebug("尝试替代路径: $alternativePath");
                
                if (file_exists($alternativePath)) {
                    $frontPhotoLocalPath = $alternativePath;
                    logDebug("找到替代文件路径: $frontPhotoLocalPath");
                } else {
                    logDebug("替代路径也不存在");
                }
            } else {
                logDebug("本地文件存在，大小: " . filesize($frontPhotoLocalPath) . " 字节");
            }
            
            // 如果文件存在，上传到OSS
            if (file_exists($frontPhotoLocalPath)) {
                // 提取文件名部分
                $frontPhotoFilename = basename($frontPhotoLocalPath);
                $ossKey = OSS_PATH_PHOTOS . 'face_analysis/' . $frontPhotoFilename;
                
                logDebug("OSS路径: $ossKey");
                
                // 确保OSS助手类已正确初始化
                try {
                    $uploadResult = $ossHelper->uploadFile($frontPhotoLocalPath, $ossKey);
                    if ($uploadResult['success']) {
                        $cdnFrontPhotoUrl = $uploadResult['url'];
                        logDebug("正面照片上传OSS成功: $cdnFrontPhotoUrl");
                    } else {
                        logDebug("正面照片上传OSS失败: " . ($uploadResult['error'] ?? '未知错误'));
                    }
                } catch (Exception $e) {
                    logDebug("上传OSS时发生异常: " . $e->getMessage());
                }
            }
        }
        
        // 上传侧面照片到OSS（如果有）
        if ($sidePhotoUrl) {
            logDebug("尝试上传侧面照片到OSS");
            
            // 解析URL，获取本地文件路径
            $parsedUrl = parse_url($sidePhotoUrl);
            $relativePath = isset($parsedUrl['path']) ? $parsedUrl['path'] : '';
            
            // 移除URL中的域名部分，只保留相对路径
            $relativePath = preg_replace('/^\/login_backend\//', '', $relativePath);
            
            // 构建绝对路径
            $sidePhotoLocalPath = __DIR__ . '/' . $relativePath;
            
            logDebug("解析后的侧面照片本地文件路径: $sidePhotoLocalPath");
            
            // 检查文件是否存在
            if (!file_exists($sidePhotoLocalPath)) {
                logDebug("错误：侧面照片本地文件不存在: $sidePhotoLocalPath");
                
                // 尝试直接使用相对路径
                $alternativePath = $relativePath;
                logDebug("尝试替代路径: $alternativePath");
                
                if (file_exists($alternativePath)) {
                    $sidePhotoLocalPath = $alternativePath;
                    logDebug("找到替代文件路径: $sidePhotoLocalPath");
                } else {
                    logDebug("替代路径也不存在");
                }
            } else {
                logDebug("侧面照片本地文件存在，大小: " . filesize($sidePhotoLocalPath) . " 字节");
            }
            
            // 如果文件存在，上传到OSS
            if (file_exists($sidePhotoLocalPath)) {
                // 提取文件名部分
                $sidePhotoFilename = basename($sidePhotoLocalPath);
                $ossKey = OSS_PATH_PHOTOS . 'face_analysis/' . $sidePhotoFilename;
                
                logDebug("侧面照片OSS路径: $ossKey");
                
                try {
                    $uploadResult = $ossHelper->uploadFile($sidePhotoLocalPath, $ossKey);
                    if ($uploadResult['success']) {
                        $cdnSidePhotoUrl = $uploadResult['url'];
                        logDebug("侧面照片上传OSS成功: $cdnSidePhotoUrl");
                    } else {
                        logDebug("侧面照片上传OSS失败: " . ($uploadResult['error'] ?? '未知错误'));
                    }
                } catch (Exception $e) {
                    logDebug("上传侧面照片到OSS时发生异常: " . $e->getMessage());
                }
            }
        }
        
        // 将分析任务保存到数据库（包含CDN URL）
        $stmt = $conn->prepare("INSERT INTO face_analysis (user_id, front_photo_url, side_photo_url, cdn_front_photo_url, cdn_side_photo_url, preferred_style, status, created_at) 
                              VALUES (:user_id, :front_photo_url, :side_photo_url, :cdn_front_photo_url, :cdn_side_photo_url, :preferred_style, 'pending', NOW())");
        
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':front_photo_url', $frontPhotoUrl);
        $stmt->bindParam(':side_photo_url', $sidePhotoUrl);
        $stmt->bindParam(':cdn_front_photo_url', $cdnFrontPhotoUrl);
        $stmt->bindParam(':cdn_side_photo_url', $cdnSidePhotoUrl);
        $stmt->bindParam(':preferred_style', $preferredStyle);
        
        if (!$stmt->execute()) {
            logDebug("数据库插入失败: " . json_encode($stmt->errorInfo()));
            $response['error'] = true;
            $response['msg'] = '保存分析请求失败';
            echo json_encode($response);
            return;
        }
        
        $analysisId = $conn->lastInsertId();
        logDebug("分析任务已创建，ID: $analysisId");
        
        // 调用中转API进行分析（优先使用Base64直传）
        $analysisResult = callTransitApi($frontPhotoUrl, $sidePhotoUrl, $preferredStyle, $cleanFrontPhotoBase64, $cleanSidePhotoBase64);
    }

    if ($analysisResult === false) {
        // 更新状态为失败
        updateAnalysisStatus($conn, $analysisId, 'failed');
        
        $response['error'] = true;
        $response['msg'] = '面容分析失败，请稍后重试';
        echo json_encode($response);
        return;
    }
    
    // 更新分析结果
    $stmt = $conn->prepare("UPDATE face_analysis SET analysis_result = :analysis_result, status = 'completed', updated_at = NOW() WHERE id = :id");
    $stmt->bindParam(':analysis_result', $analysisResult);
    $stmt->bindParam(':id', $analysisId);
    
    if (!$stmt->execute()) {
        logDebug("更新分析结果失败: " . json_encode($stmt->errorInfo()));
        updateAnalysisStatus($conn, $analysisId, 'failed');
        
        $response['error'] = true;
        $response['msg'] = '保存分析结果失败';
        echo json_encode($response);
        return;
    }
    
    // 返回成功响应
    $analysisData = getAnalysisById($conn, $analysisId);
    $response['data'] = $analysisData;
    $response['msg'] = '面容分析完成';
    
    // 记录成功的响应数据到日志文件
    logResponse($response);
    
    echo json_encode($response);
}

/**
 * 处理获取分析结果请求
 */
function handleGetResultRequest($userId, $conn) {
    global $response;
    
    $analysisId = null;
    
    // 尝试从POST参数获取analysis_id
    if (isset($_POST['analysis_id']) && !empty($_POST['analysis_id'])) {
        $analysisId = $_POST['analysis_id'];
        logDebug("从POST参数获取到分析ID: $analysisId");
    }
    // 尝试从GET参数获取analysis_id
    else if (isset($_GET['analysis_id']) && !empty($_GET['analysis_id'])) {
        $analysisId = $_GET['analysis_id'];
        logDebug("从GET参数获取到分析ID: $analysisId");
    }
    
    // 如果没有找到analysis_id，返回错误
    if (!$analysisId) {
        $response['error'] = true;
        $response['msg'] = '缺少分析ID';
        echo json_encode($response);
        return;
    }
    
    logDebug("获取分析结果，ID: $analysisId");
    
    // 获取分析结果
    $analysisData = getAnalysisById($conn, $analysisId, $userId);
    
    if (!$analysisData) {
        $response['error'] = true;
        $response['msg'] = '未找到分析结果';
        echo json_encode($response);
        return;
    }
    
    $response['data'] = $analysisData;
    echo json_encode($response);
}

/**
 * 处理获取分析历史请求
 */
function handleGetHistoryRequest($userId, $conn) {
    global $response;
    logDebug("获取用户分析历史，用户ID: $userId");
    
    // 查询用户的分析历史
    $stmt = $conn->prepare("SELECT id, front_photo_url, side_photo_url, cdn_front_photo_url, cdn_side_photo_url, preferred_style, status, created_at 
                          FROM face_analysis 
                          WHERE user_id = :user_id 
                          ORDER BY created_at DESC");
    $stmt->bindParam(':user_id', $userId);
    
    if (!$stmt->execute()) {
        logDebug("查询分析历史失败: " . json_encode($stmt->errorInfo()));
        $response['error'] = true;
        $response['msg'] = '获取分析历史失败';
        echo json_encode($response);
        return;
    }
    
    $history = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理每个分析记录，添加display_front_photo_url和display_side_photo_url字段
    foreach ($history as &$record) {
        // 使用CDN URL替换本地URL，如果CDN URL存在
        if (!empty($record['cdn_front_photo_url'])) {
            $record['display_front_photo_url'] = $record['cdn_front_photo_url'];
        } else {
            $record['display_front_photo_url'] = $record['front_photo_url'];
        }
        
        if (!empty($record['cdn_side_photo_url'])) {
            $record['display_side_photo_url'] = $record['cdn_side_photo_url'];
        } else {
            $record['display_side_photo_url'] = $record['side_photo_url'];
        }
    }
    
    $response['data'] = $history;
    echo json_encode($response);
}

/**
 * 处理删除分析记录请求
 */
function handleDeleteRequest($userId, $conn) {
    global $response;
    logDebug("处理删除分析记录请求，用户ID: $userId");

    $analysisId = null;

    // 尝试从POST参数获取analysis_id
    if (isset($_POST['analysis_id']) && !empty($_POST['analysis_id'])) {
        $analysisId = $_POST['analysis_id'];
        logDebug("从POST参数获取到分析ID: $analysisId");
    }
    // 尝试从GET参数获取analysis_id
    else if (isset($_GET['analysis_id']) && !empty($_GET['analysis_id'])) {
        $analysisId = $_GET['analysis_id'];
        logDebug("从GET参数获取到分析ID: $analysisId");
    }

    // 如果没有找到analysis_id，返回错误
    if (!$analysisId) {
        $response['error'] = true;
        $response['msg'] = '缺少分析ID';
        echo json_encode($response);
        return;
    }

    // 检查分析记录是否存在且属于当前用户
    $stmt = $conn->prepare("SELECT id FROM face_analysis WHERE id = :id AND user_id = :user_id");
    $stmt->bindParam(':id', $analysisId);
    $stmt->bindParam(':user_id', $userId);

    if (!$stmt->execute()) {
        logDebug("检查分析记录失败: " . json_encode($stmt->errorInfo()));
        $response['error'] = true;
        $response['msg'] = '检查分析记录失败';
        echo json_encode($response);
        return;
    }

    $recordExists = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$recordExists) {
        $response['error'] = true;
        $response['msg'] = '分析记录不存在或不属于您';
        echo json_encode($response);
        return;
    }

    // 删除分析记录
    $stmt = $conn->prepare("DELETE FROM face_analysis WHERE id = :id");
    $stmt->bindParam(':id', $analysisId);

    if (!$stmt->execute()) {
        logDebug("删除分析记录失败: " . json_encode($stmt->errorInfo()));
        $response['error'] = true;
        $response['msg'] = '删除分析记录失败';
        echo json_encode($response);
        return;
    }

    $response['error'] = false;
    $response['msg'] = '分析记录已删除';
    echo json_encode($response);
}

/**
 * 保存Base64图片到服务器
 */
function saveBase64Image($base64String, $filenamePrefix) {
    logDebug("开始保存Base64图片，前缀: $filenamePrefix");
    
    // 创建上传目录
    $uploadDir = 'uploads/face_analysis/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
        logDebug("创建上传目录: $uploadDir");
    }
    
    // 获取图片数据部分（去除前缀）
    if (strpos($base64String, ';base64,') !== false) {
        list(, $base64String) = explode(';base64,', $base64String);
        logDebug("已提取Base64数据部分");
    } else {
        logDebug("未找到Base64前缀，可能已经是纯Base64数据");
    }
    
    // 解码Base64
    $imageData = base64_decode($base64String);
    if ($imageData === false) {
        logDebug("Base64解码失败");
        return false;
    }
    
    logDebug("Base64解码成功，数据大小: " . strlen($imageData) . " 字节");
    
    // 生成唯一文件名
    $filename = $filenamePrefix . '_' . uniqid() . '.jpg';
    $filePath = $uploadDir . $filename;
    
    logDebug("将保存图片到: $filePath");
    
    // 保存图片
    $result = file_put_contents($filePath, $imageData);
    if ($result !== false) {
        logDebug("图片保存成功，写入 $result 字节");
        
        // 获取完整URL
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
        $host = $_SERVER['HTTP_HOST'];
        $baseUrl = $protocol . $host;
        $baseUrl = rtrim($baseUrl, '/') . '/';
        
        $fullUrl = $baseUrl . 'login_backend/' . $filePath;
        logDebug("生成的URL: $fullUrl");
        
        return $fullUrl;
    } else {
        logDebug("图片保存失败");
        return false;
    }
}

/**
 * 调用中转API进行分析
 */
function callTransitApi($frontPhotoUrl, $sidePhotoUrl, $preferredStyle, $frontPhotoBase64 = null, $sidePhotoBase64 = null) {
    logDebug("调用中转API，正面照片: $frontPhotoUrl, 风格偏好: $preferredStyle");

    // 优先使用Base64数据，如果没有则使用URL方式
    if ($frontPhotoBase64) {
        logDebug("使用Base64直传模式");
        $data = [
            'front_photo_base64' => $frontPhotoBase64,
            'preferred_style' => $preferredStyle
        ];

        if ($sidePhotoBase64) {
            $data['side_photo_base64'] = $sidePhotoBase64;
        }
    } else {
        logDebug("使用URL下载模式");
        $data = [
            'front_photo_url' => $frontPhotoUrl,
            'side_photo_url' => $sidePhotoUrl,
            'preferred_style' => $preferredStyle
        ];
    }

    // 中转API地址
    $transitApiUrl = 'https://www.furrywoo.com/gemini/mianrongfenxi.php';

    // 发起POST请求
    $ch = curl_init($transitApiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 设置60秒超时

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    // 检查请求是否成功
    if ($httpCode != 200 || $error) {
        logDebug("中转API请求失败，HTTP代码: $httpCode, 错误: $error");
        return false;
    }

    // 解析响应
    $responseData = json_decode($response, true);

    if (!$responseData || isset($responseData['error']) && $responseData['error']) {
        logDebug("中转API返回错误: " . ($responseData['msg'] ?? '未知错误'));
        return false;
    }

    // 解析并处理analysis字段中的数据（如果存在）
    if (isset($responseData['data']) && isset($responseData['data']['analysis'])) {
        // 如果analysis字段是null，尝试从raw_text中提取
        if ($responseData['data']['analysis'] === null && isset($responseData['data']['raw_text'])) {
            logDebug("尝试从raw_text提取JSON数据");
            // 尝试多种方法解析raw_text
            $analysisResult = extractJsonFromText($responseData['data']['raw_text']);
            if ($analysisResult !== null) {
                $responseData['data']['analysis'] = $analysisResult;
            }
        }
    }

    // 返回解析后的结构化数据
    logDebug("中转API分析成功，返回结构化数据");
    return json_encode($responseData);
}

/**
 * 从文本中提取JSON数据
 */
function extractJsonFromText($text) {
    // 方法1: 尝试直接解析
    try {
        $jsonData = json_decode($text, true);
        if ($jsonData && json_last_error() === JSON_ERROR_NONE) {
            return $jsonData;
        }
    } catch (Exception $e) {
        logDebug("直接解析JSON失败: " . $e->getMessage());
    }
    
    // 方法2: 尝试提取JSON部分
    $jsonStartPos = strpos($text, '{');
    $jsonEndPos = strrpos($text, '}');
    
    if ($jsonStartPos !== false && $jsonEndPos !== false) {
        $jsonStr = substr($text, $jsonStartPos, $jsonEndPos - $jsonStartPos + 1);
        
        try {
            // 处理可能的转义问题
            $jsonStr = str_replace('\\\\', '\\', $jsonStr);
            $jsonData = json_decode($jsonStr, true);
            
            if ($jsonData && json_last_error() === JSON_ERROR_NONE) {
                return $jsonData;
            }
        } catch (Exception $e) {
            logDebug("提取JSON部分解析失败: " . $e->getMessage());
        }
    }
    
    // 方法3: 尝试从markdown代码块中提取JSON
    if (preg_match('/```(?:json)?\s*({[\s\S]*?})\s*```/m', $text, $matches)) {
        try {
            $jsonStr = $matches[1];
            $jsonData = json_decode($jsonStr, true);
            
            if ($jsonData && json_last_error() === JSON_ERROR_NONE) {
                return $jsonData;
            }
        } catch (Exception $e) {
            logDebug("从markdown代码块提取JSON失败: " . $e->getMessage());
        }
    }
    
    // 所有方法都失败
    return null;
}

/**
 * 更新分析状态
 */
function updateAnalysisStatus($conn, $analysisId, $status) {
    $stmt = $conn->prepare("UPDATE face_analysis SET status = :status, updated_at = NOW() WHERE id = :id");
    $stmt->bindParam(':status', $status);
    $stmt->bindParam(':id', $analysisId);
    return $stmt->execute();
}

/**
 * 通过ID获取分析数据
 */
function getAnalysisById($conn, $analysisId, $userId = null) {
    $sql = "SELECT * FROM face_analysis WHERE id = :id";
    $params = [':id' => $analysisId];

    // 如果提供了用户ID，确保只返回该用户的数据
    if ($userId !== null) {
        $sql .= " AND user_id = :user_id";
        $params[':user_id'] = $userId;
    }

    // 只返回已支付的分析记录
    $sql .= " AND payment_status = 'paid'";

    logDebug("查询分析记录SQL: $sql, 参数: " . json_encode($params));
    
    $stmt = $conn->prepare($sql);
    foreach ($params as $param => $value) {
        $stmt->bindValue($param, $value);
    }
    
    if (!$stmt->execute()) {
        logDebug("获取分析数据失败: " . json_encode($stmt->errorInfo()));
        return false;
    }
    
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 如果找到结果，处理照片URL
    if ($result) {
        // 使用CDN URL替换本地URL，如果CDN URL存在
        if (!empty($result['cdn_front_photo_url'])) {
            $result['display_front_photo_url'] = $result['cdn_front_photo_url'];
        } else {
            $result['display_front_photo_url'] = $result['front_photo_url'];
        }
        
        if (!empty($result['cdn_side_photo_url'])) {
            $result['display_side_photo_url'] = $result['cdn_side_photo_url'];
        } else {
            $result['display_side_photo_url'] = $result['side_photo_url'];
        }
        
        // 如果找到结果且有分析数据，处理JSON字段
        if (isset($result['analysis_result']) && !empty($result['analysis_result'])) {
            logDebug("处理分析结果JSON字段");
            
            // 尝试解析JSON
            $analysisResultDecoded = json_decode($result['analysis_result'], true);
            
            // 检查是否成功解析
            if ($analysisResultDecoded === null && json_last_error() !== JSON_ERROR_NONE) {
                logDebug("JSON解析失败: " . json_last_error_msg() . "，尝试修复");
                
                // 尝试修复和清理JSON字符串
                $cleanedJson = cleanJsonString($result['analysis_result']);
                $analysisResultDecoded = json_decode($cleanedJson, true);
                
                if ($analysisResultDecoded === null && json_last_error() !== JSON_ERROR_NONE) {
                    logDebug("JSON修复后仍解析失败");
                    // 保留原始字符串，让前端处理
                } else {
                    // 更新为成功解析的数据
                    $result['analysis_result'] = $cleanedJson;
                    logDebug("JSON修复成功");
                }
            } else {
                // 解析成功，确保结果是有效的结构
                logDebug("JSON解析成功");
                
                // 检查结构，确保分析字段存在且有效
                if (isset($analysisResultDecoded['data'])) {
                    $dataField = $analysisResultDecoded['data'];
                    
                    // 如果analysis为空但raw_text存在，尝试从raw_text提取
                    if ((empty($dataField['analysis']) || $dataField['analysis'] === null) && 
                        isset($dataField['raw_text']) && !empty($dataField['raw_text'])) {
                        
                        logDebug("尝试从raw_text提取JSON");
                        $extractedJson = extractJsonFromText($dataField['raw_text']);
                        
                        if ($extractedJson !== null) {
                            $analysisResultDecoded['data']['analysis'] = $extractedJson;
                            $result['analysis_result'] = json_encode($analysisResultDecoded);
                            logDebug("从raw_text提取JSON成功");
                        }
                    }
                }
            }
        }
    }
    
    return $result;
}

/**
 * 清理Base64数据用于传输
 */
function cleanBase64ForTransmission($base64String) {
    // 如果包含数据URL前缀，则移除
    if (strpos($base64String, ';base64,') !== false) {
        list(, $base64String) = explode(';base64,', $base64String);
        logDebug("移除Base64前缀，清理后长度: " . strlen($base64String));
    }
    return $base64String;
}

/**
 * 清理和修复JSON字符串
 */
function cleanJsonString($jsonString) {
    // 移除可能导致解析错误的字符
    $jsonString = preg_replace('/[\x00-\x1F\x7F]/u', '', $jsonString);

    // 尝试修复常见错误
    $jsonString = str_replace(
        ['\\\\', '\"', '\r', '\n', '\t'],
        ['\\', '"', "\r", "\n", "\t"],
        $jsonString
    );

    // 检查JSON结构
    if (substr($jsonString, 0, 1) !== '{' && substr($jsonString, -1) !== '}') {
        // 尝试提取JSON部分
        $jsonStartPos = strpos($jsonString, '{');
        $jsonEndPos = strrpos($jsonString, '}');

        if ($jsonStartPos !== false && $jsonEndPos !== false) {
            $jsonString = substr($jsonString, $jsonStartPos, $jsonEndPos - $jsonStartPos + 1);
        }
    }

    return $jsonString;
}

/**
 * 处理更新分析记录请求（用于支付后上传照片）
 */
function handleUpdateAnalysisRequest($userId, $conn) {
    global $response;
    logDebug("处理更新分析记录请求");

    // 检查必要参数
    if (!isset($_POST['analysis_id']) || empty($_POST['analysis_id'])) {
        $response['error'] = true;
        $response['msg'] = '缺少分析ID';
        echo json_encode($response);
        return;
    }

    if (!isset($_POST['front_photo']) || empty($_POST['front_photo'])) {
        $response['error'] = true;
        $response['msg'] = '缺少正面照片';
        echo json_encode($response);
        return;
    }

    $analysisId = $_POST['analysis_id'];
    $frontPhotoBase64 = $_POST['front_photo'];
    $sidePhotoBase64 = isset($_POST['side_photo']) ? $_POST['side_photo'] : '';
    $preferredStyle = isset($_POST['preferred_style']) ? $_POST['preferred_style'] : '';

    // 验证分析记录是否属于当前用户且已支付
    $stmt = $conn->prepare("
        SELECT id, payment_status, status
        FROM face_analysis
        WHERE id = :analysis_id AND user_id = :user_id
    ");
    $stmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();

    $analysis = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$analysis) {
        $response['error'] = true;
        $response['msg'] = '分析记录不存在或无权限访问';
        echo json_encode($response);
        return;
    }

    // 检查支付状态
    if ($analysis['payment_status'] !== 'paid') {
        $response['error'] = true;
        $response['msg'] = '请先完成支付';
        echo json_encode($response);
        return;
    }

    // 检查使用状态
    if ($analysis['usage_status'] === 'used') {
        $response['error'] = true;
        $response['msg'] = '抱歉，面容分析次数已经用完，请返回上一页进行解锁';
        echo json_encode($response);
        return;
    }

    // 标记为已使用
    $updateUsageStmt = $conn->prepare("
        UPDATE face_analysis
        SET usage_status = 'used'
        WHERE id = :analysis_id
    ");
    $updateUsageStmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);
    $updateUsageStmt->execute();

    logDebug("分析记录验证通过，开始处理照片，分析ID: $analysisId");

    // 保存照片到服务器
    $frontPhotoUrl = saveBase64Image($frontPhotoBase64, 'face_front_' . $userId . '_' . time());
    $sidePhotoUrl = $sidePhotoBase64 ? saveBase64Image($sidePhotoBase64, 'face_side_' . $userId . '_' . time()) : '';

    // 清理Base64数据
    $cleanFrontPhotoBase64 = cleanBase64ForTransmission($frontPhotoBase64);
    $cleanSidePhotoBase64 = $sidePhotoBase64 ? cleanBase64ForTransmission($sidePhotoBase64) : '';

    // 更新分析记录
    $stmt = $conn->prepare("
        UPDATE face_analysis
        SET front_photo_url = :front_photo_url,
            side_photo_url = :side_photo_url,
            preferred_style = :preferred_style,
            status = 'processing',
            updated_at = NOW()
        WHERE id = :analysis_id
    ");

    $stmt->bindParam(':front_photo_url', $frontPhotoUrl);
    $stmt->bindParam(':side_photo_url', $sidePhotoUrl);
    $stmt->bindParam(':preferred_style', $preferredStyle);
    $stmt->bindParam(':analysis_id', $analysisId, PDO::PARAM_INT);

    if (!$stmt->execute()) {
        logDebug("更新分析记录失败: " . json_encode($stmt->errorInfo()));
        $response['error'] = true;
        $response['msg'] = '更新分析记录失败';
        echo json_encode($response);
        return;
    }

    // 调用中转API进行分析
    $analysisResult = callTransitApi($frontPhotoUrl, $sidePhotoUrl, $preferredStyle, $cleanFrontPhotoBase64, $cleanSidePhotoBase64);

    if ($analysisResult === false) {
        // 更新状态为失败
        updateAnalysisStatus($conn, $analysisId, 'failed');

        $response['error'] = true;
        $response['msg'] = '面容分析失败，请稍后重试';
        echo json_encode($response);
        return;
    }

    // 更新分析结果
    $stmt = $conn->prepare("UPDATE face_analysis SET analysis_result = :analysis_result, status = 'completed', updated_at = NOW() WHERE id = :id");
    $stmt->bindParam(':analysis_result', $analysisResult);
    $stmt->bindParam(':id', $analysisId);

    if (!$stmt->execute()) {
        logDebug("更新分析结果失败: " . json_encode($stmt->errorInfo()));
        updateAnalysisStatus($conn, $analysisId, 'failed');

        $response['error'] = true;
        $response['msg'] = '保存分析结果失败';
        echo json_encode($response);
        return;
    }

    logDebug("面容分析完成，分析ID: $analysisId");

    $response['error'] = false;
    $response['msg'] = '面容分析完成';
    $response['data'] = [
        'analysis_id' => $analysisId,
        'analysis_result' => $analysisResult
    ];
    echo json_encode($response);
}