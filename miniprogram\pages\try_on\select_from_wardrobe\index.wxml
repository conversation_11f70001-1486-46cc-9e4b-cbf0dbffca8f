<view class="main-content">
  <!-- 衣柜选择器 -->
  <view class="wardrobe-switcher">
    <scroll-view scroll-x="true" class="wardrobe-scroll" enhanced show-scrollbar="{{false}}">
      <view class="wardrobe-option {{selectedWardrobeId === null ? 'active' : ''}}" 
            bindtap="switchWardrobe" 
            data-id="{{null}}">全部衣物</view>
      <view wx:for="{{wardrobeList}}" 
            wx:key="id" 
            class="wardrobe-option {{selectedWardrobeId == item.id ? 'active' : ''}}"
            bindtap="switchWardrobe"
            data-id="{{item.id}}">{{item.name}}</view>
    </scroll-view>
  </view>
  
  <!-- 选择衣物类别 -->
  <view class="category-tabs">
    <view wx:for="{{categories}}" wx:key="id" 
          class="tab-item {{currentCategory === item.id ? 'active' : ''}}" 
          bindtap="switchCategory" data-id="{{item.id}}">
      {{item.name}}
    </view>
  </view>
  
  <!-- 衣物选择网格 -->
  <view class="clothes-grid">
    <block wx:if="{{currentClothes.length > 0}}">
      <view wx:for="{{currentClothes}}" wx:key="id" 
            class="clothes-item {{selectedClothes[item.id] ? 'selected' : ''}} {{item.category === 'skirts' ? 'skirt-item' : ''}}" 
            bindtap="selectClothing" data-id="{{item.id}}" data-category="{{item.category}}">
        <image src="{{item.image_url}}" mode="aspectFit" class="clothes-img"></image>
        <view class="select-icon">
          <text class="check-icon">✓</text>
        </view>
      </view>
    </block>
    <block wx:else>
      <view class="empty-clothes-tip">
        <view class="empty-icon">👔</view>
        <view class="empty-text">当前类别暂无衣物</view>
      </view>
    </block>
  </view>
</view>

<!-- 底部按钮 -->
<view class="bottom-bar">
  <!-- 已选衣物预览 -->
  <view class="selected-preview" wx:if="{{selectedCount > 0}}">
    <view class="preview-title">已选衣物:</view>
    <view class="selected-items">
      <view wx:for="{{originalAllClothes}}" wx:key="id" wx:if="{{selectedClothes[item.id]}}" 
            class="selected-item" data-id="{{item.id}}" data-category="{{item.category}}"
            bindtap="selectClothing">
        <image src="{{item.image_url}}" mode="aspectFill" class="selected-item-img"></image>
        <view class="remove-icon">
          <image src="/images/remove.png" mode="aspectFit" class="remove-img"></image>
        </view>
      </view>
    </view>
  </view>
  
  <view class="bottom-actions">
    <view class="nav-back-btn" bindtap="navigateBack">返回</view>
    <view class="proceed-btn {{canProceed ? 'active' : 'disabled'}}" bindtap="proceedToSelectPhoto">
      <text class="next-icon">→</text> 选择照片
    </view>
  </view>
</view>

<!-- 裙子搭配提示弹框 -->
<view class="skirt-tip-modal" wx:if="{{showSkirtTip}}">
  <view class="skirt-tip-content">
    <view class="skirt-tip-title">裙子搭配提示</view>
    <view class="skirt-tip-text">
      <view>• 选择<text class="highlight">全身裙</text>时，建议不要再搭配其他衣物，以获得最佳试穿效果</view>
      <view>• 选择<text class="highlight">短裙/半身裙</text>时，可以搭配上衣来完成整体造型</view>
    </view>
    <view class="skirt-tip-btn" bindtap="closeSkirtTip">我明白了</view>
  </view>
</view> 