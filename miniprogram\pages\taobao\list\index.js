const app = getApp();
const API_BASE_URL = 'https://cyyg.alidog.cn'; // 替换为你的API地址

Page({
  data: {
    keyword: '',
    products: [],
    loading: false,
    currentPage: 1,
    hasMore: true,
    activeTabIndex: 0,
    tabs: ['优惠推荐', '穿搭推荐'] 
  },

  onLoad: function (options) {
    if (options.keyword) {
      this.setData({
        keyword: options.keyword
      });
    }
    this.loadProducts(true);
  },
  
  // 搜索商品
  onSearch: function(e) {
    const keyword = e.detail.value;
    this.setData({
      keyword: keyword,
      currentPage: 1,
      products: [],
      hasMore: true
    });
    this.loadProducts(true);
  },
  
  // 切换标签
  onTabChange: function(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      activeTabIndex: index,
      products: [],
      currentPage: 1,
      hasMore: true
    });
    this.loadProducts(true);
  },

  // 加载淘宝商品数据
  loadProducts: function (isRefresh = false) {
    if (this.data.loading || (!this.data.hasMore && !isRefresh)) {
      return;
    }

    if (isRefresh) {
      this.setData({
        currentPage: 1,
        products: [],
        hasMore: true
      });
    }

    this.setData({ loading: true });

    wx.request({
      url: `${API_BASE_URL}/login_backend/get_taobao_products.php`,
      data: {
        keyword: this.data.keyword,
        page: this.data.currentPage,
        page_size: 10
      },
      success: (res) => {
        console.log('淘宝API响应:', res.data);
        
        if (res.data && res.data.error === false) {
          const newProducts = res.data.data || [];
          const pagination = res.data.pagination || {};
          
          // 检查是否有更多数据
          const hasMore = this.data.currentPage < pagination.total_pages;
          
          // 更新数据
          this.setData({
            products: [...this.data.products, ...newProducts],
            hasMore: hasMore,
            currentPage: this.data.currentPage + 1
          });
        } else {
          wx.showToast({
            title: res.data?.msg || '获取数据失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ loading: false });
        wx.stopPullDownRefresh();
      }
    });
  },

  // 查看商品详情
  viewProductDetail: function (e) {
    const productId = e.currentTarget.dataset.id;
    const productIndex = e.currentTarget.dataset.index;
    const product = this.data.products[productIndex];
    
    if (!product) {
      wx.showToast({
        title: '商品信息不存在',
        icon: 'none'
      });
      return;
    }
    
    // 将商品信息存入本地缓存，以备他用
    wx.setStorageSync('currentProduct', product);
    console.log('商品信息已缓存，准备获取购买链接');
    
    // 由于页面跳转问题，直接获取并复制淘宝链接
    this.copyCouponLink(product);
  },
  
  // 复制优惠券链接
  copyCouponLink: function(product) {
    if (!product) {
      wx.showToast({
        title: '商品信息不存在',
        icon: 'none'
      });
      return;
    }
    
    // 获取商品ID
    let itemId = product.id;
    if (typeof product.id === 'object' && product.id[0]) {
      itemId = product.id[0];
    }
    
    // 优先使用优惠券链接
    if (product.coupon_click_url) {
      let validUrl = product.coupon_click_url;
      if (!validUrl.startsWith('http')) {
        validUrl = 'https:' + validUrl;
      }
      // 将获得的链接转换为淘口令
      this.convertLinkToTpwd(validUrl, itemId);
      return;
    }
    
    // 备用链接选项
    const backupUrl = product.item_url || product.url;
    if (backupUrl) {
      let validUrl = backupUrl;
      if (!validUrl.startsWith('http')) {
        validUrl = 'https:' + validUrl;
      }
      // 将获得的链接转换为淘口令
      this.convertLinkToTpwd(validUrl, itemId);
      return;
    }
    
    // 提取商品数字ID
    let numericId = itemId;
    if (typeof product.id === 'string' || typeof product.id === 'object') {
      // 如果ID不是数字，尝试从item_url中提取
      if (product.item_url) {
        const idMatch = product.item_url.match(/id=(\d+)/);
        if (idMatch && idMatch[1]) {
          numericId = idMatch[1];
          console.log('从URL提取的商品ID:', numericId);
        }
      }
    }
    
    // 尝试通过API获取链接
    this.getProductLinkByAPI(numericId);
  },
  
  // 通过API获取淘宝链接
  getProductLinkByAPI: function(itemId) {
    wx.showLoading({
      title: '获取淘口令中...'
    });
    
    wx.request({
      url: `${API_BASE_URL}/login_backend/get_taobao_link.php`,
      method: 'GET',
      data: {
        item_id: itemId,
        need_tpwd: 1  // 请求淘口令
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          // 优先使用淘口令，其次使用普通链接
          const link = res.data.data.tpwd || res.data.data.model || res.data.data.promotion_url;
          
          if (link) {
            this.copyUrlToClipboard(link, link.includes('￥') || link.includes('¥'));
          } else {
            wx.showToast({
              title: '获取商品链接失败',
              icon: 'none',
              duration: 2000
            });
          }
        } else {
          wx.showToast({
            title: '获取商品链接失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 新增：将链接转换为淘口令
  convertLinkToTpwd: function(url, itemId) {
    console.log('开始转换链接为淘口令:', url);
    
    wx.showLoading({
      title: '生成淘口令中...'
    });
    
    // 直接使用专用的淘口令转换API
    wx.request({
      url: `${API_BASE_URL}/login_backend/convert_to_tpwd.php`,
      method: 'POST',
      data: {
        url: url,
        item_id: itemId,
        text: '好物推荐'  // 淘口令文案
      },
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();
        console.log('淘口令转换响应:', res.data);
        
        if (res.statusCode === 200 && !res.data.error && res.data.data) {
          // 获取淘口令
          const tpwd = res.data.data.model;
          
          if (tpwd && (tpwd.includes('￥') || tpwd.includes('¥'))) {
            console.log('成功获取到淘口令');
            this.copyUrlToClipboard(tpwd, true);
          } else {
            console.log('未获取到淘口令，使用普通链接');
            // 备用: 使用原始链接
            this.copyUrlToClipboard(url);
          }
        } else {
          // 如果API调用失败或未返回淘口令，使用备用方法
          console.error('转换淘口令失败:', res.data);
          this.getProductLinkByAPI(itemId);
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('转换淘口令请求失败:', err);
        // 请求失败，尝试备用方法
        this.getProductLinkByAPI(itemId);
      }
    });
  },
  
  // 复制URL到剪贴板
  copyUrlToClipboard: function(url, isTpwd = false) {
    wx.setClipboardData({
      data: url,
      success: () => {
        let toastMsg = '链接已复制，请打开淘宝APP';
        
        // 如果是淘口令，使用特定的提示
        if (isTpwd || url.includes('￥') || url.includes('¥')) {
          toastMsg = '淘口令已复制，请打开淘宝APP粘贴';
        }
        
        wx.showToast({
          title: toastMsg,
          icon: 'none',
          duration: 2500
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制链接失败',
          icon: 'none'
        });
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadProducts(true);
  },

  // 触底加载更多
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.loadProducts();
    }
  }
}); 