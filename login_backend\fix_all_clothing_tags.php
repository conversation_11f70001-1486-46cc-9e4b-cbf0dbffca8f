<?php
/**
 * 修复所有衣物标签格式 (一次性修复脚本)
 * 
 * 清理所有衣物标签中的不规范格式，确保标签为纯文本逗号分隔格式
 * 
 * 使用方法: 通过命令行或浏览器运行此脚本
 */

// 增加执行时间，防止大量数据处理超时
set_time_limit(600);
ini_set('max_execution_time', 600);
ini_set('memory_limit', '512M');

// 加载配置和数据库连接
require_once 'config.php';
require_once 'db.php';

// 清洗标签函数，确保标签格式统一
function cleanTags($tags) {
    if (is_array($tags)) {
        // 如果是数组，确保每个元素都是纯文本
        $cleanedTags = [];
        foreach ($tags as $tag) {
            if (is_array($tag)) {
                // 递归处理嵌套数组
                $subTags = cleanTags($tag);
                $cleanedTags = array_merge($cleanedTags, $subTags);
                continue;
            }
            
            $tag = trim($tag);
            if (!empty($tag)) {
                // 排除任何可能的JSON格式或包含特殊字符的标签
                if (strpos($tag, '[') === false && strpos($tag, ']') === false && 
                    strpos($tag, '{') === false && strpos($tag, '}') === false &&
                    strpos($tag, '"') === false && strpos($tag, "'") === false) {
                    $cleanedTags[] = $tag;
                } else {
                    // 尝试清理JSON格式标签
                    $tag = str_replace(['[', ']', '"', "'", '{', '}'], '', $tag);
                    if (!empty(trim($tag))) {
                        $cleanedTags[] = trim($tag);
                    }
                }
            }
        }
        return $cleanedTags;
    } elseif (is_string($tags)) {
        // 如果是字符串，先尝试解析JSON
        $firstChar = substr($tags, 0, 1);
        if ($firstChar === '[' || $firstChar === '{') {
            try {
                $jsonTags = json_decode($tags, true);
                if (is_array($jsonTags)) {
                    return cleanTags($jsonTags);
                }
            } catch (Exception $e) {
                // JSON解析失败，当作普通字符串继续处理
            }
        }
        
        // 移除可能的JSON数组部分（处理混合格式）
        // 例如: ["标签1","标签2"],标签3,标签4
        $cleanedStr = preg_replace('/\[.*?\]/', '', $tags);
        
        // 分割成数组并清理每个标签
        $tagArray = explode(',', $cleanedStr);
        $cleanedArray = [];
        
        foreach ($tagArray as $tag) {
            $tag = trim($tag);
            // 再次清理可能的JSON字符
            $tag = str_replace(['[', ']', '"', "'", '{', '}'], '', $tag);
            if (!empty($tag)) {
                $cleanedArray[] = $tag;
            }
        }
        
        return $cleanedArray;
    }
    
    return [];
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 获取所有非空标签的衣物
    $stmt = $conn->prepare("SELECT id, user_id, tags FROM clothes WHERE tags IS NOT NULL AND tags != ''");
    $stmt->execute();
    $clothes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $totalCount = count($clothes);
    $updatedCount = 0;
    $skippedCount = 0;
    
    echo "开始处理 {$totalCount} 件衣物的标签...<br>\n";
    
    foreach ($clothes as $clothing) {
        try {
            $originalTags = $clothing['tags'];
            $cleanedTags = cleanTags($originalTags);
            
            // 只有当清理后的标签与原标签不同时才更新
            if ($cleanedTags) {
                $tagsString = implode(',', $cleanedTags);
                
                if ($tagsString !== $originalTags) {
                    $updateStmt = $conn->prepare("UPDATE clothes SET tags = :tags, updated_at = NOW() WHERE id = :id");
                    $updateStmt->bindParam(':tags', $tagsString);
                    $updateStmt->bindParam(':id', $clothing['id']);
                    $updateStmt->execute();
                    $updatedCount++;
                } else {
                    $skippedCount++;
                }
            } else {
                $skippedCount++;
            }
        } catch (Exception $e) {
            echo "处理衣物ID {$clothing['id']} 出错: {$e->getMessage()}<br>\n";
            $skippedCount++;
        }
        
        // 每处理100件衣物输出一次进度
        if (($updatedCount + $skippedCount) % 100 === 0) {
            echo "已处理: " . ($updatedCount + $skippedCount) . " / {$totalCount} 件衣物<br>\n";
            // 刷新输出缓冲区
            if (function_exists('ob_flush')) {
                ob_flush();
                flush();
            }
        }
    }
    
    echo "<br>\n处理完成：<br>\n";
    echo "总衣物数: {$totalCount}<br>\n";
    echo "已更新数: {$updatedCount}<br>\n";
    echo "跳过数: {$skippedCount}<br>\n";
    
} catch (Exception $e) {
    echo "脚本执行出错: {$e->getMessage()}<br>\n";
} 