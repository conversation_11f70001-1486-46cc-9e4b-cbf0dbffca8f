-- 创建穿搭评分表
CREATE TABLE `outfit_ratings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `photo_url` text NOT NULL COMMENT '穿搭照片URL',
  `score` decimal(3,1) NOT NULL DEFAULT '0.0' COMMENT '总体评分(1-10分)',
  `rating_details` text NOT NULL COMMENT '评分详情JSON',
  `ai_comments` text COMMENT 'AI评语',
  `improvement_suggestions` text COMMENT '改进建议',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_outfit_ratings_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='穿搭评分表'; 