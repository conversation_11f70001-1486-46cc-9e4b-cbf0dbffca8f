const app = getApp();

Page({
  data: {
    outfitsList: [],
    loading: true,
    saving: false,
    originalOrder: [] // 保存原始顺序，用于比较是否有变化
  },

  onLoad() {
    console.log('穿搭排序页面开始加载');
    try {
      this.loadOutfits();
    } catch (error) {
      console.error('页面加载出错:', error);
      wx.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
      wx.navigateBack();
    }
  },

  // 加载穿搭数据
  loadOutfits() {
    console.log('开始加载穿搭数据');
    this.setData({ loading: true });
    
    // 优先使用globalData中的token，如果没有再从本地存储获取
    let token = app.globalData.token;
    if (!token) {
      token = wx.getStorageSync('token');
    }
    
    console.log('app.globalData.token:', app.globalData.token ? '存在' : '不存在');
    console.log('本地存储token:', wx.getStorageSync('token') ? '存在' : '不存在');
    console.log('最终使用的token:', token ? '存在' : '不存在');
    console.log('用户登录状态:', app.globalData.isLoggedIn);
    console.log('是否体验账号:', app.globalData.useMockUser);
    
    if (!token) {
      console.log('token不存在，返回登录');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    // 构建API请求参数
    let apiUrl = `${app.globalData.apiBaseUrl}/get_outfits.php`;
    let params = {
      page: 1,
      per_page: 100 // 获取更多穿搭用于排序
    };

    // 构建查询字符串
    const queryString = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&');
    const fullUrl = `${apiUrl}?${queryString}`;

    console.log('请求URL:', fullUrl);
    console.log('请求参数:', params);

    wx.request({
      url: fullUrl,
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + token
      },
      success: (res) => {
        console.log('网络请求成功，响应数据:', res);
        
        if (res.data && res.data.success) {
          console.log('获取到穿搭数据，数量:', res.data.data ? res.data.data.length : 0);
          
          // 转换字段名称并添加排序字段
          const outfits = res.data.data.map(item => {
            console.log('原始穿搭数据项:', item);
            return {
              id: item.id,
              name: item.name,
              description: item.description,
              thumbnail: item.thumbnail,
              category_id: item.category_id,
              category_name: item.category_name,
              created_at: item.created_at,
              updated_at: item.updated_at,
              sortOrder: item.sort_order || item.id
            };
          });

          console.log('转换后的穿搭数据:', outfits);
          console.log('第一个穿搭的thumbnail:', outfits[0]?.thumbnail);
          
          // 按sort_order排序
          const sortedOutfits = outfits.sort((a, b) => {
            return a.sortOrder - b.sortOrder;
          });
          
          console.log('排序后的穿搭数据:', sortedOutfits);
          
          this.setData({
            outfitsList: sortedOutfits,
            originalOrder: sortedOutfits.map(item => item.id),
            loading: false
          });
          
          console.log('数据设置完成');
        } else {
          wx.showToast({
            title: res.data?.message || '加载失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  // 向上移动
  moveUp(e) {
    const index = e.currentTarget.dataset.index;
    if (index === 0) return;
    
    const outfitsList = [...this.data.outfitsList];
    const item = outfitsList[index];
    outfitsList.splice(index, 1);
    outfitsList.splice(index - 1, 0, item);
    
    this.setData({ outfitsList });
  },

  // 向下移动
  moveDown(e) {
    const index = e.currentTarget.dataset.index;
    if (index === this.data.outfitsList.length - 1) return;
    
    const outfitsList = [...this.data.outfitsList];
    const item = outfitsList[index];
    outfitsList.splice(index, 1);
    outfitsList.splice(index + 1, 0, item);
    
    this.setData({ outfitsList });
  },

  // 保存排序
  saveSortOrder() {
    const currentOrder = this.data.outfitsList.map(item => item.id);
    
    // 检查是否有变化
    if (JSON.stringify(currentOrder) === JSON.stringify(this.data.originalOrder)) {
      wx.showToast({
        title: '排序未发生变化',
        icon: 'none'
      });
      return;
    }

    this.setData({ saving: true });
    
    // 优先使用globalData中的token，如果没有再从本地存储获取
    let token = app.globalData.token;
    if (!token) {
      token = wx.getStorageSync('token');
    }
    
    wx.request({
      url: `${app.globalData.apiBaseUrl}/update_outfits_sort_order.php`,
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
      },
      data: {
        outfits_order: currentOrder
      },
      success: (res) => {
        if (res.data && !res.data.error) {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          });
          
          // 更新原始顺序
          this.setData({
            originalOrder: currentOrder,
            saving: false
          });
          
          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.data?.message || '保存失败',
            icon: 'none'
          });
          this.setData({ saving: false });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({ saving: false });
      }
    });
  }
});
