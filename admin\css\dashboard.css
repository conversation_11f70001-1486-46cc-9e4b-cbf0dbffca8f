/* 仪表盘特定样式 */
.chart-tooltip {
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 10px;
    border-radius: 4px;
    font-size: 14px;
    pointer-events: none;
}

/* 为统计卡片添加特定颜色 */
.stats-card.users {
    border-left: 4px solid #1890ff;
}

.stats-card.clothes {
    border-left: 4px solid #52c41a;
}

.stats-card.photos {
    border-left: 4px solid #faad14;
}

.stats-card.try-ons {
    border-left: 4px solid #eb2f96;
}

/* 为统计卡片添加图标区域 */
.stats-card-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin: 0 auto 10px;
    background-color: rgba(24, 144, 255, 0.1);
    color: #1890ff;
}

.stats-card.users .stats-card-icon {
    background-color: rgba(24, 144, 255, 0.1);
    color: #1890ff;
}

.stats-card.clothes .stats-card-icon {
    background-color: rgba(82, 196, 26, 0.1);
    color: #52c41a;
}

.stats-card.photos .stats-card-icon {
    background-color: rgba(250, 173, 20, 0.1);
    color: #faad14;
}

.stats-card.try-ons .stats-card-icon {
    background-color: rgba(235, 47, 150, 0.1);
    color: #eb2f96;
}

/* API配额样式增强 */
.quota-item .warning {
    color: #faad14;
}

.quota-item .critical {
    color: #f5222d;
}

.progress-fill.warning {
    background-color: #faad14;
}

.progress-fill.critical {
    background-color: #f5222d;
}

/* 图表标题 */
.chart-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

/* 图表注释 */
.chart-legend {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 10px;
}

.legend-item {
    display: flex;
    align-items: center;
    margin: 0 10px 5px 0;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 5px;
}

.legend-label {
    font-size: 12px;
    color: #666;
}

/* 数据过滤控件 */
.chart-filters {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.filter-select {
    padding: 5px 10px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    margin: 0 5px;
    font-size: 14px;
}

/* 图表信息卡片 */
.data-insight {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    padding: 10px 15px;
    border-radius: 4px;
    margin-top: 10px;
    font-size: 14px;
    color: #52c41a;
}

.data-insight.warning {
    background-color: #fffbe6;
    border-color: #ffe58f;
    color: #faad14;
}

.data-insight.danger {
    background-color: #fff2f0;
    border-color: #ffccc7;
    color: #f5222d;
}

/* 图表加载动画 */
.chart-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(24, 144, 255, 0.1);
    border-radius: 50%;
    border-top-color: #1890ff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .chart-filters {
        flex-direction: column;
        align-items: center;
    }
    
    .filter-select {
        margin: 5px 0;
        width: 100%;
        max-width: 200px;
    }
} 