const app = getApp();
// 引入每日配额管理工具
const dailyQuota = require('../../utils/dailyQuota');

// 定义推荐功能名称常量
const FEATURE_NAME = 'weather_outfit_recommendation';

Page({
  data: {
    loading: true,
    weather: null,
    outfit: null,
    refreshing: false,
    hasUserInfo: false,
    isUsingMockUser: false,
    recommendedProducts: [],
    productsLoading: true,
    isRequestingRecommendation: false,
    // 新增属性，用于控制分享提示弹窗
    showShareTip: false,
    // 剩余可用次数
    availableQuota: 0
  },
  
  onLoad: function(options) {
    // 检查是否是从分享链接进入
    if (options.source === 'outfit_share') {
      // 是分享链接进入，直接跳转到首页
      wx.switchTab({
        url: '/pages/index/index'
      });
      return; // 不继续执行后面的代码
    }
    
    // 检查登录状态
    this.checkLoginStatus();
    
    // 获取推荐，强制刷新
    this.getRecommendation(true);
  },
  
  // 页面显示时触发
  onShow: function() {
    // 更新可用次数状态
    this.updateQuotaStatus();
    
    // 如果已经登录，则刷新数据
    if (this.data.hasUserInfo) {
      // 检查是否在请求中或刚刚加载完成 - 防止重复请求
      if (!this.data.isRequestingRecommendation && !this.data.loading) {
        this.getRecommendation(true);
      }
    }
  },
  
  // 检查用户登录状态
  checkLoginStatus: function() {
    // 如果已登录，则设置用户信息
    if (app.globalData.token) {
      if (app.globalData.userInfo) {
        // 检查是否使用体验账号
        const isUsingMockUser = app.globalData.useMockUser === true;
        
        this.setData({
          hasUserInfo: true,
          isUsingMockUser: isUsingMockUser
        });
      }
    } else {
      this.setData({
        hasUserInfo: false,
        isUsingMockUser: false
      });
    }
  },
  
  // 检查定位权限并获取位置
  checkLocationAuth: function() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => {
          console.log("授权设置:", res.authSetting);
          // 如果未授权位置信息
          if (!res.authSetting['scope.userLocation']) {
            wx.showModal({
              title: '需要位置权限',
              content: '为了提供精准的天气和穿搭推荐，需要获取您的位置信息',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  // 用户点击确定，主动请求授权
                  wx.authorize({
                    scope: 'scope.userLocation',
                    success: () => {
                      // 授权成功后获取位置
                      this.getLocation().then(resolve).catch(reject);
                    },
                    fail: (err) => {
                      console.error("位置授权失败:", err);
                      // 引导用户去设置页面开启
                      wx.showModal({
                        title: '授权失败',
                        content: '请在设置中开启位置权限',
                        confirmText: '去设置',
                        success: (innerModalRes) => {
                          if (innerModalRes.confirm) {
                            wx.openSetting();
                          }
                          // 无论用户是否去设置，都返回null作为位置信息
                          resolve(null);
                        }
                      });
                    }
                  });
                } else {
                  // 用户点击取消
                  resolve(null);
                }
              }
            });
          } else {
            // 已授权，直接获取位置
            this.getLocation().then(resolve).catch(reject);
          }
        },
        fail: (err) => {
          console.error("获取设置失败:", err);
          resolve(null);
        }
      });
    });
  },
  
  // 获取位置信息
  getLocation: function() {
    return new Promise((resolve, reject) => {
      // 先尝试获取精准定位
      wx.getLocation({
        type: 'gcj02',
        altitude: true, // 开启高精度定位
        isHighAccuracy: true, // 高精度模式
        highAccuracyExpireTime: 5000, // 高精度有效期5秒
        success: (res) => {
          console.log("获取精准位置成功:", res);
          
          // 缓存最新位置
          try {
            // 保存到本地存储
            wx.setStorageSync('lastLocation', {
              latitude: res.latitude,
              longitude: res.longitude,
              timestamp: Date.now()
            });
            
            // 新增：保存到全局变量，确保其他函数可以使用相同的位置信息
            if (app.globalData) {
              app.globalData.userLocation = {
                latitude: res.latitude,
                longitude: res.longitude,
                timestamp: Date.now()
              };
              console.log("位置已保存到全局变量:", app.globalData.userLocation);
            }
          } catch (e) {
            console.error("缓存位置失败:", e);
          }
          
          resolve(res);
        },
        fail: (err) => {
          console.warn("获取精准位置失败，尝试普通定位:", err);
          
          // 如果精确定位失败，尝试普通定位
          wx.getLocation({
            type: 'gcj02',
            success: (res) => {
              console.log("获取普通位置成功:", res);
              
              // 缓存最新位置
              try {
                // 保存到本地存储
                wx.setStorageSync('lastLocation', {
                  latitude: res.latitude,
                  longitude: res.longitude,
                  timestamp: Date.now()
                });
                
                // 新增：保存到全局变量，确保其他函数可以使用相同的位置信息
                if (app.globalData) {
                  app.globalData.userLocation = {
                    latitude: res.latitude,
                    longitude: res.longitude,
                    timestamp: Date.now()
                  };
                  console.log("位置已保存到全局变量:", app.globalData.userLocation);
                }
              } catch (e) {
                console.error("缓存位置失败:", e);
              }
              
              resolve(res);
            },
            fail: (err) => {
              console.error("获取位置彻底失败:", err);
              
              // 尝试从缓存读取上次位置
              try {
                const lastLocation = wx.getStorageSync('lastLocation');
                if (lastLocation && lastLocation.latitude && lastLocation.longitude) {
                  console.log("使用缓存位置:", lastLocation);
                  
                  // 如果缓存的位置不超过24小时，可以使用
                  const now = Date.now();
                  const lastTime = lastLocation.timestamp || 0;
                  if (now - lastTime < 24 * 60 * 60 * 1000) {
                    return resolve({
                      latitude: lastLocation.latitude,
                      longitude: lastLocation.longitude,
                      isFromCache: true
                    });
                  }
                }
              } catch (e) {
                console.error("读取缓存位置失败:", e);
              }
              
              // 显示错误信息
              wx.showToast({
                title: '获取位置失败，请检查授权',
                icon: 'none'
              });
              reject(err);
            }
          });
        }
      });
    });
  },
  
  // 获取穿搭推荐
  getRecommendation: function(refresh = false) {
    // 检查是否已经有请求在进行中，如果有则不再发起新请求
    if (this.data.isRequestingRecommendation) {
      console.log("已有推荐请求正在进行中，忽略重复请求");
      return;
    }
    
    // 如果是强制刷新（换一批），检查配额
    if (refresh) {
      // 检查用户是否还有可用次数
      if (!dailyQuota.hasAvailableQuota(FEATURE_NAME)) {
        console.log("用户今日推荐次数已用完");
        // 显示分享提示
        this.setData({
          showShareTip: true
        });
        return;
      }
    }
    
    // 设置请求锁定状态
    this.setData({
      loading: true,
      isRequestingRecommendation: true
    });
    
    // 先获取用户位置
    this.checkLocationAuth().then((locationRes) => {
      if (locationRes) {
        const { latitude, longitude } = locationRes;
        console.log("使用位置获取天气:", latitude, longitude);
        this.getWeatherAndRecommendation(latitude, longitude, refresh);
        
        // 同时获取商品推荐
        this.getWeatherProductRecommendation(latitude, longitude, refresh);
      } else {
        console.warn("无法获取位置，使用默认位置");
        this.getWeatherAndRecommendation(null, null, refresh);
        
        // 同时获取商品推荐
        this.getWeatherProductRecommendation(null, null, refresh);
      }
    }).catch((err) => {
      console.error("位置权限检查失败:", err);
      this.getWeatherAndRecommendation(null, null, refresh);
      
      // 同时获取商品推荐
      this.getWeatherProductRecommendation(null, null, refresh);
      
      // 释放请求锁定状态
      this.setData({
        isRequestingRecommendation: false
      });
    });
  },
  
  // 获取天气和推荐
  getWeatherAndRecommendation: function(latitude, longitude, refresh = false) {
    // 使用新的API路径
    let apiUrl = `${app.globalData.apiBaseUrl}/get_weather_recommendation.php`;
    
    let params = [];
    if (refresh) {
      params.push('refresh=1');
    }
    
    // 始终添加时间戳参数，确保不使用缓存
    params.push(`_nocache=1`);
    params.push(`t=${Date.now()}`);
    
    // 如果有经纬度，添加到请求参数中并设置标志
    let hasLocation = false;
    if (latitude && longitude) {
      // 标准参数：使用latitude和longitude参数
      params.push(`latitude=${latitude}`);
      params.push(`longitude=${longitude}`);
      
      // 新增：同时添加location参数作为备用
      params.push(`location=${longitude},${latitude}`);
      
      hasLocation = true;
      
      // 详细日志
      console.log("发送位置参数:", {
        latitude: latitude,
        longitude: longitude,
        location: `${longitude},${latitude}`
      });
      
      // 显示定位中的状态
      this.setData({
        weather: {
          ...(this.data.weather || {}),
          city: '定位中...',
          windDir: this.data.weather?.windDir || '未知',
          windScale: this.data.weather?.windScale || '--',
          humidity: this.data.weather?.humidity || '--'
        }
      });
      
      // 确保初始化临时weatherData对象，避免传递undefined
      let tempWeatherData = {
        city: '定位中...',
        windDir: this.data.weather?.windDir || '未知',
        windScale: this.data.weather?.windScale || '--',
        humidity: this.data.weather?.humidity || '--',
        ...(this.data.weather || {})
      };
      
      // 同时直接请求城市信息，确保显示准确的城市名称
      this.tryGetLocalCityName(latitude, longitude, tempWeatherData);
    } else {
      // 尝试从全局变量获取位置
      if (app.globalData && app.globalData.userLocation) {
        const userLocation = app.globalData.userLocation;
        
        // 检查位置是否过期（24小时）
        const now = Date.now();
        const locationTime = userLocation.timestamp || 0;
        if (now - locationTime < 24 * 60 * 60 * 1000) {
          console.log("使用全局缓存的位置信息:", userLocation);
          
          // 标准参数：使用latitude和longitude参数
          params.push(`latitude=${userLocation.latitude}`);
          params.push(`longitude=${userLocation.longitude}`);
          
          // 新增：同时添加location参数作为备用
          params.push(`location=${userLocation.longitude},${userLocation.latitude}`);
          
          hasLocation = true;
          
          // 详细日志
          console.log("从全局变量发送位置参数:", {
            latitude: userLocation.latitude,
            longitude: userLocation.longitude,
            location: `${userLocation.longitude},${userLocation.latitude}`
          });
          
          // 同时直接请求城市信息，确保显示准确的城市名称
          this.tryGetLocalCityName(userLocation.latitude, userLocation.longitude, null);
        } else {
          console.log("全局位置信息已过期");
        }
      }
      
      // 没有经纬度，显示提示信息
      if (!hasLocation) {
        wx.showToast({
          title: '无法获取位置，将使用默认城市',
          icon: 'none',
          duration: 2000
        });
      }
      
      // 显示城市获取中状态
      this.setData({
        weather: {
          ...(this.data.weather || {}),
          city: '获取城市中...',
          windDir: this.data.weather?.windDir || '未知',
          windScale: this.data.weather?.windScale || '--',
          humidity: this.data.weather?.humidity || '--'
        }
      });
    }
    
    // 构建URL
    if (params.length > 0) {
      apiUrl += '?' + params.join('&');
    }
    
    console.log("请求穿搭API:", apiUrl);
    
    // 使用小程序请求API
    wx.request({
      url: apiUrl,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log("穿搭推荐返回:", res.data);
        
        // 调试打印API响应结构
        this.debugObj(res.data, '穿搭API响应');
        
        if (res.data && res.data.status === 'success') {
          let weatherData;
          let recommendation;
          
          // 检查数据结构
          if (res.data.recommendation) {
            // 标准格式
            recommendation = res.data.recommendation;
            weatherData = recommendation.weather_data || {};
            console.log("标准格式天气数据:", weatherData);
          } else if (res.data.weather_data) {
            // 直接包含天气数据的格式
            weatherData = res.data.weather_data;
            recommendation = res.data;
            console.log("直接包含天气数据格式:", weatherData);
          } else {
            // 使用响应中的weather字段
            weatherData = res.data.weather || {};
            recommendation = res.data;
            console.log("使用weather字段格式:", weatherData);
          }
          
          // 记录天气数据
          console.log("处理后的天气数据:", weatherData);
          console.log("天气数据类型:", typeof weatherData);
          console.log("天气数据包含的字段:", Object.keys(weatherData));
          
          // 构建天气对象
          const weather = {
            city: weatherData.city || '未知城市',
            text: weatherData.text || '未知',
            temp: weatherData.temp || '--',
            feelsLike: weatherData.feelsLike || '--',
            windDir: weatherData.windDir || '未知',
            windScale: weatherData.windScale || '--',
            humidity: weatherData.humidity || '--'
          };
          
          console.log("设置天气数据:", weather);
          
          // 添加位置来源标记
          if (!hasLocation && weather.city) {
            weather.city += ' (非实时位置)';
          }
          
          this.setData({
            weather: weather,
            outfit: recommendation,
            loading: false,
            refreshing: false,
            isRequestingRecommendation: false
          });
          
          // 如果是刷新请求，消耗一次配额
          if (refresh) {
            dailyQuota.useQuota(FEATURE_NAME);
            // 更新可用次数状态
            this.updateQuotaStatus();
          }
          
          // 记录设置后的数据
          console.log("设置后的天气数据:", this.data.weather);
        } else {
          // 显示错误信息
          this.showErrorMessage(res.data?.message || '获取穿搭推荐失败');
          
          // 还原UI状态
          this.setData({
            loading: false,
            refreshing: false,
            isRequestingRecommendation: false
          });
        }
      },
      fail: (err) => {
        console.error("请求穿搭API失败:", err);
        this.showErrorMessage('网络错误，请稍后再试');
        
        // 还原UI状态
        this.setData({
          loading: false,
          refreshing: false,
          isRequestingRecommendation: false
        });
      }
    });
  },
  
  // 新增：获取天气商品推荐
  getWeatherProductRecommendation: function(latitude, longitude, refresh = false) {
    this.setData({
      productsLoading: true
    });
    
    // 构建API URL
    let apiUrl = `${app.globalData.apiBaseUrl}/get_weather_product_recommendations.php`;
    
    let params = [];
    if (refresh) {
      params.push('refresh=1');
    }
    
    // 始终添加时间戳参数，确保不使用缓存
    params.push(`_nocache=1`);
    params.push(`t=${Date.now()}`);
    
    // 添加经纬度参数
    let hasLocation = false;
    if (latitude && longitude) {
      // 标准参数：使用latitude和longitude参数
      params.push(`latitude=${latitude}`);
      params.push(`longitude=${longitude}`);
      
      // 新增：同时添加location参数作为备用
      params.push(`location=${longitude},${latitude}`);
      
      hasLocation = true;
      
      // 详细日志
      console.log("商品推荐API - 发送位置参数:", {
        latitude: latitude,
        longitude: longitude,
        location: `${longitude},${latitude}`
      });
    } else {
      // 尝试从全局变量获取位置
      if (app.globalData && app.globalData.userLocation) {
        const userLocation = app.globalData.userLocation;
        
        // 检查位置是否过期（24小时）
        const now = Date.now();
        const locationTime = userLocation.timestamp || 0;
        if (now - locationTime < 24 * 60 * 60 * 1000) {
          console.log("商品推荐API - 使用全局缓存的位置信息:", userLocation);
          
          // 标准参数：使用latitude和longitude参数
          params.push(`latitude=${userLocation.latitude}`);
          params.push(`longitude=${userLocation.longitude}`);
          
          // 新增：同时添加location参数作为备用
          params.push(`location=${userLocation.longitude},${userLocation.latitude}`);
          
          hasLocation = true;
          
          // 详细日志
          console.log("商品推荐API - 从全局变量发送位置参数:", {
            latitude: userLocation.latitude,
            longitude: userLocation.longitude,
            location: `${userLocation.longitude},${userLocation.latitude}`
          });
        } else {
          console.log("商品推荐API - 全局位置信息已过期");
        }
      }
    }
    
    // 限制返回的商品数量，增加到10个以便有足够的筛选空间
    params.push('limit=10'); // 最多返回10个候选商品
    
    // 构建URL
    if (params.length > 0) {
      apiUrl += '?' + params.join('&');
    }
    
    console.log("请求商品推荐API:", apiUrl);
    
    // 使用小程序请求API
    wx.request({
      url: apiUrl,
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log("商品推荐返回:", res.data);
        
        // 调试打印商品API响应结构
        this.debugObj(res.data, '商品API响应');
        
        if (res.data && res.data.status === 'success' && res.data.recommended_products) {
          let products = res.data.recommended_products;
          console.log("获取到的商品数量:", products.length);
          
          // 筛选有真实淘口令的商品
          let filteredProducts = products.filter(product => {
            // 检查是否有淘口令且is_fake_tpwd为0
            return product.tpwd && (product.is_fake_tpwd === 0 || product.is_fake_tpwd === '0');
          });
          
          console.log("筛选后的商品数量:", filteredProducts.length);
          
          // 设置商品推荐数据
          this.setData({
            recommendedProducts: filteredProducts,
            productsLoading: false,
            isRequestingRecommendation: false // 完成商品请求，释放锁定状态
          }, () => {
            // 设置完成回调，确认数据已更新
            console.log("设置后的商品数据:", this.data.recommendedProducts.length, "项");
          });
          
          // 更新天气信息（如果有）
          if (res.data.weather && !this.data.weather) {
            console.log("从商品推荐API更新天气信息:", res.data.weather);
            this.setData({
              weather: res.data.weather
            });
          }
        } else {
          // 显示错误信息
          console.error("获取商品推荐失败:", res.data?.message || '未知错误');
          
          // 还原UI状态
          this.setData({
            recommendedProducts: [],
            productsLoading: false,
            isRequestingRecommendation: false // 出错时也要释放锁定状态
          });
        }
      },
      fail: (err) => {
        console.error("请求商品推荐API失败:", err);
        
        // 还原UI状态
        this.setData({
          recommendedProducts: [],
          productsLoading: false,
          isRequestingRecommendation: false // 请求失败时释放锁定状态
        });
      }
    });
  },
  
  // 刷新穿搭推荐
  refreshRecommendation: function() {
    if (this.data.refreshing || this.data.isRequestingRecommendation) return; // 防止重复刷新
    
    // 检查用户是否还有可用次数
    if (!dailyQuota.hasAvailableQuota(FEATURE_NAME)) {
      console.log("用户今日推荐次数已用完");
      // 显示分享提示
      this.setData({
        showShareTip: true
      });
      return;
    }
    
    this.setData({
      refreshing: true
    });
    
    // 重新获取推荐，强制刷新
    this.getRecommendation(true);
  },
  
  // 尝试获取本地城市名称
  tryGetLocalCityName: function(latitude, longitude, weatherData = null) {
    if (!latitude || !longitude) {
      console.warn("无法获取本地城市名称：缺少经纬度");
      return;
    }
    
    console.log("尝试获取本地城市名称，经纬度:", latitude, longitude);
    
    // 确保weatherData对象已初始化
    if (!weatherData) {
      weatherData = {
        ...(this.data.weather || {}),
        city: '定位中...',
        windDir: this.data.weather?.windDir || '未知',
        windScale: this.data.weather?.windScale || '--',
        humidity: this.data.weather?.humidity || '--'
      };
    }
    
    // 调用后端城市查询API - 使用标准API
    wx.request({
      url: `${app.globalData.apiBaseUrl}/get_city_by_location.php?latitude=${latitude}&longitude=${longitude}`,
      method: 'GET',
      success: (res) => {
        console.log("城市查询响应:", res.data);
        
        try {
          // 尝试解析响应数据
          let responseData = res.data;
          
          // 检查是否是字符串（可能包含错误信息）
          if (typeof responseData === 'string') {
            // 尝试找到并提取JSON部分
            const jsonStart = responseData.indexOf('{');
            if (jsonStart >= 0) {
              try {
                responseData = JSON.parse(responseData.substring(jsonStart));
                console.log("从响应中提取的JSON数据:", responseData);
              } catch (e) {
                console.error("解析JSON字符串失败:", e);
              }
            }
          }
          
          // 检查是否有有效数据
          if (responseData && responseData.success && responseData.city) {
            // 新API格式
            const cityInfo = responseData.city;
            let cityName = cityInfo.fullName || cityInfo.name;
            
            console.log("本地获取城市名称成功:", cityName);
            
            // 检查是否为拼音名称
            if (/^[a-zA-Z]+$/.test(cityName.replace(/\s/g, ''))) {
              console.warn("获取到的城市名称为拼音，尝试使用后备方法");
              this.fallbackToLocalCityName(latitude, longitude, weatherData);
              return;
            }
            
            // 更新城市名称
            weatherData.city = cityName;
            this.setData({
              weather: weatherData
            });
          } else if (responseData && responseData.success && responseData.data && responseData.data.length > 0) {
            // 旧API格式
            const cityInfo = responseData.data[0];
            let cityName = '';
            
            // 格式化城市名称
            if (cityInfo.adm1 && cityInfo.adm1 !== cityInfo.name) {
              cityName += cityInfo.adm1 + ' ';
            }
            
            if (cityInfo.adm2 && cityInfo.adm2 !== cityInfo.name && cityInfo.adm1 !== cityInfo.adm2) {
              cityName += cityInfo.adm2 + ' ';
            }
            
            cityName += cityInfo.name;
            
            console.log("本地获取城市名称成功(旧格式):", cityName);
            
            // 检查是否为拼音名称
            if (/^[a-zA-Z]+$/.test(cityName.replace(/\s/g, ''))) {
              console.warn("获取到的城市名称为拼音，尝试使用后备方法");
              this.fallbackToLocalCityName(latitude, longitude, weatherData);
              return;
            }
            
            // 更新城市名称
            weatherData.city = cityName;
            this.setData({
              weather: weatherData
            });
          } else {
            console.warn("城市查询未返回有效数据，尝试使用后备方法");
            this.fallbackToLocalCityName(latitude, longitude, weatherData);
          }
        } catch (e) {
          console.error("解析城市查询响应失败:", e);
          this.fallbackToLocalCityName(latitude, longitude, weatherData);
        }
      },
      fail: (err) => {
        console.error("城市查询请求失败:", err);
        this.fallbackToLocalCityName(latitude, longitude, weatherData);
      }
    });
  },
  
  // 后备方法：使用微信接口尝试获取城市名称
  fallbackToLocalCityName: function(latitude, longitude, weatherData = null) {
    console.log("使用后备方法获取城市名称");
    
    // 确保weatherData对象已初始化
    if (!weatherData) {
      weatherData = {
        ...(this.data.weather || {}),
        city: '定位中...',
        windDir: this.data.weather?.windDir || '未知',
        windScale: this.data.weather?.windScale || '--',
        humidity: this.data.weather?.humidity || '--'
      };
    }
    
    // 使用微信原生地址解析
    wx.request({
      url: 'https://apis.map.qq.com/ws/geocoder/v1/',
      data: {
        location: `${latitude},${longitude}`,
        key: 'GUOBZ-KE76T-OH7XH-VPSMY-QGRTH-NXF63', // 使用腾讯地图API密钥
        get_poi: 0
      },
      success: (res) => {
        console.log("腾讯地图解析结果:", res.data);
        if (res.data && res.data.status === 0 && res.data.result) {
          const addressComponent = res.data.result.address_component;
          let cityName = '';
          
          if (addressComponent.province) {
            cityName += addressComponent.province + ' ';
          }
          
          if (addressComponent.city && addressComponent.city !== addressComponent.province) {
            cityName += addressComponent.city + ' ';
          }
          
          if (addressComponent.district) {
            cityName += addressComponent.district;
          }
          
          console.log("腾讯地图解析城市名称成功:", cityName);
          
          if (cityName) {
            weatherData.city = cityName;
            this.setData({
              weather: weatherData
            });
          }
        } else {
          console.warn("腾讯地图解析失败，使用经纬度作为位置");
          weatherData.city = `位置(${latitude.toFixed(2)},${longitude.toFixed(2)})`;
          this.setData({
            weather: weatherData
          });
        }
      },
      fail: (err) => {
        console.error("腾讯地图解析请求失败:", err);
        weatherData.city = `位置(${latitude.toFixed(2)},${longitude.toFixed(2)})`;
        this.setData({
          weather: weatherData
        });
      }
    });
  },
  
  // 重新获取位置
  regetLocation: function() {
    this.getRecommendation(true);
  },
  
  // 保存穿搭
  saveOutfit: function() {
    console.log("保存穿搭按钮被点击");
    
    // 检查是否有穿搭数据
    if (!this.data.outfit) {
      wx.showToast({
        title: '暂无穿搭推荐',
        icon: 'none'
      });
      return;
    }
    
    // 检查用户是否已登录
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载中
    wx.showLoading({
      title: '保存中...',
      mask: true
    });
    
    // 获取或创建AI推荐分类
    this.getOrCreateAICategory()
      .then(categoryId => {
        console.log("获取到AI推荐分类ID:", categoryId);
        
        // 构建穿搭对象
        const now = new Date();
        const outfit = {
          name: "AI推荐穿搭", // 默认名称
          description: this.data.outfit.outfit_summary || "AI智能推荐的穿搭", // 使用穿搭总结作为描述
          category_id: categoryId, // AI推荐分类
          thumbnail: "", // 由后端生成缩略图
          created_at: now.toISOString(),
          updated_at: now.toISOString(),
          items: this.convertAIOutfitToItems(this.data.outfit), // 转换穿搭项
          forceAdd: true // 强制添加，即使可能为空
        };
        
        console.log("保存的穿搭数据:", outfit);
        
        // 保存穿搭，包括同步到服务器
        app.saveOutfit(outfit, (result) => {
          wx.hideLoading();
          
          if (result.success) {
            const savedOutfit = result.data || outfit;
            console.log('保存穿搭成功:', savedOutfit);
            
            // 显示成功提示，并询问是否前往编辑
            wx.showModal({
              title: '保存成功',
              content: '穿搭已保存至"AI推荐"分类，是否立即编辑?',
              confirmText: '去编辑',
              cancelText: '稍后再说',
              success: (res) => {
                if (res.confirm) {
                  // 跳转到编辑页
                  wx.navigateTo({
                    url: `/pages/outfits/edit/edit?id=${savedOutfit.id}`
                  });
                }
              }
            });
          } else {
            console.error('保存穿搭失败:', result.error);
            wx.showToast({
              title: result.error || '保存失败',
              icon: 'none'
            });
          }
        });
      })
      .catch(error => {
        wx.hideLoading();
        console.error("保存穿搭失败:", error);
        wx.showToast({
          title: '保存失败: ' + error.message,
          icon: 'none'
        });
      });
  },
  
  // 获取或创建AI推荐分类
  getOrCreateAICategory: function() {
    return new Promise((resolve, reject) => {
      // 获取穿搭分类列表
      wx.request({
        url: `${app.globalData.apiBaseUrl}/get_outfit_categories.php`,
        method: 'GET',
        header: {
          'Authorization': app.globalData.token
        },
        data: {
          page: 1,
          per_page: 100 // 获取足够多的分类
        },
        success: (res) => {
          console.log('获取穿搭分类列表:', res.data);
          
          if (res.statusCode === 200 && res.data.success) {
            const categories = res.data.data || [];
            
            // 查找名为"AI推荐"的分类
            const aiCategory = categories.find(cat => cat.name === 'AI推荐');
            
            if (aiCategory) {
              // 已存在AI推荐分类，直接使用
              console.log('已存在AI推荐分类:', aiCategory);
              resolve(aiCategory.id);
            } else {
              // 不存在则创建新分类
              console.log('需要创建AI推荐分类');
              this.createAICategory().then(resolve).catch(reject);
            }
          } else {
            reject(new Error('获取分类列表失败'));
          }
        },
        fail: (err) => {
          console.error('获取分类列表失败:', err);
          reject(err);
        }
      });
    });
  },
  
  // 创建AI推荐分类
  createAICategory: function() {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiBaseUrl}/add_outfit_category.php`,
        method: 'POST',
        header: {
          'Authorization': app.globalData.token,
          'Content-Type': 'application/json'
        },
        data: {
          name: 'AI推荐',
          description: 'AI智能推荐的穿搭集合'
        },
        success: (res) => {
          console.log('创建AI推荐分类响应:', res.data);
          
          if (res.statusCode === 200 && res.data.success) {
            resolve(res.data.data.id);
          } else {
            reject(new Error(res.data.message || '创建分类失败'));
          }
        },
        fail: (err) => {
          console.error('创建AI推荐分类失败:', err);
          reject(err);
        }
      });
    });
  },
  
  // 将AI推荐的穿搭转换为编辑器可用的items数组
  convertAIOutfitToItems: function(aiOutfit) {
    const items = [];
    const canvasWidth = 375; // 假设画布宽度为375px（微信小程序默认设计宽度）
    const canvasHeight = 500; // 假设画布高度为500px
    
    // 定义画布区域
    const leftX = 30;
    const rightX = canvasWidth - 150; // 假设物品宽度约120px
    const topY = 30;
    const bottomY = canvasHeight - 200; // 假设物品高度约170px
    const centerX = canvasWidth / 2 - 75; // 中心X
    const centerY = canvasHeight / 2 - 100; // 中心Y
    
    // 提取所有衣物并保存到一个数组
    let clothingItems = [];
    
    // 按照显示顺序添加衣物
    if (aiOutfit.top) {
      clothingItems.push(this.createClothingItem(aiOutfit.top, "上衣"));
    }
    
    if (aiOutfit.outerwear) {
      clothingItems.push(this.createClothingItem(aiOutfit.outerwear, "外套"));
    }
    
    if (aiOutfit.bottom) {
      const category = aiOutfit.bottom.category === 'pants' ? '裤子' : '裙子';
      clothingItems.push(this.createClothingItem(aiOutfit.bottom, category));
    }
    
    if (aiOutfit.shoes) {
      clothingItems.push(this.createClothingItem(aiOutfit.shoes, "鞋子"));
    }
    
    if (aiOutfit.accessories) {
      clothingItems.push(this.createClothingItem(aiOutfit.accessories, "配饰"));
    }
    
    if (aiOutfit.bag) {
      clothingItems.push(this.createClothingItem(aiOutfit.bag, "包包"));
    }
    
    // 根据要求排列衣物
    clothingItems.forEach((item, index) => {
      const outfitItem = {
        clothing_id: item.id,
        clothing_data: {
          name: item.name,
          category: item.category,
          image_url: item.image_url
        },
        position: { x: 0, y: 0 },
        size: { width: 150, height: 200 },
        rotation: 0,
        z_index: index + 1
      };
      
      // 根据索引设置位置
      switch (index) {
        case 0: // 第一件：左上
          outfitItem.position = { x: leftX, y: topY };
          break;
        case 1: // 第二件：右上
          outfitItem.position = { x: rightX, y: topY };
          break;
        case 2: // 第三件：左下
          outfitItem.position = { x: leftX, y: bottomY };
          break;
        case 3: // 第四件：右下
          outfitItem.position = { x: rightX, y: bottomY };
          break;
        case 4: // 第五件：中间
          outfitItem.position = { x: centerX, y: centerY };
          break;
        default: // 其余：围绕中心均匀分布
          const radius = 80; // 分布半径
          const angle = (index - 5) * (2 * Math.PI / (clothingItems.length - 5));
          outfitItem.position = {
            x: centerX + radius * Math.cos(angle),
            y: centerY + radius * Math.sin(angle)
          };
      }
      
      items.push(outfitItem);
    });
    
    return items;
  },
  
  // 创建衣物项辅助函数
  createClothingItem: function(item, displayCategory) {
    return {
      id: item.id,
      name: item.name || `${displayCategory}`,
      category: item.category || 'unknown',
      image_url: item.image_url
    };
  },
  
  // 查看衣物详情
  viewClothingDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    console.log("查看衣物详情, ID:", id);
    
    // 跳转到衣物详情页
    wx.navigateTo({
      url: `/pages/clothing/detail/detail?id=${id}`
    });
  },
  
  // 查看商品详情
  viewProductDetail: function(e) {
    const productId = e.currentTarget.dataset.id;
    const productIndex = e.currentTarget.dataset.index;
    const product = this.data.recommendedProducts[productIndex];
    
    if (product && product.tpwd && (product.is_fake_tpwd === 0 || product.is_fake_tpwd === '0')) {
      // 复制淘口令到剪贴板
      wx.setClipboardData({
        data: product.tpwd,
        success: function() {
          wx.showToast({
            title: '淘口令已复制',
            icon: 'success'
          });
        }
      });
    } else {
      wx.showToast({
        title: '该商品暂无购买链接',
        icon: 'none'
      });
    }
  },
  
  // 显示错误信息
  showErrorMessage: function(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  },
  
  // 跳转到登录页
  goToLogin: function() {
    // 如果是体验账号状态，先清除当前体验账号状态
    if (app.globalData.useMockUser) {
      // 清除体验账号的数据，但不自动生成新的模拟token
      wx.removeStorageSync('token');
      wx.removeStorageSync('userInfo');
      app.globalData.token = null;
      app.globalData.userInfo = null;
      app.globalData.useMockUser = false;
      
      console.log("已退出体验账号状态");
    }
    
    // 跳转到登录页
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },
  
  // 关闭分享提示弹窗
  closeShareTip: function() {
    this.setData({
      showShareTip: false
    });
  },
  
  // 通过分享获取更多次数
  shareToGetMoreQuota: function() {
    // 分享之前先关闭弹窗
    this.setData({
      showShareTip: false
    });
    
    // 唤起系统分享
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },
  
  // 分享成功回调（需要用户手动告知分享成功）
  onShareSuccess: function() {
    // 增加分享次数
    dailyQuota.addShareQuota(FEATURE_NAME);
    
    // 更新可用次数状态
    this.updateQuotaStatus();
    
    // 显示成功提示
    wx.showToast({
      title: '获得1次推荐机会',
      icon: 'success',
      duration: 2000
    });
    
    // 自动刷新推荐
    setTimeout(() => {
      this.getRecommendation(true);
    }, 2000);
  },
  
  // 分享给好友
  onShareAppMessage: function() {
    // 分享后增加配额
    setTimeout(() => {
      this.onShareSuccess();
    }, 1000);
    
    return {
      title: '不知道怎么搭配穿搭，快用次元衣帽间',
      path: '/pages/index/index?source=outfit_share',
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png', // 使用已有的分享图片
      success: (res) => {
        console.log('分享成功', res);
      }
    }
  },
  
  // 分享到朋友圈
  onShareTimeline: function() {
    // 分享后增加配额
    setTimeout(() => {
      this.onShareSuccess();
    }, 1000);
    
    return {
      title: '不知道怎么搭配穿搭，快用次元衣帽间',
      query: 'source=outfit_share',
      imageUrl: 'https://images.alidog.cn/logo/xxfx.png' // 使用已有的分享图片
    }
  },
  
  // 调试辅助函数
  debugObj: function(obj, name = '对象') {
    if (typeof obj !== 'object' || obj === null) {
      console.log(`${name} 不是对象:`, obj);
      return;
    }
    try {
      console.log(`${name} 包含的属性:`, Object.keys(obj));
      console.log(`${name} 的值:`, JSON.stringify(obj));
    } catch (e) {
      console.log(`${name} 无法序列化:`, e);
    }
  },
  
  // 更新配额状态
  updateQuotaStatus: function() {
    const availableQuota = dailyQuota.getAvailableQuota(FEATURE_NAME);
    this.setData({
      availableQuota: availableQuota
    });
  }
}) 