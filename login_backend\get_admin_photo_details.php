<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Methods: GET, OPTIONS');

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit();
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] != 'GET') {
    http_response_code(405);
    echo json_encode(['error' => true, 'msg' => '方法不允许']);
    exit();
}

// 验证管理员身份
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '缺少授权头']);
    exit();
}

// 从Authorization头获取令牌
$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

// 验证令牌
$auth = new Auth();
$payload = $auth->verifyAdminToken($token);

if (!$payload) {
    http_response_code(401);
    echo json_encode(['error' => true, 'msg' => '无效或已过期的令牌']);
    exit();
}

// 检查照片ID参数
if (!isset($_GET['id']) || empty($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => true, 'msg' => '缺少照片ID参数']);
    exit();
}

$photoId = (int)$_GET['id'];

// 连接数据库
$db = new Database();
$conn = $db->getConnection();

// 查询照片详情
$photoQuery = "SELECT 
                p.id, p.user_id, p.image_url, p.type, p.description, p.created_at, p.updated_at
              FROM photos p
              WHERE p.id = :id";
$photoStmt = $conn->prepare($photoQuery);
$photoStmt->bindValue(':id', $photoId, PDO::PARAM_INT);
$photoStmt->execute();

$photo = $photoStmt->fetch(PDO::FETCH_ASSOC);

if (!$photo) {
    http_response_code(404);
    echo json_encode(['error' => true, 'msg' => '照片不存在']);
    exit();
}

// 查询用户信息
$userQuery = "SELECT 
                u.id, u.nickname, u.avatar_url, u.gender, u.created_at, u.status
              FROM users u
              WHERE u.id = :user_id";
$userStmt = $conn->prepare($userQuery);
$userStmt->bindValue(':user_id', $photo['user_id'], PDO::PARAM_INT);
$userStmt->execute();

$user = $userStmt->fetch(PDO::FETCH_ASSOC);

// 返回数据
echo json_encode([
    'error' => false,
    'data' => [
        'photo' => $photo,
        'user' => $user
    ]
]); 