<?php
/**
 * 调试自定义分类显示问题
 * 检查其他用户的自定义分类为什么没有显示
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: GET, OPTIONS');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 验证用户身份
$auth = new Auth();

if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo "错误：缺少授权头\n";
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo "错误：无效或已过期的令牌\n";
    exit;
}

$userId = $payload['user_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    echo "=== 自定义分类调试 ===\n";
    echo "当前用户ID: $userId\n\n";
    
    // 1. 检查用户所在的圈子
    echo "1. 用户所在的圈子:\n";
    $stmt = $conn->prepare("
        SELECT cm.circle_id, cm.status, cm.role, c.name as circle_name
        FROM circle_members cm
        LEFT JOIN outfit_circles c ON cm.circle_id = c.id
        WHERE cm.user_id = :user_id AND cm.status = 'active'
        ORDER BY cm.joined_at DESC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $userCircles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($userCircles)) {
        echo "用户不在任何活跃圈子中\n";
        exit;
    }
    
    foreach ($userCircles as $circle) {
        echo "- 圈子ID: {$circle['circle_id']}, 名称: {$circle['circle_name']}, 角色: {$circle['role']}\n";
    }
    echo "\n";
    
    $circleId = $userCircles[0]['circle_id'];
    
    // 2. 检查圈子中的所有成员
    echo "2. 圈子中的所有成员:\n";
    $stmt = $conn->prepare("
        SELECT cm.user_id, cm.status, cm.role, u.nickname
        FROM circle_members cm
        LEFT JOIN users u ON cm.user_id = u.id
        WHERE cm.circle_id = :circle_id
        ORDER BY cm.role DESC, cm.joined_at ASC
    ");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->execute();
    $allMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($allMembers as $member) {
        $isCurrent = $member['user_id'] == $userId ? ' (当前用户)' : '';
        echo "- 用户ID: {$member['user_id']}, 昵称: {$member['nickname']}, 角色: {$member['role']}, 状态: {$member['status']}{$isCurrent}\n";
    }
    echo "\n";
    
    // 3. 检查每个成员的自定义分类
    echo "3. 检查每个成员的自定义分类:\n";
    foreach ($allMembers as $member) {
        $memberId = $member['user_id'];
        $memberName = $member['nickname'];
        $isCurrent = $memberId == $userId;
        
        echo "3.{$memberId} 用户: $memberName" . ($isCurrent ? ' (当前用户)' : '') . "\n";
        
        // 检查该用户的所有自定义分类
        $stmt = $conn->prepare("
            SELECT c.id, c.name, c.code, c.circle_id, c.created_at
            FROM clothing_categories c
            WHERE c.user_id = :user_id AND c.is_system = 0
            ORDER BY c.created_at DESC
        ");
        $stmt->bindParam(':user_id', $memberId, PDO::PARAM_INT);
        $stmt->execute();
        $userCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($userCategories)) {
            echo "  - 没有自定义分类\n";
        } else {
            foreach ($userCategories as $cat) {
                $syncStatus = $cat['circle_id'] ? "已同步到圈子{$cat['circle_id']}" : "未同步";
                echo "  - {$cat['name']} (code: {$cat['code']}, ID: {$cat['id']}) - $syncStatus\n";
            }
        }
        echo "\n";
    }
    
    // 4. 检查圈子中的所有自定义分类
    echo "4. 圈子中的所有自定义分类:\n";
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.code, c.user_id, c.circle_id, c.created_at, u.nickname as creator_nickname
        FROM clothing_categories c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.is_system = 0 AND c.circle_id = :circle_id
        ORDER BY c.user_id, c.created_at DESC
    ");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->execute();
    $circleCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($circleCategories)) {
        echo "圈子中没有自定义分类\n";
    } else {
        foreach ($circleCategories as $cat) {
            $isOwner = $cat['user_id'] == $userId ? ' (自己的)' : '';
            echo "- {$cat['name']} (code: {$cat['code']}, ID: {$cat['id']}) - 创建者: {$cat['creator_nickname']}{$isOwner}\n";
        }
    }
    echo "\n";
    
    // 5. 测试当前的shared查询逻辑
    echo "5. 测试当前的shared查询逻辑:\n";
    $stmt = $conn->prepare("
        SELECT DISTINCT c.id, c.user_id, c.name, c.code, c.is_system, c.sort_order, c.created_at,
               u.nickname as creator_nickname,
               CASE
                   WHEN c.is_system = 1 THEN 'system'
                   WHEN c.circle_id IS NULL THEN 'personal'
                   ELSE 'shared'
               END as data_source
        FROM clothing_categories c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE (
            -- 当前用户的系统分类（用于显示系统分类下的共享衣物）
            (c.is_system = 1 AND c.user_id = :user_id) OR
            -- 其他用户的自定义分类（已同步到圈子的）
            (c.is_system = 0 AND c.user_id != :user_id AND c.circle_id IS NOT NULL AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active'))
        )
        ORDER BY c.sort_order ASC, c.is_system DESC, c.created_at ASC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $sharedResult = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "shared查询结果数量: " . count($sharedResult) . "\n";
    $systemCount = 0;
    $customCount = 0;
    
    foreach ($sharedResult as $cat) {
        if ($cat['is_system']) {
            $systemCount++;
            echo "- [系统] {$cat['name']} (code: {$cat['code']})\n";
        } else {
            $customCount++;
            echo "- [自定义] {$cat['name']} (code: {$cat['code']}) - 创建者: {$cat['creator_nickname']}\n";
        }
    }
    
    echo "统计: 系统分类 $systemCount 个，自定义分类 $customCount 个\n\n";
    
    // 6. 分析问题
    echo "6. 问题分析:\n";
    
    $totalCircleCustomCategories = count($circleCategories);
    $othersCircleCustomCategories = 0;
    foreach ($circleCategories as $cat) {
        if ($cat['user_id'] != $userId) {
            $othersCircleCustomCategories++;
        }
    }
    
    echo "- 圈子中总自定义分类数: $totalCircleCustomCategories\n";
    echo "- 其他用户的自定义分类数: $othersCircleCustomCategories\n";
    echo "- shared查询返回的自定义分类数: $customCount\n";
    
    if ($othersCircleCustomCategories > 0 && $customCount == 0) {
        echo "\n❌ 问题确认：圈子中有其他用户的自定义分类，但shared查询没有返回\n";
        echo "可能原因:\n";
        echo "1. 查询条件中的circle_id匹配有问题\n";
        echo "2. 圈子成员状态检查有问题\n";
        echo "3. SQL逻辑有错误\n";
    } elseif ($othersCircleCustomCategories == 0) {
        echo "\n⚠️ 圈子中没有其他用户的自定义分类\n";
        echo "可能原因:\n";
        echo "1. 其他用户没有创建自定义分类\n";
        echo "2. 其他用户的自定义分类没有同步到圈子\n";
    } else {
        echo "\n✅ shared查询正常工作\n";
    }
    
    // 7. 测试简化的查询
    echo "\n7. 测试简化的查询（排除圈子成员检查）:\n";
    $stmt = $conn->prepare("
        SELECT c.id, c.name, c.code, c.user_id, c.circle_id, u.nickname as creator_nickname
        FROM clothing_categories c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.is_system = 0 AND c.user_id != :user_id AND c.circle_id = :circle_id
        ORDER BY c.created_at DESC
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->execute();
    $simpleResult = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "简化查询结果数量: " . count($simpleResult) . "\n";
    foreach ($simpleResult as $cat) {
        echo "- {$cat['name']} (code: {$cat['code']}) - 创建者: {$cat['creator_nickname']}\n";
    }
    
    if (count($simpleResult) > $customCount) {
        echo "\n⚠️ 简化查询返回更多结果，说明圈子成员检查条件有问题\n";
    }
    
    echo "\n=== 调试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
