/**
 * 每日配额管理工具
 * 用于管理用户每日使用次数限制，支持分享解锁更多次数
 */

// 本地存储的key前缀
const STORAGE_KEY_PREFIX = 'daily_quota_';

/**
 * 获取特定功能的存储key
 * @param {string} feature 功能名称
 * @returns {string} 存储key
 */
function getStorageKey(feature) {
  return `${STORAGE_KEY_PREFIX}${feature}`;
}

/**
 * 获取当前日期的字符串，格式：YYYY-MM-DD
 * @returns {string} 日期字符串
 */
function getCurrentDateString() {
  const date = new Date();
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
}

/**
 * 获取用户当日使用记录
 * @param {string} feature 功能名称
 * @returns {object} 使用记录对象
 */
function getDailyRecord(feature) {
  const key = getStorageKey(feature);
  try {
    // 获取存储的记录
    const recordStr = wx.getStorageSync(key);
    let record = recordStr ? JSON.parse(recordStr) : null;
    
    // 检查记录是否存在且是否为当天
    const currentDate = getCurrentDateString();
    if (!record || record.date !== currentDate) {
      // 如果不是当天的记录，则创建新记录
      record = {
        date: currentDate,
        usedCount: 0,
        sharedCount: 0,
        maxCount: 3  // 默认每天可使用1次
      };
      // 保存新记录
      wx.setStorageSync(key, JSON.stringify(record));
    }
    
    return record;
  } catch (e) {
    console.error(`获取${feature}每日记录失败:`, e);
    // 出错时返回默认记录
    return {
      date: getCurrentDateString(),
      usedCount: 0,
      sharedCount: 0,
      maxCount: 1
    };
  }
}

/**
 * 保存用户当日使用记录
 * @param {string} feature 功能名称
 * @param {object} record 使用记录对象
 */
function saveDailyRecord(feature, record) {
  const key = getStorageKey(feature);
  try {
    wx.setStorageSync(key, JSON.stringify(record));
  } catch (e) {
    console.error(`保存${feature}每日记录失败:`, e);
  }
}

/**
 * 检查用户是否还有可用次数
 * @param {string} feature 功能名称
 * @returns {boolean} 是否有可用次数
 */
function hasAvailableQuota(feature) {
  const record = getDailyRecord(feature);
  const totalAvailable = record.maxCount + record.sharedCount;
  return record.usedCount < totalAvailable;
}

/**
 * 获取用户可用次数
 * @param {string} feature 功能名称
 * @returns {number} 可用次数
 */
function getAvailableQuota(feature) {
  const record = getDailyRecord(feature);
  const totalAvailable = record.maxCount + record.sharedCount;
  return Math.max(0, totalAvailable - record.usedCount);
}

/**
 * 使用一次配额
 * @param {string} feature 功能名称
 * @returns {boolean} 是否使用成功
 */
function useQuota(feature) {
  const record = getDailyRecord(feature);
  const totalAvailable = record.maxCount + record.sharedCount;
  
  if (record.usedCount < totalAvailable) {
    record.usedCount += 1;
    saveDailyRecord(feature, record);
    return true;
  }
  
  return false;
}

/**
 * 增加分享次数
 * @param {string} feature 功能名称
 * @returns {boolean} 是否增加成功
 */
function addShareQuota(feature) {
  const record = getDailyRecord(feature);
  record.sharedCount += 1;
  saveDailyRecord(feature, record);
  return true;
}

/**
 * 重置当日配额
 * @param {string} feature 功能名称
 */
function resetDailyQuota(feature) {
  const record = getDailyRecord(feature);
  record.usedCount = 0;
  record.sharedCount = 0;
  saveDailyRecord(feature, record);
}

/**
 * 清除所有配额记录（用于测试）
 */
function clearAllQuotas() {
  try {
    // 获取所有本地存储的key
    const keys = wx.getStorageInfoSync().keys;
    // 筛选出配额相关的key
    const quotaKeys = keys.filter(key => key.startsWith(STORAGE_KEY_PREFIX));
    // 删除所有配额相关的key
    quotaKeys.forEach(key => {
      wx.removeStorageSync(key);
    });
  } catch (e) {
    console.error('清除所有配额记录失败:', e);
  }
}

module.exports = {
  hasAvailableQuota,
  getAvailableQuota,
  useQuota,
  addShareQuota,
  resetDailyQuota,
  clearAllQuotas
}; 