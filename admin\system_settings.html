<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 次元衣柜后台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 菜单链接样式 */
        .menu-item a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            padding: 15px 20px;
        }
        
        .menu-item {
            padding: 0;
        }
        
        /* 添加明显的视觉反馈 */
        .menu-item a:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* 系统设置页面样式 */
        .settings-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .tab-item {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            margin-right: 5px;
        }
        
        .tab-item.active {
            border-color: #ddd;
            border-bottom-color: white;
            background-color: white;
            border-radius: 4px 4px 0 0;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .form-input[readonly] {
            background-color: #f5f5f5;
            cursor: not-allowed;
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group .form-input {
            padding-right: 35px;
        }
        
        .toggle-password {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            color: #999;
        }
        
        .form-description {
            margin-top: 5px;
            font-size: 0.85em;
            color: #666;
        }
        
        .form-section {
            margin-bottom: 25px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        
        .form-section-title {
            font-size: 1.1em;
            font-weight: 500;
            margin-bottom: 15px;
            color: #333;
        }
        
        .btn-container {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
        }
        
        .btn-default {
            background-color: #f0f0f0;
            color: #333;
        }
        
        .btn-default:hover {
            background-color: #e0e0e0;
        }
        
        .btn-danger {
            background-color: #f5222d;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #ff4d4f;
        }
        
        .alert {
            padding: 10px 15px;
            margin-bottom: 15px;
            border-radius: 4px;
            display: none;
        }
        
        .alert-success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        
        .alert-error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #f5222d;
        }
        
        .saving-indicator {
            display: none;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="sidebar">
            <!-- 侧边栏将通过JavaScript动态生成 -->
        </div>
        
        <div class="content">
            <div class="header">
                <h2>系统设置</h2>
                <div class="user-info">
                    <span id="adminName">管理员</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>
            
            <div class="card">
                <div id="successAlert" class="alert alert-success">
                    设置已成功保存！
                </div>
                
                <div id="errorAlert" class="alert alert-error">
                    保存设置时出错：<span id="errorMessage"></span>
                </div>
                
                <div class="settings-tabs">
                    <div class="tab-item active" data-tab="database">数据库设置</div>
                    <div class="tab-item" data-tab="wechat">微信配置</div>
                    <div class="tab-item" data-tab="admin">管理员设置</div>
                    <div class="tab-item" data-tab="aliyun">阿里云配置</div>
                    <div class="tab-item" data-tab="storage">存储路径设置</div>
                </div>
                
                <form id="settingsForm">
                    <!-- 数据库设置 -->
                    <div id="database-tab" class="tab-content active">
                        <div class="form-section">
                            <div class="form-section-title">MySQL数据库连接信息</div>
                            
                            <div class="form-group">
                                <label class="form-label" for="DB_HOST">数据库主机：</label>
                                <input type="text" class="form-input" id="DB_HOST" name="database[DB_HOST]" required>
                                <div class="form-description">数据库服务器地址，通常为localhost或IP地址</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="DB_NAME">数据库名称：</label>
                                <input type="text" class="form-input" id="DB_NAME" name="database[DB_NAME]" required>
                                <div class="form-description">连接的数据库名称</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="DB_USER">数据库用户名：</label>
                                <input type="text" class="form-input" id="DB_USER" name="database[DB_USER]" required>
                                <div class="form-description">数据库连接用户名</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="DB_PASS">数据库密码：</label>
                                <div class="input-group">
                                    <input type="password" class="form-input" id="DB_PASS" name="database[DB_PASS]" required>
                                    <button type="button" class="toggle-password" data-target="DB_PASS">👁️</button>
                                </div>
                                <div class="form-description">数据库连接密码</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 微信配置 -->
                    <div id="wechat-tab" class="tab-content">
                        <div class="form-section">
                            <div class="form-section-title">微信小程序配置</div>
                            
                            <div class="form-group">
                                <label class="form-label" for="WX_APPID">AppID：</label>
                                <input type="text" class="form-input" id="WX_APPID" name="wechat[WX_APPID]" required>
                                <div class="form-description">微信小程序的AppID</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="WX_SECRET">AppSecret：</label>
                                <div class="input-group">
                                    <input type="password" class="form-input" id="WX_SECRET" name="wechat[WX_SECRET]" required>
                                    <button type="button" class="toggle-password" data-target="WX_SECRET">👁️</button>
                                </div>
                                <div class="form-description">微信小程序的AppSecret</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="API_DOMAIN">API域名：</label>
                                <input type="text" class="form-input" id="API_DOMAIN" name="wechat[API_DOMAIN]" required>
                                <div class="form-description">后端API服务的域名，需要包含https://</div>
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <div class="form-section-title">微信客服消息配置</div>
                            
                            <div class="form-group">
                                <label class="form-label" for="WX_TOKEN">验证TOKEN：</label>
                                <div class="input-group">
                                    <input type="password" class="form-input" id="WX_TOKEN" name="wechat[WX_TOKEN]" required>
                                    <button type="button" class="toggle-password" data-target="WX_TOKEN">👁️</button>
                                </div>
                                <div class="form-description">用于验证微信服务器的请求</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="WX_ENCODING_AES_KEY">消息加密密钥：</label>
                                <div class="input-group">
                                    <input type="password" class="form-input" id="WX_ENCODING_AES_KEY" name="wechat[WX_ENCODING_AES_KEY]" required>
                                    <button type="button" class="toggle-password" data-target="WX_ENCODING_AES_KEY">👁️</button>
                                </div>
                                <div class="form-description">消息加解密密钥，43位</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 管理员设置 -->
                    <div id="admin-tab" class="tab-content">
                        <div class="form-section">
                            <div class="form-section-title">管理员配置</div>
                            
                            <div class="form-group">
                                <label class="form-label" for="ADMIN_SECRET_KEY">管理员密钥：</label>
                                <div class="input-group">
                                    <input type="password" class="form-input" id="ADMIN_SECRET_KEY" name="admin[ADMIN_SECRET_KEY]" required>
                                    <button type="button" class="toggle-password" data-target="ADMIN_SECRET_KEY">👁️</button>
                                </div>
                                <div class="form-description">用于管理员Token签名的密钥，修改此值会导致所有管理员需要重新登录</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 阿里云配置 -->
                    <div id="aliyun-tab" class="tab-content">
                        <div class="form-section">
                            <div class="form-section-title">阿里云API配置</div>
                            
                            <div class="form-group">
                                <label class="form-label" for="ALIYUN_ACCESS_KEY_ID">AccessKey ID：</label>
                                <div class="input-group">
                                    <input type="password" class="form-input" id="ALIYUN_ACCESS_KEY_ID" name="aliyun[ALIYUN_ACCESS_KEY_ID]" required>
                                    <button type="button" class="toggle-password" data-target="ALIYUN_ACCESS_KEY_ID">👁️</button>
                                </div>
                                <div class="form-description">阿里云AccessKey ID</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="ALIYUN_ACCESS_KEY_SECRET">AccessKey Secret：</label>
                                <div class="input-group">
                                    <input type="password" class="form-input" id="ALIYUN_ACCESS_KEY_SECRET" name="aliyun[ALIYUN_ACCESS_KEY_SECRET]" required>
                                    <button type="button" class="toggle-password" data-target="ALIYUN_ACCESS_KEY_SECRET">👁️</button>
                                </div>
                                <div class="form-description">阿里云AccessKey Secret</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="ALIYUN_OUTFIT_API_KEY">OutfitAnyone API密钥：</label>
                                <div class="input-group">
                                    <input type="password" class="form-input" id="ALIYUN_OUTFIT_API_KEY" name="aliyun[ALIYUN_OUTFIT_API_KEY]" required>
                                    <button type="button" class="toggle-password" data-target="ALIYUN_OUTFIT_API_KEY">👁️</button>
                                </div>
                                <div class="form-description">阿里云OutfitAnyone API密钥</div>
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <div class="form-section-title">阿里云OSS配置</div>
                            
                            <div class="form-group">
                                <label class="form-label" for="ALIYUN_OSS_ENDPOINT">OSS服务端点：</label>
                                <input type="text" class="form-input" id="ALIYUN_OSS_ENDPOINT" name="aliyun[ALIYUN_OSS_ENDPOINT]" required>
                                <div class="form-description">OSS服务的Endpoint，如：oss-cn-shanghai.aliyuncs.com</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="ALIYUN_OSS_BUCKET">OSS存储桶名称：</label>
                                <input type="text" class="form-input" id="ALIYUN_OSS_BUCKET" name="aliyun[ALIYUN_OSS_BUCKET]" required>
                                <div class="form-description">OSS Bucket名称</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="ALIYUN_OSS_BUCKET_DOMAIN">OSS存储桶域名：</label>
                                <input type="text" class="form-input" id="ALIYUN_OSS_BUCKET_DOMAIN" name="aliyun[ALIYUN_OSS_BUCKET_DOMAIN]" required>
                                <div class="form-description">Bucket域名，如：bucket-name.oss-cn-shanghai.aliyuncs.com</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 存储路径设置 -->
                    <div id="storage-tab" class="tab-content">
                        <div class="form-section">
                            <div class="form-section-title">OSS存储路径前缀</div>
                            
                            <div class="form-group">
                                <label class="form-label" for="OSS_PATH_CLOTHES">衣物图片存储路径：</label>
                                <input type="text" class="form-input" id="OSS_PATH_CLOTHES" name="storage_paths[OSS_PATH_CLOTHES]" required>
                                <div class="form-description">衣物图片在OSS中的存储路径前缀，如：clothes/</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="OSS_PATH_PHOTOS">用户照片存储路径：</label>
                                <input type="text" class="form-input" id="OSS_PATH_PHOTOS" name="storage_paths[OSS_PATH_PHOTOS]" required>
                                <div class="form-description">用户照片在OSS中的存储路径前缀，如：photos/</div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="OSS_PATH_TRY_ON">试衣结果图片存储路径：</label>
                                <input type="text" class="form-input" id="OSS_PATH_TRY_ON" name="storage_paths[OSS_PATH_TRY_ON]" required>
                                <div class="form-description">试衣结果图片在OSS中的存储路径前缀，如：try_on/</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="btn-container">
                        <button type="button" id="resetBtn" class="btn btn-default">重置</button>
                        <button type="submit" id="saveBtn" class="btn btn-primary">保存设置</button>
                    </div>
                    
                    <div id="savingIndicator" class="saving-indicator">正在保存设置，请稍候...</div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/system_settings.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 保护页面，未登录则跳转
            Auth.protectPage();
            
            // 初始化侧边栏，设置当前活动项为settings
            Sidebar.init('settings');
            
            // 显示用户信息
            const adminName = document.getElementById('adminName');
            const user = Auth.getCurrentUser();
            if (user) {
                adminName.textContent = user.realName || user.username;
            }
            
            // 退出登录
            document.getElementById('logoutBtn').addEventListener('click', function() {
                Auth.logout();
            });
            
            // 初始化系统设置页面
            if (typeof SystemSettings !== 'undefined') {
                SystemSettings.init();
            }
        });
    </script>
</body>
</html> 