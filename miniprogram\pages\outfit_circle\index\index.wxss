/* 共同管理衣橱主页样式 */
/* 模块1：圈子基础管理模块 */

.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* 未加入圈子状态 */
.no-circle-container {
  padding: 60rpx 40rpx;
}

.welcome-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.welcome-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.welcome-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.welcome-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 操作卡片 */
.action-section {
  margin-bottom: 60rpx;
}

.action-card {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s;
}

.action-card:active {
  transform: scale(0.98);
}

.action-icon {
  font-size: 48rpx;
  margin-right: 30rpx;
}

.action-content {
  flex: 1;
}

.action-title {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.action-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.action-arrow {
  font-size: 32rpx;
  color: #999;
}

/* 信息说明 */
.info-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.info-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
}

.info-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

/* 圈子容器 */
.circle-container {
  padding: 40rpx;
}

/* 圈子头部 */
.circle-header {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.circle-name {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.circle-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.circle-code-section {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
}

.code-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.code-value {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  letter-spacing: 2rpx;
}

.copy-btn {
  padding: 10rpx;
}

.copy-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 统计数据 */
.stats-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 成员列表 */
.members-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 30rpx;
}

.members-list {
  display: flex;
  flex-direction: column;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.member-item:last-child {
  border-bottom: none;
}

.member-content {
  display: flex;
  align-items: center;
  flex: 1;
  padding: 10rpx;
  border-radius: 8rpx;
  transition: background-color 0.2s;
}

.member-content:active {
  background-color: #f8f8f8;
}

.member-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.member-info {
  flex: 1;
}

.member-basic {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.member-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-right: 12rpx;
}

.member-role {
  font-size: 22rpx;
  color: #666;
  background-color: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.member-stats {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.stat-item {
  font-size: 22rpx;
  color: #666;
  background-color: #f8f8f8;
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
  margin-right: 8rpx;
}

.member-time {
  font-size: 24rpx;
  color: #999;
}

.member-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.contribution-badge {
  background-color: #333;
  color: #fff;
  border-radius: 50%;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
}

.badge-text {
  font-size: 20rpx;
  font-weight: 600;
}

.remove-btn {
  font-size: 24rpx;
  color: #ff4757;
  padding: 6rpx 12rpx;
  border: 1rpx solid #ff4757;
  border-radius: 6rpx;
}

/* 加入信息 */
.join-info {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  text-align: center;
}

.join-time {
  font-size: 28rpx;
  color: #666;
}

/* 底部操作 */
.bottom-actions {
  padding: 20rpx 0;
  display: flex;
  gap: 20rpx;
}

.shared-data-btn, .invite-btn, .leave-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.shared-data-btn {
  background-color: #333;
  color: #fff;
}

.invite-btn {
  background-color: #333;
  color: #fff;
}

.leave-btn {
  background-color: #fff;
  color: #ff3b30;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
  border: 1rpx solid #f0f0f0;
}

/* 弹框样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  max-width: 600rpx;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  /* 确保弹框在键盘弹出时仍然可见 */
  position: relative;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
  /* 确保在键盘弹出时内容可以滚动 */
}

.modal-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  line-height: 1.4;
  box-sizing: border-box;
  background-color: #fafafa;
  color: #333;
}

.form-input {
  height: 80rpx;
  display: block;
  text-align: left;
  /* 确保placeholder垂直居中且不会移动 */
  line-height: 40rpx;
  padding: 20rpx;
  /* 防止聚焦时样式改变 */
  box-sizing: border-box;
}

.form-textarea {
  height: 120rpx;
  resize: none;
  vertical-align: top;
  display: block;
  line-height: 1.4;
}

/* 输入框聚焦状态 */
.form-input:focus, .form-textarea:focus {
  border-color: #333;
  background-color: #fff;
  outline: none;
}

/* 修复微信小程序input组件placeholder位置问题 */
.form-input {
  /* 使用固定的行高和内边距来保持placeholder位置稳定 */
  line-height: 40rpx !important;
  padding: 20rpx !important;
  vertical-align: middle;
  /* 防止聚焦时高度变化 */
  min-height: 80rpx;
  max-height: 80rpx;
}

/* 聚焦状态下保持样式不变 */
.form-input:focus {
  line-height: 40rpx !important;
  padding: 20rpx !important;
  height: 80rpx !important;
}

/* 确保placeholder样式一致 */
.form-input::placeholder {
  color: #999 !important;
  font-size: 28rpx !important;
  line-height: 40rpx !important;
  vertical-align: middle;
  /* 防止placeholder移动 */
  position: relative;
  top: 0;
}

.form-textarea::placeholder {
  color: #999 !important;
  font-size: 28rpx !important;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  font-size: 28rpx;
  border-radius: 0;
}

.cancel-btn {
  background-color: #f8f8f8;
  color: #666;
}

.confirm-btn {
  background-color: #333;
  color: #fff;
}

.confirm-btn[disabled] {
  background-color: #ccc;
}


