<view class="container">
  <view class="header">
    <view class="title">选择适合您的套餐</view>

  </view>

  <view class="package-list">
    <view class="package-item {{selectedPackage === item.id ? 'selected' : ''}}" 
          wx:for="{{packages}}" 
          wx:key="id"
          data-id="{{item.id}}"
          bindtap="selectPackage">
      <view class="package-info">
        <view class="package-count">{{item.count}}次</view>
        <view class="package-price">¥{{item.price}}</view>
      </view>
      <view class="package-desc">{{item.description}}</view>
      <view class="check-icon" wx:if="{{selectedPackage === item.id}}">
        <icon type="success" size="20" color="#333"/>
      </view>
    </view>
  </view>

  <view class="payment-btn {{selectedPackage ? '' : 'disabled'}}" bindtap="createOrder">
    立即支付
  </view>

  <view class="tips">
    <view class="tip-item">· 购买后立即生效，可在试衣页面查看剩余次数</view>
    <view class="tip-item">· 免费次数用完后才会使用付费次数</view>
    <view class="tip-item">· 如有疑问，请联系客服：shawii</view>
  </view>

  <view class="loading-mask" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-icon"></view>
      <view class="loading-text">{{loadingText}}</view>
    </view>
  </view>
</view> 