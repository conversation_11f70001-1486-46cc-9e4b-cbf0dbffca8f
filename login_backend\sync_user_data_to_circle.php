<?php
// 将用户数据同步到圈子API
// 模块4：数据共享基础模块

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Authorization, Content-Type');
header('Access-Control-Allow-Methods: POST, OPTIONS');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

require_once 'config.php';
require_once 'auth.php';
require_once 'db.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'status' => 'error',
        'message' => '不支持的请求方法'
    ]);
    exit;
}

// 验证用户身份
$auth = new Auth();

// 获取Authorization header
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || empty($_SERVER['HTTP_AUTHORIZATION'])) {
    echo json_encode([
        'status' => 'error',
        'message' => '缺少授权头'
    ]);
    exit;
}

$token = $_SERVER['HTTP_AUTHORIZATION'];
// 如果token是Bearer格式，提取实际token值
if (strpos($token, 'Bearer ') === 0) {
    $token = substr($token, 7);
}

$payload = $auth->verifyToken($token);
if (!$payload) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效或已过期的令牌'
    ]);
    exit;
}

$userId = $payload['sub'];

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

// 验证必需参数
$syncType = isset($input['sync_type']) ? $input['sync_type'] : 'incremental'; // initial, incremental, manual
$dataTypes = isset($input['data_types']) ? $input['data_types'] : ['wardrobes', 'clothes', 'outfits', 'categories']; // 要同步的数据类型

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查找用户所在的圈子
    $findCircleSql = "SELECT cm.circle_id, cm.role, c.name as circle_name
                      FROM circle_members cm 
                      JOIN outfit_circles c ON cm.circle_id = c.id 
                      WHERE cm.user_id = :user_id AND cm.status = 'active' AND c.status = 'active'";
    $findCircleStmt = $conn->prepare($findCircleSql);
    $findCircleStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $findCircleStmt->execute();
    
    $userCircle = $findCircleStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$userCircle) {
        echo json_encode([
            'status' => 'error',
            'message' => '您当前未加入任何圈子'
        ]);
        exit;
    }
    
    $circleId = $userCircle['circle_id'];
    
    // 开始事务
    $conn->beginTransaction();
    
    $syncResults = [];
    $totalSynced = 0;
    
    try {
        // 创建同步日志记录
        $logSql = "INSERT INTO circle_data_sync_logs 
                   (circle_id, user_id, sync_type, data_type, sync_status, started_at) 
                   VALUES (:circle_id, :user_id, :sync_type, 'all', 'processing', NOW())";
        $logStmt = $conn->prepare($logSql);
        $logStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
        $logStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $logStmt->bindParam(':sync_type', $syncType);
        $logStmt->execute();
        
        $logId = $conn->lastInsertId();
        
        // 同步衣橱数据
        if (in_array('wardrobes', $dataTypes)) {
            $wardrobeCount = syncWardrobes($conn, $userId, $circleId, $syncType);
            $syncResults['wardrobes'] = $wardrobeCount;
            $totalSynced += $wardrobeCount;
        }
        
        // 同步衣物数据
        if (in_array('clothes', $dataTypes)) {
            $clothesCount = syncClothes($conn, $userId, $circleId, $syncType);
            $syncResults['clothes'] = $clothesCount;
            $totalSynced += $clothesCount;
        }
        
        // 同步穿搭数据
        if (in_array('outfits', $dataTypes)) {
            $outfitsCount = syncOutfits($conn, $userId, $circleId, $syncType);
            $syncResults['outfits'] = $outfitsCount;
            $totalSynced += $outfitsCount;
        }

        // 同步分类数据
        if (in_array('categories', $dataTypes)) {
            $categoriesCount = syncCategories($conn, $userId, $circleId, $syncType);
            $syncResults['categories'] = $categoriesCount;
            $totalSynced += $categoriesCount;
        }
        
        // 更新同步日志为完成状态
        $updateLogSql = "UPDATE circle_data_sync_logs
                         SET sync_status = 'completed', items_count = :items_count, completed_at = NOW()
                         WHERE id = :log_id";
        $updateLogStmt = $conn->prepare($updateLogSql);
        $updateLogStmt->bindParam(':items_count', $totalSynced, PDO::PARAM_INT);
        $updateLogStmt->bindParam(':log_id', $logId, PDO::PARAM_INT);
        $updateLogStmt->execute();

        // 更新圈子成员统计数据
        updateMemberStatsAfterSync($conn, $circleId, $userId, $syncResults);

        // 提交事务
        $conn->commit();
        
        echo json_encode([
            'status' => 'success',
            'message' => '数据同步完成',
            'data' => [
                'circle_id' => $circleId,
                'circle_name' => $userCircle['circle_name'],
                'sync_type' => $syncType,
                'sync_results' => $syncResults,
                'total_synced' => $totalSynced,
                'log_id' => $logId
            ]
        ]);
        
    } catch (Exception $e) {
        // 回滚事务
        $conn->rollback();
        
        // 更新同步日志为失败状态
        if (isset($logId)) {
            $updateLogSql = "UPDATE circle_data_sync_logs 
                             SET sync_status = 'failed', error_message = :error_message, completed_at = NOW() 
                             WHERE id = :log_id";
            $updateLogStmt = $conn->prepare($updateLogSql);
            $updateLogStmt->bindParam(':error_message', $e->getMessage());
            $updateLogStmt->bindParam(':log_id', $logId, PDO::PARAM_INT);
            $updateLogStmt->execute();
        }
        
        throw $e;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据同步失败：' . $e->getMessage()
    ]);
}

// 同步衣橱数据的函数
function syncWardrobes($conn, $userId, $circleId, $syncType) {
    if ($syncType === 'initial') {
        // 初始同步：将用户的所有个人衣橱复制到圈子
        $sql = "UPDATE wardrobes SET circle_id = :circle_id 
                WHERE user_id = :user_id AND circle_id IS NULL";
    } else {
        // 增量同步：只同步最近创建的衣橱
        $sql = "UPDATE wardrobes SET circle_id = :circle_id 
                WHERE user_id = :user_id AND circle_id IS NULL 
                AND created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)";
    }
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    
    return $stmt->rowCount();
}

// 同步衣物数据的函数
function syncClothes($conn, $userId, $circleId, $syncType) {
    if ($syncType === 'initial') {
        // 初始同步：将用户的所有个人衣物复制到圈子
        $sql = "UPDATE clothes SET circle_id = :circle_id 
                WHERE user_id = :user_id AND circle_id IS NULL";
    } else {
        // 增量同步：只同步最近创建的衣物
        $sql = "UPDATE clothes SET circle_id = :circle_id 
                WHERE user_id = :user_id AND circle_id IS NULL 
                AND created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)";
    }
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    
    return $stmt->rowCount();
}

// 同步穿搭数据的函数
function syncOutfits($conn, $userId, $circleId, $syncType) {
    if ($syncType === 'initial') {
        // 初始同步：将用户的所有个人穿搭复制到圈子
        $sql = "UPDATE outfits SET circle_id = :circle_id 
                WHERE user_id = :user_id AND circle_id IS NULL";
    } else {
        // 增量同步：只同步最近创建的穿搭
        $sql = "UPDATE outfits SET circle_id = :circle_id 
                WHERE user_id = :user_id AND circle_id IS NULL 
                AND created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)";
    }
    
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    
    return $stmt->rowCount();
}

// 同步分类数据的函数
function syncCategories($conn, $userId, $circleId, $syncType) {
    if ($syncType === 'initial') {
        // 初始同步：将用户的所有个人分类复制到圈子（排除系统分类）
        $sql = "UPDATE clothing_categories SET circle_id = :circle_id
                WHERE user_id = :user_id AND circle_id IS NULL AND is_system = 0";
    } else {
        // 增量同步：只同步最近创建的分类
        $sql = "UPDATE clothing_categories SET circle_id = :circle_id
                WHERE user_id = :user_id AND circle_id IS NULL AND is_system = 0
                AND created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)";
    }

    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();

    return $stmt->rowCount();
}

/**
 * 数据同步后更新圈子成员统计数据
 */
function updateMemberStatsAfterSync($conn, $circleId, $userId, $syncResults) {
    // 计算实际的统计数据
    $wardrobeCount = 0;
    $clothesCount = 0;
    $outfitCount = 0;
    $categoriesCount = 0;

    // 统计用户在圈子中的数据
    if (isset($syncResults['wardrobes'])) {
        $wardrobeCount = $syncResults['wardrobes'];
    }
    if (isset($syncResults['clothes'])) {
        $clothesCount = $syncResults['clothes'];
    }
    if (isset($syncResults['outfits'])) {
        $outfitCount = $syncResults['outfits'];
    }
    if (isset($syncResults['categories'])) {
        $categoriesCount = $syncResults['categories'];
    }

    // 重新计算用户在圈子中的实际数据量
    $actualStats = calculateActualMemberStats($conn, $circleId, $userId);

    // 插入或更新统计记录
    $upsertSql = "INSERT INTO circle_member_stats
                  (circle_id, user_id, wardrobe_count, clothes_count, outfit_count,
                   clothing_category_count, outfit_category_count, tag_count, last_contribution_at)
                  VALUES (:circle_id, :user_id, :wardrobe_count, :clothes_count, :outfit_count,
                          :clothing_category_count, 0, 0, NOW())
                  ON DUPLICATE KEY UPDATE
                  wardrobe_count = :wardrobe_count,
                  clothes_count = :clothes_count,
                  outfit_count = :outfit_count,
                  clothing_category_count = :clothing_category_count,
                  last_contribution_at = NOW(),
                  updated_at = NOW()";

    $upsertStmt = $conn->prepare($upsertSql);
    $upsertStmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $upsertStmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $upsertStmt->bindParam(':wardrobe_count', $actualStats['wardrobe_count'], PDO::PARAM_INT);
    $upsertStmt->bindParam(':clothes_count', $actualStats['clothes_count'], PDO::PARAM_INT);
    $upsertStmt->bindParam(':outfit_count', $actualStats['outfit_count'], PDO::PARAM_INT);
    $upsertStmt->bindParam(':clothing_category_count', $actualStats['clothing_category_count'], PDO::PARAM_INT);
    $upsertStmt->execute();
}

/**
 * 计算用户在圈子中的实际统计数据
 */
function calculateActualMemberStats($conn, $circleId, $userId) {
    $stats = [
        'wardrobe_count' => 0,
        'clothes_count' => 0,
        'outfit_count' => 0,
        'clothing_category_count' => 0
    ];

    // 统计衣橱数量
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM wardrobes WHERE circle_id = :circle_id AND user_id = :user_id");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['wardrobe_count'] = intval($result['count']);

    // 统计衣物数量
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM clothes WHERE circle_id = :circle_id AND user_id = :user_id");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['clothes_count'] = intval($result['count']);

    // 统计穿搭数量
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM outfits WHERE circle_id = :circle_id AND user_id = :user_id");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['outfit_count'] = intval($result['count']);

    // 统计分类数量
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM clothing_categories WHERE circle_id = :circle_id AND user_id = :user_id AND is_system = 0");
    $stmt->bindParam(':circle_id', $circleId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['clothing_category_count'] = intval($result['count']);

    return $stats;
}
?>
