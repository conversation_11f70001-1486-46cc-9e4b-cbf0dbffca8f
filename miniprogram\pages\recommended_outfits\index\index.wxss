/* 页面容器 - 设置整体背景色、最小高度和底部间距 */
.container {
  background-color: #f7f7f7;
  min-height: 100vh;
  padding-bottom: 30rpx;
  padding-left: 0;
  padding-right: 0;
  margin: 0;
  width: 100%;
  padding: 0;
  box-sizing: border-box;
}

/* 分类栏 - 固定在顶部的导航栏，带有阴影效果 */
.categories-bar {
  background-color: #fff;
  position: sticky; /* 使分类栏在滚动时固定在顶部 */
  top: 0;
  left: 0;
  right: 0;
  z-index: 10; /* 确保分类栏显示在其他内容之上 */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  width: 100%; /* 确保宽度占满屏幕 */
  padding: 0; /* 移除内边距 */
  margin: 0; /* 移除外边距 */
  width: 100%;
  height: 80rpx;
  white-space: nowrap;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 分类选项卡 - 横向滚动的分类列表 */
.category-tabs {
  width: 100%;
  height: 100%;
  display: flex;
  padding: 0 20rpx;
}

/* 隐藏滚动条 */
.category-tabs::-webkit-scrollbar {
  display: none;
}

/* 分类项 - 单个分类按钮的样式 */
.category-item {
  display: inline-block;
  padding: 0 24rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #666;
}

/* 激活状态的分类项 - 当前选中分类的样式 */
.category-item.active {
  color: #000;
  font-weight: 500;
  position: relative;
}

/* 激活分类项底部指示器 - 在选中分类下方显示的绿色指示条 */
.category-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #000;
  border-radius: 2rpx;
}

/* 第一个分类项目 */
.category-item:first-child {
  margin-left: 20rpx; /* 添加左边距 */
}

/* 最后一个分类项目 */
.category-item:last-child {
  margin-right: 0; /* 移除最后一个项目的右边距 */
}

/* 加载中状态容器 - 居中显示的加载指示器容器 */
.loading-container {
  padding: 40rpx 0;
  text-align: center;
}

/* 加载图标 - 旋转的加载动画 */
.loading-icon {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 加载文字提示 */
.loading-text {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
}

/* 旋转动画关键帧 - 定义加载图标的旋转动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态容器 - 当没有数据时显示的容器 */
.empty-container {
  padding: 100rpx 0;
  text-align: center;
}

/* 空状态图标文字 */
.empty-icon {
  font-size: 28rpx;
  color: #999;
}

/* 穿搭列表 - 二列网格布局的列表容器 */
.outfit-list {
  padding: 20rpx;
  background-color: #f8f8f8;
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 设置两列等宽布局 */
  gap: 24rpx; /* 列表项之间的间距 */
}

/* 穿搭列表项 - 单个穿搭卡片的样式 */
.outfit-item {
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 穿搭图片容器 - 控制图片展示区域和比例 */
.outfit-image-container {
  width: 100%;
  height: 400rpx;
}

/* 穿搭图片 - 确保图片居中且不被裁剪 */
.outfit-image {
  width: 100%;
  height: 100%;
}

/* 穿搭信息区域 - 图片下方的文字信息容器 */
.outfit-info {
  padding: 20rpx;
}

/* 穿搭名称 - 单行截断显示 */
.outfit-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 穿搭元数据 - 分类和查看次数的容器 */
.outfit-meta {
  display: flex;
  justify-content: space-between;
  margin-top: 12rpx;
  font-size: 24rpx;
  color: #999;
}

/* 穿搭分类标签 - 圆角背景的分类显示 */
.outfit-category {
  background-color: #f2f2f2;
  padding: 2rpx 12rpx;
  border-radius: 20rpx;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis; /* 文本溢出时显示省略号 */
  white-space: nowrap; /* 防止文本换行 */
}

/* 加载更多区域 - 列表底部的加载更多提示 */
.load-more {
  padding: 30rpx 0;
  text-align: center;
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 小型加载图标 - 加载更多时的旋转图标 */
.loading-icon-small {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid #f0f0f0;
  border-top: 3rpx solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

/* 加载更多文字 */
.load-more-text {
  font-size: 24rpx;
  color: #ccc;
}

/* 主标签页样式 */
.main-tabs {
  display: flex;
  width: 100%;
  height: 88rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #000;
  font-weight: 500;
}

.tab-item.active:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #000;
  border-radius: 2rpx;
}

/* 搜索框 */
.search-box {
  display: flex;
  padding: 20rpx;
  background-color: #fff;
}

.search-input {
  flex: 1;
  height: 72rpx;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.search-btn {
  width: 120rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f5f5f5;
  border-radius: 36rpx;
}

/* 商品列表 */
.products-list {
  padding: 20rpx;
  background-color: #f8f8f8;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.product-item {
  width: 345rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.product-image-container {
  width: 100%;
  height: 345rpx;
  position: relative;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-coupon {
  position: absolute;
  left: 0;
  top: 20rpx;
  background-color: #ff4500;
  color: #fff;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 0 6rpx 6rpx 0;
}

.product-info {
  padding: 16rpx;
}

.product-title {
  font-size: 26rpx;
  color: #333;
  line-height: 36rpx;
  height: 72rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12rpx;
}

.product-price {
  color: #ff4500;
  font-size: 26rpx;
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 22rpx;
}

.price-value {
  font-size: 32rpx;
  font-weight: 500;
}

.original-price {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 8rpx;
}

.product-sales {
  font-size: 22rpx;
  color: #999;
}

.product-shop {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1; /* 让商店名称占据剩余空间 */
}

.shop-detail-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8rpx;
}

.detail-btn {
  display: inline-block;
  padding: 4rpx 12rpx;
  background-color: #ff6700;
  color: #ffffff;
  font-size: 20rpx;
  border-radius: 20rpx;
  text-align: center;
  min-width: 60rpx;
} 