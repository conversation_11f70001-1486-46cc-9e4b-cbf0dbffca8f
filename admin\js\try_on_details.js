/**
 * 试衣历史详情脚本
 */
const TryOnDetails = {
    // 试衣历史ID
    historyId: null,
    
    // 试衣历史数据
    historyData: null,
    
    // DOM元素
    elements: {},
    
    // 内部图片查看器
    imageViewer: {
        // 查看器元素
        viewer: null,
        image: null,
        closeBtn: null,
        isInitialized: false,
        
        // 初始化查看器
        init: function() {
            if (this.isInitialized) return;
            
            // 创建查看器DOM
            this.viewer = document.createElement('div');
            this.viewer.className = 'image-viewer';
            this.viewer.style.cssText = 'position:fixed; top:0; left:0; width:100%; height:100%; ' +
                'background-color:rgba(0,0,0,0.9); z-index:9999; display:none; ' +
                'align-items:center; justify-content:center;';
            
            // 创建图片元素
            this.image = document.createElement('img');
            this.image.className = 'viewer-image';
            this.image.style.cssText = 'max-width:90%; max-height:90%; object-fit:contain;';
            this.viewer.appendChild(this.image);
            
            // 创建关闭按钮
            this.closeBtn = document.createElement('button');
            this.closeBtn.innerHTML = '&times;';
            this.closeBtn.style.cssText = 'position:absolute; top:15px; right:20px; ' +
                'background:none; border:none; color:white; font-size:30px; ' +
                'cursor:pointer; z-index:10000;';
            this.viewer.appendChild(this.closeBtn);
            
            // 添加到页面
            document.body.appendChild(this.viewer);
            
            // 绑定事件
            this.closeBtn.addEventListener('click', () => this.close());
            this.viewer.addEventListener('click', (e) => {
                if (e.target === this.viewer) this.close();
            });
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') this.close();
            });
            
            this.isInitialized = true;
            console.log('内部图片查看器初始化成功');
        },
        
        // 显示图片
        show: function(imageUrl) {
            if (!this.isInitialized) this.init();
            
            this.image.src = imageUrl;
            this.image.onload = () => {
                this.viewer.style.display = 'flex';
                document.body.style.overflow = 'hidden';
            };
            this.image.onerror = () => {
                console.error('图片加载失败:', imageUrl);
                alert('图片加载失败');
            };
        },
        
        // 关闭查看器
        close: function() {
            if (!this.isInitialized) return;
            this.viewer.style.display = 'none';
            document.body.style.overflow = '';
        },
        
        // 绑定图片点击事件
        bindImages: function(selector) {
            if (!this.isInitialized) this.init();
            
            const images = document.querySelectorAll(selector);
            console.log(`为${images.length}个元素绑定内部图片查看器`);
            
            images.forEach(img => {
                if (!img.dataset.hasInternalViewer) {
                    img.style.cursor = 'pointer';
                    
                    img.addEventListener('click', (event) => {
                        event.preventDefault();
                        event.stopPropagation();
                        
                        const imageUrl = img.getAttribute('data-origin') || img.src;
                        this.show(imageUrl);
                    });
                    
                    img.dataset.hasInternalViewer = 'true';
                }
            });
        }
    },
    
    /**
     * 初始化
     */
    init: function() {
        const urlParams = new URLSearchParams(window.location.search);
        const id = urlParams.get('id');
        
        if (!id) {
            alert('未找到试衣历史ID');
            window.location.href = 'try_on_list.html';
            return;
        }
        
        this.historyId = id;
        
        // 初始化DOM元素引用
        this.elements = {
            tryOnDetailContainer: document.getElementById('tryOnDetailContainer'),
            userInfoContainer: document.getElementById('userInfoContainer'),
            clothesContainer: document.getElementById('clothesContainer'),
            photoInfoContainer: document.getElementById('photoInfoContainer'),
            photoContainer: document.getElementById('photoContainer')
        };
        
        // 初始化内部图片查看器
        this.imageViewer.init();
        
        // 加载试衣历史详情
        this.loadTryOnDetails(id);
    },
    
    /**
     * 加载试衣历史详情
     */
    loadTryOnDetails: function(id) {
        const token = Auth.getToken();
        
        fetch(`${API_BASE_URL}/login_backend/get_admin_try_on_details.php?id=${id}`, {
            headers: {
                'Authorization': token
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('加载试衣详情失败: ' + data.msg);
                return;
            }
            
            this.historyData = data.data;
            this.renderTryOnDetails(data.data.history);
            this.renderUserInfo(data.data.user);
            this.renderClothesInfo(data.data.clothes);
            this.renderPhotoInfo(data.data.photo);
        })
        .catch(error => {
            console.error('Error:', error);
            alert('加载试衣详情时出错');
        });
    },
    
    /**
     * 渲染试衣基本信息
     * @param {Object} history 试衣历史数据
     */
    renderTryOnDetails: function(history) {
        if (!history) {
            this.elements.tryOnDetailContainer.innerHTML = '<div class="empty-text">未找到试衣历史记录</div>';
            return;
        }
        
        const statusClass = history.status === 'success' ? 'status-success' : 'status-failed';
        const statusText = history.status === 'success' ? '成功' : '失败';
        
        // 获取使用方式
        const usageType = history.usage_type || '未知次数类型';
        
        const html = `
            <div class="tryOn-details">
                <div class="tryOn-image-container">
                    <img src="${history.result_image_url || '/images/no-image.png'}" class="tryOn-image" onerror="this.src='/images/no-image.png'" alt="试衣结果">
                </div>
                <div class="tryOn-info">
                    <div class="tryOn-id">ID: ${history.id}</div>
                    <div class="tryOn-meta">
                        <div class="meta-item">
                            <span class="meta-label">状态:</span>
                            <span class="status-badge ${statusClass}">${statusText}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">任务ID:</span>
                            <span>${history.task_id || '-'}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">创建时间:</span>
                            <span>${history.created_at}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-label">使用方式:</span>
                            <span style="color: #1890ff; font-weight: bold;">${usageType}</span>
                        </div>
                    </div>
                    <div class="tryOn-actions">
                        <button class="action-btn delete-btn" onclick="TryOnDetails.deleteTryOn(${history.id})">删除</button>
                    </div>
                </div>
            </div>
        `;
        
        this.elements.tryOnDetailContainer.innerHTML = html;
        
        // 绑定图片大图预览
        try {
            console.log('使用内部图片查看器绑定试衣结果图片预览');
            this.imageViewer.bindImages('.tryOn-image');
            console.log('成功绑定图片预览');
        } catch (error) {
            console.error('绑定图片预览失败:', error);
        }
    },
    
    /**
     * 渲染用户信息
     * @param {Object} user 用户数据
     */
    renderUserInfo: function(user) {
        if (!user) {
            this.elements.userInfoContainer.style.display = 'none';
            return;
        }
        
        const html = `
            <h3 class="section-title">用户信息</h3>
            <div class="user-info-section">
                <div class="user-info-header">
                    <img src="${user.avatar_url || '/images/default-avatar.png'}" class="user-avatar" onerror="this.src='/images/default-avatar.png'" alt="用户头像">
                    <div class="user-name">${user.nickname || '未知用户'}</div>
                </div>
                <div class="user-meta">
                    <div class="user-meta-item">
                        <span class="meta-label">用户ID:</span>
                        <span>${user.id}</span>
                    </div>
                    <div class="user-meta-item">
                        <span class="meta-label">性别:</span>
                        <span>${user.gender == 1 ? '男' : (user.gender == 2 ? '女' : '未知')}</span>
                    </div>
                    <div class="user-meta-item">
                        <span class="meta-label">注册时间:</span>
                        <span>${user.created_at || '-'}</span>
                    </div>
                </div>
                <button class="action-btn view-btn" onclick="window.location.href='user_details.html?id=${user.id}'">查看用户详情</button>
            </div>
        `;
        
        this.elements.userInfoContainer.innerHTML = html;
        this.elements.userInfoContainer.style.display = 'block';
    },
    
    /**
     * 渲染衣物信息
     * @param {Array} clothes 衣物数据列表
     */
    renderClothesInfo: function(clothes) {
        if (!clothes || clothes.length === 0) {
            this.elements.clothesContainer.innerHTML = '<div class="empty-text">未找到衣物信息</div>';
            return;
        }
        
        let html = '';
        
        clothes.forEach(item => {
            html += `
                <div class="clothes-item">
                    <img src="${item.image_url || '/images/no-image.png'}" class="clothes-img" onerror="this.src='/images/no-image.png'" alt="衣物图片">
                    <div class="clothes-info">
                        <div class="clothes-name">${item.name || '未命名衣物'}</div>
                        <div class="clothes-category">${item.category || '未分类'}</div>
                    </div>
                </div>
            `;
        });
        
        this.elements.clothesContainer.innerHTML = html;
        
        // 绑定图片大图预览
        try {
            console.log('使用内部图片查看器绑定衣物图片预览');
            this.imageViewer.bindImages('.clothes-img');
            console.log('成功绑定图片预览');
        } catch (error) {
            console.error('绑定图片预览失败:', error);
        }
    },
    
    /**
     * 渲染照片信息
     * @param {Object} photo 照片数据
     */
    renderPhotoInfo: function(photo) {
        if (!photo) {
            this.elements.photoContainer.style.display = 'none';
            return;
        }
        
        const html = `
            <div class="photo-detail">
                <img src="${photo.image_url || '/images/no-image.png'}" class="photo-img" onerror="this.src='/images/no-image.png'" alt="照片">
                <div class="photo-detail-info">
                    <div class="photo-type">${photo.type || '未知类型'}</div>
                    <div class="photo-description">${photo.description || '无描述'}</div>
                    <div class="photo-date">上传时间: ${photo.created_at}</div>
                </div>
            </div>
        `;
        
        this.elements.photoInfoContainer.innerHTML = html;
        this.elements.photoContainer.style.display = 'block';
    },
    
    /**
     * 删除试衣历史
     */
    deleteTryOn: function(id) {
        if (!confirm('确定要删除这条试衣历史记录吗？')) {
            return;
        }
        
        const token = Auth.getToken();
        
        fetch(`${API_BASE_URL}/login_backend/delete_admin_try_on.php`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': token
            },
            body: JSON.stringify({ id: id })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('删除失败: ' + data.msg);
                return;
            }
            
            alert('删除成功');
            window.location.href = 'try_on_list.html';
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除时出错');
        });
    },
    
    /**
     * 获取性别文本
     * @param {Number} gender 性别码
     * @returns {String} 性别文本
     */
    getGenderText: function(gender) {
        switch(parseInt(gender)) {
            case 1: return '男';
            case 2: return '女';
            default: return '未知';
        }
    },
    
    /**
     * 获取衣物类别文本
     * @param {Number} category 类别代码
     * @returns {String} 类别文本
     */
    getCategoryText: function(category) {
        const categoryMap = {
            1: '上衣',
            2: '裤子',
            3: '裙子',
            4: '外套',
            5: '鞋子',
            6: '配饰'
        };
        
        return categoryMap[category] || '其他';
    },
    
    /**
     * 获取照片类型文本
     * @param {String} type 类型
     * @returns {String} 类型文本
     */
    getPhotoTypeText: function(type) {
        const typeMap = {
            'full': '全身照',
            'half': '半身照',
            'other': '其他'
        };
        
        return typeMap[type] || type;
    },
    
    /**
     * 格式化日期显示
     * @param {String} dateStr 日期字符串
     * @returns {String} 格式化的日期
     */
    formatDate: function(dateStr) {
        if (!dateStr) return '未知';
        
        const date = new Date(dateStr);
        return isNaN(date.getTime()) 
            ? dateStr 
            : date.getFullYear() + '-' + 
              String(date.getMonth() + 1).padStart(2, '0') + '-' + 
              String(date.getDate()).padStart(2, '0') + ' ' +
              String(date.getHours()).padStart(2, '0') + ':' +
              String(date.getMinutes()).padStart(2, '0');
    }
};

// 定义API基础URL
const API_BASE_URL = window.location.hostname === 'localhost' ? '' : ''; 