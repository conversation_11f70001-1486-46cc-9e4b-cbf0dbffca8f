<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面容分析中转API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .image-preview {
            max-width: 300px;
            max-height: 300px;
            margin-top: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>面容分析中转API测试</h1>
        
        <div class="card">
            <h2>测试面容分析中转API</h2>
            <div class="form-group">
                <label for="front-photo">正面照片：</label>
                <input type="file" id="front-photo" accept="image/*">
                <div id="front-preview-container">
                    <img id="front-preview" class="image-preview" style="display: none;">
                </div>
            </div>
            <div class="form-group">
                <label for="side-photo">侧面照片（可选）：</label>
                <input type="file" id="side-photo" accept="image/*">
                <div id="side-preview-container">
                    <img id="side-preview" class="image-preview" style="display: none;">
                </div>
            </div>
            <div class="form-group">
                <label for="preferred-style">风格偏好：</label>
                <textarea id="preferred-style" rows="3" placeholder="请描述偏好的风格，如：自然、优雅、活力等"></textarea>
            </div>
            <button id="analyze-btn">发送请求</button>
        </div>
        
        <div class="card">
            <h2>API响应结果</h2>
            <div id="result" class="result">未有响应结果</div>
        </div>
    </div>

    <script>
        // API地址
        const apiUrl = 'https://www.furrywoo.com/gemini/mianrongfenxi.php';
        
        // 显示图片预览
        document.getElementById('front-photo').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('front-preview');
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });
        
        document.getElementById('side-photo').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('side-preview');
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });
        
        // 发送分析请求
        document.getElementById('analyze-btn').addEventListener('click', function() {
            const frontPhotoInput = document.getElementById('front-photo');
            if (!frontPhotoInput.files || frontPhotoInput.files.length === 0) {
                alert('请选择正面照片');
                return;
            }
            
            const preferredStyle = document.getElementById('preferred-style').value;
            
            // 禁用按钮并显示加载状态
            const analyzeBtn = document.getElementById('analyze-btn');
            analyzeBtn.disabled = true;
            analyzeBtn.textContent = '处理中...';
            document.getElementById('result').textContent = '正在处理请求，请稍候...';
            
            // 读取正面照片
            const frontPhotoReader = new FileReader();
            frontPhotoReader.onload = function(e) {
                const frontPhotoBase64 = e.target.result;
                
                // 读取侧面照片（如果有）
                const sidePhotoInput = document.getElementById('side-photo');
                if (sidePhotoInput.files && sidePhotoInput.files.length > 0) {
                    const sidePhotoReader = new FileReader();
                    sidePhotoReader.onload = function(e) {
                        const sidePhotoBase64 = e.target.result;
                        sendRequest(frontPhotoBase64, sidePhotoBase64, preferredStyle);
                    };
                    sidePhotoReader.readAsDataURL(sidePhotoInput.files[0]);
                } else {
                    // 没有侧面照片
                    sendRequest(frontPhotoBase64, '', preferredStyle);
                }
            };
            frontPhotoReader.readAsDataURL(frontPhotoInput.files[0]);
        });
        
        // 发送请求到API
        function sendRequest(frontPhotoBase64, sidePhotoBase64, preferredStyle) {
            // 构建请求数据
            const requestData = {
                front_photo_base64: frontPhotoBase64
            };
            
            if (sidePhotoBase64) {
                requestData.side_photo_base64 = sidePhotoBase64;
            }
            
            if (preferredStyle) {
                requestData.preferred_style = preferredStyle;
            }
            
            // 发送请求
            fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('result').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                console.error('请求失败:', error);
                document.getElementById('result').textContent = '请求失败: ' + error.message;
            })
            .finally(() => {
                const analyzeBtn = document.getElementById('analyze-btn');
                analyzeBtn.disabled = false;
                analyzeBtn.textContent = '发送请求';
            });
        }
    </script>
</body>
</html> 