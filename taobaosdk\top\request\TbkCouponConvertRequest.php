<?php
/**
 * TOP API: taobao.tbk.coupon.convert request
 * 
 * <AUTHOR> create
 * @since 1.0, 2022.05.16
 */
class TbkCouponConvertRequest
{
    /**
     * 推广位id，mm_xx_xx_xx pid三段式中的第三段
     **/
    private $adzoneId;
    
    /**
     * 淘宝客商品id
     **/
    private $itemId;
    
    /**
     * 营销计划链接中的me参数
     **/
    private $me;
    
    /**
     * 1：PC，2：无线，默认：1
     **/
    private $platform;
    
    /**
     * 渠道关系ID，仅适用于渠道推广场景
     **/
    private $relationId;
    
    /**
     * 会员运营ID，仅适用于会员运营场景
     **/
    private $specialId;
    
    /**
     * 商品ID
     **/
    private $goodsId;
    
    /**
     * 券ID
     **/
    private $activityId;
    
    private $apiParas = array();
    
    public function setAdzoneId($adzoneId)
    {
        $this->adzoneId = $adzoneId;
        $this->apiParas["adzone_id"] = $adzoneId;
    }
    
    public function getAdzoneId()
    {
        return $this->adzoneId;
    }
    
    public function setItemId($itemId)
    {
        $this->itemId = $itemId;
        $this->apiParas["item_id"] = $itemId;
    }
    
    public function getItemId()
    {
        return $this->itemId;
    }
    
    public function setMe($me)
    {
        $this->me = $me;
        $this->apiParas["me"] = $me;
    }
    
    public function getMe()
    {
        return $this->me;
    }
    
    public function setPlatform($platform)
    {
        $this->platform = $platform;
        $this->apiParas["platform"] = $platform;
    }
    
    public function getPlatform()
    {
        return $this->platform;
    }
    
    public function setRelationId($relationId)
    {
        $this->relationId = $relationId;
        $this->apiParas["relation_id"] = $relationId;
    }
    
    public function getRelationId()
    {
        return $this->relationId;
    }
    
    public function setSpecialId($specialId)
    {
        $this->specialId = $specialId;
        $this->apiParas["special_id"] = $specialId;
    }
    
    public function getSpecialId()
    {
        return $this->specialId;
    }
    
    public function setGoodsId($goodsId)
    {
        $this->goodsId = $goodsId;
        $this->apiParas["goods_id"] = $goodsId;
    }
    
    public function getGoodsId()
    {
        return $this->goodsId;
    }
    
    public function setActivityId($activityId)
    {
        $this->activityId = $activityId;
        $this->apiParas["activity_id"] = $activityId;
    }
    
    public function getActivityId()
    {
        return $this->activityId;
    }
    
    public function getApiMethodName()
    {
        return "taobao.tbk.coupon.convert";
    }
    
    public function getApiParas()
    {
        return $this->apiParas;
    }
    
    /**
     * 检查参数合法性
     */
    public function check()
    {
        // 必填参数检查
        RequestCheckUtil::checkNotNull($this->adzoneId, "adzone_id");
        RequestCheckUtil::checkNotNull($this->itemId, "item_id");
        
        // 其他可选参数的条件检查
        if (!is_null($this->me)) {
            RequestCheckUtil::checkMaxLength($this->me, 20, "me");
        }
    }
} 